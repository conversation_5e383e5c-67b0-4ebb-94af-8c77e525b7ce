#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the _compute_agent_cost function to properly preserve existing agent_cost
when status is not in allowed_statuses. In Odoo, computed fields must always set a value.
"""

import re

def fix_agent_cost_final():
    file_path = "/Users/<USER>/Desktop/olivery_web/delivery3/delivery/addons/delivery_modules/rb_delivery/models/order/order_model.py"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Define the current pattern
    old_pattern = r'''        if self\.state in allow_statuses:
            if agent:
                self\.agent_cost = self\.get_agent_cost\(agent,'delivered_by'\)
            elif delivered_by:
                self\.agent_cost = self\.get_agent_cost\(delivered_by,'delivered_by'\)
            if self\.extra_agent_cost:
                self\.agent_cost = self\.agent_cost \+ self\.extra_agent_cost
        # If status is not in allowed_statuses, preserve existing agent_cost value'''
    
    # Define the new replacement that explicitly preserves the current value
    new_replacement = '''        # Store current agent_cost to preserve it if status doesn't allow recalculation
        current_agent_cost = self.agent_cost or 0
        
        if self.state in allow_statuses:
            if agent:
                self.agent_cost = self.get_agent_cost(agent,'delivered_by')
            elif delivered_by:
                self.agent_cost = self.get_agent_cost(delivered_by,'delivered_by')
            if self.extra_agent_cost:
                self.agent_cost = self.agent_cost + self.extra_agent_cost
        else:
            # Preserve existing agent_cost value when status doesn't allow recalculation
            self.agent_cost = current_agent_cost'''
    
    # Replace the pattern
    new_content = re.sub(old_pattern, new_replacement, content)
    
    if new_content != content:
        # Write the updated content back
        with open(file_path, 'w') as f:
            f.write(new_content)
        print("✅ Successfully fixed the _compute_agent_cost function to properly preserve values!")
        print("Now agent_cost will:")
        print("  - Only be recalculated when status is in allowed_status_ids")
        print("  - Explicitly preserve the existing value when status is NOT in allowed_status_ids")
    else:
        print("❌ Pattern not found. The function may have already been modified or the format is different.")

if __name__ == "__main__":
    fix_agent_cost_final()
