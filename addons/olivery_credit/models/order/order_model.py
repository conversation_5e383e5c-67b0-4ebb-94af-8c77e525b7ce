# -*- coding: utf-8 -*-

import json
import logging
from openerp import models, fields, api, _
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_credit_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    credit_id = fields.One2many('olivery_credit.credit',inverse_name='order_id',string='Credit',track_visibility="on_change")
    
    business_credit = fields.Float('client credit', compute='compute_credits')

    agent_credit = fields.Float('client credit', compute='compute_credits')


    @api.depends('assign_to_agent', 'assign_to_business')
    @api.multi
    def compute_credits(self):
        for rec in self:
            if rec.assign_to_agent: 
                rec.agent_credit = rec.sudo().assign_to_agent.total_credit
            if rec.assign_to_business: 
                rec.business_credit = rec.sudo().assign_to_business.total_credit


    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    def get_order_credit(self):
        address_form_id = self.env.ref('olivery_credit.view_tree_olivery_credit_credit').id
        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'olivery_credit.credit',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
    
    @api.multi
    def write(self, values):
        write = super(olivery_credit_order, self).write(values)
        if 'state' in values and values['state']:
            self.with_context(original_uid=self.env.user.partner_id.id).sudo().execute_credit_operations(values)

        return write
    
    def create(self, values):
        record = super(olivery_credit_order, self).create(values)
        record.execute_credit_operations(values)

    @api.model
    def execute_credit_operations(self, values):
        if 'state' in values and values['state']:
            credit_configurations = self.env['olivery_credit.credit_configurations'].sudo().search([], limit=1)

            if credit_configurations:
                configuration_items = credit_configurations.configuration_items

                for item in configuration_items:
                    if values['state'] == item.status_id.name:
                        method_to_call=getattr(olivery_credit_order,item.compute_function.name)
                        method_to_call(self,values,item)

    
    @api.model
    def function_deduct_business_company(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_business.total_credit < conf.min_credit:
                raise ValidationError(_('Business %s credit (%s) is less credit than the allowed which is %s') % (rec.sudo().assign_to_business.username, rec.sudo().assign_to_business.total_credit, conf.min_credit))
            
            customer_payment = float(values.get('customer_payment', 0)) or float(rec.customer_payment)
            if rec.delivery_cost - customer_payment != 0:
            
                self.env['olivery_credit.credit'].create({
                    'credit': float(rec.delivery_cost - customer_payment) * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_business.id,
                    'compute_function': 'function_deduct_business_company'
                })

    @api.model
    def function_negate_deduct_business_company(self, values, conf):
        for rec in self:
            credits = self.env['olivery_credit.credit'].search([('order_id', '=', rec.id), ('compute_function', '=', 'function_deduct_business_company')], order='create_date desc', limit=1)
            if credits:
                self.env['olivery_credit.credit'].create({
                    'credit': credits.credit * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_business.id,
                    'compute_function': 'function_negate_deduct_business_company'
                })

    @api.model
    def function_deduct_driver_company(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_agent.total_credit < conf.min_credit:
                raise ValidationError(_('Agent %s credit (%s) is less credit than the allowed which is %s') % (rec.sudo().assign_to_agent.username, rec.sudo().assign_to_agent.total_credit, conf.min_credit))
            if rec.delivery_profit != 0:
                self.env['olivery_credit.credit'].create({
                    'credit': float(rec.delivery_profit) * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_agent.id,
                    'compute_function': 'function_deduct_driver_company'})
                
    @api.model
    def function_negate_deduct_driver_company(self, values, conf): 
        for rec in self:
            credits = self.env['olivery_credit.credit'].search([('order_id', '=', rec.id), ('compute_function', '=', 'function_deduct_driver_company')])
            if credits:
                self.env['olivery_credit.credit'].create({
                    'credit': float(rec.delivery_profit),
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_agent.id,
                    'compute_function': 'function_negate_deduct_driver_company'})

    @api.model            
    def function_deduct_assign_driver_validation(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_agent.total_credit < conf.min_credit:
                raise ValidationError(_('Agent %s credit (%s) is less credit than the allowed which is %s') % (rec.sudo().assign_to_agent.username, rec.sudo().assign_to_agent.total_credit, conf.min_credit))
            
    @api.model          
    def function_deduct_assign_business_validation(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_business.total_credit < conf.min_credit:
                raise ValidationError(_('Business %s credit (%s) is less credit than the allowed which is %s') % (rec.sudo().assign_to_business.username, rec.sudo().assign_to_business.total_credit, conf.min_credit))
            

    @api.model
    def function_deduct_business_price_from_driver(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_agent.total_credit < conf.min_credit:
                raise ValidationError(_('Agent %s credit (%s) is less credit than the allowed which is %s') % (rec.assign_to_agent.username, rec.assign_to_agent.total_credit, conf.min_credit))
            
            if rec.sudo().assign_to_business.pricing_fixed_value:
                self.env['olivery_credit.credit'].create({
                    'credit': float(rec.sudo().assign_to_business.pricing_fixed_value) * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_agent.id,
                    'compute_function': 'function_deduct_business_price_from_driver'})
                
    @api.model
    def function_negate_deduct_business_price_from_driver(self, values, conf): 
        for rec in self:
            
            if rec.sudo().assign_to_business.pricing_fixed_value:
                credits = self.env['olivery_credit.credit'].search([('order_id', '=', rec.id), ('compute_function', '=', 'function_deduct_business_price_from_driver')])
                if credits:
                    self.env['olivery_credit.credit'].create({
                        'credit': float(rec.sudo().assign_to_business.pricing_fixed_value),
                        'order_id': rec.id,
                        'user_id': rec.sudo().assign_to_agent.id,
                        'compute_function': 'function_negate_deduct_business_price_from_driver'})
                    
    @api.model
    def add_customer_payment_to_bussiness_wallet(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_business.total_credit < conf.min_credit:
                raise ValidationError(_('Business %s credit (%s) is less credit than the allowed which is %s') % (rec.sudo().assign_to_business.username, rec.sudo().assign_to_business.total_credit, conf.min_credit))
            
            customer_payment = float(values.get('customer_payment', 0)) or float(rec.customer_payment)
            self.env['olivery_credit.credit'].create({
                'credit': customer_payment,
                'order_id': rec.id,
                'user_id': rec.sudo().assign_to_business.id,
                'compute_function': 'add_customer_payment_to_bussiness_wallet'
            })

    @api.model
    def function_negate_add_customer_payment_to_bussiness_wallet(self, values, conf): 
        for rec in self:
            credits = self.env['olivery_credit.credit'].search([('order_id', '=', rec.id), ('compute_function', '=', 'add_customer_payment_to_bussiness_wallet')],order='create_date desc', limit=1)
            if credits:
                self.env['olivery_credit.credit'].create({
                    'credit': credits.credit * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_business.id,
                    'compute_function': 'function_negate_add_customer_payment_to_bussiness_wallet'})



    @api.model
    def function_deduct_delivery_fee_business_company(self, values, conf):
        for rec in self:
            if rec.sudo().assign_to_business.total_credit < conf.min_credit:
                self.env['rb_delivery.error_log'].raise_olivery_error(990, self.id, {'business_name': rec.sudo().assign_to_business.username, 'credit': rec.sudo().assign_to_business.total_credit, 'min_credit': conf.min_credit})
            
            if rec.delivery_cost != 0:
            
                self.env['olivery_credit.credit'].create({
                    'credit': float(rec.delivery_cost) * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_business.id,
                    'compute_function': 'function_deduct_delivery_fee_business_company'
                })

    @api.model
    def function_negate_deduct_delivery_fee_business_company(self, values, conf):
        for rec in self:
            credits = self.env['olivery_credit.credit'].search([('order_id', '=', rec.id), ('compute_function', '=', 'function_deduct_delivery_fee_business_company')], order='create_date desc', limit=1)
            if credits:
                self.env['olivery_credit.credit'].create({
                    'credit': credits.credit * -1,
                    'order_id': rec.id,
                    'user_id': rec.sudo().assign_to_business.id,
                    'compute_function': 'function_negate_deduct_delivery_fee_business_company'
                })

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------

