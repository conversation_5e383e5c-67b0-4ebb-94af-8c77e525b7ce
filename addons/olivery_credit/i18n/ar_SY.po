# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_credit
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-16 06:27+0000\n"
"PO-Revision-Date: 2022-06-16 06:27+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_order
msgid "<span class=\"o_stat_text\">Credit</span>"
msgstr ""

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_user
msgid "<span class=\"o_stat_text\">Credits</span>"
msgstr ""

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_credit
#: model:ir.actions.act_window,name:olivery_credit.action_olivery_credit_add_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_user
msgid "Add Credit"
msgstr "اضافة رصيد"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "By Create Date"
msgstr "تاريخ الانشاء"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "By Order"
msgstr "رقم الطلب"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "By User"
msgstr " الستخدم"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_add_credit
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "Create Date"
msgstr "تاريخ الانشاء"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "Created By"
msgstr "أنشأ بواسطة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__create_uid
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__create_date
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_credit
#: model:ir.actions.act_window,name:olivery_credit.action_olivery_credit_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__credit
#: model:ir.model.fields,field_description:olivery_credit.field_rb_delivery_order__credit_id
#: model:ir.ui.menu,name:olivery_credit.menu_olivery_credit_credit
#: model:ir.ui.menu,name:olivery_credit.olivery_credit_top_menu
msgid "Credit"
msgstr "الدائن"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__display_name
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__id
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit____last_update
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__write_uid
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_add_credit__write_date
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__order_id
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "Order"
msgstr "الأمر"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_add_credit
msgid "Save"
msgstr "حفظ"

#. module: olivery_credit
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_form_olivery_credit_user
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_tree_olivery_credit_credit
msgid "Total Credit"
msgstr "مجموع الرصيد"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_rb_delivery_user__total_credit
msgid "Total credit"
msgstr "مجموع الرصيد"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__user_id
#: model_terms:ir.ui.view,arch_db:olivery_credit.view_search_olivery_credit_credit
msgid "User"
msgstr "المستخدم"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__compute_function
msgid "Compute function"
msgstr "الداله الحسابيه"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: olivery_credit
#: model:ir.model.fields,help:olivery_credit.field_olivery_credit_credit__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: olivery_credit
#: model:ir.model,name:olivery_credit.model_olivery_credit_add_credit
msgid "olivery_credit.add_credit"
msgstr ""

#. module: olivery_credit
#: model:ir.model,name:olivery_credit.model_olivery_credit_credit
msgid "olivery_credit.credit"
msgstr ""

#. module: olivery_credit
#: model:ir.model,name:olivery_credit.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""

#. module: olivery_credit
#: model:ir.model,name:olivery_credit.model_rb_delivery_user
msgid "rb_delivery.user"
msgstr ""

#. module: olivery_credit
#: code:addons/olivery_credit/models/order/order_model.py:110
#: code:addons/olivery_credit/models/order/order_model.py:80
#, python-format
msgid "Business %s credit (%s) is less credit than the allowed which is %s"
msgstr "رصيد التاجر %s هو %s وهو اقل من الرصيد المسموح وهو %s "

#. module: olivery_credit
#: code:addons/olivery_credit/models/order/order_model.py:104
#: code:addons/olivery_credit/models/order/order_model.py:93
#, python-format
msgid "Agent %s credit (%s) is less credit than the allowed which is %s"
msgstr "رصيد السائق %s هو %s وهو اقل من الرصيد المسموح وهو %s "

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_configuration_item__status_id
msgid "Statuses"
msgstr "الحالات"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_compute_function__name
msgid "Technichal Name"
msgstr "الاسم التقني"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_configuration_item__compute_function
msgid "Compute function"
msgstr "داله الحساب"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_configuration_item__configurations_id
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_configurations__configuration_items
msgid "Configurations"
msgstr "الاعدادات"

#. module: olivery_credit
#: model:ir.model.fields,field_description:olivery_credit.field_olivery_credit_credit_configuration_item__min_credit
msgid "Min credit"
msgstr "اقل رصيد"

#. module: olivery_credit
#: model:ir.actions.act_window,name:olivery_credit.action_olivery_credit_credit_configurations
#: model:ir.ui.menu,name:olivery_credit.menu_olivery_credit_credit_configurations
msgid "Credit Configurations"
msgstr "اعدادات الارصدة"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "You are not allowed to create an order while your credit ({credit}) is less then the ({min_credit})"
msgstr "لا يُسمح لك بإنشاء طلب طالما أن رصيدك ({credit}) أقل من الحد الأدنى ({min_credit})"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Please recharge your credit to be able to add orders"
msgstr "يرجى إعادة شحن رصيدك لتتمكن من إضافة طلبات"