<odoo>
    <data>
        <record id="credit_compute_function_deduct_driver_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_driver_company</field>
        </record>
        <record id="credit_compute_function_deduct_business_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_business_company</field>
        </record>
        <record id="credit_compute_function_deduct_business_price_from_driver" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_business_price_from_driver</field>
        </record>

        <record id="credit_compute_function_deduct_assign_business_validation" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_assign_business_validation</field>
        </record>
        <record id="credit_compute_function_deduct_assign_driver_validation" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_assign_driver_validation</field>
        </record>

        <record id="credit_compute_function_negate_deduct_business_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_negate_deduct_business_company</field>
        </record>
        <record id="credit_compute_function_negate_deduct_driver_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_negate_deduct_driver_company</field>
        </record>
        <record id="credit_compute_function_negate_deduct_business_price_from_driver" model="olivery_credit.credit_compute_function">
            <field name="name">function_negate_deduct_business_price_from_driver</field>
        </record>

        <record id="credit_compute_add_customer_payment_to_bussiness_wallet" model="olivery_credit.credit_compute_function">
            <field name="name">add_customer_payment_to_bussiness_wallet</field>
        </record>
        <record id="credit_compute_function_negate_deduct_business_price_from_driver" model="olivery_credit.credit_compute_function">
            <field name="name">function_negate_add_customer_payment_to_bussiness_wallet</field>
        </record>

        <record id="credit_compute_function_deduct_delivery_fee_business_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_deduct_delivery_fee_business_company</field>
        </record>
        <record id="credit_compute_function_negate_deduct_delivery_fee_business_company" model="olivery_credit.credit_compute_function">
            <field name="name">function_negate_deduct_delivery_fee_business_company</field>
        </record>

        <record id="credit_credit_configurations_demo" model="olivery_credit.credit_configurations"></record>
    </data>
</odoo>