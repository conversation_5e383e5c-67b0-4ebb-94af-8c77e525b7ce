<odoo>
    <data>
        <record id="order_credit_error_990" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">You are not allowed to create an order while your credit ({credit}) is less then the ({min_credit})</field>
            <field name="what_to_do">Please recharge your credit to be able to add orders</field>
            <field name="error_code">990</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>
    </data>
</odoo>