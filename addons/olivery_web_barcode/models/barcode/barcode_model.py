# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from datetime import datetime
from collections import Counter
from openerp.exceptions import ValidationError

class rb_delivery_barcode(models.Model):

    _name = 'rb_delivery.barcode'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    def _get_driver_users(self):
        ids = []

        group = self.env.ref('rb_delivery.role_driver')

        users = self.env['rb_delivery.user'].search(
            [('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    def _default_show_text_barcode(self):
        default_show_text_barcode = self.env['rb_delivery.client_configuration'].get_param('show_text_barcode')
        return default_show_text_barcode

    def _compute_show_text_barcode(self):
        show_text_barcode = self.env['rb_delivery.client_configuration'].get_param('show_text_barcode')
        self.show_text_barcode = show_text_barcode

    @api.one
    def _show_change_state_button(self):
        scan_then_update = self.env['rb_delivery.client_configuration'].get_param('scan_then_update')
        self.scan_then_update = scan_then_update
        return scan_then_update

    @api.model
    def default_payment_type(self):
        payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
        if len(payment_type) != 0:
            return payment_type[0].id if payment_type else None
        else:
            return None


    show_text_barcode = fields.Boolean('Show Text Barcode',track_visibility="on_change", default=_default_show_text_barcode, compute="_compute_show_text_barcode", readonly=True)

    order_barcode = fields.Char("Order BarCode")

    text_order_barcode = fields.Text("Order BarCodes")

    success = fields.Char("Success")

    error = fields.Text("Error")

    show_success =fields.Boolean('Show Success',readonly=True,default=False)

    show_error =fields.Boolean('Show Error',readonly=True,default=False)

    last_barcode = fields.Char("Last BarCode", readonly=True)

    assign_to_agent_id = fields.Many2one(
        'rb_delivery.user', 'Agent', domain=_get_driver_users)

    to_order_state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",required=True)

    state_id = fields.Char('State id')

    reschedule_date = fields.Datetime(string="Reschedule Date",track_visibility="on_change")

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',default=default_payment_type,track_visibility="on_change")

    customer_payment = fields.Char("Customer payment")

    reject_reason = fields.Many2one('rb_delivery.reject_reason', 'Reject Reason',track_visibility="on_change")

    stuck_comment = fields.Text('Stuck Comment')

    show_reschedule_date = fields.Boolean('Show Reschedule Date', default=False)

    show_reject_reason = fields.Boolean('Show Reschedule Date', default=False)

    show_payment_type = fields.Boolean('Show Payment Type', default=False)

    show_agent = fields.Boolean('Show Agent', default=False)

    show_customer_payment = fields.Boolean('Show Customer Payment', default=False)

    required_reschedule_date = fields.Boolean('Show Reschedule Date Required', default=False)

    required_reject_reason = fields.Boolean('Show Reschedule Date Required', default=False)

    required_payment_type = fields.Boolean('Show Payment Type Requried', default=False)

    required_agent = fields.Boolean('Show Agent Required', default=False)

    required_customer_payment = fields.Boolean('Show Customer Payment Required', default=False)

    scan_then_update = fields.Boolean("Show change state button" , compute="_show_change_state_button", default=_show_change_state_button, readonly=True)

    error_messages = fields.Text("Error Messages", readonly=True)

    payment_type_two = fields.Many2one('rb_delivery.payment_type', 'Second payment method',default=default_payment_type,track_visibility="on_change")

    customer_payment_two = fields.Char("Second customer payment")

    show_payment_type_two = fields.Boolean('Show Payment Type', default=False)

    show_customer_payment_two = fields.Boolean('Show Customer Payment', default=False)

    required_payment_type_two = fields.Boolean('Show Payment Type Requried', default=False)

    required_customer_payment_two = fields.Boolean('Show Customer Payment Required', default=False)


    @api.onchange('payment_type')
    def _onchange_payment_type(self):
        """Exclude selected payment_type from payment_type_two"""
        if self.payment_type:
            return {'domain': {'payment_type_two': [('id', '!=', self.payment_type.id)]}}
        return {'domain': {'payment_type_two': []}}
    
    @api.onchange('payment_type_two')
    def _onchange_payment_type_two(self):
        """Exclude selected payment_type_two from payment_type"""
        if self.payment_type_two:
            return {'domain': {'payment_type': [('id', '!=', self.payment_type_two.id)]}}
        return {'domain': {'payment_type': []}}

    error_records = fields.Many2many(
        comodel_name = 'rb_delivery.errors',
        string = 'Errors',
        relation = 'barcode_errors_item',
        column1 = 'barcode_id',
        column2 = 'errors_id',widget='many2many_list')

    # inherit module[olivery_branch_collection]
    def clear_values(self):
        self.reschedule_date = False
        self.reject_reason = False
        self.assign_to_agent_id = False
        self.payment_type = False
        self.customer_payment = False
        self.show_reschedule_date = False
        self.show_reject_reason = False
        self.payment_type_two = False
        self.customer_payment_two = False
        self.show_payment_type_two = False
        self.show_customer_payment_two = False
        self.required_payment_type_two = False
        self.required_customer_payment_two = False
        self.show_agent = False
        self.show_payment_type = False
        self.show_customer_payment = False
        self.required_reschedule_date = False
        self.show_to_branch = False
        self.required_to_branch = False
        self.required_reject_reason = False
        self.required_agent = False
        self.required_payment_type = False
        self.required_customer_payment = False

    # inherit module[olivery_branch_collection]
    @api.onchange('to_order_state')
    def change_state(self):
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',self.to_order_state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        self.state_id = order_state.id
        optional_status_actions = order_state.status_action_optional_related_fields
        required_status_actions = order_state.status_action_required_aditional_fields
        self.clear_values()
        if order_state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'show_reschedule_date':
                    self.show_reschedule_date = True
                if status_action.name == 'show_agent':
                    self.show_agent = True
                if status_action.name == 'show_payment_type':
                    self.show_payment_type = True
                if status_action.name == 'show_customer_payment':
                    self.show_customer_payment = True
                if status_action.name == 'show_reject_reason':
                    self.show_reject_reason = True
                if status_action.name == 'show_payment_type_two':
                    self.show_payment_type_two = True
                if status_action.name == 'show_customer_payment_two':
                    self.show_customer_payment_two = True
        if order_state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'show_reschedule_date':
                    self.show_reschedule_date = True
                    self.required_reschedule_date = True
                if status_action.name == 'show_agent':
                    self.show_agent = True
                    self.required_agent = True
                if status_action.name == 'show_payment_type':
                    self.show_payment_type = True
                    self.required_payment_type = True
                if status_action.name == 'show_customer_payment':
                    self.show_customer_payment = True
                    self.required_customer_payment = True
                if status_action.name == 'show_reject_reason':
                    self.show_reject_reason = True
                    self.required_reject_reason = True
                if status_action.name == 'show_payment_type_two':
                    self.show_payment_type_two = True
                    self.required_payment_type_two = True
                if status_action.name == 'show_customer_payment_two':
                    self.show_customer_payment_two = True
                    self.required_customer_payment_two = True

    @api.model
    def get_status(self):
        group_id = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        fields=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])

        for status in next_statuses:
            if group_id:
                if status.role_action_status_ids and len(status.role_action_status_ids)>0:
                    for role in status.role_action_status_ids:
                        if role.id == group_id.id:
                            status_list.append((status.name,status.title))
            else:
                    status_list.append((status.name,status.title))
        return status_list

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'barcode_order_item',
        column1 = 'barcode_id',
        column2 = 'order_id')

    barcode_item_ids = fields.Many2many(
        comodel_name = 'rb_delivery.barcode_item',
        string = 'Orders',
        relation = 'barcode_barcode_item_item',
        column1 = 'barcode_id',
        column2 = 'barcode_item_id')

    @api.depends('barcode_item_ids')
    @api.one
    def _compute_no_of_orders(self):
        self.number_of_orders = len(self.barcode_item_ids)

    number_of_orders = fields.Integer("Number of orders", compute=_compute_no_of_orders)

    order_barcode_temp = fields.Char('Temp')

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------


    @api.onchange('order_barcode')
    def change_order_temp(self):
        if self.order_barcode:
            self.order_barcode_temp = self.order_barcode
            self.order_barcode = ''
        return

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        order_ids = []
        if self.barcode_item_ids:
            order_ids = order_ids + [barcode_item_id.order_id.id for barcode_item_id in self.barcode_item_ids]
        if self.error_records:
            done_error_records = self.error_records.filtered(lambda x:x.is_done==True)
            order_refs = [error_record.order_reference for error_record in done_error_records]
            orders = self.env['rb_delivery.order'].search(['|','|','|',('follower_ref_id','in',order_refs),('partner_reference_id','in',order_refs),('reference_id','in',order_refs),('sequence','in',order_refs)])
            if orders:
                order_ids = order_ids + orders.ids
        if len(order_ids)>0:
            domain = [('id', 'in', order_ids)]
            return {
                'type': 'ir.actions.act_window',
                'name': 'Orders',
                'res_model': 'rb_delivery.order',
                'view_type': 'form',
                'view_mode': 'tree,form',
                'views': [(address_form_id, 'tree'), (False, 'form')],
                'target': 'current',
                'domain': domain}

    def get_error_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        order_ids = []

        if self.error_records:
            done_error_records = self.error_records.filtered(lambda x: x.is_done == False)
            order_refs = [error_record.order_reference for error_record in done_error_records]
            orders = self.env['rb_delivery.order'].search(['|','|','|',('follower_ref_id','in',order_refs),('partner_reference_id','in',order_refs),('reference_id', 'in', order_refs), ('sequence', 'in', order_refs)])
            if orders:
                order_ids = orders.ids
        if len(order_ids) >= 0:
            domain = [('id', 'in', order_ids)]
            return {
                'type': 'ir.actions.act_window',
                'name': 'Orders',
                'res_model': 'rb_delivery.order',
                'view_type': 'form',
                'view_mode': 'tree,form',
                'views': [(address_form_id, 'tree'), (False, 'form')],
                'target': 'current',
                'domain': domain
            }


    def add_success_message(self,message='',success_sound=''):
        if message:
            self.success = message
            self.show_success = True
        messages =  ['success_sound']
        if success_sound:
            messages.append(success_sound)
        self.env['rb_delivery.utility'].send_toast('for_user', messages , str(self._uid))

    def add_error_message(self,message,unstyled_message,reference ='',error_sound=''):
        error_record = self.env['rb_delivery.errors'].sudo().create({'name': message,'order_reference': reference})
        self.error = unstyled_message
        self.show_error = True
        self.error_records = [(4,error_record.id)]
        messages = ['error_sound']
        if error_sound:
            messages.append(error_sound)
        self.env['rb_delivery.utility'].send_toast('for_user', messages , str(self._uid))
        return

    # inherit module[olivery_branch_collection]
    def get_required_fields(self,values):
        field_required_messages = []
        if self.show_agent == True:
            if self.required_agent == True and not self.assign_to_agent_id:
                field_required_messages.append(_("Agent is required. \n"))
        if self.show_reschedule_date == True:
            if  not self.reschedule_date and self.required_reschedule_date == True:
                field_required_messages.append(_("Reschedule date is required.\n"))
        if self.show_reject_reason == True:
            if not self.reject_reason and self.required_reject_reason == True:
                field_required_messages.append(_("Reject reason is required.\n"))

        if self.show_customer_payment_two == True:
            if not self.customer_payment_two  and self.required_customer_payment_two == True:
                field_required_messages.append(_("Customer payment is required.\n"))
            elif self.customer_payment_two:
                values['customer_payment_two'] = self.customer_payment_two

        if self.show_customer_payment == True:
            if not self.customer_payment  and self.required_customer_payment == True:
                field_required_messages.append(_("Customer payment is required.\n"))
            elif self.customer_payment:
                if 'customer_payment_two' not in values:
                    values['customer_payment'] = self.customer_payment
                else:
                    values['customer_payment'] = str(float(self.customer_payment) + float(values['customer_payment_two']))
                    values['customer_payment_one'] = self.customer_payment

        if self.show_payment_type == True:
            if not self.payment_type and self.required_payment_type == True:
                field_required_messages.append(_("Payment type is required. "))

        if self.show_payment_type_two == True:
            if not self.payment_type_two and self.required_payment_type_two == True:
                field_required_messages.append(_("Payment type two is required. "))
            elif self.payment_type_two:
                values['payment_type_two'] = self.payment_type_two.id
        return field_required_messages


    @api.onchange('order_barcode_temp')
    def change_order_barcode(self):
        self.show_success = False
        self.show_error = False
        self.error = ''
        message = ''
        if self.order_barcode_temp:
            if not self.to_order_state:
                self.order_barcode_temp = ''
                message = _("Make sure to choose a status before you start scanning orders.")
                self.add_error_message(message,message,reference='')
                return
            barcode = self.order_barcode_temp
            self.order_barcode_temp = ''
            self.check_orders([barcode],False)

    def print_and_create_runsheet(self):
        self.create_runsheet()
        return self.print_runsheet()

    def print_and_create_runsheet_without_barcode(self):
        self.create_runsheet()
        return self.print_runsheet_without_barcode()

    def get_barcode_collection_vals(self,failed_orders,success_orders,values,barcode_items):
        seqs ="{"
        for barcode_item in barcode_items:
            seqs = seqs + " " + barcode_item.sudo().order_id.sequence
        seqs = seqs + "}"
        fmt = "%Y-%m-%d %H:%M:%S"
        date = datetime.strftime(datetime.today(), fmt)
        branch_collection_vals = {'scan_date':date,'number_of_orders':len(failed_orders)+len(success_orders),'number_of_success_orders':len(success_orders),'number_of_failed_orders':len(failed_orders),'order_seqs':seqs}
        if values.get('assign_to_agent'):
            branch_collection_vals['agent_id'] = values.get('assign_to_agent')
        return branch_collection_vals

    @api.model
    def write_on_order(self,barcode_items,values):
        original_uid = self.env.user.partner_id.id
        order_ids=[barcode_item.sudo().order_id.id for barcode_item in barcode_items]
        orders = self.env['rb_delivery.order'].with_context(original_uid=original_uid).sudo().browse(order_ids)

        error_massages , success_ids = orders.sudo(self._uid).write_and_pass_failed(values)
        for error_message in error_massages:
            barcode_item_id = self.barcode_item_ids.filtered(lambda x: x.sudo().order_id.sequence == error_message[0]).id
            self.barcode_item_ids = [(3,barcode_item_id)]
            message = _("Sequence: %s, issue: %s")%(error_message[0],str(error_message[1]))
            self.add_error_message(message,message,reference=error_message[0])

        if len(success_ids):
            logged_in_user=self.env.user.name
            success_orders = self.env['rb_delivery.order'].with_context(original_uid=original_uid).sudo().browse(success_ids)
            for success_order in success_orders:
                success_order.with_context(original_uid=original_uid).sudo().message_post(body=_("Order has been updated using Web Barcode By %s." %logged_in_user))
            message = _("Your order has been successfully updated.")
            self.add_success_message(message)
        if self.scan_then_update:
            branch_collection_vals = self.get_barcode_collection_vals(error_massages,success_ids,values,barcode_items)
            self.env['rb_delivery.barcode_collection'].create(branch_collection_vals)

    def create_runsheet(self):
        order_ids = []

        for barcode_item_id in self.barcode_item_ids:
            order_ids.append(barcode_item_id.order_id.id)
            order = barcode_item_id.order_id
            self.write_on_order(barcode_item_id,{'scanned_order_sequence':barcode_item_id.scanned_order_sequence})
        self.env['rb_delivery.create_runsheet'].create_runsheet(order_ids,False)

    def print_runsheet(self):
        order_ids = []
        for barcode_item_id in self.barcode_item_ids:
            order_ids.append(barcode_item_id.order_id.id)
        context = {
            'active_ids': order_ids
        }
        return {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': 'rb_delivery.dist',
                'activeIds': order_ids,
                'context': context,
                'report_file': 'rb_delivery.dist',}

    def print_runsheet_without_barcode(self):
        order_ids = []
        for barcode_item_id in self.barcode_item_ids:
            order_ids.append(barcode_item_id.order_id.id)
        context = {
            'active_ids': order_ids
        }
        return {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': 'rb_delivery.runsheet_without_barcode',
                'activeIds': order_ids,
                'context': context,
                'report_file': 'rb_delivery.runsheet_without_barcode',}

    def clear_orders(self):
        vals = {'error_records':[(6,0,[])],'barcode_item_ids':[(6,0,[])],'error_messages':'','success':'','error':'','show_error':False,'show_success':False}
        self.write(vals)

    def clear_errors(self):
        vals = {'error_records':[(6,0,[])],'error_messages':'','success':'','error':'','show_error':False,'show_success':False}
        self.write(vals)

    def change_status(self):
        values = self.get_order_values()
        if self.barcode_item_ids and len(values)>0:
            self.write_on_order(self.barcode_item_ids,values)

    def check_not_found_orders(self, not_found_barcodes, not_found_barcodes_str, from_text):
        allow_to_create_skeleton = self.env['rb_delivery.client_configuration'].get_param('order_create_driver_ability')

        if allow_to_create_skeleton:
            last_scanned_sequence = (
                self.barcode_item_ids[-1].scanned_order_sequence if self.barcode_item_ids else 0
            )
            created_orders = []
            created_barcodes = []
            barcode_items = []

            for barcode in not_found_barcodes:
                new_order = self.env['rb_delivery.order'].create({'reference_id': barcode})
                if new_order:
                    last_scanned_sequence += 1
                    barcode_item_values = {
                        'scanned_order_sequence': last_scanned_sequence,
                        'order_id': new_order.id,
                    }
                    barcode_items.append((barcode_item_values, new_order.id))

            if barcode_items:
                new_barcode_items = self.env['rb_delivery.barcode_item'].sudo().create(
                    [item[0] for item in barcode_items]
                )
                created_orders = [(4, item[1]) for item in barcode_items]
                created_barcodes = [(4, barcode.id) for barcode in new_barcode_items]

            if created_orders:
                self.order_ids = created_orders
            if created_barcodes:
                self.barcode_item_ids = created_barcodes

            if self.scan_then_update and not from_text:
                self.add_success_message('', 'success_sound_2.mp3')
        else:
            message = _(
                '<b>Not Found Order:</b>\n'
                'Orders of sequences %s do not exist or you have no access to them.\n'
                '<b>What to do next:</b> Make sure you have access to orders of sequences %s.'
            ) % (not_found_barcodes_str, not_found_barcodes_str)
            unstyled_message = _("Orders of sequence %s do not exist or you have no access.") % not_found_barcodes_str
            self.add_error_message(message, unstyled_message, reference=not_found_barcodes_str, error_sound='fail_sound_2.mp3')

    def get_vals(self):
        values = {}
        if self.to_order_state:
            values['state'] = self.to_order_state
        if self.assign_to_agent_id:
            values['assign_to_agent'] = self.assign_to_agent_id.id
        if self.reschedule_date:
            values['reschedule_date'] = self.reschedule_date
        if self.reject_reason:
            values['reject_reason'] = self.reject_reason.id
        if self.stuck_comment:
            values['stuck_comment'] = self.stuck_comment
        if self.customer_payment:
            values['customer_payment'] = self.customer_payment
        return values


    def fill_counter_arr(self,orders,order_barcodes):
        references_count = {}
        duplicate_references = []
        fields_to_be_checked = ['follower_ref_id','reference_id','partner_reference_id','sequence']
        for order in orders:
            for field_name in fields_to_be_checked:
                if order[field_name] and order[field_name] in order_barcodes:
                    if order[field_name] in references_count:
                        if order not in references_count[order[field_name]]:
                            references_count[order[field_name]].append(order)
                            duplicate_references = duplicate_references + [order.sequence,order[field_name]]
                    else:
                        references_count[order[field_name]] = [order]
        return references_count

    def check_for_duplicates(self, order_barcodes, orders):
        all_barcodes = self.barcode_item_ids.read(['sequence', 'reference_id', 'partner_reference_id', 'follower_ref_id'])
        existing_barcodes = {value for record in all_barcodes for value in record.values() if value}

        duplicates = list(filter(lambda barcode: order_barcodes.count(barcode) > 1, order_barcodes))
        existing_sequences = [barcode for barcode in order_barcodes if barcode in existing_barcodes]

        if duplicates or existing_sequences:
            duplicates_str = ', '.join(map(str, set(duplicates + existing_sequences)))
            message = _('<b>Existing orders:</b> Orders %s already scanned.') % duplicates_str
            unstyled_message = _("Orders %s already scanned.") % duplicates_str
            self.add_error_message(message, unstyled_message, reference=duplicates_str, error_sound='fail_sound_1.mp3')

        references_count = self.fill_counter_arr(orders, order_barcodes)
        sequences_to_exclude = set()

        if references_count:
            for reference, related_orders in references_count.items():
                if len(related_orders) <= 1:
                    continue
                business_names = [order.sudo().assign_to_business.username for order in related_orders]
                sequences = [order.sequence for order in related_orders]
                sequences_to_exclude.update(sequences)

                business_message = _(
                    '<b>Duplicate Reference:</b>\n'
                    'You have scanned %s reference, which exists in two orders %s for businesses %s.\n'
                    '<b>What to do next:</b> Scan the sequences listed above instead of the reference %s.'
                ) % (reference, sequences, business_names, reference)

                unstyled_message = _(
                    "You have scanned %s reference, which exists in two orders %s for businesses %s."
                ) % (reference, sequences, business_names)

                self.add_error_message(business_message, unstyled_message, reference=reference)
        orders_to_check = orders.read(['sequence'])
        sequences_to_exclude = [order['sequence'] for order in orders_to_check if order['sequence'] in sequences_to_exclude]
        orders = orders.filtered(lambda x: x.sequence not in sequences_to_exclude)
        return orders, duplicates + existing_sequences

    def check_orders(self,order_barcodes,from_text):
        archive_order_statuses = self.env['rb_delivery.client_configuration'].get_param('archive_order_statuses')
        orders = self.env['rb_delivery.order'].with_context(scanned_by=self._uid, is_from_web_barcode_search=True, scan_from='Web barcode').sudo().search(['|','|','|',('follower_ref_id','in',order_barcodes),('partner_reference_id','in',order_barcodes),('sequence','in',order_barcodes),('reference_id','in',order_barcodes),('state_id','not in',archive_order_statuses)])

        scan_then_update = self._show_change_state_button()[0]

        attributes = ["sequence", "reference_id", "follower_ref_id", "partner_reference_id"]
        found_barcodes = {
            getattr(order, attr) for order in orders for attr in attributes
        }

        not_found_barcodes = set(order_barcodes) - found_barcodes
        not_found_barcodes_str = ', '.join(str(not_found_barcode) for not_found_barcode in not_found_barcodes)

        if len(not_found_barcodes) > 0:
            self.check_not_found_orders(not_found_barcodes,not_found_barcodes_str,from_text)
        orders, duplicates = self.check_for_duplicates(order_barcodes,orders)


        if orders:
            duplicates_set = set(duplicates)
            orders = orders.filtered(
                lambda x: not {x.sequence, x.reference_id, x.partner_reference_id, x.follower_ref_id} & duplicates_set
            )
            barcode_items = self.env['rb_delivery.barcode_item']
            added_barcode_items = []
            order_ids = []
            for order in orders:
                if self.barcode_item_ids:
                    new_scanned_order_sequence = self.barcode_item_ids[len(self.barcode_item_ids)-1].scanned_order_sequence
                else:
                    new_scanned_order_sequence = 0
                barcode_item_values = {'scanned_order_sequence': int(new_scanned_order_sequence)+1,'order_id':order.id}
                barcode_item = self.env['rb_delivery.barcode_item'].sudo().create(barcode_item_values)
                if self.scan_then_update and not from_text:
                    self.add_success_message('','success_sound_2.mp3')
                barcode_items += barcode_item
                order_ids.append((4, barcode_item.order_id.id))
                added_barcode_items.append((4, barcode_item.id))

            self.order_ids = order_ids
            values = self.get_order_values()

            if not scan_then_update:
                field_required_messages = self.get_required_fields(values)

                if len(field_required_messages) > 0:
                    for message in field_required_messages:
                        if self.error_messages:
                            self.error_messages = self.error_messages + message +'\n'
                        else:
                            self.error_messages = message +'\n'
                    self.add_error_message(self.error_messages,self.error_messages)
                    return

            self.barcode_item_ids = added_barcode_items

            if not scan_then_update and len(values)>0:
                self.write_on_order(barcode_items,values)
        self.text_order_barcode = ""

    # inherit module[olivery_branch_collection]
    def get_order_values(self):
        values = {}
        customer_payment = 0
        if self.to_order_state:
            values['state'] = self.to_order_state
        if self.assign_to_agent_id:
            values['assign_to_agent'] = self.assign_to_agent_id.id
        if self.reschedule_date:
            values['reschedule_date'] = self.reschedule_date
        if self.reject_reason:
            values['reject_reason'] = self.reject_reason.id
        if self.stuck_comment:
            values['stuck_comment'] = self.stuck_comment
        if self.customer_payment_two:
            customer_payment = float(self.customer_payment_two)
            values['customer_payment_two'] = self.customer_payment_two
        if self.payment_type_two:
            values['payment_type_two'] = self.payment_type_two.id
        if self.customer_payment:
            if customer_payment>0:
                customer_payment += float(self.customer_payment)
                values['customer_payment'] = str(customer_payment)
                values['customer_payment_one'] = self.customer_payment
            else:
                values['customer_payment'] = self.customer_payment
        if self.payment_type:
            values['payment_type'] = self.payment_type.id
        return values


    @api.one
    def convert_data(self):
        if self.text_order_barcode:
            order_barcodes = self.text_order_barcode.splitlines()
            if len(order_barcodes):
                self.check_orders(order_barcodes,from_text=True)

    # ----------------------------------------------------------
    # Read, View
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    @api.model
    def create(self, values):
        barcode = super(rb_delivery_barcode, self).create(values)
        return barcode

    @api.one
    def write(self, values):
        barcode = super(rb_delivery_barcode, self).write(values)
        return barcode

    @api.one
    def unlink(self):
        barcode = super(rb_delivery_barcode, self).unlink()
        return barcode

    @api.one
    @api.returns('self', lambda value: value.id)
    def copy(self, default=None):
        # default = dict(default or {})
        # default.update({
        #     'name': 'name'
        #     })
        barcode = super(rb_delivery_barcode, self).copy(default)
        return barcode

    def print_barcode_label(self):
        order_ids = []
        for barcode_item_id in self.barcode_item_ids:
            order_ids.append(barcode_item_id.order_id.id)

        context = {
            'order_ids' : order_ids
        }
        form_id = self.env.ref('olivery_web_barcode.view_form_rb_delivery_barcode_label').id
        return {
            'type': 'ir.actions.act_window',
            'name': 'Print Barcode',
            'res_model': 'rb_delivery.print_barcode_label',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(form_id, 'form')],
            'target': 'new',
            'context': context
            }



    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
