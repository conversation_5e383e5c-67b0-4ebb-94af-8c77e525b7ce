<?xml version="1.0" encoding="UTF-8"?>

<odoo>

    <record id="view_tree_rb_delivery_barcode" model="ir.ui.view">
        <field name="name">view_tree_rb_delivery_barcode</field>
        <field name="model">rb_delivery.barcode</field>
        <field name="arch" type="xml">
            <tree string="BarCode" create="false">
                <field name="order_barcode"/>
            </tree>
        </field>
    </record>

    <record id="view_form_rb_delivery_barcode" model="ir.ui.view">
        <field name="name">view_form_rb_delivery_barcode</field>
        <field name="model">rb_delivery.barcode</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
                <sheet name="">
                    <field name="show_text_barcode" invisible="1"/>
                    <field name="scan_then_update" invisible="1"/>
                    <div style="padding: 20px; padding-bottom:0px">
                        <button name="convert_data" attrs="{'invisible':[('show_text_barcode', '=', False)]}" string="Done" type="object" style="padding:10px;"/>
                        <button name="change_status" attrs="{'invisible':['|',('scan_then_update', '=', False),('barcode_item_ids', '=', [])]}" string="Change status" type="object" style="padding:10px;"/>
                        <button name="print_runsheet" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Print Runsheet" type="object" style="padding:10px;"/>
                        <button name="print_runsheet_without_barcode" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Print Runsheet without barcode" type="object" style="padding:10px;"/>

                        <button name="print_and_create_runsheet" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Print Create Runsheet" type="object" style="padding:10px;"/>
                        <button name="print_and_create_runsheet_without_barcode" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Print Create Runsheet without barcode" type="object" style="padding:10px;"/>
                        <button name="print_barcode_label" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Print Barcode Label" type="object" style="padding:10px;"/>
                        <button name="clear_orders" attrs="{'invisible':[('barcode_item_ids', '=', [])]}" string="Clear" type="object" style="padding:10px;"/>
                        <button name="clear_errors" attrs="{'invisible':[('error_records', '=',[])]}" string="Clear Errors" type="object" style="padding:10px;"/>
                        <button name="get_orders" attrs="{'invisible':[('barcode_item_ids', '=',[])]}" string="View orders" type="object" style="padding:10px;"/>
                        <button name="get_error_orders" attrs="{'invisible':[('error_records', '=',[])]}" string="View Errors orders" type="object" style="padding:10px;" />
                    </div>
                    <div  style="padding: 20px;">
                        <field name='show_error' invisible="1"/>
                        <field name='show_success' invisible="1"/>
                        <field name="success" string="Success / تم" nolabel="1" style="background-color:#5CB85C !important; text-align:center; color:white; height: 30px;" attrs="{'invisible':[('show_success', '!=', True)]}"/>
                        <field name="error" string="Error / خطأ" nolabel="1" style="background-color:#D9534F !important; text-align:center; color:white; height: 70px !important;max-height: 70px !important;" attrs="{'invisible':[('show_error', '!=', True)]}"/>
                        <div style="margin:1px;display:flex;align-items:center">
                            <label style="font-weight: bold;">Counter:</label> <span style="font-size:150px;margin-inline: 30px;padding-right:30%"><field name="number_of_orders"/></span>

                        </div>
                        <group string="Change order status" name="group_top">

                            <group name="group_left">
                                <field name="show_reschedule_date" invisible="1"/>
                                <field name="show_customer_payment" invisible="1"/>
                                <field name="show_payment_type" invisible="1"/>
                                <field name="show_customer_payment_two" invisible="1"/>
                                <field name="show_payment_type_two" invisible="1"/>
                                <field name="show_agent" invisible="1"/>
                                <field name="show_reject_reason" invisible="1"/>
                                <field name="required_reject_reason" invisible="1"/>
                                <field name="required_reschedule_date" invisible="1"/>
                                <field name="required_payment_type" invisible="1"/>
                                <field name="required_payment_type_two" invisible="1"/>
                                <field name="required_agent" invisible="1"/>
                                <field name="required_customer_payment" invisible="1"/>
                                <field name="required_customer_payment_two" invisible="1"/>
                                <field name="state_id" invisible="1"/>
                                <field name='to_order_state'/>
                                <field name='assign_to_agent_id' attrs="{'invisible': [('show_agent','=',False)],'required':[('required_agent','=',True)]}" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]"/>
                                <field name="reschedule_date" attrs="{'invisible': [('show_reschedule_date','=',False)],'required':[('required_reschedule_date','=',True)]}"/>
                                <field name="reject_reason" attrs="{'invisible': [('show_reject_reason','=',False)],'required':[('required_reject_reason','=',True)]}"  domain="['|',('status','in',[state_id]),('status','=',False)]"/>
                                <field name="stuck_comment" attrs="{'invisible': [('show_reject_reason','=',False)]}"/>
                                <field name="customer_payment" attrs="{'invisible': [('show_customer_payment','=',False)],'required':[('required_customer_payment','=',True)]}"/>
                                <field name="payment_type" attrs="{'invisible': [('show_payment_type','=',False)],'required':[('required_payment_type','=',True)]}"/>
                                <field name="customer_payment_two" attrs="{'invisible': [('show_customer_payment_two','=',False)],'required':[('required_customer_payment_two','=',True)]}"/>
                                <field name="payment_type_two" attrs="{'invisible': [('show_payment_type_two','=',False)],'required':[('required_payment_type_two','=',True)]}"/>
                                <field name="order_barcode" attrs="{'invisible':[('to_order_state', '=', False)]}"/>
                                <field name="order_barcode_temp" invisible="1"/>
                                <field name="text_order_barcode" attrs="{'invisible':['|',('show_text_barcode', '=', False),('to_order_state', '=', False)]}"/>
                            </group>
                            <group name="group_right">
                                <field name="error_records" attrs="{'invisible':[('error_records', '=',[])]}" >
                                    <tree editable="bottom" limit="3" create="0" delete="1">
                                        <field name="order_reference"/>
                                        <field name="name" widget="html" options="{'sanitize': True}"/>
                                        <field name="is_done"/>
                                    </tree>
                                </field>
                            </group>
                        </group>

                        <group>

                        </group>

                        <group>
                        <field name="barcode_item_ids" attrs="{'invisible':[('to_order_state', '=', False)]}">
                            <tree create="false">
                                <field name="scanned_order_sequence" invisible="1"/>
                                <field name="sequence"/>
                                <field name="reference_id"/>
                                <field name="assign_to_business"/>
                                <field name="state_id"/>
                                <field name="customer_name"/>
                                <field name="customer_address"/>
                                <field name="assign_to_agent_id"/>
                                <field name="money_collection_cost"/>
                                <field name="note"/>
                                <field name="create_date" string="Scan Date"/>  
                            </tree>
                        </field>

                        </group>
                    </div>
                </sheet>
        </field>
    </record>


    <record id="view_form_rb_delivery_barcode_label" model="ir.ui.view">
        <field name="name">view_form_rb_delivery_barcode_label</field>
        <field name="model">rb_delivery.print_barcode_label</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <sheet>
                    <group name="group_top">
                        <field name="reports"/>
                    </group>
                </sheet>

                <footer>
                    <button name="print_report" type="object" string="Save"/>
                    <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                </footer>

                </form>
        </field>
    </record>

</odoo>