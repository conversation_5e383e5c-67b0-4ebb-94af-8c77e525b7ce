# -*- coding: utf-8 -*-
{
    'name': "olivery_web_barcode",
    'summary': """
        Olivery Web Barcode App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.3.7',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery','olivery_templates'],

    # always loaded
    'data': [
        'models/barcode/barcode_view.xml',
        'models/collection_barcode/collection_barcode_view.xml',
        'models/runsheet/runsheet_view.xml',
        'demo/delete_barcode_queue.xml',
        'demo/client_conf.xml',
        'views/module_view.xml',
        'views/print/runsheet_by_scanned_sequence_report.xml',
        'security/ir.model.access.csv',
        'models/errors/errors_view.xml',
        'models/barcode_collection/barcode_collection_view.xml'
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
