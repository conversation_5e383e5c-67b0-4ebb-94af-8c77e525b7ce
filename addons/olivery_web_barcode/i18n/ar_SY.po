# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_web_barcode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-24 09:38+0000\n"
"PO-Revision-Date: 2024-01-24 09:38+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:275
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:313
#, python-format
msgid " Orders were processed"
msgstr "تتم معالجة الطلبيات "

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:395
#, python-format
msgid "<b>Duplicate Reference:</b>\n"
" You have scanned %s reference , this reference existing two orders %s for businesess %s \n"
" <b>What to do next:</b> \n"
" Scan on the sequences listed above instead of the reference %s"
msgstr "\"<b>رقم مرجعي مكرر :</b>\n"
" تم قراءة الباركود %s للرقم المرجعي  , هذا الرقم المرجعي مكرر لأكثر من طلبية   %s للتجار  %s \n"
" <b>ما يمكنك فعله :</b> \n"
" قم بقراءة الرقم المتسلسل بدلاً من الرقم المرجعي  %s"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:375
#, python-format
msgid "<b>Existing order: </b> Order %s already scanned."
msgstr "\"<b> الطلب موجود: </b> الطلب  %s تمت قراءته سابقاً"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:362
#, python-format
msgid "<b>Not Found Order:</b>\n"
"Orders of sequences %s do not exist or you have no access to them.\n"
"<b>What to do next:</b> Make sure you have access to orders of sequences %s."
msgstr "<b>لم يتم ايجاد الطلب </b>\n"
"الطلب ذات رقم التسلسل %s غير موجود او لا يمكن الوصول اليه\n"
"<b>ما يمكنك فعله: </b> يرجى التأكد بأنه يمكنك الوصول للطلب %s."

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Address"
msgstr "العنوان"

#. modules: olivery_web_barcode, olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__agent
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__assign_to_agent_id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__agent_id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__assign_to_agent_id
msgid "Agent"
msgstr "السائق"

#. module: olivery_web_barcode
#: selection:rb_delivery.collection_barcode,collection:0
msgid "Agent Collection"
msgstr "كشف تحصيل المندوب"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__agent_collections_ids
msgid "Agent Collections"
msgstr "تحصيل السائق"

#. module: olivery_web_barcode
#: selection:rb_delivery.collection_barcode,collection:0
msgid "Agent Returned Collection"
msgstr "كشف المرتجع للسائق"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__agent_returned_collection_ids
msgid "Agent Returned Collections"
msgstr "كشف رواجع السائق"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:427
#, python-format
msgid "Agent is required. \n"
""
msgstr "حقل السائق مطلوب "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Agent's name:"
msgstr "اسم السائق: "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_tree_rb_delivery_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_tree_rb_delivery_collection_barcode
msgid "BarCode"
msgstr "باركود"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__name
#: model:ir.ui.menu,name:olivery_web_barcode.menu_web_barcode_barcode_configuration
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Barcode"
msgstr "باركود"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_print_barcode_label
msgid "Barcode Label Wizard Model"
msgstr ""

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_barcode_queue
msgid "Barcode queue"
msgstr "سلسلة الطلبيات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__money_collection_cost
msgid "COD"
msgstr "مجموع التحصيل "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode_label
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Car's number:"
msgstr "رقم السيارة"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Change order status"
msgstr "تغير حالة الطرد"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Change status"
msgstr "تغير الحالات"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Clear"
msgstr "مسح"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Clear Errors"
msgstr "مسح الأخطاء"

#. module: olivery_web_barcode
#: selection:rb_delivery.collection_barcode,collection:0
msgid "Collection"
msgstr "التحصيل"

#. module: olivery_web_barcode
#: model:ir.actions.act_window,name:olivery_web_barcode.action_web_barcode_collection_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__collection_barcode
#: model:ir.ui.menu,name:olivery_web_barcode.menu_web_barcode_collection_barcode
msgid "Collection Barcode"
msgstr "باركود التحصيل"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__to_order_state
msgid "Collection Status"
msgstr "حالات الكشوفات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__collection
msgid "Collection Type"
msgstr "نوع لتحصيل"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_collection_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Collection barcode"
msgstr "باركود التحصيل"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:269
#, python-format
msgid "Collection does not exist or you have no access on collection."
msgstr "التحصيل غير موجود أو لا يمكنك الوصول اليه"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__collections_ids
msgid "Collections"
msgstr "كشوفات"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Company Registry:"
msgstr "المشغل مرخص: "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Counter:"
msgstr " : العداد"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__create_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__create_uid
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_search_rb_delivery_barcode_collection
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__create_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__current_branch_id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__current_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__current_branch_id
msgid "Current branch"
msgstr "الفرع الحالي"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:433
#, python-format
msgid "Current branch is required. \n"
""
msgstr "حقل الفرع الحالي مطلوب "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__customer_address
msgid "Customer address"
msgstr "عنوان الزبون "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__customer_name
msgid "Customer name"
msgstr "اسم الزبون"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__customer_payment
msgid "Customer payment"
msgstr "دفعة المستلم"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:459
#, python-format
msgid "Customer payment is required.\n"
""
msgstr "حقل دفعة المستلم مطلوبة "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Date:"
msgstr "التاريخ:"

#. module: olivery_web_barcode
#: model:ir.actions.server,name:olivery_web_barcode.cron_delete_barcode_item_ir_actions_server
#: model:ir.cron,cron_name:olivery_web_barcode.cron_delete_barcode_item
#: model:ir.cron,name:olivery_web_barcode.cron_delete_barcode_item
msgid "Delete Barcode Item"
msgstr "حذف عناصر الباركود"

#. module: olivery_web_barcode
#: model:ir.actions.server,name:olivery_web_barcode.cron_delete_barcode_queue_ir_actions_server
#: model:ir.cron,cron_name:olivery_web_barcode.cron_delete_barcode_queue
#: model:ir.cron,name:olivery_web_barcode.cron_delete_barcode_queue
msgid "Delete Barcode Queue"
msgstr "حذف سلسلة الطلبيات "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__display_name
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__display_name
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__display_name
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__display_name
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__display_name
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Distribution Report"
msgstr "كشف توزيع"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__is_done
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Done"
msgstr "تم"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Driver's signature: ......................................"
msgstr "توقيع السائق : ............................................"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__error
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__error
msgid "Error"
msgstr "خطأ"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Error / خطأ"
msgstr "خطأ"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__error_messages
msgid "Error Messages"
msgstr "الأخطاء "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__name
msgid "Error message"
msgstr "رسالة الخطأ"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__error_records
msgid "Errors"
msgstr "الأخطاء"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_errors
msgid "Errors model"
msgstr ""

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:299
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:309
#, python-format
msgid "Issue in collection "
msgstr "يوجد خطأ في الكشف "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__last_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__last_barcode
msgid "Last BarCode"
msgstr "اخر قرائة للباركود"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode____last_update
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item____last_update
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue____last_update
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode____last_update
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors____last_update
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__write_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__write_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__write_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__write_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__write_uid
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__write_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__write_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_queue__write_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__write_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__write_date
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Logo"
msgstr "الشعار"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:410
#, python-format
msgid "Make sure to choose a status before you start scanning orders."
msgstr "تأكد أنك قمت باختيار الحالة قبل قراءة الباركود "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__driver_id
msgid "Money Collector Name"
msgstr "اسم المحصل"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_multi_whatsapp_runsheet
msgid "Money collection"
msgstr "التحصيل"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Money collection cost"
msgstr "مبلغ التحصيل"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__note
msgid "Note"
msgstr "ملاحظة"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Notes"
msgstr "ملاحظات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__number_of_orders
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__number_of_orders
msgid "Number of orders"
msgstr "عدد الطلبيات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__number_of_success_orders
msgid "Number of success orders"
msgstr "عدد الطلبيات الناجحة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__number_of_failed_orders
msgid "Number of failed orders"
msgstr "عدد الطلبيات الغير الناجحة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__order_id
msgid "Order"
msgstr "الأمر"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:376
#, python-format
msgid "Order %s already scanned"
msgstr "لطلب  %s تم قراءته مسبقاً"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__order_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__text_order_barcode
msgid "Order BarCode"
msgstr "باركود الطلبية"

#. module: olivery_web_barcode
#: model:ir.actions.act_window,name:olivery_web_barcode.action_web_barcode_barcode
#: model:ir.ui.menu,name:olivery_web_barcode.menu_web_barcode_barcode
msgid "Order Barcode"
msgstr "باركود الطلبية"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_errors__order_reference
msgid "Order Reference"
msgstr "رقم الطلب"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__order_seq
msgid "Order Sequence"
msgstr "رقم تسلسل الطلبية"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Order count"
msgstr "عدد الطلبيات"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:513
#, python-format
msgid "Order has been updated using Web Barcode By %s."
msgstr "تم تحديث حالة الطلبية من خلال شاشة  ويب باركود بواسطة %s."

#. module: olivery_web_barcode
#: code:addons/olivery_market/olivery_web_barcode/models/order/order_model.py:58
#: code:addons/olivery_web_barcode/models/order/order_model.py:58
#, python-format
msgid "Order has been updated using Web Barcode."
msgstr "تم تحديث حالة الطلبية من خلال شاشة  ويب باركود"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:363
#: code:addons/olivery_web_barcode/models/order/order_model.py:47
#, python-format
msgid "Order of sequence %s does not exist or you have no access on order"
msgstr "الطلب ذات رقم التسلسل %s غير موجود أو لا يمكنك الوصول اليه"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__barcode_item_ids
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__order_ids
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__order_ids
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Page:"
msgstr "صفحة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__payment_type
msgid "Payment Method"
msgstr "طريقة السداد"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:466
#, python-format
msgid "Payment type is required. "
msgstr "حقل نوع الدفعة مطلوب"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Barcode Label"
msgstr "طباعة ملصق الباركود"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Branch Runsheet"
msgstr "طباعة كشف توزيع الفرع"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Create Runsheet"
msgstr "طباعة / إنشاء كشف التوزيع"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Create Runsheet without barcode"
msgstr "طباعة / إنشاء كشف التوزيع بدون باركود "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Runsheet"
msgstr "طباعة كشف توزيع"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Print Runsheet without barcode"
msgstr "طباعة كشف التوزيع بدون باركود"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Recipient's name"
msgstr "اسم المستلم"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__reference_id
msgid "Reference Id"
msgstr "الرقم المرجعي"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__reject_reason
msgid "Reject Reason"
msgstr "سبب الرفض/التعذر"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:451
#, python-format
msgid "Reject reason is required.\n"
""
msgstr "حقل سبب الرفض مطلوب "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_print_barcode_label__reports
msgid "Reports"
msgstr "التقارير"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__reschedule_date
msgid "Reschedule Date"
msgstr "تاريخ اعادة الجدولة"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:445
#, python-format
msgid "Reschedule date is required.\n"
""
msgstr "حقل تاريخ التأجيل مطلوب "

#. module: olivery_web_barcode
#: selection:rb_delivery.collection_barcode,collection:0
msgid "Returned Collection"
msgstr "كشف المُرجَعات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__returned_collection_ids
msgid "Returned Collections"
msgstr "كشف رواجع"

#. module: olivery_web_barcode
#: model:ir.actions.report,name:olivery_web_barcode.report_rb_delevery_runsheet_action
msgid "Run sheet by scanned sequence"
msgstr "كشف المرجعات حسب تسلسل الطلبيات "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__runsheet_ids
msgid "Runsheet"
msgstr "كشف توزيع"

#. module: olivery_web_barcode
#: selection:rb_delivery.collection_barcode,collection:0
msgid "Runsheet Collection"
msgstr "كشف توزيع"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode_label
msgid "Save"
msgstr "حفظ"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__scanned_order_sequence
msgid "Scanned Order Sequence"
msgstr "تسلسل الطلبيات المقروءة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_order__scanned_order_sequence
msgid "Scanned order sequence"
msgstr "تسلسل الطلبيات المقروءة"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Select Collection"
msgstr "تحديد التحصيل"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__assign_to_business
msgid "Sender"
msgstr "المرسل"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Sender's name"
msgstr "اسم التاجر"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__sequence
msgid "Sequence Number"
msgstr "الرقم المتسلسل"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:506
#, python-format
msgid "Sequence: %s, issue: %s"
msgstr "قراءة: %s, خطأ: %s"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__shelf_id
msgid "Shelf"
msgstr "الرف"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_agent
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__show_agent
msgid "Show Agent"
msgstr "اظهار السائق"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_agent
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__required_agent
msgid "Show Agent Required"
msgstr "حقل اظهار السائق مطلوب"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_current_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__show_current_branch
msgid "Show Current Branch"
msgstr "اظهار الفرع الحالي"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_current_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__required_current_branch
msgid "Show Current Branch Required"
msgstr "حقل اظهار الفرع الحالي مطلوب "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_customer_payment
msgid "Show Customer Payment"
msgstr "اظهار دفعة الزبون"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_customer_payment
msgid "Show Customer Payment Required"
msgstr "حقل اظهار دفعة الزبون مطلوب "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_error
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__show_error
msgid "Show Error"
msgstr "إظهار الأخطاء"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_payment_type
msgid "Show Payment Type"
msgstr "إظهار نوع الدفعة "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_payment_type
msgid "Show Payment Type Requried"
msgstr "حقل إظهار نوع الدفعة مطلوب"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_reject_reason
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_reschedule_date
msgid "Show Reschedule Date"
msgstr "إظهار تاريخ إعادة الجدولة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_reject_reason
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_reschedule_date
msgid "Show Reschedule Date Required"
msgstr "إظهار حقل تاريخ التأجيل مطلوب "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_shelf
msgid "Show Shelf"
msgstr "اظهار الرف"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_success
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__show_success
msgid "Show Success"
msgstr "تم بنجاح"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_text_barcode
msgid "Show Text Barcode"
msgstr "إظهار نص قراءة الباركود"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__show_to_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__show_to_branch
msgid "Show To Branch"
msgstr "أظهار للفرع "

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__required_to_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__required_to_branch
msgid "Show To Branch Required"
msgstr ""

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__scan_then_update
msgid "Show change state button"
msgstr "اظهار زر تغيير الحالة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__state_id
msgid "State id"
msgstr "معرف الحالة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__to_order_state
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__state_id
msgid "Status"
msgstr "الحالة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__status_ids
msgid "Statuses"
msgstr "الحالات"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__stuck_comment
msgid "Stuck Comment"
msgstr "ملاحظات عالقة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__success
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__success
msgid "Success"
msgstr "إلغاء "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "Success / تم"
msgstr "تم"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__order_barcode_temp
msgid "Temp"
msgstr ""

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__to_branch_id
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_item__to_branch
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_collection_barcode__to_branch_id
msgid "To branch"
msgstr "للفرع"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:439
#, python-format
msgid "To branch is required. \n"
""
msgstr "حقل للفرع مطلوب \n"
""

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.runsheet_by_scanned_sequence
msgid "Total Money Collection"
msgstr "مجموع التحصيل "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "View Errors orders"
msgstr "إظهار أخطاء الطلبية "

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "View orders"
msgstr "إظهار الطلبيات "

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:396
#, python-format
msgid "You have scanned %s reference , this reference existing two orders %s for businesess %s"
msgstr "قمت بقراءة  %s الرقم المرجعي  ,   رقم المرجعي موجود لأكثر من %s مرسل  %s"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/barcode/barcode_model.py:514
#, python-format
msgid "Your order has been successfully updated."
msgstr "تم تعديل حالة الطلب بنجاح"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_barcode
msgid "rb_delivery.barcode"
msgstr " rbמשלוח . ברקוד"

#. module: olivery_web_barcode
#: model:ir.model,name:olivery_web_barcode.model_rb_delivery_barcode_item
msgid "rb_delivery.barcode_item"
msgstr ""

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_collection_barcode
msgid "<span style=\"display: flex;align-items: end;position: relative;font-size: 26px;color: #3C4048;\">Select Collection</span>"
msgstr "<span style=\"display: flex;align-items: end;position: relative;font-size: 26px;color: #3C4048;\">اختيار نوع التحصيل</span>"

#. module: olivery_web_barcode
#: code:addons/olivery_web_barcode/models/collection_barcode/collection_barcode_model.py:269
#, python-format
msgid "Issue in collection %s %s"
msgstr "خطأ في كشف التحصيل %s %s"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__scan_date
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_search_rb_delivery_barcode_collection
msgid "Scan date"
msgstr "تاريخ القراءة"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode_collection__order_seqs
msgid "Orders sequences"
msgstr "الارقام المتسلسلة للطلبيات"

#. module: olivery_web_barcode
#: model:ir.actions.act_window,name:olivery_web_barcode.action_rb_delivery_barcode_collection
#: model:ir.ui.menu,name:olivery_web_barcode.menu_rb_delivery_barcode_collection
msgid "Barcode collection logs"
msgstr "سجلات كشوفات الباركود"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__payment_type_two
msgid "Second payment method"
msgstr "طريقة الدفع الثانية"

#. module: olivery_web_barcode
#: model:ir.model.fields,field_description:olivery_web_barcode.field_rb_delivery_barcode__customer_payment_two
msgid "Second customer payment"
msgstr "قيمة الدفعة بالطريقة الثانية"

#. module: olivery_web_barcode
#: model_terms:ir.ui.view,arch_db:olivery_web_barcode.view_form_rb_delivery_barcode
msgid "Scan Date"
msgstr "تاريخ القراءة"
