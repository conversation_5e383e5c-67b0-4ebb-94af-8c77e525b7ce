odoo.define("rb_delivery.whatsapp_multi_button", function (require) {

    "use strict";

    let ListController = require("web.ListController");
    let session = require('web.session');
    var core = require('web.core');
    var _t = core._t;
    var framework = require('web.framework');

    var FormController = require('web.FormController');

    FormController.include({
        _onButtonClicked: function (event) {
            if (event && event.data && event.data.record && event.data.record.model &&  event.data.attrs && event.data.attrs.name ) {
                    if(event.data.record.model == "rb_delivery.whatsapp_attachment" && event.data.attrs.name === "download_attachment"){
                        event.stopPropagation();
                        this._rpc({
                            model: 'rb_delivery.whatsapp_attachment',
                            method: 'download_attachment',
                            args: [[event.data.record.res_id]],
                        }).then(function (result) {
                            window.open(result.url, '_blank');
                        });
                        return;
                    }
                    if(event.data.record.model == "rb_delivery.money_collection_send_whatsapp" && event.data.attrs.name === "download_all_attachments" && this.initialState && this.initialState.data && this.initialState.data.whatsapp_attachment_ids && this.initialState.data.whatsapp_attachment_ids.res_ids){
                        event.stopPropagation();
                        this._rpc({
                            model: 'rb_delivery.money_collection_send_whatsapp',
                            method: 'download_all_attachments',
                            args: [[],this.initialState.data.whatsapp_attachment_ids.res_ids],
                        }).then(function (result) {
                            window.open(result.url, '_blank');
                        });
                        return;
                    }
                
            }
            return this._super.apply(this, arguments);
        },
    });
    ListController.include({
       renderButtons: function($node) {
            this._super.apply(this, arguments);
            if (this.$buttons) {
                this.$buttons.find(".oe_action_button_retrieve_qr_code").click(this.proxy("get_qr_code"));
                this.$buttons.find(".oe_action_button_get_chat_list").click(this.proxy("get_chat_list"));
                this.$buttons.find(".oe_action_button_refresh_groups").click(this.proxy("refresh_groups"));
            }
            session.user_has_group("rb_delivery.role_super_manager").then(function(has_group) {
                if(!has_group) {
                    $(".oe_action_button_retrieve_qr_code" ).attr("style", "display: none!important;");
                }
            });
       },
       get_qr_code:function(){
            var action = {
                'type': 'ir.actions.act_window',
                'name': _t('Scan QR'),
                'res_model': 'rb_delivery.scan_qr',
                'views': [[false, 'form']],
                'view_type': 'form',
                'view_mode': 'tree,form',
                'target': 'new',
            };
            var self = this
            setTimeout(function () {
                framework.unblockUI();
                self.do_action({ type: 'ir.actions.act_window_close' });
            },120000)
            return this.do_action(action);

       },
       get_chat_list: function(){
        var action = {
            'type': 'ir.actions.act_window',
            'name': _t('Chat List Generator'),
            'res_model': 'rb_delivery.chat_list_wizzered',
            'views': [[false, 'form']],
            'view_type': 'form',
            'view_mode': 'tree,form',
            'target': 'new',
        };
         
        return this.do_action(action);
   },

   refresh_groups: function(){
        var self = this;

        return self._rpc({
            model: 'rb_delivery.whatsapp_chat_list',
            method: 'refresh_groups',
            args: [[]]
        }).then(function(chat_list){
            self.reload()
        })
    }

   


})
})
