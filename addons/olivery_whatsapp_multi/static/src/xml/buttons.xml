<?xml version="1.0" encoding="UTF-8"?>
  <template xml:space="preserve">
    <t t-extend="ListView.buttons">
      <t t-jquery="div.o_list_buttons" t-operation="prepend">
        <button  t-if="widget.modelName == 'rb_delivery.whatsapp_chat_list'"  class="btn btn-secondary oe_action_button_retrieve_qr_code" type="button" accesskey="a" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager">Scan QR</button>
        <button  t-if="widget.modelName == 'rb_delivery.whatsapp_chat_list'"  class="btn btn-secondary oe_action_button_get_chat_list" type="button" accesskey="a" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager">Get Chat List</button>
        <button  t-if="widget.modelName == 'rb_delivery.whatsapp_chat_list'"  class="btn btn-secondary oe_action_button_refresh_groups" type="button" accesskey="a" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager">Refresh Groups</button>
        </t>
    </t>

  </template>
