
from time import sleep
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime, timedelta
from odoo.tools import html2plaintext
import base64
import zipfile
import io
class rb_delivery_collection_send_whatsapp(models.TransientModel):
    _name="rb_delivery.money_collection_send_whatsapp"
    _description = "Collection Send Whatsapp Model"

    @api.model
    def _get_business_users(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(self._context.get('active_ids'))
        business_ids = []
        for rec in recs:
            if rec.sudo().business_id:
                business_ids.append(rec.sudo().business_id.id)
        return [(6,0,business_ids)]

    @api.model
    def _get_body(self):
        template = self.env['mail.template'].sudo().search([('model_id','=','rb_delivery.multi_print_orders_money_collector')])
        if template:
            body = template[0].body_html
            return body

    @api.model
    def _get_attachments(self):
        recs = self.env['rb_delivery.multi_print_orders_money_collector'].browse(
                self._context.get('active_ids'))
        vals = []
        vals.extend({"collection_id": rec.id} for rec in recs)
        attachment_ids = self.env['rb_delivery.whatsapp_attachment'].sudo().create(vals)
        self.env.cr.commit()
        attachment_ids = [(6, 0, attachment_ids.ids)]
        return attachment_ids
    
    @api.model
    def _get_send_eta(self):
        if not self._context.get('active_ids'):
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        number_recs = len(self._context.get('active_ids'))
        if number_recs > 1:
            time_needed = number_recs * 10
            eta = datetime.now() + timedelta(seconds=time_needed)
            return eta.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


    @api.depends('partner_ids')
    def _get_no_whatsapp_group_users(self):
        user_ids = []
        if self.partner_ids:
            for user in self.partner_ids:
                if not user.new_group_id:
                    user_ids.append(user.id)
        self.no_whatsapp_group_users = [(6,0,user_ids)]

    partner_ids = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'Recipients',
        relation = 'whatsapp_item_recipient_money_collection_item',
        column1 = 'money_collection_id',
        column2 = 'recipients_id',default=_get_business_users)

    no_whatsapp_group_users = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'User with no WhatsApp group',
        relation = 'whatsapp_item_no_whatsapp_group_returned_collection_item',
        column1 = 'returned_collection_id',
        column2 = 'user_ids',compute="_get_no_whatsapp_group_users")


    body = fields.Html("Body", default=_get_body, required=True)

    whatsapp_attachment_ids = fields.Many2many(comodel_name = 'rb_delivery.whatsapp_attachment',
        string = 'Attachments',
        relation = 'whatsapp_attachments_money_collection_item',
        column1 = 'money_collection_id',
        column2 = 'whatsapp_attachment_id',default=_get_attachments)

    send_to_whatsapp_group = fields.Boolean('Send to WhatsApp group')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    send_time_eta = fields.Datetime('Send time ETA', default=_get_send_eta)

    def submit(self):
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].browse(self._context.get('active_ids'))
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        if whatsapp_provider_config.provider == 'cloud_ways':
            self.with_delay(channel="root.basic",max_retries=2).send_whatsapp(collections)
        else:
            self.with_delay(channel="root.whatsapp_scheduler",max_retries=2).send_old_whatsapp(collections)

    def send_whatsapp(self,collections):
        attachment_arr = []
        mobile_arr = []
        business_arr = []
        collection_arr = []
        body_arr =[]
        user = self.env['res.users'].sudo().search([('id','=',self._uid)])
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        
        for collection in collections:
            mobile_number = ''
            if collection.sudo().business_id:
                business_id = collection.sudo().business_id
                body = html2plaintext(self.body)
                if self.send_to_whatsapp_group:
                    group_id_app = business_id.get_group_id(self.device)
                    if group_id_app:
                        mobile_number = group_id_app
                else:
                    if business_id.whatsapp_mobile:
                        mobile_number = business_id.whatsapp_mobile
                    else:
                        no_space_first_number=business_id.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                if mobile_number:
                    mobile_arr.append([mobile_number])
                    attachment_arr.extend(
                        attachment.attachment_id
                        for attachment in self.whatsapp_attachment_ids
                        if attachment.collection_id and attachment.collection_id.id == collection.id)
                    business_arr.append([business_id])
                    collection_arr.append(collection)
                    body_arr.append(body)
                    collection.message_post(body=_("WhatsApp message was sent by %s") % (user.name))

        if len(mobile_arr)>0:
            self.env['rb_delivery.whatsapp_center'].prepare_bulk_whatsapp_messages(body_arr,mobile_arr,business_arr,collection_arr,'rb_delivery.multi_print_orders_money_collector',attachment_arr,self.send_to_whatsapp_group,device=self.device)
    
    def download_all_attachments(self,attachment_ids):
        if not attachment_ids:
            return
        whatsapp_attachment_ids = self.env['rb_delivery.whatsapp_attachment'].sudo().browse(attachment_ids)
        if not whatsapp_attachment_ids:
            return
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for whatsapp_attachment_id in whatsapp_attachment_ids:
                attachment = whatsapp_attachment_id.attachment_id
                if attachment.datas:
                    pdf_data = base64.b64decode(attachment.datas)
                    zipf.writestr(attachment.name+".pdf" or "file.pdf", pdf_data)

        zip_buffer.seek(0)
        zip_content = base64.b64encode(zip_buffer.read())

        zip_attachment = self.env['ir.attachment'].create({
            'name': 'attachments.zip',
            'datas': zip_content,
            'datas_fname': 'attachments.zip',
            'res_model': self._name,
            'res_id': self.id,
            'type': 'binary',
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{zip_attachment.id}?download=true',
            'target': 'self',
        }
    
    def send_old_whatsapp(self,collections):
        for collection in collections:
            mobile_number = ''
            if collection.sudo().business_id:
                business_id = collection.sudo().business_id
                attachment_id = next(
                    (attachment.attachment_id for attachment in self.whatsapp_attachment_ids
                    if attachment.collection_id and attachment.collection_id.id == collection.id),
                    None)

                body = html2plaintext(self.body)
                if self.send_to_whatsapp_group:
                    group_id_app = business_id.get_group_id(self.device)
                    if group_id_app:
                        mobile_number = group_id_app
                else:
                    if business_id.whatsapp_mobile:
                        mobile_number = business_id.whatsapp_mobile
                    else:
                        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
                        no_space_first_number=business_id.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                if mobile_number:
                    user = self.env['res.users'].sudo().browse([self._uid])
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(body,[mobile_number],[business_id],collection,'rb_delivery.multi_print_orders_money_collector',attachment_id,self.send_to_whatsapp_group,device=self.device)
                    collection.message_post(body=_("WhatsApp message was sent by %s") % (user.name))
                    sleep(5)



