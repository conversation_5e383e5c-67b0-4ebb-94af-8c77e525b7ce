<odoo>
  <data>
    <record id="view_form_multi_notify_users" model="ir.ui.view">
        <field name="name">view_form_multi_notify_users</field>
        <field name="model">rb_delivery.notify_users</field>
        <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_notify_users" />
        <field name="arch" type="xml">
            <field name="template" position="replace">
                <field name="template" attrs="{'invisible':[('notification_type','!=','is_email'),('notification_type','!=','is_whatsapp')]}"/>
                <field name="attachment" attrs="{'invisible':[('notification_type','!=','is_whatsapp')]}"/>
                <field name="notification_type" widget="radio" invisible="1"/>
                <field name="device" attrs="{'invisible':[('notification_type','!=','is_whatsapp')]}"/>
                <field name="send_to_whatsapp_group" attrs="{'invisible':[('notification_type','!=','is_whatsapp')]}"/>
            </field>

        </field>
    </record>
  </data>
</odoo>