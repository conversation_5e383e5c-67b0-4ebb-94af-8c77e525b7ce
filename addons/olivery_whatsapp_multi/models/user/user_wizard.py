
from openerp import models, fields, api,_
import re



class olivery_multi_whatsapp_notify_users(models.TransientModel):

    _inherit = 'rb_delivery.notify_users'

    notification_type = fields.Selection([
        ('is_email','Email'),
        ('is_notification','Notification'),
        ('is_sms','Sms'),('is_whatsapp','Whatsapp')
    ],default='is_notification')

    attachment = fields.Binary('Attachment',relation="attach_notify_business")

    attachment_id = fields.Many2one('ir.attachment',compute="change_attach")

    send_to_whatsapp_group = fields.Boolean('Send to WhatsApp group')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    @api.depends('attachment')
    def change_attach(self):
        if self.attachment:
            attachment_id = self.env['ir.attachment'].create({
                'name': "attachment",
                'type': 'binary',
                'datas': self.attachment,
                'res_model': 'rb_delivery.order',
                'public':True
            })
            if attachment_id:
                self.attachment_id = attachment_id.id
        self.env.cr.commit()

    def whatsapp_notify(self,selected_users):
        attachment_arr = []
        mobile_arr = []
        users_arr = []
        order_arr = []
        body_arr =[]
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        for user in selected_users:
            mobile_numbers = []

            if self.send_to_whatsapp_group:
                group_id_app = user.get_group_id(self.device)
                if group_id_app:
                    mobile_numbers.append(group_id_app)
            else:
                if user.whatsapp_mobile:
                    mobile_number = user.whatsapp_mobile
                    mobile_numbers.append(mobile_number)
                else:
                    no_space_first_number=user.mobile_number.strip()
                    if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                    mobile_number = prefix_one+no_space_first_number
                    mobile_numbers.append(mobile_number)
            if len(mobile_numbers)>0:
                if self.attachment_id:
                    attachment_arr.append(self.attachment_id)
                mobile_arr.append(mobile_numbers)
                users_arr.append(selected_users)
                custom_message = self.message or ""
                if '{' in custom_message and '}' in custom_message:
                    placeholders = re.findall(r'{(.*?)}', custom_message)
                    for field in placeholders:
                        value = getattr(user, field, '')
                        custom_message = custom_message.replace(f'{{{field}}}', str(value or ''))
                order_arr.append(user)
                body_arr.append(custom_message)
        try:
            if len(mobile_arr)>0:
                self.env['rb_delivery.whatsapp_center'].prepare_bulk_whatsapp_messages(body_arr,mobile_arr,users_arr,order_arr,'rb_delivery.user',attachment_arr,self.send_to_whatsapp_group, device=self.device)
        except Exception as e:
            pass


    def whatsapp_old_notify(self,selected_users):
        for user in selected_users:
            mobile_numbers = []

            if self.send_to_whatsapp_group:
                group_id_app = user.get_group_id(self.device)
                if group_id_app:
                    mobile_numbers.append(group_id_app)
            else:
                if user.whatsapp_mobile:
                    mobile_number = user.whatsapp_mobile
                    mobile_numbers.append(mobile_number)
                else:
                    prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
                    no_space_first_number=user.mobile_number.strip()
                    if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                    mobile_number = prefix_one+no_space_first_number
                    mobile_numbers.append(mobile_number)
            try:
                if len(mobile_numbers)>0:
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(self.message,mobile_numbers,selected_users,user,'rb_delivery.user',self.attachment_id,self.send_to_whatsapp_group,device=self.device)
            except:
                pass

    def notify(self):
        if self.notification_type == 'is_whatsapp':
            if self.template:
                self.message = self.env['rb_delivery.whatsapp_center'].get_html_message(self.template.body_html)
            users = self.env['rb_delivery.user'].browse(self._context.get('active_ids'))
            whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
            if whatsapp_provider_config.provider == 'cloud_ways':
                self.whatsapp_notify(users)
            else:
                self.with_delay(channel="root.basic",max_retries=2).whatsapp_old_notify(users)
        else:
            return super(olivery_multi_whatsapp_notify_users,self).notify()

    @api.onchange('template')
    def _onchange_template(self):
        if self.template:
            self.header = self.template.subject or ''
            self.message = self.env['rb_delivery.whatsapp_center'].get_html_message(self.template.body_html) or ''
