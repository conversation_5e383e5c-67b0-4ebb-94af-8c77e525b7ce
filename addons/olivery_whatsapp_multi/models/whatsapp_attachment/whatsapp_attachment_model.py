# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from datetime import datetime
import base64
class rb_delivery_whatsapp_attachment(models.Model):

    _name = 'rb_delivery.whatsapp_attachment'
    _inherit = 'mail.thread'
    _order = "create_date DESC"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    @api.depends("collection_id")
    @api.multi
    def _get_attachment(self):
        for rec in self:
            if not rec.collection_id:
                rec.attachment_id = False
            collection = rec.collection_id
            pdf = self.env.ref('olivery_whatsapp_multi.report_rb_delivery_order_multi_print_orders_money_collector_action').render_qweb_pdf(collection.id)
            create_date = datetime.strftime(collection.create_date, "%Y-%m-%d")
            if collection.sudo().business_id:
                business_name = collection.sudo().business_id.username
                if collection.sudo().business_id.commercial_name:
                    business_name = collection.sudo().business_id.commercial_name
            else:
                business_name = collection.name
            file_name =  business_name + '_' + create_date
            file_name = file_name.replace('.','_')
            attachment_id = self.env['ir.attachment'].create({
                'name': file_name,
                'type': 'binary',
                'datas': base64.encodestring(pdf[0]),
                'res_model': 'rb_delivery.multi_print_orders_money_collector',
                'res_id': collection.id,
                'datas_fname':file_name,
                'public':True
            })

            rec.attachment_id = attachment_id.id


    collection_id = fields.Many2one("rb_delivery.multi_print_orders_money_collector","Collection")

    attachment_id = fields.Many2one('ir.attachment', string='Attachment',compute="_get_attachment",store=True)

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------
    
    def download_attachment(self):
        self.ensure_one()
        if not self.attachment_id:
            return {'type': 'ir.actions.act_window_close'}

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{self.attachment_id.id}?download=true',
            'target': 'self',
        }

    