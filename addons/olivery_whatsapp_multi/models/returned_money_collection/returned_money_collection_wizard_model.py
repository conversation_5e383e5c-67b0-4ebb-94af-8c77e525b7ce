
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime
from odoo.tools import html2plaintext

class rb_delivery_returned_collection_send_whatsapp(models.TransientModel):
    _name="rb_delivery.returned_collection_send_whatsapp"
    _description = "Returned Collection Send Whatsapp Model"

    @api.model
    def _get_business_users(self):
        recs = self.env['rb_delivery.returned_money_collection'].browse(self._context.get('active_ids'))
        business_ids = []
        for rec in recs:
            if rec.sudo().business_id:
                business_ids.append(rec.sudo().business_id.id)
        return [(6,0,business_ids)]

    @api.model
    def _get_body(self):
        template = self.env['mail.template'].sudo().search([('model_id','=','rb_delivery.returned_money_collection')])
        if template:
            body = template[0].body_html
            return body

    @api.model
    def _get_attachments(self):
        import base64
        recs = self.env['rb_delivery.returned_money_collection'].browse(
                self._context.get('active_ids'))
        attachments=[]
        for rec in recs:

            pdf = self.env.ref('olivery_whatsapp_multi.report_rb_delevery_returned_money_collection_action').render_qweb_pdf(rec.id)
            create_date = datetime.strftime(rec.create_date, "%Y-%m-%d")
            if rec.sudo().business_id:
                business_name = rec.sudo().business_id.username
                if rec.sudo().business_id.commercial_name:
                    business_name = rec.sudo().business_id.commercial_name
            else:
                business_name = rec.name
            file_name =  business_name + '_' + create_date
            file_name = file_name.replace('.','_')
            attachment_id = self.env['ir.attachment'].create({
                'name': file_name,
                'type': 'binary',
                'datas': base64.encodestring(pdf[0]),
                'res_model': 'rb_delivery.returned_money_collection',
                'res_id': rec.id,
                'datas_fname':file_name,
                'public':True
            })
            attachments.append(attachment_id.id)
        self.env.cr.commit()
        attachment_ids = [(6, 0, attachments)]
        return attachment_ids

    @api.depends('partner_ids')
    def _get_no_whatsapp_group_users(self):
        user_ids = []
        if self.partner_ids:
            for user in self.partner_ids:
                if not user.new_group_id:
                    user_ids.append(user.id)
        self.no_whatsapp_group_users = [(6,0,user_ids)]

    partner_ids = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'Recipients',
        relation = 'whatsapp_item_recipient_returned_collection_item',
        column1 = 'returned_collection_id',
        column2 = 'recipients_id',default=_get_business_users)

    no_whatsapp_group_users = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'User with no WhatsApp group',
        relation = 'whatsapp_item_no_whatsapp_group_returned_collection_item',
        column1 = 'returned_collection_id',
        column2 = 'user_ids',compute="_get_no_whatsapp_group_users")


    body = fields.Html("Body", default=_get_body, required=True)

    attachment_ids = fields.Many2many(comodel_name = 'ir.attachment',
        string = 'Attachments',
        relation = 'whatsapp_item_attachments_returned_collection_item',
        column1 = 'returned_collection_id',
        column2 = 'attachment_id',default=_get_attachments)

    send_to_whatsapp_group = fields.Boolean('Send to WhatsApp group')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    def submit(self):
        collections = self.env['rb_delivery.returned_money_collection'].browse(self._context.get('active_ids'))
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        if whatsapp_provider_config.provider == 'cloud_ways':
            self.with_delay(channel="root.basic",max_retries=2).send_whatsapp(collections)
        else:
            self.with_delay(channel="root.basic",max_retries=2).send_old_whatsapp(collections)


    def send_whatsapp(self,collections):
        attachment_arr = []
        mobile_arr = []
        business_arr = []
        collection_arr = []
        body_arr =[]
        user = self.env['res.users'].sudo().search([('id','=',self._uid)])
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        
        for collection in collections:
            mobile_number = ''
            if collection.sudo().business_id:
                business_id = collection.sudo().business_id

                body = html2plaintext(self.body)
                if self.send_to_whatsapp_group:
                    group_id_app = business_id.get_group_id(self.device)
                    if group_id_app:
                        mobile_number = group_id_app
                else:
                    if business_id.whatsapp_mobile:
                        mobile_number = business_id.whatsapp_mobile
                    else:
                        no_space_first_number=business_id.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                if mobile_number:
                    mobile_arr.append([mobile_number])
                    business_arr.append([business_id])
                    for attachment in self.attachment_ids:
                        if attachment.res_id == collection.id:
                            attachment_arr.append(attachment)
                            break
                    collection_arr.append(collection)
                    body_arr.append(body)
                    collection.message_post(body=_("WhatsApp message was sent by %s") % (user.name))

        if len(mobile_arr):
            self.env['rb_delivery.whatsapp_center'].prepare_bulk_whatsapp_messages(body_arr,mobile_arr,business_arr,collection_arr,'rb_delivery.returned_money_collection',attachment_arr,self.send_to_whatsapp_group,device=self.device)


    def send_old_whatsapp(self,collections):
        for collection in collections:
            mobile_number = ''
            if collection.sudo().business_id:
                business_id = collection.sudo().business_id
                attachment_id = False
                for attachment in self.attachment_ids:
                    if attachment.res_id == collection.id:
                        attachment_id = attachment
                        break

                body = html2plaintext(self.body)
                if self.send_to_whatsapp_group:
                    group_id_app = business_id.get_group_id(self.device)
                    if group_id_app:
                        mobile_number = group_id_app
                else:
                    if business_id.whatsapp_mobile:
                        mobile_number = business_id.whatsapp_mobile
                    else:
                        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
                        no_space_first_number=business_id.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                if mobile_number:
                    user = self.env['res.users'].sudo().browse([self._uid])
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(body,[mobile_number],[business_id],collection,'rb_delivery.returned_money_collection',attachment_id,self.send_to_whatsapp_group,device=self.device)
                    collection.message_post(body=_("WhatsApp message was sent by %s") % (user.name))
