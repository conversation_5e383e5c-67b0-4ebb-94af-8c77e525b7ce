# -*- coding: utf-8 -*-
from openerp import models, fields,_, api
from datetime import datetime, date
import re
import requests
import time
import random
from openerp.exceptions import ValidationError
from dateutil.relativedelta import relativedelta
import pytz
import logging
from odoo.osv import osv
_logger = logging.getLogger(__name__)

class rb_delivery_whatsapp_chat_list(models.Model):

    _name = 'rb_delivery.whatsapp_chat_list'

    message = fields.Char('Message',readonly=True)

    author = fields.Char('Author',readonly=True)

    author_name = fields.Char('Author Name', readonly=True)

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    is_whatsapp_group_message = fields.<PERSON><PERSON>an('Configured Whatsapp group message',readonly=True,default=False)

    def get_groups(self,whatsapp_provider_config,override,message,device=False):
        confs = self.env['rb_delivery.client_configuration'].get_param('chatapi_url')

        def get_url(whatsapp_provider_config, device):
            provider = device and device.provider or whatsapp_provider_config.provider
            if provider == 'cloud_ways':
                return whatsapp_provider_config.cloud_ways_url + '/api/v1.0/whatsapp/group/get-group-list?XDEBUG_SESSION_START=PHPSTORM'
            elif provider == 'whapi':
                return whatsapp_provider_config.whapi_url + '/groups'
            elif provider == 'api_chat':
                return confs + '/conversations'
            else:
                return False

        domain = [('active', '=', True)]
        provider = device and device.provider or whatsapp_provider_config.provider
        if not override:
            domain.append(('new_group_id', '=', False))

        users = self.env['rb_delivery.user'].sudo().search(domain)
        users_names = ''
        users_names_multiple_groups = ''
        users_issues = self.env['rb_delivery.user']
        url =  get_url(whatsapp_provider_config, device) 
        headers = whatsapp_provider_config.get_headers_by_device(device)
        if url:
            if message and provider == 'cloud_ways':
                url += '&keyword=' + message
            if provider == 'api_chat':
                headers['Accept-Encoding'] = 'identity'
            res = requests.request("GET", url, headers=headers, data="")
            _logger.info('########################################')
            _logger.info(url)
            _logger.info('########################################')
            if res.status_code == 200:
                groups = res.json()['data'] if 'data' in res.json() else res.json()['groups'] if 'groups' in res.json() else res.json()
                _logger.info('########################################')
                _logger.info(groups)
                _logger.info('########################################')
                group_id_key = 'id' if provider in ['cloud_ways', 'whapi'] else 'number'
                if not groups or len(groups) == 0:
                    raise ValidationError(_("No groups were found for your device."))
                for group in groups:
                    if group.get('name') and group.get(group_id_key):
                        if provider == 'api_chat' and not group.get('is_group'):
                            continue

                        values = {}
                        group_name = group.get('name')
                        filtered_user = users.filtered(lambda u: u.commercial_name and u.commercial_name.lower() in group_name.lower())
                        group_id = group.get(group_id_key)
                        values['author_name'] = group_name
                        values['message'] = group_id
                        if device:
                            values['device'] = device.id
                        record = self.env['rb_delivery.whatsapp_chat_list'].search([('message', '=', group_id)], limit=1)
                        if values and not record:
                            record = self.env['rb_delivery.whatsapp_chat_list'].sudo().create(values)
                        for user in filtered_user:
                            write_vals = {}
                            if user:
                                if record:
                                    record.is_whatsapp_group_message = True
                                if override:
                                    write_vals['new_group_id'] = group_id
                                if record and not record in user.whatsapp_group_ids:
                                    write_vals['whatsapp_group_ids'] = [(4, record.id)]
                                if record and not record in user.whatsapp_device_group_ids:
                                    write_vals['whatsapp_device_group_ids'] = [(4, record.id)]
                                if write_vals:
                                    user.write(write_vals)
                                if len(user.whatsapp_group_ids)==1:
                                    users_names += user.username + ' '
                                else:
                                    users_issues += user
                                    users_names_multiple_groups += user.username + ' '


        if users_names and len(users_names) > 0:
            message = _('Whatsapp group id set successfully for %s')%(users_names)
            self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
        return users_issues

    def get_chat_list(self, override=True, message='', device=False):
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        provider = device and device.provider or whatsapp_provider_config.provider
        _logger.info('########################################')
        _logger.info(whatsapp_provider_config)
        _logger.info(provider)
        _logger.info('########################################')
        return self.get_groups(whatsapp_provider_config, override, message, device)

    def clear_inactive_chat_lists(self):
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        date = datetime.now(pytz.timezone(timezone))
        fmt = "%Y-%m-%d"
        date = datetime.strftime(date,fmt) + ' 00:00:00'
        records = self.env['rb_delivery.whatsapp_chat_list'].sudo().search([('create_date','>',date)])
        batch_list = []
        batch_list = [records[i:i + 1000] for i in range(0, len(records), 1000)]
        for batch in batch_list:
                self.with_delay(channel="root.archive_whatsapp",max_retries=2).archive_chat_lists(batch)

        return True

    def archive_chat_lists(self,records):
        records.sudo().unlink()
        return True
    

    def refresh_groups(self):
        domain = [('active', '=', True)]
        users = self.env['rb_delivery.user'].search(domain)
        if users:
            users._compute_main_group_id()
            
            message = _('Whatsapp groups updated successfully for %s')%(users.mapped('username'))
            self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
    


class whatsapp_chat_list_wizzered(models.TransientModel):
    _name = 'rb_delivery.chat_list_wizzered'
    _description = "Chat List Wizzered model"

    override_existing = fields.Boolean('Override existing groups')
    key_word = fields.Char('Keyword')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    def submit_request(self):
        result = self.env['rb_delivery.whatsapp_chat_list'].get_chat_list(self.override_existing,self.key_word,self.device)
        if result:
            warning = self.env['rb_delivery.chat_list_warning'].create({'names': result.mapped('username'), 'users': [(6,0,result.ids)]})
            return {
                'type': 'ir.actions.act_window',
                'name': _('Warning: some users has multiple groups added'),
                'res_model': 'rb_delivery.chat_list_warning',
                'views': [[False, 'form']],
                'view_type': 'form',
                'res_id': warning.id,
                'view_mode': 'tree,form',
                'target': 'new',
            }
        return
    


class chat_list_warning(osv.osv):
    _name = "rb_delivery.chat_list_warning"
    _description = "Chat list warning "

    names = fields.Char('names')
    users = fields.Many2many(
        comodel_name = 'rb_delivery.user',
        string = 'Users',
        relation = 'whatsapp_item_users_issues',
    )

    def go_to_users(self):

        users_page = self.env.ref('rb_delivery.view_tree_rb_delivery_user').id
        domain = [('id', 'in', self.users.ids),('active','=',True)]
        context = {
            'model':'rb_delivery.user',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': 'Users groups',
            'res_model': 'rb_delivery.user',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(users_page, 'tree'),(False, 'form')],
            'target': 'current',
            'context': context,
            'domain': domain
            }


