<odoo>
  <data>
    <record id="view_form_rb_delivery_whatsapp_center" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_whatsapp_center</field>
      <field name="model">rb_delivery.whatsapp_center</field>

      <field name="arch" type="xml">
        <form create="0">

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>

            <group name="group_top">
              <group name="group_left">
              <field name="model_name"/>
                <field name="user_id"/>
                <field name="record_id"/>
              </group>
              <group name="group_right">
                <field name="notification_message"/>
              </group>
            </group>
          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_whatsapp_center" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_whatsapp_center</field>
      <field name="model">rb_delivery.whatsapp_center</field>

      <field name="arch" type="xml">
        <tree create="0">
          <field name="model_name"/>
          <field name="user_id"/>
          <field name="record_id"/>
          <field name="notification_message"/>
          <field name="status"/>
          <field name="status_message"/>
          <field name="create_uid"/>
          <field name="create_date"/>
        </tree>
      </field>

    </record>

    <record id="view_search_rb_delivery_whatsapp_center" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_whatsapp_center</field>
      <field name="model">rb_delivery.whatsapp_center</field>

      <field name="arch" type="xml">
        <search >

        <group>
            <field name="name"/>
            <field name="record_id"/>
        </group>

          <group string="Groups">
            <filter name="group_by_model" string="By Model" icon="terp-partner" context="{'group_by':'model_name'}"/>
            <filter name="group_by_user" string="By User" icon="terp-partner" context="{'group_by':'user_id'}"/>
            <filter name="group_by_date" string="By Date" icon="terp-partner" context="{'group_by':'create_date'}"/>
          </group>
          <filter name="filter_all" string="All" domain="[]"/>
          <filter name="filter_failed" string="Failed" domain="[('status','=','failed')]"/>
        </search>
      </field>

    </record>

  </data>
</odoo>
