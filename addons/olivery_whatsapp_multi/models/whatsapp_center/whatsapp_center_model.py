# -*- coding: utf-8 -*-
from openerp import models, fields,_
from datetime import datetime, date
import re
import requests
import time
import random
from openerp.exceptions import ValidationError
import logging
_logger = logging.getLogger(__name__)
from bs4 import BeautifulSoup
import urllib.parse
import json

class rb_delivery_whatsapp_center(models.Model):

    _name = 'rb_delivery.whatsapp_center'
    _inherit = 'mail.thread'
    _order = "create_date DESC"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Name')

    model_name = fields.Char('Model Name')

    user_id = fields.Many2one('rb_delivery.user', 'User', ondelete='restrict')

    record_id = fields.Char('Record ID')

    notification_message = fields.Text('Notification message')

    otp_message = fields.Boolean('Is OTP Message', default=False)

    mobile_number = fields.Char('Mobile Number')

    status = fields.Selection([('success','Success'),('failed','Failed')],default="success")

    status_message = fields.Char("Status Message")

    data_body = fields.Text('Data Body')

    headers = fields.Text('Headers')

    url = fields.Char('URL')

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    def send_whatsapp_message(self,attachment_id,sent_message,record,model_name,number,is_group,device=False):
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        headers = whatsapp_provider_config.get_headers_by_device(device)
        provider = device and device.provider or whatsapp_provider_config.provider
        if provider == 'whapi':
            base_url = whatsapp_provider_config.whapi_url.rstrip('/')
            endpoint = '/messages/document' if attachment_id else '/messages/text'
            url = base_url + endpoint
            attachment_url = ''
            if attachment_id:
                extention = attachment_id.mimetype.split('/')[1]
                if 'pdf' in extention:
                    extention = 'pdf'
                company = self.env['res.company'].search([])
                if company and company.base_url:
                    encoded_name = urllib.parse.unquote(attachment_id.name)
                    attachment_url = company.base_url +'/web/content/'+str(attachment_id.id) + '/' + encoded_name + '?download=true'
                else:
                    attachment_url = ''
            else:
                attachment_url = ''

            data = whatsapp_provider_config.get_data_json(number=number, sent_message=sent_message, is_group=is_group, attachment_url=attachment_url, filename=attachment_id,device=device)
            res = requests.post(url, headers=headers, data=data)
        else:
            _logger.info('########################################')
            _logger.info(whatsapp_provider_config)
            _logger.info(provider)
            _logger.info('########################################')

            if provider != 'cloud_ways':
                url =  self.env['rb_delivery.client_configuration'].get_param('chatapi_url')
                number = number.split('@')[0]
                number = number.replace('+','')
            else:
                url =  whatsapp_provider_config.cloud_ways_url
            clould_ways_url = ''
            if is_group and provider == 'cloud_ways' and attachment_id:
                clould_ways_url = '/api/v1.0/messages/bulk/send-group-custom-media'
            elif is_group and provider == 'cloud_ways':
                clould_ways_url = '/api/v1.0/messages/bulk/send-group-custom-text-message'
            elif provider == 'cloud_ways' and attachment_id:
                clould_ways_url = '/api/v1.0/messages/bulk/send-media'
            elif provider == 'cloud_ways':
                clould_ways_url = '/api/v1.0/messages/bulk/send-text-message'
            
            if (attachment_id and not isinstance(attachment_id, list)) or (attachment_id and isinstance(attachment_id, list) and len(attachment_id)>0):
                url = url + ("/sendFile" if provider == 'api_chat' else clould_ways_url + '?XDEBUG_SESSION_START=PHPSTORM')
                if not isinstance(attachment_id, list) and attachment_id and attachment_id.mimetype:
                    if attachment_id:
                        extention = attachment_id.mimetype.split('/')[1]
                        if 'pdf' in extention:
                            extention = 'pdf'
                        company = self.env['res.company'].search([])
                        if company and company.base_url:
                            encoded_name = urllib.parse.unquote(attachment_id.name)
                            attachment_url = company.base_url +'/web/content/'+str(attachment_id.id) + '/' + encoded_name + '?download=true'
                        else:
                            attachment_url = ''
                            raise Warning(_("Make sure company base URL is added to be able to send attachments."))                    
                else:
                    company = self.env['res.company'].search([])
                    attachment_url = []
                    for attachment in attachment_id:
                        if attachment:
                            extention = attachment.mimetype.split('/')[1]
                            if 'pdf' in extention:
                                extention = 'pdf'
                            if company and company.base_url:
                                encoded_name = urllib.parse.unquote(attachment.name)
                                attachment_url.append(company.base_url +'/web/content/'+str(attachment.id) + '/' + encoded_name + '?download=true')
                            else:
                                attachment_url.append('')
                                raise Warning(_("Make sure company base URL is added to be able to send attachments."))
                        else:
                            attachment_url.append('')
                data = whatsapp_provider_config.get_data_json(number=number,is_group=is_group,attachment_url=attachment_url,sent_message=sent_message,filename=attachment_id,device=device)
            else:
                url = url + ("/sendText" if provider == 'api_chat' else clould_ways_url + '?XDEBUG_SESSION_START=PHPSTORM')
                if is_group:
                    chat_type = "group"
                else:
                    chat_type = "normal"
                data = whatsapp_provider_config.get_data_json(number=number,sent_message=sent_message,chat_type=chat_type,is_group=is_group,device=device)
            res = requests.post(url, headers=headers, data=data)
        # end of provider branches
        index = 0
        for rec in record:
            otp_message = self._context.get('otp_message', False)
            if res.status_code == 200:
                vals = {'mobile_number': number,'otp_message': otp_message,"data_body": json.dumps(data),"headers": json.dumps(headers),"url": url,"model_name":model_name,"notification_message":sent_message[index] if isinstance(sent_message, list) else sent_message}
                if rec and rec.id:
                    vals['record_id'] = rec.id
                    self.update_last_whatsapp_datetime(model_name, rec.id,success_sent=True)
                self.create(vals)
            else:
                vals = {"data_body": json.dumps(data),"headers": json.dumps(headers),"url": url,'mobile_number': number,'otp_message': otp_message,"model_name":model_name,"notification_message":sent_message[index] if isinstance(sent_message, object) else sent_message,"status":"failed","status_message":res.text}
                if rec and rec.id:
                    vals['record_id'] = rec.id
                    self.update_last_whatsapp_datetime(model_name, rec.id,success_sent=False)
                self.create(vals)
            index += 1

    def check_whatsapp_limit(self,record,model_name,sent_message):
        whatsapp_limit = self.env['rb_delivery.client_configuration'].get_param('whatsapp_messages_limit')
        if whatsapp_limit:
            today = date.today()
            whatsapp_recs = self.env['rb_delivery.whatsapp_center'].search_count([('create_date', '>=', today.strftime('%Y-%m-%d 00:00:00')),('create_date', '<=', today.strftime('%Y-%m-%d 23:59:59')),('status','=','success')])
            if whatsapp_recs >= int(whatsapp_limit):
                vals = {"model_name":model_name,"notification_message":sent_message,'status':'failed','status_message':_('You have reached your limit to send whatsapp messages, please recharge and try again.')}
                if record and record.id:
                    vals['record_id'] = record.id
                self.create(vals)
                return
    def prepare_whatsapp_message(self,message,mobile_numbers,users,record,model_name,attachment_id=False,send_to_whatsapp_group=False,device=False):
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
            sent_message = self.env['rb_delivery.notification_center'].get_message(message,variables,users,record.id,model_name)
            self.check_whatsapp_limit(record,model_name,sent_message)
            whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
            provider = device and device.provider or whatsapp_provider_config.provider
            if provider != 'cloud_ways':
                for mobile_number in mobile_numbers:
                    try:
                        self.send_whatsapp_message(attachment_id,sent_message,record,model_name,mobile_number,send_to_whatsapp_group,device)
                        if len(mobile_numbers)>1:
                            random_timer = random.randint(10,90)
                            time.sleep(random_timer)
                    except Exception as e:
                        vals = {"model_name":model_name,"notification_message":sent_message,"status":"failed","status_message":str(e)}
                        if record and record.id:
                            vals['record_id'] = record.id
                        self.create(vals)

    def prepare_bulk_whatsapp_messages(self, messages, mobile_numbers,users,records,model_name,attachment_arr=[],send_to_whatsapp_group=False,device=False):
        index = 0
        sent_messages = []
        for rec in records:
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", messages[index])
            sent_message = self.env['rb_delivery.notification_center'].get_message(messages[index],variables,False,rec.id,model_name)
            sent_messages.append(sent_message)
            self.check_whatsapp_limit(rec,model_name,sent_message)
            index +=1
        try:
            self.send_whatsapp_message(attachment_arr,sent_messages,records,model_name,mobile_numbers,send_to_whatsapp_group,device)
        except Exception as e:
            vals = {"model_name":model_name,"notification_message":sent_message,"status":"failed","status_message":str(e)}
            if rec and rec.id:
                vals['record_id'] = rec.id
            self.create(vals)
        


    def update_last_whatsapp_datetime(self, model_name,record_id,success_sent):
        record = self.env[model_name].browse(record_id)
        record_vals = {'last_sent_whatsapp_datetime': datetime.now().strftime('%Y-%m-%d %H:%M'),'whatsapp_message_success_sent':False}
        if success_sent:
            record_vals['whatsapp_message_success_sent'] = True
        record.write(record_vals)

    def get_html_message(self, html_message):
        soup = BeautifulSoup(html_message, "html.parser")
        for el in soup(["script", "style"]):
            el.decompose()
        text = soup.get_text(separator="\n")
        text = re.sub(r'\n+', '\n', text)
        lines = [line.strip() for line in text.split("\n")]
        lines = [line for line in lines if line]
        return "\n".join(lines)