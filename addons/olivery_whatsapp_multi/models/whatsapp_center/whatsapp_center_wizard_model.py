# -*- coding: utf-8 -*-

import json
import logging
import base64
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import requests
_logger = logging.getLogger(__name__)


class order_scan_qr_wizard(models.TransientModel):
    _name = 'rb_delivery.scan_qr'
    _description = "Scan QR code Model"

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    qr_code = fields.Binary('QR code', compute="create_qrcode",readonly=True)

    connected_msg = fields.Char("Message",readonly=True)

    def get_qr_code(self,result,chatapi_url,whatsapp_provider_config):
        image = ''
        try:
            res = result.json()
            if res.get('errors'):
                self.connected_msg = res.get('errors')
            elif res.get('message'):
                self.connected_msg = res.get('message')

            elif res.get('data'):
                image = res.get('data').split(',')[1]
        except Exception as e:
            self.connected_msg = _("Issue with connecting to WhatsApp: %s") % str(e)
        return image
    


    @api.depends('connected_msg', 'device')
    def create_qrcode(self):
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        provider = self.device and self.device.provider or whatsapp_provider_config.provider
        if provider == 'whapi':
            if not whatsapp_provider_config.whapi_url:
                self.connected_msg = _('Whapi URL is not set.')
                return
            url = whatsapp_provider_config.whapi_url.rstrip('/') + '/users/login'
            headers = whatsapp_provider_config.get_headers_by_device(self.device)
            try:
                res = requests.get(url, headers=headers, timeout=25)
            except Exception as e:
                self.connected_msg = _('Issue with connecting to Whapi: %s') % str(e)
                return
            if res.status_code == 200:
                data = res.json()
                qr_b64 = data.get('base64', '')
                if qr_b64.startswith('data:') and ',' in qr_b64:
                    qr_b64 = qr_b64.split(',', 1)[1]
                self.qr_code = qr_b64
                self.connected_msg = False
            else:
                self.connected_msg = res.text
            return
        headers = whatsapp_provider_config.get_headers_by_device(self.device)
        _logger.info('########################################')
        _logger.info(whatsapp_provider_config)
        _logger.info(provider)
        _logger.info('########################################')
        if provider != 'api_chat':
            chatapi_url = whatsapp_provider_config.cloud_ways_url
        else:
            headers['Accept-Encoding'] = 'identity'
            chatapi_url = self.env['rb_delivery.client_configuration'].get_param('chatapi_url')
        url = chatapi_url
        qr_url = chatapi_url+("/status" if provider == 'api_chat' else '/api/v1.0/whatsapp/get_qrcode')
        body = ""
        try:
            res = requests.get(qr_url, data=body, headers=headers , timeout=25)
        except Exception as e:
            self.connected_msg = _("Issue with connecting to WhatsApp: %s") % str(e)
            return
        if res.status_code == 200:
            if provider == 'cloud_ways':
                qr_code = self.get_qr_code(res,chatapi_url,whatsapp_provider_config)
                if qr_code:
                    self.qr_code = qr_code
                    self.connected_msg = False
            else:
                try:
                    result = res.json()
                    if 'qr' in result:
                        header, b64data = result['qr'].split(',', 1)
                        self.qr_code = b64data 
                        self.connected_msg = False
                    elif 'is_connected' in result and result['is_connected']:
                        connected_message = _("You are already connected to WhatsApp")
                        url = chatapi_url+"/status" if provider == 'api_chat' else "/get_session_status?XDEBUG_SESSION_START=PHPSTORM"
                        body = ""
                        try:
                            acc_res = requests.get(url, data=body, headers=headers, timeout=15)
                        except Exception as e:
                            self.connected_msg = _("Issue with connecting to WhatsApp: %s") % str(e)
                            return
                        if acc_res.status_code == 200:
                            try:
                                acc_result = acc_res.json()
                                phone_number = ''
                                if "phone" in acc_result and acc_result["phone"]:
                                    phone_number = acc_result["phone"]
                                if phone_number:
                                    connected_message = _("You are already connected to WhatsApp with mobile number %s") % phone_number

                            except Exception as e:
                                self.connected_msg = _("Issue with connecting to WhatsApp: %s") % str(e)
                                return
                        self.connected_msg = connected_message
                except:
                    image_base64 = base64.b64encode(res.content)
                    self.qr_code = image_base64
        else:
            self.connected_msg = res.text

    def logout(self):
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        provider = self.device and self.device.provider or whatsapp_provider_config.provider
        if provider == 'api_chat':
            chatapi_url = whatsapp_provider_config.cloud_ways_url
        elif provider == 'whapi':
            chatapi_url = whatsapp_provider_config.whapi_url.rstrip('/') + '/users/logout'
        else:
            chatapi_url = self.env['rb_delivery.client_configuration'].get_param('chatapi_url')
        _logger.info('########################################')
        _logger.info(whatsapp_provider_config)
        _logger.info(provider)
        _logger.info('########################################')
        url = chatapi_url+"/logout" if provider == 'api_chat' else chatapi_url+"/api/v1.0/whatsapp/session_logout?XDEBUG_SESSION_START=PHPSTORM" if provider == 'cloud_ways' else chatapi_url
        headers = whatsapp_provider_config.get_headers_by_device(self.device)
        body = ""
        method = 'POST'
        if provider == 'cloud_ways':
            method = 'GET'
        res = requests.request(method, url, data=body, headers=headers)
        if res.status_code == 200:
            self.connected_msg = False
            self.create_qrcode()
            return {
                'context': self.env.context,
                'view_type': 'form',
                'view_mode': 'form',
                'res_model': 'rb_delivery.scan_qr',
                'res_id': self.id,
                'view_id': False,
                'type': 'ir.actions.act_window',
                'target': 'new',
            }
        else:
            self.connected_msg = res.text

    def refresh_qr_codes(self):
        recs = self.search([])
        recs.write({'qr_code': False})
        recs.create_qrcode()



class retry_sending_whatsapp_wizard(models.TransientModel):
    _name = 'rb_delivery.retry_sending_whatsapp'
    _description = "Retry Sending WhatsApp Messages"

    @api.multi
    def retry(self):
        recs = self.env['rb_delivery.whatsapp_center'].browse(self._context.get('active_ids'))
        for rec in recs:
            if not rec.data_body or not rec.headers or not rec.url:
                continue
            try:
                response = requests.post(
                    rec.url,
                    data=json.loads(rec.data_body),
                    headers=json.loads(rec.headers),
                    timeout=30
                )
                if response.status_code == 200:
                    rec.state = 'sent'
                    rec.message_post(body=_("WhatsApp message retried successfully."))
                else:
                    rec.state = 'failed'
                    rec.message_post(body=_("Failed to retry WhatsApp message: %s") % response.text)
            except requests.RequestException as e:
                rec.state = 'failed'
                rec.message_post(body=_("Failed to retry WhatsApp message: %") % str(e))
        return True