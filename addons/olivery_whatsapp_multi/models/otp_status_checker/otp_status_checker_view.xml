<odoo>
    <data>
        <record id="view_form_rb_delivery_otp_status_checker_whatsapp" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_otp_status_checker_whatsapp</field>
            <field name="model">rb_delivery.otp_status_checker</field>
            <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_otp_status_checker" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='send_through']" position="after">
                    <field name="device" attrs="{'invisible': [('send_through', '!=', 'whatsapp')]}"/>
                </xpath>
            </field>
        </record>

        <record id="view_tree_rb_delivery_otp_status_checker_whatsapp" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_otp_status_checker_whatsapp</field>
            <field name="model">rb_delivery.otp_status_checker</field>
            <field name="inherit_id" ref="rb_delivery.view_tree_rb_delivery_otp_status_checker" />
            <field name="arch" type="xml">

                <xpath expr="//field[@name='send_through']" position="after">
                    <field name="device"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>