# -*- coding: utf-8 -*-

import re
from openerp import models, fields, api, _


class otp_status_checker_client(models.Model):

    _inherit = 'rb_delivery.otp_status_checker'

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device', track_visibility="on_change")

    def get_otp_provider(self):
        providers = super(otp_status_checker_client, self).get_otp_provider()
        return providers + [('whatsapp', 'WhatsApp')]
    
    def send_otp_notification(self, recipient, recipient_email=None, otp_code=None, record=None):
        if not otp_code:
            otp_code = self.generate_otp()
            
        if self.send_through == 'whatsapp':
            return self._send_otp_via_whatsapp(recipient, otp_code, record)
        else:
            return super(otp_status_checker_client, self).send_otp_notification(recipient, recipient_email, otp_code, record)
        
    def _send_otp_via_whatsapp(self, recipient, otp_code, record):    
        new_msg = self.message.replace('{otp_code}', otp_code)
        try:
            self.env['rb_delivery.whatsapp_center'].with_context(otp_message=True).prepare_whatsapp_message(new_msg,[recipient],[record.assign_to_business],record,'rb_delivery.order',False,device=self.device)
            rec = self.env['rb_delivery.whatsapp_center'].search([('mobile_number', '=', recipient), ('record_id', '=', record.id), ('otp_message', '=', True)], limit=1)
            if rec.status == 'failed':
                return {'status': 'error', 'message': 'FAILED_TO_SEND_OTP_VIA_WHATSAPP'}
            return {'status': 'success', 'message': 'OTP_SENT_VIA_WHATSAPP'}
        except Exception as e:
            return {'status': 'error', 'message': 'FAILED_TO_SEND_OTP_VIA_WHATSAPP'}