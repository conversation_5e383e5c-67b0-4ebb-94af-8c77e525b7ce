# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import json
from odoo.exceptions import ValidationError
import urllib.parse
import requests
import base64

class rb_delivery_whatsapp_provider(models.Model):

    _name = 'olivery_whatsapp_multi.whatsapp_provider'
    _inherit = 'mail.thread'

    headers = fields.Char(string='Headers',compute="compute_headers")

    provider = fields.Selection(string='Provider',selection=[('cloud_ways','Cloud Ways'),('api_chat','Api Chat'),('whapi','Whapi')],default='api_chat',required=True, track_visibility="on_change")

    cloud_ways_api_key = fields.Char('Could ways Api Key', track_visibility="on_change")

    cloud_ways_url = fields.Char('Clould way URL', track_visibility="on_change")

    whapi_url = fields.Char('Whapi URL', track_visibility="on_change")

    whapi_token = fields.Char('Whapi Token', track_visibility="on_change")

    devices = fields.One2many('olivery_whatsapp_multi.whatsapp_device',inverse_name="provider_id",relation='olivery_whatsapp_device',string='Devices')


    @api.constrains('devices')
    def _check_max_devices(self):
        for record in self:
            if len(record.devices) > 3:
                raise ValidationError("You can only select a maximum of 3 devices.")


    @api.depends('provider')
    def compute_headers(self):

        if self.provider == 'api_chat':
            client_id = self.env['rb_delivery.client_configuration'].get_param('chatapi_client_id')
            token = self.env['rb_delivery.client_configuration'].get_param('chatapi_token')
            self.headers = json.dumps({
                "Content-Type": "application/json",
                "client-id":client_id,
                "token":token
            })
        elif self.provider == 'cloud_ways':
            self.headers = json.dumps({
                "Accept": "application/json",
                "x-api-key":self.cloud_ways_api_key,
                "Content-Type": "application/json"
            })
        elif self.provider == 'whapi':
            self.headers = json.dumps({
                "Content-Type": "application/json",
                "Authorization": self.whapi_token and f"Bearer {self.whapi_token}" or ""
            })
        else:
            self.headers = False

    def get_headers(self):
        return json.loads(self.headers)
    

    def get_headers_by_device(self, device):
        if not device:
            return self.get_headers()
        else:
            provider = device and device.provider or self.provider
            if provider == 'cloud_ways':
                return {
                    "Accept": "application/json",
                    "x-api-key": device.device_api,
                    "Content-Type": "application/json"
                }
            elif provider == 'api_chat':
                return {
                    "Content-Type": "application/json",
                    "client-id":device.client_id,
                    "token":device.device_api
                }
            elif provider == 'whapi':
                return {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {device.device_api}"
                }

    def get_data_json(self,number,sent_message,is_group=False,attachment_url=False,filename=False,chat_type=False,device=False):
        provider = device and device.provider or self.provider
        if provider == 'api_chat':
            body_enconded = urllib.parse.unquote(sent_message)
            filename_txt=''
            if filename:
                extention = filename.mimetype.split('/')[1]
                filename_txt =filename.name+'.'+extention
            if attachment_url:
                data = {
                    "number": number,
                    "is_group":is_group,
                    "url": attachment_url,
                    "caption": body_enconded,
                    "filename":filename_txt,
                }
            else:
                data={"number": number, "text": body_enconded,"chat_type":chat_type}
        elif provider == 'cloud_ways':
            if attachment_url:
                user_key = 'number'
                if is_group:
                    user_key = 'group_id'

                index = 0
                data = []
                for rec in number:
                    for num in rec:
                        if index < len(attachment_url):
                            data.append({
                                "url": attachment_url[index],
                                "message":urllib.parse.unquote(sent_message[index]) ,
                                "media_type":"document",
                                user_key: num
                            })
                        else:
                            data.append({
                                "url": '',
                                "message": urllib.parse.unquote(sent_message[index]),
                                "media_type":"document",
                                user_key: num
                            })
                    index +=1
            else:
                if is_group:
                    index = 0
                    data = []
                    for rec in number:
                        for num in rec:
                            data.append({
                                "group_id":num,
                                "message":urllib.parse.unquote(sent_message[index])
                            })
                        index +=1
                else:
                    index = 0
                    data = []
                    for rec in number:
                        for num in rec:
                            data.append({
                                "number":num,
                                "message":urllib.parse.unquote(sent_message[index])
                            })
                        index +=1
        elif provider == 'whapi':
            if attachment_url:
                data = {
                    "to": number,
                    "body": sent_message,
                    "media": attachment_url,
                    "filename": filename and filename.name or "",
                    "mime_type": filename and filename.mimetype or "",
                    "caption": sent_message,
                    "no_encode": False,
                    "no_cache": False,
                    "view_once": False
                }
            else:
                data = {"to": number, "body": sent_message}
            return json.dumps(data)
        return json.dumps(data)
