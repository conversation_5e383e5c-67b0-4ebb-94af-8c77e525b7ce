from openerp import models, fields,  _
from bs4 import BeautifulSoup
import logging

_logger = logging.getLogger(__name__)


class olivery_multi_whatsapp_action(models.Model):

    _inherit = 'rb_delivery.action'

    is_whatsapp = fields.<PERSON><PERSON>an("Is Whatsapp")

    send_to_whatsapp_group = fields.Boolean('Send to group')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device', string='Device')

    def do_actions(self, *params):
        params_list = list(params)
        order = params_list[1]
        whatsapp_actions = params_list[0].filtered(lambda x: x.is_whatsapp == True)
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')

        if len(whatsapp_actions) and whatsapp_provider_config.provider != 'cloud_ways':
            self.with_delay(channel="root.basic",max_retries=2).notify_whatsapp(whatsapp_actions, order)

        params = tuple(params_list)
        return super(olivery_multi_whatsapp_action, self).do_actions(*params)

    def get_whatsapp_mobile(self, mobile_number):
        if not mobile_number:
            return False

        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        no_space_first_number = mobile_number.strip()

        if no_space_first_number and no_space_first_number[0] == '0':
            no_space_first_number = no_space_first_number[1:]

        whatsapp_mobile = prefix_one + no_space_first_number
        return whatsapp_mobile

    def get_html_message(self, html_message):
        if not html_message:
            return ''

        soup = BeautifulSoup(html_message, features="html.parser")
        for script in soup(["script", "style"]):
            script.extract()

        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)

        return text

    def _get_message_content(self, action):
        message = ''
        if action.message:
            message = action.message
        if action.action_template:
            message = self.get_html_message(action.action_template.body_html)
        return message

    def _collect_user_mobile(self, user, send_to_whatsapp_group, device):
        mobile_numbers = []

        if send_to_whatsapp_group:
            group_id_app = user.get_group_id(device)
            if group_id_app:
                mobile_numbers.append(group_id_app)
        else:
            if user.whatsapp_mobile:
                mobile_numbers.append(user.whatsapp_mobile)
            elif user.mobile_number:
                mobile_number = self.get_whatsapp_mobile(user.mobile_number)
                mobile_numbers.append(mobile_number)

        return mobile_numbers

    def _handle_customer_notification(self, order, users):
        mobile_numbers = []

        if order.assign_to_business:
            users.append(order.assign_to_business)
            if order.cus_whatsapp_mobile:
                mobile_numbers.append(order.cus_whatsapp_mobile)
            elif order.customer_mobile:
                mobile_number = self.get_whatsapp_mobile(order.customer_mobile)
                mobile_numbers.append(mobile_number)

        return mobile_numbers

    def _handle_business_notification(self, business_user, send_to_whatsapp_group, device, users):
        mobile_numbers = []

        if business_user:
            users.append(business_user)
            mobile_numbers.extend(self._collect_user_mobile(business_user, send_to_whatsapp_group, device))

        return mobile_numbers

    def _handle_driver_notification(self, distributor_user, send_to_whatsapp_group, device, users):
        mobile_numbers = []

        if distributor_user:
            users.append(distributor_user)
            mobile_numbers.extend(self._collect_user_mobile(distributor_user, send_to_whatsapp_group, device))

        return mobile_numbers

    def _handle_sales_notification(self, order, is_sale, send_to_whatsapp_group, device, users):
        mobile_numbers = []

        if is_sale:
            sales = self.env['rb_delivery.user'].sudo().search([('user_id', '=', order.create_uid.id)])
            if sales:
                users.append(sales)
                mobile_numbers.extend(self._collect_user_mobile(sales, send_to_whatsapp_group, device))

        return mobile_numbers

    def _handle_group_notification(self, group, send_to_whatsapp_group, device, users):
        mobile_numbers = []

        del_users = self.env['rb_delivery.user'].sudo().search([('group_id', '=', group.id)])
        for user in del_users:
            users.append(user)
            mobile_numbers.extend(self._collect_user_mobile(user, send_to_whatsapp_group, device))

        return mobile_numbers

    def notify_whatsapp(self, actions, order):
        for action in actions:
            group = action.group_id
            mobile_numbers = []
            users = []
            send_to_whatsapp_group = action.send_to_whatsapp_group
            message = self._get_message_content(action)

            business_user = order.sudo().assign_to_business
            distributor_user = order.sudo().assign_to_agent
            is_sale = order.sudo().create_uid.has_group('rb_delivery.role_sales')

            if action.notify_customer:
                customer_numbers = self._handle_customer_notification(order, users)
                mobile_numbers.extend(customer_numbers)
            elif group and group.id:
                if group.code == 'rb_delivery.role_business':
                    business_numbers = self._handle_business_notification(business_user, send_to_whatsapp_group, action.device, users)
                    mobile_numbers.extend(business_numbers)
                elif group.code == 'rb_delivery.role_driver':
                    driver_numbers = self._handle_driver_notification(distributor_user, send_to_whatsapp_group, action.device, users)
                    mobile_numbers.extend(driver_numbers)
                elif group.code == 'rb_delivery.role_sales' and is_sale:
                    sales_numbers = self._handle_sales_notification(order, is_sale, send_to_whatsapp_group, action.device, users)
                    mobile_numbers.extend(sales_numbers)
                else:
                    group_numbers = self._handle_group_notification(group, send_to_whatsapp_group, action.device, users)
                    mobile_numbers.extend(group_numbers)

            if mobile_numbers or send_to_whatsapp_group:
                try:
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(message,mobile_numbers,users,order,'rb_delivery.order',send_to_whatsapp_group=send_to_whatsapp_group,device=action.device)
                except Exception as e:
                    _logger.error("Failed to send WhatsApp message: %s", str(e))
