<odoo>
  <data>

<!--inherit form view-->
      <record id="view_form_multi_whatsapp_action" model="ir.ui.view">
         <field name="name">view_form_multi_whatsapp_action</field>
         <field name="model">rb_delivery.action</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_action" />
         <field name="arch" type="xml">

            <field name="is_email" position="after">
               <field name="is_whatsapp" attrs="{'invisible':[('action_type','not in',['for_status','for_storex','for_edit'])]}"/>
               <field name="send_to_whatsapp_group" attrs="{'invisible':[('is_whatsapp','=',False)]}"/>
               <field name="device" attrs="{'invisible':[('is_whatsapp','=',False)]}"/>
            </field>
            <field name="message" position="replace">
               <field name="message" placeholder="Message" attrs="{'invisible':[('is_email','=',True),('is_notification','=',False),('is_sms','=',False)],'required':[('is_email','=',False),'|','|',('is_notification','=',True),('is_sms','=',True),('is_whatsapp','=',True),('action_template','=',False)]}"/>
            </field>

         </field>
      </record>

  </data>
</odoo>