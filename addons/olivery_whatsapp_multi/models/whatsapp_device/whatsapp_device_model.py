# -*- coding: utf-8 -*-
from openerp import models, fields, api,_


class olivery_whatsapp_multi_whatsapp_device(models.Model):

    _name = 'olivery_whatsapp_multi.whatsapp_device'

    name = fields.Char('Device name', required=True)

    device_api = fields.Char('Api key', required=True)

    client_id = fields.Char('Client id (Api chat)')

    provider = fields.Selection(string='Provider',selection=[('cloud_ways','Cloud Ways'),('api_chat','Api Chat'),('whapi','Whapi')], track_visibility="on_change")

    provider_id = fields.Many2one('olivery_whatsapp_multi.whatsapp_provider', string='provider')

