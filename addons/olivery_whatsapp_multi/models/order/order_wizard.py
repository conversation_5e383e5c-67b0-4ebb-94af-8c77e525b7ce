
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime

class olivery_multi_whatsapp_notify(models.TransientModel):

    _inherit = 'rb_delivery.notify_orders'

    notification_type = fields.Selection([
        ('is_sms','Sms'),('is_whatsapp','Whatsapp')
    ],default='is_whatsapp')

    template = fields.Many2one('mail.template', 'Template', domain=[('model_id','=','rb_delivery.order')],relation="notify_orders_templates")

    attachment = fields.Binary('Attachment',relation="attach_notify_order")

    attachment_id = fields.Many2one('ir.attachment',compute="change_attach")

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    @api.depends('attachment')
    def change_attach(self):
        if self.attachment:
            attachment_id = self.env['ir.attachment'].create({
                'name': "attachment",
                'type': 'binary',
                'datas': self.attachment,
                'res_model': 'rb_delivery.order',
                'public':True
            })
            if attachment_id:
                self.attachment_id = attachment_id.id
        self.env.cr.commit()


    def whatsapp_notify(self,orders):
        attachment_arr = []
        mobile_arr = []
        users_arr = []
        order_arr = []
        body_arr =[]
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        for order in orders:
            users = []
            mobile_numbers = []
            users.append(order.assign_to_business)
            if order.cus_whatsapp_mobile:
                mobile_number = order.cus_whatsapp_mobile
                mobile_numbers.append(mobile_number)
            else:
                no_space_first_number=order.customer_mobile.strip()
                if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                mobile_number = prefix_one+no_space_first_number
                mobile_numbers.append(mobile_number)

            if len(mobile_numbers)>0:
                mobile_arr.append(mobile_numbers)
                users_arr.append(users)
                order_arr.append(order)
                body_arr.append(self.message)
                if self.attachment_id:
                    attachment_arr.append(self.attachment_id)
        try:
            if len(mobile_arr)>0:
                self.env['rb_delivery.whatsapp_center'].prepare_bulk_whatsapp_messages(body_arr,mobile_arr,users_arr,order_arr,'rb_delivery.order',attachment_arr,device=self.device)
        except:
            pass

    def whatsapp_old_notify(self,orders):
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        for order in orders:
            users = []
            mobile_numbers = []
            users.append(order.assign_to_business)
            if order.cus_whatsapp_mobile:
                mobile_number = order.cus_whatsapp_mobile
                mobile_numbers.append(mobile_number)
            else:
                no_space_first_number=order.customer_mobile.strip()
                if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                mobile_number = prefix_one+no_space_first_number
                mobile_numbers.append(mobile_number)
            try:
                if len(mobile_numbers)>0:
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(self.message,mobile_numbers,users,order,'rb_delivery.order',self.attachment_id,device=self.device)
            except:
                pass

    def notify(self):
        if self.notification_type == 'is_whatsapp':
            if self.template:
                self.message = self.env['rb_delivery.whatsapp_center'].get_html_message(self.template.body_html)

            orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
            whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
            if whatsapp_provider_config.provider == 'cloud_ways':
                self.with_delay(channel="root.basic",max_retries=2).whatsapp_notify(orders)
            else:
                self.with_delay(channel="root.basic",max_retries=2).whatsapp_old_notify(orders)
        else:
            if self.notification_type == 'is_sms':
                self.is_sms = True
            return super(olivery_multi_whatsapp_notify,self).notify()

class olivery_multi_whatsapp_notify_business(models.TransientModel):

    _inherit = 'rb_delivery.notify_business'

    notification_type = fields.Selection([
        ('is_email','Email'),
        ('is_notification','Notification'),
        ('is_sms','Sms'),('is_whatsapp','Whatsapp')
    ],default='is_notification')

    attachment = fields.Binary('Attachment',relation="attach_notify_business")

    attachment_id = fields.Many2one('ir.attachment',compute="change_attach")

    send_to_whatsapp_group = fields.Boolean('Send to WhatsApp group')

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device')

    @api.depends('attachment')
    def change_attach(self):
        if self.attachment:
            attachment_id = self.env['ir.attachment'].create({
                'name': "attachment",
                'type': 'binary',
                'datas': self.attachment,
                'res_model': 'rb_delivery.order',
                'public':True
            })
            if attachment_id:
                self.attachment_id = attachment_id.id
        self.env.cr.commit()

    def whatsapp_notify(self,orders):
        attachment_arr = []
        mobile_arr = []
        users_arr = []
        order_arr = []
        body_arr =[]
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')

        for order in orders:
            users = []
            mobile_numbers = []
            if order.assign_to_business:
                users.append(order.assign_to_business)
                if self.send_to_whatsapp_group:
                    group_id_app = order.assign_to_business.get_group_id(self.device)
                    if group_id_app:
                        mobile_numbers.append(group_id_app)
                else:
                    if order.assign_to_business.whatsapp_mobile:
                        mobile_number = order.assign_to_business.whatsapp_mobile
                        mobile_numbers.append(mobile_number)
                    else:
                        no_space_first_number=order.assign_to_business.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                        mobile_numbers.append(mobile_number)
            
            if len(mobile_numbers)>0:
                mobile_arr.append(mobile_numbers)
                users_arr.append(users)
                order_arr.append(order)
                body_arr.append(self.message)
                if self.attachment_id:
                    attachment_arr.append(self.attachment_id)

        try:
            if len(mobile_arr)>0:
                self.env['rb_delivery.whatsapp_center'].prepare_bulk_whatsapp_messages(body_arr,mobile_arr,users_arr,order_arr,'rb_delivery.order',attachment_arr,self.send_to_whatsapp_group,device=self.device)
        except:
            pass

    def whatsapp_old_notify(self,orders):
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        for order in orders:
            users = []
            mobile_numbers = []
            if order.assign_to_business:
                users.append(order.assign_to_business)
                if self.send_to_whatsapp_group:
                    group_id_app = order.assign_to_business.get_group_id(self.device)
                    if group_id_app:
                        mobile_numbers.append(group_id_app)
                else:
                    if order.assign_to_business.whatsapp_mobile:
                        mobile_number = order.assign_to_business.whatsapp_mobile
                        mobile_numbers.append(mobile_number)
                    else:
                        no_space_first_number=order.assign_to_business.mobile_number.strip()
                        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
                        mobile_number = prefix_one+no_space_first_number
                        mobile_numbers.append(mobile_number)
            try:
                if len(mobile_numbers)>0:
                    self.env['rb_delivery.whatsapp_center'].prepare_whatsapp_message(self.message,mobile_numbers,users,order,'rb_delivery.order',self.attachment_id,self.send_to_whatsapp_group,device=self.device)
            except:
                pass

    def notify(self):
        if self.notification_type == 'is_whatsapp':
            if self.template:
                self.message = self.env['rb_delivery.whatsapp_center'].get_html_message(self.template.body_html)
            orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
            whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
            if whatsapp_provider_config.provider == 'cloud_ways':
                self.with_delay(channel="root.basic",max_retries=2).whatsapp_notify(orders)
            else:
                self.with_delay(channel="root.basic",max_retries=2).whatsapp_old_notify(orders)
        else:
            return super(olivery_multi_whatsapp_notify_business,self).notify()
