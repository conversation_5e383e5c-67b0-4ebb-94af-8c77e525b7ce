# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime
from bs4 import BeautifulSoup
import re

class rb_delivery_whatsapp_multi_order(models.Model):

    _inherit = 'rb_delivery.order'

    last_sent_whatsapp_datetime = fields.Datetime(string="Whatsapp Msg Date", track_visibility="on_change")

    whatsapp_message_success_sent = fields.Boolean('Whatsapp message successfully sent',default=False,track_visibility="on_change")

    @api.multi
    def write(self, values):
        if values.get('state'):
            self.do_whatsapp_action(values,True)
        if 'whatsapp_message_success_sent' not in values:
            self.do_whatsapp_action(values,False,fields_changed=values.keys())
        return super(rb_delivery_whatsapp_multi_order, self).write(values)
    
    @api.model
    def create(self, values):
        record = super(rb_delivery_whatsapp_multi_order, self).create(values)       
        if values.get('state'):
            record.do_whatsapp_action(values,True)
        return record


    def get_whatsapp_mobile(self, mobile_number):
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        no_space_first_number=mobile_number.strip()
        if no_space_first_number[0]=='0': no_space_first_number=no_space_first_number[1:]
        whatsapp_mobile = prefix_one+no_space_first_number
        return whatsapp_mobile
    
    @api.model
    def do_whatsapp_action(self, values,for_status,fields_changed=False):
        whatsapp_provider_config = self.env.ref('olivery_whatsapp_multi.multi_whatsapp_provider_config')
        if whatsapp_provider_config.provider == 'cloud_ways':
            domain = [('is_whatsapp', '=', True)]
            if for_status:
                domain = domain + [('order_state', '=', values['state']),('action_type','=','for_status')]
            else:
                field_names = []
                for field_name in fields_changed:
                    field_names.append('field_'+field_name)
                if len(field_names):
                    domain.append(('field_name', 'in', field_names))
                domain.append(('action_type','=','for_edit'))
            whatsapp_actions = self.env['rb_delivery.action'].search(domain)

            device_data = {}

            for order in self:
                for action in whatsapp_actions:
                    device = action.device 

                    if device not in device_data:
                        device_data[device] = {
                            'mobile_arr': [],
                            'records': [],
                            'message_arr': [],
                            'users_arr': [],
                            'group_ids': [],
                            'group_recs': [],
                            'group_messages': [],
                            'group_users': [],
                        }

                    data = device_data[device]

                    group = action.group_id
                    mobile_numbers = []
                    users = []
                    message = ''
                    send_to_whatsapp_group = False
                    business_user = order.sudo().assign_to_business
                    distributor_user = order.sudo().assign_to_agent
                    is_sale = order.sudo().create_uid.has_group('rb_delivery.role_sales')
                    if action.send_to_whatsapp_group:
                        send_to_whatsapp_group = True
                    if action.message:
                        message = action.message
                    if action.action_template:
                        message = self.get_html_message(action.action_template.body_html)

                    if action.notify_customer:
                        if order.assign_to_business:
                            users.append(order.assign_to_business)
                            if order.cus_whatsapp_mobile:
                                mobile_number = order.cus_whatsapp_mobile
                                mobile_numbers.append(mobile_number)
                            elif order.customer_mobile:
                                mobile_number = self.get_whatsapp_mobile(order.customer_mobile)
                                mobile_numbers.append(mobile_number)
                    elif group and group.id:
                        if group.code == 'rb_delivery.role_business':
                            if business_user:
                                users.append(business_user)
                                if send_to_whatsapp_group:
                                    group_id_app = business_user.get_group_id(device)
                                    if group_id_app:
                                        mobile_numbers.append(group_id_app)
                                else:
                                    if business_user.whatsapp_mobile:
                                        mobile_number = business_user.whatsapp_mobile
                                        mobile_numbers.append(mobile_number)
                                    elif business_user.mobile_number:
                                        mobile_number = self.get_whatsapp_mobile(business_user.mobile_number)
                                        mobile_numbers.append(mobile_number)
                        elif group.code == 'rb_delivery.role_driver':
                            if distributor_user:
                                users.append(distributor_user)
                                if send_to_whatsapp_group:
                                    group_id_app = distributor_user.get_group_id(device)
                                    if group_id_app:
                                        mobile_numbers.append(group_id_app)
                                else:
                                    if distributor_user.whatsapp_mobile:
                                        mobile_number = distributor_user.whatsapp_mobile
                                        mobile_numbers.append(mobile_number)
                                    elif distributor_user.mobile_number:
                                        mobile_number = self.get_whatsapp_mobile(distributor_user.mobile_number)
                                        mobile_numbers.append(mobile_number)
                        elif group.code == 'rb_delivery.role_sales' and is_sale:
                            sales = self.env['rb_delivery.user'].sudo().search([('user_id', '=', order.create_uid.id)])
                            if sales:
                                users.append(sales)
                                if send_to_whatsapp_group:
                                    group_id_app = sales.get_group_id(device)
                                    if group_id_app:
                                        mobile_numbers.append(group_id_app)
                                    elif sales.whatsapp_mobile:
                                        mobile_number = sales.whatsapp_mobile
                                        mobile_numbers.append(mobile_number)
                                    elif sales.mobile_number:
                                        mobile_number = self.get_whatsapp_mobile(sales.mobile_number)
                                        mobile_numbers.append(mobile_number)
                        else:
                            del_users = self.env['rb_delivery.user'].sudo().search([('group_id', '=', group.id)])
                            for user in del_users:
                                users.append(user)
                                if send_to_whatsapp_group:
                                    group_id_app = user.get_group_id(device)
                                    if group_id_app:
                                        mobile_numbers.append(group_id_app)
                                else:
                                    if user.whatsapp_mobile:
                                        mobile_number = user.whatsapp_mobile
                                        mobile_numbers.append(mobile_number)
                                    elif user.mobile_number:
                                        mobile_number = self.get_whatsapp_mobile(user.mobile_number)
                                        mobile_numbers.append(mobile_number)
                    if not send_to_whatsapp_group:
                        data['records'].append(order)
                        data['mobile_arr'].append(mobile_numbers)
                        data['message_arr'].append(message)
                        data['users_arr'].append(users)
                    else:
                        data['group_recs'].append(order)
                        data['group_ids'].append(mobile_numbers)
                        data['group_messages'].append(message)
                        data['group_users'].append(users)

            for device, data in device_data.items():
                if data['records']:
                    self.env['rb_delivery.whatsapp_center'].with_delay(channel="root.basic", max_retries=2).prepare_bulk_whatsapp_messages(data['message_arr'],data['mobile_arr'],data['users_arr'],data['records'],'rb_delivery.order',[],send_to_whatsapp_group=False,device=device)

                if data['group_recs']:
                    self.env['rb_delivery.whatsapp_center'].with_delay(channel="root.basic", max_retries=2).prepare_bulk_whatsapp_messages(data['group_messages'],data['group_ids'],data['group_users'],data['group_recs'],'rb_delivery.order',[],send_to_whatsapp_group=True,device=device)

        return
