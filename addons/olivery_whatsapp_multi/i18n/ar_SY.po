# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* olivery_whatsapp_multi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 12:29+0000\n"
"PO-Revision-Date: 2025-06-12 10:16+0300\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: ar_SY\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.6\n"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:130
#, python-format
msgid ""
" Check chatapi_client_id and chatapi_token and chatapi_url configuration "
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid ""
"<small>\n"
"\t\t\t\t\t\t\t\t\t<span>Page</span>\n"
"\t\t\t\t\t\t\t\t\t<span class=\"page\"/>\n"
"\t\t\t\t\t\t\t\t\tof\n"
"\t\t\t\t\t\t\t\t\t<span class=\"topage\"/>\n"
"\t\t\t\t\t\t\t\t</small>"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid ""
"<small>\n"
"\t\t\t\t\t\t\t\t\t<span>Page</span>\n"
"\t\t\t\t\t\t\t\t\t<span class=\"page\"/>\n"
"                            of\n"
"\t\t\t\t\t\t\t\t\t<span class=\"topage\"/>\n"
"\t\t\t\t\t\t\t\t</small>"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_action
msgid "Action Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_needaction
msgid "Action Needed"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Address  :"
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:olivery_whatsapp_multi.whatsapp_provider,provider:0
msgid "Api Chat"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_business__attachment
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_business__attachment_id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_orders__attachment
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_orders__attachment_id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_users__attachment
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_users__attachment_id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_attachment__attachment_id
msgid "Attachment"
msgstr "مرفق"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__whatsapp_attachment_ids
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__attachment_ids
msgid "Attachments"
msgstr "مرفقات"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__author
msgid "Author"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__author_name
msgid "Author Name"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__body
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__body
msgid "Body"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid "Business Name:"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Business mobile number:"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Business name :"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_center
msgid "By Date"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_center
msgid "By Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_center
msgid "By User"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_multi_whatsapp_user_inherited
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_order_inherited
msgid "By Whatsapp Msg Date"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_money_collection_send_whatsapp
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_returned_collection_send_whatsapp
msgid "Cancel"
msgstr ""

#. module: olivery_whatsapp_multi
#. openerp-web
#: code:addons/olivery_whatsapp_multi/static/src/js/buttons.js:44
#, python-format
msgid "Chat List Generator"
msgstr "انشاء المجموعات"

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_chat_list_wizzered
msgid "Chat List Wizzered model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_chat_list_warning
msgid "Chat list warning "
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:olivery_whatsapp_multi.whatsapp_provider,provider:0
msgid "Cloud Ways"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__cloud_ways_url
msgid "Clould way URL"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid "Collection Report"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_money_collection_send_whatsapp
msgid "Collection Send Whatsapp Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Company Registry:"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__is_whatsapp_group_message
msgid "Configured Whatsapp group message"
msgstr "تكوين رسالة مجموعة الواتس اب"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_chat_list
msgid "Configured whatsapp message"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_money_collection_send_whatsapp
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_returned_collection_send_whatsapp
msgid "Confirm"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_chat_list_warning
msgid "Confirmation Message"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__cloud_ways_api_key
msgid "Could ways Api Key"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__create_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__create_uid
msgid "Created by"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__create_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__create_date
msgid "Created on"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Date:"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.actions.server,name:olivery_whatsapp_multi.cron_delete_chat_list_ir_actions_server
#: model:ir.cron,cron_name:olivery_whatsapp_multi.cron_delete_chat_list
#: model:ir.cron,name:olivery_whatsapp_multi.cron_delete_chat_list
msgid "Delete Chat List"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Delivery cost"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__display_name
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__display_name
msgid "Display Name"
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:rb_delivery.notify_business,notification_type:0
#: selection:rb_delivery.notify_users,notification_type:0
msgid "Email"
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:rb_delivery.whatsapp_center,status:0
msgid "Failed"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_chat_list
msgid "Filters"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_follower_ids
msgid "Followers"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: olivery_whatsapp_multi
#. openerp-web
#: code:addons/olivery_whatsapp_multi/static/src/xml/buttons.xml:6
#, python-format
msgid "Get Chat List"
msgstr "انشاء المجموعات"

#. module: olivery_whatsapp_multi
#. openerp-web
#: code:addons/olivery_whatsapp_multi/static/src/xml/buttons.xml:7
#, python-format
msgid "Refresh Groups"
msgstr "تحديث المجموعات"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_chat_list_warning
msgid "Go to users"
msgstr "الذهاب الى المستخدمين"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_center
msgid "Groups"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__headers
msgid "Headers"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__id
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__id
msgid "ID"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_action__is_whatsapp
msgid "Is Whatsapp"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:32
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:53
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:71
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:83
#, python-format
msgid "Issue with connecting to WhatsApp: %s"
msgstr "حدث خلل اثناء الاتصال بواتساب %s"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__key_word
msgid "Keyword"
msgstr "الكلمات المفتاحيه"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center____last_update
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list____last_update
msgid "Last Modified on"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__write_uid
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__write_uid
msgid "Last Updated by"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__write_date
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__write_date
msgid "Last Updated on"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Logo"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_scan_qr
msgid "Logout"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_model.py:74
#, python-format
msgid "Make sure company base URL is added to be able to send attachments."
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:132
#, python-format
msgid "Make sure to add client ID, token and whatsapp url."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__connected_msg
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__message
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_multi_notify_orders
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_multi_whatsapp_action
msgid "Message"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_ids
msgid "Messages"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__model_name
msgid "Model Name"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.actions.report,name:olivery_whatsapp_multi.report_rb_delivery_order_multi_print_orders_money_collector_action
msgid "Money Collection Whatsapp"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Money collection cost"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_multi_whatsapp_user_inherited
msgid "Multiple WhatsApp Groups"
msgstr "اكثر من مجموعه"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_multi_whatsapp_user_inherited
msgid "No WhatsApp Groups"
msgstr "لا يوجد مجموعات"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__name
msgid "Name"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_chat_list_warning
msgid "No"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:54
#, python-format
msgid "No groups were found for your device."
msgstr "لم يتم ايجاد مجموعات للجهاز"

#. module: olivery_whatsapp_multi
#: selection:rb_delivery.notify_business,notification_type:0
#: selection:rb_delivery.notify_users,notification_type:0
msgid "Notification"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_business__notification_type
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_orders__notification_type
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_users__notification_type
msgid "Notification Type"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__notification_message
msgid "Notification message"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_notify_business
msgid "Notify Business Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_notify_orders
msgid "Notify Orders Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_notify_users
msgid "Notify Users Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_order
msgid "Order Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__override_existing
msgid "Override existing groups"
msgstr "تعديل المجموعات الموجوده"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:97
#, python-format
msgid "Please add Whatsapp group message to identify the user needed and get their WhatsApp group ID"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__provider
msgid "Provider"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__qr_code
msgid "QR code"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid "Recipient's Info"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Recipient's address"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Recipient's name"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Recipient's signature: .............................."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__partner_ids
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__partner_ids
msgid "Recipients"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__record_id
msgid "Record ID"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Returned Collection Report"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_returned_collection_send_whatsapp
msgid "Returned Collection Send Whatsapp Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.actions.report,name:olivery_whatsapp_multi.report_rb_delevery_returned_money_collection_action
msgid "Returned Collection Whatsapp"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Returned Notes"
msgstr ""

#. module: olivery_whatsapp_multi
#. openerp-web
#: code:addons/olivery_whatsapp_multi/static/src/js/buttons.js:26
#: code:addons/olivery_whatsapp_multi/static/src/xml/buttons.xml:5
#, python-format
msgid "Scan QR"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_scan_qr
msgid "Scan QR code Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_chat_list_wizzered
msgid "Send Request"
msgstr "ارسال الطلب"

#. module: olivery_whatsapp_multi
#: model:ir.actions.act_window,name:olivery_whatsapp_multi.action_rb_delivery_order_money_collection_send_whatsapp
#: model:ir.actions.act_window,name:olivery_whatsapp_multi.action_rb_delivery_order_returned_money_collection_send_whatsapp
msgid "Send WhatsApp"
msgstr "ارسال واتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__send_to_whatsapp_group
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_business__send_to_whatsapp_group
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_users__send_to_whatsapp_group
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__send_to_whatsapp_group
msgid "Send to WhatsApp group"
msgstr "ارسال الى مجموعة الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_action__send_to_whatsapp_group
msgid "Send to group"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Sequence number"
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:rb_delivery.notify_business,notification_type:0
#: selection:rb_delivery.notify_orders,notification_type:0
#: selection:rb_delivery.notify_users,notification_type:0
msgid "Sms"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_chat_list_warning
msgid "Some users has more than one group, to fix the issue click on Go to users and make sure each user has only one group"
msgstr "بعض المستخدمين لديهم اكثر من مجموعة على رقم الواتس اب , لحل المشكلة قم بالضغط على ايقونة "الذهاب الى المستخدمين " والتأكد من ان كل مستخدم لديه مجموعة واحدة فقط"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__status
msgid "Status"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__status_message
msgid "Status Message"
msgstr ""

#. module: olivery_whatsapp_multi
#: selection:rb_delivery.whatsapp_center,status:0
msgid "Success"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_orders__template
msgid "Template"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid "The recipient's signature: .............................."
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Total"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.returned_money_collection_custom
msgid "Total cost"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.multi_print_orders_money_collector_print_report_custom
msgid "Total net value"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_unread
msgid "Unread Messages"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__user_id
msgid "User"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_user
msgid "User Model"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__no_whatsapp_group_users
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__no_whatsapp_group_users
msgid "User with no WhatsApp group"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__users
msgid "Users"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:171
#, python-format
msgid "Warning: some users has multiple groups added"
msgstr "تحذير: بعض المستخدمين وجد لهم اكثر من مجموعة"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,help:olivery_whatsapp_multi.field_rb_delivery_whatsapp_center__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_multi_whatsapp_users
msgid "WhatsApp"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__whatsapp_group_ids
msgid "WhatsApp Group IDs"
msgstr "ارقام مجموعات الواتساب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__whatsapp_device_group_ids
msgid "WhatsApp Device Group IDs"
msgstr "ارقام اجهزة مجموعات الواتساب"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/money_collection/money_collection_wizard_model.py:121
#: code:addons/olivery_whatsapp_multi/models/returned_money_collection/returned_money_collection_wizard_model.py:122
#, python-format
msgid "WhatsApp message was sent by %s"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.ui.menu,name:olivery_whatsapp_multi.menu_rb_delivery_whatsapp
#: selection:rb_delivery.notify_business,notification_type:0
#: selection:rb_delivery.notify_orders,notification_type:0
#: selection:rb_delivery.notify_users,notification_type:0
msgid "Whatsapp"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.actions.act_window,name:olivery_whatsapp_multi.action_rb_delivery_whatsapp_center
#: model:ir.ui.menu,name:olivery_whatsapp_multi.menu_rb_delivery_whatsapp_center
msgid "Whatsapp Center"
msgstr "مركز الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.actions.act_window,name:olivery_whatsapp_multi.action_rb_delivery_whatsapp_chat_list
#: model:ir.ui.menu,name:olivery_whatsapp_multi.menu_rb_delivery_whatsapp_chat_list
msgid "Whatsapp Chat List"
msgstr "قائمة دردشة الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__whatsapp_group_id
msgid "Whatsapp Group ID"
msgstr "معرف مجموعة الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__number_of_groups
msgid "Number of groups"
msgstr "عدد المجموعات"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__new_group_id
msgid "Group Id"
msgstr "رقم المجموعه"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_order__last_sent_whatsapp_datetime
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__last_sent_whatsapp_datetime
msgid "Whatsapp Msg Date"
msgstr "تاريخ رسالة الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.actions.act_window,name:olivery_whatsapp_multi.action_rb_delivery_whatsapp_provider
#: model:ir.ui.menu,name:olivery_whatsapp_multi.menu_rb_delivery_whatsapp_provider
msgid "Whatsapp Provider"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_chat_list/whatsapp_chat_list_model.py:78
#, python-format
msgid "Whatsapp group id set successfully for %s"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_whatsapp_group_issues
msgid "Whatsapp group issues"
msgstr "مشاكل مجموعة الواتس اب"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_order__whatsapp_message_success_sent
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_user__whatsapp_message_success_sent
msgid "Whatsapp message successfully sent"
msgstr "رسائل الواتس اب المرسلة بنجاح"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_chat_list_wizzered
msgid "You are about to generate groups, do you want to override existing groups?"
msgstr "أنت تحاول تحديث مجموعة الواتس اب  لكل مستخدم ، هل أنت متأكد من أنك تريد تجاوز مجموعات الواتس اب الموجود ؟"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:60
#, python-format
msgid "You are already connected to WhatsApp"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:71
#, python-format
msgid "You are already connected to WhatsApp with mobile number %s"
msgstr ""

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_model.py:104
#, python-format
msgid "You have reached your limit to send whatsapp messages, please recharge and try again."
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_warning__names
msgid "names"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_olivery_whatsapp_multi_whatsapp_provider
msgid "olivery_whatsapp_multi.whatsapp_provider"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_whatsapp_center
msgid "rb_delivery.whatsapp_center"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model,name:olivery_whatsapp_multi.model_rb_delivery_whatsapp_chat_list
msgid "rb_delivery.whatsapp_chat_list"
msgstr ""

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_device__device_api
msgid "Api key"
msgstr "رمز الAPI"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_search_rb_delivery_whatsapp_chat_list
msgid "By Device"
msgstr "حسب الجهاز"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_action__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_chat_list_wizzered__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_business__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_orders__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_notify_users__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_returned_collection_send_whatsapp__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_scan_qr__device
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_chat_list__device
msgid "Device"
msgstr "جهاز"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_device__name
msgid "Device name"
msgstr "اسم الجهاز"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_olivery_whatsapp_multi_whatsapp_provider__devices
msgid "Devices"
msgstr "اجهزة"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_tree_rb_delivery_whatsapp_attachment
msgid "Download attachment"
msgstr "تنزيل المرفق"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_whatsapp_attachment__collection_id
msgid "Collection"
msgstr "التحصيل"

#. module: olivery_whatsapp_multi
#: model_terms:ir.ui.view,arch_db:olivery_whatsapp_multi.view_form_rb_delivery_money_collection_send_whatsapp
msgid "Download all attachments"
msgstr "تنزيل المرفقات"

#. module: olivery_whatsapp_multi
#: model:ir.model.fields,field_description:olivery_whatsapp_multi.field_rb_delivery_money_collection_send_whatsapp__send_time_eta
msgid "Send time ETA"
msgstr "زمن الارسال المتوقع"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:83
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:184
#, python-format
msgid "WhatsApp message retried successfully."
msgstr "تمت اعادة المحاولة والارسال بنجاج"

#. module: olivery_whatsapp_multi
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:189
#: code:addons/olivery_whatsapp_multi/models/whatsapp_center/whatsapp_center_wizard_model.py:191
#, python-format
msgid "Failed to retry WhatsApp message: %"
msgstr "فشل اعادة الارسل: %s"