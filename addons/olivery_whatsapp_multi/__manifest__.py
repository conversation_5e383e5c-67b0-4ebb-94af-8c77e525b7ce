# -*- coding: utf-8 -*-
{
    'name': "olivery_whatsapp_multi",
    'summary': """
        <PERSON><PERSON> Whatsapp multi App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.1.83',
    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'models/whatsapp_center/whatsapp_center_view.xml',
        'models/action/action_view.xml',
        'models/user/user_view.xml',
        'security/ir.model.access.csv',
        'view/module_view.xml',
        'view/templates.xml',
        'models/order/order_wizard_view.xml',
        'models/returned_money_collection/returned_money_collection_wizard_view.xml',
        'models/money_collection/money_collection_wizard_view.xml',
        'models/user/user_wizard_view.xml',
        'demo/client_conf.xml',
        'demo/whatsapp_provider_config.xml',
        'demo/cron_job.xml',
        'models/whatsapp_center/whatsapp_center_wizard_view.xml',
        'models/whatsapp_chat_list/whatsapp_chat_list_view.xml',
        'models/order/order_view.xml',
        'view/money_collection_report.xml',
        'view/returned_moeny_collection.xml',
        'models/whatsapp_provider/whatsapp_provider_view.xml',
        'models/whatsapp_attachment/whatsapp_attachment_view.xml',
        'models/otp_status_checker/otp_status_checker_view.xml'
    ],
    'qweb': [
        'static/src/xml/*.xml',
    ],
}
