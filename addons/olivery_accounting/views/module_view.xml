<?xml version="1.0"?>
<odoo>
    <data noupdate="0">

    <!-- Expenses -->
    <menuitem id="menu_olivery_accounting_expense_menu" name="Accounting" sequence="15" web_icon="olivery_accounting,static/src/images/accounting.png" groups="rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>

    <!-- expenses -->
    <act_window id="action_olivery_accounting_expense" name="Expenses" res_model="olivery_accounting.expense" view_mode="tree,form"/>
    <menuitem id="menu_olivery_accounting_expense" name="Expenses" parent="menu_olivery_accounting_expense_menu" sequence="11" action="action_olivery_accounting_expense" groups="rb_delivery.role_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>

    <!-- expenses category -->
    <act_window id="action_olivery_accounting_expense_category" name="Expenses Category" res_model="olivery_accounting.expense_category" view_mode="tree,form"/>
    <menuitem id="menu_olivery_accounting_expense_category" name="Expenses Category" parent="menu_olivery_accounting_expense_menu" sequence="15" action="action_olivery_accounting_expense_category" groups="rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>

    <menuitem id="menu_olivery_accounting_profit_menu" name="Profit" sequence="15" parent="menu_olivery_accounting_expense_menu"/>
    <!-- profit -->
    <act_window id="action_olivery_accounting_profit" name="Profit" res_model="olivery_accounting.profit" view_mode="tree,form" context="{'group_by':['create_date']}"/>
    <menuitem id="menu_olivery_accounting_profit" name="Profit" parent="menu_olivery_accounting_profit_menu" sequence="15" action="action_olivery_accounting_profit" groups="rb_delivery.role_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>

    <!-- Profit generator-->
    <act_window id="action_olivery_accounting_profit_generator" name="Profit Generator" target="inline" res_model="olivery_accounting.profit_generator" view_mode="form"/>
    <menuitem id="menu_olivery_accounting_profit_generator" name="Profit Generator" parent="menu_olivery_accounting_profit_menu" sequence="15" action="action_olivery_accounting_profit_generator"/>

    <act_window id="action_olivery_accounting_branch_reconciliation" name="Branch Reconciliation" res_model="olivery_accounting.branch_reconciliation" view_mode="form" target="inline"/>
    <menuitem id="menu_action_olivery_accounting_branch_reconciliation" name="Branch Reconciliation" parent="menu_olivery_accounting_expense_menu" sequence="11" action="action_olivery_accounting_branch_reconciliation" />

    <!-- Agent Receipt -->
    <act_window id="action_olivery_accounting_user_agent_receipt_action" name="Agent Receipt" res_model="olivery_accounting.agent_receipt" view_mode="tree,form"/>
    <menuitem id="menu_olivery_accounting_user_agent_receipt" name="Agent Receipt" parent="rb_delivery.menu_rb_delivery_collection" sequence="15" action="action_olivery_accounting_user_agent_receipt_action" groups="rb_delivery.role_super_manager,base.group_system"/>
    </data>
    </odoo>