<odoo >
    <data noupdate="1">

        <record id="status_action_select_state_show_expense" model="rb_delivery.status_related_field">
            <field name="name">show_expense</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field Expense in select state model when change to current status</field>
        </record>

        <record id="status_action_check_branch_collection" model="rb_delivery.status_action">
            <field name="name">check_branch_collection</field>
            <field name="description">Check if money collection record has branch or not</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('model', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_paid')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="rb_delivery.status_collection_paid" model="rb_delivery.status">
            <field name="status_action_ids" eval="[(4,ref('olivery_accounting.status_action_check_branch_collection'))]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('model', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_paid')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

    </data>
</odoo>