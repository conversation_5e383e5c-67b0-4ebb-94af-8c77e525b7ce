<odoo >
    <data noupdate="1">
        <record id="client_configuration_type_accounting" model="rb_delivery.client_configuration_type">
            <field name="name">Accounting</field>
            <field name="description">Accounting Information</field>
        </record>

        <record id="client_configuration_default_status_branch_reconciliation" model="rb_delivery.client_configuration">
            <field name="key">default_status_branch_reconciliation</field>
            <field name="value">False</field>
            <field name="description">In branch reconciliation only orders with one of these statuses will be taken when calculated reconciliation orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered'),ref('rb_delivery.status_paid')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_status_collection_branch_reconciliation" model="rb_delivery.client_configuration">
            <field name="key">default_status_collection_branch_reconciliation</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">branch_collection</field>
            <field name="status" eval="[(6, 0, [ref('olivery_branch_collection.status_branch_collection_money_out')])]" />
            <field name="description">Default status for branch collection when create branch collection through branch reconciliation with action type branch cash</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection" />
        </record>

        <record id="client_configuration_orders_statuses_when_select_agent" model="rb_delivery.client_configuration">
            <field name="key">orders_statuses_when_select_agent</field>
            <field name="value">False</field>
            <field name="description">only orders that match statuses in this configuration will be considered when select agent </field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered'),ref('rb_delivery.status_rejected'),ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck'),ref('rb_delivery.status_replacement')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="olivery_accounting.client_configuration_type_accounting"/>
        </record>

        <record id="client_configuration_agent_order_delivered_statuses" model="rb_delivery.client_configuration">
            <field name="key">agent_order_delivered_statuses</field>
            <field name="value">False</field>
            <field name="description">only orders that match statuses in this configuration will be considered when delivered orders </field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="olivery_accounting.client_configuration_type_accounting"/>
        </record>

        <record id="client_configuration_agent_order_returned_statuses" model="rb_delivery.client_configuration">
            <field name="key">agent_order_returned_statuses</field>
            <field name="value">False</field>
            <field name="description">only orders that match statuses in this configuration will be considered when returned orders </field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_rejected'),ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck'),ref('rb_delivery.status_replacement')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="olivery_accounting.client_configuration_type_accounting"/>
        </record>

    </data>
</odoo>