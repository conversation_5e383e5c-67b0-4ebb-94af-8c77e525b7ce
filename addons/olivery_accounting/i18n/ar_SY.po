# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_accounting
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-14 12:26+0000\n"
"PO-Revision-Date: 2022-06-14 12:26+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "<span style=\"font-size:1.8rem; font-weight:bold\">Agent Receipt</span>"
msgstr ""

#. module: olivery_accounting
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_expense_menu
msgid "Accounting"
msgstr "الحسابات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__action_type
msgid "Action Type"
msgstr "نوع الإجراء"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__address
msgid "Address"
msgstr "العنوان"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__advance_payment
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__advance_payment
msgid "Advance Payment"
msgstr "دفعة مقدمه"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__agent
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__driver_id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__agent_id
msgid "Agent"
msgstr "السائق"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__agent_cost
msgid "Agent Cost"
msgstr "تكلفة الموزع"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_agent_orders
#: model:ir.ui.menu,name:olivery_accounting.menu_action_olivery_accounting_agent_orders
msgid "Agent Orders"
msgstr "طلبيات السائق"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__agent_profit
msgid "Agent Profit"
msgstr "ربح السائق"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_user_agent_receipt_action
#: model:ir.actions.report,name:olivery_accounting.report_rb_delivery_agent_receipt_print
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_user_agent_receipt
msgid "Agent Receipt"
msgstr "ايصال السائق"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Agent Report"
msgstr "تقرير السائق"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__agent_cost
msgid "Agent cost"
msgstr "عمولة السائق"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Agent's name:"
msgstr "اسم السائق: "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "All"
msgstr "الكل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__allowed_statuses
msgid "Allowed statuses"
msgstr "الحالات المسموحة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__area_id
msgid "Area"
msgstr "المنطقة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_tree_olivery_accounting_agent_orders
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_tree_olivery_accounting_profit_generator
msgid "BarCode"
msgstr "باركود"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__barcode
msgid "Barcode"
msgstr "باركود"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__bonus
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__bonus
msgid "Bonus"
msgstr "علاوة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__branch_collection_id
msgid "Branch collection"
msgstr "كشف الفرع"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_agent_receipt
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
msgid "By Agent"
msgstr "السائق"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_agent_receipt
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
msgid "By Creator"
msgstr "المنشئ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_agent_receipt
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_profit
msgid "By Date"
msgstr "التاريخ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
msgid "By Expense Category"
msgstr "فئة التكلفة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
msgid "By Expense Type"
msgstr "نوع التكلفة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_profit
msgid "By From Date"
msgstr "من تاريخ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_agent_receipt
msgid "By Mobile Number"
msgstr "رقم الموبايل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_profit
msgid "By To Date"
msgstr "الى تاريخ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
msgid "By Type Of Expense"
msgstr "نوع التكلفة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__canceled_orders_count
msgid "Canceled order count"
msgstr "عدد الطلبيات الملغية"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__expense_category
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__category_id
msgid "Category"
msgstr "الفئة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_profit_generator
msgid "Choose from/to date:"
msgstr "اختار من/ الى تاريخ: "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_branch_reconciliation
msgid "Clear"
msgstr "مسح"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__collection_ids
msgid "Collections"
msgstr "كشوفات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__company_profit
msgid "Company Profit"
msgstr "ملف الموسسة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_branch_reconciliation
msgid "Confirm"
msgstr "تأكيد"

#. module: olivery_accounting
#: selection:olivery_accounting.profit_generator,generate_depend_on:0
msgid "Create Date"
msgstr "تاريخ الانشاء"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__create_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__create_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__currency
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
msgid "Currency"
msgstr "العملة"

#. module: olivery_accounting
#: selection:olivery_accounting.profit_generator,interval:0
msgid "Daily"
msgstr "يومي"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Date :"
msgstr "التاريخ : "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Day :"
msgstr "يوم : "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Delivered"
msgstr "تم التسليم"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__delivered_order_ids
msgid "Delivered Orders"
msgstr "طلبيات تم توصيلها"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__to_del_order_state
msgid "Delivered Status"
msgstr "حالات تم التوصيل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__delivered_orders_length
msgid "Delivered orders count"
msgstr "عدد الطلبيات الاتي تم توصيلها"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Delivered:"
msgstr "تم توصيل: "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_receipt
msgid "Delivery Cost"
msgstr "تكلفة التوصيل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Delivery Cost:"
msgstr "تكلفة التوصيل: "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__del_order_barcode
msgid "Delivery Order Barcode"
msgstr "باركود طلبيات التوصيل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__diesel
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__diesel
msgid "Diesel"
msgstr "ديزل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__discount
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__discount
msgid "Discount"
msgstr "الخصم"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__display_name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_profit_generator
msgid "Done"
msgstr "تم"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Driver's Signature: .............................."
msgstr "توقيع السائق : .........................."

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__error
msgid "Error"
msgstr "خطأ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Error / خطأ"
msgstr "خطأ"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__expense
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__expense
#: model:ir.model.fields,field_description:olivery_accounting.field_rb_delivery_branch_collection__expense_id
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_accounting_select_state
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_tree_olivery_accounting_expense
msgid "Expense"
msgstr "المصروفات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__expense_category
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__category_ids
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
msgid "Expense Category"
msgstr "فئات المصاريف"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__expense_value
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
msgid "Expense Local Value"
msgstr "قيمة المصاريف المحلية"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__expense_type
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_tree_olivery_accounting_expense
msgid "Expense Type"
msgstr "نوع المصاريف"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_expense
#: model:ir.model.fields,field_description:olivery_accounting.field_rb_delivery_select_state__expense_ids
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_expense
msgid "Expenses"
msgstr "المصروفات"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_expense_category
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_expense_category
msgid "Expenses Category"
msgstr "تصنيف المصروف"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_expense_type
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_expense_type
msgid "Expenses Type"
msgstr "نوع المصاريف"

#. module: olivery_accounting
#: selection:olivery_accounting.expense,type_of_expense:0
msgid "Fixed"
msgstr "ثابت"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__from_branch
msgid "From Branch"
msgstr "من فرع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__from_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__from_date
msgid "From Date"
msgstr "من تاريخ"

#. module: olivery_accounting
#: code:addons/olivery_accounting/models/profit_generator/profit_generator_model.py:176
#, python-format
msgid "From Date should be less than To Date"
msgstr "من تاريخ يجب ان تكون اقل من الى تاريخ"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__generate_depend_on
msgid "Generate Depends on"
msgstr "انشاء بناء على"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_agent_receipt
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_profit
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__id
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__interval
msgid "Interval"
msgstr "الفترة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__last_barcode
msgid "Last BarCode"
msgstr "اخر قرائة للباركود"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit____last_update
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_accounting
#: selection:olivery_accounting.profit_generator,generate_depend_on:0
msgid "Last Update ON"
msgstr "اخر تعديل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__write_uid
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__status_last_updated_on
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__status_last_updated_on
msgid "Status Last Updated on"
msgstr "آخر تحديث للحالة في"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__len_delivered_orders
msgid "Length of delivered orders"
msgstr "سعة الطلبيات التي تم توصيلها "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__len_orders
msgid "Length of orders"
msgstr "سعة الطلبيات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__len_other_orders
msgid "Length of other orders"
msgstr "سعة الطلبيات الاخرى"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__len_returned_orders
msgid "Length of returned orders"
msgstr ""

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Logo"
msgstr "الشعار"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__mobile_number
msgid "Mobile Number"
msgstr "رقم الموبايل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_receipt
msgid "Money collection"
msgstr "التحصيل"

#. module: olivery_accounting
#: selection:olivery_accounting.profit_generator,interval:0
msgid "Monthly"
msgstr "شهرياً"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_category__name
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__name
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense_type
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_search_olivery_accounting_profit
msgid "Name"
msgstr "الاسم"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Notes/ Loans / Discount:"
msgstr "الملاحظات / قروض / خصم : "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_accounting
#: model:ir.model.fields,help:olivery_accounting.field_olivery_accounting_expense__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__order_id
msgid "Order"
msgstr "الأمر"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__order_barcode
msgid "Order Barcode"
msgstr "باكود الطلبيات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__order_ids
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__order_ids
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__order_ids
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__orders_length
msgid "Orders count"
msgstr "عدد الطلبيات"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Orders:"
msgstr "الطلبيات: "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Other"
msgstr "غير ذلك"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__other_order_ids
msgid "Other Orders"
msgstr "الطلبيات الاخرى"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__to_other_order_state
msgid "Other Status"
msgstr "الحالات الاخرى"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__partial_rejected_orders_count
msgid "Partial rejected order count"
msgstr "عدد الطلبيا المرفوضة جزئي"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_profit
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__profit
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__profit_ids
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_profit
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_profit_menu
msgid "Profit"
msgstr "الربح"

#. module: olivery_accounting
#: model:ir.actions.act_window,name:olivery_accounting.action_olivery_accounting_profit_generator
#: model:ir.ui.menu,name:olivery_accounting.menu_olivery_accounting_profit_generator
msgid "Profit Generator"
msgstr "منشئ الربح"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__rate
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
msgid "Rate"
msgstr "سعر الصرف"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Receiver's Signature: .............................."
msgstr "توقيع المستلم: ......................"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__rejected_orders_length
msgid "Rejected orders count"
msgstr "عدد الطلبيات المرفوضة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Rejected:"
msgstr "مرفوض : "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Remaining amount:"
msgstr "الكمية المتبقية: "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__replacement_orders_count
msgid "Replacement order count"
msgstr "عدد الطلبيات"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Reporting"
msgstr "التقارير"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_receipt
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_branch_reconciliation
msgid "Required from business"
msgstr "مطلوب للتاجر"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__reschedule_orders_count
msgid "Reschedule order count"
msgstr "عدد الطلبيات الموجلة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Returned"
msgstr "مُرجَع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__returned_order_barcode
msgid "Returned Order Barcode"
msgstr "باركود الطلبيات المرجعة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__returned_order_ids
msgid "Returned Orders"
msgstr "الطلبيات المرتجعة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__to_returned_order_state
msgid "Returned Status"
msgstr "حالات الارجاع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__revenue
msgid "Revenue"
msgstr "الربح"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__salary
msgid "Salary"
msgstr "راتب"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Select Agent"
msgstr "اختيار سائق"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_receipt
msgid "Sequence Number"
msgstr "الرقم المتسلسل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__status_ids
msgid "Status"
msgstr "الحالة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_profit_generator
msgid "Status and Category"
msgstr "الحالات و الفئات"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__stuck_orders_length
msgid "Stuck orders count"
msgstr "عدد الطلبيات العالقة"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Stuck:"
msgstr "عالق: "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_orders__success
msgid "Success"
msgstr "إلغاء "

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
msgid "Success / تم"
msgstr "تم"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__name
msgid "Title"
msgstr "العنوان"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__to_be_paid
msgid "To Be Paid"
msgstr "جاهز للدفع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_branch_reconciliation__to_branch
msgid "To Branch"
msgstr "للفرع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit__to_date
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_profit_generator__to_date
msgid "To Date"
msgstr "الى تاريخ"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_agent_orders
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_branch_reconciliation
msgid "Total Amount"
msgstr "مجموع التحصيل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_branch_reconciliation
msgid "Total Delivery Cost"
msgstr "مجموع تكلفة التوصيل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__total_delivery_cost
msgid "Total Delivery Fee"
msgstr "مجموع تكلفة التوصيل"

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "Total Money Collection Cost:"
msgstr "مجموع التحصيل الكلي: "

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_agent_receipt__total_money_collection_cost
msgid "Total money collection cost"
msgstr "مجموع التحصيل"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense_type__name
msgid "Type"
msgstr "النوع"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__type_of_expense
#: model_terms:ir.ui.view,arch_db:olivery_accounting.view_form_olivery_accounting_expense
msgid "Type of expense"
msgstr "نوع التكاليف"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_accounting
#: model:ir.model.fields,field_description:olivery_accounting.field_olivery_accounting_expense__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_accounting
#: selection:olivery_accounting.expense,type_of_expense:0
msgid "Variable"
msgstr "متغير"

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_agent_orders
msgid "olivery_accounting.agent_orders"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_agent_receipt
msgid "olivery_accounting.agent_receipt"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_expense
msgid "olivery_accounting.expense"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_expense_category
msgid "olivery_accounting.expense_category"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_expense_type
msgid "olivery_accounting.expense_type"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_profit
msgid "olivery_accounting.profit"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_olivery_accounting_profit_generator
msgid "olivery_accounting.profit_generator"
msgstr ""

#. module: olivery_accounting
#: model_terms:ir.ui.view,arch_db:olivery_accounting.agent_receipt_print
msgid "pos-per-item"
msgstr ""

#. module: olivery_accounting
#: model:ir.model,name:olivery_accounting.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_office_needs
msgid "الأدوات المكتبية"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_salaries
msgid "الاجور و المرتبات"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_type,name:olivery_accounting.expenses_type_gas
msgid "سولار"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_type,name:olivery_accounting.expenses_type_miscellaneous
msgid "متفرقات"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_goods
msgid "مصاريف البضاعة المباعة"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_bank
msgid "مصاريف البنوك والحسابات"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_marketing
msgid "مصاريف التسويق و الدعاية"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_company_office
msgid "مصاريف مقر الشركة"
msgstr ""

#. module: olivery_accounting
#: model:olivery_accounting.expense_category,name:olivery_accounting.expenses_category_training
msgid "مصروفات تدريب وتعليم الموظفين"
msgstr ""

