# -*- coding: utf-8 -*-
from openerp import models, fields, api
from openerp.exceptions import ValidationError

class olivery_accounting_expense(models.Model):

    _name = 'olivery_accounting.expense'
    _order = "create_date DESC"
    _inherit = 'mail.thread'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------


    name = fields.Char('Name', required=True,track_visibility="on_change")

    expense = fields.Float('Expense',track_visibility="on_change")

    currency = fields.Many2one('res.currency', 'Currency',track_visibility="on_change")

    expense_category = fields.Many2one('olivery_accounting.expense_category', 'Category',track_visibility="on_change")

    rate = fields.Float("Rate",track_visibility="on_change")

    expense_value = fields.Float("Expense Local Value", compute="compute_expenses_values",track_visibility="on_change")

    type_of_expense = fields.Selection([('fixed', 'Fixed'),('variable', 'Variable')], string="Type of expense", default="fixed",track_visibility="on_change")

    branch_collection_id = fields.Many2one('rb_delivery.branch_collection','Branch collection',track_visibility="on_change")

    agent_id = fields.Many2one('rb_delivery.user','Agent',domain=[('role_code','=','rb_delivery.role_driver')],track_visibility="on_change")

    order_id = fields.Many2one('rb_delivery.order','Order',track_visibility="on_change")


    @api.one
    def compute_expenses_values(self):
        if self.rate != 0 and self.expense != 0:
            self.expense_value = self.rate * self.expense


