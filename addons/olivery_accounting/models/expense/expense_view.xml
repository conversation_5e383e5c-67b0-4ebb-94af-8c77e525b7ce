
<odoo>
  <data>
    <record id="view_form_olivery_accounting_expense" model="ir.ui.view">

      <field name="name">view_form_olivery_accounting_expense</field>
      <field name="model">olivery_accounting.expense</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>
            <div class="oe_title">
                <label for="name" string="Name" class="oe_edit_only"/>
                    <h1>
                        <field name="name"/>
                    </h1>
            </div>

             <group name="group_top">
              <group name="group_right">
                <field name="expense" string="Expense" />
                <field name="currency" string="Currency" />
                <field name="rate" string="Rate" />
                <field name="expense_value" string="Expense Local Value" attrs="{'invisible':[('rate', '=', 0)]}"/>
                <field name="agent_id"/>
                <field name="order_id"/>
              </group>
              <group name="group_left">
              <field name="create_date"/>
              <field name="expense_category" string="Expense Category" options="{'no_create': True, 'no_create_edit':True}"/>
              <field name="type_of_expense" string="Type of expense"/>
              <field name="branch_collection_id"/>
              </group>
            </group>

          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_olivery_accounting_expense" model="ir.ui.view">
      <field name="name">view_tree_olivery_accounting_expense</field>
      <field name="model">olivery_accounting.expense</field>

      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="expense" string="Expense" />
          <field name="expense_category"/>
          <field name="agent_id"/>
          <field name="create_date"/>
        </tree>
      </field>

    </record>

    <record id="view_search_olivery_accounting_expense" model="ir.ui.view">
            <field name="name">view_search_olivery_accounting_expense</field>
            <field name="model">olivery_accounting.expense</field>

            <field name="arch" type="xml">

                <search>
                    <group>
                        <field name="name" string="Name"/>
                        <field name="expense_category"/>
                        <field name="type_of_expense"/>
                        <field name="create_date"/>
                        <field name="agent_id"/>
                    </group>
                    <group string="Groups">
                        <filter name="group_by_create_date" string="By Date" icon="terp-partner" context="{'group_by':'create_date'}" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_expense_category" string="By Expense Category" icon="terp-partner" context="{'group_by':'expense_category'}" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_type_of_expense" string="By Type Of Expense" icon="terp-partner" context="{'group_by':'type_of_expense'}" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
                        <filter name="group_by_agent_id" string="By Agent" icon="terp-partner" context="{'group_by':'agent_id'}"/>
                    </group>

                </search>

            </field>

        </record>
  </data>
</odoo>
