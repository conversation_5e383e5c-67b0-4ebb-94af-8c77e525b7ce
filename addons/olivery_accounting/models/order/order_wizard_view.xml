<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <data>
      <!--inherit form view-->
      <record id="view_form_accounting_select_state" model="ir.ui.view">
         <field name="name">view_form_accounting_select_state</field>
         <field name="model">rb_delivery.select_state</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order_select_state" />
         <field name="arch" type="xml">
            <xpath expr="//group[@name='group_top']" position="after">
                <group name="group_bottom" string="Expense"  attrs="{'invisible': [('show_expense_ids','=',False)]}">
                    <field name="show_expense_ids" invisible="1"/>
                    <field name="expense_ids" widget="one2many_list">
                        <tree editable="bottom">
                            <field name="name"/>
                            <field name="expense" string="Expense" />
                            <field name="expense_category"/>
                            <field name="agent_id"/>
                            <field name="create_date"/>
                        </tree>
                    </field>
                </group>
            </xpath>
         </field>
      </record>
   </data>
</odoo>