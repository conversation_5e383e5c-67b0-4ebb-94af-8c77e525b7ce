# -*- coding: utf-8 -*-
from openerp import models, _,fields,api
from openerp.exceptions import ValidationError,Warning

class olivery_accounting_select_state(models.TransientModel):

    _inherit = 'rb_delivery.select_state'

    expense_ids = fields.Many2many(
        comodel_name = 'olivery_accounting.expense',
        string = 'Expenses',
        relation = 'select_state_expense',
        column1 = 'select_state_id',
        column2 = 'expense_id')

    show_expense_ids = fields.Boolean('Show Expense IDs', default=False)

    @api.onchange('state')
    def change_state(self):
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
        optional_status_actions = order_state.status_action_optional_related_fields
        self.show_expense_ids = False
        if order_state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'show_expense':
                    self.show_expense_ids = True

        return super(olivery_accounting_select_state, self).change_state()
