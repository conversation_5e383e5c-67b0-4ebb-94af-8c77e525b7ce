# -*- coding: utf-8 -*-
from openerp import models, fields, api
from openerp.exceptions import ValidationError

class olivery_accounting_profit(models.Model):

    _name = 'olivery_accounting.profit'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Name', required=True)

    expense = fields.Float('Expense')

    revenue = fields.Float('Revenue')

    agent_cost = fields.Float('Agent cost')

    profit = fields.Float('Profit')

    from_date = fields.Date(string="From Date", required=True)

    to_date = fields.Date(string="To Date", required=True)

    @api.model
    def create(self,values):
        if values['from_date'] and values['to_date']:
            from_date = values['from_date'].strftime('%Y-%m-%d %H:%M:%S')
            to_date = values['to_date'].strftime('%Y-%m-%d %H:%M:%S')
            values['name'] = from_date + '_' + to_date
        profit = super(olivery_accounting_profit, self).create(values)
        return profit