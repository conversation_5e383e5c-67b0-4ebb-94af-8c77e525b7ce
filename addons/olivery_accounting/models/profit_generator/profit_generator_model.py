# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from openerp.http import request, Response
from datetime import datetime, timedelta
import json


class olivery_accounting_profit_generator(models.Model):

    _name = 'olivery_accounting.profit_generator'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    # success = fields.Char("Success")

    # error = fields.Char("Error")

    @api.model
    def default_status(self):
        status = self.env['rb_delivery.status'].search([('name','=','completed'),'|',('status_type','=',False),('status_type','=','olivery_order')])
        return [status.id] if status else None

    @api.model
    def default_expense_category(self):
        expense_category = self.env.ref('olivery_accounting.expenses_category_company_office')
        return [expense_category.id] if expense_category else None

    from_date = fields.Date(string="From Date", default=fields.Date.context_today, required=True)

    to_date = fields.Date(string="To Date", default=fields.Date.context_today, required=True)

    interval = fields.Selection([('daily','Daily'),('monthly','Monthly')],string="Interval",default="daily")

    category_ids = fields.Many2many(
        comodel_name = 'olivery_accounting.expense_category',
        string = 'Expense Category',
        relation = 'profit_category_item',
        column1 = 'category_id',
        column2 = 'profit_id',default=default_expense_category)

    status_ids = fields.Many2many(
        comodel_name = 'rb_delivery.status',
        string = 'Status',
        relation = "profit_states_item",
        column1 = "status_id",
        column2 = "profit_id",default=default_status,domain="['|',('status_type','=',False),('status_type','=','olivery_order')]")

    profit_ids = fields.Many2many(
        comodel_name = 'olivery_accounting.profit',
        string = 'Profit',
        relation = 'profit_profit_item',
        column1 = 'profit_id',
        column2 = 'profit_ids')
    
    generate_depend_on = fields.Selection([('create_date','Create Date'),('status_last_updated_on','Status Last Update ON')],string="Generate Depends on",default="create_date")

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    @api.one
    def convert_data(self):
        self.profit_ids = [(6,0,[])]
        if self.from_date and self.to_date and self.status_ids and not self.category_ids:
            state_ids = []
            for status in self.status_ids:
                state_ids.append(status.name)
            if self.interval == 'daily':
                days = self.date_range(self.from_date , self.to_date)
                for day in days:
                    self.create_profit(state_ids,[],day,day)
            elif self.interval == 'monthly':
                days = self.date_range(self.from_date , self.to_date)
                sorted_list = sorted(days)
                month = sorted_list[0].month
                month_days = []
                for day in sorted_list:
                    if month == day.month:
                        month_days.append(day)
                        if sorted_list.index(day) == len(sorted_list)-1:
                            self.create_profit(state_ids,[],month_days[0],month_days[len(month_days)-1])
                    else:
                        month = day.month
                        self.create_profit(state_ids,[],month_days[0],month_days[len(month_days)-1])
                        month_days = []
                        month_days.append(day)
            else:
                self.create_profit(state_ids,[],self.from_date,self.to_date)

        if self.from_date and self.to_date and not self.status_ids and self.category_ids:
            ids = []
            for categ in self.category_ids:
                ids.append(categ.id)
            if self.interval == 'daily':
                days = self.date_range(self.from_date , self.to_date)
                for day in days:
                    self.create_profit([],ids,day,day)
            elif self.interval == 'monthly':
                days = self.date_range(self.from_date , self.to_date)
                sorted_list = sorted(days)
                month = sorted_list[0].month
                month_days = []
                for day in sorted_list:
                    if month == day.month:
                        month_days.append(day)
                        if sorted_list.index(day) == len(sorted_list)-1:
                            self.create_profit([],ids,month_days[0],month_days[len(month_days)-1])
                    else:
                        month = day.month
                        self.create_profit([],ids,month_days[0],month_days[len(month_days)-1])
                        month_days = []
                        month_days.append(day)
            else:
                self.create_profit([],ids,self.from_date,self.to_date)

        if self.from_date and self.to_date and not self.status_ids and not self.category_ids:
            if self.interval == 'daily':
                days = self.date_range(self.from_date , self.to_date)
                current_day = days[0]
                for day in days:
                    self.create_profit([],[],day,day)
            elif self.interval == 'monthly':
                days = self.date_range(self.from_date , self.to_date)
                sorted_list = sorted(days)
                month = sorted_list[0].month
                month_days = []
                for day in sorted_list:
                    if month == day.month:
                        month_days.append(day)
                        if sorted_list.index(day) == len(sorted_list)-1:
                            self.create_profit([],[],month_days[0],month_days[len(month_days)-1])
                    else:
                        month = day.month
                        self.create_profit([],[],month_days[0],month_days[len(month_days)-1])
                        month_days = []
                        month_days.append(day)
            else:
                self.create_profit([],[],self.from_date,self.to_date)

        if self.from_date and self.to_date and self.status_ids and self.category_ids:
            ids = []
            for categ in self.category_ids:
                ids.append(categ.id)
            state_ids = []
            for status in self.status_ids:
                state_ids.append(status.name)
            if self.interval == 'daily':
                days = self.date_range(self.from_date , self.to_date)
                for day in days:
                    self.create_profit(state_ids,ids,day,day)
            elif self.interval == 'monthly':
                days = self.date_range(self.from_date , self.to_date)
                sorted_list = sorted(days)
                month = sorted_list[0].month
                month_days = []
                for day in sorted_list:
                    if month == day.month:
                        month_days.append(day)
                        if sorted_list.index(day) == len(sorted_list)-1:
                            self.create_profit(state_ids,ids,month_days[0],month_days[len(month_days)-1])
                    else:
                        month = day.month
                        self.create_profit(state_ids,ids,month_days[0],month_days[len(month_days)-1])
                        month_days = []
                        month_days.append(day)
            else:
                self.create_profit(state_ids,ids,self.from_date,self.to_date)
        return True

    def date_range(self,start, end):
        delta = end - start  # as timedelta
        days = [start + timedelta(days=i) for i in range(delta.days + 1)]
        if days == []:
            raise ValidationError(_('From Date should be less than To Date'))
        return days

    @api.model
    def create_profit(self,status_domain,expense_categ_domain,from_date,to_date):
        date_from = from_date.strftime('%Y-%m-%d')
        date_to = to_date.strftime('%Y-%m-%d')
        if self.generate_depend_on == 'create_date':
            if len(status_domain) == 0:
                orders = self.env['rb_delivery.order'].search_read([('create_date','>=',date_from),('create_date','<=',date_to)],['delivery_cost','agent_cost'])
            else:
                orders = self.env['rb_delivery.order'].search_read([('create_date','>=',date_from),('create_date','<=',date_to),('state','in',status_domain)],['delivery_cost','agent_cost'])
            if len(expense_categ_domain) == 0:
                expenses = self.env['olivery_accounting.expense'].search([('create_date','>=',date_from),('create_date','<=',date_to)])
            else:
                expenses = self.env['olivery_accounting.expense'].search([('create_date','>=',date_from),('create_date','<=',date_to),('expense_category','in',expense_categ_domain)])
        else:
            if len(status_domain) == 0:
                orders = self.env['rb_delivery.order'].search_read([('status_last_updated_on','>=',date_from),('status_last_updated_on','<=',date_to)],['delivery_cost','agent_cost'])
            else:
                orders = self.env['rb_delivery.order'].search_read([('status_last_updated_on','>=',date_from),('status_last_updated_on','<=',date_to),('state','in',status_domain)],['delivery_cost','agent_cost'])
            if len(expense_categ_domain) == 0:
                expenses = self.env['olivery_accounting.expense'].search([('write_date','>=',date_from),('write_date','<=',date_to)])
            else:
                expenses = self.env['olivery_accounting.expense'].search([('write_date','>=',date_from),('write_date','<=',date_to),('expense_category','in',expense_categ_domain)])
        revenue = 0
        expenses_value = 0
        agent_cost = 0
        for order in orders:
            revenue += order.get('delivery_cost', 0)
            agent_cost += order.get('agent_cost', 0)
        for expense in expenses:
            if expense.rate == 0:
                expenses_value += expense.expense
            else:
                expenses_value += expense.expense_value

        profit = revenue - expenses_value - agent_cost



        values = {'expense':expenses_value,'revenue':revenue,'profit':profit,'agent_cost':agent_cost,'from_date':from_date,'to_date':to_date}
        profit_rec = self.env['olivery_accounting.profit'].create(values)
        self.profit_ids = [profit_rec.id]
        return True


    # ----------------------------------------------------------
    # Read, View
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
