<?xml version="1.0" encoding="UTF-8"?>

<odoo>

    <record id="view_tree_olivery_accounting_profit_generator" model="ir.ui.view">
        <field name="name">view_tree_olivery_accounting_profit_generator</field>
        <field name="model">olivery_accounting.profit_generator</field>
        <field name="arch" type="xml">
            <tree string="BarCode" create="false">

            </tree>
        </field>
    </record>

    <record id="view_form_olivery_accounting_profit_generator" model="ir.ui.view">
        <field name="name">view_form_olivery_accounting_profit_generator</field>
        <field name="model">olivery_accounting.profit_generator</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
                <sheet name="">
                    <div  style="padding: 20px; padding-bottom:0px">
                        <button name="convert_data" string="Done" type="object" style="padding:10px;"/>
                    </div>

                    <div  style="padding: 20px;">
                        <group string="Choose from/to date:">
                            <group name="group_right">
                                <field name="generate_depend_on"/>
                            </group>
                            <group name="group_right">
                            </group>
                            <group name="group_right">
                                <field name="from_date" />
                            </group>
                            <group name="group_right">
                                <field name="to_date" />
                            </group>

                        </group>

                        <group string="Status and Category">
                            <group name="group_right">
                               <field name='status_ids' widget="many2many_tags"/>
                               <field name='category_ids' widget="many2many_tags"/>
                            </group>
                            <group name="group_left">
                               <field name='interval'/>
                            </group>
                        </group>

                        <group>

                        <field name="profit_ids" readonly="1">
                            <tree>
                                <field name="from_date"/>
                                <field name="to_date"/>
                                <field name="expense"/>
                                <field name="revenue"/>
                                <field name="agent_cost"/>
                                <field name="profit"/>
                            </tree>
                        </field>

                        </group>
                    </div>
                </sheet>
        </field>
    </record>

</odoo>