# -*- coding: utf-8 -*-
from openerp import models, fields, api, _
from openerp.exceptions import ValidationError
from itertools import combinations
from datetime import datetime
import pytz

class olivery_accounting_branch_reconciliation(models.Model):

    _name = 'olivery_accounting.branch_reconciliation'
    _description='Model for branch reconciliation'
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    @api.model
    def get_default_branch(self):
        branch=self.env['rb_delivery.branch'].search([('main_branch','=',True)],limit=1)
        return branch if branch else None

    def _get_allowed_statuses(self):
        allowed_state_ids = self.env['rb_delivery.client_configuration'].get_param('default_status_branch_reconciliation')
        if allowed_state_ids:
            return [(6, 0, allowed_state_ids)]
        return []


    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'branch_reconciliation_order_item',
        column1 = 'branch_reconciliation_id',
        column2 = 'order_id')

    collection_ids = fields.Many2many(
        comodel_name = 'rb_delivery.multi_print_orders_money_collector',
        string = 'Collections',
        relation = 'branch_reconciliation_collection_item',
        column1 = 'branch_reconciliation_id',
        column2 = 'collection_id',readonly=True,compute="change_action_type")

    reconciliate_amount = fields.Float('Reconciliate amount')

    action_type= fields.Selection([
        ('branch_expense','Branch Expense'),
        ('branch_cash','Branch Cash')
    ],required=False)


    from_branch = fields.Many2one('rb_delivery.branch', 'From Branch',required=False)

    to_branch = fields.Many2one('rb_delivery.branch','To Branch',default=get_default_branch,required=False)

    allowed_statuses = fields.Many2many(
        comodel_name = 'rb_delivery.status',
        string = 'Allowed statuses',
        relation = 'branch_reconciliation_status_item',
        column1 = 'branch_reconciliation_id',
        column2 = 'status_id',default=_get_allowed_statuses)

    expense_category = fields.Many2one('olivery_accounting.expense_category','Expense Category')

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    @api.onchange('action_type','from_branch')
    def change_action_type(self):
        if self.action_type == 'branch_cash' and self.from_branch:
            collections = self.env['rb_delivery.multi_print_orders_money_collector'].search([('state','=','paid')])
            collections_without_branch = []
            collections_selected = []
            if collections:
                collections_without_branch = collections.filtered(lambda c: not c.branch_id).mapped('sequence')
                collections_selected = collections.filtered(lambda c: c.branch_id and self.from_branch.id == c.branch_id.id)
                total_money_collection_cost = sum(collections.mapped('total_money_collection_cost'))
                if collections_without_branch:
                    self.action_type = False
                    return {
                        'warning':{
                            'title': _('Warning'),
                            'message':_("The following collections do not have a branch assigned: %s. "
                                        "Please make sure to assign branches to all paid money collections.") % (collections_without_branch),
                        },
                    }
                self.reconciliate_amount = total_money_collection_cost
                self.collection_ids = [(6,0,collections_selected.ids)]
        else:
            self.reconciliate_amount = 0
            self.collection_ids = [(6,0,[])]



    def get_domain(self):
        domain = [('branch_collection_id','=',False),('money_collection_cost','>',1)]
        if self.from_branch:
            domain.append(('current_branch','=',self.from_branch.id))
        allowed_statuses = []
        allowed_state_ids = self.env['rb_delivery.client_configuration'].get_param('default_status_branch_reconciliation')
        if allowed_state_ids:
            allowed_statuses = self.env['rb_delivery.status'].browse(allowed_state_ids).mapped('name')
        if len(allowed_statuses)>0:
            domain.append(('state', 'in', allowed_statuses))
        return domain


    def get_orders_amounts(self, amounts, total_amount):
        n = len(amounts)
        choosen_amounts = [False] * (int(total_amount) + 1)
        choosen_amounts[0] = True
        chosen_paths = [[] for _ in range(int(total_amount) + 1)]

        for i in range(n):
            for j in range(int(total_amount), int(amounts[i]) - 1, -1):
                if choosen_amounts[j - int(amounts[i])]:
                    choosen_amounts[j] = True
                    chosen_paths[j] = chosen_paths[j - int(amounts[i])] + [i]
        if not choosen_amounts[int(total_amount)]:
            return False
        return chosen_paths[int(total_amount)]

    def get_orders(self):
        domain = self.get_domain()
        orders = self.env['rb_delivery.order'].search(domain)
        if not orders:
            raise ValidationError(_("There are no orders that can be reconciled."))

        amounts = [order.money_collection_cost for order in orders]
        order_ids = [order.id for order in orders]

        orders_sum = sum(amounts)

        if not self.reconciliate_amount:
            raise ValidationError(_("You need to specify a reconciliation amount."))

        if orders_sum < self.reconciliate_amount:
            raise ValidationError(_("Reconciliation amount is larger than the sum of orders."))

        selected_order_ids = []
        indices = self.get_orders_amounts(amounts, self.reconciliate_amount)
        if indices:
            selected_order_ids = [order_ids[i] for i in indices]
            self.order_ids = [(6, 0, selected_order_ids)]
        else:
            raise ValidationError(_("There are no combinations of orders to achieve the sum %s.") % (str(self.reconciliate_amount)))

    def clear(self):
        self.order_ids = [(6,0,[])]
        self.collection_ids = [(6,0,[])]
        self.from_branch = False
        self.action_type = ''

    def confirm(self):
        collection_vals = {}
        if self.order_ids:
            collection_vals['order_ids'] = self.order_ids
        if self.action_type == 'cash_expense':
            if self.from_branch:
                collection_vals['branch_id'] = self.from_branch.id
            if self.to_branch:
                collection_vals['to_branch_id'] = self.to_branch.id
        else:
            if self.to_branch:
                collection_vals['branch_id'] = self.to_branch.id

        if collection_vals and self.order_ids:
            try:
                timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
                today = datetime.now(pytz.timezone(timezone)).strftime("%d-%m-%Y")
                name = _(("Branch expenses_%s")%(today) )
                if self.action_type == 'branch_cash':
                    name = _(("Branch cash_%s")%(today) )
                    default_status_id = self.env['rb_delivery.client_configuration'].get_param('default_status_collection_branch_reconciliation')
                    if default_status_id:
                        default_status = self.env['rb_delivery.status'].browse(default_status_id[0])
                        collection_vals['state'] = default_status.name
                collection_vals['name'] = name
                collection = self.env['rb_delivery.branch_collection'].create(collection_vals)
                if self.action_type == 'branch_expense':
                    expense_vals = {'name':name,'expense':self.reconciliate_amount,'expense_category':self.env.ref('olivery_accounting.expenses_category_company_office').id,'branch_collection_id':collection.id}
   
                    if self.expense_category:
                        expense_vals['expense_category'] = self.expense_category.id
                    expense = self.env['olivery_accounting.expense'].create(expense_vals)
                    collection.write({'expense_id':expense.id})
            except Exception as e:
                raise ValidationError(_(str(e)))










    # ----------------------------------------------------------
    # Read, View
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
