# -*- coding: utf-8 -*-
from openerp import models, _
from openerp.exceptions import ValidationError,Warning

class olivery_accounting_money_collection(models.Model):

    _inherit = 'rb_delivery.multi_print_orders_money_collector'

    def check_branch_collection(self,values):
        collections_without_branch = []
        for rec in self:
            if not rec.to_branch_id and not values.get('to_branch_id'):
                collections_without_branch.append(rec.sequence)
        if collections_without_branch:
            raise Warning(
                _("The following collections do not have a branch assigned: %s. "
                  "Please make sure to assign branches to all paid money collections.") % (collections_without_branch)
            )


    def do_action_accounting(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','collection')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_accounting_money_collection,action.name)
                method_to_call(self,values)
            except Exception as e:
                if action.name == 'check_branch_collection' :
                    raise ValidationError(e)
                else:
                    pass

    def guard_function(self,values):
        if values.get('state'):
            self.do_action_accounting(values)
        return super(olivery_accounting_money_collection, self).guard_function(values)

