# -*- coding: utf-8 -*-
{
    'name': "olivery_accounting",
    'summary': """
        Olivery Accounting App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.18',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery','olivery_branch_collection','ks_dashboard_ninja'],

    # always loaded
    'data': [
        'data/sequence.xml',
        'security/ir.model.access.csv',
        'views/module_view.xml',
        'demo/expenses.xml',
        'demo/client_conf.xml',
        'demo/status.xml',
        'models/expense/expense_view.xml',
        'models/expense_type/expense_type_view.xml',
        'models/expense_category/expense_category_view.xml',
        'models/profit/profit_view.xml',
        'models/profit_generator/profit_generator_view.xml',
        'models/agent_orders/agent_orders_view.xml',
        'models/agent_receipt/agent_receipt_view.xml',
        'models/agent_receipt/agent_receipt_report.xml',
        'models/branch_reconciliation/branch_reconciliation_view.xml',
        'models/branch_collection/branch_collection_view.xml',
        'models/order/order_wizard_view.xml',
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
    'post_init_hook': 'post_init_hook',
}
