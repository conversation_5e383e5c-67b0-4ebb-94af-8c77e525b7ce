# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_driver_evaluation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-30 10:00:00+0000\n"
"PO-Revision-Date: 2025-01-30 10:00:00+0000\n"
"Last-Translator: Olivery Development Team\n"
"Language-Team: Arabic (Syria)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar_SY\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_root
msgid "Driver Evaluation"
msgstr "تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_config
msgid "Configuration"
msgstr "الإعدادات"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_config
msgid "Evaluation Settings"
msgstr "إعدادات التقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_management
msgid "Management"
msgstr "الإدارة"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_links
msgid "Evaluation Links"
msgstr "روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_results
msgid "Evaluation Results"
msgstr "نتائج التقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_generate_evaluation_links
msgid "Generate Links"
msgstr "إنشاء الروابط"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_reports
msgid "Reports"
msgstr "التقارير"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_evaluation_config
msgid "Driver Evaluation Configuration"
msgstr "إعدادات تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_evaluation_link
msgid "Driver Evaluation Link"
msgstr "رابط تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_evaluation_result
msgid "Driver Evaluation Result"
msgstr "نتيجة تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_evaluation_link_wizard
msgid "Generate Evaluation Links Wizard"
msgstr "معالج إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__name
msgid "Configuration Name"
msgstr "اسم الإعداد"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__name
msgid "Name for this evaluation configuration"
msgstr "اسم لإعداد التقييم هذا"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__eligible_role_ids
msgid "Eligible Roles"
msgstr "الأدوار المؤهلة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__eligible_role_ids
msgid "User roles that are eligible for evaluation"
msgstr "أدوار المستخدمين المؤهلة للتقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__evaluation_duration
msgid "Evaluation Duration (Days)"
msgstr "مدة التقييم (بالأيام)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__evaluation_duration
msgid "Number of days before evaluation link expires"
msgstr "عدد الأيام قبل انتهاء صلاحية رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__max_attempts
msgid "Maximum Attempts"
msgstr "الحد الأقصى للمحاولات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__max_attempts
msgid "Maximum number of evaluation attempts per user"
msgstr "الحد الأقصى لعدد محاولات التقييم لكل مستخدم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__active
msgid "Active"
msgstr "نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__active
msgid "Whether this configuration is currently active"
msgstr "ما إذا كان هذا الإعداد نشطاً حالياً"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__evaluation_criteria
msgid "Evaluation Criteria"
msgstr "معايير التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__evaluation_criteria
msgid "JSON configuration for evaluation criteria and questions"
msgstr "إعداد JSON لمعايير التقييم والأسئلة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__notification_whatsapp
msgid "Send WhatsApp Notifications"
msgstr "إرسال إشعارات واتس اب"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__notification_whatsapp
msgid "Send Whatsapp Notifications when evaluation links are generated"
msgstr "إرسال إشعارات واتس اب عند إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__company_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__company_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__company_id
msgid "Company"
msgstr "الشركة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__user_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__user_id
msgid "Driver"
msgstr "السائق"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__user_id
msgid "Driver to be evaluated"
msgstr "السائق المراد تقييمه"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__user_id
msgid "Driver who was evaluated"
msgstr "السائق الذي تم تقييمه"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__token
msgid "Evaluation Token"
msgstr "رمز التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__token
msgid "Unique token for evaluation access"
msgstr "رمز فريد للوصول إلى التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__created_date
msgid "Created Date"
msgstr "تاريخ الإنشاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__expiry_date
msgid "Expiry Date"
msgstr "تاريخ انتهاء الصلاحية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__expiry_date
msgid "Date when the evaluation link expires"
msgstr "التاريخ الذي ينتهي فيه رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__is_used
msgid "Is Used"
msgstr "مستخدم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__is_used
msgid "Whether the evaluation link has been used"
msgstr "ما إذا كان رابط التقييم قد تم استخدامه"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__used_date
msgid "Used Date"
msgstr "تاريخ الاستخدام"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__used_date
msgid "Date when the evaluation was completed"
msgstr "التاريخ الذي تم فيه إكمال التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__evaluator_info
msgid "Evaluator Information"
msgstr "معلومات المقيم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__evaluator_info
msgid "Information about the person conducting the evaluation"
msgstr "معلومات حول الشخص الذي يقوم بالتقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__evaluation_url
msgid "Evaluation URL"
msgstr "رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__evaluation_url
msgid "Complete URL for the evaluation"
msgstr "الرابط الكامل للتقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__is_expired
msgid "Is Expired"
msgstr "منتهي الصلاحية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link__is_expired
msgid "Whether the evaluation link has expired"
msgstr "ما إذا كان رابط التقييم قد انتهت صلاحيته"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link__state
msgid "State"
msgstr "الحالة"

#. module: olivery_driver_evaluation
#: selection:evaluation.link,state:0
msgid "Active"
msgstr "نشط"

#. module: olivery_driver_evaluation
#: selection:evaluation.link,state:0
msgid "Used"
msgstr "مستخدم"

#. module: olivery_driver_evaluation
#: selection:evaluation.link,state:0
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__link_id
msgid "Evaluation Link"
msgstr "رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__link_id
msgid "Associated evaluation link"
msgstr "رابط التقييم المرتبط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__score
msgid "Overall Score"
msgstr "النتيجة الإجمالية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__score
msgid "Overall evaluation score (0-100)"
msgstr "النتيجة الإجمالية للتقييم (0-100)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__feedback
msgid "General Feedback"
msgstr "الملاحظات العامة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__feedback
msgid "General feedback about the driver"
msgstr "الملاحظات العامة حول السائق"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__submission_date
msgid "Submission Date"
msgstr "تاريخ الإرسال"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__evaluation_data
msgid "Evaluation Data"
msgstr "بيانات التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__evaluation_data
msgid "JSON data containing detailed evaluation responses"
msgstr "بيانات JSON تحتوي على ردود التقييم التفصيلية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__driving_skills_score
msgid "Driving Skills Score"
msgstr "نتيجة مهارات القيادة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__driving_skills_score
msgid "Score for driving skills (0-10)"
msgstr "نتيجة مهارات القيادة (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__punctuality_score
msgid "Punctuality Score"
msgstr "نتيجة الالتزام بالمواعيد"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__punctuality_score
msgid "Score for punctuality (0-10)"
msgstr "نتيجة الالتزام بالمواعيد (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__customer_service_score
msgid "Customer Service Score"
msgstr "نتيجة خدمة العملاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__customer_service_score
msgid "Score for customer service (0-10)"
msgstr "نتيجة خدمة العملاء (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__vehicle_maintenance_score
msgid "Vehicle Maintenance Score"
msgstr "نتيجة صيانة المركبة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__vehicle_maintenance_score
msgid "Score for vehicle maintenance (0-10)"
msgstr "نتيجة صيانة المركبة (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__professionalism_score
msgid "Professionalism Score"
msgstr "نتيجة الاحترافية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__professionalism_score
msgid "Score for professionalism (0-10)"
msgstr "نتيجة الاحترافية (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link_wizard__user_ids
msgid "Drivers"
msgstr "السائقون"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link_wizard__user_ids
msgid "Select drivers for whom to generate evaluation links"
msgstr "اختر السائقين الذين تريد إنشاء روابط تقييم لهم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link_wizard__force_generate
msgid "Force Generate"
msgstr "إجبار الإنشاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link_wizard__force_generate
msgid "Generate links even if active links already exist for the user"
msgstr "إنشاء الروابط حتى لو كانت هناك روابط نشطة موجودة للمستخدم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_link_wizard__send_notification
msgid "Send Email Notification"
msgstr "إرسال إشعار بالبريد الإلكتروني"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_link_wizard__send_notification
msgid "Send email notification to administrators about generated links"
msgstr "إرسال إشعار بالبريد الإلكتروني للمديرين حول الروابط المُنشأة"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_config.py:68
#, python-format
msgid "Evaluation duration must be greater than 0 days."
msgstr "يجب أن تكون مدة التقييم أكبر من 0 أيام."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_config.py:74
#, python-format
msgid "Maximum attempts must be greater than 0."
msgstr "يجب أن يكون الحد الأقصى للمحاولات أكبر من 0."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_result.py:118
#, python-format
msgid "Overall score must be between 0 and 100."
msgstr "يجب أن تكون النتيجة الإجمالية بين 0 و 100."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link.py:124
#, python-format
msgid "Evaluation token must be unique."
msgstr "يجب أن يكون رمز التقييم فريداً."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link.py:159
#, python-format
msgid "Cannot regenerate token for used evaluation link."
msgstr "لا يمكن إعادة إنشاء رمز لرابط تقييم مستخدم."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:46
#, python-format
msgid "Please select at least one driver."
msgstr "يرجى اختيار سائق واحد على الأقل."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:61
#, python-format
msgid "User already has active evaluation links"
msgstr "المستخدم لديه روابط تقييم نشطة بالفعل"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:80
#, python-format
msgid "Successfully generated %d evaluation links."
msgstr "تم إنشاء %d روابط تقييم بنجاح."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:89
#, python-format
msgid "Failed to generate links for %d users:\n%s"
msgstr "فشل في إنشاء روابط لـ %d مستخدمين:\n%s"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:97
#, python-format
msgid "Generated Evaluation Links"
msgstr "روابط التقييم المُنشأة"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:112
#, python-format
msgid "Generation Failed"
msgstr "فشل الإنشاء"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:139
#, python-format
msgid "Ineligible Users Selected"
msgstr "تم اختيار مستخدمين غير مؤهلين"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/wizard/evaluation_link_wizard.py:141
#, python-format
msgid "The following users are not eligible for evaluation based on current configuration:\n%s"
msgstr "المستخدمون التاليون غير مؤهلين للتقييم بناءً على الإعداد الحالي:\n%s"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "General Settings"
msgstr "الإعدادات العامة"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Role Settings"
msgstr "إعدادات الأدوار"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Evaluation Criteria"
msgstr "معايير التقييم"

#. module: olivery_driver_evaluation
#: view:evaluation.link:olivery_driver_evaluation.view_evaluation_link_form
msgid "Link Information"
msgstr "معلومات الرابط"

#. module: olivery_driver_evaluation
#: view:evaluation.link:olivery_driver_evaluation.view_evaluation_link_form
msgid "Status Information"
msgstr "معلومات الحالة"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Evaluation Information"
msgstr "معلومات التقييم"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Scores"
msgstr "النتائج"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Evaluator Information"
msgstr "معلومات المقيم"

#. module: olivery_driver_evaluation
#: view:evaluation.link.wizard:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Generate Evaluation Links"
msgstr "إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: view:evaluation.link.wizard:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Options"
msgstr "الخيارات"

#. module: olivery_driver_evaluation
#: view:evaluation.link.wizard:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Generate"
msgstr "إنشاء"

#. module: olivery_driver_evaluation
#: view:evaluation.link.wizard:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_driver_evaluation
#: model:evaluation.config,name:olivery_driver_evaluation.default_evaluation_config
msgid "Default Driver Evaluation Configuration"
msgstr "إعداد تقييم السائق الافتراضي"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Driving Skills"
msgstr "مهارات القيادة"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Overall driving ability, safety awareness, and adherence to traffic rules"
msgstr "القدرة العامة على القيادة والوعي بالسلامة والالتزام بقواعد المرور"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Punctuality"
msgstr "الالتزام بالمواعيد"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Timeliness in pickup, delivery, and scheduling"
msgstr "الالتزام بالوقت في الاستلام والتسليم والجدولة"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Customer Service"
msgstr "خدمة العملاء"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Quality of customer interaction and service attitude"
msgstr "جودة التفاعل مع العملاء وموقف الخدمة"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Vehicle Maintenance"
msgstr "صيانة المركبة"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Care and maintenance of delivery vehicle"
msgstr "العناية والصيانة لمركبة التوصيل"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Professionalism"
msgstr "الاحترافية"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "Professional behavior, appearance, and communication"
msgstr "السلوك المهني والمظهر والتواصل"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How would you rate the driver's overall safety while driving?"
msgstr "كيف تقيم السلامة العامة للسائق أثناء القيادة؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How well does the driver follow traffic rules and regulations?"
msgstr "إلى أي مدى يتبع السائق قواعد وأنظمة المرور؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How would you rate the driver's vehicle handling skills?"
msgstr "كيف تقيم مهارات السائق في التعامل مع المركبة؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How punctual is the driver for scheduled pickups?"
msgstr "ما مدى التزام السائق بمواعيد الاستلام المجدولة؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How reliable is the driver in meeting delivery deadlines?"
msgstr "ما مدى موثوقية السائق في الوفاء بمواعيد التسليم؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How well does the driver communicate delays or issues?"
msgstr "إلى أي مدى يتواصل السائق بشأن التأخير أو المشاكل؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How would you rate the driver's interaction with customers?"
msgstr "كيف تقيم تفاعل السائق مع العملاء؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How professional is the driver's attitude towards customers?"
msgstr "ما مدى احترافية موقف السائق تجاه العملاء؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How well does the driver handle customer complaints or issues?"
msgstr "إلى أي مدى يتعامل السائق مع شكاوى أو مشاكل العملاء؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How well does the driver maintain vehicle cleanliness?"
msgstr "إلى أي مدى يحافظ السائق على نظافة المركبة؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How proactive is the driver in reporting vehicle issues?"
msgstr "ما مدى استباقية السائق في الإبلاغ عن مشاكل المركبة؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How would you rate the overall condition of the driver's vehicle?"
msgstr "كيف تقيم الحالة العامة لمركبة السائق؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How professional is the driver's appearance and dress code?"
msgstr "ما مدى احترافية مظهر السائق وقواعد اللباس؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How effective is the driver's communication skills?"
msgstr "ما مدى فعالية مهارات التواصل لدى السائق؟"

#. module: olivery_driver_evaluation
#: data:evaluation_criteria
msgid "How well does the driver represent the company's values?"
msgstr "إلى أي مدى يمثل السائق قيم الشركة؟"

#. module: olivery_driver_evaluation
#: view:evaluation.link:olivery_driver_evaluation.view_evaluation_link_tree
msgid "Regenerate Token"
msgstr "إعادة إنشاء الرمز"

#. module: olivery_driver_evaluation
#: view:evaluation.link:olivery_driver_evaluation.view_evaluation_link_tree
msgid "Send WhatsApp"
msgstr "إرسال واتساب"

#. module: olivery_driver_evaluation
#: view:evaluation.link:olivery_driver_evaluation.view_evaluation_link_tree
msgid "Copy URL"
msgstr "نسخ الرابط"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_config
msgid "Evaluation Configuration"
msgstr "إعداد التقييم"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_link
msgid "Evaluation Links"
msgstr "روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_result
msgid "Evaluation Results"
msgstr "نتائج التقييم"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_link_wizard
msgid "Generate Evaluation Links"
msgstr "إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_1_title
msgid "Question 1 Title"
msgstr "عنوان السؤال الأول"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_1_title
msgid "Title for the first evaluation question"
msgstr "عنوان السؤال الأول في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_1_description
msgid "Question 1 Description"
msgstr "وصف السؤال الأول"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_1_description
msgid "Description for the first evaluation question"
msgstr "وصف السؤال الأول في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_1_active
msgid "Question 1 Active"
msgstr "السؤال الأول نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_1_active
msgid "Whether question 1 is active and visible"
msgstr "ما إذا كان السؤال الأول نشطاً ومرئياً"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_2_title
msgid "Question 2 Title"
msgstr "عنوان السؤال الثاني"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_2_title
msgid "Title for the second evaluation question"
msgstr "عنوان السؤال الثاني في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_2_description
msgid "Question 2 Description"
msgstr "وصف السؤال الثاني"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_2_description
msgid "Description for the second evaluation question"
msgstr "وصف السؤال الثاني في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_2_active
msgid "Question 2 Active"
msgstr "السؤال الثاني نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_2_active
msgid "Whether question 2 is active and visible"
msgstr "ما إذا كان السؤال الثاني نشطاً ومرئياً"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_3_title
msgid "Question 3 Title"
msgstr "عنوان السؤال الثالث"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_3_title
msgid "Title for the third evaluation question"
msgstr "عنوان السؤال الثالث في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_3_description
msgid "Question 3 Description"
msgstr "وصف السؤال الثالث"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_3_description
msgid "Description for the third evaluation question"
msgstr "وصف السؤال الثالث في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_3_active
msgid "Question 3 Active"
msgstr "السؤال الثالث نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_3_active
msgid "Whether question 3 is active and visible"
msgstr "ما إذا كان السؤال الثالث نشطاً ومرئياً"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_4_title
msgid "Question 4 Title"
msgstr "عنوان السؤال الرابع"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_4_title
msgid "Title for the fourth evaluation question"
msgstr "عنوان السؤال الرابع في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_4_description
msgid "Question 4 Description"
msgstr "وصف السؤال الرابع"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_4_description
msgid "Description for the fourth evaluation question"
msgstr "وصف السؤال الرابع في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_4_active
msgid "Question 4 Active"
msgstr "السؤال الرابع نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_4_active
msgid "Whether question 4 is active and visible"
msgstr "ما إذا كان السؤال الرابع نشطاً ومرئياً"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_5_title
msgid "Question 5 Title"
msgstr "عنوان السؤال الخامس"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_5_title
msgid "Title for the fifth evaluation question"
msgstr "عنوان السؤال الخامس في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_5_description
msgid "Question 5 Description"
msgstr "وصف السؤال الخامس"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_5_description
msgid "Description for the fifth evaluation question"
msgstr "وصف السؤال الخامس في التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_config__question_5_active
msgid "Question 5 Active"
msgstr "السؤال الخامس نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_config__question_5_active
msgid "Whether question 5 is active and visible"
msgstr "ما إذا كان السؤال الخامس نشطاً ومرئياً"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Dynamic Questions"
msgstr "الأسئلة الديناميكية"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 1"
msgstr "السؤال الأول"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 2"
msgstr "السؤال الثاني"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 3"
msgstr "السؤال الثالث"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 4"
msgstr "السؤال الرابع"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 5"
msgstr "السؤال الخامس"

#. module: olivery_driver_evaluation
#: view:evaluation.config:olivery_driver_evaluation.view_evaluation_config_form
msgid "Advanced Criteria (JSON)"
msgstr "المعايير المتقدمة (JSON)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__question_1_score
msgid "Question 1 Score"
msgstr "نتيجة السؤال الأول"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__question_1_score
msgid "Score for question 1 (0-10)"
msgstr "نتيجة السؤال الأول (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__question_2_score
msgid "Question 2 Score"
msgstr "نتيجة السؤال الثاني"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__question_2_score
msgid "Score for question 2 (0-10)"
msgstr "نتيجة السؤال الثاني (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__question_3_score
msgid "Question 3 Score"
msgstr "نتيجة السؤال الثالث"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__question_3_score
msgid "Score for question 3 (0-10)"
msgstr "نتيجة السؤال الثالث (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__question_4_score
msgid "Question 4 Score"
msgstr "نتيجة السؤال الرابع"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__question_4_score
msgid "Score for question 4 (0-10)"
msgstr "نتيجة السؤال الرابع (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_evaluation_result__question_5_score
msgid "Question 5 Score"
msgstr "نتيجة السؤال الخامس"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_evaluation_result__question_5_score
msgid "Score for question 5 (0-10)"
msgstr "نتيجة السؤال الخامس (0-10)"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Dynamic Question Scores"
msgstr "نتائج الأسئلة الديناميكية"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Questions 1-3"
msgstr "الأسئلة 1-3"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Questions 4-5"
msgstr "الأسئلة 4-5"

#. module: olivery_driver_evaluation
#: view:evaluation.result:olivery_driver_evaluation.view_evaluation_result_form
msgid "Legacy Scores"
msgstr "النتائج القديمة"
