<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Main Evaluation App Template -->
        <template id="evaluation_app_template" name="Driver Evaluation App">
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <title>Driver Evaluation - <t t-esc="driver_name"/></title>
                    <style>
                        body {
                            margin: 0;
                            padding: 0;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background-color: #f5f5f5;
                        }

                        .evaluation-iframe-container {
                            width: 100%;
                            height: 100vh;
                            border: none;
                            display: block;
                        }

                        .loading-fallback {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            height: 100vh;
                            text-align: center;
                            padding: 20px;
                        }

                        .loading-spinner {
                            width: 40px;
                            height: 40px;
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin-bottom: 20px;
                        }

                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }

                        .error-message {
                            background-color: #f8d7da;
                            color: #721c24;
                            border: 1px solid #f5c6cb;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px;
                            text-align: center;
                        }

                        .success-message {
                            background-color: #d4edda;
                            color: #155724;
                            border: 1px solid #c3e6cb;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px;
                            text-align: center;
                        }
                    </style>
                </head>
                <body>
                    <div id="loading-fallback" class="loading-fallback">
                        <div class="loading-spinner"></div>
                        <h2>Loading Driver Evaluation</h2>
                        <p>Evaluating: <strong><t t-esc="driver_name"/></strong></p>
                        <p>Please wait while we load the evaluation form...</p>
                    </div>

                    <iframe
                        id="evaluation-iframe"
                        class="evaluation-iframe-container"
                        style="display: none;"
                        onload="handleIframeLoad()"
                        onerror="handleIframeError()">
                    </iframe>

                    <script>
                        // Evaluation data for iframe communication
                        const evaluationData = {
                            token: '<t t-esc="token"/>',
                            driverName: '<t t-esc="driver_name"/>',
                            driverId: <t t-esc="driver_id"/>,
                            linkId: <t t-esc="link_id"/>,
                            expiryDate: '<t t-esc="expiry_date"/>'
                        };

                        // Initialize iframe
                        function initializeIframe() {
                            const iframe = document.getElementById('evaluation-iframe');
                            const baseUrl = '/olivery_driver_evaluation/static/src/angular-dist/index.html';
                            const params = new URLSearchParams({
                                token: evaluationData.token,
                                driverName: evaluationData.driverName,
                                driverId: evaluationData.driverId,
                                linkId: evaluationData.linkId,
                                expiryDate: evaluationData.expiryDate
                            });

                            iframe.src = baseUrl + '?' + params.toString();
                        }

                        // Handle iframe load
                        function handleIframeLoad() {
                            const iframe = document.getElementById('evaluation-iframe');
                            const loading = document.getElementById('loading-fallback');

                            // Hide loading, show iframe
                            loading.style.display = 'none';
                            iframe.style.display = 'block';

                            // Send evaluation data to iframe
                            setTimeout(() => {
                                iframe.contentWindow.postMessage({
                                    type: 'EVALUATION_DATA',
                                    payload: evaluationData
                                }, '*');
                            }, 1000);
                        }

                        // Handle iframe error
                        function handleIframeError() {
                            showErrorMessage('Failed to load evaluation form. Please try refreshing the page or contact support.');
                        }

                        // Listen for messages from iframe
                        window.addEventListener('message', function(event) {
                            if (event.data &amp;&amp; event.data.type) {
                                switch (event.data.type) {
                                    case 'REQUEST_EVALUATION_DATA':
                                        // Send evaluation data to iframe
                                        event.source.postMessage({
                                            type: 'EVALUATION_DATA',
                                            payload: evaluationData
                                        }, '*');
                                        break;

                                    case 'EVALUATION_COMPLETE':
                                        // Handle successful evaluation completion
                                        showSuccessMessage(event.data.payload);
                                        break;

                                    case 'EVALUATION_ERROR':
                                        // Handle evaluation errors
                                        showErrorMessage(event.data.payload.error);
                                        break;
                                }
                            }
                        });

                        function showSuccessMessage(payload) {
                            const loading = document.getElementById('loading-fallback');
                            const iframe = document.getElementById('evaluation-iframe');

                            iframe.style.display = 'none';
                            loading.style.display = 'flex';

                            // Check if Arabic content is provided
                            const isArabic = payload.isArabic || false;
                            const direction = isArabic ? 'rtl' : 'ltr';
                            const textAlign = isArabic ? 'right' : 'center';

                            // Use translated messages if available
                            const successTitle = payload.message || 'Evaluation Submitted Successfully!';
                            const thankYouMsg = payload.thankYou || 'Thank you for your feedback!';
                            const scoreLabel = payload.overallScoreLabel || 'Overall Score';
                            const inactiveMsg = payload.linkInactiveMessage || 'This evaluation link is now inactive.';

                            loading.innerHTML = '&lt;div class="success-message" style="direction: ' + direction + '; text-align: ' + textAlign + ';"&gt;' +
                                '&lt;h3 style="color: #28a745; margin-bottom: 20px;"&gt;✓ ' + successTitle + '&lt;/h3&gt;' +
                                '&lt;p style="font-size: 1.1rem; margin-bottom: 15px;"&gt;' + thankYouMsg + '&lt;/p&gt;' +
                                (payload.overallScore ?
                                    '&lt;p style="margin-bottom: 15px;"&gt;&lt;strong&gt;' + scoreLabel + ': ' + payload.overallScore + '&lt;/strong&gt;&lt;/p&gt;' : '') +
                                '&lt;p style="color: #666;"&gt;' + inactiveMsg + '&lt;/p&gt;' +
                                '&lt;/div&gt;';
                        }

                        function showErrorMessage(errorData) {
                            const loading = document.getElementById('loading-fallback');
                            const iframe = document.getElementById('evaluation-iframe');

                            iframe.style.display = 'none';
                            loading.style.display = 'flex';

                            // Handle both string and object error data
                            const error = typeof errorData === 'string' ? errorData : errorData.error;
                            const isArabic = errorData.isArabic || false;
                            const direction = isArabic ? 'rtl' : 'ltr';
                            const textAlign = isArabic ? 'right' : 'center';

                            const errorTitle = isArabic ? 'خطأ في التقييم' : 'Evaluation Error';
                            const tryAgainText = isArabic ? 'حاول مرة أخرى' : 'Try Again';
                            const defaultError = isArabic ? 'حدث خطأ أثناء التقييم.' : 'An error occurred during evaluation.';

                            loading.innerHTML = '&lt;div class="error-message" style="direction: ' + direction + '; text-align: ' + textAlign + ';"&gt;' +
                                '&lt;h3 style="color: #dc3545; margin-bottom: 20px;"&gt;' + errorTitle + '&lt;/h3&gt;' +
                                '&lt;p style="margin-bottom: 20px;"&gt;' + (error || defaultError) + '&lt;/p&gt;' +
                                '&lt;button onclick="location.reload()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem;"&gt;' +
                                tryAgainText +
                                '&lt;/button&gt;' +
                                '&lt;/div&gt;';
                        }

                        // Initialize the iframe
                        initializeIframe();

                        // Fallback timeout
                        setTimeout(() => {
                            const iframe = document.getElementById('evaluation-iframe');
                            const loading = document.getElementById('loading-fallback');

                            if (loading.style.display !== 'none') {
                                handleIframeError();
                            }
                        }, 10000); // 10 second timeout
                    </script>
                </body>
            </html>
        </template>

        <!-- Evaluation Used Template -->
        <template id="evaluation_used_template" name="Evaluation Already Used">
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <title>Evaluation Completed</title>
                    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"/>
                </head>
                <body>
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h2>✓ Evaluation Completed</h2>
                                        <p class="lead"><t t-esc="message"/></p>
                                        <p>Thank you for your participation. This evaluation link has already been used and cannot be accessed again.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
        </template>

        <!-- Evaluation Expired Template -->
        <template id="evaluation_expired_template" name="Evaluation Link Expired">
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta name="viewport" content="width=device-width, initial-scale=1"/>
                    <title>Evaluation Expired</title>
                    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"/>
                </head>
                <body>
                    <div class="container mt-5">
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h2>⏰ Evaluation Expired</h2>
                                        <p class="lead"><t t-esc="message"/></p>
                                        <p>This evaluation link has expired and is no longer valid. Please contact the administrator for a new evaluation link if needed.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
        </template>

    </data>
</odoo>
