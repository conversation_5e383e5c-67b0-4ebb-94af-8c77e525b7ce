<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Menu -->
        <menuitem id="menu_driver_evaluation_root"
                  name="Driver Evaluation"
                  sequence="50"
                  groups="base.group_user"/>

        <!-- Configuration Menu -->
        <menuitem id="menu_driver_evaluation_config"
                  name="Configuration"
                  parent="menu_driver_evaluation_root"
                  sequence="10"
                  groups="base.group_system"/>

        <menuitem id="menu_evaluation_config"
                  name="Evaluation Settings"
                  parent="menu_driver_evaluation_config"
                  action="action_evaluation_config"
                  sequence="10"
                  groups="base.group_system"/>

        <!-- Management Menu -->
        <menuitem id="menu_driver_evaluation_management"
                  name="Management"
                  parent="menu_driver_evaluation_root"
                  sequence="20"
                  groups="base.group_user"/>

        <menuitem id="menu_evaluation_links"
                  name="Evaluation Links"
                  parent="menu_driver_evaluation_management"
                  action="action_evaluation_link"
                  sequence="10"
                  groups="base.group_user"/>

        <menuitem id="menu_evaluation_results"
                  name="Evaluation Results"
                  parent="menu_driver_evaluation_management"
                  action="action_evaluation_result"
                  sequence="20"
                  groups="base.group_user"/>

        <menuitem id="menu_generate_evaluation_links"
                  name="Generate Links"
                  parent="menu_driver_evaluation_management"
                  action="action_evaluation_link_wizard"
                  sequence="30"
                  groups="base.group_system"/>

        <!-- Reports Menu -->
        <menuitem id="menu_driver_evaluation_reports"
                  name="Reports"
                  parent="menu_driver_evaluation_root"
                  sequence="30"
                  groups="base.group_user"/>

    </data>
</odoo>
