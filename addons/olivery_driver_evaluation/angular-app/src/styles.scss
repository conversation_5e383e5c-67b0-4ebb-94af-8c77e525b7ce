/* Global Styles */
@import '~@ionic/angular/css/core.css';
@import '~@ionic/angular/css/normalize.css';
@import '~@ionic/angular/css/structure.css';
@import '~@ionic/angular/css/typography.css';
@import '~@ionic/angular/css/display.css';
@import '~@ionic/angular/css/padding.css';
@import '~@ionic/angular/css/float-elements.css';
@import '~@ionic/angular/css/text-alignment.css';
@import '~@ionic/angular/css/text-transformation.css';
@import '~@ionic/angular/css/flex-utils.css';

/* Driver Evaluation Styles */
.evaluation-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.evaluation-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  p {
    margin: 5px 0;
    opacity: 0.9;
  }
}

/* Progress Bar */
.progress-container {
  width: 100%;
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  margin-bottom: 30px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Evaluation Steps */
.evaluation-step {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Rating System */
.category-rating {
  margin-bottom: 30px;
  text-align: center;

  h3 {
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
  }
}

.rating-container {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 15px 0;
}

.rating-star {
  color: #ddd;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #ffc107;
    transform: scale(1.1);
  }

  &.selected {
    color: #ffc107;
  }
}

/* Questions */
.detailed-questions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
  }
}

.question-item {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;

  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-weight: 500;
    font-size: 1.1rem;
  }
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 30px;
  padding: 20px 0;

  ion-button {
    flex: 1;
    height: 50px;
    font-weight: 600;
  }
}

/* Loading States */
.loading-container {
  text-align: center;
  padding: 60px 20px;

  ion-spinner {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

/* Success/Error States */
.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .evaluation-container {
    padding: 10px;
  }
  
  .evaluation-header {
    padding: 15px;
    margin-bottom: 20px;

    h1 {
      font-size: 2rem;
    }
  }
  
  .step-actions {
    flex-direction: column;

    ion-button {
      margin-bottom: 10px;
    }
  }
  
  .rating-container {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .rating-star {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .evaluation-header h1 {
    font-size: 1.5rem;
  }
  
  .question-item {
    padding: 10px;
  }
}
