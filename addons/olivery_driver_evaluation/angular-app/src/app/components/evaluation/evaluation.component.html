<div class="evaluation-container" dir="rtl">
  <!-- Header -->
  <div class="evaluation-header" *ngIf="evaluationData">
    <h1>{{ 'DRIVER_EVALUATION' | translate }}</h1>
    <p>{{ 'EVALUATING' | translate }}: <strong>{{ evaluationData.driverName }}</strong></p>
    <p>{{ 'EXPIRES' | translate }}: {{ evaluationData.expiryDate | date:'medium' }}</p>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'LOADING_EVALUATION_FORM' | translate }}</p>
  </div>

  <!-- Error State -->
  <div class="alert-danger" *ngIf="error">
    <h4>{{ 'ERROR' | translate }}</h4>
    <p>{{ error }}</p>
  </div>

  <!-- Single Page Evaluation Form -->
  <div *ngIf="!isLoading && !error && config" class="evaluation-form">

    <!-- Evaluation Categories -->
    <div class="categories-section">
      <div *ngFor="let category of config.criteria.categories" class="category-card">
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ category.name }}</ion-card-title>
            <ion-card-subtitle>{{ category.description }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="category-rating">
              <h3>{{ 'RATING.OVERALL_RATING' | translate }} {{ category.name }}</h3>
              <div class="rating-container">
                <span
                  *ngFor="let star of getStarArray(category.max_score)"
                  class="rating-star"
                  [class.selected]="getRating(category.id) >= star"
                  (click)="setRating(category.id, star)"
                  [attr.aria-label]="'Rate ' + star + ' stars'">
                  ★
                </span>
              </div>
              <p *ngIf="getRating(category.id) > 0" class="rating-display">
                {{ 'RATING.RATING_LABEL' | translate: {rating: getRating(category.id), max: category.max_score} }}
              </p>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- Feedback Section -->
    <div class="feedback-section">
      <ion-card>
        <ion-card-header>
          <ion-card-title>{{ 'FEEDBACK.ADDITIONAL_FEEDBACK' | translate }}</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label position="stacked">{{ 'FEEDBACK.GENERAL_FEEDBACK' | translate }}</ion-label>
            <ion-textarea
              [(ngModel)]="feedback"
              [placeholder]="'FEEDBACK.FEEDBACK_PLACEHOLDER' | translate"
              rows="4">
            </ion-textarea>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Submit Section -->
    <div class="submit-section">
      <ion-card>
        <ion-card-header>
          <ion-card-title>{{ 'REVIEW.REVIEW_EVALUATION' | translate }}</ion-card-title>
          <ion-card-subtitle>{{ 'REVIEW.REVIEW_SUBTITLE' | translate }}</ion-card-subtitle>
        </ion-card-header>
        <ion-card-content>
          <div class="review-summary">
            <h4>{{ 'REVIEW.DRIVER' | translate }}: {{ evaluationData?.driverName }}</h4>
            <h4>{{ 'REVIEW.YOUR_RATINGS' | translate }}:</h4>
            <div class="ratings-summary">
              <div *ngFor="let score of scores | keyvalue" class="rating-item">
                <span class="category-name">{{ getCategoryName(score.key) }}</span>
                <span class="score-value">{{ score.value }}/10</span>
              </div>
            </div>

            <div *ngIf="feedback" class="feedback-summary">
              <h4>{{ 'REVIEW.ADDITIONAL_FEEDBACK' | translate }}:</h4>
              <p>{{ feedback }}</p>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

      <div class="submit-actions">
        <ion-button
          expand="block"
          size="large"
          color="success"
          (click)="submitEvaluation()"
          [disabled]="isSubmitting || !hasValidRatings()">
          <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
          {{ isSubmitting ? ('BUTTONS.SUBMITTING' | translate) : ('BUTTONS.SUBMIT_EVALUATION' | translate) }}
        </ion-button>
      </div>
    </div>

  </div>
</div>
