import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil, firstValueFrom } from 'rxjs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { EvaluationApiService } from '../../services/evaluation-api.service';
import { IframeCommunicationService } from '../../services/iframe-communication.service';
import { EvaluationDataService } from '../../services/evaluation-data.service';
import {
  EvaluationData,
  EvaluationConfig,
  EvaluationScores,
  EvaluationSubmission,
  EvaluationCategory
} from '../../models/evaluation.models';

@Component({
  selector: 'app-evaluation',
  standalone: true,
  imports: [CommonModule, FormsModule, IonicModule, TranslateModule],
  templateUrl: './evaluation.component.html',
  styleUrls: ['./evaluation.component.scss']
})
export class EvaluationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // State management
  isLoading = true;
  error: string | null = null;

  // Data
  evaluationData: EvaluationData | null = null;
  config: EvaluationConfig | null = null;
  scores: EvaluationScores = {};
  feedback = '';

  // UI state
  isSubmitting = false;

  constructor(
    private apiService: EvaluationApiService,
    private iframeService: IframeCommunicationService,
    private dataService: EvaluationDataService,
    private translate: TranslateService
  ) {
    // Set Arabic as default language
    this.translate.setDefaultLang('ar');
    this.translate.use('ar');
  }

  ngOnInit(): void {
    this.iframeService.evaluationData$
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data) {
          this.evaluationData = data;
          this.initializeEvaluation();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async initializeEvaluation(): Promise<void> {
    if (!this.evaluationData) return;

    try {
      this.isLoading = true;
      this.error = null;

      // Validate token
      const validation = await firstValueFrom(this.apiService.validateToken(this.evaluationData.token));
      if (!validation?.success) {
        const errorMessage = validation?.error || this.translate.instant('MESSAGES.INVALID_OR_EXPIRED_LINK');
        this.error = errorMessage;
        this.iframeService.notifyEvaluationError({
          error: errorMessage,
          isArabic: true
        });
        return;
      }

      // Update evaluation data with validated information
      if (validation.data) {
        this.evaluationData = {
          ...this.evaluationData,
          driverName: validation.data.driver_name,
          driverId: validation.data.driver_id,
          linkId: validation.data.link_id,
          expiryDate: validation.data.expiry_date
        };
      }

      // Get configuration - try API first, then fallback to static data
      try {
        console.log('🔄 Requesting config from API with token:', this.evaluationData.token);
        const configResponse = await firstValueFrom(this.apiService.getConfig(this.evaluationData.token));
        console.log('📡 API Response received:', configResponse);

        if (configResponse?.success && configResponse.data?.criteria?.categories && configResponse.data.criteria.categories.length > 0) {
          this.config = configResponse.data!;
          console.log('✅ Using dynamic configuration from API:');
          console.log('📋 Categories:', this.config.criteria.categories);
          console.log('🔢 Number of categories:', this.config.criteria.categories.length);
        } else {
          // Fallback to static configuration only if API fails completely
          console.log('⚠️ API config failed or returned empty categories');
          console.log('📊 Response success:', configResponse?.success);
          console.log('📊 Response data:', configResponse?.data);
          console.log('📊 Categories:', configResponse?.data?.criteria?.categories);
          this.config = await firstValueFrom(this.dataService.getEvaluationConfig());
          console.log('📁 Using static configuration:', this.config.criteria.categories);
        }
      } catch (apiError) {
        console.log('❌ API config error, using static configuration...', apiError);
        this.config = await firstValueFrom(this.dataService.getEvaluationConfig());
        console.log('📁 Using static configuration due to error:', this.config.criteria.categories);
      }
      this.isLoading = false;

    } catch (error) {
      console.error('Initialization error:', error);
      const errorMessage = this.translate.instant('MESSAGES.FAILED_TO_LOAD');
      this.error = errorMessage;
      this.iframeService.notifyEvaluationError({
        error: errorMessage,
        isArabic: true
      });
    }
  }



  setRating(categoryId: string, score: number): void {
    this.scores[categoryId] = score;
  }

  getRating(categoryId: string): number {
    return this.scores[categoryId] || 0;
  }



  async submitEvaluation(): Promise<void> {
    if (this.isSubmitting || !this.evaluationData) return;

    try {
      this.isSubmitting = true;

      const submission: EvaluationSubmission = {
        scores: this.scores,
        evaluator: {
          name: 'Anonymous',
          email: '<EMAIL>',
          phone: ''
        },
        feedback: this.feedback
      };

      const response = await firstValueFrom(this.apiService.submitEvaluation(
        this.evaluationData.token,
        submission
      ));

      if (response?.success) {
        // Send translated success message
        const translatedMessage = this.translate.instant('MESSAGES.EVALUATION_COMPLETED_SUCCESSFULLY');
        const translatedThankYou = this.translate.instant('MESSAGES.THANK_YOU_FEEDBACK');
        const translatedOverallScore = this.translate.instant('MESSAGES.OVERALL_SCORE');
        const translatedLinkInactive = this.translate.instant('MESSAGES.LINK_NOW_INACTIVE');

        this.iframeService.notifyEvaluationComplete({
          success: true,
          message: translatedMessage,
          thankYou: translatedThankYou,
          overallScore: response.data?.overall_score || 0,
          overallScoreLabel: translatedOverallScore,
          linkInactiveMessage: translatedLinkInactive,
          isArabic: true
        });
      } else {
        const errorMessage = response?.error || this.translate.instant('MESSAGES.SUBMISSION_ERROR');
        this.error = errorMessage;
        this.iframeService.notifyEvaluationError(errorMessage);
      }

    } catch (error) {
      console.error('Submission error:', error);
      const errorMessage = this.translate.instant('MESSAGES.SUBMISSION_ERROR');
      this.error = errorMessage;
      this.iframeService.notifyEvaluationError({
        error: errorMessage,
        isArabic: true
      });
    } finally {
      this.isSubmitting = false;
    }
  }

  getStarArray(maxScore: number): number[] {
    return Array.from({ length: maxScore }, (_, i) => i + 1);
  }

  getCategoryName(categoryId: string): string {
    if (!this.config) return categoryId;
    const category = this.config.criteria.categories.find(c => c.id === categoryId);

    // For dynamic questions, use the actual name from config
    if (category && category.name) {
      return category.name;
    }

    // Fallback to translation for legacy categories
    return this.translate.instant('CATEGORIES.' + categoryId.toUpperCase()) || categoryId;
  }

  hasValidRatings(): boolean {
    if (!this.config) return false;

    // Check if all categories have been rated
    for (const category of this.config.criteria.categories) {
      if (!this.scores[category.id] || this.scores[category.id] === 0) {
        return false;
      }
    }
    return true;
  }
}
