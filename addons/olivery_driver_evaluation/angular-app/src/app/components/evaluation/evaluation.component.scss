// Evaluation Component Styles - Arabic RTL Support

.evaluation-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  direction: rtl;
  text-align: right;

  &[dir="rtl"] {
    * {
      text-align: right;
    }
  }
}

.evaluation-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  p {
    margin: 5px 0;
    opacity: 0.9;
  }
}

// Single Page Layout
.evaluation-form {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Categories Section
.categories-section {
  margin-bottom: 30px;
}

.category-card {
  margin-bottom: 20px;
}

// Feedback Section
.feedback-section {
  margin-bottom: 30px;
}

// Submit Section
.submit-section {
  margin-bottom: 20px;
}

.submit-actions {
  margin-top: 20px;
  padding: 20px 0;
}

// Rating System
.category-rating {
  margin-bottom: 30px;
  text-align: center;

  h3 {
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
  }
}

.rating-container {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 15px 0;
}

.rating-star {
  color: #ddd;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #ffc107;
    transform: scale(1.1);
  }

  &.selected {
    color: #ffc107;
  }
}

// Questions
.detailed-questions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
  }
}

.question-item {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;

  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-weight: 500;
    font-size: 1.1rem;
  }
}

// Step Actions
.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 30px;
  padding: 20px 0;

  ion-button {
    flex: 1;
    height: 50px;
    font-weight: 600;
  }
}

// Loading States
.loading-container {
  text-align: center;
  padding: 60px 20px;

  ion-spinner {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

// Success/Error States
.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 10px 0;
    font-weight: 600;
  }

  p {
    margin: 0;
  }
}

// Review Section
.review-summary {
  h4 {
    color: #333;
    font-weight: 600;
    margin: 20px 0 15px 0;
  }
}

.ratings-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;

  &:last-child {
    border-bottom: none;
  }

  .category-name {
    font-weight: 500;
    color: #333;
  }

  .score-value {
    font-weight: 600;
    color: #667eea;
    font-size: 1.1rem;
  }
}

.feedback-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;

  p {
    margin: 10px 0 0 0;
    color: #555;
    line-height: 1.5;
  }
}

.rating-display {
  margin-top: 10px;
  font-weight: 500;
  color: #667eea;
}

// Responsive Design
@media (max-width: 768px) {
  .evaluation-container {
    padding: 10px;
  }
  
  .evaluation-header {
    padding: 15px;
    margin-bottom: 20px;

    h1 {
      font-size: 2rem;
    }
  }
  
  .step-actions {
    flex-direction: column;

    ion-button {
      margin-bottom: 10px;
    }
  }
  
  .rating-container {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .rating-star {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .evaluation-header h1 {
    font-size: 1.5rem;
  }
  
  .question-item {
    padding: 10px;
  }

  .category-rating h3 {
    font-size: 1.1rem;
  }

  .rating-star {
    font-size: 1.3rem;
  }
}

// Card customizations
ion-card {
  margin: 0 0 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

ion-card-header {
  padding-bottom: 10px;
}

ion-card-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
}

ion-card-subtitle {
  color: #666;
  font-size: 0.95rem;
}

// Form elements
ion-item {
  --border-color: #e0e0e0;
  --background: transparent;
  margin-bottom: 15px;
}

ion-label {
  font-weight: 500;
  color: #333;
}

ion-input, ion-textarea {
  --color: #333;
  --placeholder-color: #999;
}

// Button customizations
ion-button {
  --border-radius: 8px;
  font-weight: 600;
  text-transform: none;
}

ion-button[fill="outline"] {
  --color: #667eea;
  --border-color: #667eea;
}

ion-button[color="success"] {
  --background: #28a745;
  --background-activated: #218838;
}

// List customizations
ion-list {
  background: transparent;
  padding: 0;
}

ion-list ion-item {
  --background: #f8f9fa;
  --border-color: #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
}
