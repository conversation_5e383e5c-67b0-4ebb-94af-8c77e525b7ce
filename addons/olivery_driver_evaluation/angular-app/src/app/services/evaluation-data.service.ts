import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { EvaluationConfig } from '../models/evaluation.models';

@Injectable({
  providedIn: 'root'
})
export class EvaluationDataService {

  constructor(private http: HttpClient) {}

  /**
   * Load evaluation configuration from static JSON file
   * This serves as a fallback when API is not available
   */
  loadStaticConfig(): Observable<EvaluationConfig> {
    return this.http.get<EvaluationConfig>('./assets/evaluation-config.json').pipe(
      catchError(error => {
        console.error('Failed to load static evaluation config:', error);
        return of(this.getDefaultConfig());
      })
    );
  }

  /**
   * Load evaluation data from XML file (if needed for parsing)
   */
  loadEvaluationDataXml(): Observable<string> {
    return this.http.get('./assets/evaluation_data.xml', { responseType: 'text' }).pipe(
      catchError(error => {
        console.error('Failed to load evaluation data XML:', error);
        return of('');
      })
    );
  }

  /**
   * Parse XML evaluation data and convert to JSON format
   */
  parseEvaluationXml(xmlContent: string): Observable<EvaluationConfig | null> {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
      
      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror');
      if (parserError) {
        console.error('XML parsing error:', parserError.textContent);
        return of(null);
      }

      // Extract evaluation criteria from XML
      const criteriaField = xmlDoc.querySelector('field[name="evaluation_criteria"]');
      if (!criteriaField) {
        console.error('No evaluation criteria found in XML');
        return of(null);
      }

      const criteriaJson = criteriaField.textContent?.trim();
      if (!criteriaJson) {
        console.error('Empty evaluation criteria in XML');
        return of(null);
      }

      // Parse the JSON content
      const criteria = JSON.parse(criteriaJson);
      
      // Extract other fields
      const durationField = xmlDoc.querySelector('field[name="evaluation_duration"]');
      const attemptsField = xmlDoc.querySelector('field[name="max_attempts"]');

      const config: EvaluationConfig = {
        evaluation_duration: durationField ? parseInt(durationField.textContent || '7') : 7,
        max_attempts: attemptsField ? parseInt(attemptsField.textContent || '1') : 1,
        criteria: criteria
      };

      return of(config);

    } catch (error) {
      console.error('Error parsing evaluation XML:', error);
      return of(null);
    }
  }

  /**
   * Get evaluation configuration with fallback strategy:
   * 1. Try to load from static JSON
   * 2. If that fails, use default config
   */
  getEvaluationConfig(): Observable<EvaluationConfig> {
    return this.loadStaticConfig().pipe(
      catchError(() => {
        console.log('Static JSON config failed, using default config...');
        return of(this.getDefaultConfig());
      })
    );
  }

  /**
   * Minimal default configuration as emergency fallback only
   * This should only be used if both API and static file fail
   */
  private getDefaultConfig(): EvaluationConfig {
    return {
      evaluation_duration: 7,
      max_attempts: 1,
      criteria: {
        categories: [
          {
            id: 'question_1',
            name: 'General Evaluation',
            description: 'Please rate the overall performance',
            max_score: 10,
            questions: [
              {
                id: 'general_rating',
                text: 'How would you rate the overall performance?',
                type: 'rating',
                scale: 10
              }
            ]
          }
        ]
      }
    };
  }
}
