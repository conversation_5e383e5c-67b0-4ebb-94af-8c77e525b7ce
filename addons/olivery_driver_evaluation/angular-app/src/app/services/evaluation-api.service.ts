import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ApiResponse,
  ValidationResponse,
  EvaluationConfig,
  EvaluationSubmission,
  SubmissionResponse
} from '../models/evaluation.models';

// JSON-RPC response wrapper
interface JsonRpcResponse<T = any> {
  jsonrpc: string;
  id: any;
  result?: T;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

@Injectable({
  providedIn: 'root'
})
export class EvaluationApiService {
  private baseUrl = '/api/evaluation';
  
  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) {}

  validateToken(token: string): Observable<ApiResponse<ValidationResponse>> {
    return this.http.post<JsonRpcResponse<ApiResponse<ValidationResponse>>>(
      `${this.baseUrl}/validate`,
      { 'params': {'token': token} },
      this.httpOptions
    ).pipe(
      map(response => {
        if (response.error) {
          return {
            success: false,
            error: response.error.message,
            code: response.error.code?.toString()
          };
        }
        return response.result || { success: false, error: 'No result in response' };
      })
    );
  }

  getConfig(token: string): Observable<ApiResponse<EvaluationConfig>> {
    return this.http.post<JsonRpcResponse<ApiResponse<EvaluationConfig>>>(
      `${this.baseUrl}/config`,
      { 'params': {'token': token} },
      this.httpOptions
    ).pipe(
      map(response => {
        if (response.error) {
          return {
            success: false,
            error: response.error.message,
            code: response.error.code?.toString()
          };
        }
        return response.result || { success: false, error: 'No result in response' };
      })
    );
  }

  submitEvaluation(token: string, evaluationData: EvaluationSubmission): Observable<ApiResponse<SubmissionResponse>> {
    return this.http.post<JsonRpcResponse<ApiResponse<SubmissionResponse>>>(
      `${this.baseUrl}/submit`,
      {
        params: {'token': token, evaluation_data: evaluationData}
      },
      this.httpOptions
    ).pipe(
      map(response => {
        if (response.error) {
          return {
            success: false,
            error: response.error.message,
            code: response.error.code?.toString()
          };
        }
        return response.result || { success: false, error: 'No result in response' };
      })
    );
  }
}
