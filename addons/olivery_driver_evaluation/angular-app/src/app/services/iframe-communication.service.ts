import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { EvaluationData } from '../models/evaluation.models';

@Injectable({
  providedIn: 'root'
})
export class IframeCommunicationService {
  private evaluationDataSubject = new BehaviorSubject<EvaluationData | null>(null);
  public evaluationData$: Observable<EvaluationData | null> = this.evaluationDataSubject.asObservable();

  constructor() {
    this.initializeMessageListener();
    this.requestEvaluationData();
  }

  private initializeMessageListener(): void {
    window.addEventListener('message', (event) => {
      // Verify origin for security (adjust as needed)
      if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {
        return;
      }

      if (event.data && event.data.type === 'EVALUATION_DATA') {
        this.evaluationDataSubject.next(event.data.payload);
      }
    });
  }

  private requestEvaluationData(): void {
    // Try to get data from URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const driverName = urlParams.get('driverName');
    const driverId = urlParams.get('driverId');
    const linkId = urlParams.get('linkId');
    const expiryDate = urlParams.get('expiryDate');

    if (token && driverName && driverId && linkId && expiryDate) {
      const evaluationData: EvaluationData = {
        token,
        driverName,
        driverId: parseInt(driverId),
        linkId: parseInt(linkId),
        expiryDate
      };
      this.evaluationDataSubject.next(evaluationData);
    } else {
      // Request data from parent window
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'REQUEST_EVALUATION_DATA'
        }, '*');
      }
    }
  }

  sendMessage(type: string, payload: any): void {
    if (window.parent !== window) {
      window.parent.postMessage({
        type,
        payload
      }, '*');
    }
  }

  notifyEvaluationComplete(result: any): void {
    this.sendMessage('EVALUATION_COMPLETE', result);
  }

  notifyEvaluationError(error: string | { error: string; isArabic?: boolean }): void {
    const errorPayload = typeof error === 'string' ? { error } : error;
    this.sendMessage('EVALUATION_ERROR', errorPayload);
  }
}
