# Driver Evaluation Angular Application

This is an Angular 18 + TypeScript application for the driver evaluation system.

## Development Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Development Server**
   ```bash
   npm start
   # or
   ng serve
   ```
   Navigate to `http://localhost:4200/`

3. **Build for Production**
   ```bash
   npm run build:prod
   ```
   This builds the app and outputs to `../static/src/angular-dist/`

## Integration with Odoo

The application is designed to run inside an iframe within the Odoo evaluation template. It communicates with the parent window via postMessage API.

### Data Flow

1. **Initialization**: App requests evaluation data from parent window
2. **Token Validation**: Validates the evaluation token via Odoo API
3. **Configuration**: Loads evaluation criteria from Odoo
4. **Submission**: Submits evaluation results to Odoo API
5. **Completion**: Notifies parent window of completion/errors

### API Endpoints

- `POST /api/evaluation/validate` - Token validation
- `POST /api/evaluation/config` - Get evaluation configuration  
- `POST /api/evaluation/submit` - Submit evaluation results

### URL Parameters

The app can receive data via URL parameters:
- `token` - Evaluation token
- `driverName` - Driver name
- `driverId` - Driver ID
- `linkId` - Evaluation link ID
- `expiryDate` - Link expiry date

Example: `http://localhost:4200/?token=abc123&driverName=John&driverId=1&linkId=1&expiryDate=2025-01-01`

## Architecture

- **Standalone Components**: Uses Angular 18 standalone components
- **Services**: API service for Odoo communication, iframe service for parent communication
- **Models**: TypeScript interfaces for type safety
- **Ionic UI**: Uses Ionic components for mobile-responsive interface

## Build Output

The production build outputs to `../static/src/angular-dist/` which is served by the Odoo static file handler.
