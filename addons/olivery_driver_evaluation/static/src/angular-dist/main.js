(self["webpackChunkdriver_evaluation_app"] = self["webpackChunkdriver_evaluation_app"] || []).push([["main"],{

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);




class AppComponent {
  constructor() {
    this.title = 'driver-evaluation-app';
  }
  static {
    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || AppComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 0,
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ion-app")(1, "ion-content");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](2, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_1__.RouterOutlet, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonicModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonApp, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonContent],
      encapsulation: 2
    });
  }
}

/***/ }),

/***/ 2181:
/*!*******************************!*\
  !*** ./src/app/app.routes.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   routes: () => (/* binding */ routes)
/* harmony export */ });
/* harmony import */ var _components_evaluation_evaluation_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/evaluation/evaluation.component */ 2380);

const routes = [{
  path: '',
  component: _components_evaluation_evaluation_component__WEBPACK_IMPORTED_MODULE_0__.EvaluationComponent
}, {
  path: '**',
  redirectTo: ''
}];

/***/ }),

/***/ 2380:
/*!***************************************************************!*\
  !*** ./src/app/components/evaluation/evaluation.component.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationComponent: () => (/* binding */ EvaluationComponent)
/* harmony export */ });
/* harmony import */ var _Users_macbook_Desktop_olivery_web_delivery3_delivery_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 3900);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 6196);
/* harmony import */ var _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngx-translate/core */ 852);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_evaluation_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/evaluation-api.service */ 1877);
/* harmony import */ var _services_iframe_communication_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/iframe-communication.service */ 2519);
/* harmony import */ var _services_evaluation_data_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/evaluation-data.service */ 1856);














const _c0 = (a0, a1) => ({
  rating: a0,
  max: a1
});
function EvaluationComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 5)(1, "h1");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](3, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](6, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](11, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](3, 5, "DRIVER_EVALUATION"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](6, 7, "EVALUATING"), ": ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.evaluationData.driverName);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate2"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](11, 9, "EXPIRES"), ": ", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind2"](12, 11, ctx_r0.evaluationData.expiryDate, "medium"), "");
  }
}
function EvaluationComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "ion-spinner", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](4, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](4, 1, "LOADING_EVALUATION_FORM"));
  }
}
function EvaluationComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 8)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](3, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](3, 2, "ERROR"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.error);
  }
}
function EvaluationComponent_div_4_div_2_span_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "span", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EvaluationComponent_div_4_div_2_span_13_Template_span_click_0_listener() {
      const star_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r3).$implicit;
      const category_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r0.setRating(category_r5.id, star_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " \u2605 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const star_r4 = ctx.$implicit;
    const category_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("selected", ctx_r0.getRating(category_r5.id) >= star_r4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵattribute"]("aria-label", "Rate " + star_r4 + " stars");
  }
}
function EvaluationComponent_div_4_div_2_p_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "p", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](2, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const category_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind2"](2, 1, "RATING.RATING_LABEL", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](4, _c0, ctx_r0.getRating(category_r5.id), category_r5.max_score)), " ");
  }
}
function EvaluationComponent_div_4_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 23)(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "ion-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "ion-card-content")(8, "div", 24)(9, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](11, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](13, EvaluationComponent_div_4_div_2_span_13_Template, 2, 3, "span", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](14, EvaluationComponent_div_4_div_2_p_14_Template, 3, 7, "p", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const category_r5 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](category_r5.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](category_r5.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate2"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](11, 6, "RATING.OVERALL_RATING"), " ", category_r5.name, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r0.getStarArray(category_r5.max_score));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r0.getRating(category_r5.id) > 0);
  }
}
function EvaluationComponent_div_4_div_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 30)(1, "span", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "span", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const score_r6 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.getCategoryName(score_r6.key));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("", score_r6.value, "/10");
  }
}
function EvaluationComponent_div_4_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 33)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](3, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](3, 2, "REVIEW.ADDITIONAL_FEEDBACK"), ":");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.feedback);
  }
}
function EvaluationComponent_div_4_ion_spinner_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "ion-spinner", 7);
  }
}
function EvaluationComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 9)(1, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](2, EvaluationComponent_div_4_div_2_Template, 15, 8, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "div", 12)(4, "ion-card")(5, "ion-card-header")(6, "ion-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](8, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "ion-card-content")(10, "ion-item")(11, "ion-label", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](13, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "ion-textarea", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](15, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtwoWayListener"]("ngModelChange", function EvaluationComponent_div_4_Template_ion_textarea_ngModelChange_14_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r2);
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtwoWayBindingSet"](ctx_r0.feedback, $event) || (ctx_r0.feedback = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "div", 15)(17, "ion-card")(18, "ion-card-header")(19, "ion-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](21, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "ion-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](23);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](24, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "ion-card-content")(26, "div", 16)(27, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](28);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](29, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](30, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](31);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](32, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "div", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](34, EvaluationComponent_div_4_div_34_Template, 5, 2, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](35, "keyvalue");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](36, EvaluationComponent_div_4_div_36_Template, 6, 4, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](37, "div", 20)(38, "ion-button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EvaluationComponent_div_4_Template_ion_button_click_38_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r2);
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r0.submitEvaluation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](39, EvaluationComponent_div_4_ion_spinner_39_Template, 1, 0, "ion-spinner", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](40);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](41, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](42, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r0.config.criteria.categories);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](8, 15, "FEEDBACK.ADDITIONAL_FEEDBACK"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](13, 17, "FEEDBACK.GENERAL_FEEDBACK"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtwoWayProperty"]("ngModel", ctx_r0.feedback);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("placeholder", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](15, 19, "FEEDBACK.FEEDBACK_PLACEHOLDER"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](21, 21, "REVIEW.REVIEW_EVALUATION"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](24, 23, "REVIEW.REVIEW_SUBTITLE"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate2"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](29, 25, "REVIEW.DRIVER"), ": ", ctx_r0.evaluationData == null ? null : ctx_r0.evaluationData.driverName, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](32, 27, "REVIEW.YOUR_RATINGS"), ":");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](35, 29, ctx_r0.scores));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r0.feedback);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", ctx_r0.isSubmitting || !ctx_r0.hasValidRatings());
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r0.isSubmitting);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r0.isSubmitting ? _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](41, 31, "BUTTONS.SUBMITTING") : _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](42, 33, "BUTTONS.SUBMIT_EVALUATION"), " ");
  }
}
class EvaluationComponent {
  constructor(apiService, iframeService, dataService, translate) {
    this.apiService = apiService;
    this.iframeService = iframeService;
    this.dataService = dataService;
    this.translate = translate;
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_5__.Subject();
    // State management
    this.isLoading = true;
    this.error = null;
    // Data
    this.evaluationData = null;
    this.config = null;
    this.scores = {};
    this.feedback = '';
    // UI state
    this.isSubmitting = false;
    // Set Arabic as default language
    this.translate.setDefaultLang('ar');
    this.translate.use('ar');
  }
  ngOnInit() {
    this.iframeService.evaluationData$.pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_6__.takeUntil)(this.destroy$)).subscribe(data => {
      if (data) {
        this.evaluationData = data;
        this.initializeEvaluation();
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  initializeEvaluation() {
    var _this = this;
    return (0,_Users_macbook_Desktop_olivery_web_delivery3_delivery_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this.evaluationData) return;
      try {
        _this.isLoading = true;
        _this.error = null;
        // Validate token
        const validation = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.firstValueFrom)(_this.apiService.validateToken(_this.evaluationData.token));
        if (!validation?.success) {
          const errorMessage = validation?.error || _this.translate.instant('MESSAGES.INVALID_OR_EXPIRED_LINK');
          _this.error = errorMessage;
          _this.iframeService.notifyEvaluationError({
            error: errorMessage,
            isArabic: true
          });
          return;
        }
        // Update evaluation data with validated information
        if (validation.data) {
          _this.evaluationData = {
            ..._this.evaluationData,
            driverName: validation.data.driver_name,
            driverId: validation.data.driver_id,
            linkId: validation.data.link_id,
            expiryDate: validation.data.expiry_date
          };
        }
        // Get configuration - try API first, then fallback to static data
        try {
          console.log('🔄 Requesting config from API with token:', _this.evaluationData.token);
          const configResponse = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.firstValueFrom)(_this.apiService.getConfig(_this.evaluationData.token));
          console.log('📡 API Response received:', configResponse);
          if (configResponse?.success && configResponse.data?.criteria?.categories && configResponse.data.criteria.categories.length > 0) {
            _this.config = configResponse.data;
            console.log('✅ Using dynamic configuration from API:');
            console.log('📋 Categories:', _this.config.criteria.categories);
            console.log('🔢 Number of categories:', _this.config.criteria.categories.length);
          } else {
            // Fallback to static configuration only if API fails completely
            console.log('⚠️ API config failed or returned empty categories');
            console.log('📊 Response success:', configResponse?.success);
            console.log('📊 Response data:', configResponse?.data);
            console.log('📊 Categories:', configResponse?.data?.criteria?.categories);
            _this.config = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.firstValueFrom)(_this.dataService.getEvaluationConfig());
            console.log('📁 Using static configuration:', _this.config.criteria.categories);
          }
        } catch (apiError) {
          console.log('❌ API config error, using static configuration...', apiError);
          _this.config = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.firstValueFrom)(_this.dataService.getEvaluationConfig());
          console.log('📁 Using static configuration due to error:', _this.config.criteria.categories);
        }
        _this.isLoading = false;
      } catch (error) {
        console.error('Initialization error:', error);
        const errorMessage = _this.translate.instant('MESSAGES.FAILED_TO_LOAD');
        _this.error = errorMessage;
        _this.iframeService.notifyEvaluationError({
          error: errorMessage,
          isArabic: true
        });
      }
    })();
  }
  setRating(categoryId, score) {
    this.scores[categoryId] = score;
  }
  getRating(categoryId) {
    return this.scores[categoryId] || 0;
  }
  submitEvaluation() {
    var _this2 = this;
    return (0,_Users_macbook_Desktop_olivery_web_delivery3_delivery_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this2.isSubmitting || !_this2.evaluationData) return;
      try {
        _this2.isSubmitting = true;
        const submission = {
          scores: _this2.scores,
          evaluator: {
            name: 'Anonymous',
            email: '<EMAIL>',
            phone: ''
          },
          feedback: _this2.feedback
        };
        const response = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.firstValueFrom)(_this2.apiService.submitEvaluation(_this2.evaluationData.token, submission));
        if (response?.success) {
          // Send translated success message
          const translatedMessage = _this2.translate.instant('MESSAGES.EVALUATION_COMPLETED_SUCCESSFULLY');
          const translatedThankYou = _this2.translate.instant('MESSAGES.THANK_YOU_FEEDBACK');
          const translatedOverallScore = _this2.translate.instant('MESSAGES.OVERALL_SCORE');
          const translatedLinkInactive = _this2.translate.instant('MESSAGES.LINK_NOW_INACTIVE');
          _this2.iframeService.notifyEvaluationComplete({
            success: true,
            message: translatedMessage,
            thankYou: translatedThankYou,
            overallScore: response.data?.overall_score || 0,
            overallScoreLabel: translatedOverallScore,
            linkInactiveMessage: translatedLinkInactive,
            isArabic: true
          });
        } else {
          const errorMessage = response?.error || _this2.translate.instant('MESSAGES.SUBMISSION_ERROR');
          _this2.error = errorMessage;
          _this2.iframeService.notifyEvaluationError(errorMessage);
        }
      } catch (error) {
        console.error('Submission error:', error);
        const errorMessage = _this2.translate.instant('MESSAGES.SUBMISSION_ERROR');
        _this2.error = errorMessage;
        _this2.iframeService.notifyEvaluationError({
          error: errorMessage,
          isArabic: true
        });
      } finally {
        _this2.isSubmitting = false;
      }
    })();
  }
  getStarArray(maxScore) {
    return Array.from({
      length: maxScore
    }, (_, i) => i + 1);
  }
  getCategoryName(categoryId) {
    if (!this.config) return categoryId;
    const category = this.config.criteria.categories.find(c => c.id === categoryId);
    // For dynamic questions, use the actual name from config
    if (category && category.name) {
      return category.name;
    }
    // Fallback to translation for legacy categories
    return this.translate.instant('CATEGORIES.' + categoryId.toUpperCase()) || categoryId;
  }
  hasValidRatings() {
    if (!this.config) return false;
    // Check if all categories have been rated
    for (const category of this.config.criteria.categories) {
      if (!this.scores[category.id] || this.scores[category.id] === 0) {
        return false;
      }
    }
    return true;
  }
  static {
    this.ɵfac = function EvaluationComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_services_evaluation_api_service__WEBPACK_IMPORTED_MODULE_1__.EvaluationApiService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_services_iframe_communication_service__WEBPACK_IMPORTED_MODULE_2__.IframeCommunicationService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_services_evaluation_data_service__WEBPACK_IMPORTED_MODULE_3__.EvaluationDataService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: EvaluationComponent,
      selectors: [["app-evaluation"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵStandaloneFeature"]],
      decls: 5,
      vars: 4,
      consts: [["dir", "rtl", 1, "evaluation-container"], ["class", "evaluation-header", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "alert-danger", 4, "ngIf"], ["class", "evaluation-form", 4, "ngIf"], [1, "evaluation-header"], [1, "loading-container"], ["name", "crescent"], [1, "alert-danger"], [1, "evaluation-form"], [1, "categories-section"], ["class", "category-card", 4, "ngFor", "ngForOf"], [1, "feedback-section"], ["position", "stacked"], ["rows", "4", 3, "ngModelChange", "ngModel", "placeholder"], [1, "submit-section"], [1, "review-summary"], [1, "ratings-summary"], ["class", "rating-item", 4, "ngFor", "ngForOf"], ["class", "feedback-summary", 4, "ngIf"], [1, "submit-actions"], ["expand", "block", "size", "large", "color", "success", 3, "click", "disabled"], ["name", "crescent", 4, "ngIf"], [1, "category-card"], [1, "category-rating"], [1, "rating-container"], ["class", "rating-star", 3, "selected", "click", 4, "ngFor", "ngForOf"], ["class", "rating-display", 4, "ngIf"], [1, "rating-star", 3, "click"], [1, "rating-display"], [1, "rating-item"], [1, "category-name"], [1, "score-value"], [1, "feedback-summary"]],
      template: function EvaluationComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](1, EvaluationComponent_div_1_Template, 13, 14, "div", 1)(2, EvaluationComponent_div_2_Template, 5, 3, "div", 2)(3, EvaluationComponent_div_3_Template, 6, 4, "div", 3)(4, EvaluationComponent_div_4_Template, 43, 35, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.evaluationData);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.isLoading && !ctx.error && ctx.config);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_9__.DatePipe, _angular_common__WEBPACK_IMPORTED_MODULE_9__.KeyValuePipe, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgModel, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonicModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonCard, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonCardContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonCardHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonCardSubtitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonCardTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonLabel, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonSpinner, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonTextarea, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.TextValueAccessor, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateModule, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslatePipe],
      styles: [".evaluation-container[_ngcontent-%COMP%] {\n  max-width: 900px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: \"Segoe UI\", Tahoma, Arial, sans-serif;\n  direction: rtl;\n  text-align: right;\n}\n.evaluation-container[dir=rtl][_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\n  text-align: right;\n}\n\n.evaluation-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 10px;\n}\n.evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0 0 10px 0;\n  font-size: 2.5rem;\n  font-weight: 300;\n}\n.evaluation-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 5px 0;\n  opacity: 0.9;\n}\n\n.evaluation-form[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in;\n}\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.categories-section[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n\n.category-card[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.feedback-section[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n\n.submit-section[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.submit-actions[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  padding: 20px 0;\n}\n\n.category-rating[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n  text-align: center;\n}\n.category-rating[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: 15px;\n  color: #333;\n  font-weight: 600;\n}\n\n.rating-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  gap: 5px;\n  margin: 15px 0;\n}\n\n.rating-star[_ngcontent-%COMP%] {\n  color: #ddd;\n  font-size: 2rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.rating-star[_ngcontent-%COMP%]:hover {\n  color: #ffc107;\n  transform: scale(1.1);\n}\n.rating-star.selected[_ngcontent-%COMP%] {\n  color: #ffc107;\n}\n\n.detailed-questions[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e0e0e0;\n}\n.detailed-questions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20px;\n}\n\n.question-item[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n.question-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-weight: 500;\n  font-size: 1.1rem;\n}\n\n.step-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n  margin-top: 30px;\n  padding: 20px 0;\n}\n.step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  flex: 1;\n  height: 50px;\n  font-weight: 600;\n}\n\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 60px 20px;\n}\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  width: 50px;\n  height: 50px;\n  margin-bottom: 20px;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1.1rem;\n}\n\n.alert-success[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.alert-danger[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n.alert-danger[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 10px 0;\n  font-weight: 600;\n}\n.alert-danger[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n}\n\n.review-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  color: #333;\n  font-weight: 600;\n  margin: 20px 0 15px 0;\n}\n\n.ratings-summary[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.rating-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #e9ecef;\n}\n.rating-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.rating-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n.rating-item[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #667eea;\n  font-size: 1.1rem;\n}\n\n.feedback-summary[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 15px;\n}\n.feedback-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 10px 0 0 0;\n  color: #555;\n  line-height: 1.5;\n}\n\n.rating-display[_ngcontent-%COMP%] {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #667eea;\n}\n\n@media (max-width: 768px) {\n  .evaluation-container[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n  .evaluation-header[_ngcontent-%COMP%] {\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n  .evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n  .step-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n    margin-bottom: 10px;\n  }\n  .rating-container[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  .rating-star[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n}\n@media (max-width: 480px) {\n  .evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .question-item[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n  .category-rating[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n    font-size: 1.1rem;\n  }\n  .rating-star[_ngcontent-%COMP%] {\n    font-size: 1.3rem;\n  }\n}\nion-card[_ngcontent-%COMP%] {\n  margin: 0 0 20px 0;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\nion-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n\nion-card-title[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 600;\n  color: #333;\n}\n\nion-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.95rem;\n}\n\nion-item[_ngcontent-%COMP%] {\n  --border-color: #e0e0e0;\n  --background: transparent;\n  margin-bottom: 15px;\n}\n\nion-label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n\nion-input[_ngcontent-%COMP%], ion-textarea[_ngcontent-%COMP%] {\n  --color: #333;\n  --placeholder-color: #999;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --border-radius: 8px;\n  font-weight: 600;\n  text-transform: none;\n}\n\nion-button[fill=outline][_ngcontent-%COMP%] {\n  --color: #667eea;\n  --border-color: #667eea;\n}\n\nion-button[color=success][_ngcontent-%COMP%] {\n  --background: #28a745;\n  --background-activated: #218838;\n}\n\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n  padding: 0;\n}\n\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --background: #f8f9fa;\n  --border-color: #e9ecef;\n  border-radius: 6px;\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 1877:
/*!****************************************************!*\
  !*** ./src/app/services/evaluation-api.service.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationApiService: () => (/* binding */ EvaluationApiService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);




class EvaluationApiService {
  constructor(http) {
    this.http = http;
    this.baseUrl = '/api/evaluation';
    this.httpOptions = {
      headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__.HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
  }
  validateToken(token) {
    return this.http.post(`${this.baseUrl}/validate`, {
      'params': {
        'token': token
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  getConfig(token) {
    return this.http.post(`${this.baseUrl}/config`, {
      'params': {
        'token': token
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  submitEvaluation(token, evaluationData) {
    return this.http.post(`${this.baseUrl}/submit`, {
      params: {
        'token': token,
        evaluation_data: evaluationData
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  static {
    this.ɵfac = function EvaluationApiService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationApiService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_0__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: EvaluationApiService,
      factory: EvaluationApiService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 1856:
/*!*****************************************************!*\
  !*** ./src/app/services/evaluation-data.service.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationDataService: () => (/* binding */ EvaluationDataService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common/http */ 6443);




class EvaluationDataService {
  constructor(http) {
    this.http = http;
  }
  /**
   * Load evaluation configuration from static JSON file
   * This serves as a fallback when API is not available
   */
  loadStaticConfig() {
    return this.http.get('./assets/evaluation-config.json').pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(error => {
      console.error('Failed to load static evaluation config:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.getDefaultConfig());
    }));
  }
  /**
   * Load evaluation data from XML file (if needed for parsing)
   */
  loadEvaluationDataXml() {
    return this.http.get('./assets/evaluation_data.xml', {
      responseType: 'text'
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(error => {
      console.error('Failed to load evaluation data XML:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)('');
    }));
  }
  /**
   * Parse XML evaluation data and convert to JSON format
   */
  parseEvaluationXml(xmlContent) {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror');
      if (parserError) {
        console.error('XML parsing error:', parserError.textContent);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      // Extract evaluation criteria from XML
      const criteriaField = xmlDoc.querySelector('field[name="evaluation_criteria"]');
      if (!criteriaField) {
        console.error('No evaluation criteria found in XML');
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      const criteriaJson = criteriaField.textContent?.trim();
      if (!criteriaJson) {
        console.error('Empty evaluation criteria in XML');
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      // Parse the JSON content
      const criteria = JSON.parse(criteriaJson);
      // Extract other fields
      const durationField = xmlDoc.querySelector('field[name="evaluation_duration"]');
      const attemptsField = xmlDoc.querySelector('field[name="max_attempts"]');
      const config = {
        evaluation_duration: durationField ? parseInt(durationField.textContent || '7') : 7,
        max_attempts: attemptsField ? parseInt(attemptsField.textContent || '1') : 1,
        criteria: criteria
      };
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(config);
    } catch (error) {
      console.error('Error parsing evaluation XML:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
    }
  }
  /**
   * Get evaluation configuration with fallback strategy:
   * 1. Try to load from static JSON
   * 2. If that fails, use default config
   */
  getEvaluationConfig() {
    return this.loadStaticConfig().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(() => {
      console.log('Static JSON config failed, using default config...');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.getDefaultConfig());
    }));
  }
  /**
   * Minimal default configuration as emergency fallback only
   * This should only be used if both API and static file fail
   */
  getDefaultConfig() {
    return {
      evaluation_duration: 7,
      max_attempts: 1,
      criteria: {
        categories: [{
          id: 'question_1',
          name: 'General Evaluation',
          description: 'Please rate the overall performance',
          max_score: 10,
          questions: [{
            id: 'general_rating',
            text: 'How would you rate the overall performance?',
            type: 'rating',
            scale: 10
          }]
        }]
      }
    };
  }
  static {
    this.ɵfac = function EvaluationDataService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationDataService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: EvaluationDataService,
      factory: EvaluationDataService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 2519:
/*!**********************************************************!*\
  !*** ./src/app/services/iframe-communication.service.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IframeCommunicationService: () => (/* binding */ IframeCommunicationService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


class IframeCommunicationService {
  constructor() {
    this.evaluationDataSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(null);
    this.evaluationData$ = this.evaluationDataSubject.asObservable();
    this.initializeMessageListener();
    this.requestEvaluationData();
  }
  initializeMessageListener() {
    window.addEventListener('message', event => {
      // Verify origin for security (adjust as needed)
      if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {
        return;
      }
      if (event.data && event.data.type === 'EVALUATION_DATA') {
        this.evaluationDataSubject.next(event.data.payload);
      }
    });
  }
  requestEvaluationData() {
    // Try to get data from URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const driverName = urlParams.get('driverName');
    const driverId = urlParams.get('driverId');
    const linkId = urlParams.get('linkId');
    const expiryDate = urlParams.get('expiryDate');
    if (token && driverName && driverId && linkId && expiryDate) {
      const evaluationData = {
        token,
        driverName,
        driverId: parseInt(driverId),
        linkId: parseInt(linkId),
        expiryDate
      };
      this.evaluationDataSubject.next(evaluationData);
    } else {
      // Request data from parent window
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'REQUEST_EVALUATION_DATA'
        }, '*');
      }
    }
  }
  sendMessage(type, payload) {
    if (window.parent !== window) {
      window.parent.postMessage({
        type,
        payload
      }, '*');
    }
  }
  notifyEvaluationComplete(result) {
    this.sendMessage('EVALUATION_COMPLETE', result);
  }
  notifyEvaluationError(error) {
    const errorPayload = typeof error === 'string' ? {
      error
    } : error;
    this.sendMessage('EVALUATION_ERROR', errorPayload);
  }
  static {
    this.ɵfac = function IframeCommunicationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || IframeCommunicationService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: IframeCommunicationService,
      factory: IframeCommunicationService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HttpLoaderFactory: () => (/* binding */ HttpLoaderFactory)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_app_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.component */ 92);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngx-translate/core */ 852);
/* harmony import */ var _ngx_translate_http_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ngx-translate/http-loader */ 8952);
/* harmony import */ var _app_app_routes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app/app.routes */ 2181);









// Translation loader factory
function HttpLoaderFactory(http) {
  return new _ngx_translate_http_loader__WEBPACK_IMPORTED_MODULE_2__.TranslateHttpLoader(http, './assets/i18n/', '.json');
}
(0,_angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__.bootstrapApplication)(_app_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent, {
  providers: [(0,_angular_router__WEBPACK_IMPORTED_MODULE_4__.provideRouter)(_app_app_routes__WEBPACK_IMPORTED_MODULE_1__.routes), (0,_angular_common_http__WEBPACK_IMPORTED_MODULE_5__.provideHttpClient)(), (0,_angular_core__WEBPACK_IMPORTED_MODULE_6__.importProvidersFrom)(_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule.forRoot(), _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateModule.forRoot({
    loader: {
      provide: _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateLoader,
      useFactory: HttpLoaderFactory,
      deps: [_angular_common_http__WEBPACK_IMPORTED_MODULE_5__.HttpClient]
    },
    defaultLanguage: 'ar'
  }))]
}).catch(err => console.error(err));

/***/ }),

/***/ 8996:
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ lazy ^\.\/.*\.entry\.js$ include: \.entry\.js$ exclude: \.system\.entry\.js$ namespace object ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ion-accordion_2.entry.js": [
		7518,
		"common",
		"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js"
	],
	"./ion-action-sheet.entry.js": [
		1981,
		"common",
		"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js"
	],
	"./ion-alert.entry.js": [
		1603,
		"common",
		"node_modules_ionic_core_dist_esm_ion-alert_entry_js"
	],
	"./ion-app_8.entry.js": [
		2273,
		"common",
		"node_modules_ionic_core_dist_esm_ion-app_8_entry_js"
	],
	"./ion-avatar_3.entry.js": [
		9642,
		"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"
	],
	"./ion-back-button.entry.js": [
		2095,
		"common",
		"node_modules_ionic_core_dist_esm_ion-back-button_entry_js"
	],
	"./ion-backdrop.entry.js": [
		2335,
		"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js"
	],
	"./ion-breadcrumb_2.entry.js": [
		8221,
		"common",
		"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js"
	],
	"./ion-button_2.entry.js": [
		7184,
		"node_modules_ionic_core_dist_esm_ion-button_2_entry_js"
	],
	"./ion-card_5.entry.js": [
		8759,
		"node_modules_ionic_core_dist_esm_ion-card_5_entry_js"
	],
	"./ion-checkbox.entry.js": [
		4248,
		"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js"
	],
	"./ion-chip.entry.js": [
		9863,
		"node_modules_ionic_core_dist_esm_ion-chip_entry_js"
	],
	"./ion-col_3.entry.js": [
		1769,
		"node_modules_ionic_core_dist_esm_ion-col_3_entry_js"
	],
	"./ion-datetime-button.entry.js": [
		2569,
		"default-node_modules_ionic_core_dist_esm_data-GIsHsYIB_js",
		"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js"
	],
	"./ion-datetime_3.entry.js": [
		6534,
		"default-node_modules_ionic_core_dist_esm_data-GIsHsYIB_js",
		"common",
		"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js"
	],
	"./ion-fab_3.entry.js": [
		5458,
		"common",
		"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js"
	],
	"./ion-img.entry.js": [
		654,
		"node_modules_ionic_core_dist_esm_ion-img_entry_js"
	],
	"./ion-infinite-scroll_2.entry.js": [
		6034,
		"common",
		"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js"
	],
	"./ion-input-otp.entry.js": [
		381,
		"common",
		"node_modules_ionic_core_dist_esm_ion-input-otp_entry_js"
	],
	"./ion-input-password-toggle.entry.js": [
		5196,
		"common",
		"node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js"
	],
	"./ion-input.entry.js": [
		761,
		"default-node_modules_ionic_core_dist_esm_input_utils-zWijNCrx_js-node_modules_ionic_core_dist-2e0994",
		"common",
		"node_modules_ionic_core_dist_esm_ion-input_entry_js"
	],
	"./ion-item-option_3.entry.js": [
		6492,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js"
	],
	"./ion-item_8.entry.js": [
		9557,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item_8_entry_js"
	],
	"./ion-loading.entry.js": [
		8353,
		"common",
		"node_modules_ionic_core_dist_esm_ion-loading_entry_js"
	],
	"./ion-menu_3.entry.js": [
		1024,
		"common",
		"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js"
	],
	"./ion-modal.entry.js": [
		9160,
		"common",
		"node_modules_ionic_core_dist_esm_ion-modal_entry_js"
	],
	"./ion-nav_2.entry.js": [
		393,
		"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js"
	],
	"./ion-picker-column-option.entry.js": [
		8442,
		"node_modules_ionic_core_dist_esm_ion-picker-column-option_entry_js"
	],
	"./ion-picker-column.entry.js": [
		3110,
		"common",
		"node_modules_ionic_core_dist_esm_ion-picker-column_entry_js"
	],
	"./ion-picker.entry.js": [
		5575,
		"node_modules_ionic_core_dist_esm_ion-picker_entry_js"
	],
	"./ion-popover.entry.js": [
		6772,
		"common",
		"node_modules_ionic_core_dist_esm_ion-popover_entry_js"
	],
	"./ion-progress-bar.entry.js": [
		4810,
		"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js"
	],
	"./ion-radio_2.entry.js": [
		4639,
		"common",
		"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js"
	],
	"./ion-range.entry.js": [
		628,
		"common",
		"node_modules_ionic_core_dist_esm_ion-range_entry_js"
	],
	"./ion-refresher_2.entry.js": [
		8471,
		"common",
		"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js"
	],
	"./ion-reorder_2.entry.js": [
		1479,
		"common",
		"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js"
	],
	"./ion-ripple-effect.entry.js": [
		4065,
		"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js"
	],
	"./ion-route_4.entry.js": [
		7971,
		"node_modules_ionic_core_dist_esm_ion-route_4_entry_js"
	],
	"./ion-searchbar.entry.js": [
		3184,
		"common",
		"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js"
	],
	"./ion-segment-content.entry.js": [
		4312,
		"node_modules_ionic_core_dist_esm_ion-segment-content_entry_js"
	],
	"./ion-segment-view.entry.js": [
		4540,
		"node_modules_ionic_core_dist_esm_ion-segment-view_entry_js"
	],
	"./ion-segment_2.entry.js": [
		469,
		"common",
		"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js"
	],
	"./ion-select-modal.entry.js": [
		7101,
		"node_modules_ionic_core_dist_esm_ion-select-modal_entry_js"
	],
	"./ion-select_3.entry.js": [
		3709,
		"common",
		"node_modules_ionic_core_dist_esm_ion-select_3_entry_js"
	],
	"./ion-spinner.entry.js": [
		388,
		"common",
		"node_modules_ionic_core_dist_esm_ion-spinner_entry_js"
	],
	"./ion-split-pane.entry.js": [
		2392,
		"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js"
	],
	"./ion-tab-bar_2.entry.js": [
		6059,
		"common",
		"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js"
	],
	"./ion-tab_2.entry.js": [
		5427,
		"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js"
	],
	"./ion-text.entry.js": [
		198,
		"node_modules_ionic_core_dist_esm_ion-text_entry_js"
	],
	"./ion-textarea.entry.js": [
		1735,
		"default-node_modules_ionic_core_dist_esm_input_utils-zWijNCrx_js-node_modules_ionic_core_dist-2e0994",
		"node_modules_ionic_core_dist_esm_ion-textarea_entry_js"
	],
	"./ion-toast.entry.js": [
		7510,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toast_entry_js"
	],
	"./ion-toggle.entry.js": [
		5297,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toggle_entry_js"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = 8996;
module.exports = webpackAsyncContext;

/***/ }),

/***/ 4140:
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/@stencil/core/internal/client/ lazy ^\.\/.*\.entry\.js.*$ include: \.entry\.js$ exclude: \.system\.entry\.js$ strict namespace object ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(() => {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = () => ([]);
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 4140;
module.exports = webpackEmptyAsyncContext;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map