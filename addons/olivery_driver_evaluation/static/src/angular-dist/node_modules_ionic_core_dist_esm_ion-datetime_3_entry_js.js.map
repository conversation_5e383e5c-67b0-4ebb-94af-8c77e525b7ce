{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACwL;AACxH;AAC0C;AAC3D;AACiC;AACsD;AACo1B;AACh5B;AAC8J;AACzK;AACwD;AAC1F;AACe;AACF;AACA;AACT;AAEjC,MAAM4H,cAAc,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,KAAK;EACpD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,GAAGH,OAAO,EAAE;IACrC,OAAO,IAAI;EACf;EACA,IAAIE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,GAAGH,OAAO,EAAE;IACrC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAGA,CAACC,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,KAAK;EAC/D;AACJ;AACA;AACA;EACI,IAAID,QAAQ,CAACE,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAID,SAAS,KAAKE,SAAS,IAAI,CAACF,SAAS,CAACG,QAAQ,CAACJ,QAAQ,CAACE,GAAG,CAAC,EAAE;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIN,QAAQ,IAAIzF,oDAAQ,CAAC6F,QAAQ,EAAEJ,QAAQ,CAAC,EAAE;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,QAAQ,IAAIzF,oDAAO,CAAC4F,QAAQ,EAAEH,QAAQ,CAAC,EAAE;IACzC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,mBAAmB,GAAGA,CAACC,MAAM,EAAEN,QAAQ,EAAEO,WAAW,EAAEC,UAAU,EAAEZ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,KAAK;EACtG;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMQ,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;EACjF;AACJ;AACA;AACA;EACI,MAAMK,QAAQ,GAAGH,gBAAgB,CAACI,IAAI,CAAEC,KAAK,IAAKtG,oDAAS,CAACwF,QAAQ,EAAEc,KAAK,CAAC,CAAC,KAAKX,SAAS;EAC3F,MAAMY,OAAO,GAAGvG,oDAAS,CAACwF,QAAQ,EAAEQ,UAAU,CAAC;EAC/C,MAAMQ,QAAQ,GAAGjB,aAAa,CAACC,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,CAAC;EACvE;AACJ;AACA;AACA;EACI,OAAO;IACHe,QAAQ;IACRJ,QAAQ;IACRG,OAAO;IACPE,YAAY,EAAEL,QAAQ,GAAG,MAAM,GAAG,IAAI;IACtCM,SAAS,EAAExG,oDAAoB,CAAC4F,MAAM,EAAES,OAAO,EAAEf,QAAQ,CAAC;IAC1DmB,IAAI,EAAEnB,QAAQ,CAACE,GAAG,IAAI,IAAI,GAAGzF,oDAAM,CAAC6F,MAAM,EAAEN,QAAQ,CAAC,GAAG;EAC5D,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMoB,eAAe,GAAGA,CAACpB,QAAQ,EAAE;EAAEJ,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAC3D;EACA,IAAIH,cAAc,CAACM,QAAQ,CAACF,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,EAAE;IACnD,OAAO,IAAI;EACf;EACA;EACA;EACA,IAAKD,QAAQ,IAAIzF,oDAAQ,CAAC6F,QAAQ,EAAEJ,QAAQ,CAAC,IAAMC,QAAQ,IAAIzF,oDAAO,CAAC4F,QAAQ,EAAEH,QAAQ,CAAE,EAAE;IACzF,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMwB,mBAAmB,GAAGA,CAACrB,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,KAAK;EAC1D,MAAMyB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnH,oDAAgB,CAAC2F,QAAQ,CAAC,CAAC,EAAE;IAAEE,GAAG,EAAE;EAAK,CAAC,CAAC;EAC7F,OAAOkB,eAAe,CAACE,SAAS,EAAE;IAC9B1B,QAAQ;IACRC;EACJ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM4B,mBAAmB,GAAGA,CAACzB,QAAQ,EAAEH,QAAQ,KAAK;EAChD,MAAM6B,SAAS,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjH,oDAAY,CAACyF,QAAQ,CAAC,CAAC,EAAE;IAAEE,GAAG,EAAE;EAAK,CAAC,CAAC;EACzF,OAAOkB,eAAe,CAACM,SAAS,EAAE;IAC9B7B;EACJ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM8B,kBAAkB,GAAGA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,EAAE,KAAK;EAChE,IAAIpB,KAAK,CAACC,OAAO,CAACiB,gBAAgB,CAAC,EAAE;IACjC,MAAMG,qBAAqB,GAAGF,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,MAAMC,iBAAiB,GAAGL,gBAAgB,CAACf,IAAI,CAAEqB,EAAE,IAAKA,EAAE,CAACC,IAAI,KAAKJ,qBAAqB,CAAC;IAC1F,IAAIE,iBAAiB,EAAE;MACnB,OAAO;QACHG,SAAS,EAAEH,iBAAiB,CAACG,SAAS;QACtCC,eAAe,EAAEJ,iBAAiB,CAACI;MACvC,CAAC;IACL;EACJ,CAAC,MACI;IACD;AACR;AACA;AACA;IACQ,IAAI;MACA,OAAOT,gBAAgB,CAACC,aAAa,CAAC;IAC1C,CAAC,CACD,OAAOvJ,CAAC,EAAE;MACNP,qDAAa,CAAC,wHAAwH,EAAE+J,EAAE,EAAExJ,CAAC,CAAC;IAClJ;EACJ;EACA,OAAO6H,SAAS;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMmC,sBAAsB,GAAGA,CAACR,EAAE,EAAES,aAAa,KAAK;EAClD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAI,CAAC,CAACH,EAAE,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,MACxI,CAACH,EAAE,GAAGF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY,CAAC,KAC7I,CAACH,EAAE,GAAGH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,CAAC,KACzI,CAACD,EAAE,GAAGJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,CAAC,EAAE;IAChJ5K,qDAAe,CAAC,sFAAsF,EAAE6J,EAAE,CAAC;EAC/G;AACJ,CAAC;AACD,MAAMiB,kCAAkC,GAAGA,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,KAAK;EAC5E;EACA,IAAI,CAACA,aAAa,EACd;EACJ;EACA,QAAQS,YAAY;IAChB,KAAK,MAAM;IACX,KAAK,YAAY;IACjB,KAAK,OAAO;IACZ,KAAK,MAAM;MACP,IAAIT,aAAa,CAACJ,IAAI,KAAKhC,SAAS,EAAE;QAClClI,qDAAe,CAAC,yBAAyB+K,YAAY,yDAAyD,EAAElB,EAAE,CAAC;MACvH;MACA;IACJ,KAAK,MAAM;MACP,IAAIS,aAAa,CAACO,IAAI,KAAK3C,SAAS,EAAE;QAClClI,qDAAe,CAAC,mFAAmF,EAAE6J,EAAE,CAAC;MAC5G;MACA;IACJ,KAAK,WAAW;IAChB,KAAK,WAAW;MACZ,IAAIS,aAAa,CAACJ,IAAI,KAAKhC,SAAS,IAAIoC,aAAa,CAACO,IAAI,KAAK3C,SAAS,EAAE;QACtElI,qDAAe,CAAC,yBAAyB+K,YAAY,kFAAkF,EAAElB,EAAE,CAAC;MAChJ;MACA;EACR;AACJ,CAAC;AAED,MAAMmB,cAAc,GAAG,y4VAAy4V;AAEh6V,MAAMC,aAAa,GAAG,2jTAA2jT;AAEjlT,MAAMC,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBnL,qDAAgB,CAAC,IAAI,EAAEkL,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAGlL,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACmL,SAAS,GAAGnL,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACoL,cAAc,GAAGpL,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACqL,QAAQ,GAAGrL,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsL,OAAO,GAAGtL,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACuL,QAAQ,GAAGvL,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACwL,SAAS,GAAGxL,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyL,OAAO,GAAG,UAAUC,WAAW,EAAE,EAAE;IACxC,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC1D,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC2D,YAAY,GAAG;MAChBC,KAAK,EAAE,CAAC;MACRjE,GAAG,EAAE,EAAE;MACPJ,IAAI,EAAE,IAAI;MACVsE,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE;IACnB,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,SAAS;IACtB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAAC9C,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAAC2D,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC5B,YAAY,GAAG,WAAW;IAC/B;AACR;AACA;IACQ,IAAI,CAAC6B,UAAU,GAAG,QAAQ;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB;AACR;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,OAAO;IACxB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACzE,MAAM,GAAG,SAAS;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAAC0E,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,yBAAyB,GAAG,MAAM;MACnC,MAAM;QAAEP,QAAQ;QAAEQ;MAAM,CAAC,GAAG,IAAI;MAChC,IAAI,CAACR,QAAQ,IAAIvE,KAAK,CAACC,OAAO,CAAC8E,KAAK,CAAC,EAAE;QACnC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgBxN,qDAAe,CAAC;AAChC;AACA,mBAAmBwN,KAAK,CAACC,GAAG,CAAE/K,CAAC,IAAK,IAAIA,CAAC,GAAG,CAAC,CAACgL,IAAI,CAAC,IAAI,CAAC;AACxD,CAAC,EAAE,IAAI,CAAC7D,EAAE,CAAC;MACC;IACJ,CAAC;IACD,IAAI,CAAC8D,QAAQ,GAAIH,KAAK,IAAK;MACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACjC,SAAS,CAACqC,IAAI,CAAC;QAAEJ;MAAM,CAAC,CAAC;IAClC,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACK,0BAA0B,GAAG,MAAM;MACpC,IAAItD,EAAE;MACN,MAAM;QAAEuD;MAAa,CAAC,GAAG,IAAI;MAC7B,OAAO,CAACvD,EAAE,GAAG,IAAI,CAACwD,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIxD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGuD,YAAY;IACpF,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEzF;MAAY,CAAC,GAAG,IAAI;MAC5B,OAAOG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW;IACpE,CAAC;IACD,IAAI,CAAC0F,kBAAkB,GAAIC,IAAI,IAAK;MAChC,MAAMC,cAAc,GAAG,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,wBAAwB,CAAC;MAChE,IAAID,cAAc,EAAE;QAChBA,cAAc,CAAChH,OAAO,CAACgB,SAAS,EAAE+F,IAAI,CAAC;MAC3C;IACJ,CAAC;IACD,IAAI,CAACG,eAAe,GAAIvF,KAAK,IAAK;MAC9B,IAAI,CAACoD,YAAY,GAAG3C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,CAAC;IAChD,CAAC;IACD,IAAI,CAACwF,cAAc,GAAG,CAACxF,KAAK,EAAEyF,UAAU,GAAG,KAAK,KAAK;MACjD;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAC5B,QAAQ,EAAE;QACf;MACJ;MACA,MAAM;QAAEM,QAAQ;QAAErF,QAAQ;QAAEC,QAAQ;QAAEU;MAAY,CAAC,GAAG,IAAI;MAC1D;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMiG,cAAc,GAAG5L,oDAAa,CAACkG,KAAK,EAAElB,QAAQ,EAAEC,QAAQ,CAAC;MAC/D,IAAI,CAACwG,eAAe,CAACG,cAAc,CAAC;MACpC,IAAIvB,QAAQ,EAAE;QACV,MAAMxE,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;QACjF,IAAIgG,UAAU,EAAE;UACZ,IAAI,CAAChG,WAAW,GAAGE,gBAAgB,CAACgG,MAAM,CAAE3M,CAAC,IAAK,CAACU,oDAAS,CAACV,CAAC,EAAE0M,cAAc,CAAC,CAAC;QACpF,CAAC,MACI;UACD,IAAI,CAACjG,WAAW,GAAG,CAAC,GAAGE,gBAAgB,EAAE+F,cAAc,CAAC;QAC5D;MACJ,CAAC,MACI;QACD,IAAI,CAACjG,WAAW,GAAGgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgF,cAAc,CAAC;MACxD;MACA,MAAME,iBAAiB,GAAG,IAAI,CAAC5E,EAAE,CAAC6E,aAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI;MAC5E,IAAID,iBAAiB,IAAI,IAAI,CAACvB,kBAAkB,EAAE;QAC9C;MACJ;MACA,IAAI,CAACyB,OAAO,CAAC,CAAC;IAClB,CAAC;IACD,IAAI,CAACC,2BAA2B,GAAG,MAAM;MACrC,MAAMC,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMC,IAAI,GAAG,IAAI,CAACjF,EAAE,CAACkF,UAAU;MAC/B;AACZ;AACA;AACA;MACY,MAAMC,YAAY,GAAGH,eAAe,CAACH,aAAa,CAAC,gCAAgC,CAAC;MACpF;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMO,sBAAsB,GAAIC,EAAE,IAAK;QACnC,IAAI3E,EAAE;QACN,MAAM4E,MAAM,GAAGD,EAAE,CAAC,CAAC,CAAC;QACpB;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAI,CAAC,CAAC3E,EAAE,GAAG4E,MAAM,CAACC,QAAQ,MAAM,IAAI,IAAI7E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC0G,eAAe,CAACQ,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;UAChJ;QACJ;QACA,IAAI,CAACC,eAAe,CAACP,YAAY,CAAC;MACtC,CAAC;MACD,MAAMQ,EAAE,GAAG,IAAIC,gBAAgB,CAACR,sBAAsB,CAAC;MACvDO,EAAE,CAACE,OAAO,CAACb,eAAe,EAAE;QAAEc,eAAe,EAAE,CAAC,OAAO,CAAC;QAAEC,iBAAiB,EAAE;MAAK,CAAC,CAAC;MACpF,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3BL,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,UAAU,CAAC,CAAC;MAC3D,CAAC;MACD;AACZ;AACA;AACA;MACYjB,eAAe,CAACkB,gBAAgB,CAAC,SAAS,EAAGb,EAAE,IAAK;QAChD,MAAMc,aAAa,GAAGlB,IAAI,CAACkB,aAAa;QACxC,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAACX,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;UACrE;QACJ;QACA,MAAMzG,KAAK,GAAGhG,oDAAuB,CAACmN,aAAa,CAAC;QACpD,IAAIC,YAAY;QAChB,QAAQf,EAAE,CAACgB,GAAG;UACV,KAAK,WAAW;YACZhB,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG3M,oDAAW,CAACuF,KAAK,CAAC;YACjC;UACJ,KAAK,SAAS;YACVqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG5M,oDAAe,CAACwF,KAAK,CAAC;YACrC;UACJ,KAAK,YAAY;YACbqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG7M,oDAAU,CAACyF,KAAK,CAAC;YAChC;UACJ,KAAK,WAAW;YACZqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG/M,oDAAc,CAAC2F,KAAK,CAAC;YACpC;UACJ,KAAK,MAAM;YACPqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGhN,oDAAc,CAAC4F,KAAK,CAAC;YACpC;UACJ,KAAK,KAAK;YACNqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGjN,oDAAY,CAAC6F,KAAK,CAAC;YAClC;UACJ,KAAK,QAAQ;YACTqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGf,EAAE,CAACkB,QAAQ,GAAGrN,oDAAe,CAAC8F,KAAK,CAAC,GAAGzG,oDAAgB,CAACyG,KAAK,CAAC;YAC7E;UACJ,KAAK,UAAU;YACXqG,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGf,EAAE,CAACkB,QAAQ,GAAGtN,oDAAW,CAAC+F,KAAK,CAAC,GAAGvG,oDAAY,CAACuG,KAAK,CAAC;YACrE;UACJ;AACpB;AACA;AACA;AACA;AACA;UACoB;YACI;QACR;QACA;AAChB;AACA;AACA;QACgB,IAAIf,aAAa,CAACmI,YAAY,EAAE,IAAI,CAACtI,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC3D;QACJ;QACA,IAAI,CAACwG,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0C,YAAY,CAAC,EAAEgE,YAAY,CAAC,CAAC;QACvF;AAChB;AACA;AACA;QACgBI,qBAAqB,CAAC,MAAM,IAAI,CAACd,eAAe,CAACP,YAAY,CAAC,CAAC;MACnE,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACO,eAAe,GAAIP,YAAY,IAAK;MACrC;AACZ;AACA;AACA;AACA;MACY,MAAM;QAAE/G,GAAG;QAAEiE,KAAK;QAAErE;MAAK,CAAC,GAAG,IAAI,CAACoE,YAAY;MAC9C,MAAMqE,YAAY,GAAG,IAAIC,IAAI,CAAC,GAAGrE,KAAK,MAAMrE,IAAI,EAAE,CAAC,CAACrF,MAAM,CAAC,CAAC;MAC5D,MAAMgO,MAAM,GAAGF,YAAY,IAAI,IAAI,CAACvD,cAAc,GAC5CuD,YAAY,GAAG,IAAI,CAACvD,cAAc,GAClC,CAAC,IAAI,IAAI,CAACA,cAAc,GAAGuD,YAAY,CAAC;MAC9C,IAAIrI,GAAG,KAAK,IAAI,EAAE;QACd;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMwI,KAAK,GAAGzB,YAAY,CAACN,aAAa,CAAC,qCAAqC8B,MAAM,GAAGvI,GAAG,iBAAiB,CAAC;MAC5G,IAAIwI,KAAK,EAAE;QACPA,KAAK,CAACC,KAAK,CAAC,CAAC;MACjB;IACJ,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,MAAM;MACzB,MAAM;QAAEC,GAAG;QAAE9C;MAAa,CAAC,GAAG,IAAI;MAClC,IAAI8C,GAAG,KAAK1I,SAAS,EAAE;QACnB,IAAI,CAACP,QAAQ,GAAGO,SAAS;QACzB;MACJ;MACA,IAAI,CAACP,QAAQ,GAAGpE,oDAAa,CAACqN,GAAG,EAAE9C,YAAY,CAAC;IACpD,CAAC;IACD,IAAI,CAAC+C,eAAe,GAAG,MAAM;MACzB,MAAM;QAAEC,GAAG;QAAEhD;MAAa,CAAC,GAAG,IAAI;MAClC,IAAIgD,GAAG,KAAK5I,SAAS,EAAE;QACnB,IAAI,CAACN,QAAQ,GAAGM,SAAS;QACzB;MACJ;MACA,IAAI,CAACN,QAAQ,GAAGpE,oDAAa,CAACsN,GAAG,EAAEhD,YAAY,CAAC;IACpD,CAAC;IACD,IAAI,CAACiD,0BAA0B,GAAG,MAAM;MACpC,MAAMlC,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMmC,MAAM,GAAGnC,eAAe,CAACoC,gBAAgB,CAAC,iBAAiB,CAAC;MAClE,MAAMC,UAAU,GAAGF,MAAM,CAAC,CAAC,CAAC;MAC5B,MAAMG,YAAY,GAAGH,MAAM,CAAC,CAAC,CAAC;MAC9B,MAAMI,QAAQ,GAAGJ,MAAM,CAAC,CAAC,CAAC;MAC1B,MAAMK,IAAI,GAAG/Q,qDAAU,CAAC,IAAI,CAAC;MAC7B,MAAMgR,qBAAqB,GAAGD,IAAI,KAAK,KAAK,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,cAAc,GAAG,CAAC;MAChH;AACZ;AACA;AACA;AACA;AACA;MACYhR,qDAAS,CAAC,MAAM;QACZqO,eAAe,CAAC4C,UAAU,GAAGP,UAAU,CAACQ,WAAW,IAAIpQ,mDAAK,CAAC,IAAI,CAACuI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/E,MAAM8H,eAAe,GAAI9I,KAAK,IAAK;UAC/B,MAAM+I,GAAG,GAAG/C,eAAe,CAACgD,qBAAqB,CAAC,CAAC;UACnD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMC,SAAS,GAAGxQ,mDAAK,CAAC,IAAI,CAACuI,EAAE,CAAC,GAAGgF,eAAe,CAAC4C,UAAU,IAAI,CAAC,CAAC,GAAG5C,eAAe,CAAC4C,UAAU,IAAI,CAAC;UACrG,MAAMvF,KAAK,GAAG4F,SAAS,GAAGZ,UAAU,GAAGE,QAAQ;UAC/C;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMW,QAAQ,GAAG7F,KAAK,CAAC2F,qBAAqB,CAAC,CAAC;UAC9C,IAAIG,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAC/N,CAAC,GAAG4N,GAAG,CAAC5N,CAAC,CAAC,GAAG,CAAC,EAChC;UACJ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAM;YAAEkO;UAAgB,CAAC,GAAG,IAAI;UAChC,IAAIA,eAAe,KAAKhK,SAAS,EAAE;YAC/B,OAAO;cAAEgE,KAAK,EAAEgG,eAAe,CAAChG,KAAK;cAAErE,IAAI,EAAEqK,eAAe,CAACrK,IAAI;cAAEI,GAAG,EAAEiK,eAAe,CAACjK;YAAI,CAAC;UACjG;UACA;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAIiE,KAAK,KAAKgF,UAAU,EAAE;YACtB,OAAO9O,oDAAgB,CAACyG,KAAK,CAAC;UAClC,CAAC,MACI,IAAIqD,KAAK,KAAKkF,QAAQ,EAAE;YACzB,OAAO9O,oDAAY,CAACuG,KAAK,CAAC;UAC9B,CAAC,MACI;YACD;UACJ;QACJ,CAAC;QACD,MAAMsJ,iBAAiB,GAAGA,CAAA,KAAM;UAC5B,IAAIb,qBAAqB,EAAE;YACvBzC,eAAe,CAACuD,KAAK,CAACC,cAAc,CAAC,gBAAgB,CAAC;YACtDC,uBAAuB,GAAG,KAAK;UACnC;UACA;AACpB;AACA;AACA;UACoB,MAAMC,OAAO,GAAGZ,eAAe,CAAC,IAAI,CAAC1F,YAAY,CAAC;UAClD,IAAI,CAACsG,OAAO,EACR;UACJ,MAAM;YAAErG,KAAK;YAAEjE,GAAG;YAAEJ;UAAK,CAAC,GAAG0K,OAAO;UACpC,IAAIpJ,eAAe,CAAC;YAAE+C,KAAK;YAAErE,IAAI;YAAEI,GAAG,EAAE;UAAK,CAAC,EAAE;YAC5CN,QAAQ,EAAE2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5B,QAAQ,CAAC,EAAE;cAAEM,GAAG,EAAE;YAAK,CAAC,CAAC;YACxEL,QAAQ,EAAE0B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3B,QAAQ,CAAC,EAAE;cAAEK,GAAG,EAAE;YAAK,CAAC;UAC3E,CAAC,CAAC,EAAE;YACA;UACJ;UACA;AACpB;AACA;AACA;AACA;UACoB4G,eAAe,CAACuD,KAAK,CAACI,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC;UACvD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoBhS,qDAAS,CAAC,MAAM;YACZ,IAAI,CAAC4N,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0C,YAAY,CAAC,EAAE;cAAEC,KAAK;cAAEjE,GAAG,EAAEA,GAAG;cAAEJ;YAAK,CAAC,CAAC,CAAC;YACpGgH,eAAe,CAAC4C,UAAU,GAAGN,YAAY,CAACO,WAAW,IAAIpQ,mDAAK,CAAC,IAAI,CAACuI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACjFgF,eAAe,CAACuD,KAAK,CAACC,cAAc,CAAC,UAAU,CAAC;YAChD,IAAI,IAAI,CAACI,yBAAyB,EAAE;cAChC,IAAI,CAACA,yBAAyB,CAAC,CAAC;YACpC;UACJ,CAAC,CAAC;QACN,CAAC;QACD;AAChB;AACA;AACA;QACgB,IAAIC,aAAa;QACjB;AAChB;AACA;AACA;AACA;QACgB,IAAIJ,uBAAuB,GAAG,KAAK;QACnC,MAAMK,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAID,aAAa,EAAE;YACfE,YAAY,CAACF,aAAa,CAAC;UAC/B;UACA;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACJ,uBAAuB,IAAIhB,qBAAqB,EAAE;YACnDzC,eAAe,CAACuD,KAAK,CAACI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;YAC3DF,uBAAuB,GAAG,IAAI;UAClC;UACA;UACAI,aAAa,GAAGG,UAAU,CAACV,iBAAiB,EAAE,EAAE,CAAC;QACrD,CAAC;QACDtD,eAAe,CAACkB,gBAAgB,CAAC,QAAQ,EAAE4C,cAAc,CAAC;QAC1D,IAAI,CAACG,uBAAuB,GAAG,MAAM;UACjCjE,eAAe,CAACkE,mBAAmB,CAAC,QAAQ,EAAEJ,cAAc,CAAC;QACjE,CAAC;MACL,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACK,2BAA2B,GAAG,MAAM;MACrC,MAAM;QAAEF,uBAAuB;QAAEjD;MAAkB,CAAC,GAAG,IAAI;MAC3D,IAAIiD,uBAAuB,KAAK5K,SAAS,EAAE;QACvC4K,uBAAuB,CAAC,CAAC;MAC7B;MACA,IAAIjD,iBAAiB,KAAK3H,SAAS,EAAE;QACjC2H,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACoD,YAAY,GAAIzF,KAAK,IAAK;MAC3B,MAAM0F,QAAQ,GAAG1F,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKtF,SAAS,IAAIsF,KAAK,KAAK,EAAE,KAAK,CAAC/E,KAAK,CAACC,OAAO,CAAC8E,KAAK,CAAC,IAAIA,KAAK,CAAC2F,MAAM,GAAG,CAAC,CAAC;MACrH,MAAMC,cAAc,GAAGF,QAAQ,GAAGxP,oDAAS,CAAC8J,KAAK,CAAC,GAAG,IAAI,CAACM,YAAY;MACtE,MAAM;QAAEnG,QAAQ;QAAEC,QAAQ;QAAEqE,YAAY;QAAEpC;MAAG,CAAC,GAAG,IAAI;MACrD,IAAI,CAAC0D,yBAAyB,CAAC,CAAC;MAChC;AACZ;AACA;AACA;MACY,IAAI,CAAC6F,cAAc,EAAE;QACjB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIF,QAAQ,EAAE;QACVvP,oDAAsB,CAACyP,cAAc,EAAEzL,QAAQ,EAAEC,QAAQ,CAAC;MAC9D;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMyL,WAAW,GAAG5K,KAAK,CAACC,OAAO,CAAC0K,cAAc,CAAC,GAAGA,cAAc,CAACA,cAAc,CAACD,MAAM,GAAG,CAAC,CAAC,GAAGC,cAAc;MAC9G,MAAME,WAAW,GAAGvP,oDAAS,CAACsP,WAAW,EAAE1L,QAAQ,EAAEC,QAAQ,CAAC;MAC9D,MAAM;QAAEsE,KAAK;QAAEjE,GAAG;QAAEJ,IAAI;QAAEsE,IAAI;QAAEC;MAAO,CAAC,GAAGkH,WAAW;MACtD,MAAMjH,IAAI,GAAGxI,oDAAS,CAACsI,IAAI,CAAC;MAC5B;AACZ;AACA;AACA;AACA;MACY,IAAI+G,QAAQ,EAAE;QACV,IAAIzK,KAAK,CAACC,OAAO,CAAC0K,cAAc,CAAC,EAAE;UAC/B,IAAI,CAAC9K,WAAW,GAAG,CAAC,GAAG8K,cAAc,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAAC9K,WAAW,GAAG;YACf4D,KAAK;YACLjE,GAAG;YACHJ,IAAI;YACJsE,IAAI;YACJC,MAAM;YACNC;UACJ,CAAC;QACL;MACJ,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAAC/D,WAAW,GAAG,EAAE;MACzB;MACA,MAAMiL,cAAc,GAAIrH,KAAK,KAAKhE,SAAS,IAAIgE,KAAK,KAAKD,YAAY,CAACC,KAAK,IAAMrE,IAAI,KAAKK,SAAS,IAAIL,IAAI,KAAKoE,YAAY,CAACpE,IAAK;MAClI,MAAM2L,aAAa,GAAG3J,EAAE,CAACwF,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC;MAC7D,MAAM;QAAEmE,WAAW;QAAEzH;MAAiB,CAAC,GAAG,IAAI;MAC9C,IAAIyH,WAAW,IAAIF,cAAc,IAAIC,aAAa,IAAI,CAACxH,gBAAgB,EAAE;QACrE;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,CAAC0H,aAAa,CAACJ,WAAW,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAAClF,eAAe,CAAC;UACjBlC,KAAK;UACLjE,GAAG;UACHJ,IAAI;UACJsE,IAAI;UACJC,MAAM;UACNC;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACqH,aAAa;MAAA,IAAAC,IAAA,GAAAC,yMAAA,CAAG,WAAON,WAAW,EAAK;QACxC,MAAM;UAAErH;QAAa,CAAC,GAAGZ,KAAI;QAC7B;AACZ;AACA;AACA;AACA;AACA;QACYA,KAAI,CAAC6G,eAAe,GAAGoB,WAAW;QAClC;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,MAAMO,yBAAyB,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;UACvD1I,KAAI,CAACoH,yBAAyB,GAAGsB,OAAO;QAC5C,CAAC,CAAC;QACF;AACZ;AACA;AACA;QACY,MAAMC,mBAAmB,GAAG9R,oDAAQ,CAACoR,WAAW,EAAErH,YAAY,CAAC;QAC/D+H,mBAAmB,GAAG3I,KAAI,CAAChC,SAAS,CAAC,CAAC,GAAGgC,KAAI,CAAC5B,SAAS,CAAC,CAAC;QACzD,MAAMoK,yBAAyB;QAC/BxI,KAAI,CAACoH,yBAAyB,GAAGvK,SAAS;QAC1CmD,KAAI,CAAC6G,eAAe,GAAGhK,SAAS;MACpC,CAAC;MAAA,iBAAA+L,EAAA;QAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC3I,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACyG,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC3I,OAAO,CAACkC,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACsF,QAAQ,GAAG,MAAM;MAClB,OAAO,IAAI,CAAC1F,KAAK,IAAI,IAAI;IAC7B,CAAC;IACD,IAAI,CAAC/D,SAAS,GAAG,MAAM;MACnB,MAAMoF,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMpF,SAAS,GAAGoF,eAAe,CAACH,aAAa,CAAC,8BAA8B,CAAC;MAC/E,IAAI,CAACjF,SAAS,EAAE;QACZ;MACJ;MACA,MAAM6K,IAAI,GAAG7K,SAAS,CAAC8K,WAAW,GAAG,CAAC;MACtC1F,eAAe,CAAC2F,QAAQ,CAAC;QACrBC,GAAG,EAAE,CAAC;QACNH,IAAI,EAAEA,IAAI,IAAIhT,mDAAK,CAAC,IAAI,CAACuI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC6K,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACrL,SAAS,GAAG,MAAM;MACnB,MAAMwF,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMxF,SAAS,GAAGwF,eAAe,CAACH,aAAa,CAAC,+BAA+B,CAAC;MAChF,IAAI,CAACrF,SAAS,EAAE;QACZ;MACJ;MACAwF,eAAe,CAAC2F,QAAQ,CAAC;QACrBC,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACPI,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,IAAI,CAAC3I,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAClD,CAAC;EACL;EACA4I,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAE/K,EAAE;MAAES,aAAa;MAAES;IAAa,CAAC,GAAG,IAAI;IAChDD,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;IACnED,sBAAsB,CAACR,EAAE,EAAES,aAAa,CAAC;EAC7C;EACAuK,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACpE,eAAe,CAAC,CAAC;EAC1B;EACAqE,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnE,eAAe,CAAC,CAAC;EAC1B;EACAoE,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEpL,EAAE;MAAES,aAAa;MAAES;IAAa,CAAC,GAAG,IAAI;IAChDD,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;EACvE;EACA,IAAImJ,WAAWA,CAAA,EAAG;IACd,MAAM;MAAE1I,YAAY;MAAEuC;IAAY,CAAC,GAAG,IAAI;IAC1C,MAAM4H,mBAAmB,GAAGnK,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IACnH,OAAOmK,mBAAmB,IAAI,CAAC5H,WAAW;EAC9C;EACA6H,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,gBAAgB,GAAGnR,oDAAuB,CAAC,IAAI,CAACoR,UAAU,CAAC;EACpE;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,GAAGtR,oDAAuB,CAAC,IAAI,CAACuR,WAAW,CAAC;EACtE;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACC,eAAe,GAAGzR,oDAAuB,CAAC,IAAI,CAAC+D,SAAS,CAAC;EAClE;EACA2N,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,gBAAgB,GAAG3R,oDAAuB,CAAC,IAAI,CAAC4R,UAAU,CAAC;EACpE;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,kBAAkB,GAAG9R,oDAAuB,CAAC,IAAI,CAAC+R,YAAY,CAAC;EACxE;EACA;AACJ;AACA;EACUC,YAAYA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAtC,yMAAA;MACjB,MAAM;QAAEpG;MAAM,CAAC,GAAG0I,MAAI;MACtB,IAAIA,MAAI,CAAChD,QAAQ,CAAC,CAAC,EAAE;QACjBgD,MAAI,CAACjD,YAAY,CAACzF,KAAK,CAAC;MAC5B;MACA0I,MAAI,CAACpB,SAAS,CAAC,CAAC;MAChBoB,MAAI,CAAC1K,cAAc,CAACoC,IAAI,CAAC;QAAEJ;MAAM,CAAC,CAAC;IAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUmB,OAAOA,CAAA,EAAuB;IAAA,IAAAwH,MAAA;IAAA,OAAAvC,yMAAA,YAAtBwC,YAAY,GAAG,KAAK;MAC9B,MAAM;QAAEC,gBAAgB;QAAE/N,WAAW;QAAEgF,WAAW;QAAErB;MAAa,CAAC,GAAGkK,MAAI;MACzE;AACR;AACA;MACQ,IAAI7N,WAAW,KAAKJ,SAAS,IAAI,CAACmO,gBAAgB,EAAE;QAChD,MAAMC,kBAAkB,GAAG7N,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC;QACrD,IAAIgO,kBAAkB,IAAIhO,WAAW,CAAC6K,MAAM,KAAK,CAAC,EAAE;UAChD,IAAI7F,WAAW,EAAE;YACb;AACpB;AACA;AACA;AACA;YACoB6I,MAAI,CAACxI,QAAQ,CAACxJ,oDAAgB,CAAC8H,YAAY,CAAC,CAAC;UACjD,CAAC,MACI;YACDkK,MAAI,CAACxI,QAAQ,CAACzF,SAAS,CAAC;UAC5B;QACJ,CAAC,MACI;UACDiO,MAAI,CAACxI,QAAQ,CAACxJ,oDAAgB,CAACmE,WAAW,CAAC,CAAC;QAChD;MACJ;MACA,IAAI8N,YAAY,EAAE;QACdD,MAAI,CAACnI,kBAAkB,CAACuI,YAAY,CAAC;MACzC;IAAC,GAAArC,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUqC,KAAKA,CAACC,SAAS,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA9C,yMAAA;MACnB8C,MAAI,CAACzD,YAAY,CAACwD,SAAS,CAAC;IAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUE,MAAMA,CAAA,EAAuB;IAAA,IAAAC,MAAA;IAAA,OAAAhD,yMAAA,YAAtBwC,YAAY,GAAG,KAAK;MAC7BQ,MAAI,CAACtL,SAAS,CAACsC,IAAI,CAAC,CAAC;MACrB,IAAIwI,YAAY,EAAE;QACdQ,MAAI,CAAC5I,kBAAkB,CAAC6I,WAAW,CAAC;MACxC;IAAC,GAAA3C,KAAA,OAAAC,SAAA;EACL;EACA,IAAIkC,gBAAgBA,CAAA,EAAG;IACnB,MAAM;MAAEtL;IAAa,CAAC,GAAG,IAAI;IAC7B,OAAOA,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;EAClG;EACA+L,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,iBAAiB,GAAGjW,6EAAiB,CAAC,IAAI,CAAC+I,EAAE,CAAC,CAACmN,OAAO;EAC/D;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG7O,SAAS;IACtC;EACJ;EACAgP,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACnG,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACnC,2BAA2B,CAAC,CAAC;EACtC;EACAuI,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEtN,EAAE;MAAEuN;IAAuB,CAAC,GAAG,IAAI;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,eAAe,GAAIC,OAAO,IAAK;MACjC,MAAMpI,EAAE,GAAGoI,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI,CAACpI,EAAE,CAACqI,cAAc,EAAE;QACpB;MACJ;MACA,IAAI,CAACL,mBAAmB,CAAC,CAAC;MAC1B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY1W,qDAAS,CAAC,MAAM;QACZ,IAAI,CAACqJ,EAAE,CAACwF,SAAS,CAACmI,GAAG,CAAC,gBAAgB,CAAC;MAC3C,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,SAAS,GAAG,IAAIC,oBAAoB,CAACL,eAAe,EAAE;MAAEM,SAAS,EAAE,IAAI;MAAE7I,IAAI,EAAEjF;IAAG,CAAC,CAAC;IAC1F;AACR;AACA;AACA;AACA;AACA;IACQ9I,uDAAG,CAAC,MAAM0W,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/H,OAAO,CAAC0H,sBAAsB,CAAC,CAAC;IAC1G;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMQ,cAAc,GAAIN,OAAO,IAAK;MAChC,MAAMpI,EAAE,GAAGoI,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIpI,EAAE,CAACqI,cAAc,EAAE;QACnB;MACJ;MACA,IAAI,CAACvE,2BAA2B,CAAC,CAAC;MAClC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChH,gBAAgB,GAAG,KAAK;MAC7BxL,qDAAS,CAAC,MAAM;QACZ,IAAI,CAACqJ,EAAE,CAACwF,SAAS,CAACwI,MAAM,CAAC,gBAAgB,CAAC;MAC9C,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,QAAQ,GAAG,IAAIJ,oBAAoB,CAACE,cAAc,EAAE;MAAED,SAAS,EAAE,CAAC;MAAE7I,IAAI,EAAEjF;IAAG,CAAC,CAAC;IACrF9I,uDAAG,CAAC,MAAM+W,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpI,OAAO,CAAC0H,sBAAsB,CAAC,CAAC;IACvG;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMtI,IAAI,GAAG7N,uDAAc,CAAC,IAAI,CAAC4I,EAAE,CAAC;IACpCiF,IAAI,CAACiB,gBAAgB,CAAC,UAAU,EAAGb,EAAE,IAAKA,EAAE,CAAC6I,eAAe,CAAC,CAAC,CAAC;IAC/DjJ,IAAI,CAACiB,gBAAgB,CAAC,SAAS,EAAGb,EAAE,IAAKA,EAAE,CAAC6I,eAAe,CAAC,CAAC,CAAC;EAClE;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEjN,YAAY;MAAEgB,gBAAgB;MAAE8C,eAAe;MAAElH,QAAQ;MAAE2F,WAAW;MAAE4E;IAAgB,CAAC,GAAG,IAAI;IACxG;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+F,eAAe,GAAG,CAAC3K,WAAW,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,CAACnF,QAAQ,CAAC4C,YAAY,CAAC;IACjG,IAAIpD,QAAQ,KAAKO,SAAS,IAAI+P,eAAe,IAAIpJ,eAAe,EAAE;MAC9D,MAAMsC,YAAY,GAAGtC,eAAe,CAACH,aAAa,CAAC,gCAAgC,CAAC;MACpF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIyC,YAAY,IAAIe,eAAe,KAAKhK,SAAS,EAAE;QAC/C2G,eAAe,CAAC4C,UAAU,GAAGN,YAAY,CAACO,WAAW,IAAIpQ,mDAAK,CAAC,IAAI,CAACuI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACrF;IACJ;IACA,IAAIkC,gBAAgB,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACA,gBAAgB,GAAGhB,YAAY;MACpC;IACJ;IACA,IAAIA,YAAY,KAAKgB,gBAAgB,EAAE;MACnC;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAGhB,YAAY;IACpC,IAAI,CAACiI,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACkE,mBAAmB,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAClL,gBAAgB,GAAG,KAAK;IAC7BjL,uDAAG,CAAC,MAAM;MACN,IAAI,CAAC6K,SAAS,CAACgC,IAAI,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;EACAsK,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAErO,EAAE;MAAES,aAAa;MAAEX,gBAAgB;MAAEqD,QAAQ;MAAEjC,YAAY;MAAEuC;IAAY,CAAC,GAAG,IAAI;IACzF,IAAIN,QAAQ,EAAE;MACV,IAAIjC,YAAY,KAAK,MAAM,EAAE;QACzB/K,qDAAe,CAAC,qFAAqF,EAAE6J,EAAE,CAAC;MAC9G;MACA,IAAIyD,WAAW,EAAE;QACbtN,qDAAe,CAAC,oFAAoF,EAAE6J,EAAE,CAAC;MAC7G;IACJ;IACA,IAAIF,gBAAgB,KAAKzB,SAAS,EAAE;MAChC,IAAI6C,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW,EAAE;QACzF/K,qDAAe,CAAC,yHAAyH,EAAE6J,EAAE,CAAC;MAClJ;MACA,IAAIyD,WAAW,EAAE;QACbtN,qDAAe,CAAC,0FAA0F,EAAE6J,EAAE,CAAC;MACnH;IACJ;IACA,IAAIS,aAAa,EAAE;MACfQ,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;MACnED,sBAAsB,CAACR,EAAE,EAAES,aAAa,CAAC;IAC7C;IACA,MAAMuL,UAAU,GAAI,IAAI,CAACD,gBAAgB,GAAG3R,oDAAuB,CAAC,IAAI,CAAC4R,UAAU,CAAE;IACrF,MAAMG,YAAY,GAAI,IAAI,CAACD,kBAAkB,GAAG9R,oDAAuB,CAAC,IAAI,CAAC+R,YAAY,CAAE;IAC3F,MAAMR,WAAW,GAAI,IAAI,CAACD,iBAAiB,GAAGtR,oDAAuB,CAAC,IAAI,CAACuR,WAAW,CAAE;IACxF,MAAMH,UAAU,GAAI,IAAI,CAACD,gBAAgB,GAAGnR,oDAAuB,CAAC,IAAI,CAACoR,UAAU,CAAE;IACrF,MAAMrN,SAAS,GAAI,IAAI,CAAC0N,eAAe,GAAGzR,oDAAuB,CAAC,IAAI,CAAC+D,SAAS,CAAE;IAClF,MAAMO,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG7E,oDAAS,CAACW,oDAAQ,CAAC,CAAC,CAAE;IAC5D,IAAI,CAACsM,eAAe,CAAC,CAAC;IACtB,IAAI,CAACE,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC/C,YAAY,GAAGvJ,oDAAmB,CAAC;MACpCwD,QAAQ,EAAEQ,UAAU;MACpBiN,WAAW;MACXxN,SAAS;MACTqN,UAAU;MACVQ,UAAU;MACVG,YAAY;MACZrO,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;IACnB,CAAC,CAAC;IACF,IAAI,CAACqL,YAAY,CAAC,IAAI,CAACzF,KAAK,CAAC;IAC7B,IAAI,CAACsH,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,CAACnJ,QAAQ,CAACiC,IAAI,CAAC;MACfuK,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,IAAI;MACd,sBAAsB,EAAE,IAAI,CAACrP;IACjC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIsP,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEtP,QAAQ;MAAE2D,QAAQ;MAAEQ,kBAAkB;MAAEC;IAAgB,CAAC,GAAG,IAAI;IACxE;AACR;AACA;AACA;AACA;IACQ,MAAMmL,gBAAgB,GAAGvP,QAAQ,IAAI2D,QAAQ;IAC7C,MAAM+B,iBAAiB,GAAG,IAAI,CAAC5E,EAAE,CAAC6E,aAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI;IAC5E,IAAI,CAACD,iBAAiB,IAAI,CAACvB,kBAAkB,IAAI,CAACC,eAAe,EAAE;MAC/D;IACJ;IACA,MAAMoL,gBAAgB,GAAGA,CAAA,KAAM;MAC3B,IAAI,CAAC/B,KAAK,CAAC,CAAC;MACZ,IAAI,CAAC7I,QAAQ,CAACzF,SAAS,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAQzH,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAkB,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAmB,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;QACjG,CAAC,yBAAyB,GAAG,IAAI;QACjC,CAAC,kBAAkB,GAAG,IAAI,CAACrL;MAC/B;IAAE,CAAC,EAAE1M,qDAAC,CAAC,MAAM,EAAE;MAAEgM,IAAI,EAAE;IAAU,CAAC,EAAEhM,qDAAC,CAAC,aAAa,EAAE,IAAI,EAAEyM,kBAAkB,IAAKzM,qDAAC,CAAC,YAAY,EAAE;MAAEgY,EAAE,EAAE,eAAe;MAAEjM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEkM,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC/B,MAAM,CAAC,IAAI,CAAC;MAAE5N,QAAQ,EAAEuP;IAAiB,CAAC,EAAE,IAAI,CAAC1L,UAAU,CAAE,EAAEnM,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAoC,CAAC,EAAErL,eAAe,IAAK1M,qDAAC,CAAC,YAAY,EAAE;MAAEgY,EAAE,EAAE,cAAc;MAAEjM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEkM,OAAO,EAAEA,CAAA,KAAMH,gBAAgB,CAAC,CAAC;MAAExP,QAAQ,EAAEuP;IAAiB,CAAC,EAAE,IAAI,CAACxL,SAAS,CAAE,EAAEI,kBAAkB,IAAKzM,qDAAC,CAAC,YAAY,EAAE;MAAEgY,EAAE,EAAE,gBAAgB;MAAEjM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEkM,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC/J,OAAO,CAAC,IAAI,CAAC;MAAE5F,QAAQ,EAAEuP;IAAiB,CAAC,EAAE,IAAI,CAACzL,QAAQ,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxmB;EACA;AACJ;AACA;EACI8L,iBAAiBA,CAACC,iBAAiB,GAAG,IAAI,CAAC7N,YAAY,EAAE;IACrD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM8N,WAAW,GAAGD,iBAAiB,KAAK,WAAW,GAC/C,CAAC,IAAI,CAACE,uBAAuB,CAACF,iBAAiB,CAAC,EAAE,IAAI,CAACG,uBAAuB,CAACH,iBAAiB,CAAC,CAAC,GAClG,CAAC,IAAI,CAACG,uBAAuB,CAACH,iBAAiB,CAAC,EAAE,IAAI,CAACE,uBAAuB,CAACF,iBAAiB,CAAC,CAAC;IACxG,OAAOnY,qDAAC,CAAC,YAAY,EAAE,IAAI,EAAEoY,WAAW,CAAC;EAC7C;EACAE,uBAAuBA,CAACH,iBAAiB,EAAE;IACvC,OAAOA,iBAAiB,KAAK,WAAW,IAAIA,iBAAiB,KAAK,WAAW,GACvE,IAAI,CAACI,8BAA8B,CAAC,CAAC,GACrC,IAAI,CAACC,iCAAiC,CAACL,iBAAiB,CAAC;EACnE;EACAI,8BAA8BA,CAAA,EAAG;IAC7B,MAAM;MAAElL,YAAY;MAAE/E,QAAQ;MAAEkD,YAAY;MAAE5D,MAAM;MAAEV,QAAQ;MAAEC,QAAQ;MAAEW,UAAU;MAAE2Q;IAAc,CAAC,GAAG,IAAI;IAC5G,MAAMC,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD;AACR;AACA;AACA;IACQ,MAAMuL,cAAc,GAAG3U,oDAAc,CAACwH,YAAY,CAAC;IACnD,MAAMoN,SAAS,GAAGD,cAAc,CAACA,cAAc,CAACjG,MAAM,GAAG,CAAC,CAAC;IAC3D;AACR;AACA;IACQiG,cAAc,CAAC,CAAC,CAAC,CAACnR,GAAG,GAAG,CAAC;IACzBoR,SAAS,CAACpR,GAAG,GAAGtD,oDAAiB,CAAC0U,SAAS,CAACnN,KAAK,EAAEmN,SAAS,CAACxR,IAAI,CAAC;IAClE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+I,GAAG,GAAGjJ,QAAQ,KAAKO,SAAS,IAAI/F,oDAAO,CAACwF,QAAQ,EAAEyR,cAAc,CAAC,CAAC,CAAC,CAAC,GAAGzR,QAAQ,GAAGyR,cAAc,CAAC,CAAC,CAAC;IACzG,MAAMtI,GAAG,GAAGlJ,QAAQ,KAAKM,SAAS,IAAIhG,oDAAQ,CAAC0F,QAAQ,EAAEyR,SAAS,CAAC,GAAGzR,QAAQ,GAAGyR,SAAS;IAC1F,MAAMC,MAAM,GAAGzU,oDAAyB,CAACwD,MAAM,EAAEE,UAAU,EAAEqI,GAAG,EAAEE,GAAG,EAAE,IAAI,CAAC4E,eAAe,EAAE,IAAI,CAACH,iBAAiB,CAAC;IACpH,IAAIgE,KAAK,GAAGD,MAAM,CAACC,KAAK;IACxB,MAAM1Q,KAAK,GAAGyQ,MAAM,CAACzQ,KAAK;IAC1B,IAAIqQ,aAAa,EAAE;MACfK,KAAK,GAAGA,KAAK,CAAC9L,GAAG,CAAC,CAAC+L,UAAU,EAAEC,KAAK,KAAK;QACrC,MAAMC,cAAc,GAAG7Q,KAAK,CAAC4Q,KAAK,CAAC;QACnC,IAAI1Q,QAAQ;QACZ,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBA,QAAQ,GAAG,CAACmQ,aAAa,CAAC/U,oDAAgB,CAACuV,cAAc,CAAC,CAAC;QAC/D,CAAC,CACD,OAAOrZ,CAAC,EAAE;UACNP,qDAAa,CAAC,qHAAqH,EAAEO,CAAC,CAAC;QAC3I;QACA,OAAOiJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiQ,UAAU,CAAC,EAAE;UAAEzQ;QAAS,CAAC,CAAC;MACrE,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;IACQ,MAAM4Q,WAAW,GAAG1N,YAAY,CAAChE,GAAG,KAAK,IAAI,GACvC,GAAGgE,YAAY,CAACpE,IAAI,IAAIoE,YAAY,CAACC,KAAK,IAAID,YAAY,CAAChE,GAAG,EAAE,GAChE,GAAG6F,YAAY,CAACjG,IAAI,IAAIiG,YAAY,CAAC5B,KAAK,IAAI4B,YAAY,CAAC7F,GAAG,EAAE;IACtE,OAAQxH,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,eAAe;MAAE+X,KAAK,EAAE,aAAa;MAAEhM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAEmM,WAAW;MAAEC,WAAW,EAAG1K,EAAE,IAAK;QAC9J,MAAM;UAAE1B;QAAM,CAAC,GAAG0B,EAAE,CAAC2K,MAAM;QAC3B,MAAMC,QAAQ,GAAGjR,KAAK,CAACD,IAAI,CAAC,CAAC;UAAEsD,KAAK;UAAEjE,GAAG;UAAEJ;QAAK,CAAC,KAAK2F,KAAK,KAAK,GAAG3F,IAAI,IAAIqE,KAAK,IAAIjE,GAAG,EAAE,CAAC;QAC1F,IAAI,CAACmG,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE6N,QAAQ,CAAC,CAAC;QAC9E,IAAI,CAACzL,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAEW,QAAQ,CAAC,CAAC;QAC3E5K,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAEwB,KAAK,CAAC9L,GAAG,CAAEsM,IAAI,IAAMtZ,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAED,IAAI,CAACvM,KAAK,KAAKmM,WAAW,GAAG,GAAGM,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAE6J,IAAI,CAACvM,KAAK;MAAEzE,QAAQ,EAAEgR,IAAI,CAAChR,QAAQ;MAAEyE,KAAK,EAAEuM,IAAI,CAACvM;IAAM,CAAC,EAAEuM,IAAI,CAAC7Q,IAAI,CAAE,CAAC,CAAC;EACnP;EACA+P,iCAAiCA,CAACL,iBAAiB,EAAE;IACjD,MAAM;MAAE3M,YAAY;MAAEiN;IAAc,CAAC,GAAG,IAAI;IAC5C,MAAMiB,kBAAkB,GAAGvB,iBAAiB,KAAK,MAAM,IAAIA,iBAAiB,KAAK,MAAM;IACvF,MAAM5H,MAAM,GAAGmJ,kBAAkB,GAC3BpV,oDAAkB,CAAC,IAAI,CAACsD,MAAM,EAAE4D,YAAY,EAAE,IAAI,CAACtE,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC2N,iBAAiB,CAAC,GACnG,EAAE;IACR,MAAM6E,gBAAgB,GAAGxB,iBAAiB,KAAK,MAAM;IACrD,IAAIyB,IAAI,GAAGD,gBAAgB,GACrBnV,oDAAgB,CAAC,IAAI,CAACoD,MAAM,EAAE4D,YAAY,EAAE,IAAI,CAACtE,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC8N,eAAe,CAAC,GAC/F,EAAE;IACR,IAAIwD,aAAa,EAAE;MACfmB,IAAI,GAAGA,IAAI,CAAC5M,GAAG,CAAE6M,SAAS,IAAK;QAC3B,MAAM;UAAE9M;QAAM,CAAC,GAAG8M,SAAS;QAC3B,MAAMC,QAAQ,GAAG,OAAO/M,KAAK,KAAK,QAAQ,GAAGgN,QAAQ,CAAChN,KAAK,CAAC,GAAGA,KAAK;QACpE,MAAMkM,cAAc,GAAG;UACnBxN,KAAK,EAAED,YAAY,CAACC,KAAK;UACzBjE,GAAG,EAAEsS,QAAQ;UACb1S,IAAI,EAAEoE,YAAY,CAACpE;QACvB,CAAC;QACD,IAAIkB,QAAQ;QACZ,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBA,QAAQ,GAAG,CAACmQ,aAAa,CAAC/U,oDAAgB,CAACuV,cAAc,CAAC,CAAC;QAC/D,CAAC,CACD,OAAOrZ,CAAC,EAAE;UACNP,qDAAa,CAAC,qHAAqH,EAAEO,CAAC,CAAC;QAC3I;QACA,OAAOiJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+Q,SAAS,CAAC,EAAE;UAAEvR;QAAS,CAAC,CAAC;MACpE,CAAC,CAAC;IACN;IACA,MAAM0R,iBAAiB,GAAG7B,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,MAAM;IACvF,MAAM8B,KAAK,GAAGD,iBAAiB,GACzBtV,oDAAiB,CAAC,IAAI,CAACkD,MAAM,EAAE,IAAI,CAACyF,YAAY,EAAE,IAAI,CAACnG,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACwN,gBAAgB,CAAC,GACtG,EAAE;IACR;AACR;AACA;IACQ,MAAMuF,cAAc,GAAGtV,oDAAkB,CAAC,IAAI,CAACgD,MAAM,EAAE;MAAE6D,KAAK,EAAE,SAAS;MAAEjE,GAAG,EAAE;IAAU,CAAC,CAAC;IAC5F,IAAI4Q,WAAW,GAAG,EAAE;IACpB,IAAI8B,cAAc,EAAE;MAChB9B,WAAW,GAAG,CACV,IAAI,CAAC+B,uBAAuB,CAAC5J,MAAM,CAAC,EACpC,IAAI,CAAC6J,qBAAqB,CAACR,IAAI,CAAC,EAChC,IAAI,CAACS,sBAAsB,CAACJ,KAAK,CAAC,CACrC;IACL,CAAC,MACI;MACD7B,WAAW,GAAG,CACV,IAAI,CAACgC,qBAAqB,CAACR,IAAI,CAAC,EAChC,IAAI,CAACO,uBAAuB,CAAC5J,MAAM,CAAC,EACpC,IAAI,CAAC8J,sBAAsB,CAACJ,KAAK,CAAC,CACrC;IACL;IACA,OAAO7B,WAAW;EACtB;EACAgC,qBAAqBA,CAACR,IAAI,EAAE;IACxB,IAAI9P,EAAE;IACN,IAAI8P,IAAI,CAAClH,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,EAAE;IACb;IACA,MAAM;MAAEpK,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMkN,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,MAAMkN,iBAAiB,GAAG,CAACxQ,EAAE,GAAI0B,YAAY,CAAChE,GAAG,KAAK,IAAI,GAAGgE,YAAY,CAAChE,GAAG,GAAG,IAAI,CAAC6F,YAAY,CAAC7F,GAAI,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGrC,SAAS;IAClJ,OAAQzH,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,cAAc;MAAE+X,KAAK,EAAE,YAAY;MAAEhM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAEuN,iBAAiB;MAAEnB,WAAW,EAAG1K,EAAE,IAAK;QAClK,IAAI,CAACd,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEhE,GAAG,EAAEiH,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC9F,IAAI,CAACa,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAE;UAAElR,GAAG,EAAEiH,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC3F0B,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAEsC,IAAI,CAAC5M,GAAG,CAAExF,GAAG,IAAMxH,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAE/R,GAAG,CAACuF,KAAK,KAAKuN,iBAAiB,GAAG,GAAGd,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAEjI,GAAG,CAACuF,KAAK;MAAEzE,QAAQ,EAAEd,GAAG,CAACc,QAAQ;MAAEyE,KAAK,EAAEvF,GAAG,CAACuF;IAAM,CAAC,EAAEvF,GAAG,CAACiB,IAAI,CAAE,CAAC,CAAC;EAClP;EACA0R,uBAAuBA,CAAC5J,MAAM,EAAE;IAC5B,IAAIA,MAAM,CAACmC,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,EAAE;IACb;IACA,MAAM;MAAEpK,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMkN,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAQpN,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,gBAAgB;MAAE+X,KAAK,EAAE,cAAc;MAAEhM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAEvB,YAAY,CAACC,KAAK;MAAE0N,WAAW,EAAG1K,EAAE,IAAK;QACvK,IAAI,CAACd,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEC,KAAK,EAAEgD,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAChG,IAAI,CAACa,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAE;UAAEjN,KAAK,EAAEgD,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC7F0B,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAE/G,MAAM,CAACvD,GAAG,CAAEvB,KAAK,IAAMzL,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAE9N,KAAK,CAACsB,KAAK,KAAKvB,YAAY,CAACC,KAAK,GAAG,GAAG+N,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAEhE,KAAK,CAACsB,KAAK;MAAEzE,QAAQ,EAAEmD,KAAK,CAACnD,QAAQ;MAAEyE,KAAK,EAAEtB,KAAK,CAACsB;IAAM,CAAC,EAAEtB,KAAK,CAAChD,IAAI,CAAE,CAAC,CAAC;EACjQ;EACA4R,sBAAsBA,CAACJ,KAAK,EAAE;IAC1B,IAAIA,KAAK,CAACvH,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO,EAAE;IACb;IACA,MAAM;MAAEpK,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMkN,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAQpN,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,eAAe;MAAE+X,KAAK,EAAE,aAAa;MAAEhM,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAEvB,YAAY,CAACpE,IAAI;MAAE+R,WAAW,EAAG1K,EAAE,IAAK;QACpK,IAAI,CAACd,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEpE,IAAI,EAAEqH,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC/F,IAAI,CAACa,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAE;UAAEtR,IAAI,EAAEqH,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC5F0B,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAE2C,KAAK,CAACjN,GAAG,CAAE5F,IAAI,IAAMpH,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAEnS,IAAI,CAAC2F,KAAK,KAAKvB,YAAY,CAACpE,IAAI,GAAG,GAAGoS,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAErI,IAAI,CAAC2F,KAAK;MAAEzE,QAAQ,EAAElB,IAAI,CAACkB,QAAQ;MAAEyE,KAAK,EAAE3F,IAAI,CAAC2F;IAAM,CAAC,EAAE3F,IAAI,CAACqB,IAAI,CAAE,CAAC,CAAC;EACzP;EACA4P,uBAAuBA,CAACF,iBAAiB,EAAE;IACvC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,CAAC,CAACzQ,QAAQ,CAACyQ,iBAAiB,CAAC,EAAE;MACrE,OAAO,EAAE;IACb;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMO,UAAU,GAAG,IAAI,CAACpL,aAAa,CAAC,CAAC;IACvC,MAAMiN,mBAAmB,GAAG7B,UAAU,KAAKjR,SAAS;IACpD,MAAM;MAAE+S,SAAS;MAAEC,WAAW;MAAEC;IAAc,CAAC,GAAG5V,oDAAkB,CAAC,IAAI,CAAC8C,MAAM,EAAE,IAAI,CAAC4D,YAAY,EAAE,IAAI,CAACmP,SAAS,EAAEJ,mBAAmB,GAAG,IAAI,CAACrT,QAAQ,GAAGO,SAAS,EAAE8S,mBAAmB,GAAG,IAAI,CAACpT,QAAQ,GAAGM,SAAS,EAAE,IAAI,CAAC0N,gBAAgB,EAAE,IAAI,CAACG,kBAAkB,CAAC;IACtQ,OAAO,CACH,IAAI,CAACsF,sBAAsB,CAACJ,SAAS,CAAC,EACtC,IAAI,CAACK,wBAAwB,CAACJ,WAAW,CAAC,EAC1C,IAAI,CAACK,2BAA2B,CAACJ,aAAa,CAAC,CAClD;EACL;EACAE,sBAAsBA,CAACJ,SAAS,EAAE;IAC9B,MAAM;MAAElS,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,IAAIgP,SAAS,CAAC9H,MAAM,KAAK,CAAC,EACtB,OAAO,EAAE;IACb,MAAMgG,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAQpN,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,gBAAgB;MAAE+L,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAE2L,UAAU,CAAChN,IAAI;MAAEqP,YAAY,EAAE,IAAI;MAAE5B,WAAW,EAAG1K,EAAE,IAAK;QACjK,IAAI,CAACd,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEE,IAAI,EAAE+C,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QAC/F,IAAI,CAACa,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsE,0BAA0B,CAAC,CAAC,CAAC,EAAE;UAAE1B,IAAI,EAAE+C,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QACnH0B,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAEkD,SAAS,CAACxN,GAAG,CAAEtB,IAAI,IAAM1L,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAE7N,IAAI,CAACqB,KAAK,KAAK2L,UAAU,CAAChN,IAAI,GAAG,GAAG8N,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAE/D,IAAI,CAACqB,KAAK;MAAEzE,QAAQ,EAAEoD,IAAI,CAACpD,QAAQ;MAAEyE,KAAK,EAAErB,IAAI,CAACqB;IAAM,CAAC,EAAErB,IAAI,CAACjD,IAAI,CAAE,CAAC,CAAC;EAC3P;EACAoS,wBAAwBA,CAACJ,WAAW,EAAE;IAClC,MAAM;MAAEnS,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,IAAIiP,WAAW,CAAC/H,MAAM,KAAK,CAAC,EACxB,OAAO,EAAE;IACb,MAAMgG,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAQpN,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,iBAAiB;MAAE+L,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAE2L,UAAU,CAAC/M,MAAM;MAAEoP,YAAY,EAAE,IAAI;MAAE5B,WAAW,EAAG1K,EAAE,IAAK;QACpK,IAAI,CAACd,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEG,MAAM,EAAE8C,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QACjG,IAAI,CAACa,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsE,0BAA0B,CAAC,CAAC,CAAC,EAAE;UAAEzB,MAAM,EAAE8C,EAAE,CAAC2K,MAAM,CAACrM;QAAM,CAAC,CAAC,CAAC;QACrH0B,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAEmD,WAAW,CAACzN,GAAG,CAAErB,MAAM,IAAM3L,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAE5N,MAAM,CAACoB,KAAK,KAAK2L,UAAU,CAAC/M,MAAM,GAAG,GAAG6N,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAE9D,MAAM,CAACoB,KAAK;MAAEzE,QAAQ,EAAEqD,MAAM,CAACrD,QAAQ;MAAEyE,KAAK,EAAEpB,MAAM,CAACoB;IAAM,CAAC,EAAEpB,MAAM,CAAClD,IAAI,CAAE,CAAC,CAAC;EAC3Q;EACAqS,2BAA2BA,CAACJ,aAAa,EAAE;IACvC,MAAM;MAAEpS,QAAQ;MAAEkD;IAAa,CAAC,GAAG,IAAI;IACvC,IAAIkP,aAAa,CAAChI,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,MAAMgG,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,MAAM4N,cAAc,GAAGhW,oDAAoB,CAAC,IAAI,CAAC4C,MAAM,CAAC;IACxD,OAAQ5H,qDAAC,CAAC,mBAAmB,EAAE;MAAE,YAAY,EAAE,qBAAqB;MAAE2R,KAAK,EAAEqJ,cAAc,GAAG;QAAEC,KAAK,EAAE;MAAK,CAAC,GAAG,CAAC,CAAC;MAAElP,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEzD,QAAQ,EAAEA,QAAQ;MAAEyE,KAAK,EAAE2L,UAAU,CAAC9M,IAAI;MAAEuN,WAAW,EAAG1K,EAAE,IAAK;QAChM,MAAM/C,IAAI,GAAGxG,oDAAqB,CAACsG,YAAY,EAAEiD,EAAE,CAAC2K,MAAM,CAACrM,KAAK,CAAC;QACjE,IAAI,CAACY,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE;UAAEI,IAAI,EAAE6C,EAAE,CAAC2K,MAAM,CAACrM,KAAK;UAAErB;QAAK,CAAC,CAAC,CAAC;QACrG,IAAI,CAACkC,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsE,0BAA0B,CAAC,CAAC,CAAC,EAAE;UAAExB,IAAI,EAAE6C,EAAE,CAAC2K,MAAM,CAACrM,KAAK;UAAErB;QAAK,CAAC,CAAC,CAAC;QACzH+C,EAAE,CAAC6I,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,EAAEoD,aAAa,CAAC1N,GAAG,CAAEkO,SAAS,IAAMlb,qDAAC,CAAC,0BAA0B,EAAE;MAAEuZ,IAAI,EAAE2B,SAAS,CAACnO,KAAK,KAAK2L,UAAU,CAAC9M,IAAI,GAAG,GAAG4N,eAAe,IAAIC,sBAAsB,EAAE,GAAGD,eAAe;MAAE/J,GAAG,EAAEyL,SAAS,CAACnO,KAAK;MAAEzE,QAAQ,EAAE4S,SAAS,CAAC5S,QAAQ;MAAEyE,KAAK,EAAEmO,SAAS,CAACnO;IAAM,CAAC,EAAEmO,SAAS,CAACzS,IAAI,CAAE,CAAC,CAAC;EAC7R;EACA0S,eAAeA,CAAChD,iBAAiB,EAAE;IAC/B,MAAM;MAAEvQ;IAAO,CAAC,GAAG,IAAI;IACvB,MAAMsS,cAAc,GAAGtV,oDAAkB,CAACgD,MAAM,CAAC;IACjD,MAAMwT,WAAW,GAAGlB,cAAc,GAAG,aAAa,GAAG,YAAY;IACjE,OAAQla,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;QAClB,CAAC,eAAeqD,WAAW,EAAE,GAAG;MACpC;IAAE,CAAC,EAAE,IAAI,CAAClD,iBAAiB,CAACC,iBAAiB,CAAC,CAAC;EACvD;EACA;AACJ;AACA;EACIkD,oBAAoBA,CAACzK,IAAI,EAAE;IACvB,MAAM;MAAEtI;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMgT,YAAY,GAAG1K,IAAI,KAAK,KAAK,GAAG1P,iDAAW,GAAGC,iDAAY;IAChE,MAAMoa,aAAa,GAAG3K,IAAI,KAAK,KAAK,GAAGvP,iDAAc,GAAGE,iDAAc;IACtE,MAAMia,iBAAiB,GAAGlT,QAAQ,IAAIK,mBAAmB,CAAC,IAAI,CAAC6C,YAAY,EAAE,IAAI,CAACtE,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC;IAC1G,MAAMsU,iBAAiB,GAAGnT,QAAQ,IAAIS,mBAAmB,CAAC,IAAI,CAACyC,YAAY,EAAE,IAAI,CAACrE,QAAQ,CAAC;IAC3F;IACA,MAAMuU,OAAO,GAAG,IAAI,CAACtS,EAAE,CAACuS,YAAY,CAAC,KAAK,CAAC,IAAIlU,SAAS;IACxD,OAAQzH,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAkB,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAA0B,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAsB,CAAC,EAAE/X,qDAAC,CAAC,QAAQ,EAAE;MAAE+X,KAAK,EAAE;QACtJ,4BAA4B,EAAE,IAAI;QAClC,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;MAAEwB,IAAI,EAAE,mBAAmB;MAAEjR,QAAQ,EAAEA,QAAQ;MAAE,YAAY,EAAE,IAAI,CAACiD,gBAAgB,GAAG,kBAAkB,GAAG,kBAAkB;MAAE0M,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC/D,sBAAsB,CAAC;IAAE,CAAC,EAAElU,qDAAC,CAAC,MAAM,EAAE;MAAEgY,EAAE,EAAE;IAAiB,CAAC,EAAE1S,oDAAe,CAAC,IAAI,CAACsC,MAAM,EAAE,IAAI,CAAC4D,YAAY,CAAC,EAAExL,qDAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAE4b,IAAI,EAAE,IAAI,CAACrQ,gBAAgB,GAAG+P,YAAY,GAAGC,aAAa;MAAEM,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,EAAElL,IAAI,KAAK,IAAI,IAAI5Q,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAqB,CAAC,EAAE/X,qDAAC,CAAC,aAAa,EAAE,IAAI,EAAEA,qDAAC,CAAC,YAAY,EAAE;MAAE,YAAY,EAAE,gBAAgB;MAAEsI,QAAQ,EAAEkT,iBAAiB;MAAEvD,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACrP,SAAS,CAAC;IAAE,CAAC,EAAE5I,qDAAC,CAAC,UAAU,EAAE;MAAE+b,GAAG,EAAEL,OAAO;MAAE,aAAa,EAAE,MAAM;MAAEM,IAAI,EAAE,WAAW;MAAEJ,IAAI,EAAEpa,iDAAW;MAAEqa,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE9b,qDAAC,CAAC,YAAY,EAAE;MAAE,YAAY,EAAE,YAAY;MAAEsI,QAAQ,EAAEmT,iBAAiB;MAAExD,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACjP,SAAS,CAAC;IAAE,CAAC,EAAEhJ,qDAAC,CAAC,UAAU,EAAE;MAAE+b,GAAG,EAAEL,OAAO;MAAE,aAAa,EAAE,MAAM;MAAEM,IAAI,EAAE,WAAW;MAAEJ,IAAI,EAAEva,iDAAc;MAAEwa,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE9b,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE,uBAAuB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE3S,oDAAa,CAAC,IAAI,CAACwC,MAAM,EAAEgJ,IAAI,EAAE,IAAI,CAACtE,cAAc,GAAG,CAAC,CAAC,CAACU,GAAG,CAAEtN,CAAC,IAAK;MAChmC,OAAOM,qDAAC,CAAC,KAAK,EAAE;QAAE+X,KAAK,EAAE;MAAc,CAAC,EAAErY,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CAAC;EACR;EACAuc,WAAWA,CAACxQ,KAAK,EAAErE,IAAI,EAAE;IACrB,MAAM;MAAEkB,QAAQ;MAAE2D;IAAS,CAAC,GAAG,IAAI;IACnC,MAAMiQ,WAAW,GAAG,IAAI,CAACvH,gBAAgB,KAAKlN,SAAS,IAAI,IAAI,CAACkN,gBAAgB,CAACjN,QAAQ,CAACN,IAAI,CAAC;IAC/F,MAAM+U,YAAY,GAAG,IAAI,CAACrH,iBAAiB,KAAKrN,SAAS,IAAI,IAAI,CAACqN,iBAAiB,CAACpN,QAAQ,CAAC+D,KAAK,CAAC;IACnG,MAAM2Q,kBAAkB,GAAG,CAACF,WAAW,IAAI,CAACC,YAAY;IACxD,MAAME,kBAAkB,GAAG/T,QAAQ,IAAI2D,QAAQ;IAC/C,MAAMqQ,aAAa,GAAGhU,QAAQ,IAC1BI,eAAe,CAAC;MACZ+C,KAAK;MACLrE,IAAI;MACJI,GAAG,EAAE;IACT,CAAC,EAAE;MACC;MACA;MACA;MACAN,QAAQ,EAAE2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5B,QAAQ,CAAC,EAAE;QAAEM,GAAG,EAAE;MAAK,CAAC,CAAC;MACxEL,QAAQ,EAAE0B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3B,QAAQ,CAAC,EAAE;QAAEK,GAAG,EAAE;MAAK,CAAC;IAC3E,CAAC,CAAC;IACN;IACA;IACA;IACA,MAAM+U,cAAc,GAAG,IAAI,CAAC/Q,YAAY,CAACC,KAAK,KAAKA,KAAK,IAAI,IAAI,CAACD,YAAY,CAACpE,IAAI,KAAKA,IAAI;IAC3F,MAAMsR,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAQpN,qDAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,CAACuc,cAAc,GAAG,MAAM,GAAG,IAAI;MAAExE,KAAK,EAAE;QAClE,gBAAgB,EAAE,IAAI;QACtB;QACA,yBAAyB,EAAE,CAACwE,cAAc,IAAID;MAClD;IAAE,CAAC,EAAEtc,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAsB,CAAC,EAAEvS,oDAAc,CAACiG,KAAK,EAAErE,IAAI,EAAE,IAAI,CAACkF,cAAc,GAAG,CAAC,EAAE,IAAI,CAACJ,gBAAgB,CAAC,CAACc,GAAG,CAAC,CAACwP,UAAU,EAAExD,KAAK,KAAK;MACvJ,MAAM;QAAExR,GAAG;QAAEiV,SAAS;QAAE5Q;MAAc,CAAC,GAAG2Q,UAAU;MACpD,MAAM;QAAEpT,EAAE;QAAEF,gBAAgB;QAAEuP,aAAa;QAAElM,QAAQ;QAAEL;MAAiB,CAAC,GAAG,IAAI;MAChF,IAAIwQ,MAAM,GAAGjR,KAAK;MAClB,IAAIkR,KAAK,GAAGvV,IAAI;MAChB,IAAI8E,gBAAgB,IAAIL,aAAa,IAAIrE,GAAG,KAAK,IAAI,EAAE;QACnD,IAAIA,GAAG,GAAG,EAAE,EAAE;UACV;UACA;UACA,IAAIiE,KAAK,KAAK,CAAC,EAAE;YACbkR,KAAK,GAAGvV,IAAI,GAAG,CAAC;YAChBsV,MAAM,GAAG,EAAE;UACf,CAAC,MACI;YACDA,MAAM,GAAGjR,KAAK,GAAG,CAAC;UACtB;QACJ,CAAC,MACI,IAAIjE,GAAG,GAAG,EAAE,EAAE;UACf;UACA;UACA,IAAIiE,KAAK,KAAK,EAAE,EAAE;YACdkR,KAAK,GAAGvV,IAAI,GAAG,CAAC;YAChBsV,MAAM,GAAG,CAAC;UACd,CAAC,MACI;YACDA,MAAM,GAAGjR,KAAK,GAAG,CAAC;UACtB;QACJ;MACJ;MACA,MAAMwN,cAAc,GAAG;QAAExN,KAAK,EAAEiR,MAAM;QAAElV,GAAG;QAAEJ,IAAI,EAAEuV,KAAK;QAAE9Q;MAAc,CAAC;MACzE,MAAM+Q,iBAAiB,GAAGpV,GAAG,KAAK,IAAI;MACtC,MAAM;QAAEU,QAAQ;QAAEG,OAAO;QAAEG,SAAS;QAAED,YAAY;QAAED,QAAQ,EAAEjB,aAAa;QAAEoB;MAAM,CAAC,GAAGd,mBAAmB,CAAC,IAAI,CAACC,MAAM,EAAEqR,cAAc,EAAE,IAAI,CAACpR,WAAW,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC8N,eAAe,CAAC;MAC9N,MAAM9L,aAAa,GAAGzF,oDAAgB,CAACuV,cAAc,CAAC;MACtD,IAAI4D,gBAAgB,GAAGT,kBAAkB,IAAI/U,aAAa;MAC1D,IAAI,CAACwV,gBAAgB,IAAIpE,aAAa,KAAKhR,SAAS,EAAE;QAClD,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBoV,gBAAgB,GAAG,CAACpE,aAAa,CAACtP,aAAa,CAAC;QACpD,CAAC,CACD,OAAOvJ,CAAC,EAAE;UACNP,qDAAa,CAAC,qHAAqH,EAAE+J,EAAE,EAAExJ,CAAC,CAAC;QAC/I;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMkd,mBAAmB,GAAGD,gBAAgB,IAAIR,kBAAkB;MAClE,MAAMxE,gBAAgB,GAAGgF,gBAAgB,IAAIR,kBAAkB;MAC/D,IAAIU,SAAS,GAAGtV,SAAS;MACzB;AACZ;AACA;AACA;MACY,IAAIyB,gBAAgB,KAAKzB,SAAS,IAAI,CAACS,QAAQ,IAAIV,GAAG,KAAK,IAAI,IAAI,CAACqE,aAAa,EAAE;QAC/EkR,SAAS,GAAG9T,kBAAkB,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,EAAE,CAAC;MACvE;MACA,IAAI4T,SAAS,GAAGvV,SAAS;MACzB;MACA;MACA,IAAI,CAACmV,iBAAiB,IAAI,CAAC/Q,aAAa,EAAE;QACtCmR,SAAS,GAAG,eAAe9U,QAAQ,GAAG,SAAS,GAAG,EAAE,GAAGG,OAAO,GAAG,QAAQ,GAAG,EAAE,GAAGwU,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAE;MAC1H,CAAC,MACI,IAAIhR,aAAa,EAAE;QACpBmR,SAAS,GAAG,eAAeH,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAE;MACpE;MACA,OAAQ7c,qDAAC,CAAC,KAAK,EAAE;QAAE+X,KAAK,EAAE;MAAuB,CAAC,EAAE/X,qDAAC,CAAC,QAAQ,EAAE;QAC5D;QACA;QACA;QACA;QACA;QACA;QACAid,GAAG,EAAG7T,EAAE,IAAK;UACT,IAAIA,EAAE,EAAE;YACJA,EAAE,CAACuI,KAAK,CAACI,WAAW,CAAC,OAAO,EAAE,GAAGgL,SAAS,GAAGA,SAAS,CAACrT,SAAS,GAAG,EAAE,EAAE,EAAE,WAAW,CAAC;YACrFN,EAAE,CAACuI,KAAK,CAACI,WAAW,CAAC,kBAAkB,EAAE,GAAGgL,SAAS,GAAGA,SAAS,CAACpT,eAAe,GAAG,EAAE,EAAE,EAAE,WAAW,CAAC;UAC1G;QACJ,CAAC;QAAEuT,QAAQ,EAAE,IAAI;QAAE,UAAU,EAAE1V,GAAG;QAAE,YAAY,EAAEkV,MAAM;QAAE,WAAW,EAAEC,KAAK;QAAE,YAAY,EAAE3D,KAAK;QAAE,kBAAkB,EAAEyD,SAAS;QAAEnU,QAAQ,EAAEuP,gBAAgB;QAAEE,KAAK,EAAE;UACjK,sBAAsB,EAAE6E,iBAAiB;UACzC,cAAc,EAAE,IAAI;UACpB,qBAAqB,EAAE1U,QAAQ;UAC/B,0BAA0B,EAAE4U,mBAAmB;UAC/C,oBAAoB,EAAEzU,OAAO;UAC7B,2BAA2B,EAAEwD;QACjC,CAAC;QAAE0N,IAAI,EAAEyD,SAAS;QAAE,aAAa,EAAEJ,iBAAiB,GAAG,MAAM,GAAG,IAAI;QAAE,eAAe,EAAErU,YAAY;QAAE,YAAY,EAAEC,SAAS;QAAEyP,OAAO,EAAEA,CAAA,KAAM;UACzI,IAAI2E,iBAAiB,EAAE;YACnB;UACJ;UACA,IAAI/Q,aAAa,EAAE;YACf;YACA,IAAI,CAACzC,EAAE,CAAC+T,IAAI,CAAC,CAAC;YACd,IAAI,CAACtV,WAAW,GAAGgB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAEO,cAAc,CAAC;YAC/E,IAAI,CAAChG,aAAa,CAACgG,cAAc,CAAC;YAClC,IAAI,CAAC/K,OAAO,CAAC,CAAC;UAClB,CAAC,MACI;YACD,IAAI,CAACP,eAAe,CAAC9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0C,YAAY,CAAC,EAAEyN,cAAc,CAAC,CAAC;YACzF;YACA,IAAI1M,QAAQ,EAAE;cACV,IAAI,CAACqB,cAAc,CAACqL,cAAc,EAAE/Q,QAAQ,CAAC;YACjD,CAAC,MACI;cACD,IAAI,CAAC0F,cAAc,CAAC/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4P,UAAU,CAAC,EAAEO,cAAc,CAAC,CAAC;YACrF;UACJ;QACJ;MACJ,CAAC,EAAExQ,IAAI,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC;EACR;EACA2U,kBAAkBA,CAAA,EAAG;IACjB,OAAQpd,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE,6BAA6B;MAAEkF,GAAG,EAAG7T,EAAE,IAAM,IAAI,CAACgF,eAAe,GAAGhF,EAAG;MAAE8T,QAAQ,EAAE;IAAI,CAAC,EAAElZ,oDAAc,CAAC,IAAI,CAACwH,YAAY,EAAE,IAAI,CAACiG,eAAe,CAAC,CAACzE,GAAG,CAAC,CAAC;MAAEvB,KAAK;MAAErE;IAAK,CAAC,KAAK;MACjM,OAAO,IAAI,CAAC6U,WAAW,CAACxQ,KAAK,EAAErE,IAAI,CAAC;IACxC,CAAC,CAAC,CAAC;EACP;EACAiW,cAAcA,CAACzM,IAAI,EAAE;IACjB,OAAQ5Q,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE,mBAAmB;MAAEtI,GAAG,EAAE;IAAoB,CAAC,EAAE,IAAI,CAAC4L,oBAAoB,CAACzK,IAAI,CAAC,EAAE,IAAI,CAACwM,kBAAkB,CAAC,CAAC,CAAC;EAC1I;EACAE,eAAeA,CAAA,EAAG;IACd,MAAMC,mBAAmB,GAAG,IAAI,CAACnU,EAAE,CAAC6E,aAAa,CAAC,qBAAqB,CAAC,KAAK,IAAI;IACjF,IAAI,CAACsP,mBAAmB,IAAI,CAAC,IAAI,CAAC5Q,oBAAoB,EAAE;MACpD;IACJ;IACA,OAAO3M,qDAAC,CAAC,MAAM,EAAE;MAAEgM,IAAI,EAAE;IAAa,CAAC,EAAE,MAAM,CAAC;EACpD;EACAwR,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAChB,MAAM;MAAEnV,QAAQ;MAAEqS,SAAS;MAAE7O,iBAAiB;MAAElE,MAAM;MAAEiC;IAAc,CAAC,GAAG,IAAI;IAC9E,MAAM6T,iBAAiB,GAAGhY,oDAAY,CAACkC,MAAM,EAAE+S,SAAS,CAAC;IACzD,MAAMjC,UAAU,GAAG,IAAI,CAACtL,0BAA0B,CAAC,CAAC;IACpD,OAAO,CACHpN,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAc,CAAC,EAAE,IAAI,CAACuF,eAAe,CAAC,CAAC,CAAC,EAC1Dtd,qDAAC,CAAC,QAAQ,EAAE;MAAE+X,KAAK,EAAE;QACb,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAEjM;MACxB,CAAC;MAAEyN,IAAI,EAAE,cAAczN,iBAAiB,GAAG,SAAS,GAAG,EAAE,EAAE;MAAE,eAAe,EAAE,OAAO;MAAE,eAAe,EAAE,MAAM;MAAExD,QAAQ,EAAEA,QAAQ;MAAE2P,OAAO;QAAA,IAAA0F,KAAA,GAAAxK,yMAAA,CAAE,WAAO1E,EAAE,EAAK;UACvJ,MAAM;YAAEmP;UAAW,CAAC,GAAGH,MAAI;UAC3B,IAAIG,UAAU,EAAE;YACZH,MAAI,CAAC3R,iBAAiB,GAAG,IAAI;YAC7B8R,UAAU,CAACpX,OAAO,CAAC,IAAIqX,WAAW,CAAC,iBAAiB,EAAE;cAClDzE,MAAM,EAAE;gBACJ0E,eAAe,EAAErP,EAAE,CAACsP;cACxB;YACJ,CAAC,CAAC,CAAC;YACH,MAAMH,UAAU,CAACI,aAAa,CAAC,CAAC;YAChCP,MAAI,CAAC3R,iBAAiB,GAAG,KAAK;UAClC;QACJ,CAAC;QAAA,gBAZmImM,OAAOA,CAAAgG,GAAA;UAAA,OAAAN,KAAA,CAAAlK,KAAA,OAAAC,SAAA;QAAA;MAAA;IAYzI,CAAC,EAAE9N,oDAAgB,CAACgC,MAAM,EAAE8Q,UAAU,EAAEgF,iBAAiB,EAAE7T,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,CAAC,CAAC,EACnJpK,qDAAC,CAAC,aAAa,EAAE;MAAEke,SAAS,EAAE,QAAQ;MAAEC,WAAW,EAAE,IAAI;MAAEC,YAAY,EAAE,CAAC;MAAEC,KAAK,EAAE,KAAK;MAAEC,aAAa,EAAG7P,EAAE,IAAK;QACzG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;QACoB,MAAM8P,IAAI,GAAG9P,EAAE,CAACsP,MAAM,CAACvN,gBAAgB,CAAC,mBAAmB,CAAC;QAC5D;QACA+N,IAAI,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,wBAAwB,CAAC,CAAC,CAAC;MACzD,CAAC;MAAE/M,KAAK,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,aAAa,EAAE;MACnB,CAAC;MACD;MACA;MACAgN,cAAc,EAAE,IAAI;MAAE1B,GAAG,EAAG7T,EAAE,IAAM,IAAI,CAACwU,UAAU,GAAGxU;IAAI,CAAC,EAAE,IAAI,CAAC8O,iBAAiB,CAAC,MAAM,CAAC,CAAC,CACnG;EACL;EACA0G,yBAAyBA,CAAA,EAAG;IACxB,IAAI9U,EAAE;IACN,MAAM;MAAEjC,WAAW;MAAEgC,aAAa;MAAE0C,QAAQ;MAAEsS;IAA4B,CAAC,GAAG,IAAI;IAClF,MAAM5W,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC;IAC1C,IAAIiX,UAAU;IACd,IAAIvS,QAAQ,IAAItE,OAAO,IAAIJ,WAAW,CAAC6K,MAAM,KAAK,CAAC,EAAE;MACjDoM,UAAU,GAAG,GAAGjX,WAAW,CAAC6K,MAAM,OAAO,CAAC,CAAC;MAC3C,IAAImM,2BAA2B,KAAKpX,SAAS,EAAE;QAC3C,IAAI;UACAqX,UAAU,GAAGD,2BAA2B,CAACnb,oDAAgB,CAACmE,WAAW,CAAC,CAAC;QAC3E,CAAC,CACD,OAAOjI,CAAC,EAAE;UACNP,qDAAa,CAAC,uEAAuE,EAAEO,CAAC,CAAC;QAC7F;MACJ;IACJ,CAAC,MACI;MACD;MACAkf,UAAU,GAAGhZ,oDAAoB,CAAC,IAAI,CAAC8B,MAAM,EAAE,IAAI,CAACwF,0BAA0B,CAAC,CAAC,EAAE,CAACtD,EAAE,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;QAAEiV,OAAO,EAAE,OAAO;QAAEtT,KAAK,EAAE,OAAO;QAAEjE,GAAG,EAAE;MAAU,CAAC,CAAC;IACpQ;IACA,OAAOsX,UAAU;EACrB;EACAE,YAAYA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAAC9V,EAAE,CAAC6E,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;IACxE,IAAI,CAACiR,eAAe,IAAI,CAAC,IAAI,CAAC1S,gBAAgB,EAAE;MAC5C;IACJ;IACA,OAAQxM,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAkB,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAiB,CAAC,EAAE/X,qDAAC,CAAC,MAAM,EAAE;MAAEgM,IAAI,EAAE;IAAQ,CAAC,EAAE,aAAa,CAAC,CAAC,EAAEiT,kBAAkB,IAAIjf,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAyB,CAAC,EAAE,IAAI,CAAC6G,yBAAyB,CAAC,CAAC,CAAC,CAAC;EAC7O;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIO,UAAUA,CAAA,EAAG;IACT,MAAM;MAAE7U;IAAa,CAAC,GAAG,IAAI;IAC7B,MAAM8U,oBAAoB,GAAG9U,YAAY,KAAK,MAAM;IACpD,OAAQtK,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAgB,CAAC,EAAEqH,oBAAoB,GAAG,IAAI,CAAClH,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACsF,iBAAiB,CAAC,CAAC,CAAC;EAC5H;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,iCAAiCA,CAAA,EAAG;IAChC,OAAOrf,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE;IAAgB,CAAC,EAAE,IAAI,CAACoD,eAAe,CAAC,YAAY,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;EACImE,cAAcA,CAAC1O,IAAI,EAAE;IACjB,MAAM;MAAEtG,YAAY;MAAEuC;IAAY,CAAC,GAAG,IAAI;IAC1C;AACR;AACA;AACA;IACQ,MAAM0S,eAAe,GAAGjV,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IAC/G,IAAIuC,WAAW,IAAI0S,eAAe,EAAE;MAChC,OAAO,CAAC,IAAI,CAACP,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC7D,eAAe,CAAC,CAAC,EAAE,IAAI,CAACvD,YAAY,CAAC,CAAC,CAAC;IAClF;IACA,QAAQtN,YAAY;MAChB,KAAK,WAAW;QACZ,OAAO,CACH,IAAI,CAAC0U,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC3B,cAAc,CAACzM,IAAI,CAAC,EACzB,IAAI,CAACyO,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACF,UAAU,CAAC,CAAC,EACjB,IAAI,CAACvH,YAAY,CAAC,CAAC,CACtB;MACL,KAAK,WAAW;QACZ,OAAO,CACH,IAAI,CAACoH,YAAY,CAAC,CAAC,EACnB,IAAI,CAACG,UAAU,CAAC,CAAC,EACjB,IAAI,CAAC9B,cAAc,CAACzM,IAAI,CAAC,EACzB,IAAI,CAACyO,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACzH,YAAY,CAAC,CAAC,CACtB;MACL,KAAK,MAAM;QACP,OAAO,CAAC,IAAI,CAACoH,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE,IAAI,CAACvH,YAAY,CAAC,CAAC,CAAC;MAC7E,KAAK,OAAO;MACZ,KAAK,YAAY;MACjB,KAAK,MAAM;QACP,OAAO,CAAC,IAAI,CAACoH,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC7D,eAAe,CAAC,CAAC,EAAE,IAAI,CAACvD,YAAY,CAAC,CAAC,CAAC;MAClF;QACI,OAAO,CACH,IAAI,CAACoH,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC3B,cAAc,CAACzM,IAAI,CAAC,EACzB,IAAI,CAACyO,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACzH,YAAY,CAAC,CAAC,CACtB;IACT;EACJ;EACA4H,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExT,IAAI;MAAEe,KAAK;MAAEzE,QAAQ;MAAEc,EAAE;MAAE2C,KAAK;MAAEE,QAAQ;MAAEV,gBAAgB;MAAEsB,WAAW;MAAEvC,YAAY;MAAEsC,IAAI;MAAEoG;IAAa,CAAC,GAAG,IAAI;IAC5H,MAAMpC,IAAI,GAAG/Q,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4f,0BAA0B,GAAGnV,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,YAAY;IACvH,MAAMoV,sBAAsB,GAAGnU,gBAAgB,IAAIkU,0BAA0B;IAC7E,MAAME,mBAAmB,GAAGpU,gBAAgB,IAAI,CAACkU,0BAA0B;IAC3E,MAAMhL,mBAAmB,GAAGnK,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IACnH,MAAMiV,eAAe,GAAG9K,mBAAmB,IAAI5H,WAAW;IAC1DnM,uDAAiB,CAAC,IAAI,EAAE0I,EAAE,EAAE4C,IAAI,EAAEhG,oDAAW,CAAC+G,KAAK,CAAC,EAAEzE,QAAQ,CAAC;IAC/D,OAAQtI,qDAAC,CAACE,iDAAI,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAEnH,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEqL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEmE,KAAK,EAAElP,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/H,qDAAkB,CAACgL,KAAK,EAAE;QACrM,CAAC6E,IAAI,GAAG,IAAI;QACZ,CAAC,mBAAmB,GAAG3E,QAAQ;QAC/B,CAAC,mBAAmB,GAAG3D,QAAQ;QAC/B,qBAAqB,EAAEoX,sBAAsB;QAC7C,wBAAwB,EAAEC,mBAAmB;QAC7C,CAAC,yBAAyBrV,YAAY,EAAE,GAAG,IAAI;QAC/C,CAAC,iBAAiBsC,IAAI,EAAE,GAAG,IAAI;QAC/B,CAAC,uBAAuB,GAAG2S,eAAe;QAC1C,CAAC,eAAe,GAAGvM;MACvB,CAAC,CAAC;IAAE,CAAC,EAAEhT,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE,sBAAsB;MAAEkF,GAAG,EAAG7T,EAAE,IAAM,IAAI,CAACuN,sBAAsB,GAAGvN;IAAI,CAAC,CAAC,EAAE,IAAI,CAACkW,cAAc,CAAC1O,IAAI,CAAC,CAAC;EACxL;EACA,IAAIxH,EAAEA,CAAA,EAAG;IAAE,OAAOhJ,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,eAAe,EAAE,CAAC,sBAAsB,CAAC;MACzC,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,YAAY,EAAE,CAAC,mBAAmB,CAAC;MACnC,aAAa,EAAE,CAAC,oBAAoB,CAAC;MACrC,WAAW,EAAE,CAAC,kBAAkB,CAAC;MACjC,YAAY,EAAE,CAAC,mBAAmB,CAAC;MACnC,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIvU,WAAW,GAAG,CAAC;AACnB,MAAM+K,WAAW,GAAG,iBAAiB;AACrC,MAAMN,YAAY,GAAG,kBAAkB;AACvC,MAAM0D,eAAe,GAAG,YAAY;AACpC,MAAMC,sBAAsB,GAAG,QAAQ;AACvChP,QAAQ,CAACkH,KAAK,GAAG;EACbkO,GAAG,EAAEtV,cAAc;EACnBuV,EAAE,EAAEtV;AACR,CAAC;;AAED;AACA;AACA;AACA,MAAMuV,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGrZ,0DAAe,CAAC,CAAC;EACvC,MAAMsZ,iBAAiB,GAAGtZ,0DAAe,CAAC,CAAC;EAC3C,MAAMuZ,gBAAgB,GAAGvZ,0DAAe,CAAC,CAAC;EAC1CsZ,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAAC/R,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDoS,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCJ,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAAC/R,aAAa,CAAC,iBAAiB,CAAC,CAAC,CACnDoS,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOJ,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBQ,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACR,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMQ,iBAAiB,GAAIX,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGrZ,0DAAe,CAAC,CAAC;EACvC,MAAMsZ,iBAAiB,GAAGtZ,0DAAe,CAAC,CAAC;EAC3C,MAAMuZ,gBAAgB,GAAGvZ,0DAAe,CAAC,CAAC;EAC1CsZ,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAAC/R,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDoS,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,IAAI,CAAC;EACvDF,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAAC/R,aAAa,CAAC,iBAAiB,CAAC,CAAC,CACnDoS,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOJ,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBQ,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACR,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMS,YAAY,GAAG,u/IAAu/I;AAE5gJ,MAAMC,WAAW,GAAG,+0IAA+0I;AAEn2I,MAAMC,MAAM,GAAG,MAAM;EACjBpW,WAAWA,CAACC,OAAO,EAAE;IACjBlL,qDAAgB,CAAC,IAAI,EAAEkL,OAAO,CAAC;IAC/B,IAAI,CAACoW,UAAU,GAAGphB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACqhB,WAAW,GAAGrhB,qDAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACshB,WAAW,GAAGthB,qDAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACuhB,UAAU,GAAGvhB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACwhB,mBAAmB,GAAGxhB,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACyhB,oBAAoB,GAAGzhB,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC0hB,oBAAoB,GAAG1hB,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC2hB,mBAAmB,GAAG3hB,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4hB,kBAAkB,GAAGrb,wDAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACsb,cAAc,GAAGvb,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAACwb,iBAAiB,GAAGtb,wDAAuB,CAAC,CAAC;IAClD,IAAI,CAACub,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;AACR;AACA;IACQ,IAAI,CAACrB,QAAQ,GAAG,CAAC;IACjB;AACR;AACA;IACQ,IAAI,CAACsB,YAAY,GAAG,IAAI;IACxB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAC1b,OAAO,CAACgB,SAAS,EAAErB,oDAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACgc,qBAAqB,GAAI3T,EAAE,IAAK;MACjC,MAAMjB,IAAI,GAAGiB,EAAE,CAAC2K,MAAM,CAAC5L,IAAI;MAC3B,IAAInH,wDAAQ,CAACmH,IAAI,CAAC,EAAE;QAChB,MAAM6U,YAAY,GAAG,IAAI,CAACR,OAAO,CAAC1Z,IAAI,CAAEvG,CAAC,IAAKA,CAAC,CAAC4L,IAAI,KAAK,QAAQ,CAAC;QAClE,IAAI,CAAC8U,iBAAiB,CAACD,YAAY,CAAC;MACxC;IACJ,CAAC;EACL;EACAE,cAAcA,CAACC,QAAQ,EAAE7T,QAAQ,EAAE;IAC/B,IAAI6T,QAAQ,KAAK,IAAI,IAAI7T,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACnI,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIgc,QAAQ,KAAK,KAAK,IAAI7T,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAClI,OAAO,CAAC,CAAC;IAClB;EACJ;EACAgc,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,OAAO;MAAEtZ,EAAE;MAAEqY;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIiB,OAAO,EAAE;MACTjB,iBAAiB,CAACkB,gBAAgB,CAACvZ,EAAE,EAAEsZ,OAAO,CAAC;IACnD;EACJ;EACArM,iBAAiBA,CAAA,EAAG;IAChB/P,wDAAc,CAAC,IAAI,CAAC8C,EAAE,CAAC;IACvB,IAAI,CAACqZ,cAAc,CAAC,CAAC;EACzB;EACAjM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACiL,iBAAiB,CAACmB,mBAAmB,CAAC,CAAC;EAChD;EACAnL,iBAAiBA,CAAA,EAAG;IAChB,IAAI3N,EAAE;IACN,IAAI,EAAE,CAACA,EAAE,GAAG,IAAI,CAAC+Y,cAAc,MAAM,IAAI,IAAI/Y,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkO,EAAE,CAAC,EAAE;MAC1EzR,wDAAY,CAAC,IAAI,CAAC6C,EAAE,CAAC;IACzB;EACJ;EACAsN,gBAAgBA,CAAA,EAAG;IACfnX,qDAAe,CAAC,2RAA2R,EAAE,IAAI,CAAC6J,EAAE,CAAC;IACrT;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC8Y,MAAM,KAAK,IAAI,EAAE;MACtB5hB,uDAAG,CAAC,MAAM,IAAI,CAACkG,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACic,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACUjc,OAAOA,CAAA,EAAG;IAAA,IAAAsc,MAAA;IAAA,OAAA3P,yMAAA;MACZ,MAAM4P,MAAM,SAASD,MAAI,CAACtB,cAAc,CAACwB,IAAI,CAAC,CAAC;MAC/C,MAAMF,MAAI,CAACvB,kBAAkB,CAAC0B,eAAe,CAAC,CAAC;MAC/C,MAAMzc,wDAAO,CAACsc,MAAI,EAAE,aAAa,EAAE/C,iBAAiB,EAAEA,iBAAiB,EAAEtY,SAAS,CAAC;MACnF,IAAIqb,MAAI,CAACrC,QAAQ,GAAG,CAAC,EAAE;QACnBqC,MAAI,CAACI,eAAe,GAAG9Q,UAAU,CAAC,MAAM0Q,MAAI,CAACrc,OAAO,CAAC,CAAC,EAAEqc,MAAI,CAACrC,QAAQ,CAAC;MAC1E;MACAsC,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUtc,OAAOA,CAAC0c,IAAI,EAAE3V,IAAI,EAAE;IAAA,IAAA4V,MAAA;IAAA,OAAAjQ,yMAAA;MACtB,MAAM4P,MAAM,SAASK,MAAI,CAAC5B,cAAc,CAACwB,IAAI,CAAC,CAAC;MAC/C,IAAII,MAAI,CAACF,eAAe,EAAE;QACtB/Q,YAAY,CAACiR,MAAI,CAACF,eAAe,CAAC;MACtC;MACA,MAAMG,SAAS,SAAS5c,wDAAO,CAAC2c,MAAI,EAAED,IAAI,EAAE3V,IAAI,EAAE,aAAa,EAAEmT,iBAAiB,EAAEA,iBAAiB,CAAC;MACtG,IAAI0C,SAAS,EAAE;QACXD,MAAI,CAAC7B,kBAAkB,CAAC+B,iBAAiB,CAAC,CAAC;MAC/C;MACAP,MAAM,CAAC,CAAC;MACR,OAAOM,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAO7c,wDAAW,CAAC,IAAI,CAAC0C,EAAE,EAAE,qBAAqB,CAAC;EACtD;EACA;AACJ;AACA;EACI4U,aAAaA,CAAA,EAAG;IACZ,OAAOtX,wDAAW,CAAC,IAAI,CAAC0C,EAAE,EAAE,sBAAsB,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIoa,SAASA,CAACxX,IAAI,EAAE;IACZ,OAAOqH,OAAO,CAACC,OAAO,CAAC,IAAI,CAACwO,OAAO,CAAC3Z,IAAI,CAAEsb,MAAM,IAAKA,MAAM,CAACzX,IAAI,KAAKA,IAAI,CAAC,CAAC;EAC/E;EACM0X,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAzQ,yMAAA;MACtB,MAAM3F,IAAI,GAAGmW,MAAM,CAACnW,IAAI;MACxB,IAAInH,wDAAQ,CAACmH,IAAI,CAAC,EAAE;QAChB,OAAOoW,MAAI,CAACnd,OAAO,CAACgB,SAAS,EAAE+F,IAAI,CAAC;MACxC;MACA,MAAMqW,aAAa,SAASD,MAAI,CAACtB,iBAAiB,CAACqB,MAAM,CAAC;MAC1D,IAAIE,aAAa,EAAE;QACf,OAAOD,MAAI,CAACnd,OAAO,CAACmd,MAAI,CAACE,WAAW,CAAC,CAAC,EAAEH,MAAM,CAACnW,IAAI,CAAC;MACxD;MACA,OAAO6F,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC7B;EACMgP,iBAAiBA,CAACqB,MAAM,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAA5Q,yMAAA;MAC5B,IAAIwQ,MAAM,EAAE;QACR;QACA;QACA,MAAMK,GAAG,SAASrd,wDAAQ,CAACgd,MAAM,CAACM,OAAO,EAAEF,MAAI,CAACD,WAAW,CAAC,CAAC,CAAC;QAC9D,IAAIE,GAAG,KAAK,KAAK,EAAE;UACf;UACA,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACAF,WAAWA,CAAA,EAAG;IACV,MAAMI,QAAQ,GAAG,CAAC,CAAC;IACnB,IAAI,CAACpC,OAAO,CAACtD,OAAO,CAAC,CAACC,GAAG,EAAEzF,KAAK,KAAK;MACjC,MAAMmL,cAAc,GAAG1F,GAAG,CAAC2F,aAAa,KAAK3c,SAAS,GAAGgX,GAAG,CAAC4F,OAAO,CAAC5F,GAAG,CAAC2F,aAAa,CAAC,GAAG3c,SAAS;MACnGyc,QAAQ,CAACzF,GAAG,CAACzS,IAAI,CAAC,GAAG;QACjBvD,IAAI,EAAE0b,cAAc,GAAGA,cAAc,CAAC1b,IAAI,GAAGhB,SAAS;QACtDsF,KAAK,EAAEoX,cAAc,GAAGA,cAAc,CAACpX,KAAK,GAAGtF,SAAS;QACxD6c,WAAW,EAAEtL;MACjB,CAAC;IACL,CAAC,CAAC;IACF,OAAOkL,QAAQ;EACnB;EACA1E,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEqD;IAAe,CAAC,GAAG,IAAI;IAC/B,MAAMjS,IAAI,GAAG/Q,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQG,qDAAC,CAACE,iDAAI,EAAE2I,MAAM,CAACC,MAAM,CAAC;MAAE2G,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,MAAM;MAAEyN,QAAQ,EAAE;IAAK,CAAC,EAAE2F,cAAc,EAAE;MAAElR,KAAK,EAAE;QAC1I4S,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACnG,YAAY;MACxC,CAAC;MAAErG,KAAK,EAAElP,MAAM,CAACC,MAAM,CAAC;QAAE,CAAC8H,IAAI,GAAG,IAAI;QAClC;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QAAE,gBAAgB,EAAE;MAAK,CAAC,EAAE5P,qDAAW,CAAC,IAAI,CAACwjB,QAAQ,CAAC,CAAC;MAAEC,gBAAgB,EAAE,IAAI,CAACtC,aAAa;MAAEuC,sBAAsB,EAAE,IAAI,CAACtC;IAAsB,CAAC,CAAC,EAAEpiB,qDAAC,CAAC,cAAc,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEkV,OAAO,EAAE,IAAI,CAAC5C,YAAY;MAAE6C,QAAQ,EAAE,IAAI,CAAC5C;IAAgB,CAAC,CAAC,EAAEhiB,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEyN,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,EAAEld,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE,oCAAoC;MAAEvK,IAAI,EAAE;IAAS,CAAC,EAAExN,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAAC8J,OAAO,CAAC7U,GAAG,CAAEpL,CAAC,IAAM5B,qDAAC,CAAC,KAAK,EAAE;MAAE+X,KAAK,EAAE8M,kBAAkB,CAACjjB,CAAC;IAAE,CAAC,EAAE5B,qDAAC,CAAC,QAAQ,EAAE;MAAE8kB,IAAI,EAAE,QAAQ;MAAE7M,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACyL,WAAW,CAAC9hB,CAAC,CAAC;MAAEmW,KAAK,EAAEgN,WAAW,CAACnjB,CAAC;IAAE,CAAC,EAAEA,CAAC,CAAC6G,IAAI,CAAC,CAAE,CAAC,CAAC,EAAEzI,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE;IAAiB,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE;IAAyB,CAAC,CAAC,EAAE,IAAI,CAAC2J,SAAS,IAAI,IAAI,CAACI,OAAO,CAAC9U,GAAG,CAAElM,CAAC,IAAKd,qDAAC,CAAC,0BAA0B,EAAE;MAAEye,GAAG,EAAE3d;IAAE,CAAC,CAAC,CAAC,EAAEd,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE;IAAyB,CAAC,CAAC,CAAC,CAAC,EAAE/X,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEyN,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC;EACpvC;EACA,IAAI9T,EAAEA,CAAA,EAAG;IAAE,OAAOhJ,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAMiF,kBAAkB,GAAIlB,MAAM,IAAK;EACnC,OAAO;IACH,CAAC,kBAAkBA,MAAM,CAACnW,IAAI,EAAE,GAAGmW,MAAM,CAACnW,IAAI,KAAK/F,SAAS;IAC5D,uBAAuB,EAAE;EAC7B,CAAC;AACL,CAAC;AACD,MAAMsd,WAAW,GAAIpB,MAAM,IAAK;EAC5B,OAAO9a,MAAM,CAACC,MAAM,CAAC;IAAE,eAAe,EAAE,IAAI;IAAE,iBAAiB,EAAE;EAAK,CAAC,EAAE9H,qDAAW,CAAC2iB,MAAM,CAACa,QAAQ,CAAC,CAAC;AAC1G,CAAC;AACD1D,MAAM,CAACnP,KAAK,GAAG;EACXkO,GAAG,EAAEe,YAAY;EACjBd,EAAE,EAAEe;AACR,CAAC;AAED,MAAMmE,kBAAkB,GAAG,wqEAAwqE;AAEnsE,MAAMC,iBAAiB,GAAG,yvDAAyvD;AAEnxD,MAAMC,eAAe,GAAG,MAAM;EAC1Bxa,WAAWA,CAACC,OAAO,EAAE;IACjBlL,qDAAgB,CAAC,IAAI,EAAEkL,OAAO,CAAC;IAC/B,IAAI,CAACwa,kBAAkB,GAAGxlB,qDAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACpE,IAAI,CAACylB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAC9hB,CAAC,GAAG,CAAC;IACV,IAAI,CAAC+hB,SAAS,GAAG,IAAI;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACMpP,iBAAiBA,CAAA,EAAG;IAAA,IAAAsP,MAAA;IAAA,OAAAxS,yMAAA;MACtB,IAAIyS,kBAAkB,GAAG,CAAC;MAC1B,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,MAAMjV,IAAI,GAAG/Q,qDAAU,CAAC8lB,MAAI,CAAC;MAC7B,IAAI/U,IAAI,KAAK,KAAK,EAAE;QAChBgV,kBAAkB,GAAG,CAAC,IAAI;QAC1BC,iBAAiB,GAAG,CAAC;MACzB;MACAF,MAAI,CAACN,YAAY,GAAGO,kBAAkB;MACtCD,MAAI,CAACL,WAAW,GAAGO,iBAAiB;MACpCF,MAAI,CAACG,OAAO,GAAG,OAAO,qHAA6B,EAAEC,aAAa,CAAC;QAC/D3c,EAAE,EAAEuc,MAAI,CAACvc,EAAE;QACX4c,WAAW,EAAE,cAAc;QAC3BC,eAAe,EAAE,GAAG;QACpB/O,SAAS,EAAE,CAAC;QACZgP,OAAO,EAAE,KAAK;QACdC,OAAO,EAAG1X,EAAE,IAAKkX,MAAI,CAACQ,OAAO,CAAC1X,EAAE,CAAC;QACjC2X,MAAM,EAAG3X,EAAE,IAAKkX,MAAI,CAACS,MAAM,CAAC3X,EAAE,CAAC;QAC/B4X,KAAK,EAAG5X,EAAE,IAAKkX,MAAI,CAACU,KAAK,CAAC5X,EAAE;MAChC,CAAC,CAAC;MACFkX,MAAI,CAACG,OAAO,CAACQ,MAAM,CAAC,CAAC;MACrB;MACA;MACA;MACA;MACAX,MAAI,CAACY,KAAK,GAAGnU,UAAU,CAAC,MAAM;QAC1BuT,MAAI,CAACH,SAAS,GAAG,KAAK;QACtB;QACA;QACA;QACAG,MAAI,CAACa,OAAO,CAAC,IAAI,CAAC;MACtB,CAAC,EAAE,GAAG,CAAC;IAAC;EACZ;EACA9P,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC+P,WAAW,CAAC,CAAC;EACtB;EACAC,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,IAAI,CAACjB,YAAY,EAAE;MACnB;MACA;MACA;MACA,IAAI,CAACgB,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC;MAC7B,IAAI,CAAChB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAjP,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACmQ,KAAK,KAAKlf,SAAS,EACxBmf,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,IAAI,IAAI,CAACJ,KAAK,EACVpU,YAAY,CAAC,IAAI,CAACoU,KAAK,CAAC;IAC5B,IAAI,IAAI,CAACT,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACvP,OAAO,CAAC,CAAC;MACtB,IAAI,CAACuP,OAAO,GAAGre,SAAS;IAC5B;EACJ;EACAof,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC1B,kBAAkB,CAAChY,IAAI,CAAC,IAAI,CAACsR,GAAG,CAAC;EAC1C;EACAqI,WAAWA,CAAC1C,aAAa,EAAE3D,QAAQ,EAAE;IACjC;IACA;IACA,MAAMhd,CAAC,GAAG2gB,aAAa,GAAG,CAAC,CAAC,GAAG,EAAEA,aAAa,GAAG,IAAI,CAACgB,SAAS,CAAC,GAAG,CAAC;IACpE,IAAI,CAACG,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,IAAI,CAACoB,KAAK,KAAKlf,SAAS,EACxBmf,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,IAAI,CAACI,MAAM,CAACtjB,CAAC,EAAEgd,QAAQ,EAAE,IAAI,CAAC;IAC9B,IAAI,CAACoG,aAAa,CAAC,CAAC;EACxB;EACAE,MAAMA,CAACtjB,CAAC,EAAEgd,QAAQ,EAAEuG,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd;IACJ;IACA;IACA,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAM;MAAE1I,GAAG;MAAE4G;IAAa,CAAC,GAAG,IAAI;IAClC,MAAM+B,YAAY,GAAG3I,GAAG,CAAC2F,aAAa;IACtC,MAAMA,aAAa,GAAI3F,GAAG,CAAC2F,aAAa,GAAG,IAAI,CAACiD,SAAS,CAAC,CAAC5jB,CAAC,CAAE;IAC9D,MAAM6jB,WAAW,GAAG7G,QAAQ,KAAK,CAAC,GAAG,EAAE,GAAGA,QAAQ,GAAG,IAAI;IACzD,MAAM8G,QAAQ,GAAG,SAAS,IAAI,CAACjC,WAAW,GAAG;IAC7C,MAAMkC,QAAQ,GAAG,IAAI,CAACP,MAAM,CAACO,QAAQ;IACrC,KAAK,IAAI5mB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,QAAQ,CAAC9U,MAAM,EAAE9R,CAAC,EAAE,EAAE;MACtC,MAAM+iB,MAAM,GAAG6D,QAAQ,CAAC5mB,CAAC,CAAC;MAC1B,MAAM6mB,GAAG,GAAGhJ,GAAG,CAAC4F,OAAO,CAACzjB,CAAC,CAAC;MAC1B,MAAM8mB,SAAS,GAAG9mB,CAAC,GAAG,IAAI,CAACwkB,SAAS,GAAG3hB,CAAC;MACxC,IAAIkkB,SAAS,GAAG,EAAE;MAClB,IAAItC,YAAY,KAAK,CAAC,EAAE;QACpB,MAAMuC,OAAO,GAAGF,SAAS,GAAGrC,YAAY;QACxC,IAAI9T,IAAI,CAACC,GAAG,CAACoW,OAAO,CAAC,IAAI,EAAE,EAAE;UACzBV,UAAU,GAAG,CAAC;UACdC,UAAU,GAAG,EAAE;UACfQ,SAAS,GAAG,WAAWC,OAAO,OAAO;QACzC,CAAC,MACI;UACDV,UAAU,GAAG,CAAC,IAAI;QACtB;MACJ,CAAC,MACI;QACDC,UAAU,GAAG,CAAC;QACdD,UAAU,GAAGQ,SAAS;MAC1B;MACA,MAAMxD,QAAQ,GAAGE,aAAa,KAAKxjB,CAAC;MACpC+mB,SAAS,IAAI,mBAAmBT,UAAU,MAAMC,UAAU,MAAM;MAChE,IAAI,IAAI,CAAC7B,WAAW,KAAK,CAAC,IAAI,CAACpB,QAAQ,EAAE;QACrCyD,SAAS,IAAIJ,QAAQ;MACzB;MACA;MACA,IAAI,IAAI,CAAC/B,SAAS,EAAE;QAChBiC,GAAG,CAAChH,QAAQ,GAAG,CAAC;QAChBkD,MAAM,CAAChS,KAAK,CAACkW,kBAAkB,GAAG,EAAE;MACxC,CAAC,MACI,IAAIpH,QAAQ,KAAKgH,GAAG,CAAChH,QAAQ,EAAE;QAChCgH,GAAG,CAAChH,QAAQ,GAAGA,QAAQ;QACvBkD,MAAM,CAAChS,KAAK,CAACkW,kBAAkB,GAAGP,WAAW;MACjD;MACA;MACA,IAAIK,SAAS,KAAKF,GAAG,CAACE,SAAS,EAAE;QAC7BF,GAAG,CAACE,SAAS,GAAGA,SAAS;MAC7B;MACAhE,MAAM,CAAChS,KAAK,CAACgW,SAAS,GAAGA,SAAS;MAClC;AACZ;AACA;AACA;MACYF,GAAG,CAACvD,QAAQ,GAAGA,QAAQ;MACvB,IAAIA,QAAQ,EAAE;QACVP,MAAM,CAAC/U,SAAS,CAACmI,GAAG,CAAC+Q,mBAAmB,CAAC;MAC7C,CAAC,MACI;QACDnE,MAAM,CAAC/U,SAAS,CAACwI,MAAM,CAAC0Q,mBAAmB,CAAC;MAChD;IACJ;IACA,IAAI,CAACrJ,GAAG,CAAC2I,YAAY,GAAGA,YAAY;IACpC,IAAIJ,KAAK,EAAE;MACP,IAAI,CAACvjB,CAAC,GAAGA,CAAC;IACd;IACA,IAAI,IAAI,CAACskB,SAAS,KAAK3D,aAAa,EAAE;MAClC;MACAvd,uDAAsB,CAAC,CAAC;MACxB,IAAI,CAACkhB,SAAS,GAAG3D,aAAa;IAClC;EACJ;EACA4D,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACzC,QAAQ,KAAK,CAAC,EAAE;MACrB;MACA,IAAI,CAACA,QAAQ,IAAI0C,qBAAqB;MACtC;MACA,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC,GAAGhU,IAAI,CAAClB,GAAG,CAAC,IAAI,CAACkV,QAAQ,EAAE,CAAC,CAAC,GAAGhU,IAAI,CAACpB,GAAG,CAAC,IAAI,CAACoV,QAAQ,EAAE,CAAC,CAAC,CAAC;MAC5F,IAAI9hB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC8hB,QAAQ;MAC9B,IAAI9hB,CAAC,GAAG,IAAI,CAACykB,IAAI,EAAE;QACf;QACAzkB,CAAC,GAAG,IAAI,CAACykB,IAAI;QACb,IAAI,CAAC3C,QAAQ,GAAG,CAAC;MACrB,CAAC,MACI,IAAI9hB,CAAC,GAAG,IAAI,CAAC0kB,IAAI,EAAE;QACpB;QACA1kB,CAAC,GAAG,IAAI,CAAC0kB,IAAI;QACb,IAAI,CAAC5C,QAAQ,GAAG,CAAC;MACrB;MACA,IAAI,CAACwB,MAAM,CAACtjB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACvB,MAAM2kB,WAAW,GAAG7W,IAAI,CAAC8W,KAAK,CAAC5kB,CAAC,CAAC,GAAG,IAAI,CAAC2hB,SAAS,KAAK,CAAC,IAAI7T,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC+T,QAAQ,CAAC,GAAG,CAAC;MACvF,IAAI6C,WAAW,EAAE;QACb;QACA,IAAI,CAACzB,KAAK,GAAG/W,qBAAqB,CAAC,MAAM,IAAI,CAACoY,UAAU,CAAC,CAAC,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAACzC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAACsB,aAAa,CAAC,CAAC;QACpB/f,uDAAkB,CAAC,CAAC;MACxB;IACJ,CAAC,MACI,IAAI,IAAI,CAACrD,CAAC,GAAG,IAAI,CAAC2hB,SAAS,KAAK,CAAC,EAAE;MACpC;MACA,MAAMkD,UAAU,GAAG/W,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/N,CAAC,GAAG,IAAI,CAAC2hB,SAAS,CAAC;MACpD;MACA,IAAI,CAACG,QAAQ,GAAG+C,UAAU,GAAG,IAAI,CAAClD,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACxD,IAAI,CAAC4C,UAAU,CAAC,CAAC;IACrB;EACJ;EACAX,SAASA,CAAC5jB,CAAC,EAAE;IACT,OAAO8N,IAAI,CAACpB,GAAG,CAACoB,IAAI,CAAClB,GAAG,CAACkB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC8W,KAAK,CAAC5kB,CAAC,GAAG,IAAI,CAAC2hB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC3G,GAAG,CAAC4F,OAAO,CAAC3R,MAAM,GAAG,CAAC,CAAC;EACvG;EACAyT,OAAOA,CAAC/M,MAAM,EAAE;IACZ;IACA;IACA;IACA,IAAIA,MAAM,CAACmP,KAAK,CAACC,UAAU,EAAE;MACzBpP,MAAM,CAACmP,KAAK,CAAC7Y,cAAc,CAAC,CAAC;IACjC;IACA0J,MAAM,CAACmP,KAAK,CAACjR,eAAe,CAAC,CAAC;IAC9BvQ,uDAAoB,CAAC,CAAC;IACtB;IACA,IAAI,IAAI,CAAC4f,KAAK,KAAKlf,SAAS,EACxBmf,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,MAAMtC,OAAO,GAAG,IAAI,CAAC5F,GAAG,CAAC4F,OAAO;IAChC,IAAI6D,IAAI,GAAG7D,OAAO,CAAC3R,MAAM,GAAG,CAAC;IAC7B,IAAIyV,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIvnB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyjB,OAAO,CAAC3R,MAAM,EAAE9R,CAAC,EAAE,EAAE;MACrC,IAAI,CAACyjB,OAAO,CAACzjB,CAAC,CAAC,CAAC0H,QAAQ,EAAE;QACtB4f,IAAI,GAAG3W,IAAI,CAACpB,GAAG,CAAC+X,IAAI,EAAEtnB,CAAC,CAAC;QACxBunB,IAAI,GAAG5W,IAAI,CAAClB,GAAG,CAAC8X,IAAI,EAAEvnB,CAAC,CAAC;MAC5B;IACJ;IACA,IAAI,CAACsnB,IAAI,GAAG,EAAEA,IAAI,GAAG,IAAI,CAAC9C,SAAS,CAAC;IACpC,IAAI,CAAC+C,IAAI,GAAG,EAAEA,IAAI,GAAG,IAAI,CAAC/C,SAAS,CAAC;EACxC;EACAgB,MAAMA,CAAChN,MAAM,EAAE;IACX,IAAIA,MAAM,CAACmP,KAAK,CAACC,UAAU,EAAE;MACzBpP,MAAM,CAACmP,KAAK,CAAC7Y,cAAc,CAAC,CAAC;IACjC;IACA0J,MAAM,CAACmP,KAAK,CAACjR,eAAe,CAAC,CAAC;IAC9B;IACA,IAAI7T,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG2V,MAAM,CAACqP,MAAM;IAC9B,IAAIhlB,CAAC,GAAG,IAAI,CAACykB,IAAI,EAAE;MACf;MACAzkB,CAAC,GAAG8N,IAAI,CAACmX,GAAG,CAACjlB,CAAC,EAAE,GAAG,CAAC;MACpB,IAAI,CAACklB,UAAU,GAAGllB,CAAC;IACvB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,CAAC0kB,IAAI,EAAE;MACpB;MACA1kB,CAAC,IAAI8N,IAAI,CAACmX,GAAG,CAAC,IAAI,CAACP,IAAI,GAAG1kB,CAAC,EAAE,GAAG,CAAC;MACjC,IAAI,CAACklB,UAAU,GAAGllB,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACklB,UAAU,GAAG,CAAC;IACvB;IACA,IAAI,CAAC5B,MAAM,CAACtjB,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;EAC5B;EACA4iB,KAAKA,CAACjN,MAAM,EAAE;IACV,IAAI,IAAI,CAACuP,UAAU,GAAG,CAAC,EAAE;MACrB;MACA,IAAI,CAAC5B,MAAM,CAAC,IAAI,CAACmB,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;MACjC,IAAI,CAACrB,aAAa,CAAC,CAAC;MACpB;IACJ,CAAC,MACI,IAAI,IAAI,CAAC8B,UAAU,GAAG,CAAC,EAAE;MAC1B;MACA,IAAI,CAAC5B,MAAM,CAAC,IAAI,CAACoB,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;MACjC,IAAI,CAACtB,aAAa,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACtB,QAAQ,GAAG5kB,uDAAK,CAAC,CAAC,EAAE,EAAEyY,MAAM,CAACwP,SAAS,GAAG,EAAE,EAAEC,gBAAgB,CAAC;IACnE,IAAI,IAAI,CAACtD,QAAQ,KAAK,CAAC,IAAInM,MAAM,CAACqP,MAAM,KAAK,CAAC,EAAE;MAC5C,MAAMhB,GAAG,GAAGrO,MAAM,CAACmP,KAAK,CAACxK,MAAM,CAACrQ,OAAO,CAAC,aAAa,CAAC;MACtD,IAAI+Z,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,YAAY,CAAC,WAAW,CAAC,EAAE;QACzE,IAAI,CAAChC,WAAW,CAAC/M,QAAQ,CAAC0N,GAAG,CAAC9L,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,EAAEoN,mBAAmB,CAAC;MACtF;IACJ,CAAC,MACI;MACD,IAAI,CAACtlB,CAAC,IAAI2V,MAAM,CAACqP,MAAM;MACvB,IAAIlX,IAAI,CAACC,GAAG,CAAC4H,MAAM,CAACwP,SAAS,CAAC,GAAG,IAAI,EAAE;QACnC,MAAMI,aAAa,GAAG5P,MAAM,CAACqP,MAAM,GAAG,CAAC;QACvC,MAAMQ,iBAAiB,GAAI1X,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/N,CAAC,CAAC,GAAG,IAAI,CAAC2hB,SAAS,GAAI,IAAI,CAACA,SAAS;QAC9E,IAAI4D,aAAa,IAAIC,iBAAiB,GAAG,GAAG,EAAE;UAC1C,IAAI,CAAC1D,QAAQ,GAAGhU,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC+T,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC,MACI,IAAI,CAACyD,aAAa,IAAIC,iBAAiB,IAAI,GAAG,EAAE;UACjD,IAAI,CAAC1D,QAAQ,GAAGhU,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC+T,QAAQ,CAAC;QAC3C;MACJ;MACA,IAAI,CAACyC,UAAU,CAAC,CAAC;IACrB;EACJ;EACAxB,OAAOA,CAAC0C,YAAY,EAAEjH,QAAQ,EAAE;IAC5B,IAAInY,EAAE;IACN,IAAIqG,GAAG,GAAG,IAAI,CAACsO,GAAG,CAAC4F,OAAO,CAAC3R,MAAM,GAAG,CAAC;IACrC,IAAIrC,GAAG,GAAG,CAAC;IACX,MAAMgU,OAAO,GAAG,IAAI,CAAC5F,GAAG,CAAC4F,OAAO;IAChC,KAAK,IAAIzjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyjB,OAAO,CAAC3R,MAAM,EAAE9R,CAAC,EAAE,EAAE;MACrC,IAAI,CAACyjB,OAAO,CAACzjB,CAAC,CAAC,CAAC0H,QAAQ,EAAE;QACtB6H,GAAG,GAAGoB,IAAI,CAACpB,GAAG,CAACA,GAAG,EAAEvP,CAAC,CAAC;QACtByP,GAAG,GAAGkB,IAAI,CAAClB,GAAG,CAACA,GAAG,EAAEzP,CAAC,CAAC;MAC1B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC2kB,QAAQ,KAAK,CAAC,EAAE;MACrB;IACJ;IACA,MAAMnB,aAAa,GAAGzjB,uDAAK,CAACwP,GAAG,EAAE,CAACrG,EAAE,GAAG,IAAI,CAAC2U,GAAG,CAAC2F,aAAa,MAAM,IAAI,IAAIta,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAEuG,GAAG,CAAC;IACvG,IAAI,IAAI,CAACoO,GAAG,CAAC2I,YAAY,KAAKhD,aAAa,IAAI8E,YAAY,EAAE;MACzD,MAAMzlB,CAAC,GAAG2gB,aAAa,GAAG,IAAI,CAACgB,SAAS,GAAG,CAAC,CAAC;MAC7C,MAAM3E,QAAQ,GAAGwB,QAAQ,GAAG8G,mBAAmB,GAAG,CAAC;MACnD,IAAI,CAACxD,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACwB,MAAM,CAACtjB,CAAC,EAAEgd,QAAQ,EAAE,IAAI,CAAC;IAClC;EACJ;EACAgG,WAAWA,CAACyC,YAAY,EAAEjH,QAAQ,EAAE;IAChC,MAAMkH,KAAK,GAAG,IAAI,CAAClC,MAAM;IACzB,IAAIkC,KAAK,EAAE;MACP;MACA;MACA,IAAI,CAAC/D,SAAS,GAAG+D,KAAK,CAACC,iBAAiB,GAAGD,KAAK,CAACC,iBAAiB,CAACC,YAAY,GAAG,CAAC;IACvF;IACA,IAAI,CAAC7C,OAAO,CAAC0C,YAAY,EAAEjH,QAAQ,CAAC;EACxC;EACAzC,MAAMA,CAAA,EAAG;IACL,MAAMf,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAM7N,IAAI,GAAG/Q,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQG,qDAAC,CAACE,iDAAI,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAElP,MAAM,CAACC,MAAM,CAAC;QAAE,CAAC8H,IAAI,GAAG,IAAI;QAAE,YAAY,EAAE,IAAI;QAAE,kBAAkB,EAAE,IAAI,CAAC6N,GAAG,CAAC6K,KAAK,KAAK,MAAM;QAAE,mBAAmB,EAAE,IAAI,CAAC7K,GAAG,CAAC6K,KAAK,KAAK;MAAQ,CAAC,EAAEtoB,qDAAW,CAACyd,GAAG,CAAC+F,QAAQ,CAAC,CAAC;MAAE7S,KAAK,EAAE;QACzP,WAAW,EAAE,IAAI,CAAC8M,GAAG,CAAC8K;MAC1B;IAAE,CAAC,EAAE9K,GAAG,CAAC+K,MAAM,IAAKxpB,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE,eAAe;MAAEpG,KAAK,EAAE;QAAE8X,KAAK,EAAEhL,GAAG,CAACiL;MAAY;IAAE,CAAC,EAAEjL,GAAG,CAAC+K,MAAM,CAAE,EAAExpB,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE,aAAa;MAAEpG,KAAK,EAAE;QAAEgY,QAAQ,EAAElL,GAAG,CAACmL;MAAa,CAAC;MAAE3M,GAAG,EAAG7T,EAAE,IAAM,IAAI,CAAC6d,MAAM,GAAG7d;IAAI,CAAC,EAAEqV,GAAG,CAAC4F,OAAO,CAACrX,GAAG,CAAC,CAAC5N,CAAC,EAAE4Z,KAAK,KAAMhZ,qDAAC,CAAC,QAAQ,EAAE;MAAE,YAAY,EAAEZ,CAAC,CAACoJ,SAAS;MAAEuP,KAAK,EAAE;QAAE,YAAY,EAAE,IAAI;QAAE,qBAAqB,EAAE,CAAC,CAAC3Y,CAAC,CAACkJ;MAAS,CAAC;MAAE,WAAW,EAAE0Q;IAAM,CAAC,EAAE5Z,CAAC,CAACqJ,IAAI,CAAE,CAAC,CAAC,EAAEgW,GAAG,CAACoL,MAAM,IAAK7pB,qDAAC,CAAC,KAAK,EAAE;MAAEyP,GAAG,EAAE,0CAA0C;MAAEsI,KAAK,EAAE,eAAe;MAAEpG,KAAK,EAAE;QAAE8X,KAAK,EAAEhL,GAAG,CAACqL;MAAY;IAAE,CAAC,EAAErL,GAAG,CAACoL,MAAM,CAAE,CAAC;EACloB;EACA,IAAIzgB,EAAEA,CAAA,EAAG;IAAE,OAAOhJ,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,YAAY;IACxB,CAAC;EAAE;AACP,CAAC;AACD,MAAMkI,mBAAmB,GAAG,qBAAqB;AACjD,MAAMG,qBAAqB,GAAG,IAAI;AAClC,MAAMY,gBAAgB,GAAG,EAAE;AAC3B,MAAME,mBAAmB,GAAG,GAAG;AAC/B7D,eAAe,CAACvT,KAAK,GAAG;EACpBkO,GAAG,EAAEmF,kBAAkB;EACvBlF,EAAE,EAAEmF;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-datetime_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { o as printIonError, m as printIonWarning, r as registerInstance, d as createEvent, e as getIonMode, w as writeTask, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { startFocusVisible } from './focus-visible-BmVRXR1y.js';\nimport { r as raf, g as getElementRoot, a as renderHiddenInput, e as clamp } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { l as chevronDown, o as caretUpSharp, p as chevronForward, q as caretDownSharp, c as chevronBack } from './index-BLV6ykCk.js';\nimport { i as isBefore, a as isAfter, g as getPreviousMonth, b as getNextMonth, c as isSameDay, d as getDay, e as generateDayAriaLabel, v as validateParts, f as getPartsFromCalendarDay, h as getNextYear, j as getPreviousYear, k as getEndOfWeek, l as getStartOfWeek, m as getPreviousDay, n as getNextDay, o as getPreviousWeek, p as getNextWeek, q as parseMinParts, r as parseMaxParts, s as parseDate, w as warnIfValueOutOfBounds, t as parseAmPm, u as clampDate, x as convertToArrayOfNumbers, y as convertDataToISO, z as getToday, A as getClosestValidDate, B as generateMonths, C as getNumDaysInMonth, D as getCombinedDateColumnData, E as getMonthColumnData, F as getDayColumnData, G as getYearColumnData, H as isMonthFirstLocale, I as getTimeColumnsData, J as isLocaleDayPeriodRTL, K as calculateHourFromAMPM, L as getDaysOfWeek, M as getMonthAndYear, N as getDaysOfMonth, O as getHourCycle, P as getLocalizedTime, Q as getLocalizedDateTime, R as formatValue } from './data-GIsHsYIB.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { a as hapticSelectionChanged, h as hapticSelectionEnd, b as hapticSelectionStart } from './haptic-DzAMWJuk.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './capacitor-CFERIeaU.js';\n\nconst isYearDisabled = (refYear, minParts, maxParts) => {\n    if (minParts && minParts.year > refYear) {\n        return true;\n    }\n    if (maxParts && maxParts.year < refYear) {\n        return true;\n    }\n    return false;\n};\n/**\n * Returns true if a given day should\n * not be interactive according to its value,\n * or the max/min dates.\n */\nconst isDayDisabled = (refParts, minParts, maxParts, dayValues) => {\n    /**\n     * If this is a filler date (i.e. padding)\n     * then the date is disabled.\n     */\n    if (refParts.day === null) {\n        return true;\n    }\n    /**\n     * If user passed in a list of acceptable day values\n     * check to make sure that the date we are looking\n     * at is in this array.\n     */\n    if (dayValues !== undefined && !dayValues.includes(refParts.day)) {\n        return true;\n    }\n    /**\n     * Given a min date, perform the following\n     * checks. If any of them are true, then the\n     * day should be disabled:\n     * 1. Is the current year < the min allowed year?\n     * 2. Is the current year === min allowed year,\n     * but the current month < the min allowed month?\n     * 3. Is the current year === min allowed year, the\n     * current month === min allow month, but the current\n     * day < the min allowed day?\n     */\n    if (minParts && isBefore(refParts, minParts)) {\n        return true;\n    }\n    /**\n     * Given a max date, perform the following\n     * checks. If any of them are true, then the\n     * day should be disabled:\n     * 1. Is the current year > the max allowed year?\n     * 2. Is the current year === max allowed year,\n     * but the current month > the max allowed month?\n     * 3. Is the current year === max allowed year, the\n     * current month === max allow month, but the current\n     * day > the max allowed day?\n     */\n    if (maxParts && isAfter(refParts, maxParts)) {\n        return true;\n    }\n    /**\n     * If none of these checks\n     * passed then the date should\n     * be interactive.\n     */\n    return false;\n};\n/**\n * Given a locale, a date, the selected date(s), and today's date,\n * generate the state for a given calendar day button.\n */\nconst getCalendarDayState = (locale, refParts, activeParts, todayParts, minParts, maxParts, dayValues) => {\n    /**\n     * activeParts signals what day(s) are currently selected in the datetime.\n     * If multiple=\"true\", this will be an array, but the logic in this util\n     * is the same whether we have one selected day or many because we're only\n     * calculating the state for one button. So, we treat a single activeParts value\n     * the same as an array of length one.\n     */\n    const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n    /**\n     * The day button is active if it is selected, or in other words, if refParts\n     * matches at least one selected date.\n     */\n    const isActive = activePartsArray.find((parts) => isSameDay(refParts, parts)) !== undefined;\n    const isToday = isSameDay(refParts, todayParts);\n    const disabled = isDayDisabled(refParts, minParts, maxParts, dayValues);\n    /**\n     * Note that we always return one object regardless of whether activeParts\n     * was an array, since we pare down to one value for isActive.\n     */\n    return {\n        disabled,\n        isActive,\n        isToday,\n        ariaSelected: isActive ? 'true' : null,\n        ariaLabel: generateDayAriaLabel(locale, isToday, refParts),\n        text: refParts.day != null ? getDay(locale, refParts) : null,\n    };\n};\n/**\n * Returns `true` if the month is disabled given the\n * current date value and min/max date constraints.\n */\nconst isMonthDisabled = (refParts, { minParts, maxParts, }) => {\n    // If the year is disabled then the month is disabled.\n    if (isYearDisabled(refParts.year, minParts, maxParts)) {\n        return true;\n    }\n    // If the date value is before the min date, then the month is disabled.\n    // If the date value is after the max date, then the month is disabled.\n    if ((minParts && isBefore(refParts, minParts)) || (maxParts && isAfter(refParts, maxParts))) {\n        return true;\n    }\n    return false;\n};\n/**\n * Given a working date, an optional minimum date range,\n * and an optional maximum date range; determine if the\n * previous navigation button is disabled.\n */\nconst isPrevMonthDisabled = (refParts, minParts, maxParts) => {\n    const prevMonth = Object.assign(Object.assign({}, getPreviousMonth(refParts)), { day: null });\n    return isMonthDisabled(prevMonth, {\n        minParts,\n        maxParts,\n    });\n};\n/**\n * Given a working date and a maximum date range,\n * determine if the next navigation button is disabled.\n */\nconst isNextMonthDisabled = (refParts, maxParts) => {\n    const nextMonth = Object.assign(Object.assign({}, getNextMonth(refParts)), { day: null });\n    return isMonthDisabled(nextMonth, {\n        maxParts,\n    });\n};\n/**\n * Given the value of the highlightedDates property\n * and an ISO string, return the styles to use for\n * that date, or undefined if none are found.\n */\nconst getHighlightStyles = (highlightedDates, dateIsoString, el) => {\n    if (Array.isArray(highlightedDates)) {\n        const dateStringWithoutTime = dateIsoString.split('T')[0];\n        const matchingHighlight = highlightedDates.find((hd) => hd.date === dateStringWithoutTime);\n        if (matchingHighlight) {\n            return {\n                textColor: matchingHighlight.textColor,\n                backgroundColor: matchingHighlight.backgroundColor,\n            };\n        }\n    }\n    else {\n        /**\n         * Wrap in a try-catch to prevent exceptions in the user's function\n         * from interrupting the calendar's rendering.\n         */\n        try {\n            return highlightedDates(dateIsoString);\n        }\n        catch (e) {\n            printIonError('[ion-datetime] - Exception thrown from provided `highlightedDates` callback. Please check your function and try again.', el, e);\n        }\n    }\n    return undefined;\n};\n\n/**\n * If a time zone is provided in the format options, the rendered text could\n * differ from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst warnIfTimeZoneProvided = (el, formatOptions) => {\n    var _a, _b, _c, _d;\n    if (((_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _a === void 0 ? void 0 : _a.timeZone) ||\n        ((_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _b === void 0 ? void 0 : _b.timeZoneName) ||\n        ((_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _c === void 0 ? void 0 : _c.timeZone) ||\n        ((_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _d === void 0 ? void 0 : _d.timeZoneName)) {\n        printIonWarning('[ion-datetime] - \"timeZone\" and \"timeZoneName\" are not supported in \"formatOptions\".', el);\n    }\n};\nconst checkForPresentationFormatMismatch = (el, presentation, formatOptions) => {\n    // formatOptions is not required\n    if (!formatOptions)\n        return;\n    // If formatOptions is provided, the date and/or time objects are required, depending on the presentation\n    switch (presentation) {\n        case 'date':\n        case 'month-year':\n        case 'month':\n        case 'year':\n            if (formatOptions.date === undefined) {\n                printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires a date object in formatOptions.`, el);\n            }\n            break;\n        case 'time':\n            if (formatOptions.time === undefined) {\n                printIonWarning(`[ion-datetime] - The 'time' presentation requires a time object in formatOptions.`, el);\n            }\n            break;\n        case 'date-time':\n        case 'time-date':\n            if (formatOptions.date === undefined && formatOptions.time === undefined) {\n                printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires either a date or time object (or both) in formatOptions.`, el);\n            }\n            break;\n    }\n};\n\nconst datetimeIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-light, #f4f5f8);--background-rgb:var(--ion-color-light-rgb, 244, 245, 248);--title-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc));font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}.calendar-month-year-toggle{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-height:44px;font-size:min(1rem, 25.6px);font-weight:600}.calendar-month-year-toggle.ion-focused::after{opacity:0.15}.calendar-month-year-toggle #toggle-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host .calendar-action-buttons .calendar-month-year-toggle ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3));font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2);font-size:min(1.375rem, 35.2px)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc))}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}\";\n\nconst datetimeMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #ffffff));--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}.calendar-month-year-toggle{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;min-height:48px;background:transparent;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959));z-index:1}.calendar-month-year-toggle.ion-focused::after{opacity:0.04}.calendar-month-year-toggle ion-ripple-effect{color:currentColor}@media (any-hover: hover){.calendar-month-year-toggle.ion-activatable:not(.ion-focused):hover::after{background:currentColor;opacity:0.04}}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray));font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active,.calendar-day.calendar-day-active:focus{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}\";\n\nconst Datetime = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.ionRender = createEvent(this, \"ionRender\", 7);\n        this.inputId = `ion-dt-${datetimeIds++}`;\n        this.prevPresentation = null;\n        this.showMonthAndYear = false;\n        this.activeParts = [];\n        this.workingParts = {\n            month: 5,\n            day: 28,\n            year: 2021,\n            hour: 13,\n            minute: 52,\n            ampm: 'pm',\n            isAdjacentDay: false,\n        };\n        this.isTimePopoverOpen = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the user cannot interact with the datetime.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the datetime appears normal but the selected date cannot be changed.\n         */\n        this.readonly = false;\n        /**\n         * If `true`, the datetime calendar displays a six-week (42-day) layout,\n         * including days from the previous and next months to fill the grid.\n         * These adjacent days are selectable unless disabled.\n         */\n        this.showAdjacentDays = false;\n        /**\n         * Which values you want to select. `\"date\"` will show\n         * a calendar picker to select the month, day, and year. `\"time\"`\n         * will show a time picker to select the hour, minute, and (optionally)\n         * AM/PM. `\"date-time\"` will show the date picker first and time picker second.\n         * `\"time-date\"` will show the time picker first and date picker second.\n         */\n        this.presentation = 'date-time';\n        /**\n         * The text to display on the picker's cancel button.\n         */\n        this.cancelText = 'Cancel';\n        /**\n         * The text to display on the picker's \"Done\" button.\n         */\n        this.doneText = 'Done';\n        /**\n         * The text to display on the picker's \"Clear\" button.\n         */\n        this.clearText = 'Clear';\n        /**\n         * The locale to use for `ion-datetime`. This\n         * impacts month and day name formatting.\n         * The `\"default\"` value refers to the default\n         * locale set by your device.\n         */\n        this.locale = 'default';\n        /**\n         * The first day of the week to use for `ion-datetime`. The\n         * default value is `0` and represents Sunday.\n         */\n        this.firstDayOfWeek = 0;\n        /**\n         * If `true`, multiple dates can be selected at once. Only\n         * applies to `presentation=\"date\"` and `preferWheel=\"false\"`.\n         */\n        this.multiple = false;\n        /**\n         * If `true`, a header will be shown above the calendar\n         * picker. This will include both the slotted title, and\n         * the selected date.\n         */\n        this.showDefaultTitle = false;\n        /**\n         * If `true`, the default \"Cancel\" and \"OK\" buttons\n         * will be rendered at the bottom of the `ion-datetime`\n         * component. Developers can also use the `button` slot\n         * if they want to customize these buttons. If custom\n         * buttons are set in the `button` slot then the\n         * default buttons will not be rendered.\n         */\n        this.showDefaultButtons = false;\n        /**\n         * If `true`, a \"Clear\" button will be rendered alongside\n         * the default \"Cancel\" and \"OK\" buttons at the bottom of the `ion-datetime`\n         * component. Developers can also use the `button` slot\n         * if they want to customize these buttons. If custom\n         * buttons are set in the `button` slot then the\n         * default buttons will not be rendered.\n         */\n        this.showClearButton = false;\n        /**\n         * If `true`, the default \"Time\" label will be rendered\n         * for the time selector of the `ion-datetime` component.\n         * Developers can also use the `time-label` slot\n         * if they want to customize this label. If a custom\n         * label is set in the `time-label` slot then the\n         * default label will not be rendered.\n         */\n        this.showDefaultTimeLabel = true;\n        /**\n         * If `cover`, the `ion-datetime` will expand to cover the full width of its container.\n         * If `fixed`, the `ion-datetime` will have a fixed width.\n         */\n        this.size = 'fixed';\n        /**\n         * If `true`, a wheel picker will be rendered instead of a calendar grid\n         * where possible. If `false`, a calendar grid will be rendered instead of\n         * a wheel picker where possible.\n         *\n         * A wheel picker can be rendered instead of a grid when `presentation` is\n         * one of the following values: `\"date\"`, `\"date-time\"`, or `\"time-date\"`.\n         *\n         * A wheel picker will always be rendered regardless of\n         * the `preferWheel` value when `presentation` is one of the following values:\n         * `\"time\"`, `\"month\"`, `\"month-year\"`, or `\"year\"`.\n         */\n        this.preferWheel = false;\n        this.warnIfIncorrectValueUsage = () => {\n            const { multiple, value } = this;\n            if (!multiple && Array.isArray(value)) {\n                /**\n                 * We do some processing on the `value` array so\n                 * that it looks more like an array when logged to\n                 * the console.\n                 * Example given ['a', 'b']\n                 * Default toString() behavior: a,b\n                 * Custom behavior: ['a', 'b']\n                 */\n                printIonWarning(`[ion-datetime] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n            }\n        };\n        this.setValue = (value) => {\n            this.value = value;\n            this.ionChange.emit({ value });\n        };\n        /**\n         * Returns the DatetimePart interface\n         * to use when rendering an initial set of\n         * data. This should be used when rendering an\n         * interface in an environment where the `value`\n         * may not be set. This function works\n         * by returning the first selected date and then\n         * falling back to defaultParts if no active date\n         * is selected.\n         */\n        this.getActivePartsWithFallback = () => {\n            var _a;\n            const { defaultParts } = this;\n            return (_a = this.getActivePart()) !== null && _a !== void 0 ? _a : defaultParts;\n        };\n        this.getActivePart = () => {\n            const { activeParts } = this;\n            return Array.isArray(activeParts) ? activeParts[0] : activeParts;\n        };\n        this.closeParentOverlay = (role) => {\n            const popoverOrModal = this.el.closest('ion-modal, ion-popover');\n            if (popoverOrModal) {\n                popoverOrModal.dismiss(undefined, role);\n            }\n        };\n        this.setWorkingParts = (parts) => {\n            this.workingParts = Object.assign({}, parts);\n        };\n        this.setActiveParts = (parts, removeDate = false) => {\n            /** if the datetime component is in readonly mode,\n             * allow browsing of the calendar without changing\n             * the set value\n             */\n            if (this.readonly) {\n                return;\n            }\n            const { multiple, minParts, maxParts, activeParts } = this;\n            /**\n             * When setting the active parts, it is possible\n             * to set invalid data. For example,\n             * when updating January 31 to February,\n             * February 31 does not exist. As a result\n             * we need to validate the active parts and\n             * ensure that we are only setting valid dates.\n             * Additionally, we need to update the working parts\n             * too in the event that the validated parts are different.\n             */\n            const validatedParts = validateParts(parts, minParts, maxParts);\n            this.setWorkingParts(validatedParts);\n            if (multiple) {\n                const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n                if (removeDate) {\n                    this.activeParts = activePartsArray.filter((p) => !isSameDay(p, validatedParts));\n                }\n                else {\n                    this.activeParts = [...activePartsArray, validatedParts];\n                }\n            }\n            else {\n                this.activeParts = Object.assign({}, validatedParts);\n            }\n            const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n            if (hasSlottedButtons || this.showDefaultButtons) {\n                return;\n            }\n            this.confirm();\n        };\n        this.initializeKeyboardListeners = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const root = this.el.shadowRoot;\n            /**\n             * Get a reference to the month\n             * element we are currently viewing.\n             */\n            const currentMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(2)');\n            /**\n             * When focusing the calendar body, we want to pass focus\n             * to the working day, but other days should\n             * only be accessible using the arrow keys. Pressing\n             * Tab should jump between bodies of selectable content.\n             */\n            const checkCalendarBodyFocus = (ev) => {\n                var _a;\n                const record = ev[0];\n                /**\n                 * If calendar body was already focused\n                 * when this fired or if the calendar body\n                 * if not currently focused, we should not re-focus\n                 * the inner day.\n                 */\n                if (((_a = record.oldValue) === null || _a === void 0 ? void 0 : _a.includes('ion-focused')) || !calendarBodyRef.classList.contains('ion-focused')) {\n                    return;\n                }\n                this.focusWorkingDay(currentMonth);\n            };\n            const mo = new MutationObserver(checkCalendarBodyFocus);\n            mo.observe(calendarBodyRef, { attributeFilter: ['class'], attributeOldValue: true });\n            this.destroyKeyboardMO = () => {\n                mo === null || mo === void 0 ? void 0 : mo.disconnect();\n            };\n            /**\n             * We must use keydown not keyup as we want\n             * to prevent scrolling when using the arrow keys.\n             */\n            calendarBodyRef.addEventListener('keydown', (ev) => {\n                const activeElement = root.activeElement;\n                if (!activeElement || !activeElement.classList.contains('calendar-day')) {\n                    return;\n                }\n                const parts = getPartsFromCalendarDay(activeElement);\n                let partsToFocus;\n                switch (ev.key) {\n                    case 'ArrowDown':\n                        ev.preventDefault();\n                        partsToFocus = getNextWeek(parts);\n                        break;\n                    case 'ArrowUp':\n                        ev.preventDefault();\n                        partsToFocus = getPreviousWeek(parts);\n                        break;\n                    case 'ArrowRight':\n                        ev.preventDefault();\n                        partsToFocus = getNextDay(parts);\n                        break;\n                    case 'ArrowLeft':\n                        ev.preventDefault();\n                        partsToFocus = getPreviousDay(parts);\n                        break;\n                    case 'Home':\n                        ev.preventDefault();\n                        partsToFocus = getStartOfWeek(parts);\n                        break;\n                    case 'End':\n                        ev.preventDefault();\n                        partsToFocus = getEndOfWeek(parts);\n                        break;\n                    case 'PageUp':\n                        ev.preventDefault();\n                        partsToFocus = ev.shiftKey ? getPreviousYear(parts) : getPreviousMonth(parts);\n                        break;\n                    case 'PageDown':\n                        ev.preventDefault();\n                        partsToFocus = ev.shiftKey ? getNextYear(parts) : getNextMonth(parts);\n                        break;\n                    /**\n                     * Do not preventDefault here\n                     * as we do not want to override other\n                     * browser defaults such as pressing Enter/Space\n                     * to select a day.\n                     */\n                    default:\n                        return;\n                }\n                /**\n                 * If the day we want to move focus to is\n                 * disabled, do not do anything.\n                 */\n                if (isDayDisabled(partsToFocus, this.minParts, this.maxParts)) {\n                    return;\n                }\n                this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), partsToFocus));\n                /**\n                 * Give view a chance to re-render\n                 * then move focus to the new working day\n                 */\n                requestAnimationFrame(() => this.focusWorkingDay(currentMonth));\n            });\n        };\n        this.focusWorkingDay = (currentMonth) => {\n            /**\n             * Get the number of offset days so\n             * we know how much to offset our next selector by\n             * to grab the correct calendar-day element.\n             */\n            const { day, month, year } = this.workingParts;\n            const firstOfMonth = new Date(`${month}/1/${year}`).getDay();\n            const offset = firstOfMonth >= this.firstDayOfWeek\n                ? firstOfMonth - this.firstDayOfWeek\n                : 7 - (this.firstDayOfWeek - firstOfMonth);\n            if (day === null) {\n                return;\n            }\n            /**\n             * Get the calendar day element\n             * and focus it.\n             */\n            const dayEl = currentMonth.querySelector(`.calendar-day-wrapper:nth-of-type(${offset + day}) .calendar-day`);\n            if (dayEl) {\n                dayEl.focus();\n            }\n        };\n        this.processMinParts = () => {\n            const { min, defaultParts } = this;\n            if (min === undefined) {\n                this.minParts = undefined;\n                return;\n            }\n            this.minParts = parseMinParts(min, defaultParts);\n        };\n        this.processMaxParts = () => {\n            const { max, defaultParts } = this;\n            if (max === undefined) {\n                this.maxParts = undefined;\n                return;\n            }\n            this.maxParts = parseMaxParts(max, defaultParts);\n        };\n        this.initializeCalendarListener = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            /**\n             * For performance reasons, we only render 3\n             * months at a time: The current month, the previous\n             * month, and the next month. We have a scroll listener\n             * on the calendar body to append/prepend new months.\n             *\n             * We can do this because Stencil is smart enough to not\n             * re-create the .calendar-month containers, but rather\n             * update the content within those containers.\n             *\n             * As an added bonus, WebKit has some troubles with\n             * scroll-snap-stop: always, so not rendering all of\n             * the months in a row allows us to mostly sidestep\n             * that issue.\n             */\n            const months = calendarBodyRef.querySelectorAll('.calendar-month');\n            const startMonth = months[0];\n            const workingMonth = months[1];\n            const endMonth = months[2];\n            const mode = getIonMode(this);\n            const needsiOSRubberBandFix = mode === 'ios' && typeof navigator !== 'undefined' && navigator.maxTouchPoints > 1;\n            /**\n             * Before setting up the scroll listener,\n             * scroll the middle month into view.\n             * scrollIntoView() will scroll entire page\n             * if element is not in viewport. Use scrollLeft instead.\n             */\n            writeTask(() => {\n                calendarBodyRef.scrollLeft = startMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n                const getChangedMonth = (parts) => {\n                    const box = calendarBodyRef.getBoundingClientRect();\n                    /**\n                     * If the current scroll position is all the way to the left\n                     * then we have scrolled to the previous month.\n                     * Otherwise, assume that we have scrolled to the next\n                     * month. We have a tolerance of 2px to account for\n                     * sub pixel rendering.\n                     *\n                     * Check below the next line ensures that we did not\n                     * swipe and abort (i.e. we swiped but we are still on the current month).\n                     */\n                    const condition = isRTL(this.el) ? calendarBodyRef.scrollLeft >= -2 : calendarBodyRef.scrollLeft <= 2;\n                    const month = condition ? startMonth : endMonth;\n                    /**\n                     * The edge of the month must be lined up with\n                     * the edge of the calendar body in order for\n                     * the component to update. Otherwise, it\n                     * may be the case that the user has paused their\n                     * swipe or the browser has not finished snapping yet.\n                     * Rather than check if the x values are equal,\n                     * we give it a tolerance of 2px to account for\n                     * sub pixel rendering.\n                     */\n                    const monthBox = month.getBoundingClientRect();\n                    if (Math.abs(monthBox.x - box.x) > 2)\n                        return;\n                    /**\n                     * If we're force-rendering a month, assume we've\n                     * scrolled to that and return it.\n                     *\n                     * If forceRenderDate is ever used in a context where the\n                     * forced month is not immediately auto-scrolled to, this\n                     * should be updated to also check whether `month` has the\n                     * same month and year as the forced date.\n                     */\n                    const { forceRenderDate } = this;\n                    if (forceRenderDate !== undefined) {\n                        return { month: forceRenderDate.month, year: forceRenderDate.year, day: forceRenderDate.day };\n                    }\n                    /**\n                     * From here, we can determine if the start\n                     * month or the end month was scrolled into view.\n                     * If no month was changed, then we can return from\n                     * the scroll callback early.\n                     */\n                    if (month === startMonth) {\n                        return getPreviousMonth(parts);\n                    }\n                    else if (month === endMonth) {\n                        return getNextMonth(parts);\n                    }\n                    else {\n                        return;\n                    }\n                };\n                const updateActiveMonth = () => {\n                    if (needsiOSRubberBandFix) {\n                        calendarBodyRef.style.removeProperty('pointer-events');\n                        appliediOSRubberBandFix = false;\n                    }\n                    /**\n                     * If the month did not change\n                     * then we can return early.\n                     */\n                    const newDate = getChangedMonth(this.workingParts);\n                    if (!newDate)\n                        return;\n                    const { month, day, year } = newDate;\n                    if (isMonthDisabled({ month, year, day: null }, {\n                        minParts: Object.assign(Object.assign({}, this.minParts), { day: null }),\n                        maxParts: Object.assign(Object.assign({}, this.maxParts), { day: null }),\n                    })) {\n                        return;\n                    }\n                    /**\n                     * Prevent scrolling for other browsers\n                     * to give the DOM time to update and the container\n                     * time to properly snap.\n                     */\n                    calendarBodyRef.style.setProperty('overflow', 'hidden');\n                    /**\n                     * Use a writeTask here to ensure\n                     * that the state is updated and the\n                     * correct month is scrolled into view\n                     * in the same frame. This is not\n                     * typically a problem on newer devices\n                     * but older/slower device may have a flicker\n                     * if we did not do this.\n                     */\n                    writeTask(() => {\n                        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), { month, day: day, year }));\n                        calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n                        calendarBodyRef.style.removeProperty('overflow');\n                        if (this.resolveForceDateScrolling) {\n                            this.resolveForceDateScrolling();\n                        }\n                    });\n                };\n                /**\n                 * When the container finishes scrolling we\n                 * need to update the DOM with the selected month.\n                 */\n                let scrollTimeout;\n                /**\n                 * We do not want to attempt to set pointer-events\n                 * multiple times within a single swipe gesture as\n                 * that adds unnecessary work to the main thread.\n                 */\n                let appliediOSRubberBandFix = false;\n                const scrollCallback = () => {\n                    if (scrollTimeout) {\n                        clearTimeout(scrollTimeout);\n                    }\n                    /**\n                     * On iOS it is possible to quickly rubber band\n                     * the scroll area before the scroll timeout has fired.\n                     * This results in users reaching the end of the scrollable\n                     * container before the DOM has updated.\n                     * By setting `pointer-events: none` we can ensure that\n                     * subsequent swipes do not happen while the container\n                     * is snapping.\n                     */\n                    if (!appliediOSRubberBandFix && needsiOSRubberBandFix) {\n                        calendarBodyRef.style.setProperty('pointer-events', 'none');\n                        appliediOSRubberBandFix = true;\n                    }\n                    // Wait ~3 frames\n                    scrollTimeout = setTimeout(updateActiveMonth, 50);\n                };\n                calendarBodyRef.addEventListener('scroll', scrollCallback);\n                this.destroyCalendarListener = () => {\n                    calendarBodyRef.removeEventListener('scroll', scrollCallback);\n                };\n            });\n        };\n        /**\n         * Clean up all listeners except for the overlay\n         * listener. This is so that we can re-create the listeners\n         * if the datetime has been hidden/presented by a modal or popover.\n         */\n        this.destroyInteractionListeners = () => {\n            const { destroyCalendarListener, destroyKeyboardMO } = this;\n            if (destroyCalendarListener !== undefined) {\n                destroyCalendarListener();\n            }\n            if (destroyKeyboardMO !== undefined) {\n                destroyKeyboardMO();\n            }\n        };\n        this.processValue = (value) => {\n            const hasValue = value !== null && value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0);\n            const valueToProcess = hasValue ? parseDate(value) : this.defaultParts;\n            const { minParts, maxParts, workingParts, el } = this;\n            this.warnIfIncorrectValueUsage();\n            /**\n             * Return early if the value wasn't parsed correctly, such as\n             * if an improperly formatted date string was provided.\n             */\n            if (!valueToProcess) {\n                return;\n            }\n            /**\n             * Datetime should only warn of out of bounds values\n             * if set by the user. If the `value` is undefined,\n             * we will default to today's date which may be out\n             * of bounds. In this case, the warning makes it look\n             * like the developer did something wrong which is\n             * not true.\n             */\n            if (hasValue) {\n                warnIfValueOutOfBounds(valueToProcess, minParts, maxParts);\n            }\n            /**\n             * If there are multiple values, clamp to the last one.\n             * This is because the last value is the one that the user\n             * has most recently interacted with.\n             */\n            const singleValue = Array.isArray(valueToProcess) ? valueToProcess[valueToProcess.length - 1] : valueToProcess;\n            const targetValue = clampDate(singleValue, minParts, maxParts);\n            const { month, day, year, hour, minute } = targetValue;\n            const ampm = parseAmPm(hour);\n            /**\n             * Since `activeParts` indicates a value that been explicitly selected\n             * either by the user or the app, only update `activeParts` if the\n             * `value` property is set.\n             */\n            if (hasValue) {\n                if (Array.isArray(valueToProcess)) {\n                    this.activeParts = [...valueToProcess];\n                }\n                else {\n                    this.activeParts = {\n                        month,\n                        day,\n                        year,\n                        hour,\n                        minute,\n                        ampm,\n                    };\n                }\n            }\n            else {\n                /**\n                 * Reset the active parts if the value is not set.\n                 * This will clear the selected calendar day when\n                 * performing a clear action or using the reset() method.\n                 */\n                this.activeParts = [];\n            }\n            const didChangeMonth = (month !== undefined && month !== workingParts.month) || (year !== undefined && year !== workingParts.year);\n            const bodyIsVisible = el.classList.contains('datetime-ready');\n            const { isGridStyle, showMonthAndYear } = this;\n            if (isGridStyle && didChangeMonth && bodyIsVisible && !showMonthAndYear) {\n                /**\n                 * Only animate if:\n                 * 1. We're using grid style (wheel style pickers should just jump to new value)\n                 * 2. The month and/or year actually changed, and both are defined (otherwise there's nothing to animate to)\n                 * 3. The calendar body is visible (prevents animation when in collapsed datetime-button, for example)\n                 * 4. The month/year picker is not open (since you wouldn't see the animation anyway)\n                 */\n                this.animateToDate(targetValue);\n            }\n            else {\n                this.setWorkingParts({\n                    month,\n                    day,\n                    year,\n                    hour,\n                    minute,\n                    ampm,\n                });\n            }\n        };\n        this.animateToDate = async (targetValue) => {\n            const { workingParts } = this;\n            /**\n             * Tell other render functions that we need to force the\n             * target month to appear in place of the actual next/prev month.\n             * Because this is a State variable, a rerender will be triggered\n             * automatically, updating the rendered months.\n             */\n            this.forceRenderDate = targetValue;\n            /**\n             * Flag that we've started scrolling to the forced date.\n             * The resolve function will be called by the datetime's\n             * scroll listener when it's done updating everything.\n             * This is a replacement for making prev/nextMonth async,\n             * since the logic we're waiting on is in a listener.\n             */\n            const forceDateScrollingPromise = new Promise((resolve) => {\n                this.resolveForceDateScrolling = resolve;\n            });\n            /**\n             * Animate smoothly to the forced month. This will also update\n             * workingParts and correct the surrounding months for us.\n             */\n            const targetMonthIsBefore = isBefore(targetValue, workingParts);\n            targetMonthIsBefore ? this.prevMonth() : this.nextMonth();\n            await forceDateScrollingPromise;\n            this.resolveForceDateScrolling = undefined;\n            this.forceRenderDate = undefined;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.hasValue = () => {\n            return this.value != null;\n        };\n        this.nextMonth = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const nextMonth = calendarBodyRef.querySelector('.calendar-month:last-of-type');\n            if (!nextMonth) {\n                return;\n            }\n            const left = nextMonth.offsetWidth * 2;\n            calendarBodyRef.scrollTo({\n                top: 0,\n                left: left * (isRTL(this.el) ? -1 : 1),\n                behavior: 'smooth',\n            });\n        };\n        this.prevMonth = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const prevMonth = calendarBodyRef.querySelector('.calendar-month:first-of-type');\n            if (!prevMonth) {\n                return;\n            }\n            calendarBodyRef.scrollTo({\n                top: 0,\n                left: 0,\n                behavior: 'smooth',\n            });\n        };\n        this.toggleMonthAndYearView = () => {\n            this.showMonthAndYear = !this.showMonthAndYear;\n        };\n    }\n    formatOptionsChanged() {\n        const { el, formatOptions, presentation } = this;\n        checkForPresentationFormatMismatch(el, presentation, formatOptions);\n        warnIfTimeZoneProvided(el, formatOptions);\n    }\n    disabledChanged() {\n        this.emitStyle();\n    }\n    minChanged() {\n        this.processMinParts();\n    }\n    maxChanged() {\n        this.processMaxParts();\n    }\n    presentationChanged() {\n        const { el, formatOptions, presentation } = this;\n        checkForPresentationFormatMismatch(el, presentation, formatOptions);\n    }\n    get isGridStyle() {\n        const { presentation, preferWheel } = this;\n        const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        return hasDatePresentation && !preferWheel;\n    }\n    yearValuesChanged() {\n        this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n    }\n    monthValuesChanged() {\n        this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n    }\n    dayValuesChanged() {\n        this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n    }\n    hourValuesChanged() {\n        this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n    }\n    minuteValuesChanged() {\n        this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n    }\n    /**\n     * Update the datetime value when the value changes\n     */\n    async valueChanged() {\n        const { value } = this;\n        if (this.hasValue()) {\n            this.processValue(value);\n        }\n        this.emitStyle();\n        this.ionValueChange.emit({ value });\n    }\n    /**\n     * Confirms the selected datetime value, updates the\n     * `value` property, and optionally closes the popover\n     * or modal that the datetime was presented in.\n     *\n     * @param closeOverlay If `true`, closes the parent overlay. Defaults to `false`.\n     */\n    async confirm(closeOverlay = false) {\n        const { isCalendarPicker, activeParts, preferWheel, workingParts } = this;\n        /**\n         * We only update the value if the presentation is not a calendar picker.\n         */\n        if (activeParts !== undefined || !isCalendarPicker) {\n            const activePartsIsArray = Array.isArray(activeParts);\n            if (activePartsIsArray && activeParts.length === 0) {\n                if (preferWheel) {\n                    /**\n                     * If the datetime is using a wheel picker, but the\n                     * active parts are empty, then the user has confirmed the\n                     * initial value (working parts) presented to them.\n                     */\n                    this.setValue(convertDataToISO(workingParts));\n                }\n                else {\n                    this.setValue(undefined);\n                }\n            }\n            else {\n                this.setValue(convertDataToISO(activeParts));\n            }\n        }\n        if (closeOverlay) {\n            this.closeParentOverlay(CONFIRM_ROLE);\n        }\n    }\n    /**\n     * Resets the internal state of the datetime but does not update the value.\n     * Passing a valid ISO-8601 string will reset the state of the component to the provided date.\n     * If no value is provided, the internal state will be reset to the clamped value of the min, max and today.\n     *\n     * @param startDate A valid [ISO-8601 string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#date_time_string_format) to reset the datetime state to.\n     */\n    async reset(startDate) {\n        this.processValue(startDate);\n    }\n    /**\n     * Emits the ionCancel event and\n     * optionally closes the popover\n     * or modal that the datetime was\n     * presented in.\n     *\n     * @param closeOverlay If `true`, closes the parent overlay. Defaults to `false`.\n     */\n    async cancel(closeOverlay = false) {\n        this.ionCancel.emit();\n        if (closeOverlay) {\n            this.closeParentOverlay(CANCEL_ROLE);\n        }\n    }\n    get isCalendarPicker() {\n        const { presentation } = this;\n        return presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    }\n    connectedCallback() {\n        this.clearFocusVisible = startFocusVisible(this.el).destroy;\n    }\n    disconnectedCallback() {\n        if (this.clearFocusVisible) {\n            this.clearFocusVisible();\n            this.clearFocusVisible = undefined;\n        }\n    }\n    initializeListeners() {\n        this.initializeCalendarListener();\n        this.initializeKeyboardListeners();\n    }\n    componentDidLoad() {\n        const { el, intersectionTrackerRef } = this;\n        /**\n         * If a scrollable element is hidden using `display: none`,\n         * it will not have a scroll height meaning we cannot scroll elements\n         * into view. As a result, we will need to wait for the datetime to become\n         * visible if used inside of a modal or a popover otherwise the scrollable\n         * areas will not have the correct values snapped into place.\n         */\n        const visibleCallback = (entries) => {\n            const ev = entries[0];\n            if (!ev.isIntersecting) {\n                return;\n            }\n            this.initializeListeners();\n            /**\n             * TODO FW-2793: Datetime needs a frame to ensure that it\n             * can properly scroll contents into view. As a result\n             * we hide the scrollable content until after that frame\n             * so users do not see the content quickly shifting. The downside\n             * is that the content will pop into view a frame after. Maybe there\n             * is a better way to handle this?\n             */\n            writeTask(() => {\n                this.el.classList.add('datetime-ready');\n            });\n        };\n        const visibleIO = new IntersectionObserver(visibleCallback, { threshold: 0.01, root: el });\n        /**\n         * Use raf to avoid a race condition between the component loading and\n         * its display animation starting (such as when shown in a modal). This\n         * could cause the datetime to start at a visibility of 0, erroneously\n         * triggering the `hiddenIO` observer below.\n         */\n        raf(() => visibleIO === null || visibleIO === void 0 ? void 0 : visibleIO.observe(intersectionTrackerRef));\n        /**\n         * We need to clean up listeners when the datetime is hidden\n         * in a popover/modal so that we can properly scroll containers\n         * back into view if they are re-presented. When the datetime is hidden\n         * the scroll areas have scroll widths/heights of 0px, so any snapping\n         * we did originally has been lost.\n         */\n        const hiddenCallback = (entries) => {\n            const ev = entries[0];\n            if (ev.isIntersecting) {\n                return;\n            }\n            this.destroyInteractionListeners();\n            /**\n             * When datetime is hidden, we need to make sure that\n             * the month/year picker is closed. Otherwise,\n             * it will be open when the datetime re-appears\n             * and the scroll area of the calendar grid will be 0.\n             * As a result, the wrong month will be shown.\n             */\n            this.showMonthAndYear = false;\n            writeTask(() => {\n                this.el.classList.remove('datetime-ready');\n            });\n        };\n        const hiddenIO = new IntersectionObserver(hiddenCallback, { threshold: 0, root: el });\n        raf(() => hiddenIO === null || hiddenIO === void 0 ? void 0 : hiddenIO.observe(intersectionTrackerRef));\n        /**\n         * Datetime uses Ionic components that emit\n         * ionFocus and ionBlur. These events are\n         * composed meaning they will cross\n         * the shadow dom boundary. We need to\n         * stop propagation on these events otherwise\n         * developers will see 2 ionFocus or 2 ionBlur\n         * events at a time.\n         */\n        const root = getElementRoot(this.el);\n        root.addEventListener('ionFocus', (ev) => ev.stopPropagation());\n        root.addEventListener('ionBlur', (ev) => ev.stopPropagation());\n    }\n    /**\n     * When the presentation is changed, all calendar content is recreated,\n     * so we need to re-init behavior with the new elements.\n     */\n    componentDidRender() {\n        const { presentation, prevPresentation, calendarBodyRef, minParts, preferWheel, forceRenderDate } = this;\n        /**\n         * TODO(FW-2165)\n         * Remove this when https://bugs.webkit.org/show_bug.cgi?id=235960 is fixed.\n         * When using `min`, we add `scroll-snap-align: none`\n         * to the disabled month so that users cannot scroll to it.\n         * This triggers a bug in WebKit where the scroll position is reset.\n         * Since the month change logic is handled by a scroll listener,\n         * this causes the month to change leading to `scroll-snap-align`\n         * changing again, thus changing the scroll position again and causing\n         * an infinite loop.\n         * This issue only applies to the calendar grid, so we can disable\n         * it if the calendar grid is not being used.\n         */\n        const hasCalendarGrid = !preferWheel && ['date-time', 'time-date', 'date'].includes(presentation);\n        if (minParts !== undefined && hasCalendarGrid && calendarBodyRef) {\n            const workingMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(1)');\n            /**\n             * We need to make sure the datetime is not in the process\n             * of scrolling to a new datetime value if the value\n             * is updated programmatically.\n             * Otherwise, the datetime will appear to not scroll at all because\n             * we are resetting the scroll position to the center of the view.\n             * Prior to the datetime's value being updated programmatically,\n             * the calendarBodyRef is scrolled such that the middle month is centered\n             * in the view. The below code updates the scroll position so the middle\n             * month is also centered in the view. Since the scroll position did not change,\n             * the scroll callback in this file does not fire,\n             * and the resolveForceDateScrolling promise never resolves.\n             */\n            if (workingMonth && forceRenderDate === undefined) {\n                calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n            }\n        }\n        if (prevPresentation === null) {\n            this.prevPresentation = presentation;\n            return;\n        }\n        if (presentation === prevPresentation) {\n            return;\n        }\n        this.prevPresentation = presentation;\n        this.destroyInteractionListeners();\n        this.initializeListeners();\n        /**\n         * The month/year picker from the date interface\n         * should be closed as it is not available in non-date\n         * interfaces.\n         */\n        this.showMonthAndYear = false;\n        raf(() => {\n            this.ionRender.emit();\n        });\n    }\n    componentWillLoad() {\n        const { el, formatOptions, highlightedDates, multiple, presentation, preferWheel } = this;\n        if (multiple) {\n            if (presentation !== 'date') {\n                printIonWarning('[ion-datetime] - Multiple date selection is only supported for presentation=\"date\".', el);\n            }\n            if (preferWheel) {\n                printIonWarning('[ion-datetime] - Multiple date selection is not supported with preferWheel=\"true\".', el);\n            }\n        }\n        if (highlightedDates !== undefined) {\n            if (presentation !== 'date' && presentation !== 'date-time' && presentation !== 'time-date') {\n                printIonWarning('[ion-datetime] - The highlightedDates property is only supported with the date, date-time, and time-date presentations.', el);\n            }\n            if (preferWheel) {\n                printIonWarning('[ion-datetime] - The highlightedDates property is not supported with preferWheel=\"true\".', el);\n            }\n        }\n        if (formatOptions) {\n            checkForPresentationFormatMismatch(el, presentation, formatOptions);\n            warnIfTimeZoneProvided(el, formatOptions);\n        }\n        const hourValues = (this.parsedHourValues = convertToArrayOfNumbers(this.hourValues));\n        const minuteValues = (this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues));\n        const monthValues = (this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues));\n        const yearValues = (this.parsedYearValues = convertToArrayOfNumbers(this.yearValues));\n        const dayValues = (this.parsedDayValues = convertToArrayOfNumbers(this.dayValues));\n        const todayParts = (this.todayParts = parseDate(getToday()));\n        this.processMinParts();\n        this.processMaxParts();\n        this.defaultParts = getClosestValidDate({\n            refParts: todayParts,\n            monthValues,\n            dayValues,\n            yearValues,\n            hourValues,\n            minuteValues,\n            minParts: this.minParts,\n            maxParts: this.maxParts,\n        });\n        this.processValue(this.value);\n        this.emitStyle();\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            interactive: true,\n            datetime: true,\n            'interactive-disabled': this.disabled,\n        });\n    }\n    /**\n     * Universal render methods\n     * These are pieces of datetime that\n     * are rendered independently of presentation.\n     */\n    renderFooter() {\n        const { disabled, readonly, showDefaultButtons, showClearButton } = this;\n        /**\n         * The cancel, clear, and confirm buttons\n         * should not be interactive if the datetime\n         * is disabled or readonly.\n         */\n        const isButtonDisabled = disabled || readonly;\n        const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n        if (!hasSlottedButtons && !showDefaultButtons && !showClearButton) {\n            return;\n        }\n        const clearButtonClick = () => {\n            this.reset();\n            this.setValue(undefined);\n        };\n        /**\n         * By default we render two buttons:\n         * Cancel - Dismisses the datetime and\n         * does not update the `value` prop.\n         * OK - Dismisses the datetime and\n         * updates the `value` prop.\n         */\n        return (h(\"div\", { class: \"datetime-footer\" }, h(\"div\", { class: \"datetime-buttons\" }, h(\"div\", { class: {\n                ['datetime-action-buttons']: true,\n                ['has-clear-button']: this.showClearButton,\n            } }, h(\"slot\", { name: \"buttons\" }, h(\"ion-buttons\", null, showDefaultButtons && (h(\"ion-button\", { id: \"cancel-button\", color: this.color, onClick: () => this.cancel(true), disabled: isButtonDisabled }, this.cancelText)), h(\"div\", { class: \"datetime-action-buttons-container\" }, showClearButton && (h(\"ion-button\", { id: \"clear-button\", color: this.color, onClick: () => clearButtonClick(), disabled: isButtonDisabled }, this.clearText)), showDefaultButtons && (h(\"ion-button\", { id: \"confirm-button\", color: this.color, onClick: () => this.confirm(true), disabled: isButtonDisabled }, this.doneText)))))))));\n    }\n    /**\n     * Wheel picker render methods\n     */\n    renderWheelPicker(forcePresentation = this.presentation) {\n        /**\n         * If presentation=\"time-date\" we switch the\n         * order of the render array here instead of\n         * manually reordering each date/time picker\n         * column with CSS. This allows for additional\n         * flexibility if we need to render subsets\n         * of the date/time data or do additional ordering\n         * within the child render functions.\n         */\n        const renderArray = forcePresentation === 'time-date'\n            ? [this.renderTimePickerColumns(forcePresentation), this.renderDatePickerColumns(forcePresentation)]\n            : [this.renderDatePickerColumns(forcePresentation), this.renderTimePickerColumns(forcePresentation)];\n        return h(\"ion-picker\", null, renderArray);\n    }\n    renderDatePickerColumns(forcePresentation) {\n        return forcePresentation === 'date-time' || forcePresentation === 'time-date'\n            ? this.renderCombinedDatePickerColumn()\n            : this.renderIndividualDatePickerColumns(forcePresentation);\n    }\n    renderCombinedDatePickerColumn() {\n        const { defaultParts, disabled, workingParts, locale, minParts, maxParts, todayParts, isDateEnabled } = this;\n        const activePart = this.getActivePartsWithFallback();\n        /**\n         * By default, generate a range of 3 months:\n         * Previous month, current month, and next month\n         */\n        const monthsToRender = generateMonths(workingParts);\n        const lastMonth = monthsToRender[monthsToRender.length - 1];\n        /**\n         * Ensure that users can select the entire window of dates.\n         */\n        monthsToRender[0].day = 1;\n        lastMonth.day = getNumDaysInMonth(lastMonth.month, lastMonth.year);\n        /**\n         * Narrow the dates rendered based on min/max dates (if any).\n         * The `min` date is used if the min is after the generated min month.\n         * The `max` date is used if the max is before the generated max month.\n         * This ensures that the sliding window always stays at 3 months\n         * but still allows future dates to be lazily rendered based on any min/max\n         * constraints.\n         */\n        const min = minParts !== undefined && isAfter(minParts, monthsToRender[0]) ? minParts : monthsToRender[0];\n        const max = maxParts !== undefined && isBefore(maxParts, lastMonth) ? maxParts : lastMonth;\n        const result = getCombinedDateColumnData(locale, todayParts, min, max, this.parsedDayValues, this.parsedMonthValues);\n        let items = result.items;\n        const parts = result.parts;\n        if (isDateEnabled) {\n            items = items.map((itemObject, index) => {\n                const referenceParts = parts[index];\n                let disabled;\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    disabled = !isDateEnabled(convertDataToISO(referenceParts));\n                }\n                catch (e) {\n                    printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n                }\n                return Object.assign(Object.assign({}, itemObject), { disabled });\n            });\n        }\n        /**\n         * If we have selected a day already, then default the column\n         * to that value. Otherwise, set it to the default date.\n         */\n        const todayString = workingParts.day !== null\n            ? `${workingParts.year}-${workingParts.month}-${workingParts.day}`\n            : `${defaultParts.year}-${defaultParts.month}-${defaultParts.day}`;\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a date\", class: \"date-column\", color: this.color, disabled: disabled, value: todayString, onIonChange: (ev) => {\n                const { value } = ev.detail;\n                const findPart = parts.find(({ month, day, year }) => value === `${year}-${month}-${day}`);\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), findPart));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), findPart));\n                ev.stopPropagation();\n            } }, items.map((item) => (h(\"ion-picker-column-option\", { part: item.value === todayString ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: item.value, disabled: item.disabled, value: item.value }, item.text)))));\n    }\n    renderIndividualDatePickerColumns(forcePresentation) {\n        const { workingParts, isDateEnabled } = this;\n        const shouldRenderMonths = forcePresentation !== 'year' && forcePresentation !== 'time';\n        const months = shouldRenderMonths\n            ? getMonthColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedMonthValues)\n            : [];\n        const shouldRenderDays = forcePresentation === 'date';\n        let days = shouldRenderDays\n            ? getDayColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedDayValues)\n            : [];\n        if (isDateEnabled) {\n            days = days.map((dayObject) => {\n                const { value } = dayObject;\n                const valueNum = typeof value === 'string' ? parseInt(value) : value;\n                const referenceParts = {\n                    month: workingParts.month,\n                    day: valueNum,\n                    year: workingParts.year,\n                };\n                let disabled;\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    disabled = !isDateEnabled(convertDataToISO(referenceParts));\n                }\n                catch (e) {\n                    printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n                }\n                return Object.assign(Object.assign({}, dayObject), { disabled });\n            });\n        }\n        const shouldRenderYears = forcePresentation !== 'month' && forcePresentation !== 'time';\n        const years = shouldRenderYears\n            ? getYearColumnData(this.locale, this.defaultParts, this.minParts, this.maxParts, this.parsedYearValues)\n            : [];\n        /**\n         * Certain locales show the day before the month.\n         */\n        const showMonthFirst = isMonthFirstLocale(this.locale, { month: 'numeric', day: 'numeric' });\n        let renderArray = [];\n        if (showMonthFirst) {\n            renderArray = [\n                this.renderMonthPickerColumn(months),\n                this.renderDayPickerColumn(days),\n                this.renderYearPickerColumn(years),\n            ];\n        }\n        else {\n            renderArray = [\n                this.renderDayPickerColumn(days),\n                this.renderMonthPickerColumn(months),\n                this.renderYearPickerColumn(years),\n            ];\n        }\n        return renderArray;\n    }\n    renderDayPickerColumn(days) {\n        var _a;\n        if (days.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        const pickerColumnValue = (_a = (workingParts.day !== null ? workingParts.day : this.defaultParts.day)) !== null && _a !== void 0 ? _a : undefined;\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a day\", class: \"day-column\", color: this.color, disabled: disabled, value: pickerColumnValue, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { day: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { day: ev.detail.value }));\n                ev.stopPropagation();\n            } }, days.map((day) => (h(\"ion-picker-column-option\", { part: day.value === pickerColumnValue ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: day.value, disabled: day.disabled, value: day.value }, day.text)))));\n    }\n    renderMonthPickerColumn(months) {\n        if (months.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a month\", class: \"month-column\", color: this.color, disabled: disabled, value: workingParts.month, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { month: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { month: ev.detail.value }));\n                ev.stopPropagation();\n            } }, months.map((month) => (h(\"ion-picker-column-option\", { part: month.value === workingParts.month ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: month.value, disabled: month.disabled, value: month.value }, month.text)))));\n    }\n    renderYearPickerColumn(years) {\n        if (years.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a year\", class: \"year-column\", color: this.color, disabled: disabled, value: workingParts.year, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { year: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { year: ev.detail.value }));\n                ev.stopPropagation();\n            } }, years.map((year) => (h(\"ion-picker-column-option\", { part: year.value === workingParts.year ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: year.value, disabled: year.disabled, value: year.value }, year.text)))));\n    }\n    renderTimePickerColumns(forcePresentation) {\n        if (['date', 'month', 'month-year', 'year'].includes(forcePresentation)) {\n            return [];\n        }\n        /**\n         * If a user has not selected a date,\n         * then we should show all times. If the\n         * user has selected a date (even if it has\n         * not been confirmed yet), we should apply\n         * the max and min restrictions so that the\n         * time picker shows values that are\n         * appropriate for the selected date.\n         */\n        const activePart = this.getActivePart();\n        const userHasSelectedDate = activePart !== undefined;\n        const { hoursData, minutesData, dayPeriodData } = getTimeColumnsData(this.locale, this.workingParts, this.hourCycle, userHasSelectedDate ? this.minParts : undefined, userHasSelectedDate ? this.maxParts : undefined, this.parsedHourValues, this.parsedMinuteValues);\n        return [\n            this.renderHourPickerColumn(hoursData),\n            this.renderMinutePickerColumn(minutesData),\n            this.renderDayPeriodPickerColumn(dayPeriodData),\n        ];\n    }\n    renderHourPickerColumn(hoursData) {\n        const { disabled, workingParts } = this;\n        if (hoursData.length === 0)\n            return [];\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select an hour\", color: this.color, disabled: disabled, value: activePart.hour, numericInput: true, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { hour: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), { hour: ev.detail.value }));\n                ev.stopPropagation();\n            } }, hoursData.map((hour) => (h(\"ion-picker-column-option\", { part: hour.value === activePart.hour ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: hour.value, disabled: hour.disabled, value: hour.value }, hour.text)))));\n    }\n    renderMinutePickerColumn(minutesData) {\n        const { disabled, workingParts } = this;\n        if (minutesData.length === 0)\n            return [];\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a minute\", color: this.color, disabled: disabled, value: activePart.minute, numericInput: true, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { minute: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), { minute: ev.detail.value }));\n                ev.stopPropagation();\n            } }, minutesData.map((minute) => (h(\"ion-picker-column-option\", { part: minute.value === activePart.minute ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: minute.value, disabled: minute.disabled, value: minute.value }, minute.text)))));\n    }\n    renderDayPeriodPickerColumn(dayPeriodData) {\n        const { disabled, workingParts } = this;\n        if (dayPeriodData.length === 0) {\n            return [];\n        }\n        const activePart = this.getActivePartsWithFallback();\n        const isDayPeriodRTL = isLocaleDayPeriodRTL(this.locale);\n        return (h(\"ion-picker-column\", { \"aria-label\": \"Select a day period\", style: isDayPeriodRTL ? { order: '-1' } : {}, color: this.color, disabled: disabled, value: activePart.ampm, onIonChange: (ev) => {\n                const hour = calculateHourFromAMPM(workingParts, ev.detail.value);\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { ampm: ev.detail.value, hour }));\n                this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), { ampm: ev.detail.value, hour }));\n                ev.stopPropagation();\n            } }, dayPeriodData.map((dayPeriod) => (h(\"ion-picker-column-option\", { part: dayPeriod.value === activePart.ampm ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART, key: dayPeriod.value, disabled: dayPeriod.disabled, value: dayPeriod.value }, dayPeriod.text)))));\n    }\n    renderWheelView(forcePresentation) {\n        const { locale } = this;\n        const showMonthFirst = isMonthFirstLocale(locale);\n        const columnOrder = showMonthFirst ? 'month-first' : 'year-first';\n        return (h(\"div\", { class: {\n                [`wheel-order-${columnOrder}`]: true,\n            } }, this.renderWheelPicker(forcePresentation)));\n    }\n    /**\n     * Grid Render Methods\n     */\n    renderCalendarHeader(mode) {\n        const { disabled } = this;\n        const expandedIcon = mode === 'ios' ? chevronDown : caretUpSharp;\n        const collapsedIcon = mode === 'ios' ? chevronForward : caretDownSharp;\n        const prevMonthDisabled = disabled || isPrevMonthDisabled(this.workingParts, this.minParts, this.maxParts);\n        const nextMonthDisabled = disabled || isNextMonthDisabled(this.workingParts, this.maxParts);\n        // don't use the inheritAttributes util because it removes dir from the host, and we still need that\n        const hostDir = this.el.getAttribute('dir') || undefined;\n        return (h(\"div\", { class: \"calendar-header\" }, h(\"div\", { class: \"calendar-action-buttons\" }, h(\"div\", { class: \"calendar-month-year\" }, h(\"button\", { class: {\n                'calendar-month-year-toggle': true,\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }, part: \"month-year-button\", disabled: disabled, \"aria-label\": this.showMonthAndYear ? 'Hide year picker' : 'Show year picker', onClick: () => this.toggleMonthAndYearView() }, h(\"span\", { id: \"toggle-wrapper\" }, getMonthAndYear(this.locale, this.workingParts), h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: this.showMonthAndYear ? expandedIcon : collapsedIcon, lazy: false, flipRtl: true })), mode === 'md' && h(\"ion-ripple-effect\", null))), h(\"div\", { class: \"calendar-next-prev\" }, h(\"ion-buttons\", null, h(\"ion-button\", { \"aria-label\": \"Previous month\", disabled: prevMonthDisabled, onClick: () => this.prevMonth() }, h(\"ion-icon\", { dir: hostDir, \"aria-hidden\": \"true\", slot: \"icon-only\", icon: chevronBack, lazy: false, flipRtl: true })), h(\"ion-button\", { \"aria-label\": \"Next month\", disabled: nextMonthDisabled, onClick: () => this.nextMonth() }, h(\"ion-icon\", { dir: hostDir, \"aria-hidden\": \"true\", slot: \"icon-only\", icon: chevronForward, lazy: false, flipRtl: true }))))), h(\"div\", { class: \"calendar-days-of-week\", \"aria-hidden\": \"true\" }, getDaysOfWeek(this.locale, mode, this.firstDayOfWeek % 7).map((d) => {\n            return h(\"div\", { class: \"day-of-week\" }, d);\n        }))));\n    }\n    renderMonth(month, year) {\n        const { disabled, readonly } = this;\n        const yearAllowed = this.parsedYearValues === undefined || this.parsedYearValues.includes(year);\n        const monthAllowed = this.parsedMonthValues === undefined || this.parsedMonthValues.includes(month);\n        const isCalMonthDisabled = !yearAllowed || !monthAllowed;\n        const isDatetimeDisabled = disabled || readonly;\n        const swipeDisabled = disabled ||\n            isMonthDisabled({\n                month,\n                year,\n                day: null,\n            }, {\n                // The day is not used when checking if a month is disabled.\n                // Users should be able to access the min or max month, even if the\n                // min/max date is out of bounds (e.g. min is set to Feb 15, Feb should not be disabled).\n                minParts: Object.assign(Object.assign({}, this.minParts), { day: null }),\n                maxParts: Object.assign(Object.assign({}, this.maxParts), { day: null }),\n            });\n        // The working month should never have swipe disabled.\n        // Otherwise the CSS scroll snap will not work and the user\n        // can free-scroll the calendar.\n        const isWorkingMonth = this.workingParts.month === month && this.workingParts.year === year;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"div\", { \"aria-hidden\": !isWorkingMonth ? 'true' : null, class: {\n                'calendar-month': true,\n                // Prevents scroll snap swipe gestures for months outside of the min/max bounds\n                'calendar-month-disabled': !isWorkingMonth && swipeDisabled,\n            } }, h(\"div\", { class: \"calendar-month-grid\" }, getDaysOfMonth(month, year, this.firstDayOfWeek % 7, this.showAdjacentDays).map((dateObject, index) => {\n            const { day, dayOfWeek, isAdjacentDay } = dateObject;\n            const { el, highlightedDates, isDateEnabled, multiple, showAdjacentDays } = this;\n            let _month = month;\n            let _year = year;\n            if (showAdjacentDays && isAdjacentDay && day !== null) {\n                if (day > 20) {\n                    // Leading with the adjacent day from the previous month\n                    // if its a adjacent day and is higher than '20' (last week even in feb)\n                    if (month === 1) {\n                        _year = year - 1;\n                        _month = 12;\n                    }\n                    else {\n                        _month = month - 1;\n                    }\n                }\n                else if (day < 15) {\n                    // Leading with the adjacent day from the next month\n                    // if its a adjacent day and is lower than '15' (first two weeks)\n                    if (month === 12) {\n                        _year = year + 1;\n                        _month = 1;\n                    }\n                    else {\n                        _month = month + 1;\n                    }\n                }\n            }\n            const referenceParts = { month: _month, day, year: _year, isAdjacentDay };\n            const isCalendarPadding = day === null;\n            const { isActive, isToday, ariaLabel, ariaSelected, disabled: isDayDisabled, text, } = getCalendarDayState(this.locale, referenceParts, this.activeParts, this.todayParts, this.minParts, this.maxParts, this.parsedDayValues);\n            const dateIsoString = convertDataToISO(referenceParts);\n            let isCalDayDisabled = isCalMonthDisabled || isDayDisabled;\n            if (!isCalDayDisabled && isDateEnabled !== undefined) {\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    isCalDayDisabled = !isDateEnabled(dateIsoString);\n                }\n                catch (e) {\n                    printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', el, e);\n                }\n            }\n            /**\n             * Some days are constrained through max & min or allowed dates\n             * and also disabled because the component is readonly or disabled.\n             * These need to be displayed differently.\n             */\n            const isCalDayConstrained = isCalDayDisabled && isDatetimeDisabled;\n            const isButtonDisabled = isCalDayDisabled || isDatetimeDisabled;\n            let dateStyle = undefined;\n            /**\n             * Custom highlight styles should not override the style for selected dates,\n             * nor apply to \"filler days\" at the start of the grid.\n             */\n            if (highlightedDates !== undefined && !isActive && day !== null && !isAdjacentDay) {\n                dateStyle = getHighlightStyles(highlightedDates, dateIsoString, el);\n            }\n            let dateParts = undefined;\n            // \"Filler days\" at the beginning of the grid should not get the calendar day\n            // CSS parts added to them\n            if (!isCalendarPadding && !isAdjacentDay) {\n                dateParts = `calendar-day${isActive ? ' active' : ''}${isToday ? ' today' : ''}${isCalDayDisabled ? ' disabled' : ''}`;\n            }\n            else if (isAdjacentDay) {\n                dateParts = `calendar-day${isCalDayDisabled ? ' disabled' : ''}`;\n            }\n            return (h(\"div\", { class: \"calendar-day-wrapper\" }, h(\"button\", {\n                // We need to use !important for the inline styles here because\n                // otherwise the CSS shadow parts will override these styles.\n                // See https://github.com/WICG/webcomponents/issues/847\n                // Both the CSS shadow parts and highlightedDates styles are\n                // provided by the developer, but highlightedDates styles should\n                // always take priority.\n                ref: (el) => {\n                    if (el) {\n                        el.style.setProperty('color', `${dateStyle ? dateStyle.textColor : ''}`, 'important');\n                        el.style.setProperty('background-color', `${dateStyle ? dateStyle.backgroundColor : ''}`, 'important');\n                    }\n                }, tabindex: \"-1\", \"data-day\": day, \"data-month\": _month, \"data-year\": _year, \"data-index\": index, \"data-day-of-week\": dayOfWeek, disabled: isButtonDisabled, class: {\n                    'calendar-day-padding': isCalendarPadding,\n                    'calendar-day': true,\n                    'calendar-day-active': isActive,\n                    'calendar-day-constrained': isCalDayConstrained,\n                    'calendar-day-today': isToday,\n                    'calendar-day-adjacent-day': isAdjacentDay,\n                }, part: dateParts, \"aria-hidden\": isCalendarPadding ? 'true' : null, \"aria-selected\": ariaSelected, \"aria-label\": ariaLabel, onClick: () => {\n                    if (isCalendarPadding) {\n                        return;\n                    }\n                    if (isAdjacentDay) {\n                        // The user selected a day outside the current month. Ignore this button, as the month will be re-rendered.\n                        this.el.blur();\n                        this.activeParts = Object.assign(Object.assign({}, activePart), referenceParts);\n                        this.animateToDate(referenceParts);\n                        this.confirm();\n                    }\n                    else {\n                        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), referenceParts));\n                        // Multiple only needs date info so we can wipe out other fields like time.\n                        if (multiple) {\n                            this.setActiveParts(referenceParts, isActive);\n                        }\n                        else {\n                            this.setActiveParts(Object.assign(Object.assign({}, activePart), referenceParts));\n                        }\n                    }\n                }\n            }, text)));\n        }))));\n    }\n    renderCalendarBody() {\n        return (h(\"div\", { class: \"calendar-body ion-focusable\", ref: (el) => (this.calendarBodyRef = el), tabindex: \"0\" }, generateMonths(this.workingParts, this.forceRenderDate).map(({ month, year }) => {\n            return this.renderMonth(month, year);\n        })));\n    }\n    renderCalendar(mode) {\n        return (h(\"div\", { class: \"datetime-calendar\", key: \"datetime-calendar\" }, this.renderCalendarHeader(mode), this.renderCalendarBody()));\n    }\n    renderTimeLabel() {\n        const hasSlottedTimeLabel = this.el.querySelector('[slot=\"time-label\"]') !== null;\n        if (!hasSlottedTimeLabel && !this.showDefaultTimeLabel) {\n            return;\n        }\n        return h(\"slot\", { name: \"time-label\" }, \"Time\");\n    }\n    renderTimeOverlay() {\n        const { disabled, hourCycle, isTimePopoverOpen, locale, formatOptions } = this;\n        const computedHourCycle = getHourCycle(locale, hourCycle);\n        const activePart = this.getActivePartsWithFallback();\n        return [\n            h(\"div\", { class: \"time-header\" }, this.renderTimeLabel()),\n            h(\"button\", { class: {\n                    'time-body': true,\n                    'time-body-active': isTimePopoverOpen,\n                }, part: `time-button${isTimePopoverOpen ? ' active' : ''}`, \"aria-expanded\": \"false\", \"aria-haspopup\": \"true\", disabled: disabled, onClick: async (ev) => {\n                    const { popoverRef } = this;\n                    if (popoverRef) {\n                        this.isTimePopoverOpen = true;\n                        popoverRef.present(new CustomEvent('ionShadowTarget', {\n                            detail: {\n                                ionShadowTarget: ev.target,\n                            },\n                        }));\n                        await popoverRef.onWillDismiss();\n                        this.isTimePopoverOpen = false;\n                    }\n                } }, getLocalizedTime(locale, activePart, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time)),\n            h(\"ion-popover\", { alignment: \"center\", translucent: true, overlayIndex: 1, arrow: false, onWillPresent: (ev) => {\n                    /**\n                     * Intersection Observers do not consistently fire between Blink and Webkit\n                     * when toggling the visibility of the popover and trying to scroll the picker\n                     * column to the correct time value.\n                     *\n                     * This will correctly scroll the element position to the correct time value,\n                     * before the popover is fully presented.\n                     */\n                    const cols = ev.target.querySelectorAll('ion-picker-column');\n                    // TODO (FW-615): Potentially remove this when intersection observers are fixed in picker column\n                    cols.forEach((col) => col.scrollActiveItemIntoView());\n                }, style: {\n                    '--offset-y': '-10px',\n                    '--min-width': 'fit-content',\n                },\n                // Allow native browser keyboard events to support up/down/home/<USER>\n                // navigation within the time picker.\n                keyboardEvents: true, ref: (el) => (this.popoverRef = el) }, this.renderWheelPicker('time')),\n        ];\n    }\n    getHeaderSelectedDateText() {\n        var _a;\n        const { activeParts, formatOptions, multiple, titleSelectedDatesFormatter } = this;\n        const isArray = Array.isArray(activeParts);\n        let headerText;\n        if (multiple && isArray && activeParts.length !== 1) {\n            headerText = `${activeParts.length} days`; // default/fallback for multiple selection\n            if (titleSelectedDatesFormatter !== undefined) {\n                try {\n                    headerText = titleSelectedDatesFormatter(convertDataToISO(activeParts));\n                }\n                catch (e) {\n                    printIonError('[ion-datetime] - Exception in provided `titleSelectedDatesFormatter`:', e);\n                }\n            }\n        }\n        else {\n            // for exactly 1 day selected (multiple set or not), show a formatted version of that\n            headerText = getLocalizedDateTime(this.locale, this.getActivePartsWithFallback(), (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : { weekday: 'short', month: 'short', day: 'numeric' });\n        }\n        return headerText;\n    }\n    renderHeader(showExpandedHeader = true) {\n        const hasSlottedTitle = this.el.querySelector('[slot=\"title\"]') !== null;\n        if (!hasSlottedTitle && !this.showDefaultTitle) {\n            return;\n        }\n        return (h(\"div\", { class: \"datetime-header\" }, h(\"div\", { class: \"datetime-title\" }, h(\"slot\", { name: \"title\" }, \"Select Date\")), showExpandedHeader && h(\"div\", { class: \"datetime-selected-date\" }, this.getHeaderSelectedDateText())));\n    }\n    /**\n     * Render time picker inside of datetime.\n     * Do not pass color prop to segment on\n     * iOS mode. MD segment has been customized and\n     * should take on the color prop, but iOS\n     * should just be the default segment.\n     */\n    renderTime() {\n        const { presentation } = this;\n        const timeOnlyPresentation = presentation === 'time';\n        return (h(\"div\", { class: \"datetime-time\" }, timeOnlyPresentation ? this.renderWheelPicker() : this.renderTimeOverlay()));\n    }\n    /**\n     * Renders the month/year picker that is\n     * displayed on the calendar grid.\n     * The .datetime-year class has additional\n     * styles that let us show/hide the\n     * picker when the user clicks on the\n     * toggle in the calendar header.\n     */\n    renderCalendarViewMonthYearPicker() {\n        return h(\"div\", { class: \"datetime-year\" }, this.renderWheelView('month-year'));\n    }\n    /**\n     * Render entry point\n     * All presentation types are rendered from here.\n     */\n    renderDatetime(mode) {\n        const { presentation, preferWheel } = this;\n        /**\n         * Certain presentation types have separate grid and wheel displays.\n         * If preferWheel is true then we should show a wheel picker instead.\n         */\n        const hasWheelVariant = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        if (preferWheel && hasWheelVariant) {\n            return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n        }\n        switch (presentation) {\n            case 'date-time':\n                return [\n                    this.renderHeader(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderTime(),\n                    this.renderFooter(),\n                ];\n            case 'time-date':\n                return [\n                    this.renderHeader(),\n                    this.renderTime(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderFooter(),\n                ];\n            case 'time':\n                return [this.renderHeader(false), this.renderTime(), this.renderFooter()];\n            case 'month':\n            case 'month-year':\n            case 'year':\n                return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n            default:\n                return [\n                    this.renderHeader(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderFooter(),\n                ];\n        }\n    }\n    render() {\n        const { name, value, disabled, el, color, readonly, showMonthAndYear, preferWheel, presentation, size, isGridStyle, } = this;\n        const mode = getIonMode(this);\n        const isMonthAndYearPresentation = presentation === 'year' || presentation === 'month' || presentation === 'month-year';\n        const shouldShowMonthAndYear = showMonthAndYear || isMonthAndYearPresentation;\n        const monthYearPickerOpen = showMonthAndYear && !isMonthAndYearPresentation;\n        const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        const hasWheelVariant = hasDatePresentation && preferWheel;\n        renderHiddenInput(true, el, name, formatValue(value), disabled);\n        return (h(Host, { key: '79677f5bc0fb32fb68569636bd76e68238e62eb8', \"aria-disabled\": disabled ? 'true' : null, onFocus: this.onFocus, onBlur: this.onBlur, class: Object.assign({}, createColorClasses(color, {\n                [mode]: true,\n                ['datetime-readonly']: readonly,\n                ['datetime-disabled']: disabled,\n                'show-month-and-year': shouldShowMonthAndYear,\n                'month-year-picker-open': monthYearPickerOpen,\n                [`datetime-presentation-${presentation}`]: true,\n                [`datetime-size-${size}`]: true,\n                [`datetime-prefer-wheel`]: hasWheelVariant,\n                [`datetime-grid`]: isGridStyle,\n            })) }, h(\"div\", { key: 'bf07b1e3c64af6e837663ff470bea93787a6e86f', class: \"intersection-tracker\", ref: (el) => (this.intersectionTrackerRef = el) }), this.renderDatetime(mode)));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"formatOptions\": [\"formatOptionsChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"presentation\": [\"presentationChanged\"],\n        \"yearValues\": [\"yearValuesChanged\"],\n        \"monthValues\": [\"monthValuesChanged\"],\n        \"dayValues\": [\"dayValuesChanged\"],\n        \"hourValues\": [\"hourValuesChanged\"],\n        \"minuteValues\": [\"minuteValuesChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet datetimeIds = 0;\nconst CANCEL_ROLE = 'datetime-cancel';\nconst CONFIRM_ROLE = 'datetime-confirm';\nconst WHEEL_ITEM_PART = 'wheel-item';\nconst WHEEL_ITEM_ACTIVE_PART = `active`;\nDatetime.style = {\n    ios: datetimeIosCss,\n    md: datetimeMdCss\n};\n\n/**\n * iOS Picker Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.picker-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Picker Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 'var(--backdrop-opacity)', 0.01);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.picker-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst pickerIosCss = \".sc-ion-picker-legacy-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-ios-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-ios-h{display:none}.picker-wrapper.sc-ion-picker-legacy-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-ios:active,.picker-button.sc-ion-picker-legacy-ios:focus{outline:none}.picker-columns.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-ios,.picker-below-highlight.sc-ion-picker-legacy-ios{display:none;pointer-events:none}.sc-ion-picker-legacy-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-legacy-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-legacy-ios:last-child .picker-button.sc-ion-picker-legacy-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-legacy-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-legacy-ios,.picker-button.ion-activated.sc-ion-picker-legacy-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:16px}.picker-columns.sc-ion-picker-legacy-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-legacy-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}\";\n\nconst pickerMdCss = \".sc-ion-picker-legacy-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-md-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-md-h{display:none}.picker-wrapper.sc-ion-picker-legacy-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-md:active,.picker-button.sc-ion-picker-legacy-md:focus{outline:none}.picker-columns.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-md,.picker-below-highlight.sc-ion-picker-legacy-md{display:none;pointer-events:none}.sc-ion-picker-legacy-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-legacy-md,.picker-button.ion-activated.sc-ion-picker-legacy-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-legacy-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-legacy-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}\";\n\nconst Picker = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionPickerDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionPickerWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionPickerWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionPickerDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * Array of buttons to be displayed at the top of the picker.\n         */\n        this.buttons = [];\n        /**\n         * Array of columns to be displayed in the picker.\n         */\n        this.columns = [];\n        /**\n         * Number of milliseconds to wait before dismissing the picker.\n         */\n        this.duration = 0;\n        /**\n         * If `true`, a backdrop will be displayed behind the picker.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the picker will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, the picker will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the picker will open. If `false`, the picker will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the pickerController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the picker dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.buttons.find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        var _a;\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        printIonWarning('[ion-picker-legacy] - ion-picker-legacy and ion-picker-legacy-column have been deprecated in favor of new versions of the ion-picker and ion-picker-column components. These new components display inline with your page content allowing for more presentation flexibility than before.', this.el);\n        /**\n         * If picker was rendered with isOpen=\"true\"\n         * then we should open picker immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Present the picker overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'pickerEnter', iosEnterAnimation, iosEnterAnimation, undefined);\n        if (this.duration > 0) {\n            this.durationTimeout = setTimeout(() => this.dismiss(), this.duration);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the picker overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the picker.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the picker.\n     * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        if (this.durationTimeout) {\n            clearTimeout(this.durationTimeout);\n        }\n        const dismissed = await dismiss(this, data, role, 'pickerLeave', iosLeaveAnimation, iosLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the picker did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionPickerDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the picker will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionPickerWillDismiss');\n    }\n    /**\n     * Get the column that matches the specified name.\n     *\n     * @param name The name of the column.\n     */\n    getColumn(name) {\n        return Promise.resolve(this.columns.find((column) => column.name === name));\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        if (isCancel(role)) {\n            return this.dismiss(undefined, role);\n        }\n        const shouldDismiss = await this.callButtonHandler(button);\n        if (shouldDismiss) {\n            return this.dismiss(this.getSelected(), button.role);\n        }\n        return Promise.resolve();\n    }\n    async callButtonHandler(button) {\n        if (button) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const rtn = await safeCall(button.handler, this.getSelected());\n            if (rtn === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n        }\n        return true;\n    }\n    getSelected() {\n        const selected = {};\n        this.columns.forEach((col, index) => {\n            const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined;\n            selected[col.name] = {\n                text: selectedColumn ? selectedColumn.text : undefined,\n                value: selectedColumn ? selectedColumn.value : undefined,\n                columnIndex: index,\n            };\n        });\n        return selected;\n    }\n    render() {\n        const { htmlAttributes } = this;\n        const mode = getIonMode(this);\n        return (h(Host, Object.assign({ key: 'b95440747eb80cba23ae676d399d5e5816722c58', \"aria-modal\": \"true\", tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign({ [mode]: true,\n                // Used internally for styling\n                [`picker-${mode}`]: true, 'overlay-hidden': true }, getClassMap(this.cssClass)), onIonBackdropTap: this.onBackdropTap, onIonPickerWillDismiss: this.dispatchCancelHandler }), h(\"ion-backdrop\", { key: '169d1c83ef40e7fcb134219a585298b403a70b0f', visible: this.showBackdrop, tappable: this.backdropDismiss }), h(\"div\", { key: '98518e5f5cea2dfb8dfa63d9545e9ae3a5765023', tabindex: \"0\", \"aria-hidden\": \"true\" }), h(\"div\", { key: '151755ab8eb23f9adafbfe201349398f5a69dee7', class: \"picker-wrapper ion-overlay-wrapper\", role: \"dialog\" }, h(\"div\", { key: '5dcf93b2f4fe8f4fce7c7aec8f85ef45a03ef470', class: \"picker-toolbar\" }, this.buttons.map((b) => (h(\"div\", { class: buttonWrapperClass(b) }, h(\"button\", { type: \"button\", onClick: () => this.buttonClick(b), class: buttonClass(b) }, b.text))))), h(\"div\", { key: 'fd5d66708edd38adc5a4d2fad7298969398a05e3', class: \"picker-columns\" }, h(\"div\", { key: '1b5830fd6cef1016af7736792c514965d6cb38a8', class: \"picker-above-highlight\" }), this.presented && this.columns.map((c) => h(\"ion-picker-legacy-column\", { col: c })), h(\"div\", { key: 'c6edeca7afd69e13c9c66ba36f261974fd0f8f78', class: \"picker-below-highlight\" }))), h(\"div\", { key: 'e2a4b24710e30579b14b82dbfd3761b2187797b5', tabindex: \"0\", \"aria-hidden\": \"true\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst buttonWrapperClass = (button) => {\n    return {\n        [`picker-toolbar-${button.role}`]: button.role !== undefined,\n        'picker-toolbar-button': true,\n    };\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'picker-button': true, 'ion-activatable': true }, getClassMap(button.cssClass));\n};\nPicker.style = {\n    ios: pickerIosCss,\n    md: pickerMdCss\n};\n\nconst pickerColumnIosCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}\";\n\nconst pickerColumnMdCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #0054e9)}\";\n\nconst PickerColumnCmp = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionPickerColChange = createEvent(this, \"ionPickerColChange\", 7);\n        this.optHeight = 0;\n        this.rotateFactor = 0;\n        this.scaleFactor = 1;\n        this.velocity = 0;\n        this.y = 0;\n        this.noAnimate = true;\n        // `colDidChange` is a flag that gets set when the column is changed\n        // dynamically. When this flag is set, the column will refresh\n        // after the component re-renders to incorporate the new column data.\n        // This is necessary because `this.refresh` queries for the option elements,\n        // so it needs to wait for the latest elements to be available in the DOM.\n        // Ex: column is created with 3 options. User updates the column data\n        // to have 5 options. The column will still think it only has 3 options.\n        this.colDidChange = false;\n    }\n    colChanged() {\n        this.colDidChange = true;\n    }\n    async connectedCallback() {\n        let pickerRotateFactor = 0;\n        let pickerScaleFactor = 0.81;\n        const mode = getIonMode(this);\n        if (mode === 'ios') {\n            pickerRotateFactor = -0.46;\n            pickerScaleFactor = 1;\n        }\n        this.rotateFactor = pickerRotateFactor;\n        this.scaleFactor = pickerScaleFactor;\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el: this.el,\n            gestureName: 'picker-swipe',\n            gesturePriority: 100,\n            threshold: 0,\n            passive: false,\n            onStart: (ev) => this.onStart(ev),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.gesture.enable();\n        // Options have not been initialized yet\n        // Animation must be disabled through the `noAnimate` flag\n        // Otherwise, the options will render\n        // at the top of the column and transition down\n        this.tmrId = setTimeout(() => {\n            this.noAnimate = false;\n            // After initialization, `refresh()` will be called\n            // At this point, animation will be enabled. The options will\n            // animate as they are being selected.\n            this.refresh(true);\n        }, 250);\n    }\n    componentDidLoad() {\n        this.onDomChange();\n    }\n    componentDidUpdate() {\n        // Options may have changed since last update.\n        if (this.colDidChange) {\n            // Animation must be disabled through the `onDomChange` parameter.\n            // Otherwise, the recently added options will render\n            // at the top of the column and transition down\n            this.onDomChange(true, false);\n            this.colDidChange = false;\n        }\n    }\n    disconnectedCallback() {\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        if (this.tmrId)\n            clearTimeout(this.tmrId);\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    emitColChange() {\n        this.ionPickerColChange.emit(this.col);\n    }\n    setSelected(selectedIndex, duration) {\n        // if there is a selected index, then figure out it's y position\n        // if there isn't a selected index, then just use the top y position\n        const y = selectedIndex > -1 ? -(selectedIndex * this.optHeight) : 0;\n        this.velocity = 0;\n        // set what y position we're at\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        this.update(y, duration, true);\n        this.emitColChange();\n    }\n    update(y, duration, saveY) {\n        if (!this.optsEl) {\n            return;\n        }\n        // ensure we've got a good round number :)\n        let translateY = 0;\n        let translateZ = 0;\n        const { col, rotateFactor } = this;\n        const prevSelected = col.selectedIndex;\n        const selectedIndex = (col.selectedIndex = this.indexForY(-y));\n        const durationStr = duration === 0 ? '' : duration + 'ms';\n        const scaleStr = `scale(${this.scaleFactor})`;\n        const children = this.optsEl.children;\n        for (let i = 0; i < children.length; i++) {\n            const button = children[i];\n            const opt = col.options[i];\n            const optOffset = i * this.optHeight + y;\n            let transform = '';\n            if (rotateFactor !== 0) {\n                const rotateX = optOffset * rotateFactor;\n                if (Math.abs(rotateX) <= 90) {\n                    translateY = 0;\n                    translateZ = 90;\n                    transform = `rotateX(${rotateX}deg) `;\n                }\n                else {\n                    translateY = -9999;\n                }\n            }\n            else {\n                translateZ = 0;\n                translateY = optOffset;\n            }\n            const selected = selectedIndex === i;\n            transform += `translate3d(0px,${translateY}px,${translateZ}px) `;\n            if (this.scaleFactor !== 1 && !selected) {\n                transform += scaleStr;\n            }\n            // Update transition duration\n            if (this.noAnimate) {\n                opt.duration = 0;\n                button.style.transitionDuration = '';\n            }\n            else if (duration !== opt.duration) {\n                opt.duration = duration;\n                button.style.transitionDuration = durationStr;\n            }\n            // Update transform\n            if (transform !== opt.transform) {\n                opt.transform = transform;\n            }\n            button.style.transform = transform;\n            /**\n             * Ensure that the select column\n             * item has the selected class\n             */\n            opt.selected = selected;\n            if (selected) {\n                button.classList.add(PICKER_OPT_SELECTED);\n            }\n            else {\n                button.classList.remove(PICKER_OPT_SELECTED);\n            }\n        }\n        this.col.prevSelected = prevSelected;\n        if (saveY) {\n            this.y = y;\n        }\n        if (this.lastIndex !== selectedIndex) {\n            // have not set a last index yet\n            hapticSelectionChanged();\n            this.lastIndex = selectedIndex;\n        }\n    }\n    decelerate() {\n        if (this.velocity !== 0) {\n            // still decelerating\n            this.velocity *= DECELERATION_FRICTION;\n            // do not let it go slower than a velocity of 1\n            this.velocity = this.velocity > 0 ? Math.max(this.velocity, 1) : Math.min(this.velocity, -1);\n            let y = this.y + this.velocity;\n            if (y > this.minY) {\n                // whoops, it's trying to scroll up farther than the options we have!\n                y = this.minY;\n                this.velocity = 0;\n            }\n            else if (y < this.maxY) {\n                // gahh, it's trying to scroll down farther than we can!\n                y = this.maxY;\n                this.velocity = 0;\n            }\n            this.update(y, 0, true);\n            const notLockedIn = Math.round(y) % this.optHeight !== 0 || Math.abs(this.velocity) > 1;\n            if (notLockedIn) {\n                // isn't locked in yet, keep decelerating until it is\n                this.rafId = requestAnimationFrame(() => this.decelerate());\n            }\n            else {\n                this.velocity = 0;\n                this.emitColChange();\n                hapticSelectionEnd();\n            }\n        }\n        else if (this.y % this.optHeight !== 0) {\n            // needs to still get locked into a position so options line up\n            const currentPos = Math.abs(this.y % this.optHeight);\n            // create a velocity in the direction it needs to scroll\n            this.velocity = currentPos > this.optHeight / 2 ? 1 : -1;\n            this.decelerate();\n        }\n    }\n    indexForY(y) {\n        return Math.min(Math.max(Math.abs(Math.round(y / this.optHeight)), 0), this.col.options.length - 1);\n    }\n    onStart(detail) {\n        // We have to prevent default in order to block scrolling under the picker\n        // but we DO NOT have to stop propagation, since we still want\n        // some \"click\" events to capture\n        if (detail.event.cancelable) {\n            detail.event.preventDefault();\n        }\n        detail.event.stopPropagation();\n        hapticSelectionStart();\n        // reset everything\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        const options = this.col.options;\n        let minY = options.length - 1;\n        let maxY = 0;\n        for (let i = 0; i < options.length; i++) {\n            if (!options[i].disabled) {\n                minY = Math.min(minY, i);\n                maxY = Math.max(maxY, i);\n            }\n        }\n        this.minY = -(minY * this.optHeight);\n        this.maxY = -(maxY * this.optHeight);\n    }\n    onMove(detail) {\n        if (detail.event.cancelable) {\n            detail.event.preventDefault();\n        }\n        detail.event.stopPropagation();\n        // update the scroll position relative to pointer start position\n        let y = this.y + detail.deltaY;\n        if (y > this.minY) {\n            // scrolling up higher than scroll area\n            y = Math.pow(y, 0.8);\n            this.bounceFrom = y;\n        }\n        else if (y < this.maxY) {\n            // scrolling down below scroll area\n            y += Math.pow(this.maxY - y, 0.9);\n            this.bounceFrom = y;\n        }\n        else {\n            this.bounceFrom = 0;\n        }\n        this.update(y, 0, false);\n    }\n    onEnd(detail) {\n        if (this.bounceFrom > 0) {\n            // bounce back up\n            this.update(this.minY, 100, true);\n            this.emitColChange();\n            return;\n        }\n        else if (this.bounceFrom < 0) {\n            // bounce back down\n            this.update(this.maxY, 100, true);\n            this.emitColChange();\n            return;\n        }\n        this.velocity = clamp(-90, detail.velocityY * 23, MAX_PICKER_SPEED);\n        if (this.velocity === 0 && detail.deltaY === 0) {\n            const opt = detail.event.target.closest('.picker-opt');\n            if (opt === null || opt === void 0 ? void 0 : opt.hasAttribute('opt-index')) {\n                this.setSelected(parseInt(opt.getAttribute('opt-index'), 10), TRANSITION_DURATION);\n            }\n        }\n        else {\n            this.y += detail.deltaY;\n            if (Math.abs(detail.velocityY) < 0.05) {\n                const isScrollingUp = detail.deltaY > 0;\n                const optHeightFraction = (Math.abs(this.y) % this.optHeight) / this.optHeight;\n                if (isScrollingUp && optHeightFraction > 0.5) {\n                    this.velocity = Math.abs(this.velocity) * -1;\n                }\n                else if (!isScrollingUp && optHeightFraction <= 0.5) {\n                    this.velocity = Math.abs(this.velocity);\n                }\n            }\n            this.decelerate();\n        }\n    }\n    refresh(forceRefresh, animated) {\n        var _a;\n        let min = this.col.options.length - 1;\n        let max = 0;\n        const options = this.col.options;\n        for (let i = 0; i < options.length; i++) {\n            if (!options[i].disabled) {\n                min = Math.min(min, i);\n                max = Math.max(max, i);\n            }\n        }\n        /**\n         * Only update selected value if column has a\n         * velocity of 0. If it does not, then the\n         * column is animating might land on\n         * a value different than the value at\n         * selectedIndex\n         */\n        if (this.velocity !== 0) {\n            return;\n        }\n        const selectedIndex = clamp(min, (_a = this.col.selectedIndex) !== null && _a !== void 0 ? _a : 0, max);\n        if (this.col.prevSelected !== selectedIndex || forceRefresh) {\n            const y = selectedIndex * this.optHeight * -1;\n            const duration = animated ? TRANSITION_DURATION : 0;\n            this.velocity = 0;\n            this.update(y, duration, true);\n        }\n    }\n    onDomChange(forceRefresh, animated) {\n        const colEl = this.optsEl;\n        if (colEl) {\n            // DOM READ\n            // We perfom a DOM read over a rendered item, this needs to happen after the first render or after the column has changed\n            this.optHeight = colEl.firstElementChild ? colEl.firstElementChild.clientHeight : 0;\n        }\n        this.refresh(forceRefresh, animated);\n    }\n    render() {\n        const col = this.col;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'ed32d108dd94f0302fb453c31a3497ebae65ec37', class: Object.assign({ [mode]: true, 'picker-col': true, 'picker-opts-left': this.col.align === 'left', 'picker-opts-right': this.col.align === 'right' }, getClassMap(col.cssClass)), style: {\n                'max-width': this.col.columnWidth,\n            } }, col.prefix && (h(\"div\", { key: '9f0634890e66fd4ae74f826d1eea3431de121393', class: \"picker-prefix\", style: { width: col.prefixWidth } }, col.prefix)), h(\"div\", { key: '337e996e5be91af16446085fe22436f213b771eb', class: \"picker-opts\", style: { maxWidth: col.optionsWidth }, ref: (el) => (this.optsEl = el) }, col.options.map((o, index) => (h(\"button\", { \"aria-label\": o.ariaLabel, class: { 'picker-opt': true, 'picker-opt-disabled': !!o.disabled }, \"opt-index\": index }, o.text)))), col.suffix && (h(\"div\", { key: 'd69a132599d78d9e5107f12228978cfce4e43098', class: \"picker-suffix\", style: { width: col.suffixWidth } }, col.suffix))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"col\": [\"colChanged\"]\n    }; }\n};\nconst PICKER_OPT_SELECTED = 'picker-opt-selected';\nconst DECELERATION_FRICTION = 0.97;\nconst MAX_PICKER_SPEED = 90;\nconst TRANSITION_DURATION = 150;\nPickerColumnCmp.style = {\n    ios: pickerColumnIosCss,\n    md: pickerColumnMdCss\n};\n\nexport { Datetime as ion_datetime, Picker as ion_picker_legacy, PickerColumnCmp as ion_picker_legacy_column };\n"], "names": ["o", "printIonError", "m", "printIonWarning", "r", "registerInstance", "d", "createEvent", "e", "getIonMode", "w", "writeTask", "h", "j", "Host", "k", "getElement", "startFocusVisible", "raf", "g", "getElementRoot", "a", "renderHiddenInput", "clamp", "i", "isRTL", "c", "createColorClasses", "getClassMap", "l", "chevronDown", "caretUpSharp", "p", "chevronForward", "q", "caretDownSharp", "chevronBack", "isBefore", "isAfter", "getPrevious<PERSON><PERSON>h", "b", "getNextMonth", "isSameDay", "getDay", "generateDayAriaLabel", "v", "validateParts", "f", "getPartsFromCalendarDay", "getNextYear", "getPreviousYear", "getEndOfWeek", "getStartOfWeek", "getPreviousDay", "n", "getNextDay", "getPreviousWeek", "getNextWeek", "parseMinParts", "parseMaxParts", "s", "parseDate", "warnIfValueOutOfBounds", "t", "parseAmPm", "u", "clampDate", "x", "convertToArrayOfNumbers", "y", "convertDataToISO", "z", "get<PERSON><PERSON>y", "A", "getClosestValidDate", "B", "generateMonths", "C", "getNumDaysInMonth", "D", "getCombinedDateColumnData", "E", "getMonthColumnData", "F", "getDayColumnData", "G", "getYearColumnData", "H", "isMonthFirstLocale", "I", "getTimeColumnsData", "J", "isLocaleDayPeriodRTL", "K", "calculateHourFromAMPM", "L", "getDaysOfWeek", "M", "getMonthAndYear", "N", "getDaysOfMonth", "O", "getHourCycle", "P", "getLocalizedTime", "Q", "getLocalizedDateTime", "R", "formatValue", "createLockController", "createDelegateController", "createTriggerController", "BACKDROP", "isCancel", "prepareOverlay", "setOverlayId", "present", "dismiss", "eventMethod", "safeCall", "createAnimation", "hapticSelectionChanged", "hapticSelectionEnd", "hapticSelectionStart", "isYearDisabled", "refYear", "minParts", "maxParts", "year", "isDayDisabled", "refParts", "dayV<PERSON><PERSON>", "day", "undefined", "includes", "getCalendarDayState", "locale", "activeParts", "todayParts", "activePartsArray", "Array", "isArray", "isActive", "find", "parts", "isToday", "disabled", "ariaSelected", "aria<PERSON><PERSON><PERSON>", "text", "isMonthDisabled", "isPrevMonthDisabled", "prevMonth", "Object", "assign", "isNextMonthDisabled", "nextMonth", "getHighlightStyles", "highlightedDates", "dateIsoString", "el", "dateStringWithoutTime", "split", "matching<PERSON>ighlight", "hd", "date", "textColor", "backgroundColor", "warnIfTimeZoneProvided", "formatOptions", "_a", "_b", "_c", "_d", "timeZone", "timeZoneName", "time", "checkForPresentationFormatMismatch", "presentation", "datetimeIosCss", "datetimeMdCss", "Datetime", "constructor", "hostRef", "_this", "ionCancel", "ionChange", "ionValueChange", "ionFocus", "ionBlur", "ionStyle", "ionRender", "inputId", "datetimeIds", "prevPresentation", "showMonthAndYear", "workingParts", "month", "hour", "minute", "ampm", "isAdjacentDay", "isTimePopoverOpen", "color", "name", "readonly", "showAdjacentDays", "cancelText", "doneText", "clearText", "firstDayOfWeek", "multiple", "showDefaultTitle", "showDefaultButtons", "showClearButton", "showDefaultTimeLabel", "size", "preferWheel", "warnIfIncorrectValueUsage", "value", "map", "join", "setValue", "emit", "getActivePartsWithFallback", "defaultParts", "getActivePart", "closeParentOverlay", "role", "popoverOrModal", "closest", "setWorkingParts", "setActiveParts", "removeDate", "validatedParts", "filter", "hasSlottedButtons", "querySelector", "confirm", "initializeKeyboardListeners", "calendarBodyRef", "root", "shadowRoot", "currentMonth", "checkCalendarBodyFocus", "ev", "record", "oldValue", "classList", "contains", "focusWorkingDay", "mo", "MutationObserver", "observe", "attributeFilter", "attributeOldValue", "destroyKeyboardMO", "disconnect", "addEventListener", "activeElement", "partsToFocus", "key", "preventDefault", "shift<PERSON>ey", "requestAnimationFrame", "firstOfMonth", "Date", "offset", "dayEl", "focus", "processMinParts", "min", "processMaxParts", "max", "initializeCalendarListener", "months", "querySelectorAll", "startMonth", "workingMonth", "endMonth", "mode", "needsiOSRubberBandFix", "navigator", "maxTouchPoints", "scrollLeft", "clientWidth", "getChangedMonth", "box", "getBoundingClientRect", "condition", "monthBox", "Math", "abs", "forceRenderDate", "updateActiveMonth", "style", "removeProperty", "appliediOSRubberBandFix", "newDate", "setProperty", "resolveForceDateScrolling", "scrollTimeout", "scrollCallback", "clearTimeout", "setTimeout", "destroyCalendarListener", "removeEventListener", "destroyInteractionListeners", "processValue", "hasValue", "length", "valueToProcess", "singleValue", "targetValue", "didChange<PERSON>onth", "bodyIsVisible", "isGridStyle", "animateToDate", "_ref", "_asyncToGenerator", "forceDateScrollingPromise", "Promise", "resolve", "targetMonthIsBefore", "_x", "apply", "arguments", "onFocus", "onBlur", "left", "offsetWidth", "scrollTo", "top", "behavior", "toggleMonthAndYearView", "formatOptionsChanged", "disabled<PERSON><PERSON>ed", "emitStyle", "minC<PERSON>ed", "max<PERSON><PERSON>ed", "presentationChanged", "hasDatePresentation", "yearV<PERSON><PERSON><PERSON><PERSON>ed", "parsedYear<PERSON><PERSON>ues", "yearValues", "month<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "parsed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "month<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed<PERSON><PERSON><PERSON><PERSON><PERSON>", "hourValuesChanged", "parsedHourValues", "hourValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed<PERSON><PERSON>ute<PERSON><PERSON><PERSON>", "minute<PERSON><PERSON><PERSON>", "valueChanged", "_this2", "_this3", "closeOverlay", "isCalendarPicker", "activePartsIsArray", "CONFIRM_ROLE", "reset", "startDate", "_this4", "cancel", "_this5", "CANCEL_ROLE", "connectedCallback", "clearFocusVisible", "destroy", "disconnectedCallback", "initializeListeners", "componentDidLoad", "intersectionTrackerRef", "visibleCallback", "entries", "isIntersecting", "add", "visibleIO", "IntersectionObserver", "threshold", "hiddenCallback", "remove", "hiddenIO", "stopPropagation", "componentDidRender", "hasCalendarGrid", "componentWillLoad", "interactive", "datetime", "renderFooter", "isButtonDisabled", "clearButtonClick", "class", "id", "onClick", "renderWheelPicker", "forcePresentation", "renderArray", "renderTimePickerColumns", "renderDatePickerColumns", "renderCombinedDatePickerColumn", "renderIndividualDatePickerColumns", "isDateEnabled", "activePart", "<PERSON>T<PERSON><PERSON><PERSON>", "lastM<PERSON>h", "result", "items", "itemObject", "index", "referenceParts", "todayString", "onIonChange", "detail", "find<PERSON><PERSON>", "item", "part", "WHEEL_ITEM_PART", "WHEEL_ITEM_ACTIVE_PART", "shouldRenderMonths", "shouldRenderDays", "days", "dayObject", "valueNum", "parseInt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "years", "showMonthFirst", "renderMonthPickerColumn", "renderDayPickerColumn", "renderYearPickerColumn", "pickerColumnValue", "userHasSelectedDate", "hoursData", "minutesData", "dayPeriodData", "hourCycle", "renderHourPickerColumn", "renderMinutePickerColumn", "renderDayPeriodPickerColumn", "numericInput", "isDayPeriodRTL", "order", "<PERSON><PERSON><PERSON><PERSON>", "renderWheelView", "columnOrder", "renderCalendarHeader", "expandedIcon", "collapsedIcon", "prevMonthDisabled", "nextMonthDisabled", "hostDir", "getAttribute", "icon", "lazy", "flipRtl", "dir", "slot", "renderMonth", "yearAllowed", "monthAllowed", "isCalMonthDisabled", "isDatetimeDisabled", "swipeDisabled", "isWorkingMonth", "dateObject", "dayOfWeek", "_month", "_year", "isCalendarPadding", "isCalDayDisabled", "isCalDayConstrained", "dateStyle", "dateParts", "ref", "tabindex", "blur", "renderCalendarBody", "renderCalendar", "renderTimeLabel", "hasSlottedTimeLabel", "renderTimeOverlay", "_this6", "computedHourCycle", "_ref2", "popoverRef", "CustomEvent", "ionShadowTarget", "target", "on<PERSON>ill<PERSON><PERSON>iss", "_x2", "alignment", "translucent", "overlayIndex", "arrow", "onWillPresent", "cols", "for<PERSON>ach", "col", "scrollActiveItemIntoView", "keyboardEvents", "getHeaderSelectedDateText", "titleSelectedDatesFormatter", "headerText", "weekday", "renderHeader", "showExpandedHeader", "hasSlottedTitle", "renderTime", "timeOnlyPresentation", "renderCalendarViewMonthYearPicker", "renderDatetime", "hasWheelVariant", "render", "isMonthAndYearPresentation", "shouldShowMonthAndYear", "monthYearPickerOpen", "watchers", "ios", "md", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "addAnimation", "iosLeaveAnimation", "pickerIosCss", "pickerMdCss", "Picker", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "presented", "hasController", "keyboardClose", "buttons", "columns", "showBackdrop", "<PERSON><PERSON><PERSON><PERSON>", "animated", "isOpen", "onBackdropTap", "dispatchCancelHandler", "cancelButton", "callButtonHandler", "onIsOpenChange", "newValue", "triggerChanged", "trigger", "addClickListener", "removeClickListener", "htmlAttributes", "_this7", "unlock", "lock", "attachViewToDom", "durationTimeout", "data", "_this8", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "getColumn", "column", "buttonClick", "button", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "getSelected", "_this0", "rtn", "handler", "selected", "selectedColumn", "selectedIndex", "options", "columnIndex", "zIndex", "cssClass", "onIonBackdropTap", "onIonPickerWillDismiss", "visible", "tappable", "buttonWrapperClass", "type", "buttonClass", "pickerColumnIosCss", "pickerColumnMdCss", "PickerColumnCmp", "ionPickerColChange", "optHeight", "rotateFactor", "scaleFactor", "velocity", "noAnimate", "colDidChange", "col<PERSON><PERSON><PERSON>", "_this1", "pickerRotateFactor", "pickerScaleFactor", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "passive", "onStart", "onMove", "onEnd", "enable", "tmrId", "refresh", "onDomChange", "componentDidUpdate", "rafId", "cancelAnimationFrame", "emitColChange", "setSelected", "update", "saveY", "optsEl", "translateY", "translateZ", "prevSelected", "indexForY", "durationStr", "scaleStr", "children", "opt", "optOffset", "transform", "rotateX", "transitionDuration", "PICKER_OPT_SELECTED", "lastIndex", "decelerate", "DECELERATION_FRICTION", "minY", "maxY", "notLockedIn", "round", "currentPos", "event", "cancelable", "deltaY", "pow", "bounceFrom", "velocityY", "MAX_PICKER_SPEED", "hasAttribute", "TRANSITION_DURATION", "isScrollingUp", "optHeightFraction", "forceRefresh", "colEl", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "clientHeight", "align", "columnWidth", "prefix", "width", "prefixWidth", "max<PERSON><PERSON><PERSON>", "optionsWidth", "suffix", "suffixWidth", "ion_datetime", "ion_picker_legacy", "ion_picker_legacy_column"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}