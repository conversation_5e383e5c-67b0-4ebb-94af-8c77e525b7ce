{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2I;AAE3I,MAAMa,eAAe,GAAG,g7DAAg7D;AAEx8D,MAAMC,YAAY,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACjBf,qDAAgB,CAAC,IAAI,EAAEe,OAAO,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MAClB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BrB,qDAAQ,CAAC,MAAM;UACX,MAAMsB,IAAI,GAAGJ,KAAI,CAACK,EAAE,CAACC,qBAAqB,CAAC,CAAC;UAC5C,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK;UACxB,MAAMC,MAAM,GAAGJ,IAAI,CAACI,MAAM;UAC1B,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACJ,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGA,MAAM,CAAC;UAC7D,MAAMI,MAAM,GAAGF,IAAI,CAACG,GAAG,CAACL,MAAM,EAAED,KAAK,CAAC;UACtC,MAAMO,SAAS,GAAGd,KAAI,CAACe,SAAS,GAAGH,MAAM,GAAGH,UAAU,GAAGO,OAAO;UAChE,MAAMC,WAAW,GAAGP,IAAI,CAACQ,KAAK,CAACN,MAAM,GAAGO,oBAAoB,CAAC;UAC7D,MAAMC,UAAU,GAAGN,SAAS,GAAGG,WAAW;UAC1C,IAAII,IAAI,GAAGvB,CAAC,GAAGM,IAAI,CAACkB,IAAI;UACxB,IAAIC,IAAI,GAAGxB,CAAC,GAAGK,IAAI,CAACoB,GAAG;UACvB,IAAIxB,KAAI,CAACe,SAAS,EAAE;YAChBM,IAAI,GAAGd,KAAK,GAAG,GAAG;YAClBgB,IAAI,GAAGf,MAAM,GAAG,GAAG;UACvB;UACA,MAAMiB,MAAM,GAAGJ,IAAI,GAAGJ,WAAW,GAAG,GAAG;UACvC,MAAMS,MAAM,GAAGH,IAAI,GAAGN,WAAW,GAAG,GAAG;UACvC,MAAMU,KAAK,GAAGpB,KAAK,GAAG,GAAG,GAAGc,IAAI;UAChC,MAAMO,KAAK,GAAGpB,MAAM,GAAG,GAAG,GAAGe,IAAI;UACjCvC,qDAAS,CAAC,MAAM;YACZ,MAAM6C,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACzCF,GAAG,CAACG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;YAClC,MAAMC,KAAK,GAAGL,GAAG,CAACK,KAAK;YACvBA,KAAK,CAACV,GAAG,GAAGE,MAAM,GAAG,IAAI;YACzBQ,KAAK,CAACZ,IAAI,GAAGG,MAAM,GAAG,IAAI;YAC1BS,KAAK,CAAC3B,KAAK,GAAG2B,KAAK,CAAC1B,MAAM,GAAGS,WAAW,GAAG,IAAI;YAC/CiB,KAAK,CAACC,WAAW,CAAC,eAAe,EAAE,GAAGf,UAAU,EAAE,CAAC;YACnDc,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,GAAGR,KAAK,OAAOC,KAAK,IAAI,CAAC;YAC9D,MAAMQ,SAAS,GAAGpC,KAAI,CAACK,EAAE,CAACgC,UAAU,IAAIrC,KAAI,CAACK,EAAE;YAC/C+B,SAAS,CAACE,WAAW,CAACT,GAAG,CAAC;YAC1BU,UAAU,CAAC,MAAM;cACbpC,OAAO,CAAC,MAAM;gBACVqC,YAAY,CAACX,GAAG,CAAC;cACrB,CAAC,CAAC;YACN,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;UACjB,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,CAAC;IAAC;EACP;EACA,IAAId,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnB,IAAI,KAAK,WAAW;EACpC;EACA6C,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxD,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEsD,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;QACxF,CAACH,IAAI,GAAG,IAAI;QACZ3B,SAAS,EAAE,IAAI,CAACA;MACpB;IAAE,CAAC,CAAC;EACZ;EACA,IAAIV,EAAEA,CAAA,EAAG;IAAE,OAAOd,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAMiD,YAAY,GAAIM,MAAM,IAAK;EAC7BA,MAAM,CAACd,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;EAChCM,UAAU,CAAC,MAAM;IACbO,MAAM,CAACC,MAAM,CAAC,CAAC;EACnB,CAAC,EAAE,GAAG,CAAC;AACX,CAAC;AACD,MAAM/B,OAAO,GAAG,EAAE;AAClB,MAAMG,oBAAoB,GAAG,GAAG;AAChC1B,YAAY,CAACyC,KAAK,GAAG1C,eAAe", "sources": ["./node_modules/@ionic/core/dist/esm/ion-ripple-effect.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, f as readTask, w as writeTask, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\n\nconst rippleEffectCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}\";\n\nconst RippleEffect = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * Sets the type of ripple-effect:\n         *\n         * - `bounded`: the ripple effect expands from the user's click position\n         * - `unbounded`: the ripple effect expands from the center of the button and overflows the container.\n         *\n         * NOTE: Surfaces for bounded ripples should have the overflow property set to hidden,\n         * while surfaces for unbounded ripples should have it set to visible.\n         */\n        this.type = 'bounded';\n    }\n    /**\n     * Adds the ripple effect to the parent element.\n     *\n     * @param x The horizontal coordinate of where the ripple should start.\n     * @param y The vertical coordinate of where the ripple should start.\n     */\n    async addRipple(x, y) {\n        return new Promise((resolve) => {\n            readTask(() => {\n                const rect = this.el.getBoundingClientRect();\n                const width = rect.width;\n                const height = rect.height;\n                const hypotenuse = Math.sqrt(width * width + height * height);\n                const maxDim = Math.max(height, width);\n                const maxRadius = this.unbounded ? maxDim : hypotenuse + PADDING;\n                const initialSize = Math.floor(maxDim * INITIAL_ORIGIN_SCALE);\n                const finalScale = maxRadius / initialSize;\n                let posX = x - rect.left;\n                let posY = y - rect.top;\n                if (this.unbounded) {\n                    posX = width * 0.5;\n                    posY = height * 0.5;\n                }\n                const styleX = posX - initialSize * 0.5;\n                const styleY = posY - initialSize * 0.5;\n                const moveX = width * 0.5 - posX;\n                const moveY = height * 0.5 - posY;\n                writeTask(() => {\n                    const div = document.createElement('div');\n                    div.classList.add('ripple-effect');\n                    const style = div.style;\n                    style.top = styleY + 'px';\n                    style.left = styleX + 'px';\n                    style.width = style.height = initialSize + 'px';\n                    style.setProperty('--final-scale', `${finalScale}`);\n                    style.setProperty('--translate-end', `${moveX}px, ${moveY}px`);\n                    const container = this.el.shadowRoot || this.el;\n                    container.appendChild(div);\n                    setTimeout(() => {\n                        resolve(() => {\n                            removeRipple(div);\n                        });\n                    }, 225 + 100);\n                });\n            });\n        });\n    }\n    get unbounded() {\n        return this.type === 'unbounded';\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'ae9d3b1ed6773a9b9bb2267129f7e9af23b6c9fc', role: \"presentation\", class: {\n                [mode]: true,\n                unbounded: this.unbounded,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nconst removeRipple = (ripple) => {\n    ripple.classList.add('fade-out');\n    setTimeout(() => {\n        ripple.remove();\n    }, 200);\n};\nconst PADDING = 10;\nconst INITIAL_ORIGIN_SCALE = 0.5;\nRippleEffect.style = rippleEffectCss;\n\nexport { RippleEffect as ion_ripple_effect };\n"], "names": ["r", "registerInstance", "f", "readTask", "w", "writeTask", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "rippleEffectCss", "RippleEffect", "constructor", "hostRef", "type", "addRipple", "x", "y", "_this", "_asyncToGenerator", "Promise", "resolve", "rect", "el", "getBoundingClientRect", "width", "height", "hypotenuse", "Math", "sqrt", "maxDim", "max", "maxRadius", "unbounded", "PADDING", "initialSize", "floor", "INITIAL_ORIGIN_SCALE", "finalScale", "posX", "left", "posY", "top", "styleX", "styleY", "moveX", "moveY", "div", "document", "createElement", "classList", "add", "style", "setProperty", "container", "shadowRoot", "append<PERSON><PERSON><PERSON>", "setTimeout", "removeRipple", "render", "mode", "key", "role", "class", "ripple", "remove", "ion_ripple_effect"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}