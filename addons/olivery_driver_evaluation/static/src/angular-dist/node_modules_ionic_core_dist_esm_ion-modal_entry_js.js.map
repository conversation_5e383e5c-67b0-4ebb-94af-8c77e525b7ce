{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-modal_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACiL;AACY;AAC5E;AACY;AACnD;AACd;AAC2L;AAChM;AACiB;AACb;AACI;AACW;AACtB;AACL;AACH;AACF;AACV;AAEhC,IAAI+D,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM;EACtBA,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;EACxBA,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;AAChC,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,MAAMC,SAAS,GAAG;EACdC,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAG1B,yDAAY,CAAC,CAAC;IAChC,IAAI0B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,WAAW,CAAC,EAAE;MAChG,OAAOD,SAAS,CAACE,OAAO,CAACJ,SAAS;IACtC;IACA,OAAOK,SAAS;EACpB,CAAC;EACDC,QAAQA,CAACC,OAAO,EAAE;IACd,MAAMC,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACF,QAAQ,CAACC,OAAO,CAAC;EAC5B,CAAC;EACDE,QAAQ;IAAA,IAAAC,IAAA,GAAAC,yMAAA,CAAE,aAAkB;MACxB,MAAMH,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;MAC/B,IAAI,CAACO,MAAM,EAAE;QACT,OAAOT,KAAK,CAACa,OAAO;MACxB;MACA,MAAM;QAAEC;MAAM,CAAC,SAASL,MAAM,CAACM,OAAO,CAAC,CAAC;MACxC,OAAOD,KAAK;IAChB,CAAC;IAAA,gBAPDJ,QAAQA,CAAA;MAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA;AAQZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGA,CAACC,CAAC,EAAEC,kBAAkB,KAAK;EACxD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIA,kBAAkB,KAAK,CAAC,EAAE;IAC1B,OAAO,CAAC;EACZ;EACA,MAAMC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGD,kBAAkB,CAAC;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMhD,CAAC,GAAG,EAAEgD,kBAAkB,GAAGC,KAAK,CAAC;EACvC;AACJ;AACA;AACA;AACA;EACI,OAAOF,CAAC,GAAGE,KAAK,GAAGjD,CAAC;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkD,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,IAAI,CAACvB,kDAAG,IAAIA,kDAAG,CAACwB,UAAU,IAAI,GAAG,EAAE;IAC/B;EACJ;EACAtB,SAAS,CAACM,QAAQ,CAAC;IAAEO,KAAK,EAAEd,KAAK,CAACwB;EAAK,CAAC,CAAC;AAC7C,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAACC,YAAY,GAAG1B,KAAK,CAACa,OAAO,KAAK;EAC9D,IAAI,CAACd,kDAAG,IAAIA,kDAAG,CAACwB,UAAU,IAAI,GAAG,EAAE;IAC/B;EACJ;EACAtB,SAAS,CAACM,QAAQ,CAAC;IAAEO,KAAK,EAAEY;EAAa,CAAC,CAAC;AAC/C,CAAC;AAED,MAAMC,gBAAgB;EAAA,IAAAC,KAAA,GAAAhB,yMAAA,CAAG,WAAOiB,EAAE,EAAEC,SAAS,EAAK;IAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,OAAOD,EAAE,CAACE,UAAU,KAAK,UAAU,EAAE;MACrC;IACJ;IACA;AACJ;AACA;AACA;AACA;IACI,MAAMC,aAAa,SAASH,EAAE,CAACE,UAAU,CAACzB,SAAS,EAAE3B,oDAAO,CAAC;IAC7D,IAAI,CAACqD,aAAa,EAAE;MAChB;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIF,SAAS,CAACG,SAAS,CAAC,CAAC,EAAE;MACvBH,SAAS,CAACI,QAAQ,CAAC,MAAM;QACrBL,EAAE,CAACvC,OAAO,CAACgB,SAAS,EAAE,SAAS,CAAC;MACpC,CAAC,EAAE;QAAE6B,eAAe,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MACI;MACDN,EAAE,CAACvC,OAAO,CAACgB,SAAS,EAAE,SAAS,CAAC;IACpC;EACJ,CAAC;EAAA,gBAvCKqB,gBAAgBA,CAAAS,EAAA,EAAAC,GAAA;IAAA,OAAAT,KAAA,CAAAZ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuCrB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,mBAAmB,GAAIC,CAAC,IAAK;EAC/B,OAAO,UAAU,GAAG,OAAO,KAAK,CAAC,OAAO,GAAGA,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,KAAK,CAAC,SAAS,GAAGA,CAAC,CAAC,GAAG,CAAC;AAC7F,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAG;EACzBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,yBAAyB,GAAGA,CAACb,EAAE,EAAEC,SAAS,EAAEa,cAAc,EAAEC,SAAS,KAAK;EAC5E;AACJ;AACA;AACA;EACI,MAAMC,iBAAiB,GAAG,GAAG;EAC7B,MAAMC,MAAM,GAAGjB,EAAE,CAACkB,YAAY;EAC9B,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,QAAQ,GAAG,IAAI;EACnB,MAAMC,iBAAiB,GAAG,GAAG;EAC7B,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIL,SAAS,IAAI7F,qDAAY,CAAC6F,SAAS,CAAC,EAAE;MACtC,OAAOA,SAAS,CAACM,OAAO;MACxB;AACZ;AACA;AACA;AACA;IACQ,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ,CAAC;EACD,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IACzB,MAAMC,MAAM,GAAGD,MAAM,CAACE,KAAK,CAACD,MAAM;IAClC,IAAIA,MAAM,KAAK,IAAI,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MACpC,OAAO,IAAI;IACf;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQX,SAAS,GAAG/F,qDAAqB,CAACwG,MAAM,CAAC;IACzC,IAAIT,SAAS,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI7F,qDAAY,CAAC6F,SAAS,CAAC,EAAE;QACzB,MAAMY,IAAI,GAAG5F,uDAAc,CAACgF,SAAS,CAAC;QACtCC,QAAQ,GAAGW,IAAI,CAACC,aAAa,CAAC,eAAe,CAAC;MAClD,CAAC,MACI;QACDZ,QAAQ,GAAGD,SAAS;MACxB;MACA,MAAMc,qBAAqB,GAAG,CAAC,CAACd,SAAS,CAACa,aAAa,CAAC,eAAe,CAAC;MACxE,OAAO,CAACC,qBAAqB,IAAIb,QAAQ,CAACc,SAAS,KAAK,CAAC;IAC7D;IACA;AACR;AACA;AACA;IACQ,MAAMC,MAAM,GAAGP,MAAM,CAACE,OAAO,CAAC,YAAY,CAAC;IAC3C,IAAIK,MAAM,KAAK,IAAI,EAAE;MACjB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD,MAAMC,OAAO,GAAIT,MAAM,IAAK;IACxB,MAAM;MAAEU;IAAO,CAAC,GAAGV,MAAM;IACzB;AACR;AACA;AACA;AACA;IACQL,cAAc,GAAGE,UAAU,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQN,uBAAuB,GAAGpB,EAAE,CAACE,UAAU,KAAKzB,SAAS,IAAIuB,EAAE,CAACE,UAAU,KAAK,IAAI;IAC/E;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIqC,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MACzB5F,qDAAqB,CAAC4F,SAAS,CAAC;IACpC;IACApB,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAErB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,CAAC;EACD,MAAMsB,MAAM,GAAIZ,MAAM,IAAK;IACvB,MAAM;MAAEU;IAAO,CAAC,GAAGV,MAAM;IACzB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MACzB5F,qDAAqB,CAAC4F,SAAS,CAAC;IACpC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMqB,IAAI,GAAGb,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACnC;AACR;AACA;AACA;AACA;AACA;IACQ,MAAM0B,iCAAiC,GAAGD,IAAI,IAAI,CAAC,IAAItB,uBAAuB;IAC9E;AACR;AACA;AACA;AACA;IACQ,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMsB,aAAa,GAAGF,iCAAiC,GAAGlC,mBAAmB,CAACiC,IAAI,GAAGE,OAAO,CAAC,GAAGF,IAAI;IACpG,MAAMI,WAAW,GAAG3G,uDAAK,CAAC,MAAM,EAAE0G,aAAa,EAAED,OAAO,CAAC;IACzD3C,SAAS,CAAC8C,YAAY,CAACD,WAAW,CAAC;IACnC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIA,WAAW,IAAI9B,iBAAiB,IAAIS,QAAQ,GAAGT,iBAAiB,EAAE;MAClEpB,uBAAuB,CAACkB,cAAc,CAAC;MACvC;AACZ;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIgC,WAAW,GAAG9B,iBAAiB,IAAIS,QAAQ,IAAIT,iBAAiB,EAAE;MACvEvB,oBAAoB,CAAC,CAAC;IAC1B;IACAgC,QAAQ,GAAGqB,WAAW;EAC1B,CAAC;EACD,MAAME,KAAK,GAAInB,MAAM,IAAK;IACtB,MAAMoB,QAAQ,GAAGpB,MAAM,CAACqB,SAAS;IACjC,MAAMR,IAAI,GAAGb,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACnC,MAAM0B,iCAAiC,GAAGD,IAAI,IAAI,CAAC,IAAItB,uBAAuB;IAC9E,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E,MAAMsB,aAAa,GAAGF,iCAAiC,GAAGlC,mBAAmB,CAACiC,IAAI,GAAGE,OAAO,CAAC,GAAGF,IAAI;IACpG,MAAMI,WAAW,GAAG3G,uDAAK,CAAC,MAAM,EAAE0G,aAAa,EAAED,OAAO,CAAC;IACzD,MAAMO,SAAS,GAAG,CAACtB,MAAM,CAACU,MAAM,GAAGU,QAAQ,GAAG,IAAI,IAAIhC,MAAM;IAC5D;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMmC,cAAc,GAAG,CAACT,iCAAiC,IAAIQ,SAAS,IAAInC,iBAAiB;IAC3F,IAAIqC,YAAY,GAAGD,cAAc,GAAG,CAAC,IAAI,GAAG,KAAK;IACjD,IAAI,CAACA,cAAc,EAAE;MACjBnD,SAAS,CAACqD,MAAM,CAAC,gCAAgC,CAAC;MAClDD,YAAY,IAAIrF,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE8E,WAAW,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC,MACI;MACD7C,SAAS,CAACqD,MAAM,CAAC,gCAAgC,CAAC;MAClDD,YAAY,IAAIrF,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE8E,WAAW,CAAC,CAAC,CAAC,CAAC;IACjG;IACA,MAAMS,QAAQ,GAAGH,cAAc,GACzBI,eAAe,CAACd,IAAI,GAAGzB,MAAM,EAAEgC,QAAQ,CAAC,GACxCO,eAAe,CAAC,CAAC,CAAC,GAAGV,WAAW,IAAI7B,MAAM,EAAEgC,QAAQ,CAAC;IAC3D9B,MAAM,GAAGiC,cAAc;IACvBK,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;IACrB,IAAIrC,SAAS,EAAE;MACX3F,qDAAmB,CAAC2F,SAAS,EAAEG,cAAc,CAAC;IAClD;IACAvB,SAAS,CACJI,QAAQ,CAAC,MAAM;MAChB,IAAI,CAAC+C,cAAc,EAAE;QACjBK,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;MACxB;IACJ,CAAC,CAAC,CACGC,WAAW,CAACP,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEC,YAAY,EAAEE,QAAQ,CAAC;IAChE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIZ,iCAAiC,IAAIG,WAAW,GAAGF,OAAO,GAAG,CAAC,EAAE;MAChE9C,gBAAgB,CAACE,EAAE,EAAEC,SAAS,CAAC;IACnC,CAAC,MACI,IAAImD,cAAc,EAAE;MACrBrC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EACD,MAAM0C,OAAO,GAAGxF,kEAAa,CAAC;IAC1B+B,EAAE;IACF4D,WAAW,EAAE,mBAAmB;IAChCC,eAAe,EAAE7G,oDAAwB;IACzC8G,SAAS,EAAE,GAAG;IACdX,SAAS,EAAE,EAAE;IACbvB,QAAQ;IACRU,OAAO;IACPG,MAAM;IACNO;EACJ,CAAC,CAAC;EACF,OAAOS,OAAO;AAClB,CAAC;AACD,MAAMD,eAAe,GAAGA,CAACO,SAAS,EAAEd,QAAQ,KAAK;EAC7C,OAAO9G,uDAAK,CAAC,GAAG,EAAE4H,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAChB,QAAQ,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC;AAChE,CAAC;AAED,MAAMiB,yBAAyB,GAAIC,IAAI,IAAK;EACxC,MAAM;IAAEC,iBAAiB;IAAE7E,kBAAkB;IAAE8E;EAAe,CAAC,GAAGF,IAAI;EACtE;AACJ;AACA;AACA;AACA;EACI,MAAMG,kBAAkB,GAAG/E,kBAAkB,KAAKd,SAAS,IAAIc,kBAAkB,GAAG6E,iBAAiB;EACrG,MAAMG,eAAe,GAAGD,kBAAkB,GAAG,kCAAkCF,iBAAiB,GAAG,GAAG,GAAG;EACzG,MAAMI,iBAAiB,GAAGzG,0DAAe,CAAC,mBAAmB,CAAC,CAAC0G,MAAM,CAAC,SAAS,EAAE,CAAC,EAAEF,eAAe,CAAC;EACpG,IAAID,kBAAkB,EAAE;IACpBE,iBAAiB,CACZE,YAAY,CAAC;MACd,gBAAgB,EAAE;IACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC7C;EACA,MAAMC,gBAAgB,GAAG7G,0DAAe,CAAC,kBAAkB,CAAC,CAAC8G,SAAS,CAAC,CACnE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,EACxD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE,cAAc,GAAG,GAAGZ,iBAAiB,GAAG,GAAG;EAAK,CAAC,CACxF,CAAC;EACF;AACJ;AACA;EACI,MAAMa,gBAAgB,GAAG,CAACZ,cAAc,GAClCtG,0DAAe,CAAC,kBAAkB,CAAC,CAAC8G,SAAS,CAAC,CAC5C;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAGd,iBAAiB,IAAI,GAAG;EAAI,CAAC,EACzE;IAAEU,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEG,SAAS,EAAE,GAAGd,iBAAiB,GAAG,GAAG;EAAI,CAAC,CACtE,CAAC,GACA3F,SAAS;EACf,OAAO;IAAEmG,gBAAgB;IAAEJ,iBAAiB;IAAES;EAAiB,CAAC;AACpE,CAAC;AACD,MAAME,yBAAyB,GAAIhB,IAAI,IAAK;EACxC,MAAM;IAAEC,iBAAiB;IAAE7E;EAAmB,CAAC,GAAG4E,IAAI;EACtD;AACJ;AACA;AACA;AACA;EACI,MAAMiB,aAAa,GAAG,kCAAkC/F,wBAAwB,CAAC+E,iBAAiB,EAAE7E,kBAAkB,CAAC,GAAG;EAC1H,MAAM8F,eAAe,GAAG,CACpB;IAAEP,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAEK;EAAc,CAAC,EACrC;IAAEN,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAMO,cAAc,GAAG,CACnB;IAAER,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAEK;EAAc,CAAC,EACrC;IAAEN,MAAM,EAAEvF,kBAAkB;IAAEwF,OAAO,EAAE;EAAE,CAAC,EAC1C;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAMP,iBAAiB,GAAGzG,0DAAe,CAAC,mBAAmB,CAAC,CAAC8G,SAAS,CAACtF,kBAAkB,KAAK,CAAC,GAAG+F,cAAc,GAAGD,eAAe,CAAC;EACrI,MAAMT,gBAAgB,GAAG7G,0DAAe,CAAC,kBAAkB,CAAC,CAAC8G,SAAS,CAAC,CACnE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE,cAAc,GAAG,GAAGZ,iBAAiB,GAAG,GAAG;EAAK,CAAC,EACrF;IAAEU,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,CAC3D,CAAC;EACF,OAAO;IAAEJ,gBAAgB;IAAEJ;EAAkB,CAAC;AAClD,CAAC;AAED,MAAMe,sBAAsB,GAAGA,CAAA,KAAM;EACjC,MAAMf,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CACtC0G,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzC,MAAMC,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CAAC0G,MAAM,CAAC,WAAW,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;EACtG,OAAO;IAAED,iBAAiB;IAAEI,gBAAgB;IAAEK,gBAAgB,EAAExG;EAAU,CAAC;AAC/E,CAAC;AACD;AACA;AACA;AACA,MAAM+G,iBAAiB,GAAGA,CAACC,MAAM,EAAEtB,IAAI,KAAK;EACxC,MAAM;IAAEuB,YAAY;IAAEtB,iBAAiB;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAChE,MAAMlC,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;EACnC,MAAM;IAAEb,gBAAgB;IAAEJ,iBAAiB;IAAES;EAAiB,CAAC,GAAGb,iBAAiB,KAAK3F,SAAS,GAAGyF,yBAAyB,CAACC,IAAI,CAAC,GAAGoB,sBAAsB,CAAC,CAAC;EAC9Jf,iBAAiB,CAACmB,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChE0C,gBAAgB,CAACe,UAAU,CAAC1D,IAAI,CAAC2D,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAAClB,YAAY,CAAC;IAAEK,OAAO,EAAE;EAAE,CAAC,CAAC;EAChH;EACA;EACA,CAACV,cAAc,KAAKY,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACU,UAAU,CAACF,MAAM,CAACvD,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC;EACvJ,MAAM2D,aAAa,GAAG9H,0DAAe,CAAC,eAAe,CAAC,CACjD4H,UAAU,CAACF,MAAM,CAAC,CAClBnC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbuC,YAAY,CAAC,CAAClB,gBAAgB,CAAC,CAAC;EACrC,IAAIK,gBAAgB,EAAE;IAClBY,aAAa,CAACC,YAAY,CAACb,gBAAgB,CAAC;EAChD;EACA,IAAIS,YAAY,EAAE;IACd,MAAMK,UAAU,GAAGC,MAAM,CAACtG,UAAU,GAAG,GAAG;IAC1C,MAAMuG,YAAY,GAAGP,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAK1H,SAAS;IACzG,MAAM2H,gBAAgB,GAAG/J,uDAAc,CAACqJ,YAAY,CAAC;IACrD,MAAMW,mBAAmB,GAAGtI,0DAAe,CAAC,CAAC,CAAC2G,YAAY,CAAC;MACvDM,SAAS,EAAE,eAAe;MAC1B,kBAAkB,EAAE,YAAY;MAChCsB,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,IAAI;IAC5B,IAAIV,UAAU,EAAE;MACZ;AACZ;AACA;AACA;AACA;MACY,MAAMW,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;MAChH,MAAMC,cAAc,GAAGZ,YAAY,GAAG,OAAO,GAAGS,eAAe;MAC/D,MAAMI,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;MACnE,MAAMmG,cAAc,GAAG,cAAcF,cAAc,WAAWC,iBAAiB,GAAG;MAClFT,mBAAmB,CACdW,WAAW,CAAC;QACbhC,SAAS,EAAE+B;MACf,CAAC,CAAC,CACGE,cAAc,CAAC,MAAMV,MAAM,CAACtH,KAAK,CAACiI,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,CAC3EvB,UAAU,CAACD,YAAY,CAAC,CACxBb,SAAS,CAAC,CACX;QAAEC,MAAM,EAAE,CAAC;QAAEqC,MAAM,EAAE,aAAa;QAAEnC,SAAS,EAAE,0BAA0B;QAAEoC,YAAY,EAAE;MAAM,CAAC,EAChG;QAAEtC,MAAM,EAAE,CAAC;QAAEqC,MAAM,EAAE,gBAAgB;QAAEnC,SAAS,EAAE+B,cAAc;QAAEK,YAAY,EAAE;MAAgB,CAAC,CACpG,CAAC;MACFvB,aAAa,CAACC,YAAY,CAACO,mBAAmB,CAAC;IACnD,CAAC,MACI;MACDR,aAAa,CAACC,YAAY,CAACtB,iBAAiB,CAAC;MAC7C,IAAI,CAACyB,YAAY,EAAE;QACfrB,gBAAgB,CAACH,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;MAChD,CAAC,MACI;QACD,MAAMqC,iBAAiB,GAAGb,YAAY,GAAGtF,oBAAoB,CAACC,oBAAoB,GAAG,CAAC;QACtF,MAAMmG,cAAc,GAAG,2BAA2BD,iBAAiB,GAAG;QACtET,mBAAmB,CACdW,WAAW,CAAC;UACbhC,SAAS,EAAE+B;QACf,CAAC,CAAC,CACGpB,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAC5D2C,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEqC,MAAM,EAAE,aAAa;UAAEnC,SAAS,EAAE;QAAyB,CAAC,EACzE;UAAEF,MAAM,EAAE,CAAC;UAAEqC,MAAM,EAAE,gBAAgB;UAAEnC,SAAS,EAAE+B;QAAe,CAAC,CACrE,CAAC;QACF,MAAMM,eAAe,GAAGtJ,0DAAe,CAAC,CAAC,CACpCiJ,WAAW,CAAC;UACbhC,SAAS,EAAE+B;QACf,CAAC,CAAC,CACGpB,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D2C,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAyB,CAAC,EAChE;UAAEF,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE+B;QAAe,CAAC,CACzD,CAAC;QACFlB,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;MACtE;IACJ;EACJ,CAAC,MACI;IACDxB,aAAa,CAACC,YAAY,CAACtB,iBAAiB,CAAC;EACjD;EACA,OAAOqB,aAAa;AACxB,CAAC;AAED,MAAMyB,sBAAsB,GAAGA,CAAA,KAAM;EACjC,MAAM9C,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CAAC0G,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAC3F,MAAMG,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CAAC0G,MAAM,CAAC,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,CAAC;EACtG,OAAO;IAAED,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAM2C,iBAAiB,GAAGA,CAAC9B,MAAM,EAAEtB,IAAI,EAAEZ,QAAQ,GAAG,GAAG,KAAK;EACxD,MAAM;IAAEmC,YAAY;IAAEtB;EAAkB,CAAC,GAAGD,IAAI;EAChD,MAAMlC,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;EACnC,MAAM;IAAEb,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGJ,iBAAiB,KAAK3F,SAAS,GAAG0G,yBAAyB,CAAChB,IAAI,CAAC,GAAGmD,sBAAsB,CAAC,CAAC;EAC5I9C,iBAAiB,CAACmB,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChE0C,gBAAgB,CAACe,UAAU,CAAC1D,IAAI,CAAC2D,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAAClB,YAAY,CAAC;IAAEK,OAAO,EAAE;EAAE,CAAC,CAAC;EAChH,MAAMc,aAAa,GAAG9H,0DAAe,CAAC,cAAc,CAAC,CAChD4H,UAAU,CAACF,MAAM,CAAC,CAClBnC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAACA,QAAQ,CAAC,CAClBuC,YAAY,CAAClB,gBAAgB,CAAC;EACnC,IAAIc,YAAY,EAAE;IACd,MAAMK,UAAU,GAAGC,MAAM,CAACtG,UAAU,GAAG,GAAG;IAC1C,MAAMuG,YAAY,GAAGP,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAK1H,SAAS;IACzG,MAAM2H,gBAAgB,GAAG/J,uDAAc,CAACqJ,YAAY,CAAC;IACrD,MAAMW,mBAAmB,GAAGtI,0DAAe,CAAC,CAAC,CACxCyJ,iBAAiB,CAAC,CAAC,WAAW,CAAC,CAAC,CAChC7C,gBAAgB,CAAC,CAAC,WAAW,CAAC,CAAC,CAC/BtE,QAAQ,CAAEoH,WAAW,IAAK;MAC3B;MACA,IAAIA,WAAW,KAAK,CAAC,EAAE;QACnB;MACJ;MACA/B,YAAY,CAACzG,KAAK,CAACiI,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC;MAC9C,MAAMQ,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACrB,MAAM,CAACX,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,CAACuB,MAAM,CAAEzM,CAAC,IAAKA,CAAC,CAACyL,iBAAiB,KAAK1H,SAAS,CAAC,CAACoJ,MAAM;MAC/I,IAAIH,SAAS,IAAI,CAAC,EAAE;QAChBnB,MAAM,CAACtH,KAAK,CAACiI,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,MAAMX,MAAM,GAAGC,QAAQ,CAACC,IAAI;IAC5B,IAAIV,UAAU,EAAE;MACZ,MAAMW,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;MAChH,MAAMC,cAAc,GAAGZ,YAAY,GAAG,OAAO,GAAGS,eAAe;MAC/D,MAAMI,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;MACnE,MAAMmG,cAAc,GAAG,cAAcF,cAAc,WAAWC,iBAAiB,GAAG;MAClFT,mBAAmB,CAACV,UAAU,CAACD,YAAY,CAAC,CAACb,SAAS,CAAC,CACnD;QAAEC,MAAM,EAAE,CAAC;QAAEqC,MAAM,EAAE,gBAAgB;QAAEnC,SAAS,EAAE+B,cAAc;QAAEK,YAAY,EAAE;MAAgB,CAAC,EACjG;QAAEtC,MAAM,EAAE,CAAC;QAAEqC,MAAM,EAAE,aAAa;QAAEnC,SAAS,EAAE,0BAA0B;QAAEoC,YAAY,EAAE;MAAM,CAAC,CACnG,CAAC;MACFvB,aAAa,CAACC,YAAY,CAACO,mBAAmB,CAAC;IACnD,CAAC,MACI;MACDR,aAAa,CAACC,YAAY,CAACtB,iBAAiB,CAAC;MAC7C,IAAI,CAACyB,YAAY,EAAE;QACfrB,gBAAgB,CAACH,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;MAChD,CAAC,MACI;QACD,MAAMqC,iBAAiB,GAAGb,YAAY,GAAGtF,oBAAoB,CAACC,oBAAoB,GAAG,CAAC;QACtF,MAAMmG,cAAc,GAAG,2BAA2BD,iBAAiB,GAAG;QACtET,mBAAmB,CACdV,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAC5D8E,WAAW,CAAC;UACbhC,SAAS,EAAE;QACf,CAAC,CAAC,CACGH,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEqC,MAAM,EAAE,gBAAgB;UAAEnC,SAAS,EAAE+B;QAAe,CAAC,EAClE;UAAEjC,MAAM,EAAE,CAAC;UAAEqC,MAAM,EAAE,aAAa;UAAEnC,SAAS,EAAE;QAAyB,CAAC,CAC5E,CAAC;QACF,MAAMqC,eAAe,GAAGtJ,0DAAe,CAAC,CAAC,CACpC4H,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D8E,WAAW,CAAC;UACbhC,SAAS,EAAE;QACf,CAAC,CAAC,CACGH,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE+B;QAAe,CAAC,EACtD;UAAEjC,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAyB,CAAC,CACnE,CAAC;QACFa,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;MACtE;IACJ;EACJ,CAAC,MACI;IACDxB,aAAa,CAACC,YAAY,CAACtB,iBAAiB,CAAC;EACjD;EACA,OAAOqB,aAAa;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMiC,6BAA6B,GAAGA,CAACrC,MAAM,EAAEtB,IAAI,EAAEZ,QAAQ,GAAG,GAAG,KAAK;EACpE,MAAM;IAAEmC;EAAa,CAAC,GAAGvB,IAAI;EAC7B,IAAI,CAACuB,YAAY,EAAE;IACf;IACA,OAAO3H,0DAAe,CAAC,kCAAkC,CAAC;EAC9D;EACA,MAAMgK,uBAAuB,GAAGrC,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAK1H,SAAS;EACpH,MAAM2H,gBAAgB,GAAG/J,uDAAc,CAACqJ,YAAY,CAAC;EACrD,MAAMa,MAAM,GAAGC,QAAQ,CAACC,IAAI;EAC5B,MAAMZ,aAAa,GAAG9H,0DAAe,CAAC,kCAAkC,CAAC,CACpE4H,UAAU,CAACF,MAAM,CAAC,CAClBnC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAACA,QAAQ,CAAC;EACvB,MAAM8C,mBAAmB,GAAGtI,0DAAe,CAAC,CAAC,CAAC2G,YAAY,CAAC;IACvDM,SAAS,EAAE,eAAe;IAC1B,kBAAkB,EAAE,YAAY;IAChCsB,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,IAAI,CAACyB,uBAAuB,EAAE;IAC1B;IACA;IACA,MAAM9F,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;IACnC,MAAMb,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CACrC4H,UAAU,CAAC1D,IAAI,CAAC2D,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAClEnB,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAClC,MAAMD,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CACtC4H,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC,CAC9CuC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,yBAAyB,CAAC,CAAC,CAAC;IAC9E;IACA,MAAMiC,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;IAChH,MAAME,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;IACnE,MAAMoH,aAAa,GAAG,cAActB,eAAe,WAAWI,iBAAiB,GAAG;IAClFT,mBAAmB,CACdV,UAAU,CAACD,YAAY,CAAC,CACxBsB,WAAW,CAAC;MACbhC,SAAS,EAAE,0BAA0B;MACrC,eAAe,EAAE;IACrB,CAAC,CAAC,CACGiC,cAAc,CAAC,MAAMV,MAAM,CAACtH,KAAK,CAACiI,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,CACtEzC,MAAM,CAAC,WAAW,EAAEuD,aAAa,EAAE,0BAA0B,CAAC,CAC9DvD,MAAM,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CACjDA,MAAM,CAAC,eAAe,EAAE,eAAe,EAAE,KAAK,CAAC;IACpDoB,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEzB,gBAAgB,EAAEJ,iBAAiB,CAAC,CAAC;EAC1F,CAAC,MACI;IACD;IACA;IACA,MAAMsC,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;IACnE,MAAMoH,aAAa,GAAG,2BAA2BlB,iBAAiB,GAAG;IACrE,MAAMmB,WAAW,GAAG,0BAA0B;IAC9C5B,mBAAmB,CACdV,UAAU,CAACD,YAAY,CAAC,CACxBsB,WAAW,CAAC;MACbhC,SAAS,EAAEiD;IACf,CAAC,CAAC,CACGxD,MAAM,CAAC,WAAW,EAAEuD,aAAa,EAAEC,WAAW,CAAC,CAC/CxD,MAAM,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC;IACtD,MAAM4C,eAAe,GAAGtJ,0DAAe,CAAC,CAAC,CACpC4H,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D8E,WAAW,CAAC;MACbhC,SAAS,EAAEiD,WAAW;MACtBlD,OAAO,EAAE;IACb,CAAC,CAAC,CACGN,MAAM,CAAC,WAAW,EAAEuD,aAAa,EAAEC,WAAW,CAAC;IACpDpC,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;EACtE;EACA,OAAOxB,aAAa;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqC,6BAA6B,GAAGA,CAACzC,MAAM,EAAEtB,IAAI,EAAEZ,QAAQ,GAAG,GAAG,KAAK;EACpE,MAAM;IAAEmC;EAAa,CAAC,GAAGvB,IAAI;EAC7B,IAAI,CAACuB,YAAY,EAAE;IACf;IACA,OAAO3H,0DAAe,CAAC,kCAAkC,CAAC;EAC9D;EACA,MAAMgK,uBAAuB,GAAGrC,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAK1H,SAAS;EACpH,MAAM2H,gBAAgB,GAAG/J,uDAAc,CAACqJ,YAAY,CAAC;EACrD,MAAMa,MAAM,GAAGC,QAAQ,CAACC,IAAI;EAC5B,MAAMZ,aAAa,GAAG9H,0DAAe,CAAC,kCAAkC,CAAC,CACpE4H,UAAU,CAACF,MAAM,CAAC,CAClBnC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAACA,QAAQ,CAAC;EACvB,MAAM8C,mBAAmB,GAAGtI,0DAAe,CAAC,CAAC,CAAC2G,YAAY,CAAC;IACvDM,SAAS,EAAE,eAAe;IAC1B,kBAAkB,EAAE,YAAY;IAChCsB,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,IAAI,CAACyB,uBAAuB,EAAE;IAC1B;IACA;IACA,MAAM9F,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;IACnC,MAAMb,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CACrC4H,UAAU,CAAC1D,IAAI,CAAC2D,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAClEnB,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAClC,MAAMD,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CACtC4H,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC,CAC9CuC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,yBAAyB,CAAC,CAAC,CAAC;IAC9E;IACA,MAAMiC,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;IAChH,MAAME,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;IACnE,MAAMqH,WAAW,GAAG,cAAcvB,eAAe,WAAWI,iBAAiB,GAAG;IAChFT,mBAAmB,CACdV,UAAU,CAACD,YAAY,CAAC,CACxBsB,WAAW,CAAC;MACbhC,SAAS,EAAEiD;IACf,CAAC,CAAC,CACGhB,cAAc,CAAC,MAAMV,MAAM,CAACtH,KAAK,CAACiI,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,CAC3ErC,SAAS,CAAC,CACX;MAAEC,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAE,0BAA0B;MAAEmC,MAAM,EAAE,aAAa;MAAEC,YAAY,EAAE;IAAM,CAAC,EAChG;MAAEtC,MAAM,EAAE,GAAG;MAAEE,SAAS,EAAE,0BAA0B;MAAEmC,MAAM,EAAE,aAAa;MAAEC,YAAY,EAAE;IAAgB,CAAC,EAC5G;MAAEtC,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAEiD,WAAW;MAAEd,MAAM,EAAE,gBAAgB;MAAEC,YAAY,EAAE;IAAgB,CAAC,CACjG,CAAC;IACFvB,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEzB,gBAAgB,EAAEJ,iBAAiB,CAAC,CAAC;EAC1F,CAAC,MACI;IACD;IACA;IACA,MAAMsC,iBAAiB,GAAGnG,oBAAoB,CAACC,oBAAoB;IACnE,MAAMoH,aAAa,GAAG,2BAA2BlB,iBAAiB,GAAG;IACrE,MAAMmB,WAAW,GAAG,wBAAwB;IAC5C5B,mBAAmB,CACdV,UAAU,CAACD,YAAY,CAAC,CACxBsB,WAAW,CAAC;MACbhC,SAAS,EAAEiD;IACf,CAAC,CAAC,CACGxD,MAAM,CAAC,WAAW,EAAEuD,aAAa,EAAEC,WAAW,CAAC;IACpD,MAAMZ,eAAe,GAAGtJ,0DAAe,CAAC,CAAC,CACpC4H,UAAU,CAACS,gBAAgB,CAAClE,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D8E,WAAW,CAAC;MACbhC,SAAS,EAAEiD,WAAW;MACtBlD,OAAO,EAAE;IACb,CAAC,CAAC,CACGN,MAAM,CAAC,WAAW,EAAEuD,aAAa,EAAEC,WAAW,CAAC;IACpDpC,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;EACtE;EACA,OAAOxB,aAAa;AACxB,CAAC;AAED,MAAMsC,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,MAAM3D,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CACtC0G,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzC,MAAMC,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CAAC8G,SAAS,CAAC,CACjD;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAmB,CAAC,EAC3D;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAkB,CAAC,CAC1D,CAAC;EACF,OAAO;IAAER,iBAAiB;IAAEI,gBAAgB;IAAEK,gBAAgB,EAAExG;EAAU,CAAC;AAC/E,CAAC;AACD;AACA;AACA;AACA,MAAM2J,gBAAgB,GAAGA,CAAC3C,MAAM,EAAEtB,IAAI,KAAK;EACvC,MAAM;IAAEC,iBAAiB;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAClD,MAAMlC,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;EACnC,MAAM;IAAEb,gBAAgB;IAAEJ,iBAAiB;IAAES;EAAiB,CAAC,GAAGb,iBAAiB,KAAK3F,SAAS,GAAGyF,yBAAyB,CAACC,IAAI,CAAC,GAAGgE,oBAAoB,CAAC,CAAC;EAC5J3D,iBAAiB,CAACmB,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChE0C,gBAAgB,CAACe,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,gBAAgB,CAAC,CAAC;EACjE;EACA;EACA,CAACmC,cAAc,KAAKY,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACU,UAAU,CAACF,MAAM,CAACvD,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC;EACvJ,MAAM2D,aAAa,GAAG9H,0DAAe,CAAC,CAAC,CAClC4H,UAAU,CAACF,MAAM,CAAC,CAClBnC,MAAM,CAAC,gCAAgC,CAAC,CACxCC,QAAQ,CAAC,GAAG,CAAC,CACbuC,YAAY,CAAC,CAACtB,iBAAiB,EAAEI,gBAAgB,CAAC,CAAC;EACxD,IAAIK,gBAAgB,EAAE;IAClBY,aAAa,CAACC,YAAY,CAACb,gBAAgB,CAAC;EAChD;EACA,OAAOY,aAAa;AACxB,CAAC;AAED,MAAMwC,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,MAAM7D,iBAAiB,GAAGzG,0DAAe,CAAC,CAAC,CAAC0G,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAC3F,MAAMG,gBAAgB,GAAG7G,0DAAe,CAAC,CAAC,CAAC8G,SAAS,CAAC,CACjD;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAkB,CAAC,EAC1D;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,CAC3D,CAAC;EACF,OAAO;IAAER,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAM0D,gBAAgB,GAAGA,CAAC7C,MAAM,EAAEtB,IAAI,KAAK;EACvC,MAAM;IAAEC;EAAkB,CAAC,GAAGD,IAAI;EAClC,MAAMlC,IAAI,GAAG5F,uDAAc,CAACoJ,MAAM,CAAC;EACnC,MAAM;IAAEb,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGJ,iBAAiB,KAAK3F,SAAS,GAAG0G,yBAAyB,CAAChB,IAAI,CAAC,GAAGkE,oBAAoB,CAAC,CAAC;EAC1I7D,iBAAiB,CAACmB,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChE0C,gBAAgB,CAACe,UAAU,CAAC1D,IAAI,CAACC,aAAa,CAAC,gBAAgB,CAAC,CAAC;EACjE,MAAM2D,aAAa,GAAG9H,0DAAe,CAAC,CAAC,CAClCuF,MAAM,CAAC,kCAAkC,CAAC,CAC1CC,QAAQ,CAAC,GAAG,CAAC,CACbuC,YAAY,CAAC,CAACtB,iBAAiB,EAAEI,gBAAgB,CAAC,CAAC;EACxD,OAAOiB,aAAa;AACxB,CAAC;AAED,MAAM0C,kBAAkB,GAAGA,CAAC9C,MAAM,EAAE+C,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEnJ,kBAAkB,EAAEU,SAAS,EAAE0I,WAAW,GAAG,EAAE,EAAEtE,cAAc,EAAEuE,oBAAoB,EAAE7H,SAAS,EAAE8H,kBAAkB,KAAK;EACnM;EACA,MAAMxD,eAAe,GAAG,CACpB;IAAEP,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAA0B,CAAC,EACjD;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAK,CAAC,CAC/B;EACD,MAAMO,cAAc,GAAG,CACnB;IAAER,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAA0B,CAAC,EACjD;IAAED,MAAM,EAAE,CAAC,GAAGvF,kBAAkB;IAAEwF,OAAO,EAAE;EAAE,CAAC,EAC9C;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAM+D,aAAa,GAAG;IAClBC,iBAAiB,EAAE,CACf;MAAEjE,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAE;IAAiB,CAAC,EAC1C;MAAEF,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAE;IAAmB,CAAC,CAC/C;IACDgE,kBAAkB,EAAEzJ,kBAAkB,KAAK,CAAC,GAAG+F,cAAc,GAAGD,eAAe;IAC/E4D,iBAAiB,EAAE,CACf;MAAEnE,MAAM,EAAE,CAAC;MAAEI,SAAS,EAAE;IAAO,CAAC,EAChC;MAAEJ,MAAM,EAAE,CAAC;MAAEI,SAAS,EAAE;IAAK,CAAC;EAEtC,CAAC;EACD,MAAM7D,SAAS,GAAGoE,MAAM,CAACvD,aAAa,CAAC,aAAa,CAAC;EACrD,MAAMjB,MAAM,GAAGwH,SAAS,CAACS,YAAY;EACrC,IAAI9E,iBAAiB,GAAGsE,iBAAiB;EACzC,IAAI5D,MAAM,GAAG,CAAC;EACd,IAAI1D,uBAAuB,GAAG,KAAK;EACnC,IAAI+H,cAAc,GAAG,IAAI;EACzB,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIC,qBAAqB,GAAG,IAAI;EAChC,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,MAAM/H,iBAAiB,GAAG,IAAI;EAC9B,MAAMgI,aAAa,GAAGZ,WAAW,CAACA,WAAW,CAACd,MAAM,GAAG,CAAC,CAAC;EACzD,MAAM2B,aAAa,GAAGb,WAAW,CAAC,CAAC,CAAC;EACpC,MAAM/D,gBAAgB,GAAG3E,SAAS,CAACwJ,eAAe,CAACC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK,kBAAkB,CAAC;EAC/F,MAAMpF,iBAAiB,GAAGvE,SAAS,CAACwJ,eAAe,CAACC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK,mBAAmB,CAAC;EACjG,MAAM3E,gBAAgB,GAAGhF,SAAS,CAACwJ,eAAe,CAACC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK,kBAAkB,CAAC;EAC/F,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzBpE,MAAM,CAACxG,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAClDsB,UAAU,CAACvJ,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACtD;AACR;AACA;AACA;AACA;IACQzB,MAAM,CAACqE,SAAS,CAACC,MAAM,CAAC7M,oDAAwB,CAAC;EACrD,CAAC;EACD,MAAM8M,eAAe,GAAGA,CAAA,KAAM;IAC1BvE,MAAM,CAACxG,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAClDsB,UAAU,CAACvJ,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACtD;AACR;AACA;AACA;AACA;AACA;AACA;IACQzB,MAAM,CAACqE,SAAS,CAACG,GAAG,CAAC/M,oDAAwB,CAAC;EAClD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAMgN,kBAAkB,GAAIC,WAAW,IAAK;IACxC,IAAI,CAACf,eAAe,EAAE;MAClBA,eAAe,GAAGzB,KAAK,CAACC,IAAI,CAACnC,MAAM,CAACG,gBAAgB,CAAC,YAAY,CAAC,CAAC;MACnE,IAAI,CAACwD,eAAe,CAACvB,MAAM,EAAE;QACzB;MACJ;IACJ;IACA,MAAMuC,IAAI,GAAG3E,MAAM,CAACvD,aAAa,CAAC,WAAW,CAAC;IAC9CoH,kBAAkB,GAAGa,WAAW;IAChC,IAAIA,WAAW,KAAK,YAAY,EAAE;MAC9Bf,eAAe,CAACiB,OAAO,CAAEC,cAAc,IAAK;QACxC;QACAA,cAAc,CAACR,SAAS,CAACC,MAAM,CAAC,qBAAqB,CAAC;QACtDO,cAAc,CAACrL,KAAK,CAACsL,cAAc,CAAC,UAAU,CAAC;QAC/CD,cAAc,CAACrL,KAAK,CAACsL,cAAc,CAAC,OAAO,CAAC;QAC5CD,cAAc,CAACrL,KAAK,CAACsL,cAAc,CAAC,QAAQ,CAAC;QAC7CD,cAAc,CAACrL,KAAK,CAACsL,cAAc,CAAC,KAAK,CAAC;QAC1CD,cAAc,CAACrL,KAAK,CAACsL,cAAc,CAAC,MAAM,CAAC;QAC3CH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACnL,KAAK,CAACsL,cAAc,CAAC,gBAAgB,CAAC;QACvF;QACAH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,WAAW,CAACF,cAAc,CAAC;MAChF,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAIG,aAAa,GAAG,CAAC;MACrBrB,eAAe,CAACiB,OAAO,CAAC,CAACC,cAAc,EAAEI,KAAK,KAAK;QAC/C;QACA,MAAMC,kBAAkB,GAAGL,cAAc,CAACM,qBAAqB,CAAC,CAAC;QACjE,MAAMC,QAAQ,GAAGrE,QAAQ,CAACC,IAAI,CAACmE,qBAAqB,CAAC,CAAC;QACtD;QACA;QACAH,aAAa,IAAIH,cAAc,CAACpB,YAAY;QAC5C;QACA;QACA,MAAM4B,WAAW,GAAGH,kBAAkB,CAACI,GAAG,GAAGF,QAAQ,CAACE,GAAG;QACzD,MAAMC,YAAY,GAAGL,kBAAkB,CAACM,IAAI,GAAGJ,QAAQ,CAACI,IAAI;QAC5D;QACA;QACAX,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,GAAGoD,cAAc,CAACY,WAAW,IAAI,CAAC;QACrFZ,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,iBAAiB,EAAE,GAAGoD,cAAc,CAACpB,YAAY,IAAI,CAAC;QACvFoB,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,cAAc,EAAE,GAAG4D,WAAW,IAAI,CAAC;QACpER,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,eAAe,EAAE,GAAG8D,YAAY,IAAI,CAAC;QACtE;QACA;QACA;QACA,IAAIN,KAAK,KAAK,CAAC,EAAE;UACbrB,qBAAqB,GAAGyB,WAAW;UACnC;UACA;UACA;UACA,MAAMK,MAAM,GAAG1F,MAAM,CAACvD,aAAa,CAAC,YAAY,CAAC;UACjD,IAAIiJ,MAAM,EAAE;YACR9B,qBAAqB,IAAI8B,MAAM,CAACjC,YAAY;UAChD;QACJ;MACJ,CAAC,CAAC;MACF;MACA;MACA;MACAE,eAAe,CAACiB,OAAO,CAAEC,cAAc,IAAK;QACxC;QACA;QACA;QACA;QACAF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACnL,KAAK,CAACiI,WAAW,CAAC,gBAAgB,EAAE,GAAGuD,aAAa,IAAI,CAAC;QAC1G;QACAH,cAAc,CAACR,SAAS,CAACG,GAAG,CAAC,qBAAqB,CAAC;QACnD;QACAK,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;QACxDoD,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,OAAO,EAAE,qBAAqB,CAAC;QAChEoD,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,QAAQ,EAAE,sBAAsB,CAAC;QAClEoD,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,KAAK,EAAE,mBAAmB,CAAC;QAC5DoD,cAAc,CAACrL,KAAK,CAACiI,WAAW,CAAC,MAAM,EAAE,oBAAoB,CAAC;QAC9D;QACAV,QAAQ,CAACC,IAAI,CAAC+D,WAAW,CAACF,cAAc,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI1F,gBAAgB,IAAIJ,iBAAiB,EAAE;IACvCI,gBAAgB,CAACC,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACC,iBAAiB,CAAC,CAAC;IAChEvE,iBAAiB,CAACK,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACE,kBAAkB,CAAC,CAAC;IAClE/D,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACJ,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACG,iBAAiB,CAAC,CAAC;IACpIhJ,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAG4B,iBAAiB,CAAC;IACpD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMgH,oBAAoB,GAAGhH,iBAAiB,GAAG7E,kBAAkB;IACnE,IAAI6L,oBAAoB,EAAE;MACtBvB,cAAc,CAAC,CAAC;IACpB,CAAC,MACI;MACDG,eAAe,CAAC,CAAC;IACrB;EACJ;EACA,IAAI3I,SAAS,IAAI+C,iBAAiB,KAAKmF,aAAa,IAAIlF,cAAc,EAAE;IACpEhD,SAAS,CAACM,OAAO,GAAG,KAAK;EAC7B;EACA,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IACzB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMR,SAAS,GAAG/F,qDAAqB,CAACuG,MAAM,CAACE,KAAK,CAACD,MAAM,CAAC;IAC5DsC,iBAAiB,GAAGwE,oBAAoB,CAAC,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACvE,cAAc,IAAIhD,SAAS,EAAE;MAC9B,MAAMC,QAAQ,GAAG9F,qDAAY,CAAC6F,SAAS,CAAC,GAAGhF,uDAAc,CAACgF,SAAS,CAAC,CAACa,aAAa,CAAC,eAAe,CAAC,GAAGb,SAAS;MAC/G,OAAOC,QAAQ,CAACc,SAAS,KAAK,CAAC;IACnC;IACA,IAAIgC,iBAAiB,KAAK,CAAC,IAAI/C,SAAS,EAAE;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,QAAQ,GAAG9F,qDAAY,CAAC6F,SAAS,CAAC,GAAGhF,uDAAc,CAACgF,SAAS,CAAC,CAACa,aAAa,CAAC,eAAe,CAAC,GAAGb,SAAS;MAC/G,MAAMc,qBAAqB,GAAG,CAAC,CAACd,SAAS,CAACa,aAAa,CAAC,eAAe,CAAC;MACxE,OAAO,CAACC,qBAAqB,IAAIb,QAAQ,CAACc,SAAS,KAAK,CAAC;IAC7D;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAME,OAAO,GAAIT,MAAM,IAAK;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQT,uBAAuB,GAAGqE,MAAM,CAACvF,UAAU,KAAKzB,SAAS,IAAIgH,MAAM,CAACvF,UAAU,KAAK,IAAI,IAAIsJ,aAAa,KAAK,CAAC;IAC9G;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACnF,cAAc,EAAE;MACjB,MAAMgH,QAAQ,GAAG/P,qDAAqB,CAACuG,MAAM,CAACE,KAAK,CAACD,MAAM,CAAC;MAC3DqH,cAAc,GACVkC,QAAQ,IAAI7P,qDAAY,CAAC6P,QAAQ,CAAC,GAAGhP,uDAAc,CAACgP,QAAQ,CAAC,CAACnJ,aAAa,CAAC,eAAe,CAAC,GAAGmJ,QAAQ;IAC/G;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAChH,cAAc,EAAE;MACjB6F,kBAAkB,CAAC,QAAQ,CAAC;IAChC;IACA;AACR;AACA;AACA;IACQ,IAAIrI,MAAM,CAACU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MAChCA,SAAS,CAACM,OAAO,GAAG,KAAK;IAC7B;IACArF,uDAAG,CAAC,MAAM;MACN;AACZ;AACA;AACA;MACYmJ,MAAM,CAAC6F,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC;IACFrL,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAG4B,iBAAiB,CAAC;EACxD,CAAC;EACD,MAAM3B,MAAM,GAAIZ,MAAM,IAAK;IACvB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACwC,cAAc,IAAIgF,qBAAqB,KAAK,IAAI,IAAIC,kBAAkB,KAAK,IAAI,EAAE;MAClF;MACA,IAAIzH,MAAM,CAAC0J,QAAQ,IAAIlC,qBAAqB,IAAIC,kBAAkB,KAAK,QAAQ,EAAE;QAC7EY,kBAAkB,CAAC,YAAY,CAAC;MACpC,CAAC,MACI,IAAIrI,MAAM,CAAC0J,QAAQ,GAAGlC,qBAAqB,IAAIC,kBAAkB,KAAK,YAAY,EAAE;QACrFY,kBAAkB,CAAC,QAAQ,CAAC;MAChC;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI,CAAC7F,cAAc,IAAIxC,MAAM,CAACU,MAAM,IAAI,CAAC,IAAI4G,cAAc,EAAE;MACzD;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAItH,MAAM,CAACU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MAChCA,SAAS,CAACM,OAAO,GAAG,KAAK;IAC7B;IACA;AACR;AACA;AACA;AACA;IACQ,MAAM6J,WAAW,GAAG,CAAC,GAAGpH,iBAAiB;IACzC,MAAMqH,sBAAsB,GAAG9C,WAAW,CAACd,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGc,WAAW,CAAC,CAAC,CAAC,GAAGlK,SAAS;IACtF,MAAMiE,IAAI,GAAG8I,WAAW,GAAG3J,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACjD,MAAM0B,iCAAiC,GAAG8I,sBAAsB,KAAKhN,SAAS,IAAIiE,IAAI,IAAI+I,sBAAsB,IAAIrK,uBAAuB;IAC3I;AACR;AACA;AACA;AACA;IACQ,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMsB,aAAa,GAAGF,iCAAiC,IAAI8I,sBAAsB,KAAKhN,SAAS,GACzFgN,sBAAsB,GACpBhL,mBAAmB,CAAC,CAACiC,IAAI,GAAG+I,sBAAsB,KAAK7I,OAAO,GAAG6I,sBAAsB,CAAC,CAAC,GAC3F/I,IAAI;IACVoC,MAAM,GAAG3I,uDAAK,CAAC,MAAM,EAAE0G,aAAa,EAAED,OAAO,CAAC;IAC9C3C,SAAS,CAAC8C,YAAY,CAAC+B,MAAM,CAAC;EAClC,CAAC;EACD,MAAM9B,KAAK,GAAInB,MAAM,IAAK;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwC,cAAc,IAAIxC,MAAM,CAACU,MAAM,IAAI,CAAC,IAAI4G,cAAc,IAAIA,cAAc,CAAC/G,SAAS,GAAG,CAAC,EAAE;MACzF;AACZ;AACA;AACA;AACA;AACA;AACA;MACY8H,kBAAkB,CAAC,YAAY,CAAC;MAChC;IACJ;IACA;AACR;AACA;AACA;IACQ,MAAMjH,QAAQ,GAAGpB,MAAM,CAACqB,SAAS;IACjC,MAAMC,SAAS,GAAG,CAACtB,MAAM,CAACU,MAAM,GAAGU,QAAQ,GAAG,GAAG,IAAIhC,MAAM;IAC3D,MAAMyK,IAAI,GAAGtH,iBAAiB,GAAGjB,SAAS;IAC1C,MAAMnB,OAAO,GAAG2G,WAAW,CAACgD,MAAM,CAAC,CAAChQ,CAAC,EAAEY,CAAC,KAAK;MACzC,OAAOyH,IAAI,CAACC,GAAG,CAAC1H,CAAC,GAAGmP,IAAI,CAAC,GAAG1H,IAAI,CAACC,GAAG,CAACtI,CAAC,GAAG+P,IAAI,CAAC,GAAGnP,CAAC,GAAGZ,CAAC;IAC1D,CAAC,CAAC;IACFiQ,qBAAqB,CAAC;MAClBC,UAAU,EAAE7J,OAAO;MACnB8J,gBAAgB,EAAEhH,MAAM;MACxB5E,UAAU,EAAEkB,uBAAuB;MACnC;AACZ;AACA;AACA;MACY2K,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC;EACD,MAAMH,qBAAqB,GAAIjN,OAAO,IAAK;IACvC,MAAM;MAAEkN,UAAU;MAAE3L,UAAU;MAAE4L,gBAAgB;MAAEC;IAAS,CAAC,GAAGpN,OAAO;IACtE;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMqN,oBAAoB,GAAG9L,UAAU,IAAI2L,UAAU,KAAK,CAAC;IAC3D,MAAMI,gBAAgB,GAAGD,oBAAoB,GAAG5H,iBAAiB,GAAGyH,UAAU;IAC9E,MAAMK,gBAAgB,GAAGD,gBAAgB,KAAK,CAAC;IAC/C7H,iBAAiB,GAAG,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAIQ,gBAAgB,IAAIJ,iBAAiB,EAAE;MACvCI,gBAAgB,CAACC,SAAS,CAAC,CACvB;QAAEC,MAAM,EAAE,CAAC;QAAEE,SAAS,EAAE,cAAc8G,gBAAgB,GAAG,GAAG;MAAK,CAAC,EAClE;QAAEhH,MAAM,EAAE,CAAC;QAAEE,SAAS,EAAE,cAAc,CAAC,CAAC,GAAGiH,gBAAgB,IAAI,GAAG;MAAK,CAAC,CAC3E,CAAC;MACFzH,iBAAiB,CAACK,SAAS,CAAC,CACxB;QACIC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,kCAAkC1F,wBAAwB,CAAC,CAAC,GAAGyM,gBAAgB,EAAEvM,kBAAkB,CAAC;MACjH,CAAC,EACD;QACIuF,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,kCAAkC1F,wBAAwB,CAAC4M,gBAAgB,EAAE1M,kBAAkB,CAAC;MAC7G,CAAC,CACJ,CAAC;MACF,IAAI0F,gBAAgB,EAAE;QAClB;AAChB;AACA;AACA;AACA;AACA;AACA;QACgBA,gBAAgB,CAACJ,SAAS,CAAC,CACvB;UAAEC,MAAM,EAAE,CAAC;UAAEI,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG4G,gBAAgB,IAAI,GAAG;QAAI,CAAC,EAC5D;UAAEhH,MAAM,EAAE,CAAC;UAAEI,SAAS,EAAE,GAAG+G,gBAAgB,GAAG,GAAG;QAAI,CAAC,CACzD,CAAC;MACN;MACAhM,SAAS,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;IACQU,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;IACrB,IAAIsI,oBAAoB,EAAE;MACtBlM,gBAAgB,CAAC2F,MAAM,EAAExF,SAAS,CAAC;IACvC,CAAC,MACI,IAAI,CAACiM,gBAAgB,EAAE;MACxBnL,SAAS,CAAC,CAAC;IACf;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIM,SAAS,KAAK4K,gBAAgB,KAAKtD,WAAW,CAACA,WAAW,CAACd,MAAM,GAAG,CAAC,CAAC,IAAI,CAACxD,cAAc,CAAC,EAAE;MAC5FhD,SAAS,CAACM,OAAO,GAAG,IAAI;IAC5B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC0C,cAAc,IAAI4H,gBAAgB,KAAK,CAAC,EAAE;MAC3C/B,kBAAkB,CAAC,YAAY,CAAC;IACpC;IACA,OAAO,IAAIiC,OAAO,CAAEC,OAAO,IAAK;MAC5BnM,SAAS,CACJI,QAAQ,CAAC,MAAM;QAChB,IAAI6L,gBAAgB,EAAE;UAClB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAAC7H,cAAc,EAAE;YACjB6F,kBAAkB,CAAC,YAAY,CAAC;UACpC;UACA;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAItF,gBAAgB,IAAIJ,iBAAiB,EAAE;YACvClI,uDAAG,CAAC,MAAM;cACNsI,gBAAgB,CAACC,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACC,iBAAiB,CAAC,CAAC;cAChEvE,iBAAiB,CAACK,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACE,kBAAkB,CAAC,CAAC;cAClE/D,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACJ,SAAS,CAAC,CAAC,GAAGiE,aAAa,CAACG,iBAAiB,CAAC,CAAC;cACpIhJ,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAGyJ,gBAAgB,CAAC;cACnD7H,iBAAiB,GAAG6H,gBAAgB;cACpCpD,kBAAkB,CAACzE,iBAAiB,CAAC;cACrC;AAC5B;AACA;AACA;cAC4B,MAAMgH,oBAAoB,GAAGhH,iBAAiB,GAAG7E,kBAAkB;cACnE,IAAI6L,oBAAoB,EAAE;gBACtBvB,cAAc,CAAC,CAAC;cACpB,CAAC,MACI;gBACDG,eAAe,CAAC,CAAC;cACrB;cACAvG,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cACpB0I,OAAO,CAAC,CAAC;YACb,CAAC,CAAC;UACN,CAAC,MACI;YACD3I,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;YACpB0I,OAAO,CAAC,CAAC;UACb;QACJ,CAAC,MACI;UACDA,OAAO,CAAC,CAAC;QACb;QACA;AAChB;AACA;AACA;AACA;MACY,CAAC,EAAE;QAAE9L,eAAe,EAAE;MAAK,CAAC,CAAC,CACxBqD,WAAW,CAAC,CAAC,EAAE,CAAC,EAAEoI,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN,CAAC;EACD,MAAMtI,OAAO,GAAGxF,kEAAa,CAAC;IAC1B+B,EAAE,EAAEyI,SAAS;IACb7E,WAAW,EAAE,YAAY;IACzBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,GAAG;IACdX,SAAS,EAAE,EAAE;IACbvB,QAAQ;IACRU,OAAO;IACPG,MAAM;IACNO;EACJ,CAAC,CAAC;EACF,OAAO;IACHS,OAAO;IACPmI;EACJ,CAAC;AACL,CAAC;AAED,MAAMS,WAAW,GAAG,82HAA82H;AAEl4H,MAAMC,UAAU,GAAG,8kFAA8kF;AAEjmF,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBpS,qDAAgB,CAAC,IAAI,EAAEoS,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGnS,qDAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACoS,WAAW,GAAGpS,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACqS,WAAW,GAAGrS,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACsS,UAAU,GAAGtS,qDAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACuS,sBAAsB,GAAGvS,qDAAW,CAAC,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAC;IAC5E,IAAI,CAACwS,mBAAmB,GAAGxS,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACyS,oBAAoB,GAAGzS,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC0S,oBAAoB,GAAG1S,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC2S,mBAAmB,GAAG3S,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4S,QAAQ,GAAG5S,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC6S,cAAc,GAAGzQ,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAAC0Q,iBAAiB,GAAGlQ,wDAAuB,CAAC,CAAC;IAClD,IAAI,CAACmQ,YAAY,GAAGtR,iEAAY,CAAC,CAAC;IAClC,IAAI,CAACuR,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACxJ,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC9E,kBAAkB,GAAG,CAAC;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACuO,cAAc,GAAG,MAAM;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;IACQ,IAAI,CAACjC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC5K,MAAM,GAAG,KAAK;IACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8M,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAChO,UAAU,GAAG,IAAI;IACtB,IAAI,CAACiO,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEC,eAAe;QAAEN;MAAe,CAAC,GAAG,IAAI;MAChD,IAAIA,cAAc,KAAK,OAAO,IAAIM,eAAe,KAAK3P,SAAS,EAAE;QAC7D;AAChB;AACA;AACA;AACA;QACgB;MACJ;MACA,IAAI,CAAC4P,oBAAoB,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEF;MAAgB,CAAC,GAAG,IAAI;MAChC,IAAIA,eAAe,KAAK3P,SAAS,EAAE;QAC/B;AAChB;AACA;AACA;AACA;AACA;QACgB;MACJ;MACA,IAAI,CAAChB,OAAO,CAACgB,SAAS,EAAEpB,oDAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACkR,WAAW,GAAIC,UAAU,IAAK;MAC/B,MAAMxO,EAAE,GAAG,IAAI,CAACyO,YAAY;MAC5B,MAAMC,IAAI,GAAGC,aAAa,CAACH,UAAU,CAACI,IAAI,CAAC;MAC3C,IAAI5O,EAAE,IAAI0O,IAAI,EAAE;QACZ,MAAMG,EAAE,GAAG,IAAIC,WAAW,CAACJ,IAAI,EAAE;UAC7BK,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBnN,MAAM,EAAE2M,UAAU,CAAC3M;QACvB,CAAC,CAAC;QACF7B,EAAE,CAACiP,aAAa,CAACJ,EAAE,CAAC;MACxB;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACK,YAAY,GAAIL,EAAE,IAAK;MACxB,MAAM;QAAEM,YAAY;QAAEnP;MAAG,CAAC,GAAG,IAAI;MACjC;MACA,IAAI6O,EAAE,CAAC/M,MAAM,KAAK9B,EAAE,IAAImP,YAAY,IAAIA,YAAY,CAACC,QAAQ,KAAK,CAAC,CAAC,EAAE;QAClED,YAAY,CAAC7D,KAAK,CAAC,CAAC;MACxB;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+D,YAAY,GAAG,CAAC;MAAEvN;IAAO,CAAC,KAAK;MAChC,MAAMwN,IAAI,GAAGxN,MAAM;MACnBwN,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAClF,OAAO,CAAErK,EAAE,IAAK;QACpCA,EAAE,CAAC4F,gBAAgB,CAAC,WAAW,CAAC,CAACyE,OAAO,CAAEmF,UAAU,IAAK;UACrD;UACA;UACA;UACA,IAAIA,UAAU,CAACC,YAAY,CAAC,uBAAuB,CAAC,KAAK,IAAI,EAAE;YAC3DD,UAAU,CAACE,YAAY,CAAC,uBAAuB,EAAE,IAAI,CAAC1P,EAAE,CAAC4J,EAAE,CAAC;UAChE;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC;EACL;EACA+F,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACrS,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIoS,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACpS,OAAO,CAAC,CAAC;IAClB;EACJ;EACAqS,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,OAAO;MAAE/P,EAAE;MAAEqN;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAI0C,OAAO,EAAE;MACT1C,iBAAiB,CAAC2C,gBAAgB,CAAChQ,EAAE,EAAE+P,OAAO,CAAC;IACnD;EACJ;EACAE,cAAcA,CAAA,EAAG;IACb;IACA,IAAIxV,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC0L,iBAAiB,IAAI,IAAI,CAAC+J,cAAc,IAAI,IAAI,CAACC,cAAc,EAAE;MACrG;IACJ;IACAC,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;IAChC,IAAI,CAACA,aAAa,GAAGC,UAAU,CAAC,MAAM;MAClC,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC/B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACZ;EACAC,kBAAkBA,CAAC7H,WAAW,EAAE;IAC5B,IAAIA,WAAW,KAAKlK,SAAS,EAAE;MAC3B,IAAI,CAACgS,iBAAiB,GAAG9H,WAAW,CAAC+H,IAAI,CAAC,CAAC/U,CAAC,EAAEY,CAAC,KAAKZ,CAAC,GAAGY,CAAC,CAAC;IAC9D;EACJ;EACAoU,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAE3Q;IAAG,CAAC,GAAG,IAAI;IACnB1C,wDAAc,CAAC0C,EAAE,CAAC;IAClB,IAAI,CAAC8P,cAAc,CAAC,CAAC;EACzB;EACAc,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACvD,iBAAiB,CAACwD,mBAAmB,CAAC,CAAC;IAC5C,IAAI,CAACC,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,MAAM;MAAEtI,WAAW;MAAED,iBAAiB;MAAE1I,EAAE;MAAEkR;IAAe,CAAC,GAAG,IAAI;IACnE,MAAM3D,YAAY,GAAI,IAAI,CAACA,YAAY,GAAG5E,WAAW,KAAKlK,SAAS,IAAIiK,iBAAiB,KAAKjK,SAAU;IACvG,MAAM0S,mBAAmB,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC;IAClD,IAAI,CAAC3D,mBAAmB,GAAGhR,uDAAiB,CAACwD,EAAE,EAAEmR,mBAAmB,CAAC;IACrE;IACA,IAAInR,EAAE,CAACoR,UAAU,EAAE;MACf,IAAI,CAACC,oBAAoB,GAAGrR,EAAE,CAACoR,UAAU;IAC7C;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIF,cAAc,KAAKzS,SAAS,EAAE;MAC9B0S,mBAAmB,CAAC9G,OAAO,CAAEiH,SAAS,IAAK;QACvC,MAAMC,cAAc,GAAGL,cAAc,CAACI,SAAS,CAAC;QAChD,IAAIC,cAAc,EAAE;UAChB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAAC/D,mBAAmB,GAAGgE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjE,mBAAmB,CAAC,EAAE;YAAE,CAAC8D,SAAS,GAAGJ,cAAc,CAACI,SAAS;UAAE,CAAC,CAAC;UACjI,OAAOJ,cAAc,CAACI,SAAS,CAAC;QACpC;MACJ,CAAC,CAAC;IACN;IACA,IAAI/D,YAAY,EAAE;MACd,IAAI,CAACnJ,iBAAiB,GAAG,IAAI,CAACsE,iBAAiB;IACnD;IACA,IAAIC,WAAW,KAAKlK,SAAS,IAAIiK,iBAAiB,KAAKjK,SAAS,IAAI,CAACkK,WAAW,CAAC+I,QAAQ,CAAChJ,iBAAiB,CAAC,EAAE;MAC1G/N,qDAAe,CAAC,gFAAgF,CAAC;IACrG;IACA,IAAI,EAAE,CAACsW,EAAE,GAAG,IAAI,CAACC,cAAc,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrH,EAAE,CAAC,EAAE;MAC1ErM,wDAAY,CAAC,IAAI,CAACyC,EAAE,CAAC;IACzB;EACJ;EACA2R,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACxQ,MAAM,KAAK,IAAI,EAAE;MACtB7E,uDAAG,CAAC,MAAM,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA,IAAI,CAACgT,kBAAkB,CAAC,IAAI,CAAC7H,WAAW,CAAC;IACzC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmH,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8B,WAAWA,CAACC,KAAK,GAAG,KAAK,EAAE;IACvB,IAAI,IAAI,CAACC,eAAe,IAAI,CAACD,KAAK,EAAE;MAChC,OAAO;QACHE,QAAQ,EAAE,IAAI,CAACD,eAAe;QAC9BrE,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMuE,QAAQ,GAAG,IAAI,CAAChS,EAAE,CAACoR,UAAU;IACnC,MAAM3D,MAAM,GAAI,IAAI,CAACA,MAAM,GAAGuE,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAACpE,aAAc;IACvE,MAAMmE,QAAQ,GAAI,IAAI,CAACD,eAAe,GAAGrE,MAAM,GAAG,IAAI,CAACsE,QAAQ,IAAI,IAAI,CAACzE,YAAY,GAAG,IAAI,CAACyE,QAAS;IACrG,OAAO;MAAEtE,MAAM;MAAEsE;IAAS,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACUE,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAArT,yMAAA;MAC9B,MAAM;QAAEmB;MAAW,CAAC,GAAGkS,KAAI;MAC3B,IAAI,OAAOlS,UAAU,KAAK,UAAU,EAAE;QAClC,OAAOA,UAAU,CAACgS,IAAI,EAAEC,IAAI,CAAC;MACjC;MACA,OAAOjS,UAAU;IAAC;EACtB;EACA;AACJ;AACA;EACU1C,OAAOA,CAAA,EAAG;IAAA,IAAA6U,MAAA;IAAA,OAAAtT,yMAAA;MACZ,MAAMuT,MAAM,SAASD,MAAI,CAACjF,cAAc,CAACmF,IAAI,CAAC,CAAC;MAC/C,IAAIF,MAAI,CAAC1E,SAAS,EAAE;QAChB2E,MAAM,CAAC,CAAC;QACR;MACJ;MACA,MAAM;QAAEnM,iBAAiB;QAAEnG;MAAG,CAAC,GAAGqS,MAAI;MACtC;AACR;AACA;AACA;MACQA,MAAI,CAACjO,iBAAiB,GAAGiO,MAAI,CAAC3J,iBAAiB;MAC/C,MAAM;QAAE+E,MAAM;QAAEsE;MAAS,CAAC,GAAGM,MAAI,CAACT,WAAW,CAAC,IAAI,CAAC;MACnD;AACR;AACA;AACA;AACA;MACQS,MAAI,CAAClF,QAAQ,CAACqF,IAAI,CAAC,CAAC;MACpBH,MAAI,CAAC5D,YAAY,SAASxS,iEAAe,CAAC8V,QAAQ,EAAE/R,EAAE,EAAEqS,MAAI,CAACI,SAAS,EAAE,CAAC,UAAU,CAAC,EAAEJ,MAAI,CAACK,cAAc,EAAEjF,MAAM,CAAC;MAClH;AACR;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIhR,uDAAY,CAACuD,EAAE,CAAC,EAAE;QAClB,MAAMpC,qDAAS,CAACyU,MAAI,CAAC5D,YAAY,CAAC;QAClC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,CAAC,MACI,IAAI,CAAC4D,MAAI,CAACpE,mBAAmB,EAAE;QAChC,MAAMpQ,qDAAY,CAAC,CAAC;MACxB;MACAhD,qDAAS,CAAC,MAAMwX,MAAI,CAACrS,EAAE,CAAC8J,SAAS,CAACG,GAAG,CAAC,YAAY,CAAC,CAAC;MACpD,MAAMhE,YAAY,GAAGE,iBAAiB,KAAK1H,SAAS;MACpD;AACR;AACA;AACA;AACA;MACQ,IAAIwH,YAAY,IAAIxL,qDAAU,CAAC4X,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5C;QACAA,MAAI,CAACvR,cAAc,SAAS1C,SAAS,CAACS,QAAQ,CAAC,CAAC;QAChDY,oBAAoB,CAAC,CAAC;MAC1B;MACA,MAAMjC,wDAAO,CAAC6U,MAAI,EAAE,YAAY,EAAE7M,iBAAiB,EAAE4C,gBAAgB,EAAE;QACnE1C,YAAY,EAAES,iBAAiB;QAC/B/B,iBAAiB,EAAEiO,MAAI,CAAC3J,iBAAiB;QACzCnJ,kBAAkB,EAAE8S,MAAI,CAAC9S,kBAAkB;QAC3C8E,cAAc,EAAEgO,MAAI,CAAChO;MACzB,CAAC,CAAC;MACF;MACA,IAAI,OAAO2B,MAAM,KAAK,WAAW,EAAE;QAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;QACYqM,MAAI,CAACM,oBAAoB,GAAG,MAAM;UAC9B,IAAIN,MAAI,CAAC5O,OAAO,EAAE;YACd;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB4O,MAAI,CAAC5O,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;YAC1BpH,uDAAG,CAAC,MAAM;cACN,IAAI+V,MAAI,CAAC5O,OAAO,EAAE;gBACd4O,MAAI,CAAC5O,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cAC7B;YACJ,CAAC,CAAC;UACN;QACJ,CAAC;QACDsC,MAAM,CAAC4M,gBAAgB,CAAC9U,qEAAiB,EAAEuU,MAAI,CAACM,oBAAoB,CAAC;MACzE;MACA,IAAIN,MAAI,CAAC9E,YAAY,EAAE;QACnB8E,MAAI,CAACQ,gBAAgB,CAAC,CAAC;MAC3B,CAAC,MACI,IAAI5M,YAAY,EAAE;QACnBoM,MAAI,CAACS,gBAAgB,CAAC,CAAC;MAC3B;MACA;MACAT,MAAI,CAACU,0BAA0B,CAAC,CAAC;MACjC;MACAV,MAAI,CAACW,yBAAyB,CAAC,CAAC;MAChCV,MAAM,CAAC,CAAC;IAAC;EACb;EACAQ,gBAAgBA,CAAA,EAAG;IAAA,IAAAG,MAAA;IACf,IAAIhC,EAAE;IACN,IAAIxW,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;MAC5B;IACJ;IACA,MAAM;MAAEuF;IAAG,CAAC,GAAG,IAAI;IACnB;IACA;IACA;IACA,MAAMkT,gBAAgB,GAAG,IAAI,CAAC/C,cAAc,IAAIpV,iDAAM,CAACoY,GAAG,CAAC,YAAY,EAAE5L,iBAAiB,CAAC;IAC3F,MAAMoC,GAAG,GAAI,IAAI,CAAC1J,SAAS,GAAGiT,gBAAgB,CAAClT,EAAE,EAAE;MAC/C0F,YAAY,EAAE,IAAI,CAACS,iBAAiB;MACpC9B,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC,CAAE;IACH,MAAMhD,SAAS,GAAGzF,qDAAc,CAACoE,EAAE,CAAC;IACpC,IAAI,CAACqB,SAAS,EAAE;MACZvF,qDAAuB,CAACkE,EAAE,CAAC;MAC3B;IACJ;IACA,MAAMc,cAAc,GAAG,CAACmQ,EAAE,GAAG,IAAI,CAACnQ,cAAc,MAAM,IAAI,IAAImQ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG9S,KAAK,CAACa,OAAO;IAChG,IAAI,CAACyE,OAAO,GAAG5C,yBAAyB,CAACb,EAAE,EAAE2J,GAAG,EAAE7I,cAAc,EAAE,MAAM;MACpE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC4M,0BAA0B,GAAG,IAAI;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY9N,uBAAuB,CAAC,IAAI,CAACkB,cAAc,CAAC;MAC5C,IAAI,CAACb,SAAS,CAACI,QAAQ,cAAAtB,yMAAA,CAAC,aAAY;QAChC,MAAMkU,MAAI,CAACxV,OAAO,CAACgB,SAAS,EAAE3B,oDAAO,CAAC;QACtCmW,MAAI,CAACvF,0BAA0B,GAAG,KAAK;MAC3C,CAAC,EAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACjK,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7B;EACAmP,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEpK,SAAS;MAAEC,iBAAiB;MAAEnJ;IAAmB,CAAC,GAAG,IAAI;IACjE,IAAI,CAACkJ,SAAS,IAAIC,iBAAiB,KAAKjK,SAAS,EAAE;MAC/C;IACJ;IACA,MAAMyU,gBAAgB,GAAG,IAAI,CAAChD,cAAc,IAAInV,iDAAM,CAACoY,GAAG,CAAC,YAAY,EAAE3N,iBAAiB,CAAC;IAC3F,MAAMmE,GAAG,GAAI,IAAI,CAAC1J,SAAS,GAAGiT,gBAAgB,CAAC,IAAI,CAAClT,EAAE,EAAE;MACpD0F,YAAY,EAAE,IAAI,CAACS,iBAAiB;MACpC/B,iBAAiB,EAAEsE,iBAAiB;MACpCnJ,kBAAkB;MAClB8E,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC,CAAE;IACHsF,GAAG,CAACnH,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1B,MAAM;MAAEiB,OAAO;MAAEmI;IAAsB,CAAC,GAAGrD,kBAAkB,CAAC,IAAI,CAACvI,EAAE,EAAE,IAAI,CAACwI,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEnJ,kBAAkB,EAAEoK,GAAG,EAAE,IAAI,CAAC8G,iBAAiB,EAAE,IAAI,CAACpM,cAAc,EAAE,MAAM;MAAE,IAAI4M,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG,IAAI,CAAC7M,iBAAiB,MAAM,IAAI,IAAI6M,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAAE,CAAC,EAAE,MAAM,IAAI,CAACmC,cAAc,CAAC,CAAC,EAAGvH,UAAU,IAAK;MACjU,IAAI,IAAI,CAACzH,iBAAiB,KAAKyH,UAAU,EAAE;QACvC,IAAI,CAACzH,iBAAiB,GAAGyH,UAAU;QACnC,IAAI,CAACiB,sBAAsB,CAAC0F,IAAI,CAAC;UAAE3G;QAAW,CAAC,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,CAACpI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmI,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACnI,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7B;EACA0P,cAAcA,CAAA,EAAG;IAAA,IAAAC,MAAA;IACb;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC3F,0BAA0B,GAAG,IAAI;IACtC,IAAI,CAACzN,SAAS,CAACI,QAAQ,cAAAtB,yMAAA,CAAC,aAAY;MAChCsU,MAAI,CAACjP,iBAAiB,GAAG,CAAC;MAC1BiP,MAAI,CAACvG,sBAAsB,CAAC0F,IAAI,CAAC;QAAE3G,UAAU,EAAEwH,MAAI,CAACjP;MAAkB,CAAC,CAAC;MACxE,MAAMiP,MAAI,CAAC5V,OAAO,CAACgB,SAAS,EAAE3B,oDAAO,CAAC;MACtCuW,MAAI,CAAC3F,0BAA0B,GAAG,KAAK;IAC3C,CAAC,EAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUjQ,OAAOA,CAACyU,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAmB,MAAA;IAAA,OAAAvU,yMAAA;MACtB,IAAIkS,EAAE;MACN,IAAIqC,MAAI,CAAC5F,0BAA0B,IAAIyE,IAAI,KAAKrV,oDAAO,EAAE;QACrD,OAAO,KAAK;MAChB;MACA;AACR;AACA;AACA;AACA;MACQ,MAAMwV,MAAM,SAASgB,MAAI,CAAClG,cAAc,CAACmF,IAAI,CAAC,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,MAAMe,MAAI,CAACC,mBAAmB,CAAC,CAAC;MAChC;AACR;AACA;AACA;AACA;MACQ,IAAIpB,IAAI,KAAK,SAAS,IAAI,QAAQmB,MAAI,CAACrB,eAAe,CAACC,IAAI,EAAEC,IAAI,CAAC,CAAC,EAAE;QACjEG,MAAM,CAAC,CAAC;QACR,OAAO,KAAK;MAChB;MACA,MAAM;QAAEnM;MAAkB,CAAC,GAAGmN,MAAI;MAClC;AACR;AACA;AACA;AACA;MACQ,MAAMrN,YAAY,GAAGE,iBAAiB,KAAK1H,SAAS;MACpD,IAAIwH,YAAY,IAAIxL,qDAAU,CAAC6Y,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5C1T,uBAAuB,CAAC0T,MAAI,CAACxS,cAAc,CAAC;MAChD;MACA;MACA,IAAI,OAAOkF,MAAM,KAAK,WAAW,IAAIsN,MAAI,CAACX,oBAAoB,EAAE;QAC5D3M,MAAM,CAACwN,mBAAmB,CAAC1V,qEAAiB,EAAEwV,MAAI,CAACX,oBAAoB,CAAC;QACxEW,MAAI,CAACX,oBAAoB,GAAGlU,SAAS;MACzC;MACA,MAAMgV,SAAS,SAAShW,wDAAO,CAAC6V,MAAI,EAAEpB,IAAI,EAAEC,IAAI,EAAE,YAAY,EAAE5K,iBAAiB,EAAEe,gBAAgB,EAAE;QACjG5C,YAAY,EAAES,iBAAiB;QAC/B/B,iBAAiB,EAAE,CAAC6M,EAAE,GAAGqC,MAAI,CAAClP,iBAAiB,MAAM,IAAI,IAAI6M,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGqC,MAAI,CAAC5K,iBAAiB;QACxGnJ,kBAAkB,EAAE+T,MAAI,CAAC/T,kBAAkB;QAC3C8E,cAAc,EAAEiP,MAAI,CAACjP;MACzB,CAAC,CAAC;MACF,IAAIoP,SAAS,EAAE;QACX,MAAM;UAAE1B;QAAS,CAAC,GAAGuB,MAAI,CAAC1B,WAAW,CAAC,CAAC;QACvC,MAAM1V,iEAAe,CAAC6V,QAAQ,EAAEuB,MAAI,CAAC7E,YAAY,CAAC;QAClD5T,qDAAS,CAAC,MAAMyY,MAAI,CAACtT,EAAE,CAAC8J,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC,CAAC;QACvD,IAAIuJ,MAAI,CAACrT,SAAS,EAAE;UAChBqT,MAAI,CAACrT,SAAS,CAACyT,OAAO,CAAC,CAAC;QAC5B;QACA,IAAIJ,MAAI,CAAC7P,OAAO,EAAE;UACd6P,MAAI,CAAC7P,OAAO,CAACiQ,OAAO,CAAC,CAAC;QAC1B;QACAJ,MAAI,CAACxC,6BAA6B,CAAC,CAAC;QACpCwC,MAAI,CAACvC,4BAA4B,CAAC,CAAC;MACvC;MACAuC,MAAI,CAAClP,iBAAiB,GAAG3F,SAAS;MAClC6U,MAAI,CAACrT,SAAS,GAAGxB,SAAS;MAC1B6T,MAAM,CAAC,CAAC;MACR,OAAOmB,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAOjW,wDAAW,CAAC,IAAI,CAACsC,EAAE,EAAE,oBAAoB,CAAC;EACrD;EACA;AACJ;AACA;EACI4T,aAAaA,CAAA,EAAG;IACZ,OAAOlW,wDAAW,CAAC,IAAI,CAACsC,EAAE,EAAE,qBAAqB,CAAC;EACtD;EACA;AACJ;AACA;AACA;AACA;AACA;EACU6T,oBAAoBA,CAAChI,UAAU,EAAE;IAAA,IAAAiI,MAAA;IAAA,OAAA/U,yMAAA;MACnC,IAAI,CAAC+U,MAAI,CAACvG,YAAY,EAAE;QACpB5S,qDAAe,CAAC,uEAAuE,CAAC;QACxF;MACJ;MACA,IAAI,CAACmZ,MAAI,CAACnL,WAAW,CAAC+I,QAAQ,CAAC7F,UAAU,CAAC,EAAE;QACxClR,qDAAe,CAAC,2DAA2DkR,UAAU,sFAAsF,CAAC;QAC5K;MACJ;MACA,MAAM;QAAEzH,iBAAiB;QAAEwH,qBAAqB;QAAE1L,UAAU;QAAEyI,WAAW;QAAEoD;MAAS,CAAC,GAAG+H,MAAI;MAC5F,IAAI1P,iBAAiB,KAAKyH,UAAU,EAAE;QAClC;MACJ;MACA,IAAID,qBAAqB,EAAE;QACvBkI,MAAI,CAAC1F,eAAe,GAAGxC,qBAAqB,CAAC;UACzCC,UAAU;UACVC,gBAAgB,EAAE,CAAC,GAAG1H,iBAAiB;UACvClE,UAAU,EAAEA,UAAU,KAAKzB,SAAS,IAAIyB,UAAU,KAAK,IAAI,IAAIyI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;UACnFoD;QACJ,CAAC,CAAC;QACF,MAAM+H,MAAI,CAAC1F,eAAe;QAC1B0F,MAAI,CAAC1F,eAAe,GAAG3P,SAAS;MACpC;IAAC;EACL;EACA;AACJ;AACA;EACUmK,oBAAoBA,CAAA,EAAG;IAAA,IAAAmL,MAAA;IAAA,OAAAhV,yMAAA;MACzB,OAAOgV,MAAI,CAAC3P,iBAAiB;IAAC;EAClC;EACMiK,oBAAoBA,CAAA,EAAG;IAAA,IAAA2F,MAAA;IAAA,OAAAjV,yMAAA;MACzB,MAAM;QAAE4J,WAAW;QAAEvE;MAAkB,CAAC,GAAG4P,MAAI;MAC/C,IAAI,CAACrL,WAAW,IAAIvE,iBAAiB,IAAI,IAAI,EAAE;QAC3C;AACZ;AACA;AACA;QACY,OAAO,KAAK;MAChB;MACA,MAAM6P,kBAAkB,GAAGtL,WAAW,CAACxB,MAAM,CAAE5K,CAAC,IAAKA,CAAC,KAAK,CAAC,CAAC;MAC7D,MAAM2X,sBAAsB,GAAGD,kBAAkB,CAACE,OAAO,CAAC/P,iBAAiB,CAAC;MAC5E,MAAMgQ,mBAAmB,GAAG,CAACF,sBAAsB,GAAG,CAAC,IAAID,kBAAkB,CAACpM,MAAM;MACpF,MAAMwM,cAAc,GAAGJ,kBAAkB,CAACG,mBAAmB,CAAC;MAC9D;AACR;AACA;AACA;AACA;MACQ,MAAMJ,MAAI,CAACH,oBAAoB,CAACQ,cAAc,CAAC;MAC/C,OAAO,IAAI;IAAC;EAChB;EACAtB,0BAA0BA,CAAA,EAAG;IACzB;IACA,IAAItY,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC0L,iBAAiB,IAAI,IAAI,CAAC+J,cAAc,IAAI,IAAI,CAACC,cAAc,EAAE;MACrG;IACJ;IACA;IACA,IAAI,CAACmE,qBAAqB,GAAGtO,MAAM,CAACtG,UAAU,GAAG,GAAG;EACxD;EACA6Q,oBAAoBA,CAAA,EAAG;IACnB,MAAMxK,UAAU,GAAGC,MAAM,CAACtG,UAAU,GAAG,GAAG;IAC1C;IACA,IAAI,IAAI,CAAC4U,qBAAqB,KAAKvO,UAAU,EAAE;MAC3C;IACJ;IACA;IACA,IAAI,IAAI,CAACwO,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACb,OAAO,CAAC,CAAC;MACtC,IAAI,CAACa,uBAAuB,GAAG9V,SAAS;IAC5C;IACA,MAAM;MAAE0H;IAAkB,CAAC,GAAG,IAAI;IAClC,IAAI,CAACA,iBAAiB,EAAE;MACpB;IACJ;IACA;IACA,IAAIqO,mBAAmB;IACvB,IAAI,IAAI,CAACF,qBAAqB,IAAI,CAACvO,UAAU,EAAE;MAC3C;MACAyO,mBAAmB,GAAG1M,6BAA6B,CAAC,IAAI,CAAC9H,EAAE,EAAE;QACzD0F,YAAY,EAAES;MAAiB,CAAC,CAAC;IACzC,CAAC,MACI;MACD;MACAqO,mBAAmB,GAAGtM,6BAA6B,CAAC,IAAI,CAAClI,EAAE,EAAE;QACzD0F,YAAY,EAAES;MAAiB,CAAC,CAAC;IACzC;IACA;IACA,IAAI,CAACmO,qBAAqB,GAAGvO,UAAU;IACvC,IAAI,CAACwO,uBAAuB,GAAGC,mBAAmB;IAClDA,mBAAmB,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAClC,IAAI,CAACH,uBAAuB,GAAG9V,SAAS;MACxC;MACA;MACA,IAAI,CAACkW,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EACA7D,6BAA6BA,CAAA,EAAG;IAC5B;IACA,IAAI,IAAI,CAACT,aAAa,EAAE;MACpBD,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG5R,SAAS;IAClC;IACA,IAAI,IAAI,CAAC8V,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACb,OAAO,CAAC,CAAC;MACtC,IAAI,CAACa,uBAAuB,GAAG9V,SAAS;IAC5C;EACJ;EACAkW,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAIla,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC0L,iBAAiB,EAAE;MACvD;IACJ;IACA;IACA,IAAI,IAAI,CAAC1C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACiQ,OAAO,CAAC,CAAC;MACtB,IAAI,CAACjQ,OAAO,GAAGhF,SAAS;IAC5B;IACA,IAAI,IAAI,CAACwB,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAAC0D,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnC,IAAI,CAAC1D,SAAS,CAACyT,OAAO,CAAC,CAAC;MACxB,IAAI,CAACzT,SAAS,GAAGxB,SAAS;IAC9B;IACA;IACA;IACAnC,uDAAG,CAAC,MAAM;MACN,IAAI,CAACsY,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAAC9B,gBAAgB,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA8B,0BAA0BA,CAAA,EAAG;IACzB,MAAM;MAAE5U,EAAE;MAAEmG;IAAkB,CAAC,GAAG,IAAI;IACtC,MAAMlE,IAAI,GAAG5F,uDAAc,CAAC2D,EAAE,CAAC;IAC/B,MAAMyI,SAAS,GAAGxG,IAAI,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACtD,IAAIuG,SAAS,EAAE;MACXA,SAAS,CAACxJ,KAAK,CAAC+F,SAAS,GAAG,iBAAiB;MAC7CyD,SAAS,CAACxJ,KAAK,CAAC8F,OAAO,GAAG,GAAG;IACjC;IACA,IAAI,CAACoB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACD,OAAO,MAAM,WAAW,EAAE;MACnH,MAAMH,UAAU,GAAGC,MAAM,CAACtG,UAAU,GAAG,GAAG;MAC1C,IAAIqG,UAAU,EAAE;QACZ,MAAMW,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GACzD,MAAM,GACN,qCAAqC;QAC3C,MAAMiO,KAAK,GAAGlU,oBAAoB,CAACC,oBAAoB;QACvDuF,iBAAiB,CAAClH,KAAK,CAAC+F,SAAS,GAAG,cAAc0B,eAAe,WAAWmO,KAAK,GAAG;MACxF,CAAC,MACI;QACD1O,iBAAiB,CAAClH,KAAK,CAAC+F,SAAS,GAAG,0BAA0B;MAClE;IACJ;EACJ;EACMuO,mBAAmBA,CAAA,EAAG;IAAA,IAAAuB,MAAA;IAAA,OAAA/V,yMAAA;MACxB,MAAMgW,YAAY,GAAGvO,QAAQ,CAACZ,gBAAgB,CAAC,oCAAoCkP,MAAI,CAAC9U,EAAE,CAAC4J,EAAE,IAAI,CAAC;MAClGmL,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC1K,OAAO;QAAA,IAAA2K,KAAA,GAAAjW,yMAAA,CAAC,WAAOkW,KAAK,EAAK;UAC9F,MAAMA,KAAK,CAACxX,OAAO,CAACgB,SAAS,EAAE,kBAAkB,CAAC;QACtD,CAAC;QAAA,iBAAAyW,GAAA;UAAA,OAAAF,KAAA,CAAA7V,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACA4T,yBAAyBA,CAAA,EAAG;IACxB,IAAI,OAAOmC,gBAAgB,KAAK,WAAW,EAAE;MACzC;IACJ;IACA;IACA,IAAI,OAAOnP,MAAM,KAAK,WAAW,IAAI,CAAC,IAAI,CAACqL,oBAAoB,EAAE;MAC7D;IACJ;IACA;IACA,IAAI,IAAI,CAACA,oBAAoB,CAAC+D,QAAQ,KAAKC,IAAI,CAACC,aAAa,IACzD,IAAI,CAACjE,oBAAoB,CAAC+D,QAAQ,KAAKC,IAAI,CAACE,sBAAsB,EAAE;MACpE;IACJ;IACA,IAAI,CAACC,qBAAqB,GAAG,IAAIL,gBAAgB,CAAEM,SAAS,IAAK;MAC7DA,SAAS,CAACpL,OAAO,CAAEqL,QAAQ,IAAK;QAC5B,IAAIA,QAAQ,CAAC9G,IAAI,KAAK,WAAW,IAAI8G,QAAQ,CAACC,YAAY,CAAC9N,MAAM,GAAG,CAAC,EAAE;UACnE;UACA,MAAM+N,sBAAsB,GAAGjO,KAAK,CAACC,IAAI,CAAC8N,QAAQ,CAACC,YAAY,CAAC,CAACE,IAAI,CAAEC,IAAI,IAAK;YAC5E,IAAI7E,EAAE,EAAE8E,EAAE;YACV,MAAMC,aAAa,GAAGF,IAAI,KAAK,IAAI,CAACzE,oBAAoB;YACxD,MAAM4E,gBAAgB,GAAG,IAAI,CAAC5E,oBAAoB,GAC5C,CAAC0E,EAAE,GAAG,CAAC9E,EAAE,GAAG6E,IAAI,EAAEI,QAAQ,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAClF,EAAE,EAAE,IAAI,CAACI,oBAAoB,CAAC,GACvG,KAAK;YACX,OAAO2E,aAAa,IAAIC,gBAAgB;UAC5C,CAAC,CAAC;UACF;UACA,MAAMG,wBAAwB,GAAG,IAAI,CAAC/E,oBAAoB,IAAI,CAAC,IAAI,CAACA,oBAAoB,CAACgF,WAAW;UACpG,IAAIT,sBAAsB,IAAIQ,wBAAwB,EAAE;YACpD,IAAI,CAAC3Y,OAAO,CAACgB,SAAS,EAAE,gBAAgB,CAAC;YACzC;YACA;YACA,IAAI,CAAC4S,oBAAoB,GAAG5S,SAAS;UACzC;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF;IACA,IAAI,CAAC+W,qBAAqB,CAACc,OAAO,CAAC9P,QAAQ,CAACC,IAAI,EAAE;MAC9C8P,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACAzF,4BAA4BA,CAAA,EAAG;IAC3B,IAAIE,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACuE,qBAAqB,MAAM,IAAI,IAAIvE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwF,UAAU,CAAC,CAAC;IACtF,IAAI,CAACjB,qBAAqB,GAAG/W,SAAS;EAC1C;EACAiY,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,MAAM;MAAEpJ,YAAY;MAAEpH,iBAAiB;MAAE+K,cAAc;MAAEpD,cAAc;MAAEN,mBAAmB;MAAEU,SAAS;MAAE7J;IAAgB,CAAC,GAAG,IAAI;IACzI,MAAMuS,UAAU,GAAGD,MAAM,KAAK,KAAK,IAAIpJ,YAAY;IACnD,MAAMsJ,IAAI,GAAGpc,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqc,WAAW,GAAG3Q,iBAAiB,KAAK1H,SAAS,IAAIoY,IAAI,KAAK,KAAK;IACrE,MAAME,aAAa,GAAGjJ,cAAc,KAAK,OAAO;IAChD,MAAMkJ,sBAAsB,GAAGzJ,YAAY,IAAIqJ,UAAU;IACzD,OAAQ5b,qDAAC,CAACE,iDAAI,EAAEsW,MAAM,CAACC,MAAM,CAAC;MAAEwF,GAAG,EAAE,0CAA0C;MAAE,WAAW,EAAE,IAAI;MAC9F;MACA7H,QAAQ,EAAE2H,aAAa,IAAIC,sBAAsB,GAAG,CAAC,GAAG,CAAC;IAAE,CAAC,EAAE9F,cAAc,EAAE;MAAEjS,KAAK,EAAE;QACnFiY,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACC,YAAY;MACxC,CAAC;MAAEC,KAAK,EAAE5F,MAAM,CAACC,MAAM,CAAC;QAAE,CAACoF,IAAI,GAAG,IAAI;QAAE,CAAC,eAAe,GAAG,CAACC,WAAW,IAAI,CAACvJ,YAAY;QAAE,CAAC,YAAY,GAAGuJ,WAAW;QAAE,CAAC,aAAa,GAAGvJ,YAAY;QAAE,CAAC,wBAAwB,GAAGA,YAAY,IAAI,CAAClJ,cAAc;QAAE,gBAAgB,EAAE,IAAI;QAAE,CAACnH,oDAAwB,GAAGgR,SAAS,KAAK;MAAM,CAAC,EAAEvQ,qDAAW,CAAC,IAAI,CAAC0Z,QAAQ,CAAC,CAAC;MAAEC,gBAAgB,EAAE,IAAI,CAAChJ,aAAa;MAAEiJ,oBAAoB,EAAE,IAAI,CAAChJ,WAAW;MAAEiJ,qBAAqB,EAAE,IAAI,CAACjJ,WAAW;MAAEkJ,qBAAqB,EAAE,IAAI,CAAClJ,WAAW;MAAEmJ,oBAAoB,EAAE,IAAI,CAACnJ,WAAW;MAAEoJ,OAAO,EAAE,IAAI,CAACzI;IAAa,CAAC,CAAC,EAAElU,qDAAC,CAAC,cAAc,EAAE;MAAEic,GAAG,EAAE,0CAA0C;MAAEW,GAAG,EAAG5X,EAAE,IAAM,IAAI,CAACwI,UAAU,GAAGxI,EAAG;MAAE6X,OAAO,EAAE,IAAI,CAAC7J,YAAY;MAAE8J,QAAQ,EAAE,IAAI,CAAC/J,eAAe;MAAEgK,IAAI,EAAE;IAAW,CAAC,CAAC,EAAElB,IAAI,KAAK,KAAK,IAAI7b,qDAAC,CAAC,KAAK,EAAE;MAAEic,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAe,CAAC,CAAC,EAAEpc,qDAAC,CAAC,KAAK,EAAEwW,MAAM,CAACC,MAAM,CAAC;MAAEwF,GAAG,EAAE,0CAA0C;MAC74B;AACZ;AACA;AACA;AACA;AACA;MACY9E,IAAI,EAAE;IAAS,CAAC,EAAE3E,mBAAmB,EAAE;MAAE,YAAY,EAAE,MAAM;MAAE4J,KAAK,EAAE,mCAAmC;MAAEW,IAAI,EAAE,SAAS;MAAEH,GAAG,EAAG5X,EAAE,IAAM,IAAI,CAACyI,SAAS,GAAGzI;IAAI,CAAC,CAAC,EAAE4W,UAAU,IAAK5b,qDAAC,CAAC,QAAQ,EAAE;MAAEic,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,cAAc;MACtQ;MACAhI,QAAQ,EAAE,CAAC2H,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;MAAE,YAAY,EAAE,iEAAiE;MAAEiB,OAAO,EAAEjB,aAAa,GAAG,IAAI,CAAC5I,aAAa,GAAG1P,SAAS;MAAEsZ,IAAI,EAAE,QAAQ;MAAEH,GAAG,EAAG5X,EAAE,IAAM,IAAI,CAACmP,YAAY,GAAGnP;IAAI,CAAC,CAAE,EAAEhF,qDAAC,CAAC,MAAM,EAAE;MAAEic,GAAG,EAAE,0CAA0C;MAAEgB,YAAY,EAAE,IAAI,CAAC5I;IAAa,CAAC,CAAC,CAAC,CAAC;EACjV;EACA,IAAIrP,EAAEA,CAAA,EAAG;IAAE,OAAO5E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8c,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAMvJ,aAAa,GAAG;EAClBwJ,kBAAkB,EAAE,iBAAiB;EACrCC,mBAAmB,EAAE,kBAAkB;EACvCC,mBAAmB,EAAE,kBAAkB;EACvCC,kBAAkB,EAAE;AACxB,CAAC;AACD/L,KAAK,CAACtN,KAAK,GAAG;EACVsZ,GAAG,EAAElM,WAAW;EAChBmM,EAAE,EAAElM;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, m as printIonWarning, w as writeTask, l as config, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, i as isIonContent, d as disableContentScrollY, r as resetContentScrollY, a as findIonContent, p as printIonContentErrorMsg } from './index-BlJTBdxG.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { e as clamp, g as getElementRoot, r as raf, b as inheritAttributes, h as hasLazyBuild } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\nimport { G as GESTURE, O as OVERLAY_GESTURE_PRIORITY, F as FOCUS_TRAP_DISABLE_CLASS, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { e as deepReady, w as waitForMount } from './index-DfBA5ztX.js';\nimport { KEYBOARD_DID_OPEN } from './keyboard-ywgs5efA.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { createGesture } from './index-CfgBF1SE.js';\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './keyboard-CUw4ekVy.js';\n\nvar Style;\n(function (Style) {\n    Style[\"Dark\"] = \"DARK\";\n    Style[\"Light\"] = \"LIGHT\";\n    Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nconst StatusBar = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('StatusBar')) {\n            return capacitor.Plugins.StatusBar;\n        }\n        return undefined;\n    },\n    setStyle(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.setStyle(options);\n    },\n    getStyle: async function () {\n        const engine = this.getEngine();\n        if (!engine) {\n            return Style.Default;\n        }\n        const { style } = await engine.getInfo();\n        return style;\n    },\n};\n\n/**\n * Use y = mx + b to\n * figure out the backdrop value\n * at a particular x coordinate. This\n * is useful when the backdrop does\n * not begin to fade in until after\n * the 0 breakpoint.\n */\nconst getBackdropValueForSheet = (x, backdropBreakpoint) => {\n    /**\n     * We will use these points:\n     * (backdropBreakpoint, 0)\n     * (maxBreakpoint, 1)\n     * We know that at the beginning breakpoint,\n     * the backdrop will be hidden. We also\n     * know that at the maxBreakpoint, the backdrop\n     * must be fully visible. maxBreakpoint should\n     * always be 1 even if the maximum value\n     * of the breakpoints array is not 1 since\n     * the animation runs from a progress of 0\n     * to a progress of 1.\n     * m = (y2 - y1) / (x2 - x1)\n     *\n     * This is simplified from:\n     * m = (1 - 0) / (maxBreakpoint - backdropBreakpoint)\n     *\n     * If the backdropBreakpoint is 1, we return 0 as the\n     * backdrop is completely hidden.\n     *\n     */\n    if (backdropBreakpoint === 1) {\n        return 0;\n    }\n    const slope = 1 / (1 - backdropBreakpoint);\n    /**\n     * From here, compute b which is\n     * the backdrop opacity if the offset\n     * is 0. If the backdrop does not\n     * begin to fade in until after the\n     * 0 breakpoint, this b value will be\n     * negative. This is fine as we never pass\n     * b directly into the animation keyframes.\n     * b = y - mx\n     * Use a known point: (backdropBreakpoint, 0)\n     * This is simplified from:\n     * b = 0 - (backdropBreakpoint * slope)\n     */\n    const b = -(backdropBreakpoint * slope);\n    /**\n     * Finally, we can now determine the\n     * backdrop offset given an arbitrary\n     * gesture offset.\n     */\n    return x * slope + b;\n};\n/**\n * The tablet/desktop card modal activates\n * when the window width is >= 768.\n * At that point, the presenting element\n * is not transformed, so we do not need to\n * adjust the status bar color.\n *\n */\nconst setCardStatusBarDark = () => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: Style.Dark });\n};\nconst setCardStatusBarDefault = (defaultStyle = Style.Default) => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: defaultStyle });\n};\n\nconst handleCanDismiss = async (el, animation) => {\n    /**\n     * If canDismiss is not a function\n     * then we can return early. If canDismiss is `true`,\n     * then canDismissBlocksGesture is `false` as canDismiss\n     * will never interrupt the gesture. As a result,\n     * this code block is never reached. If canDismiss is `false`,\n     * then we never dismiss.\n     */\n    if (typeof el.canDismiss !== 'function') {\n        return;\n    }\n    /**\n     * Run the canDismiss callback.\n     * If the function returns `true`,\n     * then we can proceed with dismiss.\n     */\n    const shouldDismiss = await el.canDismiss(undefined, GESTURE);\n    if (!shouldDismiss) {\n        return;\n    }\n    /**\n     * If canDismiss resolved after the snap\n     * back animation finished, we can\n     * dismiss immediately.\n     *\n     * If canDismiss resolved before the snap\n     * back animation finished, we need to\n     * wait until the snap back animation is\n     * done before dismissing.\n     */\n    if (animation.isRunning()) {\n        animation.onFinish(() => {\n            el.dismiss(undefined, 'handler');\n        }, { oneTimeCallback: true });\n    }\n    else {\n        el.dismiss(undefined, 'handler');\n    }\n};\n/**\n * This function lets us simulate a realistic spring-like animation\n * when swiping down on the modal.\n * There are two forces that we need to use to compute the spring physics:\n *\n * 1. Stiffness, k: This is a measure of resistance applied a spring.\n * 2. Dampening, c: This value has the effect of reducing or preventing oscillation.\n *\n * Using these two values, we can calculate the Spring Force and the Dampening Force\n * to compute the total force applied to a spring.\n *\n * Spring Force: This force pulls a spring back into its equilibrium position.\n * Hooke's Law tells us that that spring force (FS) = kX.\n * k is the stiffness of a spring, and X is the displacement of the spring from its\n * equilibrium position. In this case, it is the amount by which the free end\n * of a spring was displaced (stretched/pushed) from its \"relaxed\" position.\n *\n * Dampening Force: This force slows down motion. Without it, a spring would oscillate forever.\n * The dampening force, FD, can be found via this formula: FD = -cv\n * where c the dampening value and v is velocity.\n *\n * Therefore, the resulting force that is exerted on the block is:\n * F = FS + FD = -kX - cv\n *\n * Newton's 2nd Law tells us that F = ma:\n * ma = -kX - cv.\n *\n * For Ionic's purposes, we can assume that m = 1:\n * a = -kX - cv\n *\n * Imagine a block attached to the end of a spring. At equilibrium\n * the block is at position x = 1.\n * Pressing on the block moves it to position x = 0;\n * So, to calculate the displacement, we need to take the\n * current position and subtract the previous position from it.\n * X = x - x0 = 0 - 1 = -1.\n *\n * For Ionic's purposes, we are only pushing on the spring modal\n * so we have a max position of 1.\n * As a result, we can expand displacement to this formula:\n * X = x - 1\n *\n * a = -k(x - 1) - cv\n *\n * We can represent the motion of something as a function of time: f(t) = x.\n * The derivative of position gives us the velocity: f'(t)\n * The derivative of the velocity gives us the acceleration: f''(t)\n *\n * We can substitute the formula above with these values:\n *\n * f\"(t) = -k * (f(t) - 1) - c * f'(t)\n *\n * This is called a differential equation.\n *\n * We know that at t = 0, we are at x = 0 because the modal does not move: f(0) = 0\n * This means our velocity is also zero: f'(0) = 0.\n *\n * We can cheat a bit and plug the formula into Wolfram Alpha.\n * However, we need to pick stiffness and dampening values:\n * k = 0.57\n * c = 15\n *\n * I picked these as they are fairly close to native iOS's spring effect\n * with the modal.\n *\n * What we plug in is this: f(0) = 0; f'(0) = 0; f''(t) = -0.57(f(t) - 1) - 15f'(t)\n *\n * The result is a formula that lets us calculate the acceleration\n * for a given time t.\n * Note: This is the approximate form of the solution. Wolfram Alpha will\n * give you a complex differential equation too.\n */\nconst calculateSpringStep = (t) => {\n    return 0.00255275 * 2.71828 ** (-14.9619 * t) - 1.00255 * 2.71828 ** (-0.0380968 * t) + 1;\n};\n\n// Defaults for the card swipe animation\nconst SwipeToCloseDefaults = {\n    MIN_PRESENTING_SCALE: 0.915,\n};\nconst createSwipeToCloseGesture = (el, animation, statusBarStyle, onDismiss) => {\n    /**\n     * The step value at which a card modal\n     * is eligible for dismissing via gesture.\n     */\n    const DISMISS_THRESHOLD = 0.5;\n    const height = el.offsetHeight;\n    let isOpen = false;\n    let canDismissBlocksGesture = false;\n    let contentEl = null;\n    let scrollEl = null;\n    const canDismissMaxStep = 0.2;\n    let initialScrollY = true;\n    let lastStep = 0;\n    const getScrollY = () => {\n        if (contentEl && isIonContent(contentEl)) {\n            return contentEl.scrollY;\n            /**\n             * Custom scroll containers are intended to be\n             * used with virtual scrolling, so we assume\n             * there is scrolling in this case.\n             */\n        }\n        else {\n            return true;\n        }\n    };\n    const canStart = (detail) => {\n        const target = detail.event.target;\n        if (target === null || !target.closest) {\n            return true;\n        }\n        /**\n         * If we are swiping on the content,\n         * swiping should only be possible if\n         * the content is scrolled all the way\n         * to the top so that we do not interfere\n         * with scrolling.\n         *\n         * We cannot assume that the `ion-content`\n         * target will remain consistent between\n         * swipes. For example, when using\n         * ion-nav within a card modal it is\n         * possible to swipe, push a view, and then\n         * swipe again. The target content will not\n         * be the same between swipes.\n         */\n        contentEl = findClosestIonContent(target);\n        if (contentEl) {\n            /**\n             * The card should never swipe to close\n             * on the content with a refresher.\n             * Note: We cannot solve this by making the\n             * swipeToClose gesture have a higher priority\n             * than the refresher gesture as the iOS native\n             * refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note: Do not use getScrollElement here\n             * because we need this to be a synchronous\n             * operation, and getScrollElement is\n             * asynchronous.\n             */\n            if (isIonContent(contentEl)) {\n                const root = getElementRoot(contentEl);\n                scrollEl = root.querySelector('.inner-scroll');\n            }\n            else {\n                scrollEl = contentEl;\n            }\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        /**\n         * Card should be swipeable on all\n         * parts of the modal except for the footer.\n         */\n        const footer = target.closest('ion-footer');\n        if (footer === null) {\n            return true;\n        }\n        return false;\n    };\n    const onStart = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * Get the initial scrollY value so\n         * that we can correctly reset the scrollY\n         * prop when the gesture ends.\n         */\n        initialScrollY = getScrollY();\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = el.canDismiss !== undefined && el.canDismiss !== true;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        animation.progressStart(true, isOpen ? 1 : 0);\n    };\n    const onMove = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        /**\n         * If we are swiping on the content\n         * then the swipe gesture should only\n         * happen if we are pulling down.\n         *\n         * However, if we pull up and\n         * then down such that the scroll position\n         * returns to 0, we should be able to swipe\n         * the card.\n         */\n        const step = detail.deltaY / height;\n        /**\n         * Check if user is swiping down and\n         * if we have a canDismiss value that\n         * should block the gesture from\n         * proceeding,\n         */\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that the starting breakpoint is always 0,\n         * so we omit adding 0 to the result.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(clampedStep);\n        /**\n         * When swiping down half way, the status bar style\n         * should be reset to its default value.\n         *\n         * We track lastStep so that we do not fire these\n         * functions on every onMove, only when the user has\n         * crossed a certain threshold.\n         */\n        if (clampedStep >= DISMISS_THRESHOLD && lastStep < DISMISS_THRESHOLD) {\n            setCardStatusBarDefault(statusBarStyle);\n            /**\n             * However, if we swipe back up, then the\n             * status bar style should be set to have light\n             * text on a dark background.\n             */\n        }\n        else if (clampedStep < DISMISS_THRESHOLD && lastStep >= DISMISS_THRESHOLD) {\n            setCardStatusBarDark();\n        }\n        lastStep = clampedStep;\n    };\n    const onEnd = (detail) => {\n        const velocity = detail.velocityY;\n        const step = detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        const threshold = (detail.deltaY + velocity * 1000) / height;\n        /**\n         * If canDismiss blocks\n         * the swipe gesture, then the\n         * animation can never complete until\n         * canDismiss is checked.\n         */\n        const shouldComplete = !isAttemptingDismissWithCanDismiss && threshold >= DISMISS_THRESHOLD;\n        let newStepValue = shouldComplete ? -1e-3 : 0.001;\n        if (!shouldComplete) {\n            animation.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n            newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], clampedStep)[0];\n        }\n        else {\n            animation.easing('cubic-bezier(0.32, 0.72, 0, 1)');\n            newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], clampedStep)[0];\n        }\n        const duration = shouldComplete\n            ? computeDuration(step * height, velocity)\n            : computeDuration((1 - clampedStep) * height, velocity);\n        isOpen = shouldComplete;\n        gesture.enable(false);\n        if (contentEl) {\n            resetContentScrollY(contentEl, initialScrollY);\n        }\n        animation\n            .onFinish(() => {\n            if (!shouldComplete) {\n                gesture.enable(true);\n            }\n        })\n            .progressEnd(shouldComplete ? 1 : 0, newStepValue, duration);\n        /**\n         * If the canDismiss value blocked the gesture\n         * from proceeding, then we should ignore whatever\n         * shouldComplete is. Whether or not the modal\n         * animation should complete is now determined by\n         * canDismiss.\n         *\n         * If the user swiped >25% of the way\n         * to the max step, then we should\n         * check canDismiss. 25% was chosen\n         * to avoid accidental swipes.\n         */\n        if (isAttemptingDismissWithCanDismiss && clampedStep > maxStep / 4) {\n            handleCanDismiss(el, animation);\n        }\n        else if (shouldComplete) {\n            onDismiss();\n        }\n    };\n    const gesture = createGesture({\n        el,\n        gestureName: 'modalSwipeToClose',\n        gesturePriority: OVERLAY_GESTURE_PRIORITY,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return gesture;\n};\nconst computeDuration = (remaining, velocity) => {\n    return clamp(400, remaining / Math.abs(velocity * 1.1), 500);\n};\n\nconst createSheetEnterAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint, expandToScroll } = opts;\n    /**\n     * If the backdropBreakpoint is undefined, then the backdrop\n     * should always fade in. If the backdropBreakpoint came before the\n     * current breakpoint, then the backdrop should be fading in.\n     */\n    const shouldShowBackdrop = backdropBreakpoint === undefined || backdropBreakpoint < currentBreakpoint;\n    const initialBackdrop = shouldShowBackdrop ? `calc(var(--backdrop-opacity) * ${currentBreakpoint})` : '0';\n    const backdropAnimation = createAnimation('backdropAnimation').fromTo('opacity', 0, initialBackdrop);\n    if (shouldShowBackdrop) {\n        backdropAnimation\n            .beforeStyles({\n            'pointer-events': 'none',\n        })\n            .afterClearStyles(['pointer-events']);\n    }\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: 'translateY(100%)' },\n        { offset: 1, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n    ]);\n    /**\n     * This allows the content to be scrollable at any breakpoint.\n     */\n    const contentAnimation = !expandToScroll\n        ? createAnimation('contentAnimation').keyframes([\n            { offset: 0, opacity: 1, maxHeight: `${(1 - currentBreakpoint) * 100}%` },\n            { offset: 1, opacity: 1, maxHeight: `${currentBreakpoint * 100}%` },\n        ])\n        : undefined;\n    return { wrapperAnimation, backdropAnimation, contentAnimation };\n};\nconst createSheetLeaveAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint } = opts;\n    /**\n     * Backdrop does not always fade in from 0 to 1 if backdropBreakpoint\n     * is defined, so we need to account for that offset by figuring out\n     * what the current backdrop value should be.\n     */\n    const backdropValue = `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(currentBreakpoint, backdropBreakpoint)})`;\n    const defaultBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: 1, opacity: 0 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const backdropAnimation = createAnimation('backdropAnimation').keyframes(backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop);\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n        { offset: 1, opacity: 1, transform: `translateY(100%)` },\n    ]);\n    return { wrapperAnimation, backdropAnimation };\n};\n\nconst createEnterAnimation$1 = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(100vh)', 'translateY(0vh)');\n    return { backdropAnimation, wrapperAnimation, contentAnimation: undefined };\n};\n/**\n * iOS Modal Enter Animation for the Card presentation style\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n    const { presentingEl, currentBreakpoint, expandToScroll } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation, contentAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    // The content animation is only added if scrolling is enabled for\n    // all the breakpoints.\n    !expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n    const baseAnimation = createAnimation('entering-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(500)\n        .addAnimation([wrapperAnimation]);\n    if (contentAnimation) {\n        baseAnimation.addAnimation(contentAnimation);\n    }\n    if (presentingEl) {\n        const isPortrait = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation().beforeStyles({\n            transform: 'translateY(0)',\n            'transform-origin': 'top center',\n            overflow: 'hidden',\n        });\n        const bodyEl = document.body;\n        if (isPortrait) {\n            /**\n             * Fallback for browsers that does not support `max()` (ex: Firefox)\n             * No need to worry about statusbar padding since engines like Gecko\n             * are not used as the engine for standalone Cordova/Capacitor apps\n             */\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation\n                .afterStyles({\n                transform: finalTransform,\n            })\n                .beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black'))\n                .addElement(presentingEl)\n                .keyframes([\n                { offset: 0, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n                { offset: 1, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '0', '1');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, filter: 'contrast(0.85)', transform: finalTransform },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .keyframes([\n                    { offset: 0, opacity: '1', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, opacity: '0', transform: finalTransform },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createLeaveAnimation$1 = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(0vh)', 'translateY(100vh)');\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * iOS Modal Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts, duration = 500) => {\n    const { presentingEl, currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    const baseAnimation = createAnimation('leaving-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration)\n        .addAnimation(wrapperAnimation);\n    if (presentingEl) {\n        const isPortrait = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation()\n            .beforeClearStyles(['transform'])\n            .afterClearStyles(['transform'])\n            .onFinish((currentStep) => {\n            // only reset background color if this is the last card-style modal\n            if (currentStep !== 1) {\n                return;\n            }\n            presentingEl.style.setProperty('overflow', '');\n            const numModals = Array.from(bodyEl.querySelectorAll('ion-modal:not(.overlay-hidden)')).filter((m) => m.presentingElement !== undefined).length;\n            if (numModals <= 1) {\n                bodyEl.style.setProperty('background-color', '');\n            }\n        });\n        const bodyEl = document.body;\n        if (isPortrait) {\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation.addElement(presentingEl).keyframes([\n                { offset: 0, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n                { offset: 1, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '1', '0');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .afterStyles({\n                    transform: 'translate3d(0, 0, 0)',\n                })\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(0.85)', transform: finalTransform },\n                    { offset: 1, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .afterStyles({\n                    transform: 'translateY(0) scale(1)',\n                })\n                    .keyframes([\n                    { offset: 0, opacity: '0', transform: finalTransform },\n                    { offset: 1, opacity: '1', transform: 'translateY(0) scale(1)' },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\n/**\n * Transition animation from portrait view to landscape view\n * This handles the case where a card modal is open in portrait view\n * and the user switches to landscape view\n */\nconst portraitToLandscapeTransition = (baseEl, opts, duration = 300) => {\n    const { presentingEl } = opts;\n    if (!presentingEl) {\n        // No transition needed for non-card modals\n        return createAnimation('portrait-to-landscape-transition');\n    }\n    const presentingElIsCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const bodyEl = document.body;\n    const baseAnimation = createAnimation('portrait-to-landscape-transition')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration);\n    const presentingAnimation = createAnimation().beforeStyles({\n        transform: 'translateY(0)',\n        'transform-origin': 'top center',\n        overflow: 'hidden',\n    });\n    if (!presentingElIsCardModal) {\n        // The presenting element is not a card modal, so we do not\n        // need to care about layering and modal-specific styles.\n        const root = getElementRoot(baseEl);\n        const wrapperAnimation = createAnimation()\n            .addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow'))\n            .fromTo('opacity', '1', '1'); // Keep wrapper visible in landscape\n        const backdropAnimation = createAnimation()\n            .addElement(root.querySelector('ion-backdrop'))\n            .fromTo('opacity', 'var(--backdrop-opacity)', 'var(--backdrop-opacity)'); // Keep backdrop visible\n        // Animate presentingEl from portrait state back to normal\n        const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(${transformOffset}) scale(${toPresentingScale})`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: 'translateY(0px) scale(1)',\n            'border-radius': '0px',\n        })\n            .beforeAddWrite(() => bodyEl.style.setProperty('background-color', ''))\n            .fromTo('transform', fromTransform, 'translateY(0px) scale(1)')\n            .fromTo('filter', 'contrast(0.85)', 'contrast(1)')\n            .fromTo('border-radius', '10px 10px 0 0', '0px');\n        baseAnimation.addAnimation([presentingAnimation, wrapperAnimation, backdropAnimation]);\n    }\n    else {\n        // The presenting element is a card modal, so we do\n        // need to care about layering and modal-specific styles.\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        const toTransform = `translateY(0px) scale(1)`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .fromTo('transform', fromTransform, toTransform)\n            .fromTo('filter', 'contrast(0.85)', 'contrast(1)');\n        const shadowAnimation = createAnimation()\n            .addElement(presentingElRoot.querySelector('.modal-shadow'))\n            .afterStyles({\n            transform: toTransform,\n            opacity: '0',\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n    }\n    return baseAnimation;\n};\n/**\n * Transition animation from landscape view to portrait view\n * This handles the case where a card modal is open in landscape view\n * and the user switches to portrait view\n */\nconst landscapeToPortraitTransition = (baseEl, opts, duration = 300) => {\n    const { presentingEl } = opts;\n    if (!presentingEl) {\n        // No transition needed for non-card modals\n        return createAnimation('landscape-to-portrait-transition');\n    }\n    const presentingElIsCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const bodyEl = document.body;\n    const baseAnimation = createAnimation('landscape-to-portrait-transition')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration);\n    const presentingAnimation = createAnimation().beforeStyles({\n        transform: 'translateY(0)',\n        'transform-origin': 'top center',\n        overflow: 'hidden',\n    });\n    if (!presentingElIsCardModal) {\n        // The presenting element is not a card modal, so we do not\n        // need to care about layering and modal-specific styles.\n        const root = getElementRoot(baseEl);\n        const wrapperAnimation = createAnimation()\n            .addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow'))\n            .fromTo('opacity', '1', '1'); // Keep wrapper visible\n        const backdropAnimation = createAnimation()\n            .addElement(root.querySelector('ion-backdrop'))\n            .fromTo('opacity', 'var(--backdrop-opacity)', 'var(--backdrop-opacity)'); // Keep backdrop visible\n        // Animate presentingEl from normal state to portrait state\n        const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const toTransform = `translateY(${transformOffset}) scale(${toPresentingScale})`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black'))\n            .keyframes([\n            { offset: 0, transform: 'translateY(0px) scale(1)', filter: 'contrast(1)', borderRadius: '0px' },\n            { offset: 0.2, transform: 'translateY(0px) scale(1)', filter: 'contrast(1)', borderRadius: '10px 10px 0 0' },\n            { offset: 1, transform: toTransform, filter: 'contrast(0.85)', borderRadius: '10px 10px 0 0' },\n        ]);\n        baseAnimation.addAnimation([presentingAnimation, wrapperAnimation, backdropAnimation]);\n    }\n    else {\n        // The presenting element is also a card modal, so we need\n        // to handle layering and modal-specific styles.\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        const toTransform = `translateY(0) scale(1)`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        const shadowAnimation = createAnimation()\n            .addElement(presentingElRoot.querySelector('.modal-shadow'))\n            .afterStyles({\n            transform: toTransform,\n            opacity: '0',\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n    }\n    return baseAnimation;\n};\n\nconst createEnterAnimation = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.01, transform: 'translateY(40px)' },\n        { offset: 1, opacity: 1, transform: `translateY(0px)` },\n    ]);\n    return { backdropAnimation, wrapperAnimation, contentAnimation: undefined };\n};\n/**\n * Md Modal Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n    const { currentBreakpoint, expandToScroll } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation, contentAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    // The content animation is only added if scrolling is enabled for\n    // all the breakpoints.\n    !expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n    const baseAnimation = createAnimation()\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.36,0.66,0.04,1)')\n        .duration(280)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n    if (contentAnimation) {\n        baseAnimation.addAnimation(contentAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createLeaveAnimation = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.99, transform: `translateY(0px)` },\n        { offset: 1, opacity: 0, transform: 'translateY(40px)' },\n    ]);\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * Md Modal Leave Animation\n */\nconst mdLeaveAnimation = (baseEl, opts) => {\n    const { currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    const baseAnimation = createAnimation()\n        .easing('cubic-bezier(0.47,0,0.745,0.715)')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n    return baseAnimation;\n};\n\nconst createSheetGesture = (baseEl, backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, animation, breakpoints = [], expandToScroll, getCurrentBreakpoint, onDismiss, onBreakpointChange) => {\n    // Defaults for the sheet swipe animation\n    const defaultBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1, opacity: 0.01 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1 - backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const SheetDefaults = {\n        WRAPPER_KEYFRAMES: [\n            { offset: 0, transform: 'translateY(0%)' },\n            { offset: 1, transform: 'translateY(100%)' },\n        ],\n        BACKDROP_KEYFRAMES: backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop,\n        CONTENT_KEYFRAMES: [\n            { offset: 0, maxHeight: '100%' },\n            { offset: 1, maxHeight: '0%' },\n        ],\n    };\n    const contentEl = baseEl.querySelector('ion-content');\n    const height = wrapperEl.clientHeight;\n    let currentBreakpoint = initialBreakpoint;\n    let offset = 0;\n    let canDismissBlocksGesture = false;\n    let cachedScrollEl = null;\n    let cachedFooterEls = null;\n    let cachedFooterYPosition = null;\n    let currentFooterState = null;\n    const canDismissMaxStep = 0.95;\n    const maxBreakpoint = breakpoints[breakpoints.length - 1];\n    const minBreakpoint = breakpoints[0];\n    const wrapperAnimation = animation.childAnimations.find((ani) => ani.id === 'wrapperAnimation');\n    const backdropAnimation = animation.childAnimations.find((ani) => ani.id === 'backdropAnimation');\n    const contentAnimation = animation.childAnimations.find((ani) => ani.id === 'contentAnimation');\n    const enableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'auto');\n        backdropEl.style.setProperty('pointer-events', 'auto');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         */\n        baseEl.classList.remove(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    const disableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'none');\n        backdropEl.style.setProperty('pointer-events', 'none');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         * Adding this class disables focus trapping\n         * for the sheet temporarily.\n         */\n        baseEl.classList.add(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    /**\n     * Toggles the footer to an absolute position while moving to prevent\n     * it from shaking while the sheet is being dragged.\n     * @param newPosition Whether the footer is in a moving or stationary position.\n     */\n    const swapFooterPosition = (newPosition) => {\n        if (!cachedFooterEls) {\n            cachedFooterEls = Array.from(baseEl.querySelectorAll('ion-footer'));\n            if (!cachedFooterEls.length) {\n                return;\n            }\n        }\n        const page = baseEl.querySelector('.ion-page');\n        currentFooterState = newPosition;\n        if (newPosition === 'stationary') {\n            cachedFooterEls.forEach((cachedFooterEl) => {\n                // Reset positioning styles to allow normal document flow\n                cachedFooterEl.classList.remove('modal-footer-moving');\n                cachedFooterEl.style.removeProperty('position');\n                cachedFooterEl.style.removeProperty('width');\n                cachedFooterEl.style.removeProperty('height');\n                cachedFooterEl.style.removeProperty('top');\n                cachedFooterEl.style.removeProperty('left');\n                page === null || page === void 0 ? void 0 : page.style.removeProperty('padding-bottom');\n                // Move to page\n                page === null || page === void 0 ? void 0 : page.appendChild(cachedFooterEl);\n            });\n        }\n        else {\n            let footerHeights = 0;\n            cachedFooterEls.forEach((cachedFooterEl, index) => {\n                // Get both the footer and document body positions\n                const cachedFooterElRect = cachedFooterEl.getBoundingClientRect();\n                const bodyRect = document.body.getBoundingClientRect();\n                // Calculate the total height of all footers\n                // so we can add padding to the page element\n                footerHeights += cachedFooterEl.clientHeight;\n                // Calculate absolute position relative to body\n                // We need to subtract the body's offsetTop to get true position within document.body\n                const absoluteTop = cachedFooterElRect.top - bodyRect.top;\n                const absoluteLeft = cachedFooterElRect.left - bodyRect.left;\n                // Capture the footer's current dimensions and store them in CSS variables for\n                // later use when applying absolute positioning.\n                cachedFooterEl.style.setProperty('--pinned-width', `${cachedFooterEl.clientWidth}px`);\n                cachedFooterEl.style.setProperty('--pinned-height', `${cachedFooterEl.clientHeight}px`);\n                cachedFooterEl.style.setProperty('--pinned-top', `${absoluteTop}px`);\n                cachedFooterEl.style.setProperty('--pinned-left', `${absoluteLeft}px`);\n                // Only cache the first footer's Y position\n                // This is used to determine if the sheet has been moved below the footer\n                // and needs to be swapped back to stationary so it collapses correctly.\n                if (index === 0) {\n                    cachedFooterYPosition = absoluteTop;\n                    // If there's a header, we need to combine the header height with the footer position\n                    // because the header moves with the drag handle, so when it starts overlapping the footer,\n                    // we need to account for that.\n                    const header = baseEl.querySelector('ion-header');\n                    if (header) {\n                        cachedFooterYPosition -= header.clientHeight;\n                    }\n                }\n            });\n            // Apply the pinning of styles after we've calculated everything\n            // so that we don't cause layouts to shift while calculating the footer positions.\n            // Otherwise, with multiple footers we'll end up capturing the wrong positions.\n            cachedFooterEls.forEach((cachedFooterEl) => {\n                // Add padding to the parent element to prevent content from being hidden\n                // when the footer is positioned absolutely. This has to be done before we\n                // make the footer absolutely positioned or we may accidentally cause the\n                // sheet to scroll.\n                page === null || page === void 0 ? void 0 : page.style.setProperty('padding-bottom', `${footerHeights}px`);\n                // Apply positioning styles to keep footer at bottom\n                cachedFooterEl.classList.add('modal-footer-moving');\n                // Apply our preserved styles to pin the footer\n                cachedFooterEl.style.setProperty('position', 'absolute');\n                cachedFooterEl.style.setProperty('width', 'var(--pinned-width)');\n                cachedFooterEl.style.setProperty('height', 'var(--pinned-height)');\n                cachedFooterEl.style.setProperty('top', 'var(--pinned-top)');\n                cachedFooterEl.style.setProperty('left', 'var(--pinned-left)');\n                // Move the element to the body when everything else is done\n                document.body.appendChild(cachedFooterEl);\n            });\n        }\n    };\n    /**\n     * After the entering animation completes,\n     * we need to set the animation to go from\n     * offset 0 to offset 1 so that users can\n     * swipe in any direction. We then set the\n     * animation offset to the current breakpoint\n     * so there is no flickering.\n     */\n    if (wrapperAnimation && backdropAnimation) {\n        wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n        backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n        contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n        animation.progressStart(true, 1 - currentBreakpoint);\n        /**\n         * If backdrop is not enabled, then content\n         * behind modal should be clickable. To do this, we need\n         * to remove pointer-events from ion-modal as a whole.\n         * ion-backdrop and .modal-wrapper always have pointer-events: auto\n         * applied, so the modal content can still be interacted with.\n         */\n        const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n        if (shouldEnableBackdrop) {\n            enableBackdrop();\n        }\n        else {\n            disableBackdrop();\n        }\n    }\n    if (contentEl && currentBreakpoint !== maxBreakpoint && expandToScroll) {\n        contentEl.scrollY = false;\n    }\n    const canStart = (detail) => {\n        /**\n         * If we are swiping on the content, swiping should only be possible if the content\n         * is scrolled all the way to the top so that we do not interfere with scrolling.\n         *\n         * We cannot assume that the `ion-content` target will remain consistent between swipes.\n         * For example, when using ion-nav within a modal it is possible to swipe, push a view,\n         * and then swipe again. The target content will not be the same between swipes.\n         */\n        const contentEl = findClosestIonContent(detail.event.target);\n        currentBreakpoint = getCurrentBreakpoint();\n        /**\n         * If `expandToScroll` is disabled, we should not allow the swipe gesture\n         * to start if the content is not scrolled to the top.\n         */\n        if (!expandToScroll && contentEl) {\n            const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n            return scrollEl.scrollTop === 0;\n        }\n        if (currentBreakpoint === 1 && contentEl) {\n            /**\n             * The modal should never swipe to close on the content with a refresher.\n             * Note 1: We cannot solve this by making this gesture have a higher priority than\n             * the refresher gesture as the iOS native refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note 2: Do not use getScrollElement here because we need this to be a synchronous\n             * operation, and getScrollElement is asynchronous.\n             */\n            const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        return true;\n    };\n    const onStart = (detail) => {\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         *\n         * canDismiss is never fired via gesture if there is\n         * no 0 breakpoint. However, it can be fired if the user\n         * presses Esc or the hardware back button.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = baseEl.canDismiss !== undefined && baseEl.canDismiss !== true && minBreakpoint === 0;\n        /**\n         * Cache the scroll element reference when the gesture starts,\n         * this allows us to avoid querying the DOM for the target in onMove,\n         * which would impact performance significantly.\n         */\n        if (!expandToScroll) {\n            const targetEl = findClosestIonContent(detail.event.target);\n            cachedScrollEl =\n                targetEl && isIonContent(targetEl) ? getElementRoot(targetEl).querySelector('.inner-scroll') : targetEl;\n        }\n        /**\n         * If expandToScroll is disabled, we need to swap\n         * the footer position to moving so that it doesn't shake\n         * while the sheet is being dragged.\n         */\n        if (!expandToScroll) {\n            swapFooterPosition('moving');\n        }\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        raf(() => {\n            /**\n             * Dismisses the open keyboard when the sheet drag gesture is started.\n             * Sets the focus onto the modal element.\n             */\n            baseEl.focus();\n        });\n        animation.progressStart(true, 1 - currentBreakpoint);\n    };\n    const onMove = (detail) => {\n        /**\n         * If `expandToScroll` is disabled, we need to see if we're currently below\n         * the footer element and the footer is in a stationary position. If so,\n         * we need to make the stationary the original position so that the footer\n         * collapses with the sheet.\n         */\n        if (!expandToScroll && cachedFooterYPosition !== null && currentFooterState !== null) {\n            // Check if we need to swap the footer position\n            if (detail.currentY >= cachedFooterYPosition && currentFooterState === 'moving') {\n                swapFooterPosition('stationary');\n            }\n            else if (detail.currentY < cachedFooterYPosition && currentFooterState === 'stationary') {\n                swapFooterPosition('moving');\n            }\n        }\n        /**\n         * If `expandToScroll` is disabled, and an upwards swipe gesture is done within\n         * the scrollable content, we should not allow the swipe gesture to continue.\n         */\n        if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl) {\n            return;\n        }\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         * This accounts for when the user scrolls down, scrolls all the way up, and then\n         * pulls down again such that the modal should start to move.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        /**\n         * Given the change in gesture position on the Y axis,\n         * compute where the offset of the animation should be\n         * relative to where the user dragged.\n         */\n        const initialStep = 1 - currentBreakpoint;\n        const secondToLastBreakpoint = breakpoints.length > 1 ? 1 - breakpoints[1] : undefined;\n        const step = initialStep + detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = secondToLastBreakpoint !== undefined && step >= secondToLastBreakpoint && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that when isAttemptingDismissWithCanDismiss is true,\n         * the modifier is always added to the breakpoint that\n         * appears right after the 0 breakpoint.\n         *\n         * Note that this modifier is essentially the progression\n         * between secondToLastBreakpoint and maxStep which is\n         * why we subtract secondToLastBreakpoint. This lets us get\n         * the result as a value from 0 to 1.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss && secondToLastBreakpoint !== undefined\n            ? secondToLastBreakpoint +\n                calculateSpringStep((step - secondToLastBreakpoint) / (maxStep - secondToLastBreakpoint))\n            : step;\n        offset = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(offset);\n    };\n    const onEnd = (detail) => {\n        /**\n         * If expandToScroll is disabled, we should not allow the moveSheetToBreakpoint\n         * function to be called if the user is trying to swipe content upwards and the content\n         * is not scrolled to the top.\n         */\n        if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl && cachedScrollEl.scrollTop > 0) {\n            /**\n             * If expand to scroll is disabled, we need to make sure we swap the footer position\n             * back to stationary so that it will collapse correctly if the modal is dismissed without\n             * dragging (e.g. through a dismiss button).\n             * This can cause issues if the user has a modal with content that can be dragged, as we'll\n             * swap to moving on drag and if we don't swap back here then the footer will get stuck.\n             */\n            swapFooterPosition('stationary');\n            return;\n        }\n        /**\n         * When the gesture releases, we need to determine\n         * the closest breakpoint to snap to.\n         */\n        const velocity = detail.velocityY;\n        const threshold = (detail.deltaY + velocity * 350) / height;\n        const diff = currentBreakpoint - threshold;\n        const closest = breakpoints.reduce((a, b) => {\n            return Math.abs(b - diff) < Math.abs(a - diff) ? b : a;\n        });\n        moveSheetToBreakpoint({\n            breakpoint: closest,\n            breakpointOffset: offset,\n            canDismiss: canDismissBlocksGesture,\n            /**\n             * The swipe is user-driven, so we should\n             * always animate when the gesture ends.\n             */\n            animated: true,\n        });\n    };\n    const moveSheetToBreakpoint = (options) => {\n        const { breakpoint, canDismiss, breakpointOffset, animated } = options;\n        /**\n         * canDismiss should only prevent snapping\n         * when users are trying to dismiss. If canDismiss\n         * is present but the user is trying to swipe upwards,\n         * we should allow that to happen,\n         */\n        const shouldPreventDismiss = canDismiss && breakpoint === 0;\n        const snapToBreakpoint = shouldPreventDismiss ? currentBreakpoint : breakpoint;\n        const shouldRemainOpen = snapToBreakpoint !== 0;\n        currentBreakpoint = 0;\n        /**\n         * Update the animation so that it plays from\n         * the last offset to the closest snap point.\n         */\n        if (wrapperAnimation && backdropAnimation) {\n            wrapperAnimation.keyframes([\n                { offset: 0, transform: `translateY(${breakpointOffset * 100}%)` },\n                { offset: 1, transform: `translateY(${(1 - snapToBreakpoint) * 100}%)` },\n            ]);\n            backdropAnimation.keyframes([\n                {\n                    offset: 0,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(1 - breakpointOffset, backdropBreakpoint)})`,\n                },\n                {\n                    offset: 1,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(snapToBreakpoint, backdropBreakpoint)})`,\n                },\n            ]);\n            if (contentAnimation) {\n                /**\n                 * The modal content should scroll at any breakpoint when expandToScroll\n                 * is disabled. In order to do this, the content needs to be completely\n                 * viewable so scrolling can access everything. Otherwise, the default\n                 * behavior would show the content off the screen and only allow\n                 * scrolling when the sheet is fully expanded.\n                 */\n                contentAnimation.keyframes([\n                    { offset: 0, maxHeight: `${(1 - breakpointOffset) * 100}%` },\n                    { offset: 1, maxHeight: `${snapToBreakpoint * 100}%` },\n                ]);\n            }\n            animation.progressStep(0);\n        }\n        /**\n         * Gesture should remain disabled until the\n         * snapping animation completes.\n         */\n        gesture.enable(false);\n        if (shouldPreventDismiss) {\n            handleCanDismiss(baseEl, animation);\n        }\n        else if (!shouldRemainOpen) {\n            onDismiss();\n        }\n        /**\n         * Enables scrolling immediately if the sheet is about to fully expand\n         * or if it allows scrolling at any breakpoint. Without this, there would\n         * be a ~500ms delay while the modal animation completes, causing a\n         * noticeable lag. Native iOS allows scrolling as soon as the gesture is\n         * released, so we align with that behavior.\n         */\n        if (contentEl && (snapToBreakpoint === breakpoints[breakpoints.length - 1] || !expandToScroll)) {\n            contentEl.scrollY = true;\n        }\n        /**\n         * If expandToScroll is disabled and we're animating\n         * to close the sheet, we need to swap\n         * the footer position to stationary so that it\n         * will collapse correctly. We cannot just always swap\n         * here or it'll be jittery while animating movement.\n         */\n        if (!expandToScroll && snapToBreakpoint === 0) {\n            swapFooterPosition('stationary');\n        }\n        return new Promise((resolve) => {\n            animation\n                .onFinish(() => {\n                if (shouldRemainOpen) {\n                    /**\n                     * If expandToScroll is disabled, we need to swap\n                     * the footer position to stationary so that it\n                     * will act as it would by default.\n                     */\n                    if (!expandToScroll) {\n                        swapFooterPosition('stationary');\n                    }\n                    /**\n                     * Once the snapping animation completes,\n                     * we need to reset the animation to go\n                     * from 0 to 1 so users can swipe in any direction.\n                     * We then set the animation offset to the current\n                     * breakpoint so that it starts at the snapped position.\n                     */\n                    if (wrapperAnimation && backdropAnimation) {\n                        raf(() => {\n                            wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n                            backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n                            contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n                            animation.progressStart(true, 1 - snapToBreakpoint);\n                            currentBreakpoint = snapToBreakpoint;\n                            onBreakpointChange(currentBreakpoint);\n                            /**\n                             * Backdrop should become enabled\n                             * after the backdropBreakpoint value\n                             */\n                            const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n                            if (shouldEnableBackdrop) {\n                                enableBackdrop();\n                            }\n                            else {\n                                disableBackdrop();\n                            }\n                            gesture.enable(true);\n                            resolve();\n                        });\n                    }\n                    else {\n                        gesture.enable(true);\n                        resolve();\n                    }\n                }\n                else {\n                    resolve();\n                }\n                /**\n                 * This must be a one time callback\n                 * otherwise a new callback will\n                 * be added every time onEnd runs.\n                 */\n            }, { oneTimeCallback: true })\n                .progressEnd(1, 0, animated ? 500 : 0);\n        });\n    };\n    const gesture = createGesture({\n        el: wrapperEl,\n        gestureName: 'modalSheet',\n        gesturePriority: 40,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return {\n        gesture,\n        moveSheetToBreakpoint,\n    };\n};\n\nconst modalIosCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}\";\n\nconst modalMdCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}\";\n\nconst Modal = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionModalDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionModalWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionModalWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionModalDidDismiss\", 7);\n        this.ionBreakpointDidChange = createEvent(this, \"ionBreakpointDidChange\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.ionMount = createEvent(this, \"ionMount\", 7);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.coreDelegate = CoreDelegate();\n        this.isSheetModal = false;\n        this.inheritedAttributes = {};\n        this.inline = false;\n        // Whether or not modal is being dismissed via gesture\n        this.gestureAnimationDismissing = false;\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * Controls whether scrolling or dragging within the sheet modal expands\n         * it to a larger breakpoint. This only takes effect when `breakpoints`\n         * and `initialBreakpoint` are set.\n         *\n         * If `true`, scrolling or dragging anywhere in the modal will first expand\n         * it to the next breakpoint. Once fully expanded, scrolling will affect the\n         * content.\n         * If `false`, scrolling will always affect the content. The modal will\n         * only expand when dragging the header or handle. The modal will close when\n         * dragging the header or handle. It can also be closed when dragging the\n         * content, but only if the content is scrolled to the top.\n         */\n        this.expandToScroll = true;\n        /**\n         * A decimal value between 0 and 1 that indicates the\n         * point after which the backdrop will begin to fade in\n         * when using a sheet modal. Prior to this point, the\n         * backdrop will be hidden and the content underneath\n         * the sheet can be interacted with. This value is exclusive\n         * meaning the backdrop will become active after the value\n         * specified.\n         */\n        this.backdropBreakpoint = 0;\n        /**\n         * The interaction behavior for the sheet modal when the handle is pressed.\n         *\n         * Defaults to `\"none\"`, which  means the modal will not change size or position when the handle is pressed.\n         * Set to `\"cycle\"` to let the modal cycle between available breakpoints when pressed.\n         *\n         * Handle behavior is unavailable when the `handle` property is set to `false` or\n         * when the `breakpoints` property is not set (using a fullscreen or card modal).\n         */\n        this.handleBehavior = 'none';\n        /**\n         * If `true`, the modal will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, a backdrop will be displayed behind the modal.\n         * This property controls whether or not the backdrop\n         * darkens the screen when the modal is presented.\n         * It does not control whether or not the backdrop\n         * is active or present in the DOM.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the modal will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the modal will open. If `false`, the modal will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the modalController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the modal dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        /**\n         * If `true`, the component passed into `ion-modal` will\n         * automatically be mounted when the modal is created. The\n         * component will remain mounted even when the modal is dismissed.\n         * However, the component will be destroyed when the modal is\n         * destroyed. This property is not reactive and should only be\n         * used when initially creating a modal.\n         *\n         * Note: This feature only applies to inline modals in JavaScript\n         * frameworks such as Angular, React, and Vue.\n         */\n        this.keepContentsMounted = false;\n        /**\n         * If `true`, focus will not be allowed to move outside of this overlay.\n         * If `false`, focus will be allowed to move outside of the overlay.\n         *\n         * In most scenarios this property should remain set to `true`. Setting\n         * this property to `false` can cause severe accessibility issues as users\n         * relying on assistive technologies may be able to move focus into\n         * a confusing state. We recommend only setting this to `false` when\n         * absolutely necessary.\n         *\n         * Developers may want to consider disabling focus trapping if this\n         * overlay presents a non-Ionic overlay from a 3rd party library.\n         * Developers would disable focus trapping on the Ionic overlay\n         * when presenting the 3rd party overlay and then re-enable\n         * focus trapping when dismissing the 3rd party overlay and moving\n         * focus back to the Ionic overlay.\n         */\n        this.focusTrap = true;\n        /**\n         * Determines whether or not a modal can dismiss\n         * when calling the `dismiss` method.\n         *\n         * If the value is `true` or the value's function returns `true`, the modal will close when trying to dismiss.\n         * If the value is `false` or the value's function returns `false`, the modal will not close when trying to dismiss.\n         *\n         * See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this\n         * if you need to access `this` from within the callback.\n         */\n        this.canDismiss = true;\n        this.onHandleClick = () => {\n            const { sheetTransition, handleBehavior } = this;\n            if (handleBehavior !== 'cycle' || sheetTransition !== undefined) {\n                /**\n                 * The sheet modal should not advance to the next breakpoint\n                 * if the handle behavior is not `cycle` or if the handle\n                 * is clicked while the sheet is moving to a breakpoint.\n                 */\n                return;\n            }\n            this.moveToNextBreakpoint();\n        };\n        this.onBackdropTap = () => {\n            const { sheetTransition } = this;\n            if (sheetTransition !== undefined) {\n                /**\n                 * When the handle is double clicked at the largest breakpoint,\n                 * it will start to move to the first breakpoint. While transitioning,\n                 * the backdrop will often receive the second click. We prevent the\n                 * backdrop from dismissing the modal while moving between breakpoints.\n                 */\n                return;\n            }\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.onLifecycle = (modalEvent) => {\n            const el = this.usersElement;\n            const name = LIFECYCLE_MAP[modalEvent.type];\n            if (el && name) {\n                const ev = new CustomEvent(name, {\n                    bubbles: false,\n                    cancelable: false,\n                    detail: modalEvent.detail,\n                });\n                el.dispatchEvent(ev);\n            }\n        };\n        /**\n         * When the modal receives focus directly, pass focus to the handle\n         * if it exists and is focusable, otherwise let the focus trap handle it.\n         */\n        this.onModalFocus = (ev) => {\n            const { dragHandleEl, el } = this;\n            // Only handle focus if the modal itself was focused (not a child element)\n            if (ev.target === el && dragHandleEl && dragHandleEl.tabIndex !== -1) {\n                dragHandleEl.focus();\n            }\n        };\n        /**\n         * When the slot changes, we need to find all the modals in the slot\n         * and set the data-parent-ion-modal attribute on them so we can find them\n         * and dismiss them when we get dismissed.\n         * We need to do it this way because when a modal is opened, it's moved to\n         * the end of the body and is no longer an actual child of the modal.\n         */\n        this.onSlotChange = ({ target }) => {\n            const slot = target;\n            slot.assignedElements().forEach((el) => {\n                el.querySelectorAll('ion-modal').forEach((childModal) => {\n                    // We don't need to write to the DOM if the modal is already tagged\n                    // If this is a deeply nested modal, this effect should cascade so we don't\n                    // need to worry about another modal claiming the same child.\n                    if (childModal.getAttribute('data-parent-ion-modal') === null) {\n                        childModal.setAttribute('data-parent-ion-modal', this.el.id);\n                    }\n                });\n            });\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    onWindowResize() {\n        // Only handle resize for iOS card modals when no custom animations are provided\n        if (getIonMode(this) !== 'ios' || !this.presentingElement || this.enterAnimation || this.leaveAnimation) {\n            return;\n        }\n        clearTimeout(this.resizeTimeout);\n        this.resizeTimeout = setTimeout(() => {\n            this.handleViewTransition();\n        }, 50); // Debounce to avoid excessive calls during active resizing\n    }\n    breakpointsChanged(breakpoints) {\n        if (breakpoints !== undefined) {\n            this.sortedBreakpoints = breakpoints.sort((a, b) => a - b);\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        prepareOverlay(el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n        this.cleanupViewTransitionListener();\n        this.cleanupParentRemovalObserver();\n    }\n    componentWillLoad() {\n        var _a;\n        const { breakpoints, initialBreakpoint, el, htmlAttributes } = this;\n        const isSheetModal = (this.isSheetModal = breakpoints !== undefined && initialBreakpoint !== undefined);\n        const attributesToInherit = ['aria-label', 'role'];\n        this.inheritedAttributes = inheritAttributes(el, attributesToInherit);\n        // Cache original parent before modal gets moved to body during presentation\n        if (el.parentNode) {\n            this.cachedOriginalParent = el.parentNode;\n        }\n        /**\n         * When using a controller modal you can set attributes\n         * using the htmlAttributes property. Since the above attributes\n         * need to be inherited inside of the modal, we need to look\n         * and see if these attributes are being set via htmlAttributes.\n         *\n         * We could alternatively move this to componentDidLoad to simplify the work\n         * here, but we'd then need to make inheritedAttributes a State variable,\n         * thus causing another render to always happen after the first render.\n         */\n        if (htmlAttributes !== undefined) {\n            attributesToInherit.forEach((attribute) => {\n                const attributeValue = htmlAttributes[attribute];\n                if (attributeValue) {\n                    /**\n                     * If an attribute we need to inherit was\n                     * set using htmlAttributes then add it to\n                     * inheritedAttributes and remove it from htmlAttributes.\n                     * This ensures the attribute is inherited and not\n                     * set on the host.\n                     *\n                     * In this case, if an inherited attribute is set\n                     * on the host element and using htmlAttributes then\n                     * htmlAttributes wins, but that's not a pattern that we recommend.\n                     * The only time you'd need htmlAttributes is when using modalController.\n                     */\n                    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { [attribute]: htmlAttributes[attribute] });\n                    delete htmlAttributes[attribute];\n                }\n            });\n        }\n        if (isSheetModal) {\n            this.currentBreakpoint = this.initialBreakpoint;\n        }\n        if (breakpoints !== undefined && initialBreakpoint !== undefined && !breakpoints.includes(initialBreakpoint)) {\n            printIonWarning('[ion-modal] - Your breakpoints array must include the initialBreakpoint value.');\n        }\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * If modal was rendered with isOpen=\"true\"\n         * then we should open modal immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        this.breakpointsChanged(this.breakpoints);\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Determines whether or not an overlay\n     * is being used inline or via a controller/JS\n     * and returns the correct delegate.\n     * By default, subsequent calls to getDelegate\n     * will use a cached version of the delegate.\n     * This is useful for calling dismiss after\n     * present so that the correct delegate is given.\n     */\n    getDelegate(force = false) {\n        if (this.workingDelegate && !force) {\n            return {\n                delegate: this.workingDelegate,\n                inline: this.inline,\n            };\n        }\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = this.el.parentNode;\n        const inline = (this.inline = parentEl !== null && !this.hasController);\n        const delegate = (this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate);\n        return { inline, delegate };\n    }\n    /**\n     * Determines whether or not the\n     * modal is allowed to dismiss based\n     * on the state of the canDismiss prop.\n     */\n    async checkCanDismiss(data, role) {\n        const { canDismiss } = this;\n        if (typeof canDismiss === 'function') {\n            return canDismiss(data, role);\n        }\n        return canDismiss;\n    }\n    /**\n     * Present the modal overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        if (this.presented) {\n            unlock();\n            return;\n        }\n        const { presentingElement, el } = this;\n        /**\n         * If the modal is presented multiple times (inline modals), we\n         * need to reset the current breakpoint to the initial breakpoint.\n         */\n        this.currentBreakpoint = this.initialBreakpoint;\n        const { inline, delegate } = this.getDelegate(true);\n        /**\n         * Emit ionMount so JS Frameworks have an opportunity\n         * to add the child component to the DOM. The child\n         * component will be assigned to this.usersElement below.\n         */\n        this.ionMount.emit();\n        this.usersElement = await attachComponent(delegate, el, this.component, ['ion-page'], this.componentProps, inline);\n        /**\n         * When using the lazy loaded build of Stencil, we need to wait\n         * for every Stencil component instance to be ready before presenting\n         * otherwise there can be a flash of unstyled content. With the\n         * custom elements bundle we need to wait for the JS framework\n         * mount the inner contents of the overlay otherwise WebKit may\n         * get the transition incorrect.\n         */\n        if (hasLazyBuild(el)) {\n            await deepReady(this.usersElement);\n            /**\n             * If keepContentsMounted=\"true\" then the\n             * JS Framework has already mounted the inner\n             * contents so there is no need to wait.\n             * Otherwise, we need to wait for the JS\n             * Framework to mount the inner contents\n             * of this component.\n             */\n        }\n        else if (!this.keepContentsMounted) {\n            await waitForMount();\n        }\n        writeTask(() => this.el.classList.add('show-modal'));\n        const hasCardModal = presentingElement !== undefined;\n        /**\n         * We need to change the status bar at the\n         * start of the animation so that it completes\n         * by the time the card animation is done.\n         */\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            // Cache the original status bar color before the modal is presented\n            this.statusBarStyle = await StatusBar.getStyle();\n            setCardStatusBarDark();\n        }\n        await present(this, 'modalEnter', iosEnterAnimation, mdEnterAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        });\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined') {\n            /**\n             * This needs to be setup before any\n             * non-transition async work so it can be dereferenced\n             * in the dismiss method. The dismiss method\n             * only waits for the entering transition\n             * to finish. It does not wait for all of the `present`\n             * method to resolve.\n             */\n            this.keyboardOpenCallback = () => {\n                if (this.gesture) {\n                    /**\n                     * When the native keyboard is opened and the webview\n                     * is resized, the gesture implementation will become unresponsive\n                     * and enter a free-scroll mode.\n                     *\n                     * When the keyboard is opened, we disable the gesture for\n                     * a single frame and re-enable once the contents have repositioned\n                     * from the keyboard placement.\n                     */\n                    this.gesture.enable(false);\n                    raf(() => {\n                        if (this.gesture) {\n                            this.gesture.enable(true);\n                        }\n                    });\n                }\n            };\n            window.addEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n        }\n        if (this.isSheetModal) {\n            this.initSheetGesture();\n        }\n        else if (hasCardModal) {\n            this.initSwipeToClose();\n        }\n        // Initialize view transition listener for iOS card modals\n        this.initViewTransitionListener();\n        // Initialize parent removal observer\n        this.initParentRemovalObserver();\n        unlock();\n    }\n    initSwipeToClose() {\n        var _a;\n        if (getIonMode(this) !== 'ios') {\n            return;\n        }\n        const { el } = this;\n        // All of the elements needed for the swipe gesture\n        // should be in the DOM and referenced by now, except\n        // for the presenting el\n        const animationBuilder = this.leaveAnimation || config.get('modalLeave', iosLeaveAnimation);\n        const ani = (this.animation = animationBuilder(el, {\n            presentingEl: this.presentingElement,\n            expandToScroll: this.expandToScroll,\n        }));\n        const contentEl = findIonContent(el);\n        if (!contentEl) {\n            printIonContentErrorMsg(el);\n            return;\n        }\n        const statusBarStyle = (_a = this.statusBarStyle) !== null && _a !== void 0 ? _a : Style.Default;\n        this.gesture = createSwipeToCloseGesture(el, ani, statusBarStyle, () => {\n            /**\n             * While the gesture animation is finishing\n             * it is possible for a user to tap the backdrop.\n             * This would result in the dismiss animation\n             * being played again. Typically this is avoided\n             * by setting `presented = false` on the overlay\n             * component; however, we cannot do that here as\n             * that would prevent the element from being\n             * removed from the DOM.\n             */\n            this.gestureAnimationDismissing = true;\n            /**\n             * Reset the status bar style as the dismiss animation\n             * starts otherwise the status bar will be the wrong\n             * color for the duration of the dismiss animation.\n             * The dismiss method does this as well, but\n             * in this case it's only called once the animation\n             * has finished.\n             */\n            setCardStatusBarDefault(this.statusBarStyle);\n            this.animation.onFinish(async () => {\n                await this.dismiss(undefined, GESTURE);\n                this.gestureAnimationDismissing = false;\n            });\n        });\n        this.gesture.enable(true);\n    }\n    initSheetGesture() {\n        const { wrapperEl, initialBreakpoint, backdropBreakpoint } = this;\n        if (!wrapperEl || initialBreakpoint === undefined) {\n            return;\n        }\n        const animationBuilder = this.enterAnimation || config.get('modalEnter', iosEnterAnimation);\n        const ani = (this.animation = animationBuilder(this.el, {\n            presentingEl: this.presentingElement,\n            currentBreakpoint: initialBreakpoint,\n            backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        }));\n        ani.progressStart(true, 1);\n        const { gesture, moveSheetToBreakpoint } = createSheetGesture(this.el, this.backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, ani, this.sortedBreakpoints, this.expandToScroll, () => { var _a; return (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : 0; }, () => this.sheetOnDismiss(), (breakpoint) => {\n            if (this.currentBreakpoint !== breakpoint) {\n                this.currentBreakpoint = breakpoint;\n                this.ionBreakpointDidChange.emit({ breakpoint });\n            }\n        });\n        this.gesture = gesture;\n        this.moveSheetToBreakpoint = moveSheetToBreakpoint;\n        this.gesture.enable(true);\n    }\n    sheetOnDismiss() {\n        /**\n         * While the gesture animation is finishing\n         * it is possible for a user to tap the backdrop.\n         * This would result in the dismiss animation\n         * being played again. Typically this is avoided\n         * by setting `presented = false` on the overlay\n         * component; however, we cannot do that here as\n         * that would prevent the element from being\n         * removed from the DOM.\n         */\n        this.gestureAnimationDismissing = true;\n        this.animation.onFinish(async () => {\n            this.currentBreakpoint = 0;\n            this.ionBreakpointDidChange.emit({ breakpoint: this.currentBreakpoint });\n            await this.dismiss(undefined, GESTURE);\n            this.gestureAnimationDismissing = false;\n        });\n    }\n    /**\n     * Dismiss the modal overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the modal.\n     * For example, `cancel` or `backdrop`.\n     */\n    async dismiss(data, role) {\n        var _a;\n        if (this.gestureAnimationDismissing && role !== GESTURE) {\n            return false;\n        }\n        /**\n         * Because the canDismiss check below is async,\n         * we need to claim a lock before the check happens,\n         * in case the dismiss transition does run.\n         */\n        const unlock = await this.lockController.lock();\n        /**\n         * Dismiss all child modals. This is especially important in\n         * Angular and React because it's possible to lose control of a child\n         * modal when the parent modal is dismissed.\n         */\n        await this.dismissNestedModals();\n        /**\n         * If a canDismiss handler is responsible\n         * for calling the dismiss method, we should\n         * not run the canDismiss check again.\n         */\n        if (role !== 'handler' && !(await this.checkCanDismiss(data, role))) {\n            unlock();\n            return false;\n        }\n        const { presentingElement } = this;\n        /**\n         * We need to start the status bar change\n         * before the animation so that the change\n         * finishes when the dismiss animation does.\n         */\n        const hasCardModal = presentingElement !== undefined;\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            setCardStatusBarDefault(this.statusBarStyle);\n        }\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined' && this.keyboardOpenCallback) {\n            window.removeEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n            this.keyboardOpenCallback = undefined;\n        }\n        const dismissed = await dismiss(this, data, role, 'modalLeave', iosLeaveAnimation, mdLeaveAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        });\n        if (dismissed) {\n            const { delegate } = this.getDelegate();\n            await detachComponent(delegate, this.usersElement);\n            writeTask(() => this.el.classList.remove('show-modal'));\n            if (this.animation) {\n                this.animation.destroy();\n            }\n            if (this.gesture) {\n                this.gesture.destroy();\n            }\n            this.cleanupViewTransitionListener();\n            this.cleanupParentRemovalObserver();\n        }\n        this.currentBreakpoint = undefined;\n        this.animation = undefined;\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the modal did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionModalDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the modal will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionModalWillDismiss');\n    }\n    /**\n     * Move a sheet style modal to a specific breakpoint.\n     *\n     * @param breakpoint The breakpoint value to move the sheet modal to.\n     * Must be a value defined in your `breakpoints` array.\n     */\n    async setCurrentBreakpoint(breakpoint) {\n        if (!this.isSheetModal) {\n            printIonWarning('[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.');\n            return;\n        }\n        if (!this.breakpoints.includes(breakpoint)) {\n            printIonWarning(`[ion-modal] - Attempted to set invalid breakpoint value ${breakpoint}. Please double check that the breakpoint value is part of your defined breakpoints.`);\n            return;\n        }\n        const { currentBreakpoint, moveSheetToBreakpoint, canDismiss, breakpoints, animated } = this;\n        if (currentBreakpoint === breakpoint) {\n            return;\n        }\n        if (moveSheetToBreakpoint) {\n            this.sheetTransition = moveSheetToBreakpoint({\n                breakpoint,\n                breakpointOffset: 1 - currentBreakpoint,\n                canDismiss: canDismiss !== undefined && canDismiss !== true && breakpoints[0] === 0,\n                animated,\n            });\n            await this.sheetTransition;\n            this.sheetTransition = undefined;\n        }\n    }\n    /**\n     * Returns the current breakpoint of a sheet style modal\n     */\n    async getCurrentBreakpoint() {\n        return this.currentBreakpoint;\n    }\n    async moveToNextBreakpoint() {\n        const { breakpoints, currentBreakpoint } = this;\n        if (!breakpoints || currentBreakpoint == null) {\n            /**\n             * If the modal does not have breakpoints and/or the current\n             * breakpoint is not set, we can't move to the next breakpoint.\n             */\n            return false;\n        }\n        const allowedBreakpoints = breakpoints.filter((b) => b !== 0);\n        const currentBreakpointIndex = allowedBreakpoints.indexOf(currentBreakpoint);\n        const nextBreakpointIndex = (currentBreakpointIndex + 1) % allowedBreakpoints.length;\n        const nextBreakpoint = allowedBreakpoints[nextBreakpointIndex];\n        /**\n         * Sets the current breakpoint to the next available breakpoint.\n         * If the current breakpoint is the last breakpoint, we set the current\n         * breakpoint to the first non-zero breakpoint to avoid dismissing the sheet.\n         */\n        await this.setCurrentBreakpoint(nextBreakpoint);\n        return true;\n    }\n    initViewTransitionListener() {\n        // Only enable for iOS card modals when no custom animations are provided\n        if (getIonMode(this) !== 'ios' || !this.presentingElement || this.enterAnimation || this.leaveAnimation) {\n            return;\n        }\n        // Set initial view state\n        this.currentViewIsPortrait = window.innerWidth < 768;\n    }\n    handleViewTransition() {\n        const isPortrait = window.innerWidth < 768;\n        // Only transition if view state actually changed\n        if (this.currentViewIsPortrait === isPortrait) {\n            return;\n        }\n        // Cancel any ongoing transition animation\n        if (this.viewTransitionAnimation) {\n            this.viewTransitionAnimation.destroy();\n            this.viewTransitionAnimation = undefined;\n        }\n        const { presentingElement } = this;\n        if (!presentingElement) {\n            return;\n        }\n        // Create transition animation\n        let transitionAnimation;\n        if (this.currentViewIsPortrait && !isPortrait) {\n            // Portrait to landscape transition\n            transitionAnimation = portraitToLandscapeTransition(this.el, {\n                presentingEl: presentingElement});\n        }\n        else {\n            // Landscape to portrait transition\n            transitionAnimation = landscapeToPortraitTransition(this.el, {\n                presentingEl: presentingElement});\n        }\n        // Update state and play animation\n        this.currentViewIsPortrait = isPortrait;\n        this.viewTransitionAnimation = transitionAnimation;\n        transitionAnimation.play().then(() => {\n            this.viewTransitionAnimation = undefined;\n            // After orientation transition, recreate the swipe-to-close gesture\n            // with updated animation that reflects the new presenting element state\n            this.reinitSwipeToClose();\n        });\n    }\n    cleanupViewTransitionListener() {\n        // Clear any pending resize timeout\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            this.resizeTimeout = undefined;\n        }\n        if (this.viewTransitionAnimation) {\n            this.viewTransitionAnimation.destroy();\n            this.viewTransitionAnimation = undefined;\n        }\n    }\n    reinitSwipeToClose() {\n        // Only reinitialize if we have a presenting element and are on iOS\n        if (getIonMode(this) !== 'ios' || !this.presentingElement) {\n            return;\n        }\n        // Clean up existing gesture and animation\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        if (this.animation) {\n            // Properly end the progress-based animation at initial state before destroying\n            // to avoid leaving modal in intermediate swipe position\n            this.animation.progressEnd(0, 0, 0);\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Force the modal back to the correct position or it could end up\n        // in a weird state after destroying the animation\n        raf(() => {\n            this.ensureCorrectModalPosition();\n            this.initSwipeToClose();\n        });\n    }\n    ensureCorrectModalPosition() {\n        const { el, presentingElement } = this;\n        const root = getElementRoot(el);\n        const wrapperEl = root.querySelector('.modal-wrapper');\n        if (wrapperEl) {\n            wrapperEl.style.transform = 'translateY(0vh)';\n            wrapperEl.style.opacity = '1';\n        }\n        if ((presentingElement === null || presentingElement === void 0 ? void 0 : presentingElement.tagName) === 'ION-MODAL') {\n            const isPortrait = window.innerWidth < 768;\n            if (isPortrait) {\n                const transformOffset = !CSS.supports('width', 'max(0px, 1px)')\n                    ? '30px'\n                    : 'max(30px, var(--ion-safe-area-top))';\n                const scale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n                presentingElement.style.transform = `translateY(${transformOffset}) scale(${scale})`;\n            }\n            else {\n                presentingElement.style.transform = 'translateY(0px) scale(1)';\n            }\n        }\n    }\n    async dismissNestedModals() {\n        const nestedModals = document.querySelectorAll(`ion-modal[data-parent-ion-modal=\"${this.el.id}\"]`);\n        nestedModals === null || nestedModals === void 0 ? void 0 : nestedModals.forEach(async (modal) => {\n            await modal.dismiss(undefined, 'parent-dismissed');\n        });\n    }\n    initParentRemovalObserver() {\n        if (typeof MutationObserver === 'undefined') {\n            return;\n        }\n        // Only observe if we have a cached parent and are in browser environment\n        if (typeof window === 'undefined' || !this.cachedOriginalParent) {\n            return;\n        }\n        // Don't observe document or fragment nodes as they can't be \"removed\"\n        if (this.cachedOriginalParent.nodeType === Node.DOCUMENT_NODE ||\n            this.cachedOriginalParent.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n            return;\n        }\n        this.parentRemovalObserver = new MutationObserver((mutations) => {\n            mutations.forEach((mutation) => {\n                if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {\n                    // Check if our cached original parent was removed\n                    const cachedParentWasRemoved = Array.from(mutation.removedNodes).some((node) => {\n                        var _a, _b;\n                        const isDirectMatch = node === this.cachedOriginalParent;\n                        const isContainedMatch = this.cachedOriginalParent\n                            ? (_b = (_a = node).contains) === null || _b === void 0 ? void 0 : _b.call(_a, this.cachedOriginalParent)\n                            : false;\n                        return isDirectMatch || isContainedMatch;\n                    });\n                    // Also check if parent is no longer connected to DOM\n                    const cachedParentDisconnected = this.cachedOriginalParent && !this.cachedOriginalParent.isConnected;\n                    if (cachedParentWasRemoved || cachedParentDisconnected) {\n                        this.dismiss(undefined, 'parent-removed');\n                        // Release the reference to the cached original parent\n                        // so we don't have a memory leak\n                        this.cachedOriginalParent = undefined;\n                    }\n                }\n            });\n        });\n        // Observe document body with subtree to catch removals at any level\n        this.parentRemovalObserver.observe(document.body, {\n            childList: true,\n            subtree: true,\n        });\n    }\n    cleanupParentRemovalObserver() {\n        var _a;\n        (_a = this.parentRemovalObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this.parentRemovalObserver = undefined;\n    }\n    render() {\n        const { handle, isSheetModal, presentingElement, htmlAttributes, handleBehavior, inheritedAttributes, focusTrap, expandToScroll, } = this;\n        const showHandle = handle !== false && isSheetModal;\n        const mode = getIonMode(this);\n        const isCardModal = presentingElement !== undefined && mode === 'ios';\n        const isHandleCycle = handleBehavior === 'cycle';\n        const isSheetModalWithHandle = isSheetModal && showHandle;\n        return (h(Host, Object.assign({ key: '9e9a7bd591eb17a225a00b4fa2e379e94601d17f', \"no-router\": true,\n            // Allow the modal to be navigable when the handle is focusable\n            tabIndex: isHandleCycle && isSheetModalWithHandle ? 0 : -1 }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign({ [mode]: true, ['modal-default']: !isCardModal && !isSheetModal, [`modal-card`]: isCardModal, [`modal-sheet`]: isSheetModal, [`modal-no-expand-scroll`]: isSheetModal && !expandToScroll, 'overlay-hidden': true, [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false }, getClassMap(this.cssClass)), onIonBackdropTap: this.onBackdropTap, onIonModalDidPresent: this.onLifecycle, onIonModalWillPresent: this.onLifecycle, onIonModalWillDismiss: this.onLifecycle, onIonModalDidDismiss: this.onLifecycle, onFocus: this.onModalFocus }), h(\"ion-backdrop\", { key: 'e5eae2c14f830f75e308fcd7f4c10c86fac5b962', ref: (el) => (this.backdropEl = el), visible: this.showBackdrop, tappable: this.backdropDismiss, part: \"backdrop\" }), mode === 'ios' && h(\"div\", { key: 'e268f9cd310c3cf4e051b5b92524ce4fb70d005e', class: \"modal-shadow\" }), h(\"div\", Object.assign({ key: '9c380f36c18144c153077b15744d1c3346bce63e',\n            /*\n              role and aria-modal must be used on the\n              same element. They must also be set inside the\n              shadow DOM otherwise ion-button will not be highlighted\n              when using VoiceOver: https://bugs.webkit.org/show_bug.cgi?id=247134\n            */\n            role: \"dialog\" }, inheritedAttributes, { \"aria-modal\": \"true\", class: \"modal-wrapper ion-overlay-wrapper\", part: \"content\", ref: (el) => (this.wrapperEl = el) }), showHandle && (h(\"button\", { key: '2d5ee6d5959d97309c306e8ce72eb0f2c19be144', class: \"modal-handle\",\n            // Prevents the handle from receiving keyboard focus when it does not cycle\n            tabIndex: !isHandleCycle ? -1 : 0, \"aria-label\": \"Activate to adjust the size of the dialog overlaying the screen\", onClick: isHandleCycle ? this.onHandleClick : undefined, part: \"handle\", ref: (el) => (this.dragHandleEl = el) })), h(\"slot\", { key: '5590434c35ea04c42fc006498bc189038e15a298', onSlotchange: this.onSlotChange }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst LIFECYCLE_MAP = {\n    ionModalDidPresent: 'ionViewDidEnter',\n    ionModalWillPresent: 'ionViewWillEnter',\n    ionModalWillDismiss: 'ionViewWillLeave',\n    ionModalDidDismiss: 'ionViewDidLeave',\n};\nModal.style = {\n    ios: modalIosCss,\n    md: modalMdCss\n};\n\nexport { Modal as ion_modal };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "m", "printIonWarning", "w", "writeTask", "l", "config", "h", "j", "Host", "k", "getElement", "f", "findClosestIonContent", "i", "isIonContent", "disableContentScrollY", "resetContentScrollY", "a", "find<PERSON><PERSON><PERSON><PERSON>nt", "p", "printIonContentErrorMsg", "C", "CoreDelegate", "attachComponent", "detachComponent", "clamp", "g", "getElementRoot", "raf", "b", "inheritAttributes", "hasLazyBuild", "c", "createLockController", "getCapacitor", "G", "GESTURE", "O", "OVERLAY_GESTURE_PRIORITY", "F", "FOCUS_TRAP_DISABLE_CLASS", "createTriggerController", "B", "BACKDROP", "prepareOverlay", "setOverlayId", "present", "dismiss", "eventMethod", "getClassMap", "deepReady", "waitForMount", "KEYBOARD_DID_OPEN", "createAnimation", "getTimeGivenProgression", "createGesture", "win", "Style", "StatusBar", "getEngine", "capacitor", "isPluginAvailable", "Plugins", "undefined", "setStyle", "options", "engine", "getStyle", "_ref", "_asyncToGenerator", "<PERSON><PERSON><PERSON>", "style", "getInfo", "apply", "arguments", "getBackdropValueForSheet", "x", "backdropBreakpoint", "slope", "setCardStatusBarDark", "innerWidth", "Dark", "setCardStatusBarDefault", "defaultStyle", "handleCanDismiss", "_ref2", "el", "animation", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isRunning", "onFinish", "oneTimeCallback", "_x", "_x2", "calculateSpringStep", "t", "SwipeToCloseDefaults", "MIN_PRESENTING_SCALE", "createSwipeToCloseGesture", "statusBarStyle", "on<PERSON><PERSON><PERSON>", "DISMISS_THRESHOLD", "height", "offsetHeight", "isOpen", "canDismissBlocksGesture", "contentEl", "scrollEl", "canDismissMaxStep", "initialScrollY", "lastStep", "getScrollY", "scrollY", "canStart", "detail", "target", "event", "closest", "root", "querySelector", "hasRefresherInContent", "scrollTop", "footer", "onStart", "deltaY", "progressStart", "onMove", "step", "isAttemptingDismissWithCanDismiss", "maxStep", "processedStep", "clampedStep", "progressStep", "onEnd", "velocity", "velocityY", "threshold", "shouldComplete", "newStepValue", "easing", "duration", "computeDuration", "gesture", "enable", "progressEnd", "<PERSON><PERSON><PERSON>", "gesturePriority", "direction", "remaining", "Math", "abs", "createSheetEnterAnimation", "opts", "currentBreakpoint", "expandToScroll", "shouldShowBackdrop", "initialBackdrop", "backdropAnimation", "fromTo", "beforeStyles", "afterClearStyles", "wrapperAnimation", "keyframes", "offset", "opacity", "transform", "contentAnimation", "maxHeight", "createSheetLeaveAnimation", "backdropValue", "defaultBackdrop", "customBackdrop", "createEnterAnimation$1", "iosEnterAnimation", "baseEl", "presentingEl", "addElement", "querySelectorAll", "baseAnimation", "addAnimation", "isPortrait", "window", "hasCardModal", "tagName", "presentingElement", "presentingElRoot", "presentingAnimation", "overflow", "bodyEl", "document", "body", "transformOffset", "CSS", "supports", "modalTransform", "toPresentingScale", "finalTransform", "afterStyles", "beforeAddWrite", "setProperty", "filter", "borderRadius", "shadowAnimation", "createLeaveAnimation$1", "iosLeaveAnimation", "beforeClearStyles", "currentStep", "numModals", "Array", "from", "length", "portraitToLandscapeTransition", "presentingElIsCardModal", "fromTransform", "toTransform", "landscapeToPortraitTransition", "createEnterAnimation", "mdEnterAnimation", "createLeaveAnimation", "mdLeaveAnimation", "createSheetGesture", "backdropEl", "wrapperEl", "initialBreakpoint", "breakpoints", "getCurrentBreakpoint", "onBreakpointChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WRAPPER_KEYFRAMES", "BACKDROP_KEYFRAMES", "CONTENT_KEYFRAMES", "clientHeight", "cachedScrollEl", "cachedFooterEls", "cachedFooterYPosition", "currentFooterState", "maxBreakpoint", "minBreakpoint", "childAnimations", "find", "ani", "id", "enableBackdrop", "classList", "remove", "disableBackdrop", "add", "swapFooterPosition", "newPosition", "page", "for<PERSON>ach", "cachedFooterEl", "removeProperty", "append<PERSON><PERSON><PERSON>", "footerHeights", "index", "cachedFooterElRect", "getBoundingClientRect", "bodyRect", "absoluteTop", "top", "absoluteLeft", "left", "clientWidth", "header", "shouldEnableBackdrop", "targetEl", "focus", "currentY", "initialStep", "secondToLastBreakpoint", "diff", "reduce", "moveSheetToBreakpoint", "breakpoint", "breakpointOffset", "animated", "shouldPreventDismiss", "snapToBreakpoint", "shouldRemainOpen", "Promise", "resolve", "modalIosCss", "modalMdCss", "Modal", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ionBreakpointDidChange", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ionMount", "lockController", "triggerController", "coreDelegate", "isSheetModal", "inheritedAttributes", "inline", "gestureAnimationDismissing", "presented", "hasController", "keyboardClose", "handleBehavior", "<PERSON><PERSON><PERSON><PERSON>", "showBackdrop", "keepContentsMounted", "focusTrap", "onHandleClick", "sheetTransition", "moveToNextBreakpoint", "onBackdropTap", "onLifecycle", "modalEvent", "usersElement", "name", "LIFECYCLE_MAP", "type", "ev", "CustomEvent", "bubbles", "cancelable", "dispatchEvent", "onModalFocus", "dragHandleEl", "tabIndex", "onSlotChange", "slot", "assignedElements", "childModal", "getAttribute", "setAttribute", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "trigger", "addClickListener", "onWindowResize", "enterAnimation", "leaveAnimation", "clearTimeout", "resizeTimeout", "setTimeout", "handleViewTransition", "breakpointsChanged", "sortedBreakpoints", "sort", "connectedCallback", "disconnectedCallback", "removeClickListener", "cleanupViewTransitionListener", "cleanupParentRemovalObserver", "componentWillLoad", "_a", "htmlAttributes", "attributesToInherit", "parentNode", "cachedOriginalParent", "attribute", "attributeValue", "Object", "assign", "includes", "componentDidLoad", "getDelegate", "force", "workingDelegate", "delegate", "parentEl", "checkCanDismiss", "data", "role", "_this", "_this2", "unlock", "lock", "emit", "component", "componentProps", "keyboardOpenCallback", "addEventListener", "initSheetGesture", "initSwipeToClose", "initViewTransitionListener", "initParentRemovalObserver", "_this3", "animationBuilder", "get", "sheetOn<PERSON><PERSON>iss", "_this4", "_this5", "dismissNestedModals", "removeEventListener", "dismissed", "destroy", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "setCurrentBreakpoint", "_this6", "_this7", "_this8", "allowedBreakpoints", "currentBreakpointIndex", "indexOf", "nextBreakpointIndex", "nextBreakpoint", "currentViewIsPortrait", "viewTransitionAnimation", "transitionAnimation", "play", "then", "reinitSwipeToClose", "ensureCorrectModalPosition", "scale", "_this9", "nestedModals", "_ref5", "modal", "_x3", "MutationObserver", "nodeType", "Node", "DOCUMENT_NODE", "DOCUMENT_FRAGMENT_NODE", "parentRemovalObserver", "mutations", "mutation", "removedNodes", "cachedParentWasRemoved", "some", "node", "_b", "isDirectMatch", "isContainedMatch", "contains", "call", "cachedParentDisconnected", "isConnected", "observe", "childList", "subtree", "disconnect", "render", "handle", "showHandle", "mode", "isCardModal", "isHandleCycle", "isSheetModalWithHandle", "key", "zIndex", "overlayIndex", "class", "cssClass", "onIonBackdropTap", "onIonModalDidPresent", "onIonModalWillPresent", "onIonModalWillDismiss", "onIonModalDidDismiss", "onFocus", "ref", "visible", "tappable", "part", "onClick", "onSlotchange", "watchers", "ionModalDidPresent", "ionModalWillPresent", "ionModalWillDismiss", "ionModalDidDismiss", "ios", "md", "ion_modal"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}