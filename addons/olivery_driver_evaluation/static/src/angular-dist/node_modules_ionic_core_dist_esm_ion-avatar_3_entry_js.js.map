{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2F;AAC7B;AAE9D,MAAMS,YAAY,GAAG,2PAA2P;AAEhR,MAAMC,WAAW,GAAG,2PAA2P;AAE/Q,MAAMC,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQZ,qDAAC,CAACI,iDAAI,EAAE;MAAES,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,qDAAU,CAAC,IAAI;IAAE,CAAC,EAAEF,qDAAC,CAAC,MAAM,EAAE;MAAEa,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDJ,MAAM,CAACM,KAAK,GAAG;EACXC,GAAG,EAAET,YAAY;EACjBU,EAAE,EAAET;AACR,CAAC;AAED,MAAMU,WAAW,GAAG,q4BAAq4B;AAEz5B,MAAMC,UAAU,GAAG,k7BAAk7B;AAEr8B,MAAMC,KAAK,GAAG,MAAM;EAChBV,WAAWA,CAACC,OAAO,EAAE;IACjBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMS,IAAI,GAAGnB,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQF,qDAAC,CAACI,iDAAI,EAAE;MAAES,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAER,qDAAkB,CAAC,IAAI,CAACgB,KAAK,EAAE;QACjG,CAACD,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAErB,qDAAC,CAAC,MAAM,EAAE;MAAEa,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDO,KAAK,CAACL,KAAK,GAAG;EACVC,GAAG,EAAEE,WAAW;EAChBD,EAAE,EAAEE;AACR,CAAC;AAED,MAAMI,YAAY,GAAG,6QAA6Q;AAElS,MAAMC,SAAS,GAAG,MAAM;EACpBd,WAAWA,CAACC,OAAO,EAAE;IACjBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQZ,qDAAC,CAACI,iDAAI,EAAE;MAAES,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,qDAAU,CAAC,IAAI;IAAE,CAAC,EAAEF,qDAAC,CAAC,MAAM,EAAE;MAAEa,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDW,SAAS,CAACT,KAAK,GAAGQ,YAAY", "sources": ["./node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as getIonMode, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\n\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\n\nconst Avatar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: '998217066084f966bf5d356fed85bcbd451f675a', class: getIonMode(this) }, h(\"slot\", { key: '1a6f7c9d4dc6a875f86b5b3cda6d59cb39587f22' })));\n    }\n};\nAvatar.style = {\n    ios: avatarIosCss,\n    md: avatarMdCss\n};\n\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\n\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\n\nconst Badge = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '1a2d39c5deec771a2f2196447627b62a7d4c8389', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'fc1b6587f1ed24715748eb6785e7fb7a57cdd5cd' })));\n    }\n};\nBadge.style = {\n    ios: badgeIosCss,\n    md: badgeMdCss\n};\n\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\n\nconst Thumbnail = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: '70ada828e8cf541ab3b47f94b7e56ce34114ef88', class: getIonMode(this) }, h(\"slot\", { key: 'c43e105669d2bae123619b616f3af8ca2f722d61' })));\n    }\n};\nThumbnail.style = thumbnailCss;\n\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };\n"], "names": ["r", "registerInstance", "h", "e", "getIonMode", "j", "Host", "c", "createColorClasses", "avatarIosCss", "avatarMdCss", "Avatar", "constructor", "hostRef", "render", "key", "class", "style", "ios", "md", "badgeIosCss", "badgeMdCss", "Badge", "mode", "color", "thumbnailCss", "<PERSON><PERSON><PERSON><PERSON>", "ion_avatar", "ion_badge", "ion_thumbnail"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}