{"version": 3, "file": "default-node_modules_ionic_core_dist_esm_input_utils-zWijNCrx_js-node_modules_ionic_core_dist-2e0994.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+C;AACE;AACQ;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,4BAA4B,GAAGA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,gBAAgB,KAAK;EACrE,IAAIC,oBAAoB;EACxB,IAAIC,8BAA8B;EAClC,IAAIV,iDAAG,KAAKW,SAAS,IAAI,uEAAyB,EAAE;IAChD,MAAMC,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DE,oBAAoB,GAAG,IAAIM,gBAAgB,CAAEC,OAAO,IAAK;MACrD,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACE,UAAU,EAAE;UACjC;AACpB;AACA;AACA;UACoB,IAAID,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIV,KAAK,CAACW,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;YAClE;AACxB;AACA;AACA;AACA;YACwBhB,gBAAgB,CAAC,CAAC;YAClB;AACxB;AACA;AACA;AACA;AACA;YACwBN,uDAAG,CAAC,MAAMuB,kBAAkB,CAACP,IAAI,CAAC,CAAC;YACnC;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACFT,oBAAoB,CAACiB,OAAO,CAACpB,EAAE,EAAE;MAC7BqB,SAAS,EAAE,IAAI;MACf;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYC,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMH,kBAAkB,GAAII,SAAS,IAAK;IACtC,IAAIC,EAAE;IACN,IAAIpB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACqB,UAAU,CAAC,CAAC;MAC3CrB,8BAA8B,GAAGC,SAAS;IAC9C;IACAD,8BAA8B,GAAG,IAAIK,gBAAgB,CAAEC,OAAO,IAAK;MAC/DR,gBAAgB,CAAC,CAAC;MAClB,KAAK,MAAMS,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACe,YAAY,EAAE;UACnC;AACpB;AACA;AACA;AACA;UACoB,IAAId,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIJ,IAAI,CAACM,IAAI,KAAKjB,QAAQ,EAAE;YAC/D0B,6BAA6B,CAAC,CAAC;UACnC;QACJ;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;IACQvB,8BAA8B,CAACgB,OAAO,CAAC,CAACI,EAAE,GAAGD,SAAS,CAACK,aAAa,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,SAAS,EAAE;MAAED,OAAO,EAAE,IAAI;MAAED,SAAS,EAAE;IAAK,CAAC,CAAC;EACzJ,CAAC;EACD,MAAMQ,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAI1B,oBAAoB,EAAE;MACtBA,oBAAoB,CAACsB,UAAU,CAAC,CAAC;MACjCtB,oBAAoB,GAAGE,SAAS;IACpC;IACAsB,6BAA6B,CAAC,CAAC;EACnC,CAAC;EACD,MAAMA,6BAA6B,GAAGA,CAAA,KAAM;IACxC,IAAIvB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACqB,UAAU,CAAC,CAAC;MAC3CrB,8BAA8B,GAAGC,SAAS;IAC9C;EACJ,CAAC;EACD,OAAO;IACHwB;EACJ,CAAC;AACL,CAAC;AAED,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,KAAK;EAC3D,MAAMC,WAAW,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACC,MAAM;EAC/D,MAAMC,kBAAkB,GAAGC,uBAAuB,CAACJ,WAAW,EAAEF,SAAS,CAAC;EAC1E;AACJ;AACA;AACA;EACI,IAAIC,gBAAgB,KAAK5B,SAAS,EAAE;IAChC,OAAOgC,kBAAkB;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI;IACA,OAAOJ,gBAAgB,CAACC,WAAW,EAAEF,SAAS,CAAC;EACnD,CAAC,CACD,OAAOO,CAAC,EAAE;IACNzC,qDAAa,CAAC,yDAAyD,EAAEyC,CAAC,CAAC;IAC3E,OAAOF,kBAAkB;EAC7B;AACJ,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAACF,MAAM,EAAEI,SAAS,KAAK;EACnD,OAAO,GAAGJ,MAAM,MAAMI,SAAS,EAAE;AACrC,CAAC;;;;;;;;;;;;;;;;;AChJD;AACA;AACA;AAC+C;AACE;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,qBAAqB,GAAGA,CAAC3C,EAAE,EAAE4C,gBAAgB,EAAEC,YAAY,KAAK;EAClE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;IACQI,aAAa,KAAK3C,SAAS;IACvB;AACZ;AACA;AACA;AACA;IACYL,EAAE,CAACiD,KAAK,KAAK5C,SAAS,IACtBwC,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIH,uBAAuB,CAAC,CAAC,EAAE;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACYnD,uDAAG,CAAC,MAAM;QACNuD,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMA,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMH,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC,IAAII,aAAa,KAAK3C,SAAS,EAAE;MAC7B;IACJ;IACA,IAAI,CAAC0C,uBAAuB,CAAC,CAAC,EAAE;MAC5BC,aAAa,CAACI,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC;MAC3C;IACJ;IACA,MAAMC,KAAK,GAAGT,YAAY,CAAC,CAAC,CAACU,WAAW;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,KAAK,KAAK,CAAC,IACPN,aAAa,CAACQ,YAAY,KAAK,IAAI,IACnC9D,iDAAG,KAAKW,SAAS,IACjB,2EAA6B,EAAE;MAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIyC,iBAAiB,KAAKzC,SAAS,EAAE;QACjC;MACJ;MACA,MAAMoD,EAAE,GAAIX,iBAAiB,GAAG,IAAIY,oBAAoB,CAAEC,EAAE,IAAK;QAC7D;AAChB;AACA;AACA;QACgB,IAAIA,EAAE,CAAC,CAAC,CAAC,CAACC,iBAAiB,KAAK,CAAC,EAAE;UAC/BT,aAAa,CAAC,CAAC;UACfM,EAAE,CAAChC,UAAU,CAAC,CAAC;UACfqB,iBAAiB,GAAGzC,SAAS;QACjC;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;QAAEwD,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAE9D;MAAG,CAAC,CAAE;MAC/ByD,EAAE,CAACrC,OAAO,CAAC4B,aAAa,CAAC;MACzB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,aAAa,CAACI,KAAK,CAACW,WAAW,CAAC,OAAO,EAAE,GAAGT,KAAK,GAAG,IAAI,IAAI,CAAC;EACjE,CAAC;EACD,MAAMzB,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIiB,iBAAiB,EAAE;MACnBA,iBAAiB,CAACrB,UAAU,CAAC,CAAC;MAC9BqB,iBAAiB,GAAGzC,SAAS;IACjC;EACJ,CAAC;EACD,OAAO;IACH6C,mBAAmB;IACnBrB;EACJ,CAAC;AACL,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/input.utils-zWijNCrx.js", "./node_modules/@ionic/core/dist/esm/notch-controller-C5LPspO8.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { o as printIonError } from './index-B_U9CtaY.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n    let hostMutationObserver;\n    let slottedContentMutationObserver;\n    if (win !== undefined && 'MutationObserver' in win) {\n        const slots = Array.isArray(slotName) ? slotName : [slotName];\n        hostMutationObserver = new MutationObserver((entries) => {\n            for (const entry of entries) {\n                for (const node of entry.addedNodes) {\n                    /**\n                     * Check to see if the added node\n                     *  is our slotted content.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n                        /**\n                         * If so, we want to watch the slotted\n                         * content itself for changes. This lets us\n                         * detect when content inside of the slot changes.\n                         */\n                        mutationCallback();\n                        /**\n                         * Adding the listener in an raf\n                         * waits until Stencil moves the slotted element\n                         * into the correct place in the event that\n                         * slotted content is being added.\n                         */\n                        raf(() => watchForSlotChange(node));\n                        return;\n                    }\n                }\n            }\n        });\n        hostMutationObserver.observe(el, {\n            childList: true,\n            /**\n             * This fixes an issue with the `ion-input` and\n             * `ion-textarea` not re-rendering in some cases\n             * when using the label slot functionality.\n             *\n             * HTML element patches in Stencil that are enabled\n             * by the `experimentalSlotFixes` flag in Stencil v4\n             * result in DOM manipulations that won't trigger\n             * the current mutation observer configuration and\n             * callback.\n             */\n            subtree: true,\n        });\n    }\n    /**\n     * Listen for changes inside of the slotted content.\n     * We can listen for subtree changes here to be\n     * informed of text within the slotted content\n     * changing. Doing this on the host is possible\n     * but it is much more expensive to do because\n     * it also listens for changes to the internals\n     * of the component.\n     */\n    const watchForSlotChange = (slottedEl) => {\n        var _a;\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n        slottedContentMutationObserver = new MutationObserver((entries) => {\n            mutationCallback();\n            for (const entry of entries) {\n                for (const node of entry.removedNodes) {\n                    /**\n                     * If the element was removed then we\n                     * need to destroy the MutationObserver\n                     * so the element can be garbage collected.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n                        destroySlottedContentObserver();\n                    }\n                }\n            }\n        });\n        /**\n         * Listen for changes inside of the element\n         * as well as anything deep in the tree.\n         * We listen on the parentElement so that we can\n         * detect when slotted element itself is removed.\n         */\n        slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, { subtree: true, childList: true });\n    };\n    const destroy = () => {\n        if (hostMutationObserver) {\n            hostMutationObserver.disconnect();\n            hostMutationObserver = undefined;\n        }\n        destroySlottedContentObserver();\n    };\n    const destroySlottedContentObserver = () => {\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n    };\n    return {\n        destroy,\n    };\n};\n\nconst getCounterText = (value, maxLength, counterFormatter) => {\n    const valueLength = value == null ? 0 : value.toString().length;\n    const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n    /**\n     * If developers did not pass a custom formatter,\n     * use the default one.\n     */\n    if (counterFormatter === undefined) {\n        return defaultCounterText;\n    }\n    /**\n     * Otherwise, try to use the custom formatter\n     * and fallback to the default formatter if\n     * there was an error.\n     */\n    try {\n        return counterFormatter(valueLength, maxLength);\n    }\n    catch (e) {\n        printIonError('[ion-input] - Exception in provided `counterFormatter`:', e);\n        return defaultCounterText;\n    }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n    return `${length} / ${maxlength}`;\n};\n\nexport { createSlotMutationController as c, getCounterText as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n    let notchVisibilityIO;\n    const needsExplicitNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (\n        /**\n         * If the notch is not being used\n         * then we do not need to set the notch width.\n         */\n        notchSpacerEl === undefined ||\n            /**\n             * If either the label property is being\n             * used or the label slot is not defined,\n             * then we do not need to estimate the notch width.\n             */\n            el.label !== undefined ||\n            getLabelSlot() === null) {\n            return false;\n        }\n        return true;\n    };\n    const calculateNotchWidth = () => {\n        if (needsExplicitNotchWidth()) {\n            /**\n             * Run this the frame after\n             * the browser has re-painted the host element.\n             * Otherwise, the label element may have a width\n             * of 0 and the IntersectionObserver will be used.\n             */\n            raf(() => {\n                setNotchWidth();\n            });\n        }\n    };\n    /**\n     * When using a label prop we can render\n     * the label value inside of the notch and\n     * let the browser calculate the size of the notch.\n     * However, we cannot render the label slot in multiple\n     * places so we need to manually calculate the notch dimension\n     * based on the size of the slotted content.\n     *\n     * This function should only be used to set the notch width\n     * on slotted label content. The notch width for label prop\n     * content is automatically calculated based on the\n     * intrinsic size of the label text.\n     */\n    const setNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (notchSpacerEl === undefined) {\n            return;\n        }\n        if (!needsExplicitNotchWidth()) {\n            notchSpacerEl.style.removeProperty('width');\n            return;\n        }\n        const width = getLabelSlot().scrollWidth;\n        if (\n        /**\n         * If the computed width of the label is 0\n         * and notchSpacerEl's offsetParent is null\n         * then that means the element is hidden.\n         * As a result, we need to wait for the element\n         * to become visible before setting the notch width.\n         *\n         * We do not check el.offsetParent because\n         * that can be null if the host element has\n         * position: fixed applied to it.\n         * notchSpacerEl does not have position: fixed.\n         */\n        width === 0 &&\n            notchSpacerEl.offsetParent === null &&\n            win !== undefined &&\n            'IntersectionObserver' in win) {\n            /**\n             * If there is an IO already attached\n             * then that will update the notch\n             * once the element becomes visible.\n             * As a result, there is no need to create\n             * another one.\n             */\n            if (notchVisibilityIO !== undefined) {\n                return;\n            }\n            const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n                /**\n                 * If the element is visible then we\n                 * can try setting the notch width again.\n                 */\n                if (ev[0].intersectionRatio === 1) {\n                    setNotchWidth();\n                    io.disconnect();\n                    notchVisibilityIO = undefined;\n                }\n            }, \n            /**\n             * Set the root to be the host element\n             * This causes the IO callback\n             * to be fired in WebKit as soon as the element\n             * is visible. If we used the default root value\n             * then WebKit would only fire the IO callback\n             * after any animations (such as a modal transition)\n             * finished, and there would potentially be a flicker.\n             */\n            { threshold: 0.01, root: el }));\n            io.observe(notchSpacerEl);\n            return;\n        }\n        /**\n         * If the element is visible then we can set the notch width.\n         * The notch is only visible when the label is scaled,\n         * which is why we multiply the width by 0.75 as this is\n         * the same amount the label element is scaled by in the host CSS.\n         * (See $form-control-label-stacked-scale in ionic.globals.scss).\n         */\n        notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n    };\n    const destroy = () => {\n        if (notchVisibilityIO) {\n            notchVisibilityIO.disconnect();\n            notchVisibilityIO = undefined;\n        }\n    };\n    return {\n        calculateNotchWidth,\n        destroy,\n    };\n};\n\nexport { createNotchController as c };\n"], "names": ["w", "win", "r", "raf", "o", "printIonError", "createSlotMutationController", "el", "slotName", "mutationCallback", "hostMutationObserver", "slottedContentMutationObserver", "undefined", "slots", "Array", "isArray", "MutationObserver", "entries", "entry", "node", "addedNodes", "nodeType", "Node", "ELEMENT_NODE", "includes", "slot", "watchForSlotChange", "observe", "childList", "subtree", "slottedEl", "_a", "disconnect", "removedNodes", "destroySlottedContentObserver", "parentElement", "destroy", "getCounterText", "value", "max<PERSON><PERSON><PERSON>", "counterFormatter", "valueLength", "toString", "length", "defaultCounterText", "defaultCounterFormatter", "e", "maxlength", "c", "g", "createNotchController", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "notchSpacerEl", "label", "calculateNotchWidth", "set<PERSON><PERSON>chWidth", "style", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "ev", "intersectionRatio", "threshold", "root", "setProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}