{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-text_entry_js.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2F;AAC7B;AAE9D,MAAMS,OAAO,GAAG,gDAAgD;AAEhE,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBX,qDAAgB,CAAC,IAAI,EAAEW,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGX,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAES,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAER,qDAAkB,CAAC,IAAI,CAACS,KAAK,EAAE;QACjG,CAACH,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAEV,qDAAC,CAAC,MAAM,EAAE;MAAEW,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDL,IAAI,CAACQ,KAAK,GAAGT,OAAO", "sources": ["./node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\n\nconst Text = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '361035eae7b92dc109794348d39bad2f596eb6be', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'c7b8835cf485ba9ecd73298f0529276ce1ea0852' })));\n    }\n};\nText.style = textCss;\n\nexport { Text as ion_text };\n"], "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "c", "createColorClasses", "textCss", "Text", "constructor", "hostRef", "render", "mode", "key", "class", "color", "style", "ion_text"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}