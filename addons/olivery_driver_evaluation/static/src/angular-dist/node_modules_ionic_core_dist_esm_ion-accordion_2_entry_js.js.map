{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACiK;AACjB;AACzF;AAEvD,MAAMwB,eAAe,GAAG,qxCAAqxC;AAE7yC,MAAMC,cAAc,GAAG,wrCAAwrC;AAE/sC,MAAMC,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB5B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACE,cAAc,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC,KAAK,CAAC;IACnD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,iBAAiBC,YAAY,EAAE,EAAE;IAC9C;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAGhB,iDAAW;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACiB,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,eAAe,GAAG,MAAM;MACzB,MAAMC,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;MACYA,OAAO,CAACE,MAAM,GAAG,IAAI;MACrBF,OAAO,CAACG,MAAM,GAAG,KAAK;MACtB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIH,OAAO,CAACI,KAAK,KAAKC,SAAS,EAAE;QAC7BL,OAAO,CAACI,KAAK,GAAG,MAAM;MAC1B;IACJ,CAAC;IACD,IAAI,CAACH,uBAAuB,GAAG,MAAM;MACjC,MAAM;QAAEK;MAAS,CAAC,GAAG,IAAI;MACzB,IAAI,CAACA,QAAQ,EAAE;QACX;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMC,IAAI,GAAGD,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;MAC3C,IAAI,CAACD,IAAI,EAAE;QACP;MACJ;MACA;MACA,IAAIA,IAAI,CAACE,gBAAgB,KAAKJ,SAAS,EACnC;MACJ,OAAOE,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,OAAO,KAAK,UAAU,CAAC;IAC1E,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAACC,QAAQ,GAAG,KAAK,KAAK;MACjC,MAAMd,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMe,IAAI,GAAGzC,uDAAc,CAAC0B,OAAO,CAAC;MACpC,MAAME,MAAM,GAAGa,IAAI,CAACP,aAAa,CAAC,QAAQ,CAAC;MAC3C,IAAI,CAACN,MAAM,EAAE;QACT;MACJ;MACAA,MAAM,CAACc,YAAY,CAAC,eAAe,EAAE,GAAGF,QAAQ,EAAE,CAAC;IACvD,CAAC;IACD,IAAI,CAACG,cAAc,GAAG,MAAM;MACxB,MAAMjB,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA,MAAM;QAAEF,cAAc;QAAED;MAAW,CAAC,GAAG,IAAI;MAC3C;AACZ;AACA;AACA;MACY,MAAMqB,kBAAkB,GAAGlB,OAAO,CAACQ,aAAa,CAAC,4BAA4B,CAAC;MAC9E,IAAIU,kBAAkB,EAAE;QACpB;MACJ;MACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACjDF,MAAM,CAACZ,IAAI,GAAGT,cAAc;MAC5BqB,MAAM,CAACG,IAAI,GAAG,KAAK;MACnBH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACjDL,MAAM,CAACM,IAAI,GAAG5B,UAAU;MACxBsB,MAAM,CAACH,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC1ChB,OAAO,CAAC0B,WAAW,CAACP,MAAM,CAAC;IAC/B,CAAC;IACD,IAAI,CAACQ,eAAe,GAAG,CAACC,aAAa,GAAG,KAAK,KAAK;MAC9C,MAAM;QAAEC,SAAS;QAAEC;MAAiB,CAAC,GAAG,IAAI;MAC5C,IAAIF,aAAa,IAAIC,SAAS,KAAKxB,SAAS,IAAIyB,gBAAgB,KAAKzB,SAAS,EAAE;QAC5E,IAAI,CAACf,KAAK,GAAG,CAAC,CAAC;QACf;MACJ;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,+BAA+B;QAChD;MACJ;MACA,IAAI,IAAI,CAACyC,UAAU,KAAK1B,SAAS,EAAE;QAC/B2B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACzC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACtB1D,uDAAG,CAAC,MAAM;UACN,IAAI,CAACe,KAAK,GAAG,CAAC,CAAC;UACf,IAAI,CAACyC,UAAU,GAAGxD,uDAAG,cAAA2D,yMAAA,CAAC,aAAY;YAC9B,MAAMC,aAAa,GAAGL,gBAAgB,CAACM,YAAY;YACnD,MAAMC,iBAAiB,GAAGzD,uDAAkB,CAACiD,SAAS,EAAE,IAAI,CAAC;YAC7DA,SAAS,CAACS,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,GAAGJ,aAAa,IAAI,CAAC;YAC/D,MAAME,iBAAiB;YACvBlD,KAAI,CAACG,KAAK,GAAG,CAAC,CAAC;YACfuC,SAAS,CAACS,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAChD,CAAC,EAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAAClD,KAAK,GAAG,CAAC,CAAC;MACnB;IACJ,CAAC;IACD,IAAI,CAACmD,iBAAiB,GAAG,CAACb,aAAa,GAAG,KAAK,KAAK;MAChD,MAAM;QAAEC;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAID,aAAa,IAAIC,SAAS,KAAKxB,SAAS,EAAE;QAC1C,IAAI,CAACf,KAAK,GAAG,CAAC,CAAC;QACf;MACJ;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,gCAAgC;QACjD;MACJ;MACA,IAAI,IAAI,CAACyC,UAAU,KAAK1B,SAAS,EAAE;QAC/B2B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACzC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACtB,IAAI,CAACF,UAAU,GAAGxD,uDAAG,cAAA2D,yMAAA,CAAC,aAAY;UAC9B,MAAMC,aAAa,GAAGN,SAAS,CAACO,YAAY;UAC5CP,SAAS,CAACS,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,GAAGJ,aAAa,IAAI,CAAC;UAC/D5D,uDAAG,cAAA2D,yMAAA,CAAC,aAAY;YACZ,MAAMG,iBAAiB,GAAGzD,uDAAkB,CAACiD,SAAS,EAAE,IAAI,CAAC;YAC7D1C,KAAI,CAACG,KAAK,GAAG,CAAC,CAAC;YACf,MAAM+C,iBAAiB;YACvBlD,KAAI,CAACG,KAAK,GAAG,CAAC,CAAC;YACfuC,SAAS,CAACS,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAChD,CAAC,EAAC;QACN,CAAC,EAAC;MACN,CAAC,MACI;QACD,IAAI,CAAClD,KAAK,GAAG,CAAC,CAAC;MACnB;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC2C,aAAa,GAAG,MAAM;MACvB,IAAI,OAAOS,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,KAAK;MAChB;MACA,MAAMC,oBAAoB,GAAGC,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO;MACnF,IAAIF,oBAAoB,EAAE;QACtB,OAAO,KAAK;MAChB;MACA,MAAMG,QAAQ,GAAGrF,iDAAM,CAACsF,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;MAC7C,IAAI,CAACD,QAAQ,EAAE;QACX,OAAO,KAAK;MAChB;MACA,IAAI,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAACF,QAAQ,EAAE;QAC1D,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC;IACD,IAAI,CAACzD,WAAW,gBAAA6C,yMAAA,CAAG,WAAON,aAAa,GAAG,KAAK,EAAK;MAChD,MAAMqB,cAAc,GAAG9D,KAAI,CAAC6D,gBAAgB;MAC5C,MAAME,cAAc,GAAG/D,KAAI,CAACM,KAAK;MACjC,IAAI,CAACwD,cAAc,EAAE;QACjB;MACJ;MACA,MAAMxD,KAAK,GAAGwD,cAAc,CAACxD,KAAK;MAClC,MAAM0D,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAC5D,KAAK,CAAC,GAAGA,KAAK,CAAC6D,QAAQ,CAACJ,cAAc,CAAC,GAAGzD,KAAK,KAAKyD,cAAc;MACrG,IAAIC,YAAY,EAAE;QACdhE,KAAI,CAACwC,eAAe,CAACC,aAAa,CAAC;QACnCzC,KAAI,CAACI,MAAM,GAAGJ,KAAI,CAACK,UAAU,GAAG,KAAK;MACzC,CAAC,MACI;QACDL,KAAI,CAACsD,iBAAiB,CAACb,aAAa,CAAC;QACrC;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAM2B,aAAa,GAAGpE,KAAI,CAACqE,cAAc,CAAC,CAAC;QAC3C,MAAMC,kBAAkB,GAAGF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC9D,KAAK;QAC5G,IAAIgE,kBAAkB,KAAKpD,SAAS,EAAE;UAClClB,KAAI,CAACK,UAAU,GAAG4D,KAAK,CAACC,OAAO,CAAC5D,KAAK,CAAC,GAAGA,KAAK,CAAC6D,QAAQ,CAACG,kBAAkB,CAAC,GAAGhE,KAAK,KAAKgE,kBAAkB;QAC9G;QACA,MAAMC,iBAAiB,GAAGvE,KAAI,CAACwE,kBAAkB,CAAC,CAAC;QACnD,MAAMC,sBAAsB,GAAGF,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACjE,KAAK;QAC5H,IAAImE,sBAAsB,KAAKvD,SAAS,EAAE;UACtClB,KAAI,CAACI,MAAM,GAAG6D,KAAK,CAACC,OAAO,CAAC5D,KAAK,CAAC,GAAGA,KAAK,CAAC6D,QAAQ,CAACM,sBAAsB,CAAC,GAAGnE,KAAK,KAAKmE,sBAAsB;QAClH;MACJ;IACJ,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MACxB,IAAI,CAAC,IAAI,CAAC7C,EAAE,EAAE;QACV;MACJ;MACA,MAAMkD,WAAW,GAAG,IAAI,CAAClD,EAAE,CAACmD,kBAAkB;MAC9C,IAAI,CAACD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACjD,OAAO,MAAM,eAAe,EAAE;QACrG;MACJ;MACA,OAAOiD,WAAW;IACtB,CAAC;IACD,IAAI,CAACF,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAAC,IAAI,CAAChD,EAAE,EAAE;QACV;MACJ;MACA,MAAMoD,eAAe,GAAG,IAAI,CAACpD,EAAE,CAACqD,sBAAsB;MACtD,IAAI,CAACD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACnD,OAAO,MAAM,eAAe,EAAE;QACjH;MACJ;MACA,OAAOmD,eAAe;IAC1B,CAAC;EACL;EACAE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC5E,WAAW,CAAC,CAAC;EACtB;EACA6E,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,MAAMnB,gBAAgB,GAAI,IAAI,CAACA,gBAAgB,GAAG,CAACmB,EAAE,GAAG,IAAI,CAACxD,EAAE,MAAM,IAAI,IAAIwD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,qBAAqB,CAAE;IACxI,IAAIpB,gBAAgB,EAAE;MAClB,IAAI,CAAC3D,WAAW,CAAC,IAAI,CAAC;MACtBZ,uDAAgB,CAACuE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC5D,cAAc,CAAC;IAC7E;EACJ;EACAiF,oBAAoBA,CAAA,EAAG;IACnB,MAAMrB,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIA,gBAAgB,EAAE;MAClBtE,uDAAmB,CAACsE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC5D,cAAc,CAAC;IAChF;EACJ;EACAkF,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACvE,eAAe,CAAC,CAAC;IACtB,IAAI,CAACkB,cAAc,CAAC,CAAC;IACrB;AACR;AACA;AACA;AACA;IACQ1C,uDAAG,CAAC,MAAM;MACN;AACZ;AACA;AACA;MACY,MAAMuC,QAAQ,GAAG,IAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;MACpF,IAAI,CAACuB,OAAO,CAACC,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACN;EACAyD,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEvB,gBAAgB;MAAErD,QAAQ;MAAEC,QAAQ;MAAEH,KAAK;MAAEH;IAAM,CAAC,GAAG,IAAI;IACnE,IAAIK,QAAQ,IAAIC,QAAQ,EACpB;IACJ,IAAIoD,gBAAgB,EAAE;MAClB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMwB,MAAM,GAAGlF,KAAK,KAAK,CAAC,CAAC,kCAAkCA,KAAK,KAAK,CAAC,CAAC;MACzE0D,gBAAgB,CAACyB,sBAAsB,CAAChF,KAAK,EAAE+E,MAAM,CAAC;IAC1D;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE/E,QAAQ;MAAEC;IAAS,CAAC,GAAG,IAAI;IACnC,MAAM+E,IAAI,GAAGhH,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmD,QAAQ,GAAG,IAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;IACpF,MAAMsF,UAAU,GAAG9D,QAAQ,GAAG,iBAAiB,GAAG,QAAQ;IAC1D,MAAM+D,WAAW,GAAG/D,QAAQ,GAAG,kBAAkB,GAAG,SAAS;IAC7D,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC;IACtB,OAAQlD,qDAAC,CAACE,iDAAI,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACrF,KAAK,KAAK,CAAC,CAAC;QACxC,oBAAoB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACvC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACzC,qBAAqB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACxC,gBAAgB,EAAE,IAAI,CAACC,MAAM;QAC7B,oBAAoB,EAAE,IAAI,CAACC,UAAU;QACrC,oBAAoB,EAAEG,QAAQ;QAC9B,oBAAoB,EAAEC,QAAQ;QAC9B,oBAAoB,EAAE,IAAI,CAACqC,aAAa,CAAC;MAC7C;IAAE,CAAC,EAAErE,qDAAC,CAAC,KAAK,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEE,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACT,cAAc,CAAC,CAAC;MAAEU,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAEN,UAAU;MAAE,eAAe,EAAE,SAAS;MAAEO,GAAG,EAAG7E,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA;IAAU,CAAC,EAAE1C,qDAAC,CAAC,MAAM,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC,EAAExH,qDAAC,CAAC,KAAK,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEG,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAEL,WAAW;MAAEQ,IAAI,EAAE,QAAQ;MAAE,iBAAiB,EAAE,QAAQ;MAAEF,GAAG,EAAGtD,SAAS,IAAM,IAAI,CAACA,SAAS,GAAGA;IAAW,CAAC,EAAEjE,qDAAC,CAAC,KAAK,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEG,EAAE,EAAE,iBAAiB;MAAEE,GAAG,EAAGrD,gBAAgB,IAAM,IAAI,CAACA,gBAAgB,GAAGA;IAAkB,CAAC,EAAElE,qDAAC,CAAC,MAAM,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1tB;EACA,WAAWE,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE;EAC3C,IAAI3E,EAAEA,CAAA,EAAG;IAAE,OAAO3C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAI7F,YAAY,GAAG,CAAC;AACpBV,SAAS,CAACsD,KAAK,GAAG;EACdkD,GAAG,EAAE1G,eAAe;EACpB2G,EAAE,EAAE1G;AACR,CAAC;AAED,MAAM2G,oBAAoB,GAAG,sXAAsX;AAEnZ,MAAMC,mBAAmB,GAAG,ioCAAioC;AAE7pC,MAAMC,cAAc,GAAG,MAAM;EACzB3G,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAAC2G,SAAS,GAAG3H,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC4H,cAAc,GAAG5H,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4E,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACnD,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4E,MAAM,GAAG,SAAS;EAC3B;EACAP,YAAYA,CAAA,EAAG;IACX,MAAM;MAAExE,KAAK;MAAEsG;IAAS,CAAC,GAAG,IAAI;IAChC,IAAI,CAACA,QAAQ,IAAI3C,KAAK,CAACC,OAAO,CAAC5D,KAAK,CAAC,EAAE;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYrB,qDAAe,CAAC;AAC5B;AACA,mBAAmBqB,KAAK,CAACuG,GAAG,CAAEC,CAAC,IAAK,IAAIA,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACxD,CAAC,EAAE,IAAI,CAACvF,EAAE,CAAC;IACH;IACA;AACR;AACA;AACA;IACQ,IAAI,CAACmF,cAAc,CAACK,IAAI,CAAC;MAAE1G,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EACnD;EACM2G,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnE,yMAAA;MACpB,MAAM;QAAEvC;MAAS,CAAC,GAAG0G,MAAI;MACzB,MAAMC,UAAU,SAASD,MAAI,CAACE,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAChCE,SAAS,CAAC7G,QAAQ,GAAGA,QAAQ;MACjC;IAAC;EACL;EACM8G,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxE,yMAAA;MACpB,MAAM;QAAEtC;MAAS,CAAC,GAAG8G,MAAI;MACzB,MAAMJ,UAAU,SAASI,MAAI,CAACH,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAChCE,SAAS,CAAC5G,QAAQ,GAAGA,QAAQ;MACjC;IAAC;EACL;EACM+G,SAASA,CAACC,EAAE,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3E,yMAAA;MAChB,MAAM4E,aAAa,GAAG1F,QAAQ,CAAC0F,aAAa;MAC5C,IAAI,CAACA,aAAa,EAAE;QAChB;MACJ;MACA;AACR;AACA;AACA;AACA;AACA;MACQ,MAAMC,qBAAqB,GAAGD,aAAa,CAAC1C,OAAO,CAAC,+BAA+B,CAAC;MACpF,IAAI,CAAC2C,qBAAqB,EAAE;QACxB;MACJ;MACA,MAAMC,WAAW,GAAGF,aAAa,CAAClG,OAAO,KAAK,eAAe,GAAGkG,aAAa,GAAGA,aAAa,CAAC1C,OAAO,CAAC,eAAe,CAAC;MACtH,IAAI,CAAC4C,WAAW,EAAE;QACd;MACJ;MACA,MAAMC,YAAY,GAAGD,WAAW,CAAC5C,OAAO,CAAC,qBAAqB,CAAC;MAC/D,IAAI6C,YAAY,KAAKJ,MAAI,CAAClG,EAAE,EAAE;QAC1B;MACJ;MACA;MACA,MAAM2F,UAAU,SAASO,MAAI,CAACN,aAAa,CAAC,CAAC;MAC7C,MAAMW,aAAa,GAAGZ,UAAU,CAACa,SAAS,CAAEC,CAAC,IAAKA,CAAC,KAAKJ,WAAW,CAAC;MACpE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB;MACJ;MACA,IAAIV,SAAS;MACb,IAAII,EAAE,CAAC9B,GAAG,KAAK,WAAW,EAAE;QACxB0B,SAAS,GAAGK,MAAI,CAACQ,iBAAiB,CAACf,UAAU,EAAEY,aAAa,CAAC;MACjE,CAAC,MACI,IAAIN,EAAE,CAAC9B,GAAG,KAAK,SAAS,EAAE;QAC3B0B,SAAS,GAAGK,MAAI,CAACS,qBAAqB,CAAChB,UAAU,EAAEY,aAAa,CAAC;MACrE,CAAC,MACI,IAAIN,EAAE,CAAC9B,GAAG,KAAK,MAAM,EAAE;QACxB0B,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC,MACI,IAAIM,EAAE,CAAC9B,GAAG,KAAK,KAAK,EAAE;QACvB0B,SAAS,GAAGF,UAAU,CAACA,UAAU,CAACiB,MAAM,GAAG,CAAC,CAAC;MACjD;MACA,IAAIf,SAAS,KAAKnG,SAAS,IAAImG,SAAS,KAAKM,aAAa,EAAE;QACxDN,SAAS,CAACgB,KAAK,CAAC,CAAC;MACrB;IAAC;EACL;EACMlD,gBAAgBA,CAAA,EAAG;IAAA,IAAAmD,MAAA;IAAA,OAAAvF,yMAAA;MACrB,IAAIuF,MAAI,CAAC9H,QAAQ,EAAE;QACf8H,MAAI,CAACrB,eAAe,CAAC,CAAC;MAC1B;MACA,IAAIqB,MAAI,CAAC7H,QAAQ,EAAE;QACf6H,MAAI,CAAChB,eAAe,CAAC,CAAC;MAC1B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQgB,MAAI,CAACxD,YAAY,CAAC,CAAC;IAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIyD,QAAQA,CAACxE,cAAc,EAAE;IACrB,MAAMzD,KAAK,GAAI,IAAI,CAACA,KAAK,GAAGyD,cAAe;IAC3C,IAAI,CAAC2C,SAAS,CAACM,IAAI,CAAC;MAAE1G;IAAM,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUgF,sBAAsBA,CAACvB,cAAc,EAAEyE,eAAe,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA1F,yMAAA;MAC1D,MAAM;QAAE6D,QAAQ;QAAEtG,KAAK;QAAEG,QAAQ;QAAED;MAAS,CAAC,GAAGiI,MAAI;MACpD,IAAIhI,QAAQ,IAAID,QAAQ,EAAE;QACtB;MACJ;MACA,IAAIgI,eAAe,EAAE;QACjB;AACZ;AACA;AACA;AACA;AACA;QACY,IAAI5B,QAAQ,EAAE;UACV,MAAM8B,UAAU,GAAGpI,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMqI,cAAc,GAAG1E,KAAK,CAACC,OAAO,CAACwE,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5E,MAAME,WAAW,GAAGD,cAAc,CAACpH,IAAI,CAAEuF,CAAC,IAAKA,CAAC,KAAK/C,cAAc,CAAC;UACpE,IAAI6E,WAAW,KAAK1H,SAAS,IAAI6C,cAAc,KAAK7C,SAAS,EAAE;YAC3DuH,MAAI,CAACF,QAAQ,CAAC,CAAC,GAAGI,cAAc,EAAE5E,cAAc,CAAC,CAAC;UACtD;QACJ,CAAC,MACI;UACD0E,MAAI,CAACF,QAAQ,CAACxE,cAAc,CAAC;QACjC;MACJ,CAAC,MACI;QACD;AACZ;AACA;AACA;QACY,IAAI6C,QAAQ,EAAE;UACV,MAAM8B,UAAU,GAAGpI,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMqI,cAAc,GAAG1E,KAAK,CAACC,OAAO,CAACwE,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5ED,MAAI,CAACF,QAAQ,CAACI,cAAc,CAACE,MAAM,CAAE/B,CAAC,IAAKA,CAAC,KAAK/C,cAAc,CAAC,CAAC;QACrE,CAAC,MACI;UACD0E,MAAI,CAACF,QAAQ,CAACrH,SAAS,CAAC;QAC5B;MACJ;IAAC;EACL;EACAgH,iBAAiBA,CAACf,UAAU,EAAEY,aAAa,EAAE;IACzC,MAAM3D,aAAa,GAAG+C,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAI3D,aAAa,KAAKlD,SAAS,EAAE;MAC7B,OAAOiG,UAAU,CAAC,CAAC,CAAC;IACxB;IACA,OAAO/C,aAAa;EACxB;EACA+D,qBAAqBA,CAAChB,UAAU,EAAEY,aAAa,EAAE;IAC7C,MAAMe,aAAa,GAAG3B,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAIe,aAAa,KAAK5H,SAAS,EAAE;MAC7B,OAAOiG,UAAU,CAACA,UAAU,CAACiB,MAAM,GAAG,CAAC,CAAC;IAC5C;IACA,OAAOU,aAAa;EACxB;EACA;AACJ;AACA;EACU1B,aAAaA,CAAA,EAAG;IAAA,IAAA2B,MAAA;IAAA,OAAAhG,yMAAA;MAClB,OAAOkB,KAAK,CAAC+E,IAAI,CAACD,MAAI,CAACvH,EAAE,CAACyH,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;IAAC;EAC1E;EACA1D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE/E,QAAQ;MAAEC,QAAQ;MAAE4E;IAAO,CAAC,GAAG,IAAI;IAC3C,MAAMG,IAAI,GAAGhH,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ,0BAA0B,EAAEhF,QAAQ;QACpC,0BAA0B,EAAEC,QAAQ;QACpC,CAAC,0BAA0B4E,MAAM,EAAE,GAAG;MAC1C,CAAC;MAAEa,IAAI,EAAE;IAAe,CAAC,EAAEzH,qDAAC,CAAC,MAAM,EAAE;MAAEkH,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAClG;EACA,IAAInE,EAAEA,CAAA,EAAG;IAAE,OAAO3C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDK,cAAc,CAACtD,KAAK,GAAG;EACnBkD,GAAG,EAAEE,oBAAoB;EACzBD,EAAE,EAAEE;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-accordion_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host, k as getElement, d as createEvent, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { g as getElementRoot, r as raf, f as addEventListener, m as removeEventListener, t as transitionEndAsync } from './helpers-1O4D2b7y.js';\nimport { l as chevronDown } from './index-BLV6ykCk.js';\n\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\n\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\n\nconst Accordion = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.updateListener = () => this.updateState(false);\n        this.state = 1 /* AccordionState.Collapsed */;\n        this.isNext = false;\n        this.isPrevious = false;\n        /**\n         * The value of the accordion. Defaults to an autogenerated\n         * value.\n         */\n        this.value = `ion-accordion-${accordionIds++}`;\n        /**\n         * If `true`, the accordion cannot be interacted with.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the accordion cannot be interacted with,\n         * but does not alter the opacity.\n         */\n        this.readonly = false;\n        /**\n         * The toggle icon to use. This icon will be\n         * rotated when the accordion is expanded\n         * or collapsed.\n         */\n        this.toggleIcon = chevronDown;\n        /**\n         * The slot inside of `ion-item` to\n         * place the toggle icon. Defaults to `\"end\"`.\n         */\n        this.toggleIconSlot = 'end';\n        this.setItemDefaults = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * For a11y purposes, we make\n             * the ion-item a button so users\n             * can tab to it and use keyboard\n             * navigation to get around.\n             */\n            ionItem.button = true;\n            ionItem.detail = false;\n            /**\n             * By default, the lines in an\n             * item should be full here, but\n             * only do that if a user has\n             * not explicitly overridden them\n             */\n            if (ionItem.lines === undefined) {\n                ionItem.lines = 'full';\n            }\n        };\n        this.getSlottedHeaderIonItem = () => {\n            const { headerEl } = this;\n            if (!headerEl) {\n                return;\n            }\n            /**\n             * Get the first ion-item\n             * slotted in the header slot\n             */\n            const slot = headerEl.querySelector('slot');\n            if (!slot) {\n                return;\n            }\n            // This is not defined in unit tests\n            if (slot.assignedElements === undefined)\n                return;\n            return slot.assignedElements().find((el) => el.tagName === 'ION-ITEM');\n        };\n        this.setAria = (expanded = false) => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * Get the native <button> element inside of\n             * ion-item because that is what will be focused\n             */\n            const root = getElementRoot(ionItem);\n            const button = root.querySelector('button');\n            if (!button) {\n                return;\n            }\n            button.setAttribute('aria-expanded', `${expanded}`);\n        };\n        this.slotToggleIcon = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            const { toggleIconSlot, toggleIcon } = this;\n            /**\n             * Check if there already is a toggle icon.\n             * If so, do not add another one.\n             */\n            const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n            if (existingToggleIcon) {\n                return;\n            }\n            const iconEl = document.createElement('ion-icon');\n            iconEl.slot = toggleIconSlot;\n            iconEl.lazy = false;\n            iconEl.classList.add('ion-accordion-toggle-icon');\n            iconEl.icon = toggleIcon;\n            iconEl.setAttribute('aria-hidden', 'true');\n            ionItem.appendChild(iconEl);\n        };\n        this.expandAccordion = (initialUpdate = false) => {\n            const { contentEl, contentElWrapper } = this;\n            if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n                this.state = 4 /* AccordionState.Expanded */;\n                return;\n            }\n            if (this.state === 4 /* AccordionState.Expanded */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                raf(() => {\n                    this.state = 8 /* AccordionState.Expanding */;\n                    this.currentRaf = raf(async () => {\n                        const contentHeight = contentElWrapper.offsetHeight;\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                        await waitForTransition;\n                        this.state = 4 /* AccordionState.Expanded */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 4 /* AccordionState.Expanded */;\n            }\n        };\n        this.collapseAccordion = (initialUpdate = false) => {\n            const { contentEl } = this;\n            if (initialUpdate || contentEl === undefined) {\n                this.state = 1 /* AccordionState.Collapsed */;\n                return;\n            }\n            if (this.state === 1 /* AccordionState.Collapsed */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                this.currentRaf = raf(async () => {\n                    const contentHeight = contentEl.offsetHeight;\n                    contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                    raf(async () => {\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        this.state = 2 /* AccordionState.Collapsing */;\n                        await waitForTransition;\n                        this.state = 1 /* AccordionState.Collapsed */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 1 /* AccordionState.Collapsed */;\n            }\n        };\n        /**\n         * Helper function to determine if\n         * something should animate.\n         * If prefers-reduced-motion is set\n         * then we should not animate, regardless\n         * of what is set in the config.\n         */\n        this.shouldAnimate = () => {\n            if (typeof window === 'undefined') {\n                return false;\n            }\n            const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n            if (prefersReducedMotion) {\n                return false;\n            }\n            const animated = config.get('animated', true);\n            if (!animated) {\n                return false;\n            }\n            if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n                return false;\n            }\n            return true;\n        };\n        this.updateState = async (initialUpdate = false) => {\n            const accordionGroup = this.accordionGroupEl;\n            const accordionValue = this.value;\n            if (!accordionGroup) {\n                return;\n            }\n            const value = accordionGroup.value;\n            const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n            if (shouldExpand) {\n                this.expandAccordion(initialUpdate);\n                this.isNext = this.isPrevious = false;\n            }\n            else {\n                this.collapseAccordion(initialUpdate);\n                /**\n                 * When using popout or inset,\n                 * the collapsed accordion items\n                 * may need additional border radius\n                 * applied. Check to see if the\n                 * next or previous accordion is selected.\n                 */\n                const nextAccordion = this.getNextSibling();\n                const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n                if (nextAccordionValue !== undefined) {\n                    this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n                }\n                const previousAccordion = this.getPreviousSibling();\n                const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n                if (previousAccordionValue !== undefined) {\n                    this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n                }\n            }\n        };\n        this.getNextSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const nextSibling = this.el.nextElementSibling;\n            if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return nextSibling;\n        };\n        this.getPreviousSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const previousSibling = this.el.previousElementSibling;\n            if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return previousSibling;\n        };\n    }\n    valueChanged() {\n        this.updateState();\n    }\n    connectedCallback() {\n        var _a;\n        const accordionGroupEl = (this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group'));\n        if (accordionGroupEl) {\n            this.updateState(true);\n            addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    disconnectedCallback() {\n        const accordionGroupEl = this.accordionGroupEl;\n        if (accordionGroupEl) {\n            removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    componentDidLoad() {\n        this.setItemDefaults();\n        this.slotToggleIcon();\n        /**\n         * We need to wait a tick because we\n         * just set ionItem.button = true and\n         * the button has not have been rendered yet.\n         */\n        raf(() => {\n            /**\n             * Set aria label on button inside of ion-item\n             * once the inner content has been rendered.\n             */\n            const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n            this.setAria(expanded);\n        });\n    }\n    toggleExpanded() {\n        const { accordionGroupEl, disabled, readonly, value, state } = this;\n        if (disabled || readonly)\n            return;\n        if (accordionGroupEl) {\n            /**\n             * Because the accordion group may or may\n             * not allow multiple accordions open, we\n             * need to request the toggling of this\n             * accordion and the accordion group will\n             * make the decision on whether or not\n             * to allow it.\n             */\n            const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n            accordionGroupEl.requestAccordionToggle(value, expand);\n        }\n    }\n    render() {\n        const { disabled, readonly } = this;\n        const mode = getIonMode(this);\n        const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n        const headerPart = expanded ? 'header expanded' : 'header';\n        const contentPart = expanded ? 'content expanded' : 'content';\n        this.setAria(expanded);\n        return (h(Host, { key: '073e1d02c18dcbc20c68648426e87c14750c031d', class: {\n                [mode]: true,\n                'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n                'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n                'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n                'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n                'accordion-next': this.isNext,\n                'accordion-previous': this.isPrevious,\n                'accordion-disabled': disabled,\n                'accordion-readonly': readonly,\n                'accordion-animated': this.shouldAnimate(),\n            } }, h(\"div\", { key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc', onClick: () => this.toggleExpanded(), id: \"header\", part: headerPart, \"aria-controls\": \"content\", ref: (headerEl) => (this.headerEl = headerEl) }, h(\"slot\", { key: '464c32a37f64655eacf4218284214f5f30b14a1e', name: \"header\" })), h(\"div\", { key: '8bb52e6a62d7de0106b253201a89a32e79d9a594', id: \"content\", part: contentPart, role: \"region\", \"aria-labelledby\": \"header\", ref: (contentEl) => (this.contentEl = contentEl) }, h(\"div\", { key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0', id: \"content-wrapper\", ref: (contentElWrapper) => (this.contentElWrapper = contentElWrapper) }, h(\"slot\", { key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479', name: \"content\" })))));\n    }\n    static get delegatesFocus() { return true; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet accordionIds = 0;\nAccordion.style = {\n    ios: accordionIosCss,\n    md: accordionMdCss\n};\n\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\n\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst AccordionGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        /**\n         * If `true`, all accordions inside of the\n         * accordion group will animate when expanding\n         * or collapsing.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the accordion group cannot be interacted with.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the accordion group cannot be interacted with,\n         * but does not alter the opacity.\n         */\n        this.readonly = false;\n        /**\n         * Describes the expansion behavior for each accordion.\n         * Possible values are `\"compact\"` and `\"inset\"`.\n         * Defaults to `\"compact\"`.\n         */\n        this.expand = 'compact';\n    }\n    valueChanged() {\n        const { value, multiple } = this;\n        if (!multiple && Array.isArray(value)) {\n            /**\n             * We do some processing on the `value` array so\n             * that it looks more like an array when logged to\n             * the console.\n             * Example given ['a', 'b']\n             * Default toString() behavior: a,b\n             * Custom behavior: ['a', 'b']\n             */\n            printIonWarning(`[ion-accordion-group] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n        }\n        /**\n         * Do not use `value` here as that will be\n         * not account for the adjustment we make above.\n         */\n        this.ionValueChange.emit({ value: this.value });\n    }\n    async disabledChanged() {\n        const { disabled } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.disabled = disabled;\n        }\n    }\n    async readonlyChanged() {\n        const { readonly } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.readonly = readonly;\n        }\n    }\n    async onKeydown(ev) {\n        const activeElement = document.activeElement;\n        if (!activeElement) {\n            return;\n        }\n        /**\n         * Make sure focus is in the header, not the body, of the accordion. This ensures\n         * that if there are any interactable elements in the body, their keyboard\n         * interaction doesn't get stolen by the accordion. Example: using up/down keys\n         * in ion-textarea.\n         */\n        const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n        if (!activeAccordionHeader) {\n            return;\n        }\n        const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n        if (!accordionEl) {\n            return;\n        }\n        const closestGroup = accordionEl.closest('ion-accordion-group');\n        if (closestGroup !== this.el) {\n            return;\n        }\n        // If the active accordion is not in the current array of accordions, do not do anything\n        const accordions = await this.getAccordions();\n        const startingIndex = accordions.findIndex((a) => a === accordionEl);\n        if (startingIndex === -1) {\n            return;\n        }\n        let accordion;\n        if (ev.key === 'ArrowDown') {\n            accordion = this.findNextAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'ArrowUp') {\n            accordion = this.findPreviousAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'Home') {\n            accordion = accordions[0];\n        }\n        else if (ev.key === 'End') {\n            accordion = accordions[accordions.length - 1];\n        }\n        if (accordion !== undefined && accordion !== activeElement) {\n            accordion.focus();\n        }\n    }\n    async componentDidLoad() {\n        if (this.disabled) {\n            this.disabledChanged();\n        }\n        if (this.readonly) {\n            this.readonlyChanged();\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.valueChanged();\n    }\n    /**\n     * Sets the value property and emits ionChange.\n     * This should only be called when the user interacts\n     * with the accordion and not for any update\n     * to the value property. The exception is when\n     * the app sets the value of a single-select\n     * accordion group to an array.\n     */\n    setValue(accordionValue) {\n        const value = (this.value = accordionValue);\n        this.ionChange.emit({ value });\n    }\n    /**\n     * This method is used to ensure that the value\n     * of ion-accordion-group is being set in a valid\n     * way. This method should only be called in\n     * response to a user generated action.\n     * @internal\n     */\n    async requestAccordionToggle(accordionValue, accordionExpand) {\n        const { multiple, value, readonly, disabled } = this;\n        if (readonly || disabled) {\n            return;\n        }\n        if (accordionExpand) {\n            /**\n             * If group accepts multiple values\n             * check to see if value is already in\n             * in values array. If not, add it\n             * to the array.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                const valueExists = processedValue.find((v) => v === accordionValue);\n                if (valueExists === undefined && accordionValue !== undefined) {\n                    this.setValue([...processedValue, accordionValue]);\n                }\n            }\n            else {\n                this.setValue(accordionValue);\n            }\n        }\n        else {\n            /**\n             * If collapsing accordion, either filter the value\n             * out of the values array or unset the value.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                this.setValue(processedValue.filter((v) => v !== accordionValue));\n            }\n            else {\n                this.setValue(undefined);\n            }\n        }\n    }\n    findNextAccordion(accordions, startingIndex) {\n        const nextAccordion = accordions[startingIndex + 1];\n        if (nextAccordion === undefined) {\n            return accordions[0];\n        }\n        return nextAccordion;\n    }\n    findPreviousAccordion(accordions, startingIndex) {\n        const prevAccordion = accordions[startingIndex - 1];\n        if (prevAccordion === undefined) {\n            return accordions[accordions.length - 1];\n        }\n        return prevAccordion;\n    }\n    /**\n     * @internal\n     */\n    async getAccordions() {\n        return Array.from(this.el.querySelectorAll(':scope > ion-accordion'));\n    }\n    render() {\n        const { disabled, readonly, expand } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd1a79a93179474fbba66fcf11a92f4871dacc975', class: {\n                [mode]: true,\n                'accordion-group-disabled': disabled,\n                'accordion-group-readonly': readonly,\n                [`accordion-group-expand-${expand}`]: true,\n            }, role: \"presentation\" }, h(\"slot\", { key: 'e6b8954b686d1fbb4fc92adb07fddc97a24b0a31' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"readonly\": [\"readonlyChanged\"]\n    }; }\n};\nAccordionGroup.style = {\n    ios: accordionGroupIosCss,\n    md: accordionGroupMdCss\n};\n\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };\n"], "names": ["r", "registerInstance", "l", "config", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "d", "createEvent", "m", "printIonWarning", "g", "getElementRoot", "raf", "f", "addEventListener", "removeEventListener", "t", "transitionEndAsync", "chevronDown", "accordionIosCss", "accordionMdCss", "Accordion", "constructor", "hostRef", "_this", "updateListener", "updateState", "state", "isNext", "isPrevious", "value", "accordionIds", "disabled", "readonly", "toggleIcon", "toggleIconSlot", "setItemDefaults", "ionItem", "getSlottedHeaderIonItem", "button", "detail", "lines", "undefined", "headerEl", "slot", "querySelector", "assignedElements", "find", "el", "tagName", "setAria", "expanded", "root", "setAttribute", "slotToggleIcon", "existingToggleIcon", "iconEl", "document", "createElement", "lazy", "classList", "add", "icon", "append<PERSON><PERSON><PERSON>", "expandAccordion", "initialUpdate", "contentEl", "contentElWrapper", "currentRaf", "cancelAnimationFrame", "shouldAnimate", "_asyncToGenerator", "contentHeight", "offsetHeight", "waitForTransition", "style", "setProperty", "removeProperty", "collapseAccordion", "window", "prefersReducedMotion", "matchMedia", "matches", "animated", "get", "accordionGroupEl", "accordionGroup", "accordionValue", "shouldExpand", "Array", "isArray", "includes", "nextAccordion", "getNextSibling", "nextAccordionValue", "previousAccordion", "getPrevious<PERSON><PERSON>ling", "previousAccordionValue", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "previousSibling", "previousElementSibling", "valueChanged", "connectedCallback", "_a", "closest", "disconnectedCallback", "componentDidLoad", "toggleExpanded", "expand", "requestAccordion<PERSON><PERSON>gle", "render", "mode", "headerPart", "contentPart", "key", "class", "onClick", "id", "part", "ref", "name", "role", "delegatesFocus", "watchers", "ios", "md", "accordionGroupIosCss", "accordionGroupMdCss", "AccordionGroup", "ionChange", "ionValueChange", "multiple", "map", "v", "join", "emit", "disabled<PERSON><PERSON>ed", "_this2", "accordions", "getAccordions", "accordion", "readonly<PERSON><PERSON>ed", "_this3", "onKeydown", "ev", "_this4", "activeElement", "activeAccordionHeader", "accordion<PERSON>l", "closestGroup", "startingIndex", "findIndex", "a", "findNextAccordion", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "focus", "_this5", "setValue", "accordionExpand", "_this6", "groupValue", "processedValue", "valueExists", "filter", "prevAccordion", "_this7", "from", "querySelectorAll", "ion_accordion", "ion_accordion_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}