{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-popover_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACqK;AACkC;AACtF;AACD;AACtC;AACnB;AACiB;AACT;AAClC;AACe;AACF;;AAE1C;AACA;AACA;AACA;AACA;AACA,MAAM2C,kBAAkB,GAAIC,OAAO,IAAK;EACpC,IAAI,CAACA,OAAO,EAAE;IACV,OAAO;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;EAC5C;EACA,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;EACzD,OAAO;IAAEJ,UAAU,EAAEE,KAAK;IAAED,WAAW,EAAEE;EAAO,CAAC;AACrD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,SAAS,EAAEC,SAAS,KAAK;EACzD,MAAMC,iBAAiB,GAAGF,SAAS,CAACH,qBAAqB,CAAC,CAAC;EAC3D,MAAMM,aAAa,GAAGD,iBAAiB,CAACN,MAAM;EAC9C,IAAIQ,YAAY,GAAGF,iBAAiB,CAACP,KAAK;EAC1C,IAAII,IAAI,KAAK,OAAO,IAAIE,SAAS,EAAE;IAC/B,MAAMI,iBAAiB,GAAGJ,SAAS,CAACJ,qBAAqB,CAAC,CAAC;IAC3DO,YAAY,GAAGC,iBAAiB,CAACV,KAAK;EAC1C;EACA,OAAO;IACHS,YAAY;IACZD;EACJ,CAAC;AACL,CAAC;AACD,MAAMG,2BAA2B,GAAGA,CAACL,SAAS,EAAEM,aAAa,EAAEC,SAAS,EAAEC,eAAe,KAAK;EAC1F,IAAIC,gBAAgB,GAAG,EAAE;EACzB,MAAMC,IAAI,GAAG/B,uDAAc,CAAC6B,eAAe,CAAC;EAC5C,MAAMG,eAAe,GAAGD,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC;EAC9D,QAAQN,aAAa;IACjB,KAAK,OAAO;MACRG,gBAAgB,GAAG,CACf;QACI;AACpB;AACA;AACA;AACA;AACA;AACA;QACoBI,SAAS,EAAE,YAAY;QACvBC,QAAQ,EAAGC,EAAE,IAAK;UACd;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;UACwB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,gBAAgB,CAACH,EAAE,CAACI,OAAO,EAAEJ,EAAE,CAACK,OAAO,CAAC;UACjE,IAAIJ,OAAO,KAAKhB,SAAS,EAAE;YACvB;UACJ;UACAO,SAAS,CAACpC,OAAO,CAACkD,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;QAClD;MACJ,CAAC,CACJ;MACD;IACJ,KAAK,cAAc;IACnB,KAAK,OAAO;IACZ;MACIZ,gBAAgB,GAAG,CACf;QACII,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAGC,EAAE,IAAK;UACd;AACxB;AACA;AACA;UACwB,MAAMO,MAAM,GAAGP,EAAE,CAACO,MAAM;UACxB,MAAMC,cAAc,GAAGD,MAAM,CAACE,OAAO,CAAC,4BAA4B,CAAC;UACnE,IAAID,cAAc,KAAKvB,SAAS,EAAE;YAC9B;AAC5B;AACA;AACA;AACA;AACA;YAC4Be,EAAE,CAACU,eAAe,CAAC,CAAC;YACpB;UACJ;UACAlB,SAAS,CAACpC,OAAO,CAACkD,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;QAClD;MACJ,CAAC,CACJ;MACD;EACR;EACAZ,gBAAgB,CAACiB,OAAO,CAAC,CAAC;IAAEb,SAAS;IAAEC;EAAS,CAAC,KAAKH,eAAe,CAAC9B,gBAAgB,CAACgC,SAAS,EAAEC,QAAQ,CAAC,CAAC;EAC5G,OAAO,MAAM;IACTL,gBAAgB,CAACiB,OAAO,CAAC,CAAC;MAAEb,SAAS;MAAEC;IAAS,CAAC,KAAKH,eAAe,CAACgB,mBAAmB,CAACd,SAAS,EAAEC,QAAQ,CAAC,CAAC;EACnH,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMc,2BAA2B,GAAGA,CAAC5B,SAAS,EAAEM,aAAa,EAAEC,SAAS,KAAK;EACzE,IAAIsB,gBAAgB,GAAG,EAAE;EACzB;AACJ;AACA;AACA;AACA;EACI,QAAQvB,aAAa;IACjB,KAAK,OAAO;MACR,IAAIwB,YAAY;MAChBD,gBAAgB,GAAG,CACf;QACIhB,SAAS,EAAE,YAAY;QACvBC,QAAQ;UAAA,IAAAiB,IAAA,GAAAC,yMAAA,CAAE,WAAOjB,EAAE,EAAK;YACpBA,EAAE,CAACU,eAAe,CAAC,CAAC;YACpB,IAAIK,YAAY,EAAE;cACdG,YAAY,CAACH,YAAY,CAAC;YAC9B;YACA;AACxB;AACA;AACA;YACwBA,YAAY,GAAGI,UAAU,CAAC,MAAM;cAC5BtD,uDAAG,CAAC,MAAM;gBACN2B,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE,CAAC;gBAChCe,YAAY,GAAGT,SAAS;cAC5B,CAAC,CAAC;YACN,CAAC,EAAE,GAAG,CAAC;UACX,CAAC;UAAA,gBAfDP,QAAQA,CAAAsB,EAAA;YAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;UAAA;QAAA;MAgBZ,CAAC,EACD;QACIzB,SAAS,EAAE,YAAY;QACvBC,QAAQ,EAAGC,EAAE,IAAK;UACd,IAAIe,YAAY,EAAE;YACdG,YAAY,CAACH,YAAY,CAAC;UAC9B;UACA;AACxB;AACA;AACA;AACA;UACwB,MAAMR,MAAM,GAAGP,EAAE,CAACwB,aAAa;UAC/B,IAAI,CAACjB,MAAM,EAAE;YACT;UACJ;UACA,IAAIA,MAAM,CAACE,OAAO,CAAC,aAAa,CAAC,KAAKjB,SAAS,EAAE;YAC7CA,SAAS,CAACpC,OAAO,CAACkD,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;UAClD;QACJ;MACJ,CAAC,EACD;QACI;AACpB;AACA;AACA;QACoBR,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAGC,EAAE,IAAKA,EAAE,CAACU,eAAe,CAAC;MACzC,CAAC,EACD;QACIZ,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAGC,EAAE,IAAKR,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE,EAAE,IAAI;MAC3D,CAAC,CACJ;MACD;IACJ,KAAK,cAAc;MACfc,gBAAgB,GAAG,CACf;QACIhB,SAAS,EAAE,aAAa;QACxBC,QAAQ,EAAGC,EAAE,IAAK;UACd;AACxB;AACA;AACA;UACwBA,EAAE,CAACyB,cAAc,CAAC,CAAC;UACnBjC,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE,CAAC;QACpC;MACJ,CAAC,EACD;QACIF,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAGC,EAAE,IAAKA,EAAE,CAACU,eAAe,CAAC;MACzC,CAAC,EACD;QACIZ,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAGC,EAAE,IAAKR,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE,EAAE,IAAI;MAC3D,CAAC,CACJ;MACD;IACJ,KAAK,OAAO;IACZ;MACIc,gBAAgB,GAAG,CACf;QACI;AACpB;AACA;AACA;AACA;AACA;AACA;QACoBhB,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAGC,EAAE,IAAKR,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE;MACrD,CAAC,EACD;QACIF,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAGC,EAAE,IAAKR,SAAS,CAAC4B,kBAAkB,CAACpB,EAAE,EAAE,IAAI;MAC3D,CAAC,CACJ;MACD;EACR;EACAc,gBAAgB,CAACH,OAAO,CAAC,CAAC;IAAEb,SAAS;IAAEC;EAAS,CAAC,KAAKd,SAAS,CAACnB,gBAAgB,CAACgC,SAAS,EAAEC,QAAQ,CAAC,CAAC;EACtGd,SAAS,CAACyC,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC;EAC1D,OAAO,MAAM;IACTZ,gBAAgB,CAACH,OAAO,CAAC,CAAC;MAAEb,SAAS;MAAEC;IAAS,CAAC,KAAKd,SAAS,CAAC2B,mBAAmB,CAACd,SAAS,EAAEC,QAAQ,CAAC,CAAC;IACzGd,SAAS,CAAC0C,eAAe,CAAC,0BAA0B,CAAC;EACzD,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EACpC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAK,UAAU,EAAE;IACtC,OAAO,CAAC,CAAC;EACb;EACA,OAAOF,KAAK,CAACG,SAAS,CAAEC,EAAE,IAAKA,EAAE,KAAKH,IAAI,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMI,WAAW,GAAGA,CAACL,KAAK,EAAEM,WAAW,KAAK;EACxC,MAAMC,gBAAgB,GAAGR,cAAc,CAACC,KAAK,EAAEM,WAAW,CAAC;EAC3D,OAAON,KAAK,CAACO,gBAAgB,GAAG,CAAC,CAAC;AACtC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAACR,KAAK,EAAEM,WAAW,KAAK;EACxC,MAAMC,gBAAgB,GAAGR,cAAc,CAACC,KAAK,EAAEM,WAAW,CAAC;EAC3D,OAAON,KAAK,CAACO,gBAAgB,GAAG,CAAC,CAAC;AACtC,CAAC;AACD;AACA,MAAME,SAAS,GAAIR,IAAI,IAAK;EACxB,MAAMnC,IAAI,GAAG/B,uDAAc,CAACkE,IAAI,CAAC;EACjC,MAAMS,MAAM,GAAG5C,IAAI,CAACE,aAAa,CAAC,QAAQ,CAAC;EAC3C,IAAI0C,MAAM,EAAE;IACR1E,uDAAG,CAAC,MAAM0E,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAC7B;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIR,EAAE,IAAKA,EAAE,CAACS,YAAY,CAAC,0BAA0B,CAAC;AAC5E,MAAMC,4BAA4B,GAAInD,SAAS,IAAK;EAChD,MAAMO,QAAQ;IAAA,IAAA6C,KAAA,GAAA3B,yMAAA,CAAG,WAAOjB,EAAE,EAAK;MAC3B,IAAI6C,EAAE;MACN,MAAMC,aAAa,GAAG5C,QAAQ,CAAC4C,aAAa;MAC5C,IAAIjB,KAAK,GAAG,EAAE;MACd,MAAMkB,aAAa,GAAG,CAACF,EAAE,GAAG7C,EAAE,CAACO,MAAM,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACd,OAAO;MACtF;AACR;AACA;AACA;MACQ,IAAIgB,aAAa,KAAK,aAAa,IAAIA,aAAa,KAAK,UAAU,EAAE;QACjE;MACJ;MACA;AACR;AACA;AACA;AACA;MACQ,IAAI;QACA;AACZ;AACA;AACA;QACYlB,KAAK,GAAGmB,KAAK,CAACC,IAAI,CAACzD,SAAS,CAAC0D,gBAAgB,CAAC,yDAAyD,CAAC,CAAC;QACzG;MACJ,CAAC,CACD,OAAOC,EAAE,EAAE,CAAE;MACb,QAAQnD,EAAE,CAACoD,GAAG;QACV;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,KAAK,WAAW;UACZ,MAAMC,aAAa,SAAS7D,SAAS,CAAC8D,gBAAgB,CAAC,CAAC;UACxD,IAAID,aAAa,EAAE;YACf7D,SAAS,CAACpC,OAAO,CAACkD,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;UAClD;UACA;QACJ;AACZ;AACA;QACY,KAAK,WAAW;UACZ;UACAN,EAAE,CAACyB,cAAc,CAAC,CAAC;UACnB,MAAM8B,QAAQ,GAAGrB,WAAW,CAACL,KAAK,EAAEiB,aAAa,CAAC;UAClD,IAAIS,QAAQ,KAAKjD,SAAS,EAAE;YACxBgC,SAAS,CAACiB,QAAQ,CAAC;UACvB;UACA;QACJ;AACZ;AACA;QACY,KAAK,SAAS;UACV;UACAvD,EAAE,CAACyB,cAAc,CAAC,CAAC;UACnB,MAAM+B,QAAQ,GAAGnB,WAAW,CAACR,KAAK,EAAEiB,aAAa,CAAC;UAClD,IAAIU,QAAQ,KAAKlD,SAAS,EAAE;YACxBgC,SAAS,CAACkB,QAAQ,CAAC;UACvB;UACA;QACJ;AACZ;AACA;QACY,KAAK,MAAM;UACPxD,EAAE,CAACyB,cAAc,CAAC,CAAC;UACnB,MAAMgC,SAAS,GAAG5B,KAAK,CAAC,CAAC,CAAC;UAC1B,IAAI4B,SAAS,KAAKnD,SAAS,EAAE;YACzBgC,SAAS,CAACmB,SAAS,CAAC;UACxB;UACA;QACJ;AACZ;AACA;QACY,KAAK,KAAK;UACNzD,EAAE,CAACyB,cAAc,CAAC,CAAC;UACnB,MAAMiC,QAAQ,GAAG7B,KAAK,CAACA,KAAK,CAAC8B,MAAM,GAAG,CAAC,CAAC;UACxC,IAAID,QAAQ,KAAKpD,SAAS,EAAE;YACxBgC,SAAS,CAACoB,QAAQ,CAAC;UACvB;UACA;QACJ;AACZ;AACA;AACA;AACA;QACY,KAAK,YAAY;QACjB,KAAK,GAAG;QACR,KAAK,OAAO;UACR,IAAIZ,aAAa,IAAIL,gBAAgB,CAACK,aAAa,CAAC,EAAE;YAClD,MAAMc,UAAU,GAAG,IAAIC,WAAW,CAAC,2BAA2B,CAAC;YAC/Df,aAAa,CAACgB,aAAa,CAACF,UAAU,CAAC;UAC3C;UACA;MACR;IACJ,CAAC;IAAA,gBAhGK7D,QAAQA,CAAAgE,GAAA;MAAA,OAAAnB,KAAA,CAAAtB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAgGb;EACD/B,SAAS,CAAC1B,gBAAgB,CAAC,SAAS,EAAEiC,QAAQ,CAAC;EAC/C,OAAO,MAAMP,SAAS,CAACoB,mBAAmB,CAAC,SAAS,EAAEb,QAAQ,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMiE,kBAAkB,GAAGA,CAACC,KAAK,EAAE7E,YAAY,EAAED,aAAa,EAAEV,UAAU,EAAEC,WAAW,EAAEwF,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,eAAe,EAAEpF,SAAS,EAAEqF,KAAK,KAAK;EACnJ,IAAIzB,EAAE;EACN,IAAI0B,oBAAoB,GAAG;IACvBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACP9F,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACZ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,QAAQsF,SAAS;IACb,KAAK,OAAO;MACR,IAAI,CAACI,KAAK,EAAE;QACR,OAAOD,eAAe;MAC1B;MACA,MAAMK,OAAO,GAAGJ,KAAK;MACrBC,oBAAoB,GAAG;QACnBC,GAAG,EAAEE,OAAO,CAACrE,OAAO;QACpBoE,IAAI,EAAEC,OAAO,CAACtE,OAAO;QACrBzB,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACZ,CAAC;MACD;IACJ;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,KAAK,SAAS;IACd;MACI,MAAM+F,QAAQ,GAAGL,KAAK;MACtB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMM,eAAe,GAAI3F,SAAS,KAC7B,CAAC4D,EAAE,GAAG8B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,MAAM,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,eAAe,CAAC,KACnIH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpE,MAAM,CAAE;MAC1E,IAAI,CAACqE,eAAe,EAAE;QAClB,OAAOP,eAAe;MAC1B;MACA,MAAMU,kBAAkB,GAAGH,eAAe,CAAC/F,qBAAqB,CAAC,CAAC;MAClE0F,oBAAoB,GAAG;QACnBC,GAAG,EAAEO,kBAAkB,CAACP,GAAG;QAC3BC,IAAI,EAAEM,kBAAkB,CAACN,IAAI;QAC7B9F,KAAK,EAAEoG,kBAAkB,CAACpG,KAAK;QAC/BC,MAAM,EAAEmG,kBAAkB,CAACnG;MAC/B,CAAC;MACD;EACR;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMoG,WAAW,GAAGC,oBAAoB,CAACd,IAAI,EAAEI,oBAAoB,EAAEnF,YAAY,EAAED,aAAa,EAAEV,UAAU,EAAEC,WAAW,EAAEuF,KAAK,CAAC;EACjI;AACJ;AACA;AACA;AACA;EACI,MAAMiB,kBAAkB,GAAGC,qBAAqB,CAACf,KAAK,EAAED,IAAI,EAAEI,oBAAoB,EAAEnF,YAAY,EAAED,aAAa,CAAC;EAChH,MAAMqF,GAAG,GAAGQ,WAAW,CAACR,GAAG,GAAGU,kBAAkB,CAACV,GAAG;EACpD,MAAMC,IAAI,GAAGO,WAAW,CAACP,IAAI,GAAGS,kBAAkB,CAACT,IAAI;EACvD,MAAM;IAAEW,QAAQ;IAAEC;EAAU,CAAC,GAAGC,sBAAsB,CAACnB,IAAI,EAAE1F,UAAU,EAAEC,WAAW,EAAE8F,GAAG,EAAEC,IAAI,EAAErF,YAAY,EAAED,aAAa,EAAE8E,KAAK,CAAC;EACpI,MAAM;IAAEsB,OAAO;IAAEC;EAAQ,CAAC,GAAGC,sBAAsB,CAACtB,IAAI,EAAEC,KAAK,EAAEH,KAAK,CAAC;EACvE,OAAO;IAAEO,GAAG;IAAEC,IAAI;IAAEF,oBAAoB;IAAEa,QAAQ;IAAEC,SAAS;IAAEE,OAAO;IAAEC;EAAQ,CAAC;AACrF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGA,CAACtB,IAAI,EAAEC,KAAK,EAAEH,KAAK,KAAK;EACnD,QAAQE,IAAI;IACR,KAAK,KAAK;MACN,OAAO;QAAEoB,OAAO,EAAEG,mBAAmB,CAACtB,KAAK,CAAC;QAAEoB,OAAO,EAAE;MAAS,CAAC;IACrE,KAAK,QAAQ;MACT,OAAO;QAAED,OAAO,EAAEG,mBAAmB,CAACtB,KAAK,CAAC;QAAEoB,OAAO,EAAE;MAAM,CAAC;IAClE,KAAK,MAAM;MACP,OAAO;QAAED,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAEG,mBAAmB,CAACvB,KAAK;MAAE,CAAC;IACpE,KAAK,OAAO;MACR,OAAO;QAAEmB,OAAO,EAAE,MAAM;QAAEC,OAAO,EAAEG,mBAAmB,CAACvB,KAAK;MAAE,CAAC;IACnE,KAAK,OAAO;MACR,OAAO;QAAEmB,OAAO,EAAEtB,KAAK,GAAG,MAAM,GAAG,OAAO;QAAEuB,OAAO,EAAEG,mBAAmB,CAACvB,KAAK;MAAE,CAAC;IACrF,KAAK,KAAK;MACN,OAAO;QAAEmB,OAAO,EAAEtB,KAAK,GAAG,OAAO,GAAG,MAAM;QAAEuB,OAAO,EAAEG,mBAAmB,CAACvB,KAAK;MAAE,CAAC;EACzF;AACJ,CAAC;AACD,MAAMsB,mBAAmB,GAAItB,KAAK,IAAK;EACnC,QAAQA,KAAK;IACT,KAAK,OAAO;MACR,OAAO,MAAM;IACjB,KAAK,QAAQ;MACT,OAAO,QAAQ;IACnB,KAAK,KAAK;MACN,OAAO,OAAO;EACtB;AACJ,CAAC;AACD,MAAMuB,mBAAmB,GAAIvB,KAAK,IAAK;EACnC,QAAQA,KAAK;IACT,KAAK,OAAO;MACR,OAAO,KAAK;IAChB,KAAK,QAAQ;MACT,OAAO,QAAQ;IACnB,KAAK,KAAK;MACN,OAAO,QAAQ;EACvB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMkB,sBAAsB,GAAGA,CAACnB,IAAI,EAAE1F,UAAU,EAAEC,WAAW,EAAE8F,GAAG,EAAEC,IAAI,EAAErF,YAAY,EAAED,aAAa,EAAE8E,KAAK,KAAK;EAC7G;AACJ;AACA;AACA;AACA;EACI,MAAM2B,YAAY,GAAG;IACjBR,QAAQ,EAAEZ,GAAG,GAAGrF,aAAa,GAAG,CAAC,GAAGV,UAAU,GAAG,CAAC;IAClD4G,SAAS,EAAEZ,IAAI,GAAGrF,YAAY,GAAGX,UAAU,GAAG;EAClD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAMoH,aAAa,GAAG;IAAET,QAAQ,EAAEZ,GAAG,GAAGrF,aAAa,GAAG,CAAC,GAAGV,UAAU,GAAG,CAAC;IAAE4G,SAAS,EAAEZ,IAAI,GAAGhG,UAAU,GAAG;EAAI,CAAC;EAChH,QAAQ0F,IAAI;IACR,KAAK,KAAK;MACN,OAAO;QAAEiB,QAAQ,EAAEZ,GAAG,GAAGrF,aAAa;QAAEkG,SAAS,EAAEZ,IAAI,GAAGrF,YAAY,GAAG,CAAC,GAAGX,UAAU,GAAG;MAAE,CAAC;IACjG,KAAK,QAAQ;MACT,OAAO;QAAE2G,QAAQ,EAAEZ,GAAG,GAAG9F,WAAW;QAAE2G,SAAS,EAAEZ,IAAI,GAAGrF,YAAY,GAAG,CAAC,GAAGX,UAAU,GAAG;MAAE,CAAC;IAC/F,KAAK,MAAM;MACP,OAAOmH,YAAY;IACvB,KAAK,OAAO;MACR,OAAOC,aAAa;IACxB,KAAK,OAAO;MACR,OAAO5B,KAAK,GAAG4B,aAAa,GAAGD,YAAY;IAC/C,KAAK,KAAK;MACN,OAAO3B,KAAK,GAAG2B,YAAY,GAAGC,aAAa;IAC/C;MACI,OAAO;QAAET,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAC;EAC5C;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMJ,oBAAoB,GAAGA,CAACd,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,EAAEV,UAAU,EAAEC,WAAW,EAAEuF,KAAK,KAAK;EACpH,MAAM6B,QAAQ,GAAG;IACbtB,GAAG,EAAEO,kBAAkB,CAACP,GAAG;IAC3BC,IAAI,EAAEM,kBAAkB,CAACN,IAAI,GAAGrF,YAAY,GAAGX;EACnD,CAAC;EACD,MAAMsH,SAAS,GAAG;IACdvB,GAAG,EAAEO,kBAAkB,CAACP,GAAG;IAC3BC,IAAI,EAAEM,kBAAkB,CAACN,IAAI,GAAGM,kBAAkB,CAACpG,KAAK,GAAGF;EAC/D,CAAC;EACD,QAAQ0F,IAAI;IACR,KAAK,KAAK;MACN,OAAO;QACHK,GAAG,EAAEO,kBAAkB,CAACP,GAAG,GAAGrF,aAAa,GAAGT,WAAW;QACzD+F,IAAI,EAAEM,kBAAkB,CAACN;MAC7B,CAAC;IACL,KAAK,OAAO;MACR,OAAOsB,SAAS;IACpB,KAAK,QAAQ;MACT,OAAO;QACHvB,GAAG,EAAEO,kBAAkB,CAACP,GAAG,GAAGO,kBAAkB,CAACnG,MAAM,GAAGF,WAAW;QACrE+F,IAAI,EAAEM,kBAAkB,CAACN;MAC7B,CAAC;IACL,KAAK,MAAM;MACP,OAAOqB,QAAQ;IACnB,KAAK,OAAO;MACR,OAAO7B,KAAK,GAAG8B,SAAS,GAAGD,QAAQ;IACvC,KAAK,KAAK;MACN,OAAO7B,KAAK,GAAG6B,QAAQ,GAAGC,SAAS;EAC3C;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,qBAAqB,GAAGA,CAACf,KAAK,EAAED,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,KAAK;EAC5F,QAAQiF,KAAK;IACT,KAAK,QAAQ;MACT,OAAO4B,2BAA2B,CAAC7B,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,CAAC;IAC7F,KAAK,KAAK;MACN,OAAO8G,wBAAwB,CAAC9B,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,CAAC;IAC1F,KAAK,OAAO;IACZ;MACI,OAAO;QAAEqF,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC;EAClC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,wBAAwB,GAAGA,CAAC9B,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,KAAK;EACxF,QAAQgF,IAAI;IACR,KAAK,OAAO;IACZ,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,OAAO;MACR,OAAO;QACHK,GAAG,EAAE,EAAErF,aAAa,GAAG4F,kBAAkB,CAACnG,MAAM,CAAC;QACjD6F,IAAI,EAAE;MACV,CAAC;IACL,KAAK,KAAK;IACV,KAAK,QAAQ;IACb;MACI,OAAO;QACHD,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,EAAErF,YAAY,GAAG2F,kBAAkB,CAACpG,KAAK;MACnD,CAAC;EACT;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqH,2BAA2B,GAAGA,CAAC7B,IAAI,EAAEY,kBAAkB,EAAE3F,YAAY,EAAED,aAAa,KAAK;EAC3F,QAAQgF,IAAI;IACR,KAAK,OAAO;IACZ,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,OAAO;MACR,OAAO;QACHK,GAAG,EAAE,EAAErF,aAAa,GAAG,CAAC,GAAG4F,kBAAkB,CAACnG,MAAM,GAAG,CAAC,CAAC;QACzD6F,IAAI,EAAE;MACV,CAAC;IACL,KAAK,KAAK;IACV,KAAK,QAAQ;IACb;MACI,OAAO;QACHD,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,EAAErF,YAAY,GAAG,CAAC,GAAG2F,kBAAkB,CAACpG,KAAK,GAAG,CAAC;MAC3D,CAAC;EACT;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMuH,yBAAyB,GAAGA,CAAC/B,IAAI,EAAEgC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEnH,YAAY,EAAED,aAAa,EAAEqH,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,aAAa,GAAG,CAAC,EAAEC,cAAc,GAAG,CAAC,EAAEnI,WAAW,GAAG,CAAC,KAAK;EAC1P,IAAI0G,QAAQ,GAAGwB,aAAa;EAC5B,MAAMvB,SAAS,GAAGwB,cAAc;EAChC,IAAIpC,IAAI,GAAG2B,SAAS;EACpB,IAAI5B,GAAG,GAAG2B,QAAQ;EAClB,IAAIW,MAAM;EACV,IAAIvB,OAAO,GAAGkB,cAAc;EAC5B,IAAIjB,OAAO,GAAGkB,cAAc;EAC5B,IAAIK,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,MAAMC,UAAU,GAAGN,kBAAkB,GAC/BA,kBAAkB,CAACnC,GAAG,GAAGmC,kBAAkB,CAAC/H,MAAM,GAClD2H,UAAU,GAAG,CAAC,GAAGpH,aAAa,GAAG,CAAC;EACxC,MAAM+H,aAAa,GAAGP,kBAAkB,GAAGA,kBAAkB,CAAC/H,MAAM,GAAG,CAAC;EACxE,IAAIuI,qBAAqB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;EACI,IAAI1C,IAAI,GAAG4B,WAAW,GAAGG,cAAc,EAAE;IACrC/B,IAAI,GAAG4B,WAAW;IAClBU,iBAAiB,GAAG,IAAI;IACxBxB,OAAO,GAAG,MAAM;IAChB;AACR;AACA;AACA;EACI,CAAC,MACI,IAAInG,YAAY,GAAGiH,WAAW,GAAG5B,IAAI,GAAG+B,cAAc,GAAGF,SAAS,EAAE;IACrEU,kBAAkB,GAAG,IAAI;IACzBvC,IAAI,GAAG6B,SAAS,GAAGlH,YAAY,GAAGiH,WAAW;IAC7Cd,OAAO,GAAG,OAAO;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI0B,UAAU,GAAGC,aAAa,GAAG/H,aAAa,GAAGoH,UAAU,KAAKpC,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,QAAQ,CAAC,EAAE;IAClG,IAAI8C,UAAU,GAAG9H,aAAa,GAAG,CAAC,EAAE;MAChC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYqF,GAAG,GAAG4C,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEJ,UAAU,GAAG9H,aAAa,GAAG+H,aAAa,IAAIxI,WAAW,GAAG,CAAC,CAAC,CAAC;MAClF0G,QAAQ,GAAGZ,GAAG,GAAGrF,aAAa;MAC9BqG,OAAO,GAAG,QAAQ;MAClB2B,qBAAqB,GAAG,IAAI;MAC5B;AACZ;AACA;AACA;IACQ,CAAC,MACI;MACDL,MAAM,GAAGT,WAAW;IACxB;EACJ;EACA,OAAO;IACH7B,GAAG;IACHC,IAAI;IACJqC,MAAM;IACNvB,OAAO;IACPC,OAAO;IACPuB,iBAAiB;IACjBC,kBAAkB;IAClB5B,QAAQ;IACRC,SAAS;IACT8B;EACJ,CAAC;AACL,CAAC;AACD,MAAMG,eAAe,GAAGA,CAACnD,IAAI,EAAEoD,eAAe,GAAG,KAAK,EAAEvH,EAAE,EAAEwH,OAAO,KAAK;EACpE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACxH,EAAE,IAAI,CAACwH,OAAO,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIrD,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,QAAQ,IAAIoD,eAAe,EAAE;IACxD,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AAED,MAAME,wBAAwB,GAAG,CAAC;AAClC;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;EACxC,IAAI/E,EAAE;EACN,MAAM;IAAEyB,KAAK,EAAEtE,EAAE;IAAEjB,IAAI;IAAEyI,OAAO;IAAEtD,SAAS;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGwD,IAAI;EACjE,MAAMC,GAAG,GAAGF,MAAM,CAACG,aAAa;EAChC,MAAM7D,KAAK,GAAG4D,GAAG,CAACE,GAAG,KAAK,KAAK;EAC/B,MAAMzB,SAAS,GAAGuB,GAAG,CAACG,WAAW,CAACC,UAAU;EAC5C,MAAM1B,UAAU,GAAGsB,GAAG,CAACG,WAAW,CAACE,WAAW;EAC9C,MAAMvI,IAAI,GAAG/B,uDAAc,CAAC+J,MAAM,CAAC;EACnC,MAAM3I,SAAS,GAAGW,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC;EACxD,MAAMrB,OAAO,GAAGmB,IAAI,CAACE,aAAa,CAAC,gBAAgB,CAAC;EACpD,MAAMsI,eAAe,GAAGX,OAAO,KAAK,CAAC3E,EAAE,GAAG7C,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6E,MAAM,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,eAAe,CAAC,KAAK9E,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,MAAM,CAAC;EAC5M,MAAM;IAAEnB,YAAY;IAAED;EAAc,CAAC,GAAGL,oBAAoB,CAACC,IAAI,EAAEC,SAAS,EAAEmJ,eAAe,CAAC;EAC9F,MAAM;IAAE1J,UAAU;IAAEC;EAAY,CAAC,GAAGH,kBAAkB,CAACC,OAAO,CAAC;EAC/D,MAAM6F,eAAe,GAAG;IACpBG,GAAG,EAAE+B,UAAU,GAAG,CAAC,GAAGpH,aAAa,GAAG,CAAC;IACvCsF,IAAI,EAAE6B,SAAS,GAAG,CAAC,GAAGlH,YAAY,GAAG,CAAC;IACtCmG,OAAO,EAAEtB,KAAK,GAAG,OAAO,GAAG,MAAM;IACjCuB,OAAO,EAAE;EACb,CAAC;EACD,MAAM4C,OAAO,GAAGpE,kBAAkB,CAACC,KAAK,EAAE7E,YAAY,EAAED,aAAa,EAAEV,UAAU,EAAEC,WAAW,EAAEwF,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,eAAe,EAAEmD,OAAO,EAAExH,EAAE,CAAC;EACrJ,MAAMqI,OAAO,GAAGtJ,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG0I,wBAAwB;EAC/D,MAAMa,MAAM,GAAGvJ,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;EACxC,MAAM;IAAEwG,OAAO;IAAEC,OAAO;IAAEhB,GAAG;IAAEC,IAAI;IAAEqC,MAAM;IAAEC,iBAAiB;IAAEC,kBAAkB;IAAE5B,QAAQ;IAAEC,SAAS;IAAE8B;EAAuB,CAAC,GAAGjB,yBAAyB,CAAC/B,IAAI,EAAEiE,OAAO,CAAC5D,GAAG,EAAE4D,OAAO,CAAC3D,IAAI,EAAE4D,OAAO,EAAE/B,SAAS,EAAEC,UAAU,EAAEnH,YAAY,EAAED,aAAa,EAAEmJ,MAAM,EAAEF,OAAO,CAAC7C,OAAO,EAAE6C,OAAO,CAAC5C,OAAO,EAAE4C,OAAO,CAAC7D,oBAAoB,EAAE6D,OAAO,CAAChD,QAAQ,EAAEgD,OAAO,CAAC/C,SAAS,EAAE3G,WAAW,CAAC;EACrX,MAAM6J,aAAa,GAAGjK,yDAAe,CAAC,CAAC;EACvC,MAAMkK,iBAAiB,GAAGlK,yDAAe,CAAC,CAAC;EAC3C,MAAMmK,gBAAgB,GAAGnK,yDAAe,CAAC,CAAC;EAC1CkK,iBAAiB,CACZE,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,cAAc,CAAC,CAAC,CAC9C8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzC;EACA;EACA;EACA;EACAJ,gBAAgB,CACXC,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAChD6I,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAClD8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC/B;EACA,OAAOJ,aAAa,CACfO,MAAM,CAAC,MAAM,CAAC,CACdC,QAAQ,CAAC,GAAG,CAAC,CACbC,cAAc,CAAC,MAAM;IACtB,IAAIjK,IAAI,KAAK,OAAO,EAAE;MAClB4I,MAAM,CAACsB,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG9J,YAAY,IAAI,CAAC;IAC5D;IACA,IAAI+H,qBAAqB,EAAE;MACvBQ,MAAM,CAACwB,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC1C;IACA,IAAItC,MAAM,KAAKxG,SAAS,EAAE;MACtBtB,SAAS,CAACiK,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,GAAGpC,MAAM,IAAI,CAAC;IACxD;IACA,MAAMuC,YAAY,GAAG,iCAAiC;IACtD,MAAMC,aAAa,GAAG,kCAAkC;IACxD,IAAIC,SAAS,GAAG,GAAG9E,IAAI,IAAI;IAC3B,IAAIsC,iBAAiB,EAAE;MACnBwC,SAAS,GAAG,GAAG9E,IAAI,KAAK4E,YAAY,EAAE;IAC1C;IACA,IAAIrC,kBAAkB,EAAE;MACpBuC,SAAS,GAAG,GAAG9E,IAAI,KAAK6E,aAAa,EAAE;IAC3C;IACAtK,SAAS,CAACiK,KAAK,CAACC,WAAW,CAAC,KAAK,EAAE,QAAQ1E,GAAG,0BAA0B,CAAC;IACzExF,SAAS,CAACiK,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,QAAQK,SAAS,wBAAwB,CAAC;IAC9EvK,SAAS,CAACiK,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAE,GAAG1D,OAAO,IAAID,OAAO,EAAE,CAAC;IACxE,IAAI/G,OAAO,KAAK,IAAI,EAAE;MAClB,MAAM+I,eAAe,GAAGa,OAAO,CAAC5D,GAAG,KAAKA,GAAG,IAAI4D,OAAO,CAAC3D,IAAI,KAAKA,IAAI;MACpE,MAAM+E,SAAS,GAAGlC,eAAe,CAACnD,IAAI,EAAEoD,eAAe,EAAEvH,EAAE,EAAEwH,OAAO,CAAC;MACrE,IAAIgC,SAAS,EAAE;QACXhL,OAAO,CAACyK,KAAK,CAACC,WAAW,CAAC,KAAK,EAAE,QAAQ9D,QAAQ,0BAA0B,CAAC;QAC5E5G,OAAO,CAACyK,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,QAAQ7D,SAAS,0BAA0B,CAAC;MAClF,CAAC,MACI;QACD7G,OAAO,CAACyK,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;MAChD;IACJ;EACJ,CAAC,CAAC,CACGO,YAAY,CAAC,CAACjB,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMiB,iBAAiB,GAAI/B,MAAM,IAAK;EAClC,MAAMhI,IAAI,GAAG/B,uDAAc,CAAC+J,MAAM,CAAC;EACnC,MAAM3I,SAAS,GAAGW,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC;EACxD,MAAMrB,OAAO,GAAGmB,IAAI,CAACE,aAAa,CAAC,gBAAgB,CAAC;EACpD,MAAM0I,aAAa,GAAGjK,yDAAe,CAAC,CAAC;EACvC,MAAMkK,iBAAiB,GAAGlK,yDAAe,CAAC,CAAC;EAC3C,MAAMmK,gBAAgB,GAAGnK,yDAAe,CAAC,CAAC;EAC1CkK,iBAAiB,CAACE,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC8I,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAChHF,gBAAgB,CACXC,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAChD6I,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAClD8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC/B,OAAOJ,aAAa,CACfO,MAAM,CAAC,MAAM,CAAC,CACda,aAAa,CAAC,MAAM;IACrBhC,MAAM,CAACsB,KAAK,CAACW,cAAc,CAAC,SAAS,CAAC;IACtCjC,MAAM,CAACwB,SAAS,CAACU,MAAM,CAAC,gBAAgB,CAAC;IACzC7K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,KAAK,CAAC;IACrC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,MAAM,CAAC;IACtC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,QAAQ,CAAC;IACxC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,kBAAkB,CAAC;IAClD,IAAIpL,OAAO,EAAE;MACTA,OAAO,CAACyK,KAAK,CAACW,cAAc,CAAC,KAAK,CAAC;MACnCpL,OAAO,CAACyK,KAAK,CAACW,cAAc,CAAC,MAAM,CAAC;MACpCpL,OAAO,CAACyK,KAAK,CAACW,cAAc,CAAC,SAAS,CAAC;IAC3C;EACJ,CAAC,CAAC,CACGb,QAAQ,CAAC,GAAG,CAAC,CACbU,YAAY,CAAC,CAACjB,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMqB,uBAAuB,GAAG,EAAE;AAClC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAACpC,MAAM,EAAEC,IAAI,KAAK;EACvC,IAAI/E,EAAE;EACN,MAAM;IAAEyB,KAAK,EAAEtE,EAAE;IAAEjB,IAAI;IAAEyI,OAAO;IAAEtD,SAAS;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGwD,IAAI;EACjE,MAAMC,GAAG,GAAGF,MAAM,CAACG,aAAa;EAChC,MAAM7D,KAAK,GAAG4D,GAAG,CAACE,GAAG,KAAK,KAAK;EAC/B,MAAMzB,SAAS,GAAGuB,GAAG,CAACG,WAAW,CAACC,UAAU;EAC5C,MAAM1B,UAAU,GAAGsB,GAAG,CAACG,WAAW,CAACE,WAAW;EAC9C,MAAMvI,IAAI,GAAG/B,uDAAc,CAAC+J,MAAM,CAAC;EACnC,MAAM3I,SAAS,GAAGW,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC;EACxD,MAAMsI,eAAe,GAAGX,OAAO,KAAK,CAAC3E,EAAE,GAAG7C,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6E,MAAM,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,eAAe,CAAC,KAAK9E,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,MAAM,CAAC;EAC5M,MAAM;IAAEnB,YAAY;IAAED;EAAc,CAAC,GAAGL,oBAAoB,CAACC,IAAI,EAAEC,SAAS,EAAEmJ,eAAe,CAAC;EAC9F,MAAM9D,eAAe,GAAG;IACpBG,GAAG,EAAE+B,UAAU,GAAG,CAAC,GAAGpH,aAAa,GAAG,CAAC;IACvCsF,IAAI,EAAE6B,SAAS,GAAG,CAAC,GAAGlH,YAAY,GAAG,CAAC;IACtCmG,OAAO,EAAEtB,KAAK,GAAG,OAAO,GAAG,MAAM;IACjCuB,OAAO,EAAE;EACb,CAAC;EACD,MAAM4C,OAAO,GAAGpE,kBAAkB,CAACC,KAAK,EAAE7E,YAAY,EAAED,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE+E,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,eAAe,EAAEmD,OAAO,EAAExH,EAAE,CAAC;EAClI,MAAMqI,OAAO,GAAGtJ,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG+K,uBAAuB;EAC9D,MAAM;IAAEvE,OAAO;IAAEC,OAAO;IAAEhB,GAAG;IAAEC,IAAI;IAAEqC;EAAO,CAAC,GAAGZ,yBAAyB,CAAC/B,IAAI,EAAEiE,OAAO,CAAC5D,GAAG,EAAE4D,OAAO,CAAC3D,IAAI,EAAE4D,OAAO,EAAE/B,SAAS,EAAEC,UAAU,EAAEnH,YAAY,EAAED,aAAa,EAAE,CAAC,EAAEiJ,OAAO,CAAC7C,OAAO,EAAE6C,OAAO,CAAC5C,OAAO,EAAE4C,OAAO,CAAC7D,oBAAoB,CAAC;EAC1O,MAAMgE,aAAa,GAAGjK,yDAAe,CAAC,CAAC;EACvC,MAAMkK,iBAAiB,GAAGlK,yDAAe,CAAC,CAAC;EAC3C,MAAM0L,gBAAgB,GAAG1L,yDAAe,CAAC,CAAC;EAC1C,MAAMmK,gBAAgB,GAAGnK,yDAAe,CAAC,CAAC;EAC1C,MAAM2L,iBAAiB,GAAG3L,yDAAe,CAAC,CAAC;EAC3CkK,iBAAiB,CACZE,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,cAAc,CAAC,CAAC,CAC9C8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCmB,gBAAgB,CAACtB,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACkJ,QAAQ,CAAC,GAAG,CAAC,CAACJ,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC5GF,gBAAgB,CACXC,UAAU,CAAC1J,SAAS,CAAC,CACrB4J,YAAY,CAAC;IACdpE,GAAG,EAAE,QAAQA,GAAG,4BAA4B;IAC5CC,IAAI,EAAE,QAAQA,IAAI,4BAA4B;IAC9C,kBAAkB,EAAE,GAAGe,OAAO,IAAID,OAAO;EAC7C,CAAC,CAAC,CACGyD,cAAc,CAAC,MAAM;IACtB,IAAIlC,MAAM,KAAKxG,SAAS,EAAE;MACtBtB,SAAS,CAACiK,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,GAAGpC,MAAM,IAAI,CAAC;IACxD;EACJ,CAAC,CAAC,CACG6B,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC;EAClDsB,iBAAiB,CAACvB,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAChG,OAAOJ,aAAa,CACfO,MAAM,CAAC,gCAAgC,CAAC,CACxCC,QAAQ,CAAC,GAAG,CAAC,CACbC,cAAc,CAAC,MAAM;IACtB,IAAIjK,IAAI,KAAK,OAAO,EAAE;MAClB4I,MAAM,CAACsB,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG9J,YAAY,IAAI,CAAC;IAC5D;IACA,IAAIoG,OAAO,KAAK,QAAQ,EAAE;MACtBmC,MAAM,CAACwB,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC1C;EACJ,CAAC,CAAC,CACGK,YAAY,CAAC,CAACjB,iBAAiB,EAAEwB,gBAAgB,EAAEvB,gBAAgB,EAAEwB,iBAAiB,CAAC,CAAC;AACjG,CAAC;;AAED;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIvC,MAAM,IAAK;EACjC,MAAMhI,IAAI,GAAG/B,uDAAc,CAAC+J,MAAM,CAAC;EACnC,MAAM3I,SAAS,GAAGW,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC;EACxD,MAAM0I,aAAa,GAAGjK,yDAAe,CAAC,CAAC;EACvC,MAAMkK,iBAAiB,GAAGlK,yDAAe,CAAC,CAAC;EAC3C,MAAM0L,gBAAgB,GAAG1L,yDAAe,CAAC,CAAC;EAC1CkK,iBAAiB,CAACE,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC8I,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAChHqB,gBAAgB,CAACtB,UAAU,CAAC/I,IAAI,CAACE,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC8I,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC9F,OAAOJ,aAAa,CACfO,MAAM,CAAC,MAAM,CAAC,CACda,aAAa,CAAC,MAAM;IACrBhC,MAAM,CAACsB,KAAK,CAACW,cAAc,CAAC,SAAS,CAAC;IACtCjC,MAAM,CAACwB,SAAS,CAACU,MAAM,CAAC,gBAAgB,CAAC;IACzC7K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,KAAK,CAAC;IACrC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,MAAM,CAAC;IACtC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,QAAQ,CAAC;IACxC5K,SAAS,CAACiK,KAAK,CAACW,cAAc,CAAC,kBAAkB,CAAC;EACtD,CAAC,CAAC,CACGb,QAAQ,CAAC,GAAG,CAAC,CACbU,YAAY,CAAC,CAACjB,iBAAiB,EAAEwB,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMG,aAAa,GAAG,4iIAA4iI;AAElkI,MAAMC,YAAY,GAAG,m/EAAm/E;AAExgF,MAAMC,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB1O,qDAAgB,CAAC,IAAI,EAAE0O,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGzO,qDAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC0O,WAAW,GAAG1O,qDAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC2O,WAAW,GAAG3O,qDAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC4O,UAAU,GAAG5O,qDAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC6O,mBAAmB,GAAG7O,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC8O,oBAAoB,GAAG9O,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC+O,oBAAoB,GAAG/O,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACgP,mBAAmB,GAAGhP,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACiP,QAAQ,GAAGjP,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsH,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC4H,YAAY,GAAGxN,iEAAY,CAAC,CAAC;IAClC,IAAI,CAACyN,cAAc,GAAGjN,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAACkN,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACpM,aAAa,GAAG,OAAO;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACR,IAAI,GAAG,MAAM;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAAC6M,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC1H,SAAS,GAAG,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAAC0H,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAC9O,OAAO,CAACkD,SAAS,EAAE1D,oDAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACuP,WAAW,GAAIC,UAAU,IAAK;MAC/B,MAAMnK,EAAE,GAAG,IAAI,CAACoK,YAAY;MAC5B,MAAMC,IAAI,GAAGC,aAAa,CAACH,UAAU,CAACI,IAAI,CAAC;MAC3C,IAAIvK,EAAE,IAAIqK,IAAI,EAAE;QACZ,MAAMhI,KAAK,GAAG,IAAIT,WAAW,CAACyI,IAAI,EAAE;UAChCG,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjB7H,MAAM,EAAEuH,UAAU,CAACvH;QACvB,CAAC,CAAC;QACF5C,EAAE,CAAC6B,aAAa,CAACQ,KAAK,CAAC;MAC3B;IACJ,CAAC;IACD,IAAI,CAACzD,2BAA2B,GAAG,MAAM;MACrC,MAAM;QAAE2G,OAAO;QAAEjI,aAAa;QAAE0C,EAAE;QAAE0K;MAA0B,CAAC,GAAG,IAAI;MACtE,IAAIA,yBAAyB,EAAE;QAC3BA,yBAAyB,CAAC,CAAC;MAC/B;MACA,IAAInF,OAAO,KAAKlH,SAAS,EAAE;QACvB;MACJ;MACA,MAAMrB,SAAS,GAAI,IAAI,CAACA,SAAS,GAAGuI,OAAO,KAAKlH,SAAS,GAAGJ,QAAQ,CAAC0M,cAAc,CAACpF,OAAO,CAAC,GAAG,IAAK;MACpG,IAAI,CAACvI,SAAS,EAAE;QACZhD,qDAAe,CAAC,kDAAkDuL,OAAO,uHAAuH,EAAE,IAAI,CAACvF,EAAE,CAAC;QAC1M;MACJ;MACA,IAAI,CAAC0K,yBAAyB,GAAG9L,2BAA2B,CAAC5B,SAAS,EAAEM,aAAa,EAAE0C,EAAE,CAAC;IAC9F,CAAC;IACD,IAAI,CAACU,4BAA4B,GAAG,MAAM;MACtC,MAAM;QAAEkK,0BAA0B;QAAE5K;MAAG,CAAC,GAAG,IAAI;MAC/C,IAAI4K,0BAA0B,EAAE;QAC5BA,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAI,CAACA,0BAA0B,GAAGlK,4BAA4B,CAACV,EAAE,CAAC;IACtE,CAAC;IACD,IAAI,CAAC3C,2BAA2B,GAAG,MAAM;MACrC,MAAM;QAAEwN,yBAAyB;QAAEzJ,aAAa;QAAE9D,aAAa;QAAEN,SAAS;QAAEgD;MAAG,CAAC,GAAG,IAAI;MACvF,IAAI,CAACoB,aAAa,IAAI,CAACpE,SAAS,EAAE;QAC9B;MACJ;MACA,IAAI6N,yBAAyB,EAAE;QAC3BA,yBAAyB,CAAC,CAAC;MAC/B;MACA,IAAI,CAACA,yBAAyB,GAAGxN,2BAA2B,CAACL,SAAS,EAAEM,aAAa,EAAE0C,EAAE,EAAEoB,aAAa,CAAC;IAC7G,CAAC;EACL;EACA0J,eAAeA,CAAA,EAAG;IACd,IAAI,CAAClM,2BAA2B,CAAC,CAAC;EACtC;EACAmM,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAAClQ,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIiQ,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAC9P,OAAO,CAAC,CAAC;IAClB;EACJ;EACA+P,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEtM,2BAA2B;MAAEoB;IAAG,CAAC,GAAG,IAAI;IAChDpF,wDAAc,CAACoF,EAAE,CAAC;IAClBpB,2BAA2B,CAAC,CAAC;EACjC;EACAuM,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAET;IAA0B,CAAC,GAAG,IAAI;IAC1C,IAAIA,yBAAyB,EAAE;MAC3BA,yBAAyB,CAAC,CAAC;IAC/B;EACJ;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAIxK,EAAE,EAAEM,EAAE;IACV,MAAM;MAAElB;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMqL,SAAS,GAAG,CAACnK,EAAE,GAAG,CAACN,EAAE,GAAG,IAAI,CAAC0K,cAAc,MAAM,IAAI,IAAI1K,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2K,EAAE,MAAM,IAAI,IAAIrK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGrG,wDAAY,CAACmF,EAAE,CAAC;IAChJ,IAAI,CAACoB,aAAa,GAAGpB,EAAE,CAACxB,OAAO,CAAC,oBAAoB6M,SAAS,GAAG,CAAC;IACjE,IAAI,IAAI,CAACG,SAAS,KAAKnN,SAAS,EAAE;MAC9B,IAAI,CAACmN,SAAS,GAAGtR,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,QAAQ,GAAG,OAAO;IACpE;EACJ;EACAuR,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAErK,aAAa;MAAEyI;IAAO,CAAC,GAAG,IAAI;IACtC;AACR;AACA;AACA;IACQ,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjBjO,uDAAG,CAAC,MAAM,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA,IAAIqG,aAAa,EAAE;MACfvF,uDAAgB,CAACuF,aAAa,EAAE,uBAAuB,EAAE,MAAM;QAC3D,IAAI,CAACjG,OAAO,CAACkD,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACO,2BAA2B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUO,kBAAkBA,CAAAuM,GAAA,EAAiC;IAAA,IAAAC,KAAA;IAAA,OAAA3M,yMAAA,YAAhCqD,KAAK,EAAEuJ,eAAe,GAAG,KAAK;MACnDD,KAAI,CAACxC,wBAAwB,GAAGyC,eAAe;MAC/C,MAAMD,KAAI,CAAC5Q,OAAO,CAACsH,KAAK,CAAC;MACzBsJ,KAAI,CAACxC,wBAAwB,GAAG,KAAK;IAAC,GAAA9J,KAAA,OAAAC,SAAA;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuM,WAAWA,CAACC,KAAK,GAAG,KAAK,EAAE;IACvB,IAAI,IAAI,CAACC,eAAe,IAAI,CAACD,KAAK,EAAE;MAChC,OAAO;QACHE,QAAQ,EAAE,IAAI,CAACD,eAAe;QAC9B7C,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+C,QAAQ,GAAG,IAAI,CAACjM,EAAE,CAACkM,UAAU;IACnC,MAAMhD,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG+C,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC5C,aAAc;IACvE,MAAM2C,QAAQ,GAAI,IAAI,CAACD,eAAe,GAAG7C,MAAM,GAAG,IAAI,CAAC8C,QAAQ,IAAI,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACgD,QAAS;IACrG,OAAO;MAAE9C,MAAM;MAAE8C;IAAS,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUjR,OAAOA,CAACsH,KAAK,EAAE;IAAA,IAAA8J,MAAA;IAAA,OAAAnN,yMAAA;MACjB,MAAMoN,MAAM,SAASD,MAAI,CAAClD,cAAc,CAACoD,IAAI,CAAC,CAAC;MAC/C,IAAIF,MAAI,CAAC/C,SAAS,EAAE;QAChBgD,MAAM,CAAC,CAAC;QACR;MACJ;MACA,MAAM;QAAEpM;MAAG,CAAC,GAAGmM,MAAI;MACnB,MAAM;QAAEjD,MAAM;QAAE8C;MAAS,CAAC,GAAGG,MAAI,CAACN,WAAW,CAAC,IAAI,CAAC;MACnD;AACR;AACA;AACA;AACA;MACQM,MAAI,CAACpD,QAAQ,CAACuD,IAAI,CAAC,CAAC;MACpBH,MAAI,CAAC/B,YAAY,SAAS3O,iEAAe,CAACuQ,QAAQ,EAAEhM,EAAE,EAAEmM,MAAI,CAACI,SAAS,EAAE,CAAC,kBAAkB,CAAC,EAAEJ,MAAI,CAACK,cAAc,EAAEtD,MAAM,CAAC;MAC1H,IAAI,CAACiD,MAAI,CAACrC,cAAc,EAAE;QACtBqC,MAAI,CAACzL,4BAA4B,CAAC,CAAC;MACvC;MACAyL,MAAI,CAAC9O,2BAA2B,CAAC,CAAC;MAClC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIvB,uDAAY,CAACkE,EAAE,CAAC,EAAE;QAClB,MAAM9D,qDAAS,CAACiQ,MAAI,CAAC/B,YAAY,CAAC;QAClC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,CAAC,MACI,IAAI,CAAC+B,MAAI,CAACnC,mBAAmB,EAAE;QAChC,MAAM5N,qDAAY,CAAC,CAAC;MACxB;MACA,MAAMrB,wDAAO,CAACoR,MAAI,EAAE,cAAc,EAAE1G,iBAAiB,EAAEqC,gBAAgB,EAAE;QACrEzF,KAAK,EAAEA,KAAK,IAAI8J,MAAI,CAAC9J,KAAK;QAC1BvF,IAAI,EAAEqP,MAAI,CAACrP,IAAI;QACfyI,OAAO,EAAE4G,MAAI,CAACnP,SAAS;QACvBiF,SAAS,EAAEkK,MAAI,CAAClK,SAAS;QACzBC,IAAI,EAAEiK,MAAI,CAACjK,IAAI;QACfC,KAAK,EAAEgK,MAAI,CAACX;MAChB,CAAC,CAAC;MACF;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIW,MAAI,CAAChD,wBAAwB,EAAE;QAC/BlO,wDAAoB,CAAC+E,EAAE,CAAC;MAC5B;MACAoM,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUjR,OAAOA,CAAAsR,GAAA,EAAAC,GAAA,EAA0C;IAAA,IAAAC,MAAA;IAAA,OAAA3N,yMAAA,YAAzC4N,IAAI,EAAEC,IAAI,EAAEC,oBAAoB,GAAG,IAAI;MACjD,MAAMV,MAAM,SAASO,MAAI,CAAC1D,cAAc,CAACoD,IAAI,CAAC,CAAC;MAC/C,MAAM;QAAEzB,0BAA0B;QAAEC;MAA0B,CAAC,GAAG8B,MAAI;MACtE,IAAIG,oBAAoB,IAAIH,MAAI,CAACvL,aAAa,EAAE;QAC5CuL,MAAI,CAACvL,aAAa,CAACjG,OAAO,CAACyR,IAAI,EAAEC,IAAI,EAAEC,oBAAoB,CAAC;MAChE;MACA,MAAMC,aAAa,SAAS5R,wDAAO,CAACwR,MAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE,cAAc,EAAEpF,iBAAiB,EAAEQ,gBAAgB,EAAE0E,MAAI,CAACtK,KAAK,CAAC;MACtH,IAAI0K,aAAa,EAAE;QACf,IAAInC,0BAA0B,EAAE;UAC5BA,0BAA0B,CAAC,CAAC;UAC5B+B,MAAI,CAAC/B,0BAA0B,GAAGvM,SAAS;QAC/C;QACA,IAAIwM,yBAAyB,EAAE;UAC3BA,yBAAyB,CAAC,CAAC;UAC3B8B,MAAI,CAAC9B,yBAAyB,GAAGxM,SAAS;QAC9C;QACA;AACZ;AACA;AACA;AACA;QACY,MAAM;UAAE2N;QAAS,CAAC,GAAGW,MAAI,CAACd,WAAW,CAAC,CAAC;QACvC,MAAMnQ,iEAAe,CAACsQ,QAAQ,EAAEW,MAAI,CAACvC,YAAY,CAAC;MACtD;MACAgC,MAAM,CAAC,CAAC;MACR,OAAOW,aAAa;IAAC,GAAA1N,KAAA,OAAAC,SAAA;EACzB;EACA;AACJ;AACA;EACU+B,gBAAgBA,CAAA,EAAG;IAAA,IAAA2L,MAAA;IAAA,OAAAhO,yMAAA;MACrB,OAAOgO,MAAI,CAAC5L,aAAa;IAAC;EAC9B;EACA;AACJ;AACA;EACI6L,YAAYA,CAAA,EAAG;IACX,OAAO7R,wDAAW,CAAC,IAAI,CAAC4E,EAAE,EAAE,sBAAsB,CAAC;EACvD;EACA;AACJ;AACA;EACIkN,aAAaA,CAAA,EAAG;IACZ,OAAO9R,wDAAW,CAAC,IAAI,CAAC4E,EAAE,EAAE,uBAAuB,CAAC;EACxD;EACAmN,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGlT,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEgQ,WAAW;MAAE9I,aAAa;MAAEuI,eAAe;MAAEzH,IAAI;MAAE0H,KAAK;MAAE0B,cAAc;MAAEvB;IAAU,CAAC,GAAG,IAAI;IACpG,MAAMsD,OAAO,GAAGjT,qDAAU,CAAC,SAAS,CAAC;IACrC,MAAMkT,WAAW,GAAG1D,KAAK,IAAI,CAACxI,aAAa;IAC3C,OAAQ/G,qDAAC,CAACE,iDAAI,EAAEgT,MAAM,CAACC,MAAM,CAAC;MAAErM,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,MAAM;MAAE,WAAW,EAAE,IAAI;MAAEsM,QAAQ,EAAE;IAAK,CAAC,EAAEnC,cAAc,EAAE;MAAEtE,KAAK,EAAE;QAC7J0G,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACC,YAAY;MACxC,CAAC;MAAEC,KAAK,EAAEL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvR,qDAAW,CAAC,IAAI,CAAC4R,QAAQ,CAAC,CAAC,EAAE;QAAE,CAACT,IAAI,GAAG,IAAI;QAAE,qBAAqB,EAAE,IAAI,CAAC3D,WAAW;QAAE,gBAAgB,EAAE,IAAI;QAAE,iBAAiB,EAAE4D,OAAO;QAAE,CAAC,gBAAgBnL,IAAI,EAAE,GAAG,IAAI;QAAE,CAAC5G,oDAAwB,GAAGyO,SAAS,KAAK,KAAK;QAAE,gBAAgB,EAAE,CAAC,CAAC3I;MAAc,CAAC,CAAC;MAAE0M,sBAAsB,EAAE5D,WAAW;MAAE6D,uBAAuB,EAAE7D,WAAW;MAAE8D,uBAAuB,EAAE9D,WAAW;MAAE+D,sBAAsB,EAAE/D,WAAW;MAAEgE,gBAAgB,EAAE,IAAI,CAACjE;IAAc,CAAC,CAAC,EAAE,CAAC7I,aAAa,IAAI/G,qDAAC,CAAC,cAAc,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEgN,QAAQ,EAAE,IAAI,CAAC5E,eAAe;MAAE6E,OAAO,EAAE,IAAI,CAAC5E,YAAY;MAAE6E,IAAI,EAAE;IAAW,CAAC,CAAC,EAAEhU,qDAAC,CAAC,KAAK,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEyM,KAAK,EAAE,qCAAqC;MAAEU,OAAO,EAAE3E,eAAe,GAAG,MAAM,IAAI,CAACxO,OAAO,CAAC,CAAC,GAAGkD;IAAU,CAAC,EAAEiP,WAAW,IAAIjT,qDAAC,CAAC,KAAK,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEyM,KAAK,EAAE,eAAe;MAAES,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEhU,qDAAC,CAAC,KAAK,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEyM,KAAK,EAAE,iBAAiB;MAAES,IAAI,EAAE;IAAU,CAAC,EAAEhU,qDAAC,CAAC,MAAM,EAAE;MAAE8G,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC,CAAC;EAClmC;EACA,IAAInB,EAAEA,CAAA,EAAG;IAAE,OAAOvF,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8T,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,SAAS,EAAE,CAAC,iBAAiB,CAAC;MAC9B,eAAe,EAAE,CAAC,iBAAiB,CAAC;MACpC,QAAQ,EAAE,CAAC,gBAAgB;IAC/B,CAAC;EAAE;AACP,CAAC;AACD,MAAMjE,aAAa,GAAG;EAClBkE,oBAAoB,EAAE,iBAAiB;EACvCC,qBAAqB,EAAE,kBAAkB;EACzCC,qBAAqB,EAAE,kBAAkB;EACzCC,oBAAoB,EAAE;AAC1B,CAAC;AACDvG,OAAO,CAACpB,KAAK,GAAG;EACZ4H,GAAG,EAAE1G,aAAa;EAClB2G,EAAE,EAAE1G;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-popover.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, a as isPlatform, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, n as focusFirstDescendant, g as dismiss, h as eventMethod, F as FOCUS_TRAP_DISABLE_CLASS } from './overlays-8Y2rA-ps.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { g as getElementRoot, r as raf, f as addEventListener, h as hasLazyBuild } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { e as deepReady, w as waitForMount } from './index-DfBA5ztX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './gesture-controller-BTEOs1at.js';\n\n/**\n * Returns the dimensions of the popover\n * arrow on `ios` mode. If arrow is disabled\n * returns (0, 0).\n */\nconst getArrowDimensions = (arrowEl) => {\n    if (!arrowEl) {\n        return { arrowWidth: 0, arrowHeight: 0 };\n    }\n    const { width, height } = arrowEl.getBoundingClientRect();\n    return { arrowWidth: width, arrowHeight: height };\n};\n/**\n * Returns the recommended dimensions of the popover\n * that takes into account whether or not the width\n * should match the trigger width.\n */\nconst getPopoverDimensions = (size, contentEl, triggerEl) => {\n    const contentDimentions = contentEl.getBoundingClientRect();\n    const contentHeight = contentDimentions.height;\n    let contentWidth = contentDimentions.width;\n    if (size === 'cover' && triggerEl) {\n        const triggerDimensions = triggerEl.getBoundingClientRect();\n        contentWidth = triggerDimensions.width;\n    }\n    return {\n        contentWidth,\n        contentHeight,\n    };\n};\nconst configureDismissInteraction = (triggerEl, triggerAction, popoverEl, parentPopoverEl) => {\n    let dismissCallbacks = [];\n    const root = getElementRoot(parentPopoverEl);\n    const parentContentEl = root.querySelector('.popover-content');\n    switch (triggerAction) {\n        case 'hover':\n            dismissCallbacks = [\n                {\n                    /**\n                     * Do not use mouseover here\n                     * as this will causes the event to\n                     * be dispatched on each underlying\n                     * element rather than on the popover\n                     * content as a whole.\n                     */\n                    eventName: 'mouseenter',\n                    callback: (ev) => {\n                        /**\n                         * Do not dismiss the popover is we\n                         * are hovering over its trigger.\n                         * This would be easier if we used mouseover\n                         * but this would cause the event to be dispatched\n                         * more often than we would like, potentially\n                         * causing performance issues.\n                         */\n                        const element = document.elementFromPoint(ev.clientX, ev.clientY);\n                        if (element === triggerEl) {\n                            return;\n                        }\n                        popoverEl.dismiss(undefined, undefined, false);\n                    },\n                },\n            ];\n            break;\n        case 'context-menu':\n        case 'click':\n        default:\n            dismissCallbacks = [\n                {\n                    eventName: 'click',\n                    callback: (ev) => {\n                        /**\n                         * Do not dismiss the popover is we\n                         * are hovering over its trigger.\n                         */\n                        const target = ev.target;\n                        const closestTrigger = target.closest('[data-ion-popover-trigger]');\n                        if (closestTrigger === triggerEl) {\n                            /**\n                             * stopPropagation here so if the\n                             * popover has dismissOnSelect=\"true\"\n                             * the popover does not dismiss since\n                             * we just clicked a trigger element.\n                             */\n                            ev.stopPropagation();\n                            return;\n                        }\n                        popoverEl.dismiss(undefined, undefined, false);\n                    },\n                },\n            ];\n            break;\n    }\n    dismissCallbacks.forEach(({ eventName, callback }) => parentContentEl.addEventListener(eventName, callback));\n    return () => {\n        dismissCallbacks.forEach(({ eventName, callback }) => parentContentEl.removeEventListener(eventName, callback));\n    };\n};\n/**\n * Configures the triggerEl to respond\n * to user interaction based upon the triggerAction\n * prop that devs have defined.\n */\nconst configureTriggerInteraction = (triggerEl, triggerAction, popoverEl) => {\n    let triggerCallbacks = [];\n    /**\n     * Based upon the kind of trigger interaction\n     * the user wants, we setup the correct event\n     * listeners.\n     */\n    switch (triggerAction) {\n        case 'hover':\n            let hoverTimeout;\n            triggerCallbacks = [\n                {\n                    eventName: 'mouseenter',\n                    callback: async (ev) => {\n                        ev.stopPropagation();\n                        if (hoverTimeout) {\n                            clearTimeout(hoverTimeout);\n                        }\n                        /**\n                         * Hovering over a trigger should not\n                         * immediately open the next popover.\n                         */\n                        hoverTimeout = setTimeout(() => {\n                            raf(() => {\n                                popoverEl.presentFromTrigger(ev);\n                                hoverTimeout = undefined;\n                            });\n                        }, 100);\n                    },\n                },\n                {\n                    eventName: 'mouseleave',\n                    callback: (ev) => {\n                        if (hoverTimeout) {\n                            clearTimeout(hoverTimeout);\n                        }\n                        /**\n                         * If mouse is over another popover\n                         * that is not this popover then we should\n                         * close this popover.\n                         */\n                        const target = ev.relatedTarget;\n                        if (!target) {\n                            return;\n                        }\n                        if (target.closest('ion-popover') !== popoverEl) {\n                            popoverEl.dismiss(undefined, undefined, false);\n                        }\n                    },\n                },\n                {\n                    /**\n                     * stopPropagation here prevents the popover\n                     * from dismissing when dismiss-on-select=\"true\".\n                     */\n                    eventName: 'click',\n                    callback: (ev) => ev.stopPropagation(),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n        case 'context-menu':\n            triggerCallbacks = [\n                {\n                    eventName: 'contextmenu',\n                    callback: (ev) => {\n                        /**\n                         * Prevents the platform context\n                         * menu from appearing.\n                         */\n                        ev.preventDefault();\n                        popoverEl.presentFromTrigger(ev);\n                    },\n                },\n                {\n                    eventName: 'click',\n                    callback: (ev) => ev.stopPropagation(),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n        case 'click':\n        default:\n            triggerCallbacks = [\n                {\n                    /**\n                     * Do not do a stopPropagation() here\n                     * because if you had two click triggers\n                     * then clicking the first trigger and then\n                     * clicking the second trigger would not cause\n                     * the first popover to dismiss.\n                     */\n                    eventName: 'click',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n    }\n    triggerCallbacks.forEach(({ eventName, callback }) => triggerEl.addEventListener(eventName, callback));\n    triggerEl.setAttribute('data-ion-popover-trigger', 'true');\n    return () => {\n        triggerCallbacks.forEach(({ eventName, callback }) => triggerEl.removeEventListener(eventName, callback));\n        triggerEl.removeAttribute('data-ion-popover-trigger');\n    };\n};\n/**\n * Returns the index of an ion-item in an array of ion-items.\n */\nconst getIndexOfItem = (items, item) => {\n    if (!item || item.tagName !== 'ION-ITEM') {\n        return -1;\n    }\n    return items.findIndex((el) => el === item);\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the next ion-item relative to the focused one or\n * undefined.\n */\nconst getNextItem = (items, currentItem) => {\n    const currentItemIndex = getIndexOfItem(items, currentItem);\n    return items[currentItemIndex + 1];\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the previous ion-item relative to the focused one or\n * undefined.\n */\nconst getPrevItem = (items, currentItem) => {\n    const currentItemIndex = getIndexOfItem(items, currentItem);\n    return items[currentItemIndex - 1];\n};\n/** Focus the internal button of the ion-item */\nconst focusItem = (item) => {\n    const root = getElementRoot(item);\n    const button = root.querySelector('button');\n    if (button) {\n        raf(() => button.focus());\n    }\n};\n/**\n * Returns `true` if `el` has been designated\n * as a trigger element for an ion-popover.\n */\nconst isTriggerElement = (el) => el.hasAttribute('data-ion-popover-trigger');\nconst configureKeyboardInteraction = (popoverEl) => {\n    const callback = async (ev) => {\n        var _a;\n        const activeElement = document.activeElement;\n        let items = [];\n        const targetTagName = (_a = ev.target) === null || _a === void 0 ? void 0 : _a.tagName;\n        /**\n         * Only handle custom keyboard interactions for the host popover element\n         * and children ion-item elements.\n         */\n        if (targetTagName !== 'ION-POPOVER' && targetTagName !== 'ION-ITEM') {\n            return;\n        }\n        /**\n         * Complex selectors with :not() are :not supported\n         * in older versions of Chromium so we need to do a\n         * try/catch here so errors are not thrown.\n         */\n        try {\n            /**\n             * Select all ion-items that are not children of child popovers.\n             * i.e. only select ion-item elements that are part of this popover\n             */\n            items = Array.from(popoverEl.querySelectorAll('ion-item:not(ion-popover ion-popover *):not([disabled])'));\n            /* eslint-disable-next-line */\n        }\n        catch (_b) { }\n        switch (ev.key) {\n            /**\n             * If we are in a child popover\n             * then pressing the left arrow key\n             * should close this popover and move\n             * focus to the popover that presented\n             * this one.\n             */\n            case 'ArrowLeft':\n                const parentPopover = await popoverEl.getParentPopover();\n                if (parentPopover) {\n                    popoverEl.dismiss(undefined, undefined, false);\n                }\n                break;\n            /**\n             * ArrowDown should move focus to the next focusable ion-item.\n             */\n            case 'ArrowDown':\n                // Disable movement/scroll with keyboard\n                ev.preventDefault();\n                const nextItem = getNextItem(items, activeElement);\n                if (nextItem !== undefined) {\n                    focusItem(nextItem);\n                }\n                break;\n            /**\n             * ArrowUp should move focus to the previous focusable ion-item.\n             */\n            case 'ArrowUp':\n                // Disable movement/scroll with keyboard\n                ev.preventDefault();\n                const prevItem = getPrevItem(items, activeElement);\n                if (prevItem !== undefined) {\n                    focusItem(prevItem);\n                }\n                break;\n            /**\n             * Home should move focus to the first focusable ion-item.\n             */\n            case 'Home':\n                ev.preventDefault();\n                const firstItem = items[0];\n                if (firstItem !== undefined) {\n                    focusItem(firstItem);\n                }\n                break;\n            /**\n             * End should move focus to the last focusable ion-item.\n             */\n            case 'End':\n                ev.preventDefault();\n                const lastItem = items[items.length - 1];\n                if (lastItem !== undefined) {\n                    focusItem(lastItem);\n                }\n                break;\n            /**\n             * ArrowRight, Spacebar, or Enter should activate\n             * the currently focused trigger item to open a\n             * popover if the element is a trigger item.\n             */\n            case 'ArrowRight':\n            case ' ':\n            case 'Enter':\n                if (activeElement && isTriggerElement(activeElement)) {\n                    const rightEvent = new CustomEvent('ionPopoverActivateTrigger');\n                    activeElement.dispatchEvent(rightEvent);\n                }\n                break;\n        }\n    };\n    popoverEl.addEventListener('keydown', callback);\n    return () => popoverEl.removeEventListener('keydown', callback);\n};\n/**\n * Positions a popover by taking into account\n * the reference point, preferred side, alignment\n * and viewport dimensions.\n */\nconst getPopoverPosition = (isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, triggerEl, event) => {\n    var _a;\n    let referenceCoordinates = {\n        top: 0,\n        left: 0,\n        width: 0,\n        height: 0,\n    };\n    /**\n     * Calculate position relative to the\n     * x-y coordinates in the event that\n     * was passed in\n     */\n    switch (reference) {\n        case 'event':\n            if (!event) {\n                return defaultPosition;\n            }\n            const mouseEv = event;\n            referenceCoordinates = {\n                top: mouseEv.clientY,\n                left: mouseEv.clientX,\n                width: 1,\n                height: 1,\n            };\n            break;\n        /**\n         * Calculate position relative to the bounding\n         * box on either the trigger element\n         * specified via the `trigger` prop or\n         * the target specified on the event\n         * that was passed in.\n         */\n        case 'trigger':\n        default:\n            const customEv = event;\n            /**\n             * ionShadowTarget is used when we need to align the\n             * popover with an element inside of the shadow root\n             * of an Ionic component. Ex: Presenting a popover\n             * by clicking on the collapsed indicator inside\n             * of `ion-breadcrumb` and centering it relative\n             * to the indicator rather than `ion-breadcrumb`\n             * as a whole.\n             */\n            const actualTriggerEl = (triggerEl ||\n                ((_a = customEv === null || customEv === void 0 ? void 0 : customEv.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) ||\n                (customEv === null || customEv === void 0 ? void 0 : customEv.target));\n            if (!actualTriggerEl) {\n                return defaultPosition;\n            }\n            const triggerBoundingBox = actualTriggerEl.getBoundingClientRect();\n            referenceCoordinates = {\n                top: triggerBoundingBox.top,\n                left: triggerBoundingBox.left,\n                width: triggerBoundingBox.width,\n                height: triggerBoundingBox.height,\n            };\n            break;\n    }\n    /**\n     * Get top/left offset that would allow\n     * popover to be positioned on the\n     * preferred side of the reference.\n     */\n    const coordinates = calculatePopoverSide(side, referenceCoordinates, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL);\n    /**\n     * Get the top/left adjustments that\n     * would allow the popover content\n     * to have the correct alignment.\n     */\n    const alignedCoordinates = calculatePopoverAlign(align, side, referenceCoordinates, contentWidth, contentHeight);\n    const top = coordinates.top + alignedCoordinates.top;\n    const left = coordinates.left + alignedCoordinates.left;\n    const { arrowTop, arrowLeft } = calculateArrowPosition(side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL);\n    const { originX, originY } = calculatePopoverOrigin(side, align, isRTL);\n    return { top, left, referenceCoordinates, arrowTop, arrowLeft, originX, originY };\n};\n/**\n * Determines the transform-origin\n * of the popover animation so that it\n * is in line with what the side and alignment\n * prop values are. Currently only used\n * with the MD animation.\n */\nconst calculatePopoverOrigin = (side, align, isRTL) => {\n    switch (side) {\n        case 'top':\n            return { originX: getOriginXAlignment(align), originY: 'bottom' };\n        case 'bottom':\n            return { originX: getOriginXAlignment(align), originY: 'top' };\n        case 'left':\n            return { originX: 'right', originY: getOriginYAlignment(align) };\n        case 'right':\n            return { originX: 'left', originY: getOriginYAlignment(align) };\n        case 'start':\n            return { originX: isRTL ? 'left' : 'right', originY: getOriginYAlignment(align) };\n        case 'end':\n            return { originX: isRTL ? 'right' : 'left', originY: getOriginYAlignment(align) };\n    }\n};\nconst getOriginXAlignment = (align) => {\n    switch (align) {\n        case 'start':\n            return 'left';\n        case 'center':\n            return 'center';\n        case 'end':\n            return 'right';\n    }\n};\nconst getOriginYAlignment = (align) => {\n    switch (align) {\n        case 'start':\n            return 'top';\n        case 'center':\n            return 'center';\n        case 'end':\n            return 'bottom';\n    }\n};\n/**\n * Calculates where the arrow positioning\n * should be relative to the popover content.\n */\nconst calculateArrowPosition = (side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL) => {\n    /**\n     * Note: When side is left, right, start, or end, the arrow is\n     * been rotated using a `transform`, so to move the arrow up or down\n     * by its dimension, you need to use `arrowWidth`.\n     */\n    const leftPosition = {\n        arrowTop: top + contentHeight / 2 - arrowWidth / 2,\n        arrowLeft: left + contentWidth - arrowWidth / 2,\n    };\n    /**\n     * Move the arrow to the left by arrowWidth and then\n     * again by half of its width because we have rotated\n     * the arrow using a transform.\n     */\n    const rightPosition = { arrowTop: top + contentHeight / 2 - arrowWidth / 2, arrowLeft: left - arrowWidth * 1.5 };\n    switch (side) {\n        case 'top':\n            return { arrowTop: top + contentHeight, arrowLeft: left + contentWidth / 2 - arrowWidth / 2 };\n        case 'bottom':\n            return { arrowTop: top - arrowHeight, arrowLeft: left + contentWidth / 2 - arrowWidth / 2 };\n        case 'left':\n            return leftPosition;\n        case 'right':\n            return rightPosition;\n        case 'start':\n            return isRTL ? rightPosition : leftPosition;\n        case 'end':\n            return isRTL ? leftPosition : rightPosition;\n        default:\n            return { arrowTop: 0, arrowLeft: 0 };\n    }\n};\n/**\n * Calculates the required top/left\n * values needed to position the popover\n * content on the side specified in the\n * `side` prop.\n */\nconst calculatePopoverSide = (side, triggerBoundingBox, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL) => {\n    const sideLeft = {\n        top: triggerBoundingBox.top,\n        left: triggerBoundingBox.left - contentWidth - arrowWidth,\n    };\n    const sideRight = {\n        top: triggerBoundingBox.top,\n        left: triggerBoundingBox.left + triggerBoundingBox.width + arrowWidth,\n    };\n    switch (side) {\n        case 'top':\n            return {\n                top: triggerBoundingBox.top - contentHeight - arrowHeight,\n                left: triggerBoundingBox.left,\n            };\n        case 'right':\n            return sideRight;\n        case 'bottom':\n            return {\n                top: triggerBoundingBox.top + triggerBoundingBox.height + arrowHeight,\n                left: triggerBoundingBox.left,\n            };\n        case 'left':\n            return sideLeft;\n        case 'start':\n            return isRTL ? sideRight : sideLeft;\n        case 'end':\n            return isRTL ? sideLeft : sideRight;\n    }\n};\n/**\n * Calculates the required top/left\n * offset values needed to provide the\n * correct alignment regardless while taking\n * into account the side the popover is on.\n */\nconst calculatePopoverAlign = (align, side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (align) {\n        case 'center':\n            return calculatePopoverCenterAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n        case 'end':\n            return calculatePopoverEndAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n        case 'start':\n        default:\n            return { top: 0, left: 0 };\n    }\n};\n/**\n * Calculate the end alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverEndAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (side) {\n        case 'start':\n        case 'end':\n        case 'left':\n        case 'right':\n            return {\n                top: -(contentHeight - triggerBoundingBox.height),\n                left: 0,\n            };\n        case 'top':\n        case 'bottom':\n        default:\n            return {\n                top: 0,\n                left: -(contentWidth - triggerBoundingBox.width),\n            };\n    }\n};\n/**\n * Calculate the center alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverCenterAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (side) {\n        case 'start':\n        case 'end':\n        case 'left':\n        case 'right':\n            return {\n                top: -(contentHeight / 2 - triggerBoundingBox.height / 2),\n                left: 0,\n            };\n        case 'top':\n        case 'bottom':\n        default:\n            return {\n                top: 0,\n                left: -(contentWidth / 2 - triggerBoundingBox.width / 2),\n            };\n    }\n};\n/**\n * Adjusts popover positioning coordinates\n * such that popover does not appear offscreen\n * or overlapping safe area bounds.\n */\nconst calculateWindowAdjustment = (side, coordTop, coordLeft, bodyPadding, bodyWidth, bodyHeight, contentWidth, contentHeight, safeAreaMargin, contentOriginX, contentOriginY, triggerCoordinates, coordArrowTop = 0, coordArrowLeft = 0, arrowHeight = 0) => {\n    let arrowTop = coordArrowTop;\n    const arrowLeft = coordArrowLeft;\n    let left = coordLeft;\n    let top = coordTop;\n    let bottom;\n    let originX = contentOriginX;\n    let originY = contentOriginY;\n    let checkSafeAreaLeft = false;\n    let checkSafeAreaRight = false;\n    const triggerTop = triggerCoordinates\n        ? triggerCoordinates.top + triggerCoordinates.height\n        : bodyHeight / 2 - contentHeight / 2;\n    const triggerHeight = triggerCoordinates ? triggerCoordinates.height : 0;\n    let addPopoverBottomClass = false;\n    /**\n     * Adjust popover so it does not\n     * go off the left of the screen.\n     */\n    if (left < bodyPadding + safeAreaMargin) {\n        left = bodyPadding;\n        checkSafeAreaLeft = true;\n        originX = 'left';\n        /**\n         * Adjust popover so it does not\n         * go off the right of the screen.\n         */\n    }\n    else if (contentWidth + bodyPadding + left + safeAreaMargin > bodyWidth) {\n        checkSafeAreaRight = true;\n        left = bodyWidth - contentWidth - bodyPadding;\n        originX = 'right';\n    }\n    /**\n     * Adjust popover so it does not\n     * go off the top of the screen.\n     * If popover is on the left or the right of\n     * the trigger, then we should not adjust top\n     * margins.\n     */\n    if (triggerTop + triggerHeight + contentHeight > bodyHeight && (side === 'top' || side === 'bottom')) {\n        if (triggerTop - contentHeight > 0) {\n            /**\n             * While we strive to align the popover with the trigger\n             * on smaller screens this is not always possible. As a result,\n             * we adjust the popover up so that it does not hang\n             * off the bottom of the screen. However, we do not want to move\n             * the popover up so much that it goes off the top of the screen.\n             *\n             * We chose 12 here so that the popover position looks a bit nicer as\n             * it is not right up against the edge of the screen.\n             */\n            top = Math.max(12, triggerTop - contentHeight - triggerHeight - (arrowHeight - 1));\n            arrowTop = top + contentHeight;\n            originY = 'bottom';\n            addPopoverBottomClass = true;\n            /**\n             * If not enough room for popover to appear\n             * above trigger, then cut it off.\n             */\n        }\n        else {\n            bottom = bodyPadding;\n        }\n    }\n    return {\n        top,\n        left,\n        bottom,\n        originX,\n        originY,\n        checkSafeAreaLeft,\n        checkSafeAreaRight,\n        arrowTop,\n        arrowLeft,\n        addPopoverBottomClass,\n    };\n};\nconst shouldShowArrow = (side, didAdjustBounds = false, ev, trigger) => {\n    /**\n     * If no event provided and\n     * we do not have a trigger,\n     * then this popover was likely\n     * presented via the popoverController\n     * or users called `present` manually.\n     * In this case, the arrow should not be\n     * shown as we do not have a reference.\n     */\n    if (!ev && !trigger) {\n        return false;\n    }\n    /**\n     * If popover is on the left or the right\n     * of a trigger, but we needed to adjust the\n     * popover due to screen bounds, then we should\n     * hide the arrow as it will never be pointing\n     * at the trigger.\n     */\n    if (side !== 'top' && side !== 'bottom' && didAdjustBounds) {\n        return false;\n    }\n    return true;\n};\n\nconst POPOVER_IOS_BODY_PADDING = 5;\n/**\n * iOS Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst iosEnterAnimation = (baseEl, opts) => {\n    var _a;\n    const { event: ev, size, trigger, reference, side, align } = opts;\n    const doc = baseEl.ownerDocument;\n    const isRTL = doc.dir === 'rtl';\n    const bodyWidth = doc.defaultView.innerWidth;\n    const bodyHeight = doc.defaultView.innerHeight;\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const arrowEl = root.querySelector('.popover-arrow');\n    const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n    const { contentWidth, contentHeight } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n    const { arrowWidth, arrowHeight } = getArrowDimensions(arrowEl);\n    const defaultPosition = {\n        top: bodyHeight / 2 - contentHeight / 2,\n        left: bodyWidth / 2 - contentWidth / 2,\n        originX: isRTL ? 'right' : 'left',\n        originY: 'top',\n    };\n    const results = getPopoverPosition(isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, trigger, ev);\n    const padding = size === 'cover' ? 0 : POPOVER_IOS_BODY_PADDING;\n    const margin = size === 'cover' ? 0 : 25;\n    const { originX, originY, top, left, bottom, checkSafeAreaLeft, checkSafeAreaRight, arrowTop, arrowLeft, addPopoverBottomClass, } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, margin, results.originX, results.originY, results.referenceCoordinates, results.arrowTop, results.arrowLeft, arrowHeight);\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    backdropAnimation\n        .addElement(root.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    // In Chromium, if the wrapper animates, the backdrop filter doesn't work.\n    // The Chromium team stated that this behavior is expected and not a bug. The element animating opacity creates a backdrop root for the backdrop-filter.\n    // To get around this, instead of animating the wrapper, animate both the arrow and content.\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=1148826\n    contentAnimation\n        .addElement(root.querySelector('.popover-arrow'))\n        .addElement(root.querySelector('.popover-content'))\n        .fromTo('opacity', 0.01, 1);\n    // TODO(FW-4376) Ensure that arrow also blurs when translucent\n    return baseAnimation\n        .easing('ease')\n        .duration(100)\n        .beforeAddWrite(() => {\n        if (size === 'cover') {\n            baseEl.style.setProperty('--width', `${contentWidth}px`);\n        }\n        if (addPopoverBottomClass) {\n            baseEl.classList.add('popover-bottom');\n        }\n        if (bottom !== undefined) {\n            contentEl.style.setProperty('bottom', `${bottom}px`);\n        }\n        const safeAreaLeft = ' + var(--ion-safe-area-left, 0)';\n        const safeAreaRight = ' - var(--ion-safe-area-right, 0)';\n        let leftValue = `${left}px`;\n        if (checkSafeAreaLeft) {\n            leftValue = `${left}px${safeAreaLeft}`;\n        }\n        if (checkSafeAreaRight) {\n            leftValue = `${left}px${safeAreaRight}`;\n        }\n        contentEl.style.setProperty('top', `calc(${top}px + var(--offset-y, 0))`);\n        contentEl.style.setProperty('left', `calc(${leftValue} + var(--offset-x, 0))`);\n        contentEl.style.setProperty('transform-origin', `${originY} ${originX}`);\n        if (arrowEl !== null) {\n            const didAdjustBounds = results.top !== top || results.left !== left;\n            const showArrow = shouldShowArrow(side, didAdjustBounds, ev, trigger);\n            if (showArrow) {\n                arrowEl.style.setProperty('top', `calc(${arrowTop}px + var(--offset-y, 0))`);\n                arrowEl.style.setProperty('left', `calc(${arrowLeft}px + var(--offset-x, 0))`);\n            }\n            else {\n                arrowEl.style.setProperty('display', 'none');\n            }\n        }\n    })\n        .addAnimation([backdropAnimation, contentAnimation]);\n};\n\n/**\n * iOS Popover Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const arrowEl = root.querySelector('.popover-arrow');\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    contentAnimation\n        .addElement(root.querySelector('.popover-arrow'))\n        .addElement(root.querySelector('.popover-content'))\n        .fromTo('opacity', 0.99, 0);\n    return baseAnimation\n        .easing('ease')\n        .afterAddWrite(() => {\n        baseEl.style.removeProperty('--width');\n        baseEl.classList.remove('popover-bottom');\n        contentEl.style.removeProperty('top');\n        contentEl.style.removeProperty('left');\n        contentEl.style.removeProperty('bottom');\n        contentEl.style.removeProperty('transform-origin');\n        if (arrowEl) {\n            arrowEl.style.removeProperty('top');\n            arrowEl.style.removeProperty('left');\n            arrowEl.style.removeProperty('display');\n        }\n    })\n        .duration(300)\n        .addAnimation([backdropAnimation, contentAnimation]);\n};\n\nconst POPOVER_MD_BODY_PADDING = 12;\n/**\n * Md Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst mdEnterAnimation = (baseEl, opts) => {\n    var _a;\n    const { event: ev, size, trigger, reference, side, align } = opts;\n    const doc = baseEl.ownerDocument;\n    const isRTL = doc.dir === 'rtl';\n    const bodyWidth = doc.defaultView.innerWidth;\n    const bodyHeight = doc.defaultView.innerHeight;\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n    const { contentWidth, contentHeight } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n    const defaultPosition = {\n        top: bodyHeight / 2 - contentHeight / 2,\n        left: bodyWidth / 2 - contentWidth / 2,\n        originX: isRTL ? 'right' : 'left',\n        originY: 'top',\n    };\n    const results = getPopoverPosition(isRTL, contentWidth, contentHeight, 0, 0, reference, side, align, defaultPosition, trigger, ev);\n    const padding = size === 'cover' ? 0 : POPOVER_MD_BODY_PADDING;\n    const { originX, originY, top, left, bottom } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, 0, results.originX, results.originY, results.referenceCoordinates);\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    const viewportAnimation = createAnimation();\n    backdropAnimation\n        .addElement(root.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).duration(150).fromTo('opacity', 0.01, 1);\n    contentAnimation\n        .addElement(contentEl)\n        .beforeStyles({\n        top: `calc(${top}px + var(--offset-y, 0px))`,\n        left: `calc(${left}px + var(--offset-x, 0px))`,\n        'transform-origin': `${originY} ${originX}`,\n    })\n        .beforeAddWrite(() => {\n        if (bottom !== undefined) {\n            contentEl.style.setProperty('bottom', `${bottom}px`);\n        }\n    })\n        .fromTo('transform', 'scale(0.8)', 'scale(1)');\n    viewportAnimation.addElement(root.querySelector('.popover-viewport')).fromTo('opacity', 0.01, 1);\n    return baseAnimation\n        .easing('cubic-bezier(0.36,0.66,0.04,1)')\n        .duration(300)\n        .beforeAddWrite(() => {\n        if (size === 'cover') {\n            baseEl.style.setProperty('--width', `${contentWidth}px`);\n        }\n        if (originY === 'bottom') {\n            baseEl.classList.add('popover-bottom');\n        }\n    })\n        .addAnimation([backdropAnimation, wrapperAnimation, contentAnimation, viewportAnimation]);\n};\n\n/**\n * Md Popover Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).fromTo('opacity', 0.99, 0);\n    return baseAnimation\n        .easing('ease')\n        .afterAddWrite(() => {\n        baseEl.style.removeProperty('--width');\n        baseEl.classList.remove('popover-bottom');\n        contentEl.style.removeProperty('top');\n        contentEl.style.removeProperty('left');\n        contentEl.style.removeProperty('bottom');\n        contentEl.style.removeProperty('transform-origin');\n    })\n        .duration(150)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst popoverIosCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:\\\"\\\";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}\";\n\nconst popoverMdCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}\";\n\nconst Popover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionPopoverDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionPopoverWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionPopoverWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionPopoverDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.ionMount = createEvent(this, \"ionMount\", 7);\n        this.parentPopover = null;\n        this.coreDelegate = CoreDelegate();\n        this.lockController = createLockController();\n        this.inline = false;\n        this.focusDescendantOnPresent = false;\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * If `true`, the popover will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, a backdrop will be displayed behind the popover.\n         * This property controls whether or not the backdrop\n         * darkens the screen when the popover is presented.\n         * It does not control whether or not the backdrop\n         * is active or present in the DOM.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the popover will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the popover will animate.\n         */\n        this.animated = true;\n        /**\n         * Describes what kind of interaction with the trigger that\n         * should cause the popover to open. Does not apply when the `trigger`\n         * property is `undefined`.\n         * If `\"click\"`, the popover will be presented when the trigger is left clicked.\n         * If `\"hover\"`, the popover will be presented when a pointer hovers over the trigger.\n         * If `\"context-menu\"`, the popover will be presented when the trigger is right\n         * clicked on desktop and long pressed on mobile. This will also prevent your\n         * device's normal context menu from appearing.\n         */\n        this.triggerAction = 'click';\n        /**\n         * Describes how to calculate the popover width.\n         * If `\"cover\"`, the popover width will match the width of the trigger.\n         * If `\"auto\"`, the popover width will be set to a static default value.\n         */\n        this.size = 'auto';\n        /**\n         * If `true`, the popover will be automatically\n         * dismissed when the content has been clicked.\n         */\n        this.dismissOnSelect = false;\n        /**\n         * Describes what to position the popover relative to.\n         * If `\"trigger\"`, the popover will be positioned relative\n         * to the trigger button. If passing in an event, this is\n         * determined via event.target.\n         * If `\"event\"`, the popover will be positioned relative\n         * to the x/y coordinates of the trigger action. If passing\n         * in an event, this is determined via event.clientX and event.clientY.\n         */\n        this.reference = 'trigger';\n        /**\n         * Describes which side of the `reference` point to position\n         * the popover on. The `\"start\"` and `\"end\"` values are RTL-aware,\n         * and the `\"left\"` and `\"right\"` values are not.\n         */\n        this.side = 'bottom';\n        /**\n         * If `true`, the popover will display an arrow that points at the\n         * `reference` when running in `ios` mode. Does not apply in `md` mode.\n         */\n        this.arrow = true;\n        /**\n         * If `true`, the popover will open. If `false`, the popover will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the popoverController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the popover dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        /**\n         * @internal\n         *\n         * If `true` the popover will not register its own keyboard event handlers.\n         * This allows the contents of the popover to handle their own keyboard interactions.\n         *\n         * If `false`, the popover will register its own keyboard event handlers for\n         * navigating `ion-list` items within a popover (up/down/home/<USER>/etc.).\n         * This will also cancel browser keyboard event bindings to prevent scroll\n         * behavior in a popover using a list of items.\n         */\n        this.keyboardEvents = false;\n        /**\n         * If `true`, focus will not be allowed to move outside of this overlay.\n         * If `false`, focus will be allowed to move outside of the overlay.\n         *\n         * In most scenarios this property should remain set to `true`. Setting\n         * this property to `false` can cause severe accessibility issues as users\n         * relying on assistive technologies may be able to move focus into\n         * a confusing state. We recommend only setting this to `false` when\n         * absolutely necessary.\n         *\n         * Developers may want to consider disabling focus trapping if this\n         * overlay presents a non-Ionic overlay from a 3rd party library.\n         * Developers would disable focus trapping on the Ionic overlay\n         * when presenting the 3rd party overlay and then re-enable\n         * focus trapping when dismissing the 3rd party overlay and moving\n         * focus back to the Ionic overlay.\n         */\n        this.focusTrap = true;\n        /**\n         * If `true`, the component passed into `ion-popover` will\n         * automatically be mounted when the popover is created. The\n         * component will remain mounted even when the popover is dismissed.\n         * However, the component will be destroyed when the popover is\n         * destroyed. This property is not reactive and should only be\n         * used when initially creating a popover.\n         *\n         * Note: This feature only applies to inline popovers in JavaScript\n         * frameworks such as Angular, React, and Vue.\n         */\n        this.keepContentsMounted = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.onLifecycle = (modalEvent) => {\n            const el = this.usersElement;\n            const name = LIFECYCLE_MAP[modalEvent.type];\n            if (el && name) {\n                const event = new CustomEvent(name, {\n                    bubbles: false,\n                    cancelable: false,\n                    detail: modalEvent.detail,\n                });\n                el.dispatchEvent(event);\n            }\n        };\n        this.configureTriggerInteraction = () => {\n            const { trigger, triggerAction, el, destroyTriggerInteraction } = this;\n            if (destroyTriggerInteraction) {\n                destroyTriggerInteraction();\n            }\n            if (trigger === undefined) {\n                return;\n            }\n            const triggerEl = (this.triggerEl = trigger !== undefined ? document.getElementById(trigger) : null);\n            if (!triggerEl) {\n                printIonWarning(`[ion-popover] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on ion-popover.`, this.el);\n                return;\n            }\n            this.destroyTriggerInteraction = configureTriggerInteraction(triggerEl, triggerAction, el);\n        };\n        this.configureKeyboardInteraction = () => {\n            const { destroyKeyboardInteraction, el } = this;\n            if (destroyKeyboardInteraction) {\n                destroyKeyboardInteraction();\n            }\n            this.destroyKeyboardInteraction = configureKeyboardInteraction(el);\n        };\n        this.configureDismissInteraction = () => {\n            const { destroyDismissInteraction, parentPopover, triggerAction, triggerEl, el } = this;\n            if (!parentPopover || !triggerEl) {\n                return;\n            }\n            if (destroyDismissInteraction) {\n                destroyDismissInteraction();\n            }\n            this.destroyDismissInteraction = configureDismissInteraction(triggerEl, triggerAction, el, parentPopover);\n        };\n    }\n    onTriggerChange() {\n        this.configureTriggerInteraction();\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    connectedCallback() {\n        const { configureTriggerInteraction, el } = this;\n        prepareOverlay(el);\n        configureTriggerInteraction();\n    }\n    disconnectedCallback() {\n        const { destroyTriggerInteraction } = this;\n        if (destroyTriggerInteraction) {\n            destroyTriggerInteraction();\n        }\n    }\n    componentWillLoad() {\n        var _a, _b;\n        const { el } = this;\n        const popoverId = (_b = (_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : setOverlayId(el);\n        this.parentPopover = el.closest(`ion-popover:not(#${popoverId})`);\n        if (this.alignment === undefined) {\n            this.alignment = getIonMode(this) === 'ios' ? 'center' : 'start';\n        }\n    }\n    componentDidLoad() {\n        const { parentPopover, isOpen } = this;\n        /**\n         * If popover was rendered with isOpen=\"true\"\n         * then we should open popover immediately.\n         */\n        if (isOpen === true) {\n            raf(() => this.present());\n        }\n        if (parentPopover) {\n            addEventListener(parentPopover, 'ionPopoverWillDismiss', () => {\n                this.dismiss(undefined, undefined, false);\n            });\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.configureTriggerInteraction();\n    }\n    /**\n     * When opening a popover from a trigger, we should not be\n     * modifying the `event` prop from inside the component.\n     * Additionally, when pressing the \"Right\" arrow key, we need\n     * to shift focus to the first descendant in the newly presented\n     * popover.\n     *\n     * @internal\n     */\n    async presentFromTrigger(event, focusDescendant = false) {\n        this.focusDescendantOnPresent = focusDescendant;\n        await this.present(event);\n        this.focusDescendantOnPresent = false;\n    }\n    /**\n     * Determines whether or not an overlay\n     * is being used inline or via a controller/JS\n     * and returns the correct delegate.\n     * By default, subsequent calls to getDelegate\n     * will use a cached version of the delegate.\n     * This is useful for calling dismiss after\n     * present so that the correct delegate is given.\n     */\n    getDelegate(force = false) {\n        if (this.workingDelegate && !force) {\n            return {\n                delegate: this.workingDelegate,\n                inline: this.inline,\n            };\n        }\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = this.el.parentNode;\n        const inline = (this.inline = parentEl !== null && !this.hasController);\n        const delegate = (this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate);\n        return { inline, delegate };\n    }\n    /**\n     * Present the popover overlay after it has been created.\n     * Developers can pass a mouse, touch, or pointer event\n     * to position the popover relative to where that event\n     * was dispatched.\n     *\n     * @param event The event to position the popover relative to.\n     */\n    async present(event) {\n        const unlock = await this.lockController.lock();\n        if (this.presented) {\n            unlock();\n            return;\n        }\n        const { el } = this;\n        const { inline, delegate } = this.getDelegate(true);\n        /**\n         * Emit ionMount so JS Frameworks have an opportunity\n         * to add the child component to the DOM. The child\n         * component will be assigned to this.usersElement below.\n         */\n        this.ionMount.emit();\n        this.usersElement = await attachComponent(delegate, el, this.component, ['popover-viewport'], this.componentProps, inline);\n        if (!this.keyboardEvents) {\n            this.configureKeyboardInteraction();\n        }\n        this.configureDismissInteraction();\n        /**\n         * When using the lazy loaded build of Stencil, we need to wait\n         * for every Stencil component instance to be ready before presenting\n         * otherwise there can be a flash of unstyled content. With the\n         * custom elements bundle we need to wait for the JS framework\n         * mount the inner contents of the overlay otherwise WebKit may\n         * get the transition incorrect.\n         */\n        if (hasLazyBuild(el)) {\n            await deepReady(this.usersElement);\n            /**\n             * If keepContentsMounted=\"true\" then the\n             * JS Framework has already mounted the inner\n             * contents so there is no need to wait.\n             * Otherwise, we need to wait for the JS\n             * Framework to mount the inner contents\n             * of this component.\n             */\n        }\n        else if (!this.keepContentsMounted) {\n            await waitForMount();\n        }\n        await present(this, 'popoverEnter', iosEnterAnimation, mdEnterAnimation, {\n            event: event || this.event,\n            size: this.size,\n            trigger: this.triggerEl,\n            reference: this.reference,\n            side: this.side,\n            align: this.alignment,\n        });\n        /**\n         * If popover is nested and was\n         * presented using the \"Right\" arrow key,\n         * we need to move focus to the first\n         * descendant inside of the popover.\n         */\n        if (this.focusDescendantOnPresent) {\n            focusFirstDescendant(el);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the popover overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the popover. For example, `cancel` or `backdrop`.\n     * @param dismissParentPopover If `true`, dismissing this popover will also dismiss\n     * a parent popover if this popover is nested. Defaults to `true`.\n     */\n    async dismiss(data, role, dismissParentPopover = true) {\n        const unlock = await this.lockController.lock();\n        const { destroyKeyboardInteraction, destroyDismissInteraction } = this;\n        if (dismissParentPopover && this.parentPopover) {\n            this.parentPopover.dismiss(data, role, dismissParentPopover);\n        }\n        const shouldDismiss = await dismiss(this, data, role, 'popoverLeave', iosLeaveAnimation, mdLeaveAnimation, this.event);\n        if (shouldDismiss) {\n            if (destroyKeyboardInteraction) {\n                destroyKeyboardInteraction();\n                this.destroyKeyboardInteraction = undefined;\n            }\n            if (destroyDismissInteraction) {\n                destroyDismissInteraction();\n                this.destroyDismissInteraction = undefined;\n            }\n            /**\n             * If using popover inline\n             * we potentially need to use the coreDelegate\n             * so that this works in vanilla JS apps\n             */\n            const { delegate } = this.getDelegate();\n            await detachComponent(delegate, this.usersElement);\n        }\n        unlock();\n        return shouldDismiss;\n    }\n    /**\n     * @internal\n     */\n    async getParentPopover() {\n        return this.parentPopover;\n    }\n    /**\n     * Returns a promise that resolves when the popover did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionPopoverDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the popover will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionPopoverWillDismiss');\n    }\n    render() {\n        const mode = getIonMode(this);\n        const { onLifecycle, parentPopover, dismissOnSelect, side, arrow, htmlAttributes, focusTrap } = this;\n        const desktop = isPlatform('desktop');\n        const enableArrow = arrow && !parentPopover;\n        return (h(Host, Object.assign({ key: '16866c02534968c982cf4730d2936d03a5107c8b', \"aria-modal\": \"true\", \"no-router\": true, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), { [mode]: true, 'popover-translucent': this.translucent, 'overlay-hidden': true, 'popover-desktop': desktop, [`popover-side-${side}`]: true, [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false, 'popover-nested': !!parentPopover }), onIonPopoverDidPresent: onLifecycle, onIonPopoverWillPresent: onLifecycle, onIonPopoverWillDismiss: onLifecycle, onIonPopoverDidDismiss: onLifecycle, onIonBackdropTap: this.onBackdropTap }), !parentPopover && h(\"ion-backdrop\", { key: '0df258601a4d30df3c27aa8234a7d5e056c3ecbb', tappable: this.backdropDismiss, visible: this.showBackdrop, part: \"backdrop\" }), h(\"div\", { key: 'f94e80ed996b957b5cd09b826472b4f60e8fcc78', class: \"popover-wrapper ion-overlay-wrapper\", onClick: dismissOnSelect ? () => this.dismiss() : undefined }, enableArrow && h(\"div\", { key: '185ce22f6386e8444a9cc7b8818dbfc16c463c99', class: \"popover-arrow\", part: \"arrow\" }), h(\"div\", { key: '206202b299404e110de5397b229678cca18568d3', class: \"popover-content\", part: \"content\" }, h(\"slot\", { key: 'ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0' })))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"trigger\": [\"onTriggerChange\"],\n        \"triggerAction\": [\"onTriggerChange\"],\n        \"isOpen\": [\"onIsOpenChange\"]\n    }; }\n};\nconst LIFECYCLE_MAP = {\n    ionPopoverDidPresent: 'ionViewDidEnter',\n    ionPopoverWillPresent: 'ionViewWillEnter',\n    ionPopoverWillDismiss: 'ionViewWillLeave',\n    ionPopoverDidDismiss: 'ionViewDidLeave',\n};\nPopover.style = {\n    ios: popoverIosCss,\n    md: popoverMdCss\n};\n\nexport { Popover as ion_popover };\n"], "names": ["r", "registerInstance", "d", "createEvent", "m", "printIonWarning", "e", "getIonMode", "a", "isPlatform", "h", "j", "Host", "k", "getElement", "B", "BACKDROP", "prepareOverlay", "setOverlayId", "f", "present", "n", "focusFirstDescendant", "g", "dismiss", "eventMethod", "F", "FOCUS_TRAP_DISABLE_CLASS", "C", "CoreDelegate", "attachComponent", "detachComponent", "getElementRoot", "raf", "addEventListener", "hasLazyBuild", "c", "createLockController", "getClassMap", "deepReady", "w", "waitForMount", "createAnimation", "getArrowDimensions", "arrowEl", "arrow<PERSON>idth", "arrowHeight", "width", "height", "getBoundingClientRect", "getPopoverDimensions", "size", "contentEl", "triggerEl", "contentDimentions", "contentHeight", "contentWidth", "triggerDimensions", "configureDismissInteraction", "triggerAction", "popoverEl", "parentPopoverEl", "dismissCallbacks", "root", "parentContentEl", "querySelector", "eventName", "callback", "ev", "element", "document", "elementFromPoint", "clientX", "clientY", "undefined", "target", "closestTrigger", "closest", "stopPropagation", "for<PERSON>ach", "removeEventListener", "configureTriggerInteraction", "triggerCallbacks", "hoverTimeout", "_ref", "_asyncToGenerator", "clearTimeout", "setTimeout", "present<PERSON><PERSON><PERSON><PERSON>ger", "_x", "apply", "arguments", "relatedTarget", "preventDefault", "setAttribute", "removeAttribute", "getIndexOfItem", "items", "item", "tagName", "findIndex", "el", "getNextItem", "currentItem", "currentItemIndex", "getPrevItem", "focusItem", "button", "focus", "isTriggerElement", "hasAttribute", "configureKeyboardInteraction", "_ref2", "_a", "activeElement", "targetTagName", "Array", "from", "querySelectorAll", "_b", "key", "parentPopover", "getParentPopover", "nextItem", "prevItem", "firstItem", "lastItem", "length", "rightEvent", "CustomEvent", "dispatchEvent", "_x2", "getPopoverPosition", "isRTL", "reference", "side", "align", "defaultPosition", "event", "referenceCoordinates", "top", "left", "mouseEv", "customEv", "actualTriggerEl", "detail", "ionShadowTarget", "triggerBoundingBox", "coordinates", "calculatePopoverSide", "alignedCoordinates", "calculatePopoverAlign", "arrowTop", "arrowLeft", "calculateArrowPosition", "originX", "originY", "calculatePopoverOrigin", "getOriginXAlignment", "getOriginYAlignment", "leftPosition", "rightPosition", "sideLeft", "sideRight", "calculatePopoverCenterAlign", "calculatePopoverEndAlign", "calculateWindowAdjustment", "coordTop", "coordLeft", "bodyPadding", "bodyWidth", "bodyHeight", "safe<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentOriginX", "contentOriginY", "triggerCoordinates", "coordArrowTop", "coordArrowLeft", "bottom", "checkSafeAreaLeft", "checkSafeAreaRight", "triggerTop", "triggerHeight", "addPopoverBottomClass", "Math", "max", "shouldShowArrow", "didAdjustBounds", "trigger", "POPOVER_IOS_BODY_PADDING", "iosEnterAnimation", "baseEl", "opts", "doc", "ownerDocument", "dir", "defaultView", "innerWidth", "innerHeight", "referenceSizeEl", "results", "padding", "margin", "baseAnimation", "backdropAnimation", "contentAnimation", "addElement", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "beforeAddWrite", "style", "setProperty", "classList", "add", "safeAreaLeft", "safeAreaRight", "leftValue", "showArrow", "addAnimation", "iosLeaveAnimation", "afterAddWrite", "removeProperty", "remove", "POPOVER_MD_BODY_PADDING", "mdEnterAnimation", "wrapperAnimation", "viewportAnimation", "mdLeaveAnimation", "popoverIosCss", "popoverMdCss", "Popover", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ionMount", "coreDelegate", "lockController", "inline", "focusDescendantOnPresent", "presented", "hasController", "keyboardClose", "<PERSON><PERSON><PERSON><PERSON>", "showBackdrop", "translucent", "animated", "dismissOnSelect", "arrow", "isOpen", "keyboardEvents", "focusTrap", "keepContentsMounted", "onBackdropTap", "onLifecycle", "modalEvent", "usersElement", "name", "LIFECYCLE_MAP", "type", "bubbles", "cancelable", "destroyTriggerInteraction", "getElementById", "destroyKeyboardInteraction", "destroyDismissInteraction", "onTriggerChange", "onIsOpenChange", "newValue", "oldValue", "connectedCallback", "disconnectedCallback", "componentWillLoad", "popoverId", "htmlAttributes", "id", "alignment", "componentDidLoad", "_x3", "_this", "focusDescendant", "getDelegate", "force", "workingDelegate", "delegate", "parentEl", "parentNode", "_this2", "unlock", "lock", "emit", "component", "componentProps", "_x4", "_x5", "_this3", "data", "role", "dismissParentPopover", "<PERSON><PERSON><PERSON><PERSON>", "_this4", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "render", "mode", "desktop", "enableArrow", "Object", "assign", "tabindex", "zIndex", "overlayIndex", "class", "cssClass", "onIonPopoverDidPresent", "onIonPopoverWillPresent", "onIonPopoverWillDismiss", "onIonPopoverDidDismiss", "onIonBackdropTap", "tappable", "visible", "part", "onClick", "watchers", "ionPopoverDidPresent", "ionPopoverWillPresent", "ionPopoverWillDismiss", "ionPopoverDidDismiss", "ios", "md", "ion_popover"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}