{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-nav_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACiK;AACvF;AACM;AAC2F;AACnG;AAExE,MAAM+B,cAAc,GAAG,CAAC;AACxB,MAAMC,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,oBAAoB,GAAG,CAAC;AAC9B;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC3B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGP,cAAc;EAC/B;EACMQ,IAAIA,CAACC,SAAS,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MAClBD,KAAI,CAACH,KAAK,GAAGN,mBAAmB;MAChC,IAAI,CAACS,KAAI,CAACE,OAAO,EAAE;QACf,MAAMP,SAAS,GAAGK,KAAI,CAACL,SAAS;QAChCK,KAAI,CAACE,OAAO,SAASb,iEAAe,CAACW,KAAI,CAACG,QAAQ,EAAEJ,SAAS,EAAEJ,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEK,KAAI,CAACJ,MAAM,CAAC;MAC9H;IAAC;EACL;EACA;AACJ;AACA;EACIQ,QAAQA,CAAA,EAAG;IACP1B,uDAAM,CAAC,IAAI,CAACmB,KAAK,KAAKL,oBAAoB,EAAE,6BAA6B,CAAC;IAC1E,MAAMU,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,IAAI,IAAI,CAACC,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,CAACE,iBAAiB,CAACH,OAAO,CAACI,aAAa,EAAEJ,OAAO,CAAC;MACnE,CAAC,MACI;QACDA,OAAO,CAACK,MAAM,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACC,GAAG,GAAGC,SAAS;IACpB,IAAI,CAACZ,KAAK,GAAGL,oBAAoB;EACrC;AACJ;AACA,MAAMkB,OAAO,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEhB,MAAM,KAAK;EAClC,IAAI,CAACe,IAAI,EAAE;IACP,OAAO,KAAK;EAChB;EACA,IAAIA,IAAI,CAAChB,SAAS,KAAKiB,EAAE,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,OAAOnC,uDAAqB,CAACkC,IAAI,CAACf,MAAM,EAAEA,MAAM,CAAC;AACrD,CAAC;AACD,MAAMiB,aAAa,GAAGA,CAACC,IAAI,EAAElB,MAAM,KAAK;EACpC,IAAI,CAACkB,IAAI,EAAE;IACP,OAAO,IAAI;EACf;EACA,IAAIA,IAAI,YAAYrB,cAAc,EAAE;IAChC,OAAOqB,IAAI;EACf;EACA,OAAO,IAAIrB,cAAc,CAACqB,IAAI,EAAElB,MAAM,CAAC;AAC3C,CAAC;AACD,MAAMmB,cAAc,GAAIC,KAAK,IAAK;EAC9B,OAAOA,KAAK,CACPC,GAAG,CAAEH,IAAI,IAAK;IACf,IAAIA,IAAI,YAAYrB,cAAc,EAAE;MAChC,OAAOqB,IAAI;IACf;IACA,IAAI,WAAW,IAAIA,IAAI,EAAE;MACrB,OAAOD,aAAa,CAACC,IAAI,CAACnB,SAAS,EAAEmB,IAAI,CAACI,cAAc,KAAK,IAAI,GAAGT,SAAS,GAAGK,IAAI,CAACI,cAAc,CAAC;IACxG;IACA,OAAOL,aAAa,CAACC,IAAI,EAAEL,SAAS,CAAC;EACzC,CAAC,CAAC,CACGU,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC;AAClC,CAAC;AAED,MAAMC,MAAM,GAAG,4FAA4F;AAE3G,MAAMC,GAAG,GAAG,MAAM;EACd5B,WAAWA,CAAC6B,OAAO,EAAE;IACjB/D,qDAAgB,CAAC,IAAI,EAAE+D,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAG9D,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC+D,gBAAgB,GAAG/D,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACgE,eAAe,GAAGhE,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACiE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,YAAY,KAAK,IAAI,CAAC;IACnD;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,IAAI,KAAK/B,SAAS,EAAE;MACzB;IACJ;IACA,IAAI,IAAI,CAACwB,OAAO,KAAK,KAAK,EAAE;MACxB;AACZ;AACA;AACA;MACY;IACJ;IACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,IAAI,CAACW,IAAI,KAAK/B,SAAS,EAAE;QACzB,IAAI,CAACgC,OAAO,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAACE,UAAU,CAAC;MAC5C;IACJ;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACd,SAAS,GAAGe,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,IAAI,CAACC,EAAE,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI;IACzG,IAAI,IAAI,CAACT,YAAY,KAAK7B,SAAS,EAAE;MACjC,MAAMuC,IAAI,GAAGpF,qDAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAC0E,YAAY,GAAGxE,iDAAM,CAACmF,UAAU,CAAC,kBAAkB,EAAED,IAAI,KAAK,KAAK,CAAC;IAC7E;IACA,IAAI,CAACxB,cAAc,CAAC0B,IAAI,CAAC,CAAC;EAC9B;EACMC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnD,yMAAA;MACrB;MACAmD,MAAI,CAACnB,OAAO,GAAG,IAAI;MACnBmB,MAAI,CAACb,WAAW,CAAC,CAAC;MAClBa,MAAI,CAAChB,OAAO,GAAG,OAAO,yIAAkC,EAAEiB,sBAAsB,CAACD,MAAI,CAACN,EAAE,EAAEM,MAAI,CAACE,QAAQ,CAACC,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACI,OAAO,CAACD,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACK,MAAM,CAACF,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACM,KAAK,CAACH,IAAI,CAACH,MAAI,CAAC,CAAC;MAC3LA,MAAI,CAACjB,mBAAmB,CAAC,CAAC;IAAC;EAC/B;EACAwB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC5B,SAAS,GAAG,KAAK;EAC1B;EACA6B,oBAAoBA,CAAA,EAAG;IACnB,KAAK,MAAMjD,IAAI,IAAI,IAAI,CAACqB,KAAK,EAAE;MAC3BrD,qDAAS,CAACgC,IAAI,CAACT,OAAO,EAAEnB,iDAAqB,CAAC;MAC9C4B,IAAI,CAACP,QAAQ,CAAC,CAAC;IACnB;IACA;IACA,IAAI,IAAI,CAACgC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACyB,OAAO,CAAC,CAAC;MACtB,IAAI,CAACzB,OAAO,GAAG3B,SAAS;IAC5B;IACA,IAAI,CAACkB,UAAU,CAACmC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAAC9B,KAAK,CAAC8B,MAAM,GAAG,CAAC;IACrB,IAAI,CAAC/B,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgC,IAAIA,CAACpE,SAAS,EAAEuB,cAAc,EAAE8C,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvE,SAAS,EAAEuB,cAAc,EAAE8C,IAAI,EAAEC,IAAI,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACC,WAAW,EAAExE,SAAS,EAAEuB,cAAc,EAAE8C,IAAI,EAAEC,IAAI,EAAE;IACvD,OAAO,IAAI,CAACG,WAAW,CAACD,WAAW,EAAE,CAAC;MAAExE,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAE8C,IAAI,EAAEC,IAAI,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAACD,WAAW,EAAEE,gBAAgB,EAAEL,IAAI,EAAEC,IAAI,EAAE;IACnD,OAAO,IAAI,CAACK,SAAS,CAAC;MAClBC,WAAW,EAAEJ,WAAW;MACxBK,WAAW,EAAEH,gBAAgB;MAC7BL;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACT,IAAI,EAAEC,IAAI,EAAE;IACZ,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,KAAKA,CAACC,eAAe,EAAEZ,IAAI,EAAEC,IAAI,EAAE;IAC/B,MAAMY,EAAE,GAAG;MACPC,WAAW,EAAE,CAAC,CAAC;MACfC,WAAW,EAAE,CAAC,CAAC;MACff;IACJ,CAAC;IACD,IAAI,OAAOY,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACjF,SAAS,EAAE;MAClEkF,EAAE,CAACG,UAAU,GAAGJ,eAAe;MAC/BC,EAAE,CAACC,WAAW,GAAG,CAAC;IACtB,CAAC,MACI,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;MAC1CC,EAAE,CAACC,WAAW,GAAGF,eAAe,GAAG,CAAC;IACxC;IACA,OAAO,IAAI,CAACN,SAAS,CAACO,EAAE,EAAEZ,IAAI,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,SAASA,CAACjB,IAAI,EAAEC,IAAI,EAAE;IAClB,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,WAAWA,CAACQ,UAAU,EAAEH,WAAW,GAAG,CAAC,EAAEf,IAAI,EAAEC,IAAI,EAAE;IACjD,OAAO,IAAI,CAACK,SAAS,CAAC;MAClBQ,WAAW,EAAEI,UAAU;MACvBH,WAAW;MACXf;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIxB,OAAOA,CAAC9C,SAAS,EAAEuB,cAAc,EAAE8C,IAAI,EAAEC,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACkB,QAAQ,CAAC,CAAC;MAAExF,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAE8C,IAAI,EAAEC,IAAI,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,QAAQA,CAACnD,KAAK,EAAEgC,IAAI,EAAEC,IAAI,EAAE;IACxBD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAIA,IAAI,GAAG,CAAC,CAAE;IACrD;IACA,IAAIA,IAAI,CAAC9B,QAAQ,KAAK,IAAI,EAAE;MACxB8B,IAAI,CAAC9B,QAAQ,GAAG,KAAK;IACzB;IACA,OAAO,IAAI,CAACoC,SAAS,CAAC;MAClBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAExC,KAAK;MAClB8C,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC,CAAC;MACff;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,UAAUA,CAACxE,EAAE,EAAEhB,MAAM,EAAEyF,SAAS,EAAEC,SAAS,EAAE;IACzC,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACnC,IAAI9E,OAAO,CAAC6E,MAAM,EAAE3E,EAAE,EAAEhB,MAAM,CAAC,EAAE;MAC7B,OAAO6F,OAAO,CAACC,OAAO,CAAC;QACnBC,OAAO,EAAE,KAAK;QACdzF,OAAO,EAAEqF,MAAM,CAACrF;MACpB,CAAC,CAAC;IACN;IACA,IAAIwF,OAAO;IACX,MAAME,OAAO,GAAG,IAAIH,OAAO,CAAElI,CAAC,IAAMmI,OAAO,GAAGnI,CAAE,CAAC;IACjD,IAAIsI,MAAM;IACV,MAAMC,UAAU,GAAG;MACfC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAGC,UAAU,IAAK;QACzB,IAAIC,IAAI;QACR,MAAMC,CAAC,GAAG,IAAIV,OAAO,CAAElI,CAAC,IAAM2I,IAAI,GAAG3I,CAAE,CAAC;QACxCmI,OAAO,CAAC;UACJC,OAAO,EAAE,IAAI;UACbzF,OAAO,EAAE+F,UAAU;UACnBG,WAAW;YAAA,IAAAC,IAAA,GAAApG,yMAAA,CAAE,aAAY;cACrBiG,IAAI,CAAC,CAAC;cACN,MAAML,MAAM;YAChB,CAAC;YAAA,gBAHDO,WAAWA,CAAA;cAAA,OAAAC,IAAA,CAAAC,KAAA,OAAAC,SAAA;YAAA;UAAA;QAIf,CAAC,CAAC;QACF,OAAOJ,CAAC;MACZ;IACJ,CAAC;IACD,IAAId,SAAS,KAAK,MAAM,EAAE;MACtBQ,MAAM,GAAG,IAAI,CAACpD,OAAO,CAAC7B,EAAE,EAAEhB,MAAM,EAAEkG,UAAU,CAAC;IACjD,CAAC,MACI;MACD;MACA,MAAMU,cAAc,GAAG,IAAI,CAACxE,KAAK,CAACyE,IAAI,CAAErF,CAAC,IAAKV,OAAO,CAACU,CAAC,EAAER,EAAE,EAAEhB,MAAM,CAAC,CAAC;MACrE,IAAI4G,cAAc,EAAE;QAChBX,MAAM,GAAG,IAAI,CAAClB,KAAK,CAAC6B,cAAc,EAAEE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAET,SAAS,EAAE,MAAM;UAAEuB,gBAAgB,EAAEtB;QAAU,CAAC,CAAC,CAAC;MACzI,CAAC,MACI,IAAID,SAAS,KAAK,SAAS,EAAE;QAC9BQ,MAAM,GAAG,IAAI,CAAC9B,IAAI,CAACnD,EAAE,EAAEhB,MAAM,EAAE8G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAEc,gBAAgB,EAAEtB;QAAU,CAAC,CAAC,CAAC;MACjH,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,EAAE;QAC3BQ,MAAM,GAAG,IAAI,CAACpD,OAAO,CAAC7B,EAAE,EAAEhB,MAAM,EAAE8G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAET,SAAS,EAAE,MAAM;UAAEnD,QAAQ,EAAE,IAAI;UAAE0E,gBAAgB,EAAEtB;QAAU,CAAC,CAAC,CAAC;MACvJ;IACJ;IACA,OAAOM,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACUiB,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7G,yMAAA;MACf,MAAMsF,MAAM,GAAGuB,MAAI,CAACtB,aAAa,CAAC,CAAC;MACnC,IAAID,MAAM,EAAE;QACR,OAAO;UACH3E,EAAE,EAAE2E,MAAM,CAACrF,OAAO,CAAC6G,OAAO;UAC1BnH,MAAM,EAAE2F,MAAM,CAAC3F,MAAM;UACrBM,OAAO,EAAEqF,MAAM,CAACrF;QACpB,CAAC;MACL;MACA,OAAOO,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACUuG,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhH,yMAAA;MACd,OAAOgH,MAAI,CAACzB,aAAa,CAAC,CAAC;IAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACU0B,UAAUA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnH,yMAAA;MACpB,OAAOmH,MAAI,CAACpF,KAAK,CAACmF,KAAK,CAAC;IAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACUE,SAASA,CAAC1G,IAAI,EAAE;IAAA,IAAA2G,MAAA;IAAA,OAAArH,yMAAA;MAClB,OAAOqH,MAAI,CAACC,aAAa,CAAC5G,IAAI,CAAC;IAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACU6G,WAAWA,CAAC7G,IAAI,EAAE;IAAA,IAAA8G,MAAA;IAAA,OAAAxH,yMAAA;MACpB,OAAOwH,MAAI,CAACC,eAAe,CAAC/G,IAAI,CAAC;IAAC;EACtC;EACA;AACJ;AACA;EACUgH,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3H,yMAAA;MACd,OAAOwF,OAAO,CAACC,OAAO,CAACkC,MAAI,CAAC5F,KAAK,CAAC8B,MAAM,CAAC;IAAC;EAC9C;EACA0B,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACxD,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC8B,MAAM,GAAG,CAAC,CAAC;EAC5C;EACAyD,aAAaA,CAAC5G,IAAI,GAAG,IAAI,CAAC6E,aAAa,CAAC,CAAC,EAAE;IACvC,OAAO,CAAC,EAAE7E,IAAI,IAAI,IAAI,CAAC+G,eAAe,CAAC/G,IAAI,CAAC,CAAC;EACjD;EACA+G,eAAeA,CAAC/G,IAAI,GAAG,IAAI,CAAC6E,aAAa,CAAC,CAAC,EAAE;IACzC,IAAI,CAAC7E,IAAI,EAAE;MACP,OAAOF,SAAS;IACpB;IACA,MAAMuB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC6F,OAAO,CAAClH,IAAI,CAAC;IACjC,OAAOwG,KAAK,GAAG,CAAC,GAAGnF,KAAK,CAACmF,KAAK,GAAG,CAAC,CAAC,GAAG1G,SAAS;EACnD;EACA;AACJ;AACA;AACA;AACA;EACU6D,SAASA,CAACO,EAAE,EAAEZ,IAAI,EAAE;IAAA,IAAA6D,MAAA;IAAA,OAAA7H,yMAAA;MACtB,IAAI8H,EAAE,EAAEC,EAAE;MACV,IAAIF,MAAI,CAAChG,eAAe,KAAK,CAACiG,EAAE,GAAGlD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI+D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,CAAC,EAAE;QAC7F,OAAO,KAAK;MAChB;MACA,MAAMrC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAACC,OAAO,EAAEwC,MAAM,KAAK;QAC7CrD,EAAE,CAACa,OAAO,GAAGA,OAAO;QACpBb,EAAE,CAACqD,MAAM,GAAGA,MAAM;MACtB,CAAC,CAAC;MACFrD,EAAE,CAACZ,IAAI,GAAGA,IAAI;MACd;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIY,EAAE,CAACb,IAAI,IAAIa,EAAE,CAACb,IAAI,CAAC+B,SAAS,KAAK,KAAK,IAAI+B,MAAI,CAACjG,SAAS,EAAE;QAC1D,MAAMsG,MAAM,GAAGvF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnD,IAAIsF,MAAM,EAAE;UACR,MAAMC,aAAa,SAASD,MAAM,CAACC,aAAa,CAAC,CAAC;UAClD,IAAIA,aAAa,KAAK,KAAK,EAAE;YACzB,OAAO,KAAK;UAChB;UACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;YACnCD,MAAM,CAACpE,IAAI,CAACqE,aAAa,EAAEvD,EAAE,CAACb,IAAI,CAACqB,SAAS,IAAI,MAAM,CAAC;YACvD,OAAO,KAAK;UAChB;QACJ;MACJ;MACA;MACA,IAAI,CAAC,CAAC2C,EAAE,GAAGnD,EAAE,CAACL,WAAW,MAAM,IAAI,IAAIwD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClE,MAAM,MAAM,CAAC,EAAE;QAC9Ee,EAAE,CAACL,WAAW,GAAG/D,SAAS;MAC9B;MACA;MACAqH,MAAI,CAACnG,UAAU,CAACoC,IAAI,CAACc,EAAE,CAAC;MACxB;MACA;MACAiD,MAAI,CAACO,QAAQ,CAAC,CAAC;MACf,OAAOzC,OAAO;IAAC;EACnB;EACA0C,OAAOA,CAACC,MAAM,EAAE1D,EAAE,EAAE;IAChB,IAAI,IAAI,CAAC9C,SAAS,EAAE;MAChB,IAAI,CAACyG,SAAS,CAAC,8BAA8B,EAAE3D,EAAE,CAAC;MAClD;IACJ;IACA,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACTY,EAAE,CAACZ,IAAI,CAACsE,MAAM,CAACE,YAAY,EAAEF,MAAM,CAACG,kBAAkB,EAAEH,MAAM,CAACI,YAAY,EAAEJ,MAAM,CAACK,WAAW,EAAEL,MAAM,CAAClD,SAAS,CAAC;IACtH;IACAR,EAAE,CAACa,OAAO,CAAC6C,MAAM,CAACE,YAAY,CAAC;IAC/B,IAAI5D,EAAE,CAACb,IAAI,CAAC+B,SAAS,KAAK,KAAK,IAAI,IAAI,CAAClE,SAAS,EAAE;MAC/C,MAAMsG,MAAM,GAAGvF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIsF,MAAM,EAAE;QACR,MAAM9C,SAAS,GAAGkD,MAAM,CAAClD,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QAClE8C,MAAM,CAACU,UAAU,CAACxD,SAAS,CAAC;MAChC;IACJ;EACJ;EACAyD,MAAMA,CAACC,YAAY,EAAElE,EAAE,EAAE;IACrB,IAAI,IAAI,CAAC9C,SAAS,EAAE;MAChB,IAAI,CAACyG,SAAS,CAAC,8BAA8B,EAAE3D,EAAE,CAAC;MAClD;IACJ;IACA,IAAI,CAAClD,UAAU,CAACmC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAAC0E,SAAS,CAACO,YAAY,EAAElE,EAAE,CAAC;EACpC;EACA2D,SAASA,CAACO,YAAY,EAAElE,EAAE,EAAE;IACxB,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACTY,EAAE,CAACZ,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE8E,YAAY,CAAC;IACvC;IACA,IAAIlE,EAAE,CAACqD,MAAM,IAAI,CAAC,IAAI,CAACnG,SAAS,EAAE;MAC9B8C,EAAE,CAACqD,MAAM,CAACa,YAAY,CAAC;IAC3B,CAAC,MACI;MACDlE,EAAE,CAACa,OAAO,CAAC,KAAK,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2C,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,IAAI,CAACvG,eAAe,EAAE;MACtB,OAAO,KAAK;IAChB;IACA;IACA,MAAM+C,EAAE,GAAG,IAAI,CAAClD,UAAU,CAACqH,KAAK,CAAC,CAAC;IAClC,IAAI,CAACnE,EAAE,EAAE;MACL,OAAO,KAAK;IAChB;IACA,IAAI,CAACoE,aAAa,CAACpE,EAAE,CAAC;IACtB,OAAO,IAAI;EACf;EACA;EACMoE,aAAaA,CAACpE,EAAE,EAAE;IAAA,IAAAqE,MAAA;IAAA,OAAAjJ,yMAAA;MACpB,IAAI;QACA;QACAiJ,MAAI,CAACzH,gBAAgB,CAACyB,IAAI,CAAC,CAAC;QAC5BgG,MAAI,CAACpH,eAAe,GAAG,IAAI;QAC3BoH,MAAI,CAACC,SAAS,CAACtE,EAAE,CAAC;QAClB,MAAM+D,WAAW,GAAGM,MAAI,CAAC1D,aAAa,CAAC,CAAC;QACxC,MAAMmD,YAAY,GAAGO,MAAI,CAACE,eAAe,CAACvE,EAAE,EAAE+D,WAAW,CAAC;QAC1D,IAAI,CAACA,WAAW,IAAI,CAACD,YAAY,EAAE;UAC/B,MAAM,IAAIU,KAAK,CAAC,qCAAqC,CAAC;QAC1D;QACA,IAAIV,YAAY,IAAIA,YAAY,CAAC9I,KAAK,KAAKP,cAAc,EAAE;UACvD,MAAMqJ,YAAY,CAAC7I,IAAI,CAACoJ,MAAI,CAACpG,EAAE,CAAC;QACpC;QACAoG,MAAI,CAACI,YAAY,CAACX,YAAY,EAAEC,WAAW,EAAE/D,EAAE,CAAC;QAChD;QACA,MAAM6D,kBAAkB,GAAG,CAAC7D,EAAE,CAAC0E,0BAA0B,IAAI1E,EAAE,CAAC2E,yBAAyB,KAAKb,YAAY,KAAKC,WAAW;QAC1H,IAAIF,kBAAkB,IAAI7D,EAAE,CAACb,IAAI,IAAI4E,WAAW,EAAE;UAC9C,MAAMa,eAAe,GAAG5E,EAAE,CAACb,IAAI,CAACqB,SAAS,KAAK,MAAM;UACpD;AAChB;AACA;AACA;UACgB,IAAIoE,eAAe,EAAE;YACjB5E,EAAE,CAACb,IAAI,CAAC4C,gBAAgB,GAAG/B,EAAE,CAACb,IAAI,CAAC4C,gBAAgB,KAAK+B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC/B,gBAAgB,CAAC;UACtJ;UACAgC,WAAW,CAAChC,gBAAgB,GAAG/B,EAAE,CAACb,IAAI,CAAC4C,gBAAgB;QAC3D;QACA,IAAI2B,MAAM;QACV,IAAIG,kBAAkB,EAAE;UACpBH,MAAM,SAASW,MAAI,CAACrK,UAAU,CAAC8J,YAAY,EAAEC,WAAW,EAAE/D,EAAE,CAAC;QACjE,CAAC,MACI;UACD;UACA;UACA;UACA;UACA0D,MAAM,GAAG;YACLE,YAAY,EAAE,IAAI;YAClBC,kBAAkB,EAAE;UACxB,CAAC;QACL;QACAQ,MAAI,CAACZ,OAAO,CAACC,MAAM,EAAE1D,EAAE,CAAC;QACxBqE,MAAI,CAACxH,eAAe,CAACwB,IAAI,CAAC,CAAC;MAC/B,CAAC,CACD,OAAO6F,YAAY,EAAE;QACjBG,MAAI,CAACJ,MAAM,CAACC,YAAY,EAAElE,EAAE,CAAC;MACjC;MACAqE,MAAI,CAACpH,eAAe,GAAG,KAAK;MAC5BoH,MAAI,CAACb,QAAQ,CAAC,CAAC;IAAC;EACpB;EACAc,SAASA,CAACtE,EAAE,EAAE;IACV,IAAIkD,EAAE,EAAEC,EAAE;IACV,IAAI0B,EAAE;IACN,MAAMC,WAAW,GAAG,IAAI,CAAC3H,KAAK,CAAC8B,MAAM;IACrC,CAACiE,EAAE,GAAGlD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI+D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIlD,EAAE,CAACb,IAAI,GAAG,CAAC,CAAE;IAC9D,CAACgE,EAAE,GAAG,CAAC0B,EAAE,GAAG7E,EAAE,CAACb,IAAI,EAAE7D,QAAQ,MAAM,IAAI,IAAI6H,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI0B,EAAE,CAACvJ,QAAQ,GAAG,IAAI,CAACA,QAAS;IAC7F,IAAI0E,EAAE,CAACG,UAAU,KAAKvE,SAAS,EAAE;MAC7B/B,uDAAM,CAACmG,EAAE,CAACC,WAAW,KAAKrE,SAAS,EAAE,8BAA8B,CAAC;MACpE/B,uDAAM,CAACmG,EAAE,CAACE,WAAW,KAAKtE,SAAS,EAAE,8BAA8B,CAAC;MACpE,MAAM0G,KAAK,GAAG,IAAI,CAACnF,KAAK,CAAC6F,OAAO,CAAChD,EAAE,CAACG,UAAU,CAAC;MAC/C,IAAImC,KAAK,GAAG,CAAC,EAAE;QACX,MAAM,IAAIkC,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MACAxE,EAAE,CAACC,WAAW,IAAIqC,KAAK;IAC3B;IACA,IAAItC,EAAE,CAACC,WAAW,KAAKrE,SAAS,EAAE;MAC9B,IAAIoE,EAAE,CAACC,WAAW,GAAG,CAAC,EAAE;QACpBD,EAAE,CAACC,WAAW,GAAG6E,WAAW,GAAG,CAAC;MACpC;MACA,IAAI9E,EAAE,CAACE,WAAW,GAAG,CAAC,EAAE;QACpBF,EAAE,CAACE,WAAW,GAAG4E,WAAW,GAAG9E,EAAE,CAACC,WAAW;MACjD;MACAD,EAAE,CAAC2E,yBAAyB,GAAG3E,EAAE,CAACE,WAAW,GAAG,CAAC,IAAIF,EAAE,CAACC,WAAW,GAAGD,EAAE,CAACE,WAAW,KAAK4E,WAAW;IACxG;IACA,IAAI9E,EAAE,CAACL,WAAW,EAAE;MAChB;MACA;MACA,IAAIK,EAAE,CAACN,WAAW,GAAG,CAAC,IAAIM,EAAE,CAACN,WAAW,GAAGoF,WAAW,EAAE;QACpD9E,EAAE,CAACN,WAAW,GAAGoF,WAAW;MAChC;MACA9E,EAAE,CAAC0E,0BAA0B,GAAG1E,EAAE,CAACN,WAAW,KAAKoF,WAAW;IAClE;IACA,MAAMnF,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAI,CAACA,WAAW,EAAE;MACd;IACJ;IACA9F,uDAAM,CAAC8F,WAAW,CAACV,MAAM,GAAG,CAAC,EAAE,wBAAwB,CAAC;IACxD,MAAM8F,eAAe,GAAG7I,cAAc,CAACyD,WAAW,CAAC;IACnD,IAAIoF,eAAe,CAAC9F,MAAM,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAIuF,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA;IACA,KAAK,MAAM1I,IAAI,IAAIiJ,eAAe,EAAE;MAChCjJ,IAAI,CAACR,QAAQ,GAAG0E,EAAE,CAACb,IAAI,CAAC7D,QAAQ;MAChC,MAAMK,GAAG,GAAGG,IAAI,CAACH,GAAG;MACpB,IAAIA,GAAG,IAAIA,GAAG,KAAK,IAAI,EAAE;QACrB,MAAM,IAAI6I,KAAK,CAAC,oCAAoC,CAAC;MACzD;MACA,IAAI1I,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE;QACrC,MAAM,IAAI6J,KAAK,CAAC,qCAAqC,CAAC;MAC1D;IACJ;IACAxE,EAAE,CAACL,WAAW,GAAGoF,eAAe;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,eAAeA,CAACvE,EAAE,EAAE+D,WAAW,EAAE;IAC7B;IACA,MAAMpE,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAIA,WAAW,KAAK/D,SAAS,EAAE;MAC3B,OAAO+D,WAAW,CAACA,WAAW,CAACV,MAAM,GAAG,CAAC,CAAC;IAC9C;IACA;IACA,MAAMgB,WAAW,GAAGD,EAAE,CAACC,WAAW;IAClC,IAAIA,WAAW,KAAKrE,SAAS,EAAE;MAC3B,MAAMuB,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAM6H,SAAS,GAAG/E,WAAW,GAAGD,EAAE,CAACE,WAAW;MAC9C,KAAK,IAAI+E,CAAC,GAAG9H,KAAK,CAAC8B,MAAM,GAAG,CAAC,EAAEgG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxC,MAAMnJ,IAAI,GAAGqB,KAAK,CAAC8H,CAAC,CAAC;QACrB,IAAI,CAACA,CAAC,GAAGhF,WAAW,IAAIgF,CAAC,IAAID,SAAS,KAAKlJ,IAAI,KAAKiI,WAAW,EAAE;UAC7D,OAAOjI,IAAI;QACf;MACJ;IACJ;IACA,OAAOF,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6I,YAAYA,CAACX,YAAY,EAAEC,WAAW,EAAE/D,EAAE,EAAE;IACxC,IAAIkD,EAAE,EAAEC,EAAE,EAAE0B,EAAE;IACdhL,uDAAM,CAACkK,WAAW,IAAID,YAAY,EAAE,4CAA4C,CAAC;IACjFjK,uDAAM,CAACmG,EAAE,CAACa,OAAO,EAAE,uBAAuB,CAAC;IAC3ChH,uDAAM,CAACmG,EAAE,CAACqD,MAAM,EAAE,sBAAsB,CAAC;IACzC;IACA,MAAMlE,IAAI,GAAGa,EAAE,CAACb,IAAI;IACpB,MAAM;MAAEQ,WAAW;MAAEM,WAAW;MAAEC;IAAY,CAAC,GAAGF,EAAE;IACpD;IACA,IAAIkF,YAAY;IAChB;IACA,IAAIjF,WAAW,KAAKrE,SAAS,IAAIsE,WAAW,KAAKtE,SAAS,EAAE;MACxD/B,uDAAM,CAACoG,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3DpG,uDAAM,CAACqG,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3DgF,YAAY,GAAG,EAAE;MACjB,KAAK,IAAID,CAAC,GAAGhF,WAAW,EAAEgF,CAAC,GAAGhF,WAAW,GAAGC,WAAW,EAAE+E,CAAC,EAAE,EAAE;QAC1D,MAAMnJ,IAAI,GAAG,IAAI,CAACqB,KAAK,CAAC8H,CAAC,CAAC;QAC1B,IAAInJ,IAAI,KAAKF,SAAS,IAAIE,IAAI,KAAKgI,YAAY,IAAIhI,IAAI,KAAKiI,WAAW,EAAE;UACrEmB,YAAY,CAAChG,IAAI,CAACpD,IAAI,CAAC;QAC3B;MACJ;MACA;MACA,CAACoH,EAAE,GAAG/D,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAI0C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI/D,IAAI,CAACqB,SAAS,GAAG,MAAO;IACpF;IACA,MAAM2E,aAAa,GAAG,IAAI,CAAChI,KAAK,CAAC8B,MAAM,IAAI,CAACkE,EAAE,GAAGxD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACV,MAAM,MAAM,IAAI,IAAIkE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,IAAIjD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;IACvOrG,uDAAM,CAACsL,aAAa,IAAI,CAAC,EAAE,mCAAmC,CAAC;IAC/D,IAAIA,aAAa,KAAK,CAAC,EAAE;MACrBhM,qDAAe,CAAC,kHAAkH,EAAE,IAAI,EAAE,IAAI,CAAC8E,EAAE,CAAC;MAClJ,MAAM,IAAIuG,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA;IACA;IACA,IAAI7E,WAAW,EAAE;MACb;MACA,IAAIL,WAAW,GAAGU,EAAE,CAACN,WAAW;MAChC,KAAK,MAAM5D,IAAI,IAAI6D,WAAW,EAAE;QAC5B,IAAI,CAACyF,YAAY,CAACtJ,IAAI,EAAEwD,WAAW,CAAC;QACpCA,WAAW,EAAE;MACjB;MACA,IAAIU,EAAE,CAAC0E,0BAA0B,EAAE;QAC/B;QACA,CAACG,EAAE,GAAG1F,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAIqE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI1F,IAAI,CAACqB,SAAS,GAAG,SAAU;MACvF;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI0E,YAAY,IAAIA,YAAY,CAACjG,MAAM,GAAG,CAAC,EAAE;MACzC,KAAK,MAAMnD,IAAI,IAAIoJ,YAAY,EAAE;QAC7BpL,qDAAS,CAACgC,IAAI,CAACT,OAAO,EAAEjB,iDAAoB,CAAC;QAC7CN,qDAAS,CAACgC,IAAI,CAACT,OAAO,EAAEf,iDAAmB,CAAC;QAC5CR,qDAAS,CAACgC,IAAI,CAACT,OAAO,EAAEnB,iDAAqB,CAAC;MAClD;MACA;MACA,KAAK,MAAM4B,IAAI,IAAIoJ,YAAY,EAAE;QAC7B,IAAI,CAACG,WAAW,CAACvJ,IAAI,CAAC;MAC1B;IACJ;EACJ;EACM9B,UAAUA,CAAC8J,YAAY,EAAEC,WAAW,EAAE/D,EAAE,EAAE;IAAA,IAAAsF,MAAA;IAAA,OAAAlK,yMAAA;MAC5C;MACA;MACA,MAAM+D,IAAI,GAAGa,EAAE,CAACb,IAAI;MACpB,MAAMoG,gBAAgB,GAAGpG,IAAI,CAACqG,iBAAiB,GACxCC,GAAG,IAAK;QACP;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,IAAIA,GAAG,KAAK7J,SAAS,IAAI,CAAC0J,MAAI,CAACvI,4BAA4B,EAAE;UACzDuI,MAAI,CAACvI,4BAA4B,GAAG,IAAI;UACxC0I,GAAG,CAACC,QAAQ,CAAC,MAAM;YACfJ,MAAI,CAACvI,4BAA4B,GAAG,KAAK;UAC7C,CAAC,EAAE;YAAE4I,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;AACpB;AACA;AACA;AACA;AACA;UACoBF,GAAG,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,CAAC,MACI;UACDN,MAAI,CAACO,KAAK,GAAGJ,GAAG;QACpB;MACJ,CAAC,GACC7J,SAAS;MACf,MAAMuC,IAAI,GAAGpF,qDAAU,CAACuM,MAAI,CAAC;MAC7B,MAAMlE,UAAU,GAAG0C,YAAY,CAACzI,OAAO;MACvC;MACA,MAAMyK,SAAS,GAAG/B,WAAW,IAAIA,WAAW,CAAC1I,OAAO;MACpD,MAAM0K,aAAa,GAAGlE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE3D,IAAI;QAAE6H,UAAU,EAAEV,MAAI,CAAC5C,aAAa,CAACoB,YAAY,CAAC;QAAEmC,MAAM,EAAEX,MAAI,CAACrH,EAAE;QAAEsH,gBAAgB;QAAElI,QAAQ,EAAEiI,MAAI,CAACjI,QAAQ,IAAIpE,iDAAM,CAACmF,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;QAAEgD,UAAU;QACjN0E;MAAU,CAAC,EAAE3G,IAAI,CAAC,EAAE;QAAE4C,gBAAgB,EAAE5C,IAAI,CAAC4C,gBAAgB,IAAIuD,MAAI,CAAC7E,SAAS,IAAIxH,iDAAM,CAACiN,GAAG,CAAC,cAAc;MAAE,CAAC,CAAC;MACpH,MAAM;QAAEtC;MAAa,CAAC,SAAS5J,qDAAU,CAAC+L,aAAa,CAAC;MACxD,OAAOT,MAAI,CAACa,gBAAgB,CAACvC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE5E,IAAI,CAAC;IAAC;EAChF;EACAgH,gBAAgBA,CAACvC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE5E,IAAI,EAAE;IAC5D;AACR;AACA;AACA;IACQ,MAAMiH,UAAU,GAAGxC,YAAY,GAAGE,YAAY,GAAGC,WAAW;IAC5D,IAAIqC,UAAU,EAAE;MACZ,IAAI,CAACC,oBAAoB,CAACD,UAAU,CAAC;IACzC;IACA,OAAO;MACHxC,YAAY;MACZC,kBAAkB,EAAE,IAAI;MACxBC,YAAY;MACZC,WAAW;MACXvD,SAAS,EAAErB,IAAI,CAACqB;IACpB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4E,YAAYA,CAACtJ,IAAI,EAAEwG,KAAK,EAAE;IACtB,MAAMnF,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmJ,aAAa,GAAGnJ,KAAK,CAAC6F,OAAO,CAAClH,IAAI,CAAC;IACzC,IAAIwK,aAAa,GAAG,CAAC,CAAC,EAAE;MACpBzM,uDAAM,CAACiC,IAAI,CAACH,GAAG,KAAK,IAAI,EAAE,6BAA6B,CAAC;MACxD;MACAwB,KAAK,CAACoJ,MAAM,CAACD,aAAa,EAAE,CAAC,CAAC;MAC9B;MACAnJ,KAAK,CAACoJ,MAAM,CAACjE,KAAK,EAAE,CAAC,EAAExG,IAAI,CAAC;IAChC,CAAC,MACI;MACDjC,uDAAM,CAAC,CAACiC,IAAI,CAACH,GAAG,EAAE,aAAa,CAAC;MAChC;MACA;MACAG,IAAI,CAACH,GAAG,GAAG,IAAI;MACfwB,KAAK,CAACoJ,MAAM,CAACjE,KAAK,EAAE,CAAC,EAAExG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqE,UAAUA,CAACrE,IAAI,EAAE;IACbjC,uDAAM,CAACiC,IAAI,CAACd,KAAK,KAAKN,mBAAmB,IAAIoB,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE,0CAA0C,CAAC;IAC7H,MAAMwC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC6F,OAAO,CAAClH,IAAI,CAAC;IACjCjC,uDAAM,CAACyI,KAAK,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC;IACpD,IAAIA,KAAK,IAAI,CAAC,EAAE;MACZnF,KAAK,CAACoJ,MAAM,CAACjE,KAAK,EAAE,CAAC,CAAC;IAC1B;EACJ;EACA+C,WAAWA,CAACvJ,IAAI,EAAE;IACdA,IAAI,CAACP,QAAQ,CAAC,CAAC;IACf,IAAI,CAAC4E,UAAU,CAACrE,IAAI,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuK,oBAAoBA,CAACD,UAAU,EAAE;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAAClJ,SAAS,EAAE;MAChB;IACJ;IACA,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMqJ,eAAe,GAAGrJ,KAAK,CAAC6F,OAAO,CAACoD,UAAU,CAAC;IACjD,KAAK,IAAInB,CAAC,GAAG9H,KAAK,CAAC8B,MAAM,GAAG,CAAC,EAAEgG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxC,MAAMnJ,IAAI,GAAGqB,KAAK,CAAC8H,CAAC,CAAC;MACrB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM5J,OAAO,GAAGS,IAAI,CAACT,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,IAAI4J,CAAC,GAAGuB,eAAe,EAAE;UACrB;UACA;UACA1M,qDAAS,CAACuB,OAAO,EAAEnB,iDAAqB,CAAC;UACzC,IAAI,CAACmL,WAAW,CAACvJ,IAAI,CAAC;QAC1B,CAAC,MACI,IAAImJ,CAAC,GAAGuB,eAAe,EAAE;UAC1B;UACA;UACAvM,qDAAa,CAACoB,OAAO,EAAE,IAAI,CAAC;QAChC;MACJ;IACJ;EACJ;EACAoD,QAAQA,CAAA,EAAG;IACP,OAAQ,CAAC,IAAI,CAAC1B,4BAA4B,IACtC,CAAC,CAAC,IAAI,CAACU,YAAY,IACnB,CAAC,IAAI,CAACR,eAAe,IACrB,IAAI,CAACH,UAAU,CAACmC,MAAM,KAAK,CAAC,IAC5B,IAAI,CAACyD,aAAa,CAAC,CAAC;EAC5B;EACA/D,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC5B,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAAC6C,GAAG,CAAC;MAAEY,SAAS,EAAE,MAAM;MAAEgF,iBAAiB,EAAE;IAAK,CAAC,CAAC;EAC5D;EACA5G,MAAMA,CAAC6H,SAAS,EAAE;IACd,IAAI,IAAI,CAACZ,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACa,YAAY,CAACD,SAAS,CAAC;IACtC;EACJ;EACA5H,KAAKA,CAAC8H,cAAc,EAAEF,SAAS,EAAEG,GAAG,EAAE;IAClC,IAAI,IAAI,CAACf,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACH,QAAQ,CAAC,MAAM;QACtB,IAAI,CAAC3I,4BAA4B,GAAG,KAAK;MAC7C,CAAC,EAAE;QAAE4I,eAAe,EAAE;MAAK,CAAC,CAAC;MAC7B;MACA,IAAIkB,YAAY,GAAGF,cAAc,GAAG,CAAC,IAAI,GAAG,KAAK;MACjD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACA,cAAc,EAAE;QACjB,IAAI,CAACd,KAAK,CAACiB,MAAM,CAAC,gCAAgC,CAAC;QACnDD,YAAY,IAAInN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE+M,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/F,CAAC,MACI;QACDI,YAAY,IAAInN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE+M,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/F;MACA,IAAI,CAACZ,KAAK,CAACD,WAAW,CAACe,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEE,YAAY,EAAED,GAAG,CAAC;IACrE,CAAC,MACI;MACD,IAAI,CAAC7J,4BAA4B,GAAG,KAAK;IAC7C;EACJ;EACAgK,MAAMA,CAAA,EAAG;IACL,OAAO3N,qDAAC,CAAC,MAAM,EAAE;MAAE4N,GAAG,EAAE;IAA2C,CAAC,CAAC;EACzE;EACA,IAAI/I,EAAEA,CAAA,EAAG;IAAE,OAAO3E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW2N,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACDxK,GAAG,CAACyK,KAAK,GAAG1K,MAAM;AAElB,MAAM2K,OAAO,GAAGA,CAAClJ,EAAE,EAAEmJ,eAAe,EAAEtM,SAAS,EAAEuB,cAAc,EAAEgL,eAAe,KAAK;EACjF,MAAM1L,GAAG,GAAGsC,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;EACjC,IAAIvC,GAAG,EAAE;IACL,IAAIyL,eAAe,KAAK,SAAS,EAAE;MAC/B,IAAItM,SAAS,KAAKc,SAAS,EAAE;QACzB,OAAOD,GAAG,CAACuD,IAAI,CAACpE,SAAS,EAAEuB,cAAc,EAAE;UAAE+G,UAAU,EAAE,IAAI;UAAErB,gBAAgB,EAAEsF;QAAgB,CAAC,CAAC;MACvG;IACJ,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACjC,IAAItM,SAAS,KAAKc,SAAS,EAAE;QACzB,OAAOD,GAAG,CAACiC,OAAO,CAAC9C,SAAS,EAAEuB,cAAc,EAAE;UAAE+G,UAAU,EAAE,IAAI;UAAErB,gBAAgB,EAAEsF;QAAgB,CAAC,CAAC;MAC1G;IACJ,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACjC,OAAOzL,GAAG,CAACiE,GAAG,CAAC;QAAEwD,UAAU,EAAE,IAAI;QAAErB,gBAAgB,EAAEsF;MAAgB,CAAC,CAAC;IAC3E;EACJ;EACA,OAAOzG,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;AACjC,CAAC;AAED,MAAMyG,OAAO,GAAG,MAAM;EAClBzM,WAAWA,CAAC6B,OAAO,EAAE;IACjB/D,qDAAgB,CAAC,IAAI,EAAE+D,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAAC0K,eAAe,GAAG,SAAS;IAChC,IAAI,CAACG,OAAO,GAAG,MAAM;MACjB,OAAOJ,OAAO,CAAC,IAAI,CAAClJ,EAAE,EAAE,IAAI,CAACmJ,eAAe,EAAE,IAAI,CAACtM,SAAS,EAAE,IAAI,CAACuB,cAAc,EAAE,IAAI,CAACgL,eAAe,CAAC;IAC5G,CAAC;EACL;EACAN,MAAMA,CAAA,EAAG;IACL,OAAO3N,qDAAC,CAACI,iDAAI,EAAE;MAAEwN,GAAG,EAAE,0CAA0C;MAAEO,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;EAC9F;EACA,IAAItJ,EAAEA,CAAA,EAAG;IAAE,OAAO3E,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-nav_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, l as config, m as printIonWarning, h, k as getElement, j as Host } from './index-B_U9CtaY.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { s as shallowEqualStringMap, l as assert } from './helpers-1O4D2b7y.js';\nimport { l as lifecycle, t as transition, s as set<PERSON>ageHidden, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-DfBA5ztX.js';\nimport { a as attachComponent } from './framework-delegate-DxcnWic_.js';\n\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n    constructor(component, params) {\n        this.component = component;\n        this.params = params;\n        this.state = VIEW_STATE_NEW;\n    }\n    async init(container) {\n        this.state = VIEW_STATE_ATTACHED;\n        if (!this.element) {\n            const component = this.component;\n            this.element = await attachComponent(this.delegate, container, component, ['ion-page', 'ion-page-invisible'], this.params);\n        }\n    }\n    /**\n     * DOM WRITE\n     */\n    _destroy() {\n        assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n        const element = this.element;\n        if (element) {\n            if (this.delegate) {\n                this.delegate.removeViewFromDom(element.parentElement, element);\n            }\n            else {\n                element.remove();\n            }\n        }\n        this.nav = undefined;\n        this.state = VIEW_STATE_DESTROYED;\n    }\n}\nconst matches = (view, id, params) => {\n    if (!view) {\n        return false;\n    }\n    if (view.component !== id) {\n        return false;\n    }\n    return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n    if (!page) {\n        return null;\n    }\n    if (page instanceof ViewController) {\n        return page;\n    }\n    return new ViewController(page, params);\n};\nconst convertToViews = (pages) => {\n    return pages\n        .map((page) => {\n        if (page instanceof ViewController) {\n            return page;\n        }\n        if ('component' in page) {\n            return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n        }\n        return convertToView(page, undefined);\n    })\n        .filter((v) => v !== null);\n};\n\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\n\nconst Nav = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.transInstr = [];\n        this.gestureOrAnimationInProgress = false;\n        this.useRouter = false;\n        this.isTransitioning = false;\n        this.destroyed = false;\n        this.views = [];\n        this.didLoad = false;\n        /**\n         * If `true`, the nav should animate the transition of components.\n         */\n        this.animated = true;\n    }\n    swipeGestureChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeGesture === true);\n        }\n    }\n    rootChanged() {\n        if (this.root === undefined) {\n            return;\n        }\n        if (this.didLoad === false) {\n            /**\n             * If the component has not loaded yet, we can skip setting up the root component.\n             * It will be called when `componentDidLoad` fires.\n             */\n            return;\n        }\n        if (!this.useRouter) {\n            if (this.root !== undefined) {\n                this.setRoot(this.root, this.rootParams);\n            }\n        }\n    }\n    componentWillLoad() {\n        this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n        if (this.swipeGesture === undefined) {\n            const mode = getIonMode(this);\n            this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n        }\n        this.ionNavWillLoad.emit();\n    }\n    async componentDidLoad() {\n        // We want to set this flag before any watch callbacks are manually called\n        this.didLoad = true;\n        this.rootChanged();\n        this.gesture = (await import('./swipe-back-VdaUzLWy.js')).createSwipeBackGesture(this.el, this.canStart.bind(this), this.onStart.bind(this), this.onMove.bind(this), this.onEnd.bind(this));\n        this.swipeGestureChanged();\n    }\n    connectedCallback() {\n        this.destroyed = false;\n    }\n    disconnectedCallback() {\n        for (const view of this.views) {\n            lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            view._destroy();\n        }\n        // Release swipe back gesture and transition.\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.transInstr.length = 0;\n        this.views.length = 0;\n        this.destroyed = true;\n    }\n    /**\n     * Push a new component onto the current navigation stack. Pass any additional\n     * information along as an object. This additional information is accessible\n     * through NavParams.\n     *\n     * @param component The component to push onto the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    push(component, componentProps, opts, done) {\n        return this.insert(-1, component, componentProps, opts, done);\n    }\n    /**\n     * Inserts a component into the navigation stack at the specified index.\n     * This is useful to add a component at any point in the navigation stack.\n     *\n     * @param insertIndex The index to insert the component at in the stack.\n     * @param component The component to insert into the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insert(insertIndex, component, componentProps, opts, done) {\n        return this.insertPages(insertIndex, [{ component, componentProps }], opts, done);\n    }\n    /**\n     * Inserts an array of components into the navigation stack at the specified index.\n     * The last component in the array will become instantiated as a view, and animate\n     * in to become the active view.\n     *\n     * @param insertIndex The index to insert the components at in the stack.\n     * @param insertComponents The components to insert into the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insertPages(insertIndex, insertComponents, opts, done) {\n        return this.queueTrns({\n            insertStart: insertIndex,\n            insertViews: insertComponents,\n            opts,\n        }, done);\n    }\n    /**\n     * Pop a component off of the navigation stack. Navigates back from the current\n     * component.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    pop(opts, done) {\n        return this.removeIndex(-1, 1, opts, done);\n    }\n    /**\n     * Pop to a specific index in the navigation stack.\n     *\n     * @param indexOrViewCtrl The index or view controller to pop to.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popTo(indexOrViewCtrl, opts, done) {\n        const ti = {\n            removeStart: -1,\n            removeCount: -1,\n            opts,\n        };\n        if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n            ti.removeView = indexOrViewCtrl;\n            ti.removeStart = 1;\n        }\n        else if (typeof indexOrViewCtrl === 'number') {\n            ti.removeStart = indexOrViewCtrl + 1;\n        }\n        return this.queueTrns(ti, done);\n    }\n    /**\n     * Navigate back to the root of the stack, no matter how far back that is.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popToRoot(opts, done) {\n        return this.removeIndex(1, -1, opts, done);\n    }\n    /**\n     * Removes a component from the navigation stack at the specified index.\n     *\n     * @param startIndex The number to begin removal at.\n     * @param removeCount The number of components to remove.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    removeIndex(startIndex, removeCount = 1, opts, done) {\n        return this.queueTrns({\n            removeStart: startIndex,\n            removeCount,\n            opts,\n        }, done);\n    }\n    /**\n     * Set the root for the current navigation stack to a component.\n     *\n     * @param component The component to set as the root of the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setRoot(component, componentProps, opts, done) {\n        return this.setPages([{ component, componentProps }], opts, done);\n    }\n    /**\n     * Set the views of the current navigation stack and navigate to the last view.\n     * By default animations are disabled, but they can be enabled by passing options\n     * to the navigation controller. Navigation parameters can also be passed to the\n     * individual pages in the array.\n     *\n     * @param views The list of views to set as the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setPages(views, opts, done) {\n        opts !== null && opts !== void 0 ? opts : (opts = {});\n        // if animation wasn't set to true then default it to NOT animate\n        if (opts.animated !== true) {\n            opts.animated = false;\n        }\n        return this.queueTrns({\n            insertStart: 0,\n            insertViews: views,\n            removeStart: 0,\n            removeCount: -1,\n            opts,\n        }, done);\n    }\n    /**\n     * Called by the router to update the view.\n     *\n     * @param id The component tag.\n     * @param params The component params.\n     * @param direction A direction hint.\n     * @param animation an AnimationBuilder.\n     *\n     * @return the status.\n     * @internal\n     */\n    setRouteId(id, params, direction, animation) {\n        const active = this.getActiveSync();\n        if (matches(active, id, params)) {\n            return Promise.resolve({\n                changed: false,\n                element: active.element,\n            });\n        }\n        let resolve;\n        const promise = new Promise((r) => (resolve = r));\n        let finish;\n        const commonOpts = {\n            updateURL: false,\n            viewIsReady: (enteringEl) => {\n                let mark;\n                const p = new Promise((r) => (mark = r));\n                resolve({\n                    changed: true,\n                    element: enteringEl,\n                    markVisible: async () => {\n                        mark();\n                        await finish;\n                    },\n                });\n                return p;\n            },\n        };\n        if (direction === 'root') {\n            finish = this.setRoot(id, params, commonOpts);\n        }\n        else {\n            // Look for a view matching the target in the view stack.\n            const viewController = this.views.find((v) => matches(v, id, params));\n            if (viewController) {\n                finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animationBuilder: animation }));\n            }\n            else if (direction === 'forward') {\n                finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), { animationBuilder: animation }));\n            }\n            else if (direction === 'back') {\n                finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animated: true, animationBuilder: animation }));\n            }\n        }\n        return promise;\n    }\n    /**\n     * Called by <ion-router> to retrieve the current component.\n     *\n     * @internal\n     */\n    async getRouteId() {\n        const active = this.getActiveSync();\n        if (active) {\n            return {\n                id: active.element.tagName,\n                params: active.params,\n                element: active.element,\n            };\n        }\n        return undefined;\n    }\n    /**\n     * Get the active view.\n     */\n    async getActive() {\n        return this.getActiveSync();\n    }\n    /**\n     * Get the view at the specified index.\n     *\n     * @param index The index of the view.\n     */\n    async getByIndex(index) {\n        return this.views[index];\n    }\n    /**\n     * Returns `true` if the current view can go back.\n     *\n     * @param view The view to check.\n     */\n    async canGoBack(view) {\n        return this.canGoBackSync(view);\n    }\n    /**\n     * Get the previous view.\n     *\n     * @param view The view to get.\n     */\n    async getPrevious(view) {\n        return this.getPreviousSync(view);\n    }\n    /**\n     * Returns the number of views in the stack.\n     */\n    async getLength() {\n        return Promise.resolve(this.views.length);\n    }\n    getActiveSync() {\n        return this.views[this.views.length - 1];\n    }\n    canGoBackSync(view = this.getActiveSync()) {\n        return !!(view && this.getPreviousSync(view));\n    }\n    getPreviousSync(view = this.getActiveSync()) {\n        if (!view) {\n            return undefined;\n        }\n        const views = this.views;\n        const index = views.indexOf(view);\n        return index > 0 ? views[index - 1] : undefined;\n    }\n    /**\n     * Adds a navigation stack change to the queue and schedules it to run.\n     *\n     * @returns Whether the transition succeeds.\n     */\n    async queueTrns(ti, done) {\n        var _a, _b;\n        if (this.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n            return false;\n        }\n        const promise = new Promise((resolve, reject) => {\n            ti.resolve = resolve;\n            ti.reject = reject;\n        });\n        ti.done = done;\n        /**\n         * If using router, check to see if navigation hooks\n         * will allow us to perform this transition. This\n         * is required in order for hooks to work with\n         * the ion-back-button or swipe to go back.\n         */\n        if (ti.opts && ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const canTransition = await router.canTransition();\n                if (canTransition === false) {\n                    return false;\n                }\n                if (typeof canTransition === 'string') {\n                    router.push(canTransition, ti.opts.direction || 'back');\n                    return false;\n                }\n            }\n        }\n        // Normalize empty\n        if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n            ti.insertViews = undefined;\n        }\n        // Enqueue transition instruction\n        this.transInstr.push(ti);\n        // if there isn't a transition already happening\n        // then this will kick off this transition\n        this.nextTrns();\n        return promise;\n    }\n    success(result, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        if (ti.done) {\n            ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n        }\n        ti.resolve(result.hasCompleted);\n        if (ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const direction = result.direction === 'back' ? 'back' : 'forward';\n                router.navChanged(direction);\n            }\n        }\n    }\n    failed(rejectReason, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        this.transInstr.length = 0;\n        this.fireError(rejectReason, ti);\n    }\n    fireError(rejectReason, ti) {\n        if (ti.done) {\n            ti.done(false, false, rejectReason);\n        }\n        if (ti.reject && !this.destroyed) {\n            ti.reject(rejectReason);\n        }\n        else {\n            ti.resolve(false);\n        }\n    }\n    /**\n     * Consumes the next transition in the queue.\n     *\n     * @returns whether the transition is executed.\n     */\n    nextTrns() {\n        // this is the framework's bread 'n butta function\n        // only one transition is allowed at any given time\n        if (this.isTransitioning) {\n            return false;\n        }\n        // there is no transition happening right now, executes the next instructions.\n        const ti = this.transInstr.shift();\n        if (!ti) {\n            return false;\n        }\n        this.runTransition(ti);\n        return true;\n    }\n    /** Executes all the transition instruction from the queue. */\n    async runTransition(ti) {\n        try {\n            // set that this nav is actively transitioning\n            this.ionNavWillChange.emit();\n            this.isTransitioning = true;\n            this.prepareTI(ti);\n            const leavingView = this.getActiveSync();\n            const enteringView = this.getEnteringView(ti, leavingView);\n            if (!leavingView && !enteringView) {\n                throw new Error('no views in the stack to be removed');\n            }\n            if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n                await enteringView.init(this.el);\n            }\n            this.postViewInit(enteringView, leavingView, ti);\n            // Needs transition?\n            const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n            if (requiresTransition && ti.opts && leavingView) {\n                const isBackDirection = ti.opts.direction === 'back';\n                /**\n                 * If heading back, use the entering page's animation\n                 * unless otherwise specified by the developer.\n                 */\n                if (isBackDirection) {\n                    ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n                }\n                leavingView.animationBuilder = ti.opts.animationBuilder;\n            }\n            let result;\n            if (requiresTransition) {\n                result = await this.transition(enteringView, leavingView, ti);\n            }\n            else {\n                // transition is not required, so we are already done!\n                // they're inserting/removing the views somewhere in the middle or\n                // beginning, so visually nothing needs to animate/transition\n                // resolve immediately because there's no animation that's happening\n                result = {\n                    hasCompleted: true,\n                    requiresTransition: false,\n                };\n            }\n            this.success(result, ti);\n            this.ionNavDidChange.emit();\n        }\n        catch (rejectReason) {\n            this.failed(rejectReason, ti);\n        }\n        this.isTransitioning = false;\n        this.nextTrns();\n    }\n    prepareTI(ti) {\n        var _a, _b;\n        var _c;\n        const viewsLength = this.views.length;\n        (_a = ti.opts) !== null && _a !== void 0 ? _a : (ti.opts = {});\n        (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : (_c.delegate = this.delegate);\n        if (ti.removeView !== undefined) {\n            assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n            assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n            const index = this.views.indexOf(ti.removeView);\n            if (index < 0) {\n                throw new Error('removeView was not found');\n            }\n            ti.removeStart += index;\n        }\n        if (ti.removeStart !== undefined) {\n            if (ti.removeStart < 0) {\n                ti.removeStart = viewsLength - 1;\n            }\n            if (ti.removeCount < 0) {\n                ti.removeCount = viewsLength - ti.removeStart;\n            }\n            ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n        }\n        if (ti.insertViews) {\n            // allow -1 to be passed in to auto push it on the end\n            // and clean up the index if it's larger then the size of the stack\n            if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n                ti.insertStart = viewsLength;\n            }\n            ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n        }\n        const insertViews = ti.insertViews;\n        if (!insertViews) {\n            return;\n        }\n        assert(insertViews.length > 0, 'length can not be zero');\n        const viewControllers = convertToViews(insertViews);\n        if (viewControllers.length === 0) {\n            throw new Error('invalid views to insert');\n        }\n        // Check all the inserted view are correct\n        for (const view of viewControllers) {\n            view.delegate = ti.opts.delegate;\n            const nav = view.nav;\n            if (nav && nav !== this) {\n                throw new Error('inserted view was already inserted');\n            }\n            if (view.state === VIEW_STATE_DESTROYED) {\n                throw new Error('inserted view was already destroyed');\n            }\n        }\n        ti.insertViews = viewControllers;\n    }\n    /**\n     * Returns the view that will be entered considering the transition instructions.\n     *\n     * @param ti The instructions.\n     * @param leavingView The view being left or undefined if none.\n     *\n     * @returns The view that will be entered, undefined if none.\n     */\n    getEnteringView(ti, leavingView) {\n        // The last inserted view will be entered when view are inserted.\n        const insertViews = ti.insertViews;\n        if (insertViews !== undefined) {\n            return insertViews[insertViews.length - 1];\n        }\n        // When views are deleted, we will enter the last view that is not removed and not the view being left.\n        const removeStart = ti.removeStart;\n        if (removeStart !== undefined) {\n            const views = this.views;\n            const removeEnd = removeStart + ti.removeCount;\n            for (let i = views.length - 1; i >= 0; i--) {\n                const view = views[i];\n                if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n                    return view;\n                }\n            }\n        }\n        return undefined;\n    }\n    /**\n     * Adds and Removes the views from the navigation stack.\n     *\n     * @param enteringView The view being entered.\n     * @param leavingView The view being left.\n     * @param ti The instructions.\n     */\n    postViewInit(enteringView, leavingView, ti) {\n        var _a, _b, _c;\n        assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n        assert(ti.resolve, 'resolve must be valid');\n        assert(ti.reject, 'reject must be valid');\n        // Compute the views to remove.\n        const opts = ti.opts;\n        const { insertViews, removeStart, removeCount } = ti;\n        /** Records the view to destroy */\n        let destroyQueue;\n        // there are views to remove\n        if (removeStart !== undefined && removeCount !== undefined) {\n            assert(removeStart >= 0, 'removeStart can not be negative');\n            assert(removeCount >= 0, 'removeCount can not be negative');\n            destroyQueue = [];\n            for (let i = removeStart; i < removeStart + removeCount; i++) {\n                const view = this.views[i];\n                if (view !== undefined && view !== enteringView && view !== leavingView) {\n                    destroyQueue.push(view);\n                }\n            }\n            // default the direction to \"back\"\n            (_a = opts.direction) !== null && _a !== void 0 ? _a : (opts.direction = 'back');\n        }\n        const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n        assert(finalNumViews >= 0, 'final balance can not be negative');\n        if (finalNumViews === 0) {\n            printIonWarning(`[ion-nav] - You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n            throw new Error('navigation stack needs at least one root page');\n        }\n        // At this point the transition can not be rejected, any throw should be an error\n        // Insert the new views in the stack.\n        if (insertViews) {\n            // add the views to the\n            let insertIndex = ti.insertStart;\n            for (const view of insertViews) {\n                this.insertViewAt(view, insertIndex);\n                insertIndex++;\n            }\n            if (ti.enteringRequiresTransition) {\n                // default to forward if not already set\n                (_c = opts.direction) !== null && _c !== void 0 ? _c : (opts.direction = 'forward');\n            }\n        }\n        // if the views to be removed are in the beginning or middle\n        // and there is not a view that needs to visually transition out\n        // then just destroy them and don't transition anything\n        // batch all of lifecycles together\n        // let's make sure, callbacks are zoned\n        if (destroyQueue && destroyQueue.length > 0) {\n            for (const view of destroyQueue) {\n                lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n                lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n                lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            }\n            // once all lifecycle events has been delivered, we can safely detroy the views\n            for (const view of destroyQueue) {\n                this.destroyView(view);\n            }\n        }\n    }\n    async transition(enteringView, leavingView, ti) {\n        // we should animate (duration > 0) if the pushed page is not the first one (startup)\n        // or if it is a portal (modal, actionsheet, etc.)\n        const opts = ti.opts;\n        const progressCallback = opts.progressAnimation\n            ? (ani) => {\n                /**\n                 * Because this progress callback is called asynchronously\n                 * it is possible for the gesture to start and end before\n                 * the animation is ever set. In that scenario, we should\n                 * immediately call progressEnd so that the transition promise\n                 * resolves and the gesture does not get locked up.\n                 */\n                if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                    this.gestureOrAnimationInProgress = true;\n                    ani.onFinish(() => {\n                        this.gestureOrAnimationInProgress = false;\n                    }, { oneTimeCallback: true });\n                    /**\n                     * Playing animation to beginning\n                     * with a duration of 0 prevents\n                     * any flickering when the animation\n                     * is later cleaned up.\n                     */\n                    ani.progressEnd(0, 0, 0);\n                }\n                else {\n                    this.sbAni = ani;\n                }\n            }\n            : undefined;\n        const mode = getIonMode(this);\n        const enteringEl = enteringView.element;\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const leavingEl = leavingView && leavingView.element;\n        const animationOpts = Object.assign(Object.assign({ mode, showGoBack: this.canGoBackSync(enteringView), baseEl: this.el, progressCallback, animated: this.animated && config.getBoolean('animated', true), enteringEl,\n            leavingEl }, opts), { animationBuilder: opts.animationBuilder || this.animation || config.get('navAnimation') });\n        const { hasCompleted } = await transition(animationOpts);\n        return this.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n    }\n    transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n        /**\n         * If the transition did not complete, the leavingView will still be the active\n         * view on the stack. Otherwise unmount all the views after the enteringView.\n         */\n        const activeView = hasCompleted ? enteringView : leavingView;\n        if (activeView) {\n            this.unmountInactiveViews(activeView);\n        }\n        return {\n            hasCompleted,\n            requiresTransition: true,\n            enteringView,\n            leavingView,\n            direction: opts.direction,\n        };\n    }\n    /**\n     * Inserts a view at the specified index.\n     *\n     * When the view already is in the stack it will be moved to the new position.\n     *\n     * @param view The view to insert.\n     * @param index The index where to insert the view.\n     */\n    insertViewAt(view, index) {\n        const views = this.views;\n        const existingIndex = views.indexOf(view);\n        if (existingIndex > -1) {\n            assert(view.nav === this, 'view is not part of the nav');\n            // The view already in the stack, removes it.\n            views.splice(existingIndex, 1);\n            // and add it back at the requested index.\n            views.splice(index, 0, view);\n        }\n        else {\n            assert(!view.nav, 'nav is used');\n            // this is a new view to add to the stack\n            // create the new entering view\n            view.nav = this;\n            views.splice(index, 0, view);\n        }\n    }\n    /**\n     * Removes a view from the stack.\n     *\n     * @param view The view to remove.\n     */\n    removeView(view) {\n        assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n        const views = this.views;\n        const index = views.indexOf(view);\n        assert(index > -1, 'view must be part of the stack');\n        if (index >= 0) {\n            views.splice(index, 1);\n        }\n    }\n    destroyView(view) {\n        view._destroy();\n        this.removeView(view);\n    }\n    /**\n     * Unmounts all inactive views after the specified active view.\n     *\n     * DOM WRITE\n     *\n     * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n     */\n    unmountInactiveViews(activeView) {\n        // ok, cleanup time!! Destroy all of the views that are\n        // INACTIVE and come after the active view\n        // only do this if the views exist, though\n        if (this.destroyed) {\n            return;\n        }\n        const views = this.views;\n        const activeViewIndex = views.indexOf(activeView);\n        for (let i = views.length - 1; i >= 0; i--) {\n            const view = views[i];\n            /**\n             * When inserting multiple views via insertPages\n             * the last page will be transitioned to, but the\n             * others will not be. As a result, a DOM element\n             * will only be created for the last page inserted.\n             * As a result, it is possible to have views in the\n             * stack that do not have `view.element` yet.\n             */\n            const element = view.element;\n            if (element) {\n                if (i > activeViewIndex) {\n                    // this view comes after the active view\n                    // let's unload it\n                    lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n                    this.destroyView(view);\n                }\n                else if (i < activeViewIndex) {\n                    // this view comes before the active view\n                    // and it is not a portal then ensure it is hidden\n                    setPageHidden(element, true);\n                }\n            }\n        }\n    }\n    canStart() {\n        return (!this.gestureOrAnimationInProgress &&\n            !!this.swipeGesture &&\n            !this.isTransitioning &&\n            this.transInstr.length === 0 &&\n            this.canGoBackSync());\n    }\n    onStart() {\n        this.gestureOrAnimationInProgress = true;\n        this.pop({ direction: 'back', progressAnimation: true });\n    }\n    onMove(stepValue) {\n        if (this.sbAni) {\n            this.sbAni.progressStep(stepValue);\n        }\n    }\n    onEnd(shouldComplete, stepValue, dur) {\n        if (this.sbAni) {\n            this.sbAni.onFinish(() => {\n                this.gestureOrAnimationInProgress = false;\n            }, { oneTimeCallback: true });\n            // Account for rounding errors in JS\n            let newStepValue = shouldComplete ? -1e-3 : 0.001;\n            /**\n             * Animation will be reversed here, so need to\n             * reverse the easing curve as well\n             *\n             * Additionally, we need to account for the time relative\n             * to the new easing curve, as `stepValue` is going to be given\n             * in terms of a linear curve.\n             */\n            if (!shouldComplete) {\n                this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n            }\n            else {\n                newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n            }\n            this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n        }\n        else {\n            this.gestureOrAnimationInProgress = false;\n        }\n    }\n    render() {\n        return h(\"slot\", { key: '8067c9835d255daec61f33dba200fd3a6ff839a0' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeGesture\": [\"swipeGestureChanged\"],\n        \"root\": [\"rootChanged\"]\n    }; }\n};\nNav.style = navCss;\n\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n    const nav = el.closest('ion-nav');\n    if (nav) {\n        if (routerDirection === 'forward') {\n            if (component !== undefined) {\n                return nav.push(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'root') {\n            if (component !== undefined) {\n                return nav.setRoot(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'back') {\n            return nav.pop({ skipIfBusy: true, animationBuilder: routerAnimation });\n        }\n    }\n    return Promise.resolve(false);\n};\n\nconst NavLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * The transition direction when navigating to another page.\n         */\n        this.routerDirection = 'forward';\n        this.onClick = () => {\n            return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n        };\n    }\n    render() {\n        return h(Host, { key: '6dbb1ad4f351e9215375aac11ab9b53762e07a08', onClick: this.onClick });\n    }\n    get el() { return getElement(this); }\n};\n\nexport { Nav as ion_nav, NavLink as ion_nav_link };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "l", "config", "m", "printIonWarning", "h", "k", "getElement", "j", "Host", "g", "getTimeGivenProgression", "s", "shallowEqualStringMap", "assert", "lifecycle", "t", "transition", "setPageHidden", "LIFECYCLE_WILL_UNLOAD", "b", "LIFECYCLE_WILL_LEAVE", "c", "LIFECYCLE_DID_LEAVE", "a", "attachComponent", "VIEW_STATE_NEW", "VIEW_STATE_ATTACHED", "VIEW_STATE_DESTROYED", "ViewController", "constructor", "component", "params", "state", "init", "container", "_this", "_asyncToGenerator", "element", "delegate", "_destroy", "removeViewFromDom", "parentElement", "remove", "nav", "undefined", "matches", "view", "id", "convertToView", "page", "convertToViews", "pages", "map", "componentProps", "filter", "v", "navCss", "Nav", "hostRef", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "transInstr", "gestureOrAnimationInProgress", "useRouter", "isTransitioning", "destroyed", "views", "didLoad", "animated", "swipeGestureChanged", "gesture", "enable", "swipeGesture", "rootChanged", "root", "setRoot", "rootParams", "componentWillLoad", "document", "querySelector", "el", "closest", "mode", "getBoolean", "emit", "componentDidLoad", "_this2", "createSwipeBackGesture", "canStart", "bind", "onStart", "onMove", "onEnd", "connectedCallback", "disconnectedCallback", "destroy", "length", "push", "opts", "done", "insert", "insertIndex", "insertPages", "insertComponents", "queueTrns", "insertStart", "insertViews", "pop", "removeIndex", "popTo", "indexOrViewCtrl", "ti", "removeStart", "removeCount", "<PERSON><PERSON><PERSON><PERSON>", "popToRoot", "startIndex", "setPages", "setRouteId", "direction", "animation", "active", "getActiveSync", "Promise", "resolve", "changed", "promise", "finish", "commonOpts", "updateURL", "viewIsReady", "enteringEl", "mark", "p", "markVisible", "_ref", "apply", "arguments", "viewController", "find", "Object", "assign", "animationBuilder", "getRouteId", "_this3", "tagName", "getActive", "_this4", "getByIndex", "index", "_this5", "canGoBack", "_this6", "canGoBackSync", "getPrevious", "_this7", "getPreviousSync", "<PERSON><PERSON><PERSON><PERSON>", "_this8", "indexOf", "_this9", "_a", "_b", "skipIfBusy", "reject", "router", "canTransition", "nextTrns", "success", "result", "fireError", "hasCompleted", "requiresTransition", "enteringView", "leavingView", "navChanged", "failed", "rejectReason", "shift", "runTransition", "_this0", "prepareTI", "getEnteringView", "Error", "postViewInit", "enteringRequiresTransition", "leavingRequiresTransition", "isBackDirection", "_c", "viewsLength", "viewControllers", "removeEnd", "i", "destroyQueue", "finalNumViews", "insertViewAt", "destroyView", "_this1", "progressCallback", "progressAnimation", "ani", "onFinish", "oneTimeCallback", "progressEnd", "sbAni", "leavingEl", "animationOpts", "showGoBack", "baseEl", "get", "transitionFinish", "activeView", "unmountInactiveViews", "existingIndex", "splice", "activeViewIndex", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "shouldComplete", "dur", "newStepValue", "easing", "render", "key", "watchers", "style", "navLink", "routerDirection", "routerAnimation", "NavLink", "onClick", "ion_nav", "ion_nav_link"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}