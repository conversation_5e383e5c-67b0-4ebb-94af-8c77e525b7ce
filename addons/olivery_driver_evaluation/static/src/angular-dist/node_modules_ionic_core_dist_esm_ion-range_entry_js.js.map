{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-range_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACoJ;AAC7B;AACuB;AAC/F;AACiC;AAEhF,SAAS4B,gBAAgBA,CAACC,CAAC,EAAE;EACzB,IAAI,CAACZ,uDAAY,CAACY,CAAC,CAAC,EAChB,OAAO,CAAC;EACZ,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,EACX,OAAO,CAAC;EACZ,OAAOA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACJ,CAAC,EAAE,GAAGK,UAAU,EAAE;EAC/C,IAAI,CAACjB,uDAAY,CAACY,CAAC,CAAC,EAChB,OAAO,CAAC;EACZ,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGH,UAAU,CAACI,GAAG,CAAEtC,CAAC,IAAK4B,gBAAgB,CAAC5B,CAAC,CAAC,CAAC,CAAC;EACzE,OAAOuC,MAAM,CAACV,CAAC,CAACW,OAAO,CAACL,SAAS,CAAC,CAAC;AACvC;AAEA,MAAMM,WAAW,GAAG,2tOAA2tO;AAE/uO,MAAMC,UAAU,GAAG,mjUAAmjU;AAEtkU,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB7C,qDAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAG5C,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6C,QAAQ,GAAG7C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8C,QAAQ,GAAG9C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+C,OAAO,GAAG/C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACgD,gBAAgB,GAAGhD,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACiD,cAAc,GAAGjD,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACkD,OAAO,GAAG,SAASC,QAAQ,EAAE,EAAE;IACpC,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACW,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ;AACR;AACA;IACQ,IAAI,CAAC5B,GAAG,GAAG,GAAG;IACd;AACR;AACA;AACA;IACQ,IAAI,CAAC6B,GAAG,GAAG,KAAK;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAIC,KAAK,IAAKhC,IAAI,CAACiC,KAAK,CAACD,KAAK,CAAC;IAChD;AACR;AACA;AACA;IACQ,IAAI,CAACE,KAAK,GAAG,KAAK;IAClB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,CAAC;IACb;AACR;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACL,KAAK,GAAG,CAAC;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACM,aAAa,GAAG,CAACC,MAAM,EAAEC,MAAM,KAAK;MACrC,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC1D,OAAOD,MAAM,CAACE,KAAK,KAAKD,MAAM,CAACC,KAAK,IAAIF,MAAM,CAACG,KAAK,KAAKF,MAAM,CAACE,KAAK;MACzE;MACA,OAAOH,MAAM,KAAKC,MAAM;IAC5B,CAAC;IACD,IAAI,CAACG,WAAW,GAAIX,KAAK,IAAK;MAC1B,OAAOlD,uDAAK,CAAC,IAAI,CAAC+C,GAAG,EAAEG,KAAK,EAAE,IAAI,CAAC/B,GAAG,CAAC;IAC3C,CAAC;IACD,IAAI,CAAC2C,mBAAmB,GAAIZ,KAAK,IAAK;MAClC,IAAI,IAAI,CAACJ,SAAS,EAAE;QAChB,OAAO;UACHa,KAAK,EAAE,IAAI,CAACE,WAAW,CAACX,KAAK,CAACS,KAAK,CAAC;UACpCC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACX,KAAK,CAACU,KAAK;QACvC,CAAC;MACL,CAAC,MACI;QACD,OAAO,IAAI,CAACC,WAAW,CAACX,KAAK,CAAC;MAClC;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACa,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,YAAY,gBAAAC,yMAAA,CAAG,aAAY;MAC5B,MAAMC,WAAW,GAAGtC,KAAI,CAACsC,WAAW;MACpC,IAAIA,WAAW,EAAE;QACbtC,KAAI,CAACuC,OAAO,GAAG,OAAO,qHAA6B,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,OAAO;UACpBC,eAAe,EAAE,GAAG;UACpB;AACpB;AACA;AACA;AACA;AACA;UACoBC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAEA,CAAA,KAAM7C,KAAI,CAAC6C,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAK/C,KAAI,CAAC8C,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAK/C,KAAI,CAACgD,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACF/C,KAAI,CAACuC,OAAO,CAACU,MAAM,CAAC,CAACjD,KAAI,CAAC2B,QAAQ,CAAC;MACvC;IACJ,CAAC;IACD,IAAI,CAACuB,cAAc,GAAG,CAACC,IAAI,EAAEC,UAAU,KAAK;MACxC,MAAM;QAAElB;MAAoB,CAAC,GAAG,IAAI;MACpC,IAAIT,IAAI,GAAG,IAAI,CAACA,IAAI;MACpBA,IAAI,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC;MAC1BA,IAAI,GAAGA,IAAI,IAAI,IAAI,CAAClC,GAAG,GAAG,IAAI,CAAC4B,GAAG,CAAC;MACnC,IAAI,CAACiC,UAAU,EAAE;QACb3B,IAAI,IAAI,CAAC,CAAC;MACd;MACA,IAAI0B,IAAI,KAAK,GAAG,EAAE;QACd,IAAI,CAACpC,MAAM,GAAG3C,uDAAK,CAAC,CAAC,EAAE,IAAI,CAAC2C,MAAM,GAAGU,IAAI,EAAE,CAAC,CAAC;MACjD,CAAC,MACI;QACD,IAAI,CAACT,MAAM,GAAG5C,uDAAK,CAAC,CAAC,EAAE,IAAI,CAAC4C,MAAM,GAAGS,IAAI,EAAE,CAAC,CAAC;MACjD;MACA,IAAI,CAACpB,gBAAgB,CAACgD,IAAI,CAAC;QAAE/B,KAAK,EAAEY,mBAAmB,CAAC,IAAI,CAACZ,KAAK;MAAE,CAAC,CAAC;MACtE,IAAI,CAACgC,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACjD,cAAc,CAAC+C,IAAI,CAAC;QAAE/B,KAAK,EAAEY,mBAAmB,CAAC,IAAI,CAACZ,KAAK;MAAE,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,CAACkC,MAAM,GAAG,MAAM;MAChB,IAAI,IAAI,CAAC7C,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACP,OAAO,CAACiD,IAAI,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC,IAAI,CAAC9C,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACR,QAAQ,CAACkD,IAAI,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACK,WAAW,GAAIP,IAAI,IAAK;MACzB,IAAI,CAAC,IAAI,CAACxC,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACR,QAAQ,CAACkD,IAAI,CAAC,CAAC;MACxB;MACA;MACA,IAAI,IAAI,CAACnC,SAAS,IAAI,IAAI,CAACuB,EAAE,CAACkB,UAAU,EAAE;QACtC,MAAMC,KAAK,GAAG,IAAI,CAACnB,EAAE,CAACkB,UAAU,CAACE,aAAa,CAAC,eAAe,CAAC;QAC/D,MAAMC,KAAK,GAAG,IAAI,CAACrB,EAAE,CAACkB,UAAU,CAACE,aAAa,CAAC,eAAe,CAAC;QAC/D;QACAD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;QACnFF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;QACnF;QACA,MAAMC,aAAa,GAAGd,IAAI,KAAK,GAAG,GAAGS,KAAK,GAAGE,KAAK;QAClDG,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACF,SAAS,CAACG,GAAG,CAAC,aAAa,CAAC;MAC5G;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,GAAG,MAAM;MACpB;MACA;MACAC,UAAU,CAAC,MAAM;QACb,IAAIC,EAAE;QACN,MAAMC,aAAa,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC5B,EAAE,CAACkB,UAAU,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,aAAa;QACrG,MAAMC,oBAAoB,GAAGD,aAAa,IAAIA,aAAa,CAACP,SAAS,CAACS,QAAQ,CAAC,mBAAmB,CAAC;QACnG,IAAI,CAACD,oBAAoB,EAAE;UACvB,IAAI,IAAI,CAAC5D,QAAQ,EAAE;YACf,IAAI,CAACA,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACP,OAAO,CAACiD,IAAI,CAAC,CAAC;UACvB;UACA;UACA,IAAI,IAAI,CAACnC,SAAS,IAAI,IAAI,CAACuB,EAAE,CAACkB,UAAU,EAAE;YACtC,MAAMC,KAAK,GAAG,IAAI,CAACnB,EAAE,CAACkB,UAAU,CAACE,aAAa,CAAC,eAAe,CAAC;YAC/D,MAAMC,KAAK,GAAG,IAAI,CAACrB,EAAE,CAACkB,UAAU,CAACE,aAAa,CAAC,eAAe,CAAC;YAC/DD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;YACnFF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;UACvF;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT,CAAC;EACL;EACAS,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEvE,QAAQ;MAAEwE,QAAQ;MAAEC;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACzE,QAAQ,GAAGwE,QAAQ,KAAKE,SAAS,GAAGD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGzE,QAAQ,GAAG7B,uDAAa,CAAC6B,QAAQ,EAAEwE,QAAQ,CAAC;EACvK;EACAG,UAAUA,CAACC,QAAQ,EAAE;IACjB,IAAI,CAAC3G,uDAAY,CAAC2G,QAAQ,CAAC,EAAE;MACzB,IAAI,CAAC3D,GAAG,GAAG,CAAC;IAChB;IACA,IAAI,CAAC,IAAI,CAACT,QAAQ,EAAE;MAChB,IAAI,CAACqE,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,UAAUA,CAACF,QAAQ,EAAE;IACjB,IAAI,CAAC3G,uDAAY,CAAC2G,QAAQ,CAAC,EAAE;MACzB,IAAI,CAACvF,GAAG,GAAG,GAAG;IAClB;IACA,IAAI,CAAC,IAAI,CAACmB,QAAQ,EAAE;MAChB,IAAI,CAACqE,WAAW,CAAC,CAAC;IACtB;EACJ;EACAE,WAAWA,CAACH,QAAQ,EAAE;IAClB,IAAI,CAAC3G,uDAAY,CAAC2G,QAAQ,CAAC,EAAE;MACzB,IAAI,CAACrD,IAAI,GAAG,CAAC;IACjB;EACJ;EACAyD,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,KAAKP,SAAS,EAAE;MAC9B,IAAIO,cAAc,GAAG,IAAI,CAAC5F,GAAG,EAAE;QAC3BhC,qDAAe,CAAC,8CAA8C4H,cAAc,8BAA8B,IAAI,CAAC5F,GAAG,qGAAqG,EAAE,IAAI,CAACkD,EAAE,CAAC;QACjO,IAAI,CAAC0C,cAAc,GAAG,IAAI,CAAC5F,GAAG;MAClC,CAAC,MACI,IAAI4F,cAAc,GAAG,IAAI,CAAChE,GAAG,EAAE;QAChC5D,qDAAe,CAAC,8CAA8C4H,cAAc,2BAA2B,IAAI,CAAChE,GAAG,qGAAqG,EAAE,IAAI,CAACsB,EAAE,CAAC;QAC9N,IAAI,CAAC0C,cAAc,GAAG,IAAI,CAAChE,GAAG;MAClC;IACJ;EACJ;EACAiE,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC7C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACU,MAAM,CAAC,CAAC,IAAI,CAACtB,QAAQ,CAAC;IACvC;EACJ;EACA0D,YAAYA,CAACP,QAAQ,EAAEQ,QAAQ,EAAE;IAC7B,MAAMC,aAAa,GAAG,IAAI,CAAC3D,aAAa,CAACkD,QAAQ,EAAEQ,QAAQ,CAAC;IAC5D,IAAIC,aAAa,EAAE;MACf,IAAI,CAACrF,QAAQ,CAACmD,IAAI,CAAC;QAAE/B,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACZ,QAAQ,EAAE;MAChB,IAAI,CAACqE,WAAW,CAAC,CAAC;IACtB;EACJ;EACAS,iBAAiBA,CAAA,EAAG;IAChB;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC/C,EAAE,CAACgD,YAAY,CAAC,IAAI,CAAC,EAAE;MAC5B,IAAI,CAAClF,OAAO,GAAG,IAAI,CAACkC,EAAE,CAACiD,YAAY,CAAC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC9E,mBAAmB,GAAGrC,uDAAqB,CAAC,IAAI,CAACkE,EAAE,CAAC;IACzD;IACA;IACA,IAAI,CAACtB,GAAG,GAAGhD,uDAAY,CAAC,IAAI,CAACgD,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,GAAG,CAAC;IAChD,IAAI,CAAC5B,GAAG,GAAGpB,uDAAY,CAAC,IAAI,CAACoB,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,GAAG,GAAG;IAClD,IAAI,CAACkC,IAAI,GAAGtD,uDAAY,CAAC,IAAI,CAACsD,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC;EACvD;EACAkE,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAChB,gBAAgB,GAAG,IAAI,CAACzE,QAAQ;IACrC,IAAI,CAACkC,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC2C,WAAW,CAAC,CAAC;IAClB,IAAI,CAACtE,OAAO,GAAG,IAAI;EACvB;EACAmF,iBAAiBA,CAAA,EAAG;IAChB,IAAIvB,EAAE;IACN,IAAI,CAACU,WAAW,CAAC,CAAC;IAClB,IAAI,CAACN,eAAe,CAAC,CAAC;IACtB,IAAI,CAACW,eAAe,CAAC,CAAC;IACtB,IAAI,CAACF,qBAAqB,CAAC,CAAC;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACzE,OAAO,EAAE;MACd,IAAI,CAAC2B,YAAY,CAAC,CAAC;IACvB;IACA,MAAMyD,UAAU,GAAG7H,qDAAqB,CAAC,IAAI,CAACyE,EAAE,CAAC;IACjD,IAAI,CAAC5B,SAAS,GAAG,CAACwD,EAAE,GAAGwB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChC,aAAa,CAAC,0BAA0B,CAAC,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGwB,UAAU;EACpL;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvD,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACwD,OAAO,CAAC,CAAC;MACtB,IAAI,CAACxD,OAAO,GAAGqC,SAAS;IAC5B;EACJ;EACAoB,QAAQA,CAAA,EAAG;IACP,IAAI3B,EAAE;IACN,MAAM/C,KAAK,GAAG,CAAC+C,EAAE,GAAG,IAAI,CAAC/C,KAAK,MAAM,IAAI,IAAI+C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAClE,IAAI,IAAI,CAACnD,SAAS,EAAE;MAChB,IAAI,OAAOI,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK;MAChB;MACA,OAAO;QACHS,KAAK,EAAE,CAAC;QACRC,KAAK,EAAEV;MACX,CAAC;IACL,CAAC,MACI;MACD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK,CAACU,KAAK;MACtB;MACA,OAAOV,KAAK;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACjC,KAAK,GAAG,IAAI,CAACY,mBAAmB,CAAC,IAAI,CAACZ,KAAK,CAAC;IACjD,IAAI,CAACrB,SAAS,CAACoD,IAAI,CAAC;MAAE/B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIuB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxC,gBAAgB,CAACgD,IAAI,CAAC;MAAE/B,KAAK,EAAE,IAAI,CAACY,mBAAmB,CAAC,IAAI,CAACZ,KAAK;IAAE,CAAC,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIwB,MAAMA,CAACmD,MAAM,EAAE;IACX,MAAM;MAAEpF,SAAS;MAAEqF;IAAY,CAAC,GAAG,IAAI;IACvC,MAAMC,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAItF,SAAS,IAAI,IAAI,CAACqF,WAAW,KAAKtB,SAAS,EAAE;MAC7C,IAAI,CAAC9D,qBAAqB,GAAG7C,qDAAqB,CAAC4C,SAAS,CAAC;IACjE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIqF,WAAW,KAAKtB,SAAS,EAAE;MAC3B,IAAI,CAACwB,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA,IAAI,CAACE,MAAM,CAACF,QAAQ,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACInD,KAAKA,CAACiD,MAAM,EAAE;IACV,IAAI5B,EAAE;IACN,MAAM;MAAExD,SAAS;MAAEC;IAAsB,CAAC,GAAG,IAAI;IACjD,MAAMqF,QAAQ,GAAG,CAAC9B,EAAE,GAAG4B,MAAM,CAACE,QAAQ,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG4B,MAAM,CAACK,OAAO;IACvF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACJ,WAAW,KAAKtB,SAAS,EAAE;MAChC,IAAI,CAACwB,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAItF,SAAS,IAAI,IAAI,CAACqF,WAAW,KAAKtB,SAAS,EAAE;MAC7C1G,qDAAmB,CAAC2C,SAAS,EAAEC,qBAAqB,CAAC;IACzD;IACA;IACA,IAAI,CAACuF,MAAM,CAACF,QAAQ,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACD,WAAW,GAAGtB,SAAS;IAC5B,IAAI,CAACrB,eAAe,CAAC,CAAC;IACtB,IAAI,CAACjD,cAAc,CAAC+C,IAAI,CAAC;MAAE/B,KAAK,EAAE,IAAI,CAACY,mBAAmB,CAAC,IAAI,CAACZ,KAAK;IAAE,CAAC,CAAC;EAC7E;EACA+E,MAAMA,CAACF,QAAQ,EAAE;IACb;IACA;IACA,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIC,KAAK,GAAGpI,uDAAK,CAAC,CAAC,EAAE,CAAC+H,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAIhI,mDAAK,CAAC,IAAI,CAAC+D,EAAE,CAAC,EAAE;MAChB+D,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,IAAI,CAAChF,KAAK,EAAE;MACZ;MACAgF,KAAK,GAAGG,YAAY,CAACC,YAAY,CAACJ,KAAK,EAAE,IAAI,CAACrF,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACkC,IAAI,CAAC,EAAE,IAAI,CAACN,GAAG,EAAE,IAAI,CAAC5B,GAAG,CAAC;IAChG;IACA;IACA,IAAI,IAAI,CAAC2G,WAAW,KAAK,GAAG,EAAE;MAC1B,IAAI,CAACnF,MAAM,GAAGyF,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAACxF,MAAM,GAAGwF,KAAK;IACvB;IACA;IACA,IAAI,CAAClD,WAAW,CAAC,CAAC;EACtB;EACA8C,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAMI,IAAI,GAAI,IAAI,CAACA,IAAI,GAAG,IAAI,CAACjE,WAAW,CAACuE,qBAAqB,CAAC,CAAE;IACnE;IACA,IAAIL,KAAK,GAAGpI,uDAAK,CAAC,CAAC,EAAE,CAAC+H,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAIhI,mDAAK,CAAC,IAAI,CAAC+D,EAAE,CAAC,EAAE;MAChB+D,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,CAACN,WAAW,GAAG,CAAC,IAAI,CAAChF,SAAS,IAAI5B,IAAI,CAACwH,GAAG,CAAC,IAAI,CAAC/F,MAAM,GAAGyF,KAAK,CAAC,GAAGlH,IAAI,CAACwH,GAAG,CAAC,IAAI,CAAC9F,MAAM,GAAGwF,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,IAAI,CAACO,QAAQ,CAAC,IAAI,CAACb,WAAW,CAAC;EACnC;EACA,IAAIc,IAAIA,CAAA,EAAG;IACP,OAAOJ,YAAY,CAAC,IAAI,CAAC7F,MAAM,EAAE,IAAI,CAACI,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACkC,IAAI,CAAC;EACnE;EACA,IAAIwF,IAAIA,CAAA,EAAG;IACP,OAAOL,YAAY,CAAC,IAAI,CAAC5F,MAAM,EAAE,IAAI,CAACG,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACkC,IAAI,CAAC;EACnE;EACA,IAAIyF,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAChG,SAAS,EAAE;MAChB,OAAO5B,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACJ,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,MAAM;MAAEmE;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,IAAI,IAAI,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,OAAOwB,YAAY,CAACxB,cAAc,EAAE,IAAI,CAAChE,GAAG,EAAE,IAAI,CAAC5B,GAAG,CAAC;EAC3D;EACA,IAAI4H,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjG,SAAS,EAAE;MAChB,OAAO5B,IAAI,CAACC,GAAG,CAAC,IAAI,CAACwB,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,OAAO,IAAI,CAACD,MAAM;EACtB;EACAgE,WAAWA,CAAA,EAAG;IACV,MAAMzD,KAAK,GAAG,IAAI,CAAC0E,QAAQ,CAAC,CAAC;IAC7B,MAAM;MAAE7E,GAAG;MAAE5B;IAAI,CAAC,GAAG,IAAI;IACzB,IAAI,IAAI,CAAC2B,SAAS,EAAE;MAChB,IAAI,CAACH,MAAM,GAAG4F,YAAY,CAACrF,KAAK,CAACS,KAAK,EAAEZ,GAAG,EAAE5B,GAAG,CAAC;MACjD,IAAI,CAACyB,MAAM,GAAG2F,YAAY,CAACrF,KAAK,CAACU,KAAK,EAAEb,GAAG,EAAE5B,GAAG,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACwB,MAAM,GAAG4F,YAAY,CAACrF,KAAK,EAAEH,GAAG,EAAE5B,GAAG,CAAC;IAC/C;EACJ;EACA+D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5C,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAEsG,IAAI;MAAEC;IAAK,CAAC,GAAG,IAAI;IAC3B,IAAI,CAAC3F,KAAK,GAAG,CAAC,IAAI,CAACJ,SAAS,GACtB8F,IAAI,GACJ;MACEjF,KAAK,EAAEzC,IAAI,CAAC6B,GAAG,CAAC6F,IAAI,EAAEC,IAAI,CAAC;MAC3BjF,KAAK,EAAE1C,IAAI,CAACC,GAAG,CAACyH,IAAI,EAAEC,IAAI;IAC9B,CAAC;IACL,IAAI,CAACvG,QAAQ,GAAG,KAAK;EACzB;EACAqG,QAAQA,CAAC5D,IAAI,EAAE;IACX,IAAI,IAAI,CAACV,EAAE,CAACkB,UAAU,EAAE;MACpB,MAAMyD,MAAM,GAAG,IAAI,CAAC3E,EAAE,CAACkB,UAAU,CAACE,aAAa,CAACV,IAAI,KAAK,GAAG,GAAG,eAAe,GAAG,eAAe,CAAC;MACjG,IAAIiE,MAAM,EAAE;QACRA,MAAM,CAACC,KAAK,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;AACJ;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAC7E,EAAE,CAACoB,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EAC3D;EACA;AACJ;AACA;EACI,IAAI0D,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC9E,EAAE,CAACoB,aAAa,CAAC,cAAc,CAAC,KAAK,IAAI;EACzD;EACA,IAAI2D,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,KAAK,KAAK7C,SAAS,IAAI,IAAI,CAACnC,EAAE,CAACoB,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EACvF;EACA6D,iBAAiBA,CAAA,EAAG;IAChB,IAAIrD,EAAE;IACN,MAAM;MAAElD,GAAG;MAAE5B,GAAG;MAAEkC,IAAI;MAAEyB,cAAc;MAAEgD,WAAW;MAAEvE,QAAQ;MAAEP,GAAG;MAAE8F,UAAU;MAAEC,UAAU;MAAE9F,YAAY;MAAET;IAAqB,CAAC,GAAG,IAAI;IACvI,IAAI+G,QAAQ,GAAG,GAAGT,UAAU,GAAG,GAAG,GAAG;IACrC,IAAIU,MAAM,GAAG,GAAG,GAAG,GAAGT,UAAU,GAAG,GAAG,GAAG;IACzC,MAAMU,GAAG,GAAGnJ,mDAAK,CAAC,IAAI,CAAC+D,EAAE,CAAC;IAC1B,MAAMqF,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;IACpC,MAAME,GAAG,GAAGF,GAAG,GAAG,MAAM,GAAG,OAAO;IAClC,MAAMG,SAAS,GAAIC,IAAI,IAAK;MACxB,OAAO;QACH,CAACH,KAAK,GAAGG,IAAI,CAACH,KAAK;MACvB,CAAC;IACL,CAAC;IACD,IAAI,IAAI,CAAC5G,SAAS,KAAK,KAAK,EAAE;MAC1B;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAC8F,IAAI,IAAI,CAAC3C,EAAE,GAAG,IAAI,CAACc,cAAc,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAClD,GAAG,CAAC,EAAE;QACpF;AAChB;AACA;AACA;AACA;AACA;AACA;QACgBwG,QAAQ,GAAG,GAAGR,UAAU,GAAG,GAAG,GAAG;QACjCS,MAAM,GAAG,GAAG,GAAG,GAAGV,UAAU,GAAG,GAAG,GAAG;MACzC,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;QACgBS,QAAQ,GAAG,GAAGT,UAAU,GAAG,GAAG,GAAG;QACjCU,MAAM,GAAG,GAAG,GAAG,GAAGT,UAAU,GAAG,GAAG,GAAG;MACzC;IACJ;IACA,MAAMe,QAAQ,GAAG;MACb,CAACJ,KAAK,GAAGH,QAAQ;MACjB,CAACI,GAAG,GAAGH;IACX,CAAC;IACD,MAAMlG,KAAK,GAAG,EAAE;IAChB,IAAI,IAAI,CAACF,KAAK,IAAI,IAAI,CAACE,KAAK,EAAE;MAC1B,KAAK,IAAIJ,KAAK,GAAGH,GAAG,EAAEG,KAAK,IAAI/B,GAAG,EAAE+B,KAAK,IAAIG,IAAI,EAAE;QAC/C,MAAM+E,KAAK,GAAGG,YAAY,CAACrF,KAAK,EAAEH,GAAG,EAAE5B,GAAG,CAAC;QAC3C,MAAM4I,QAAQ,GAAG7I,IAAI,CAAC6B,GAAG,CAAC+F,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAMiB,QAAQ,GAAG9I,IAAI,CAACC,GAAG,CAAC2H,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAMc,IAAI,GAAG;UACTzB,KAAK;UACL;AACpB;AACA;AACA;UACoB6B,MAAM,EAAE7B,KAAK,IAAI2B,QAAQ,IAAI3B,KAAK,IAAI4B;QAC1C,CAAC;QACDH,IAAI,CAACH,KAAK,CAAC,GAAG,GAAGtB,KAAK,GAAG,GAAG,GAAG;QAC/B9E,KAAK,CAAC4G,IAAI,CAACL,IAAI,CAAC;MACpB;IACJ;IACA,OAAQzK,qDAAC,CAAC,KAAK,EAAE;MAAE+K,KAAK,EAAE,cAAc;MAAEC,GAAG,EAAGC,OAAO,IAAM,IAAI,CAACnG,WAAW,GAAGmG,OAAQ;MACpF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYC,WAAW,EAAG3F,EAAE,IAAK;QACjB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACmD,WAAW,KAAKtB,SAAS,EAAE;UAChC,IAAI,CAAC/B,OAAO,CAAC,CAAC;UACd,IAAI,CAACG,KAAK,CAACD,EAAE,CAAC;QAClB;MACJ;IAAE,CAAC,EAAErB,KAAK,CAAClC,GAAG,CAAEyI,IAAI,IAAMzK,qDAAC,CAAC,KAAK,EAAE;MAAEmL,KAAK,EAAEX,SAAS,CAACC,IAAI,CAAC;MAAEW,IAAI,EAAE,cAAc;MAAEL,KAAK,EAAE;QACtF,YAAY,EAAE,IAAI;QAClB,mBAAmB,EAAEN,IAAI,CAACI;MAC9B,CAAC;MAAEQ,IAAI,EAAEZ,IAAI,CAACI,MAAM,GAAG,aAAa,GAAG;IAAO,CAAC,CAAE,CAAC,EAAE7K,qDAAC,CAAC,KAAK,EAAE;MAAE+K,KAAK,EAAE;IAAsB,CAAC,EAAE/K,qDAAC,CAAC,KAAK,EAAE;MAAE+K,KAAK,EAAE,WAAW;MAAEK,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAErL,qDAAC,CAAC,KAAK,EAAE;MAAE+K,KAAK,EAAE;QAClL,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,IAAI;QACxB,WAAW,EAAE7G,KAAK,CAACxC,MAAM,GAAG;MAChC,CAAC;MAAE0J,IAAI,EAAE,cAAc;MAAED,KAAK,EAAET,QAAQ;MAAEW,IAAI,EAAE;IAAa,CAAC,CAAC,CAAC,EAAEC,UAAU,CAACjB,GAAG,EAAE;MAClF1E,IAAI,EAAE,GAAG;MACT4F,OAAO,EAAE7C,WAAW,KAAK,GAAG;MAC5B5E,KAAK,EAAE,IAAI,CAAC0F,IAAI;MAChBR,KAAK,EAAE,IAAI,CAACzF,MAAM;MAClBK,GAAG;MACHC,YAAY;MACZM,QAAQ;MACRuB,cAAc;MACd/B,GAAG;MACH5B,GAAG;MACHqB,mBAAmB;MACnB8C,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BS,UAAU,EAAE,IAAI,CAACA;IACrB,CAAC,CAAC,EAAE,IAAI,CAACjD,SAAS,IACd4H,UAAU,CAACjB,GAAG,EAAE;MACZ1E,IAAI,EAAE,GAAG;MACT4F,OAAO,EAAE7C,WAAW,KAAK,GAAG;MAC5B5E,KAAK,EAAE,IAAI,CAAC2F,IAAI;MAChBT,KAAK,EAAE,IAAI,CAACxF,MAAM;MAClBI,GAAG;MACHC,YAAY;MACZM,QAAQ;MACRuB,cAAc;MACd/B,GAAG;MACH5B,GAAG;MACHqB,mBAAmB;MACnB8C,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BS,UAAU,EAAE,IAAI,CAACA;IACrB,CAAC,CAAC,CAAC;EACX;EACA6E,MAAMA,CAAA,EAAG;IACL,MAAM;MAAErH,QAAQ;MAAEc,EAAE;MAAE+E,QAAQ;MAAEjH,OAAO;MAAEa,GAAG;MAAE8E,WAAW;MAAE/D,cAAc;MAAEsF;IAAM,CAAC,GAAG,IAAI;IACzF,MAAMwB,MAAM,GAAGtK,qDAAW,CAAC,UAAU,EAAE8D,EAAE,CAAC;IAC1C;AACR;AACA;AACA;IACQ,MAAMyG,eAAe,GAAI1B,QAAQ,KAAKrF,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,OAAO,CAAC,IAAK,IAAI,CAACmF,mBAAmB;IAC5H,MAAM6B,oBAAoB,GAAGF,MAAM,IAAI,CAACC,eAAe;IACvD;AACR;AACA;AACA;IACQ,MAAME,aAAa,GAAI5B,QAAQ,IAAIrF,cAAc,KAAK,KAAK,IAAK,IAAI,CAACoF,iBAAiB;IACtF,MAAM8B,kBAAkB,GAAGJ,MAAM,IAAI,CAACG,aAAa;IACnD,MAAME,IAAI,GAAG5L,qDAAU,CAAC,IAAI,CAAC;IAC7Be,uDAAiB,CAAC,IAAI,EAAEgE,EAAE,EAAE,IAAI,CAACxB,IAAI,EAAEsI,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxD,QAAQ,CAAC,CAAC,CAAC,EAAErE,QAAQ,CAAC;IACjF,OAAQnE,qDAAC,CAACI,iDAAI,EAAE;MAAE6L,GAAG,EAAE,0CAA0C;MAAEC,SAAS,EAAE,IAAI,CAACjG,OAAO;MAAEkG,UAAU,EAAE,IAAI,CAACnG,MAAM;MAAEoG,EAAE,EAAErJ,OAAO;MAAEgI,KAAK,EAAE1J,qDAAkB,CAAC,IAAI,CAACgL,KAAK,EAAE;QAChK,CAACP,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEL,MAAM;QACjB,gBAAgB,EAAEtH,QAAQ;QAC1B,eAAe,EAAEuE,WAAW,KAAKtB,SAAS;QAC1C,eAAe,EAAExD,GAAG;QACpB,CAAC,yBAAyBe,cAAc,EAAE,GAAG,IAAI;QACjD,6BAA6B,EAAEgH,oBAAoB;QACnD,2BAA2B,EAAEE;MACjC,CAAC;IAAE,CAAC,EAAE7L,qDAAC,CAAC,OAAO,EAAE;MAAEiM,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE,eAAe;MAAEqB,EAAE,EAAE;IAAc,CAAC,EAAEpM,qDAAC,CAAC,KAAK,EAAE;MAAEiM,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE;QAChL,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACf;MAClC,CAAC;MAAEqB,IAAI,EAAE;IAAQ,CAAC,EAAEpB,KAAK,KAAK7C,SAAS,GAAGpH,qDAAC,CAAC,KAAK,EAAE;MAAE+K,KAAK,EAAE;IAAa,CAAC,EAAEd,KAAK,CAAC,GAAGjK,qDAAC,CAAC,MAAM,EAAE;MAAEyD,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAEzD,qDAAC,CAAC,KAAK,EAAE;MAAEiM,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE;IAAiB,CAAC,EAAE/K,qDAAC,CAAC,MAAM,EAAE;MAAEiM,GAAG,EAAE,0CAA0C;MAAExI,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE,IAAI,CAACyG,iBAAiB,CAAC,CAAC,EAAElK,qDAAC,CAAC,MAAM,EAAE;MAAEiM,GAAG,EAAE,0CAA0C;MAAExI,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1Y;EACA,IAAIwB,EAAEA,CAAA,EAAG;IAAE,OAAO3E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgM,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,gBAAgB,EAAE,CAAC,uBAAuB,CAAC;MAC3C,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAMhB,UAAU,GAAGA,CAACjB,GAAG,EAAE;EAAE1E,IAAI;EAAE7B,KAAK;EAAEkF,KAAK;EAAErF,GAAG;EAAE5B,GAAG;EAAEoC,QAAQ;EAAEoH,OAAO;EAAE3H,GAAG;EAAE8B,cAAc;EAAE7B,YAAY;EAAET,mBAAmB;EAAE8C,WAAW;EAAES;AAAY,CAAC,KAAK;EAC/J,MAAM2D,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;EACpC,MAAMkC,SAAS,GAAGA,CAAA,KAAM;IACpB,MAAMpB,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACb,KAAK,CAAC,GAAG,GAAGtB,KAAK,GAAG,GAAG,GAAG;IAChC,OAAOmC,KAAK;EAChB,CAAC;EACD;EACA,MAAMqB,SAAS,GAAGpJ,mBAAmB,CAAC,YAAY,CAAC;EACnD,OAAQpD,qDAAC,CAAC,KAAK,EAAE;IAAEyM,SAAS,EAAGlH,EAAE,IAAK;MAC9B,MAAM0G,GAAG,GAAG1G,EAAE,CAAC0G,GAAG;MAClB,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,WAAW,EAAE;QAC5CvG,cAAc,CAACC,IAAI,EAAE,KAAK,CAAC;QAC3BJ,EAAE,CAACmH,cAAc,CAAC,CAAC;QACnBnH,EAAE,CAACoH,eAAe,CAAC,CAAC;MACxB,CAAC,MACI,IAAIV,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,SAAS,EAAE;QAChDvG,cAAc,CAACC,IAAI,EAAE,IAAI,CAAC;QAC1BJ,EAAE,CAACmH,cAAc,CAAC,CAAC;QACnBnH,EAAE,CAACoH,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;IAAE1G,OAAO,EAAEA,CAAA,KAAMC,WAAW,CAACP,IAAI,CAAC;IAAEK,MAAM,EAAEW,UAAU;IAAEoE,KAAK,EAAE;MAC5D,mBAAmB,EAAE,IAAI;MACzB,cAAc,EAAEpF,IAAI,KAAK,GAAG;MAC5B,cAAc,EAAEA,IAAI,KAAK,GAAG;MAC5B,oBAAoB,EAAE4F,OAAO;MAC7B,gBAAgB,EAAEzH,KAAK,KAAKH,GAAG;MAC/B,gBAAgB,EAAEG,KAAK,KAAK/B,GAAG;MAC/B,iBAAiB,EAAE,IAAI;MACvB,eAAe,EAAE;IACrB,CAAC;IAAEoJ,KAAK,EAAEoB,SAAS,CAAC,CAAC;IAAEnB,IAAI,EAAE,QAAQ;IAAEwB,QAAQ,EAAEzI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAAE,YAAY,EAAEqI,SAAS,KAAKpF,SAAS,GAAGoF,SAAS,GAAG,IAAI;IAAE,iBAAiB,EAAEA,SAAS,KAAKpF,SAAS,GAAG,aAAa,GAAG,IAAI;IAAE,eAAe,EAAEzD,GAAG;IAAE,eAAe,EAAE5B,GAAG;IAAE,eAAe,EAAEoC,QAAQ,GAAG,MAAM,GAAG,IAAI;IAAE,eAAe,EAAEL;EAAM,CAAC,EAAEF,GAAG,IAAK5D,qDAAC,CAAC,KAAK,EAAE;IAAE+K,KAAK,EAAE,WAAW;IAAEK,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAM,CAAC,EAAExH,YAAY,CAACC,KAAK,CAAC,CAAE,EAAE9D,qDAAC,CAAC,KAAK,EAAE;IAAE+K,KAAK,EAAE,YAAY;IAAEK,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAO,CAAC,CAAC,CAAC;AACne,CAAC;AACD,MAAMjC,YAAY,GAAGA,CAACJ,KAAK,EAAErF,GAAG,EAAE5B,GAAG,EAAEkC,IAAI,KAAK;EAC5C,IAAIH,KAAK,GAAG,CAAC/B,GAAG,GAAG4B,GAAG,IAAIqF,KAAK;EAC/B,IAAI/E,IAAI,GAAG,CAAC,EAAE;IACV;IACAH,KAAK,GAAGhC,IAAI,CAACiC,KAAK,CAACD,KAAK,GAAGG,IAAI,CAAC,GAAGA,IAAI,GAAGN,GAAG;EACjD;EACA,MAAMkJ,YAAY,GAAGjM,uDAAK,CAAC+C,GAAG,EAAEG,KAAK,EAAE/B,GAAG,CAAC;EAC3C,OAAOJ,uBAAuB,CAACkL,YAAY,EAAElJ,GAAG,EAAE5B,GAAG,EAAEkC,IAAI,CAAC;AAChE,CAAC;AACD,MAAMkF,YAAY,GAAGA,CAACrF,KAAK,EAAEH,GAAG,EAAE5B,GAAG,KAAK;EACtC,OAAOnB,uDAAK,CAAC,CAAC,EAAE,CAACkD,KAAK,GAAGH,GAAG,KAAK5B,GAAG,GAAG4B,GAAG,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC;AACD,IAAIX,QAAQ,GAAG,CAAC;AAChBX,KAAK,CAAC8I,KAAK,GAAG;EACV2B,GAAG,EAAE3K,WAAW;EAChB4K,EAAE,EAAE3K;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-range.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-BlJTBdxG.js';\nimport { j as isSafeNumber, e as clamp, d as debounceEvent, i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nfunction getDecimalPlaces(n) {\n    if (!isSafeNumber(n))\n        return 0;\n    if (n % 1 === 0)\n        return 0;\n    return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n    if (!isSafeNumber(n))\n        return 0;\n    const maxPlaces = Math.max(...references.map((r) => getDecimalPlaces(r)));\n    return Number(n.toFixed(maxPlaces));\n}\n\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:2px;--height:42px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\n\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.26);--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #0054e9);--pin-color:var(--ion-color-primary-contrast, #fff)}::slotted(:not(ion-icon)[slot=start]),::slotted(:not(ion-icon)[slot=end]),.native-wrapper{font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}.range-knob::before{inset-inline-start:0}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}.range-pin::before{inset-inline-start:50%}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\n\nconst Range = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n        this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n        this.rangeId = `ion-r-${rangeIds++}`;\n        this.didLoad = false;\n        this.noUpdate = false;\n        this.hasFocus = false;\n        this.inheritedAttributes = {};\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        this.ratioA = 0;\n        this.ratioB = 0;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.rangeId;\n        /**\n         * Show two knobs.\n         */\n        this.dualKnobs = false;\n        /**\n         * Minimum integer value of the range.\n         */\n        this.min = 0;\n        /**\n         * Maximum integer value of the range.\n         */\n        this.max = 100;\n        /**\n         * If `true`, a pin with integer value is shown when the knob\n         * is pressed.\n         */\n        this.pin = false;\n        /**\n         * A callback used to format the pin text.\n         * By default the pin text is set to `Math.round(value)`.\n         *\n         * See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this\n         * if you need to access `this` from within the callback.\n         */\n        this.pinFormatter = (value) => Math.round(value);\n        /**\n         * If `true`, the knob snaps to tick marks evenly spaced based\n         * on the step property value.\n         */\n        this.snaps = false;\n        /**\n         * Specifies the value granularity.\n         */\n        this.step = 1;\n        /**\n         * If `true`, tick marks are displayed based on the step value.\n         * Only applies when `snaps` is `true`.\n         */\n        this.ticks = true;\n        /**\n         * If `true`, the user cannot interact with the range.\n         */\n        this.disabled = false;\n        /**\n         * the value of the range.\n         */\n        this.value = 0;\n        /**\n         * Compares two RangeValue inputs to determine if they are different.\n         *\n         * @param newVal - The new value.\n         * @param oldVal - The old value.\n         * @returns `true` if the values are different, `false` otherwise.\n         */\n        this.compareValues = (newVal, oldVal) => {\n            if (typeof newVal === 'object' && typeof oldVal === 'object') {\n                return newVal.lower !== oldVal.lower || newVal.upper !== oldVal.upper;\n            }\n            return newVal !== oldVal;\n        };\n        this.clampBounds = (value) => {\n            return clamp(this.min, value, this.max);\n        };\n        this.ensureValueInBounds = (value) => {\n            if (this.dualKnobs) {\n                return {\n                    lower: this.clampBounds(value.lower),\n                    upper: this.clampBounds(value.upper),\n                };\n            }\n            else {\n                return this.clampBounds(value);\n            }\n        };\n        /**\n         * Where to place the label relative to the range.\n         * `\"start\"`: The label will appear to the left of the range in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the range in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the range regardless of the direction.\n         */\n        this.labelPlacement = 'start';\n        this.setupGesture = async () => {\n            const rangeSlider = this.rangeSlider;\n            if (rangeSlider) {\n                this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n                    el: rangeSlider,\n                    gestureName: 'range',\n                    gesturePriority: 100,\n                    /**\n                     * Provide a threshold since the drag movement\n                     * might be a user scrolling the view.\n                     * If this is true, then the range\n                     * should not move.\n                     */\n                    threshold: 10,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.gesture.enable(!this.disabled);\n            }\n        };\n        this.handleKeyboard = (knob, isIncrease) => {\n            const { ensureValueInBounds } = this;\n            let step = this.step;\n            step = step > 0 ? step : 1;\n            step = step / (this.max - this.min);\n            if (!isIncrease) {\n                step *= -1;\n            }\n            if (knob === 'A') {\n                this.ratioA = clamp(0, this.ratioA + step, 1);\n            }\n            else {\n                this.ratioB = clamp(0, this.ratioB + step, 1);\n            }\n            this.ionKnobMoveStart.emit({ value: ensureValueInBounds(this.value) });\n            this.updateValue();\n            this.emitValueChange();\n            this.ionKnobMoveEnd.emit({ value: ensureValueInBounds(this.value) });\n        };\n        this.onBlur = () => {\n            if (this.hasFocus) {\n                this.hasFocus = false;\n                this.ionBlur.emit();\n            }\n        };\n        this.onFocus = () => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n            }\n        };\n        this.onKnobFocus = (knob) => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n            }\n            // Manually manage ion-focused class for dual knobs\n            if (this.dualKnobs && this.el.shadowRoot) {\n                const knobA = this.el.shadowRoot.querySelector('.range-knob-a');\n                const knobB = this.el.shadowRoot.querySelector('.range-knob-b');\n                // Remove ion-focused from both knobs first\n                knobA === null || knobA === void 0 ? void 0 : knobA.classList.remove('ion-focused');\n                knobB === null || knobB === void 0 ? void 0 : knobB.classList.remove('ion-focused');\n                // Add ion-focused only to the focused knob\n                const focusedKnobEl = knob === 'A' ? knobA : knobB;\n                focusedKnobEl === null || focusedKnobEl === void 0 ? void 0 : focusedKnobEl.classList.add('ion-focused');\n            }\n        };\n        this.onKnobBlur = () => {\n            // Check if focus is moving to another knob within the same range\n            // by delaying the reset to allow the new focus to register\n            setTimeout(() => {\n                var _a;\n                const activeElement = (_a = this.el.shadowRoot) === null || _a === void 0 ? void 0 : _a.activeElement;\n                const isStillFocusedOnKnob = activeElement && activeElement.classList.contains('range-knob-handle');\n                if (!isStillFocusedOnKnob) {\n                    if (this.hasFocus) {\n                        this.hasFocus = false;\n                        this.ionBlur.emit();\n                    }\n                    // Remove ion-focused from both knobs when focus leaves the range\n                    if (this.dualKnobs && this.el.shadowRoot) {\n                        const knobA = this.el.shadowRoot.querySelector('.range-knob-a');\n                        const knobB = this.el.shadowRoot.querySelector('.range-knob-b');\n                        knobA === null || knobA === void 0 ? void 0 : knobA.classList.remove('ion-focused');\n                        knobB === null || knobB === void 0 ? void 0 : knobB.classList.remove('ion-focused');\n                    }\n                }\n            }, 0);\n        };\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    minChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.min = 0;\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    maxChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.max = 100;\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    stepChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.step = 1;\n        }\n    }\n    activeBarStartChanged() {\n        const { activeBarStart } = this;\n        if (activeBarStart !== undefined) {\n            if (activeBarStart > this.max) {\n                printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.max;\n            }\n            else if (activeBarStart < this.min) {\n                printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.min;\n            }\n        }\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    valueChanged(newValue, oldValue) {\n        const valuesChanged = this.compareValues(newValue, oldValue);\n        if (valuesChanged) {\n            this.ionInput.emit({ value: this.value });\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    componentWillLoad() {\n        /**\n         * If user has custom ID set then we should\n         * not assign the default incrementing ID.\n         */\n        if (this.el.hasAttribute('id')) {\n            this.rangeId = this.el.getAttribute('id');\n        }\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        // If min, max, or step are not safe, set them to 0, 100, and 1, respectively.\n        // Each watch does this, but not before the initial load.\n        this.min = isSafeNumber(this.min) ? this.min : 0;\n        this.max = isSafeNumber(this.max) ? this.max : 100;\n        this.step = isSafeNumber(this.step) ? this.step : 1;\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.setupGesture();\n        this.updateRatio();\n        this.didLoad = true;\n    }\n    connectedCallback() {\n        var _a;\n        this.updateRatio();\n        this.debounceChanged();\n        this.disabledChanged();\n        this.activeBarStartChanged();\n        /**\n         * If we have not yet rendered\n         * ion-range, then rangeSlider is not defined.\n         * But if we are moving ion-range via appendChild,\n         * then rangeSlider will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n        const ionContent = findClosestIonContent(this.el);\n        this.contentEl = (_a = ionContent === null || ionContent === void 0 ? void 0 : ionContent.querySelector('.ion-content-scroll-host')) !== null && _a !== void 0 ? _a : ionContent;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    getValue() {\n        var _a;\n        const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n        if (this.dualKnobs) {\n            if (typeof value === 'object') {\n                return value;\n            }\n            return {\n                lower: 0,\n                upper: value,\n            };\n        }\n        else {\n            if (typeof value === 'object') {\n                return value.upper;\n            }\n            return value;\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange() {\n        this.value = this.ensureValueInBounds(this.value);\n        this.ionChange.emit({ value: this.value });\n    }\n    /**\n     * The value should be updated on touch end or\n     * when the component is being dragged.\n     * This follows the native behavior of mobile devices.\n     *\n     * For example: When the user lifts their finger from the\n     * screen after tapping the bar or dragging the bar or knob.\n     */\n    onStart() {\n        this.ionKnobMoveStart.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    /**\n     * The value should be updated while dragging the\n     * bar or knob.\n     *\n     * While the user is dragging, the view\n     * should not scroll. This is to prevent the user from\n     * feeling disoriented while dragging.\n     *\n     * The user can scroll on the view if the knob or\n     * bar is not being dragged.\n     *\n     * @param detail The details of the gesture event.\n     */\n    onMove(detail) {\n        const { contentEl, pressedKnob } = this;\n        const currentX = detail.currentX;\n        /**\n         * Since the user is dragging on the bar or knob, the view should not scroll.\n         *\n         * This only needs to be done once.\n         */\n        if (contentEl && this.pressedKnob === undefined) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        /**\n         * The `pressedKnob` can be undefined if the user just\n         * started dragging the knob.\n         *\n         * This is necessary to determine which knob the user is dragging,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         *\n         * This only needs to be done once since the knob won't change\n         * while the user is dragging.\n         */\n        if (pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        this.update(currentX);\n    }\n    /**\n     * The value should be updated on touch end:\n     * - When the user lifts their finger from the screen after\n     * tapping the bar.\n     *\n     * @param detail The details of the gesture or mouse event.\n     */\n    onEnd(detail) {\n        var _a;\n        const { contentEl, initialContentScrollY } = this;\n        const currentX = (_a = detail.currentX) !== null && _a !== void 0 ? _a : detail.clientX;\n        /**\n         * The `pressedKnob` can be undefined if the user never\n         * dragged the knob. They just tapped on the bar.\n         *\n         * This is necessary to determine which knob the user is changing,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         */\n        if (this.pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        /**\n         * The user is no longer dragging the bar or\n         * knob (if they were dragging it).\n         *\n         * The user can now scroll on the view in the next gesture event.\n         */\n        if (contentEl && this.pressedKnob !== undefined) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        // update the active knob's position\n        this.update(currentX);\n        /**\n         * Reset the pressed knob to undefined since the user\n         * may start dragging a different knob in the next gesture event.\n         */\n        this.pressedKnob = undefined;\n        this.emitValueChange();\n        this.ionKnobMoveEnd.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    update(currentX) {\n        // figure out where the pointer is currently at\n        // update the knob being interacted with\n        const rect = this.rect;\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        if (this.snaps) {\n            // snaps the ratio to the current value\n            ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n        }\n        // update which knob is pressed\n        if (this.pressedKnob === 'A') {\n            this.ratioA = ratio;\n        }\n        else {\n            this.ratioB = ratio;\n        }\n        // Update input value\n        this.updateValue();\n    }\n    setPressedKnob(currentX) {\n        const rect = (this.rect = this.rangeSlider.getBoundingClientRect());\n        // figure out which knob they started closer to\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n        this.setFocus(this.pressedKnob);\n    }\n    get valA() {\n        return ratioToValue(this.ratioA, this.min, this.max, this.step);\n    }\n    get valB() {\n        return ratioToValue(this.ratioB, this.min, this.max, this.step);\n    }\n    get ratioLower() {\n        if (this.dualKnobs) {\n            return Math.min(this.ratioA, this.ratioB);\n        }\n        const { activeBarStart } = this;\n        if (activeBarStart == null) {\n            return 0;\n        }\n        return valueToRatio(activeBarStart, this.min, this.max);\n    }\n    get ratioUpper() {\n        if (this.dualKnobs) {\n            return Math.max(this.ratioA, this.ratioB);\n        }\n        return this.ratioA;\n    }\n    updateRatio() {\n        const value = this.getValue();\n        const { min, max } = this;\n        if (this.dualKnobs) {\n            this.ratioA = valueToRatio(value.lower, min, max);\n            this.ratioB = valueToRatio(value.upper, min, max);\n        }\n        else {\n            this.ratioA = valueToRatio(value, min, max);\n        }\n    }\n    updateValue() {\n        this.noUpdate = true;\n        const { valA, valB } = this;\n        this.value = !this.dualKnobs\n            ? valA\n            : {\n                lower: Math.min(valA, valB),\n                upper: Math.max(valA, valB),\n            };\n        this.noUpdate = false;\n    }\n    setFocus(knob) {\n        if (this.el.shadowRoot) {\n            const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n            if (knobEl) {\n                knobEl.focus();\n            }\n        }\n    }\n    /**\n     * Returns true if content was passed to the \"start\" slot\n     */\n    get hasStartSlotContent() {\n        return this.el.querySelector('[slot=\"start\"]') !== null;\n    }\n    /**\n     * Returns true if content was passed to the \"end\" slot\n     */\n    get hasEndSlotContent() {\n        return this.el.querySelector('[slot=\"end\"]') !== null;\n    }\n    get hasLabel() {\n        return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n    }\n    renderRangeSlider() {\n        var _a;\n        const { min, max, step, handleKeyboard, pressedKnob, disabled, pin, ratioLower, ratioUpper, pinFormatter, inheritedAttributes, } = this;\n        let barStart = `${ratioLower * 100}%`;\n        let barEnd = `${100 - ratioUpper * 100}%`;\n        const rtl = isRTL(this.el);\n        const start = rtl ? 'right' : 'left';\n        const end = rtl ? 'left' : 'right';\n        const tickStyle = (tick) => {\n            return {\n                [start]: tick[start],\n            };\n        };\n        if (this.dualKnobs === false) {\n            /**\n             * When the value is less than the activeBarStart or the min value,\n             * the knob will display at the start of the active bar.\n             */\n            if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n                /**\n                 * Sets the bar positions relative to the upper and lower limits.\n                 * Converts the ratio values into percentages, used as offsets for left/right styles.\n                 *\n                 * The ratioUpper refers to the knob position on the bar.\n                 * The ratioLower refers to the end position of the active bar (the value).\n                 */\n                barStart = `${ratioUpper * 100}%`;\n                barEnd = `${100 - ratioLower * 100}%`;\n            }\n            else {\n                /**\n                 * Otherwise, the knob will display at the end of the active bar.\n                 *\n                 * The ratioLower refers to the start position of the active bar (the value).\n                 * The ratioUpper refers to the knob position on the bar.\n                 */\n                barStart = `${ratioLower * 100}%`;\n                barEnd = `${100 - ratioUpper * 100}%`;\n            }\n        }\n        const barStyle = {\n            [start]: barStart,\n            [end]: barEnd,\n        };\n        const ticks = [];\n        if (this.snaps && this.ticks) {\n            for (let value = min; value <= max; value += step) {\n                const ratio = valueToRatio(value, min, max);\n                const ratioMin = Math.min(ratioLower, ratioUpper);\n                const ratioMax = Math.max(ratioLower, ratioUpper);\n                const tick = {\n                    ratio,\n                    /**\n                     * Sets the tick mark as active when the tick is between the min bounds and the knob.\n                     * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n                     */\n                    active: ratio >= ratioMin && ratio <= ratioMax,\n                };\n                tick[start] = `${ratio * 100}%`;\n                ticks.push(tick);\n            }\n        }\n        return (h(\"div\", { class: \"range-slider\", ref: (rangeEl) => (this.rangeSlider = rangeEl),\n            /**\n             * Since the gesture has a threshold, the value\n             * won't change until the user has dragged past\n             * the threshold. This is to prevent the range\n             * from moving when the user is scrolling.\n             *\n             * This results in the value not being updated\n             * and the event emitters not being triggered\n             * if the user taps on the range. This is why\n             * we need to listen for the \"pointerUp\" event.\n             */\n            onPointerUp: (ev) => {\n                /**\n                 * If the user drags the knob on the web\n                 * version (does not occur on mobile),\n                 * the \"pointerUp\" event will be triggered\n                 * along with the gesture's events.\n                 * This leads to duplicate events.\n                 *\n                 * By checking if the pressedKnob is undefined,\n                 * we can determine if the \"pointerUp\" event was\n                 * triggered by a tap or a drag. If it was\n                 * dragged, the pressedKnob will be defined.\n                 */\n                if (this.pressedKnob === undefined) {\n                    this.onStart();\n                    this.onEnd(ev);\n                }\n            } }, ticks.map((tick) => (h(\"div\", { style: tickStyle(tick), role: \"presentation\", class: {\n                'range-tick': true,\n                'range-tick-active': tick.active,\n            }, part: tick.active ? 'tick-active' : 'tick' }))), h(\"div\", { class: \"range-bar-container\" }, h(\"div\", { class: \"range-bar\", role: \"presentation\", part: \"bar\" }), h(\"div\", { class: {\n                'range-bar': true,\n                'range-bar-active': true,\n                'has-ticks': ticks.length > 0,\n            }, role: \"presentation\", style: barStyle, part: \"bar-active\" })), renderKnob(rtl, {\n            knob: 'A',\n            pressed: pressedKnob === 'A',\n            value: this.valA,\n            ratio: this.ratioA,\n            pin,\n            pinFormatter,\n            disabled,\n            handleKeyboard,\n            min,\n            max,\n            inheritedAttributes,\n            onKnobFocus: this.onKnobFocus,\n            onKnobBlur: this.onKnobBlur,\n        }), this.dualKnobs &&\n            renderKnob(rtl, {\n                knob: 'B',\n                pressed: pressedKnob === 'B',\n                value: this.valB,\n                ratio: this.ratioB,\n                pin,\n                pinFormatter,\n                disabled,\n                handleKeyboard,\n                min,\n                max,\n                inheritedAttributes,\n                onKnobFocus: this.onKnobFocus,\n                onKnobBlur: this.onKnobBlur,\n            })));\n    }\n    render() {\n        const { disabled, el, hasLabel, rangeId, pin, pressedKnob, labelPlacement, label } = this;\n        const inItem = hostContext('ion-item', el);\n        /**\n         * If there is no start content then the knob at\n         * the min value will be cut off by the item margin.\n         */\n        const hasStartContent = (hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed')) || this.hasStartSlotContent;\n        const needsStartAdjustment = inItem && !hasStartContent;\n        /**\n         * If there is no end content then the knob at\n         * the max value will be cut off by the item margin.\n         */\n        const hasEndContent = (hasLabel && labelPlacement === 'end') || this.hasEndSlotContent;\n        const needsEndAdjustment = inItem && !hasEndContent;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n        return (h(Host, { key: 'ef7b01f80515bcaeb2983934ad7f10a6bd5d13ec', onFocusin: this.onFocus, onFocusout: this.onBlur, id: rangeId, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'range-disabled': disabled,\n                'range-pressed': pressedKnob !== undefined,\n                'range-has-pin': pin,\n                [`range-label-placement-${labelPlacement}`]: true,\n                'range-item-start-adjustment': needsStartAdjustment,\n                'range-item-end-adjustment': needsEndAdjustment,\n            }) }, h(\"label\", { key: 'fd8aa90a9d52be9da024b907e68858dae424449d', class: \"range-wrapper\", id: \"range-label\" }, h(\"div\", { key: '2172b4f329c22017dd23475c80aac25ba6e753eb', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, label !== undefined ? h(\"div\", { class: \"label-text\" }, label) : h(\"slot\", { name: \"label\" })), h(\"div\", { key: '3c318bf2ea0576646d4c010bf44573fd0f483186', class: \"native-wrapper\" }, h(\"slot\", { key: '6586fd6fc96271e73f8a86c202d1913ad1a26f96', name: \"start\" }), this.renderRangeSlider(), h(\"slot\", { key: '74ac0bc2d2cb66ef708bb729f88b6ecbc1b2155d', name: \"end\" })))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"step\": [\"stepChanged\"],\n        \"activeBarStart\": [\"activeBarStartChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nconst renderKnob = (rtl, { knob, value, ratio, min, max, disabled, pressed, pin, handleKeyboard, pinFormatter, inheritedAttributes, onKnobFocus, onKnobBlur, }) => {\n    const start = rtl ? 'right' : 'left';\n    const knobStyle = () => {\n        const style = {};\n        style[start] = `${ratio * 100}%`;\n        return style;\n    };\n    // The aria label should be preferred over visible text if both are specified\n    const ariaLabel = inheritedAttributes['aria-label'];\n    return (h(\"div\", { onKeyDown: (ev) => {\n            const key = ev.key;\n            if (key === 'ArrowLeft' || key === 'ArrowDown') {\n                handleKeyboard(knob, false);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            else if (key === 'ArrowRight' || key === 'ArrowUp') {\n                handleKeyboard(knob, true);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n        }, onFocus: () => onKnobFocus(knob), onBlur: onKnobBlur, class: {\n            'range-knob-handle': true,\n            'range-knob-a': knob === 'A',\n            'range-knob-b': knob === 'B',\n            'range-knob-pressed': pressed,\n            'range-knob-min': value === min,\n            'range-knob-max': value === max,\n            'ion-activatable': true,\n            'ion-focusable': true,\n        }, style: knobStyle(), role: \"slider\", tabindex: disabled ? -1 : 0, \"aria-label\": ariaLabel !== undefined ? ariaLabel : null, \"aria-labelledby\": ariaLabel === undefined ? 'range-label' : null, \"aria-valuemin\": min, \"aria-valuemax\": max, \"aria-disabled\": disabled ? 'true' : null, \"aria-valuenow\": value }, pin && (h(\"div\", { class: \"range-pin\", role: \"presentation\", part: \"pin\" }, pinFormatter(value))), h(\"div\", { class: \"range-knob\", role: \"presentation\", part: \"knob\" })));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n    let value = (max - min) * ratio;\n    if (step > 0) {\n        // round to nearest multiple of step, then add min\n        value = Math.round(value / step) * step + min;\n    }\n    const clampedValue = clamp(min, value, max);\n    return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n    return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n    ios: rangeIosCss,\n    md: rangeMdCss\n};\n\nexport { Range as ion_range };\n"], "names": ["r", "registerInstance", "d", "createEvent", "m", "printIonWarning", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "f", "findClosestIonContent", "disableContentScrollY", "resetContentScrollY", "isSafeNumber", "clamp", "debounceEvent", "i", "inheritAriaAttributes", "a", "renderHiddenInput", "isRTL", "hostContext", "c", "createColorClasses", "getDecimalPlaces", "n", "toString", "split", "length", "roundToMaxDecimalPlaces", "references", "maxPlaces", "Math", "max", "map", "Number", "toFixed", "rangeIosCss", "rangeMdCss", "Range", "constructor", "hostRef", "_this", "ionChange", "ionInput", "ionFocus", "ionBlur", "ionKnobMoveStart", "ionKnobMoveEnd", "rangeId", "rangeIds", "didLoad", "noUpdate", "hasFocus", "inheritedAttributes", "contentEl", "initialContentScrollY", "ratioA", "ratioB", "name", "dualKnobs", "min", "pin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "round", "snaps", "step", "ticks", "disabled", "compareValues", "newVal", "oldVal", "lower", "upper", "clampBounds", "ensureValueInBounds", "labelPlacement", "setupGesture", "_asyncToGenerator", "rangeSlider", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "onStart", "onMove", "ev", "onEnd", "enable", "handleKeyboard", "knob", "isIncrease", "emit", "updateValue", "emitValueChange", "onBlur", "onFocus", "onKnobFocus", "shadowRoot", "knobA", "querySelector", "knobB", "classList", "remove", "focusedKnobEl", "add", "onKnobBlur", "setTimeout", "_a", "activeElement", "isStillFocusedOnKnob", "contains", "debounce<PERSON><PERSON>ed", "debounce", "originalIonInput", "undefined", "minC<PERSON>ed", "newValue", "updateRatio", "max<PERSON><PERSON>ed", "<PERSON><PERSON><PERSON><PERSON>", "activeBarStartChanged", "activeBarStart", "disabled<PERSON><PERSON>ed", "valueChanged", "oldValue", "valuesChanged", "componentWillLoad", "hasAttribute", "getAttribute", "componentDidLoad", "connectedCallback", "ionContent", "disconnectedCallback", "destroy", "getValue", "detail", "pressedKnob", "currentX", "setPressedKnob", "update", "clientX", "rect", "ratio", "left", "width", "valueToRatio", "ratioToValue", "getBoundingClientRect", "abs", "setFocus", "valA", "valB", "ratioLower", "ratioUpper", "knobEl", "focus", "hasStartSlotContent", "hasEndSlotContent", "<PERSON><PERSON><PERSON><PERSON>", "label", "renderRangeSlider", "barStart", "barEnd", "rtl", "start", "end", "tickStyle", "tick", "barStyle", "ratioMin", "ratioMax", "active", "push", "class", "ref", "rangeEl", "onPointerUp", "style", "role", "part", "renderKnob", "pressed", "render", "inItem", "hasStartContent", "needsStartAdjustment", "hasEndContent", "needsEndAdjustment", "mode", "JSON", "stringify", "key", "onFocusin", "onFocusout", "id", "color", "watchers", "knobStyle", "aria<PERSON><PERSON><PERSON>", "onKeyDown", "preventDefault", "stopPropagation", "tabindex", "clampedValue", "ios", "md", "ion_range"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}