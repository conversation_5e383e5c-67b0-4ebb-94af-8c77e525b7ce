{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-segment-content_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AAE1E,MAAMK,iBAAiB,GAAG,2NAA2N;AAErP,MAAMC,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IACjBP,qDAAgB,CAAC,IAAI,EAAEO,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQP,qDAAC,CAACE,iDAAI,EAAE;MAAEM,GAAG,EAAE;IAA2C,CAAC,EAAER,qDAAC,CAAC,MAAM,EAAE;MAAEQ,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxI;AACJ,CAAC;AACDJ,cAAc,CAACK,KAAK,GAAGN,iBAAiB", "sources": ["./node_modules/@ionic/core/dist/esm/ion-segment-content.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, j as Host } from './index-B_U9CtaY.js';\n\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;min-height:1px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}\";\n\nconst SegmentContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'db6876f2aee7afa1ea8bc147337670faa68fae1c' }, h(\"slot\", { key: 'bc05714a973a5655668679033f5809a1da6db8cc' })));\n    }\n};\nSegmentContent.style = segmentContentCss;\n\nexport { SegmentContent as ion_segment_content };\n"], "names": ["r", "registerInstance", "h", "j", "Host", "segmentContentCss", "SegmentContent", "constructor", "hostRef", "render", "key", "style", "ion_segment_content"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}