{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-img_entry_js.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8H;AAC/D;AAE/D,MAAMa,MAAM,GAAG,oMAAoM;AAEnN,MAAMC,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBf,qDAAgB,CAAC,IAAI,EAAEe,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGd,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACe,aAAa,GAAGf,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACgB,QAAQ,GAAGhB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiB,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACH,aAAa,CAACI,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACJ,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC;EACL;EACAE,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,mBAAmB,GAAGR,uDAAiB,CAAC,IAAI,CAACe,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;EACxE;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACH,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACI,GAAG,KAAKC,SAAS,EAAE;MACxB;IACJ;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAC7B,sBAAsB,IAAIA,MAAM,IAChC,2BAA2B,IAAIA,MAAM,IACrC,gBAAgB,IAAIA,MAAM,CAACC,yBAAyB,CAACC,SAAS,EAAE;MAChE,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,IAAI,IAAK;QACzC;AAChB;AACA;AACA;AACA;QACgB,IAAIA,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc,EAAE;UACtC,IAAI,CAACC,IAAI,CAAC,CAAC;UACX,IAAI,CAACN,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;MACF,IAAI,CAACC,EAAE,CAACM,OAAO,CAAC,IAAI,CAACd,EAAE,CAAC;IAC5B,CAAC,MACI;MACD;MACAe,UAAU,CAAC,MAAM,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC;EACJ;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAACG,SAAS,GAAG,IAAI,CAACpB,OAAO;IAC7B,IAAI,CAACqB,OAAO,GAAG,IAAI,CAACf,GAAG;IACvB,IAAI,CAACZ,cAAc,CAACK,IAAI,CAAC,CAAC;EAC9B;EACAY,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACU,UAAU,CAAC,CAAC;MACpB,IAAI,CAACV,EAAE,GAAGL,SAAS;IACvB;EACJ;EACAgB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEF,OAAO;MAAEG,GAAG;MAAE1B,MAAM;MAAEsB,SAAS;MAAEvB;IAAoB,CAAC,GAAG,IAAI;IACrE,MAAM;MAAE4B;IAAU,CAAC,GAAG5B,mBAAmB;IACzC,OAAQhB,qDAAC,CAACI,iDAAI,EAAE;MAAEyC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE5C,qDAAU,CAAC,IAAI;IAAE,CAAC,EAAEF,qDAAC,CAAC,KAAK,EAAE;MAAE6C,GAAG,EAAE,0CAA0C;MAAEE,QAAQ,EAAE,OAAO;MAAEtB,GAAG,EAAEe,OAAO;MAAEG,GAAG,EAAEA,GAAG;MAAE1B,MAAM,EAAEA,MAAM;MAAEE,OAAO,EAAEoB,SAAS;MAAES,IAAI,EAAE,OAAO;MAAEJ,SAAS,EAAEK,WAAW,CAACL,SAAS;IAAE,CAAC,CAAC,CAAC;EACjS;EACA,IAAIrB,EAAEA,CAAA,EAAG;IAAE,OAAOjB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,YAAY;IACxB,CAAC;EAAE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,GAAIL,SAAS,IAAK;EAC/B,QAAQA,SAAS;IACb,KAAK,MAAM;MACP,OAAO,IAAI;IACf,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAOlB,SAAS;EACxB;AACJ,CAAC;AACDhB,GAAG,CAACyC,KAAK,GAAG1C,MAAM", "sources": ["./node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\n\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\n\nconst Img = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n        this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n        this.ionError = createEvent(this, \"ionError\", 7);\n        this.inheritedAttributes = {};\n        this.onLoad = () => {\n            this.ionImgDidLoad.emit();\n        };\n        this.onError = () => {\n            this.ionError.emit();\n        };\n    }\n    srcChanged() {\n        this.addIO();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n    }\n    componentDidLoad() {\n        this.addIO();\n    }\n    addIO() {\n        if (this.src === undefined) {\n            return;\n        }\n        if (typeof window !== 'undefined' &&\n            'IntersectionObserver' in window &&\n            'IntersectionObserverEntry' in window &&\n            'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n            this.removeIO();\n            this.io = new IntersectionObserver((data) => {\n                /**\n                 * On slower devices, it is possible for an intersection observer entry to contain multiple\n                 * objects in the array. This happens when quickly scrolling an image into view and then out of\n                 * view. In this case, the last object represents the current state of the component.\n                 */\n                if (data[data.length - 1].isIntersecting) {\n                    this.load();\n                    this.removeIO();\n                }\n            });\n            this.io.observe(this.el);\n        }\n        else {\n            // fall back to setTimeout for Safari and IE\n            setTimeout(() => this.load(), 200);\n        }\n    }\n    load() {\n        this.loadError = this.onError;\n        this.loadSrc = this.src;\n        this.ionImgWillLoad.emit();\n    }\n    removeIO() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    render() {\n        const { loadSrc, alt, onLoad, loadError, inheritedAttributes } = this;\n        const { draggable } = inheritedAttributes;\n        return (h(Host, { key: 'da600442894427dee1974a28e545613afac69fca', class: getIonMode(this) }, h(\"img\", { key: '16df0c7069af86c0fa7ce5af598bc0f63b4eb71a', decoding: \"async\", src: loadSrc, alt: alt, onLoad: onLoad, onError: loadError, part: \"image\", draggable: isDraggable(draggable) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"src\": [\"srcChanged\"]\n    }; }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = (draggable) => {\n    switch (draggable) {\n        case 'true':\n            return true;\n        case 'false':\n            return false;\n        default:\n            return undefined;\n    }\n};\nImg.style = imgCss;\n\nexport { Img as ion_img };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "b", "inheritAttributes", "imgCss", "Img", "constructor", "hostRef", "ionImgWillLoad", "ionImgDidLoad", "ionError", "inheritedAttributes", "onLoad", "emit", "onError", "srcChanged", "addIO", "componentWillLoad", "el", "componentDidLoad", "src", "undefined", "window", "IntersectionObserverEntry", "prototype", "removeIO", "io", "IntersectionObserver", "data", "length", "isIntersecting", "load", "observe", "setTimeout", "loadError", "loadSrc", "disconnect", "render", "alt", "draggable", "key", "class", "decoding", "part", "isDraggable", "watchers", "style", "ion_img"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}