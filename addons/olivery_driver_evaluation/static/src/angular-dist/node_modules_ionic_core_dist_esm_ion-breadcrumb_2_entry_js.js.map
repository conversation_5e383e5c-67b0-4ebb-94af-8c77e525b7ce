{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8H;AAC3D;AAC2B;AACJ;AAE1F,MAAMsB,gBAAgB,GAAG,wqIAAwqI;AAEjsI,MAAMC,eAAe,GAAG,y6HAAy6H;AAEj8H,MAAMC,UAAU,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGxB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyB,OAAO,GAAGzB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC0B,cAAc,GAAG1B,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC2B,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACR,QAAQ,CAACS,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACT,OAAO,CAACQ,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,uBAAuB,GAAG,MAAM;MACjC,IAAI,CAACT,cAAc,CAACO,IAAI,CAAC;QAAEG,eAAe,EAAE,IAAI,CAACC;MAAa,CAAC,CAAC;IACpE,CAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACX,mBAAmB,GAAGlB,uDAAqB,CAAC,IAAI,CAAC8B,EAAE,CAAC;EAC7D;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,IAAI,KAAKC,SAAS;EAClC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,KAAK;MAAEf,MAAM;MAAED,SAAS;MAAEE,QAAQ;MAAEe,QAAQ;MAAEN,EAAE;MAAEZ,mBAAmB;MAAEmB,IAAI;MAAEC,eAAe;MAAEhB,eAAe;MAAEiB,SAAS;MAAEC,sBAAsB;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC1K,MAAMC,SAAS,GAAG,IAAI,CAACX,WAAW,CAAC,CAAC;IACpC,MAAMY,OAAO,GAAG,IAAI,CAACX,IAAI,KAAKC,SAAS,GAAG,MAAM,GAAG,GAAG;IACtD;IACA;IACA,MAAMD,IAAI,GAAGX,QAAQ,GAAGY,SAAS,GAAG,IAAI,CAACD,IAAI;IAC7C,MAAMY,IAAI,GAAGnD,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoD,KAAK,GAAGF,OAAO,KAAK,MAAM,GAC1B,CAAC,CAAC,GACF;MACEP,QAAQ;MACRJ,IAAI;MACJS;IACJ,CAAC;IACL;IACA;IACA;IACA,MAAMK,aAAa,GAAGT,IAAI,GAAG,KAAK,GAAGlB,SAAS,GAAIqB,sBAAsB,IAAI,CAACH,IAAI,GAAG,IAAI,GAAG,KAAK,GAAIE,SAAS;IAC7G,OAAQ7C,qDAAC,CAACE,iDAAI,EAAE;MAAEmD,GAAG,EAAE,0CAA0C;MAAEC,OAAO,EAAGC,EAAE,IAAK7C,qDAAO,CAAC4B,IAAI,EAAEiB,EAAE,EAAE3B,eAAe,EAAEgB,eAAe,CAAC;MAAE,eAAe,EAAEjB,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE6B,KAAK,EAAEhD,qDAAkB,CAACiC,KAAK,EAAE;QAC7M,CAACS,IAAI,GAAG,IAAI;QACZ,mBAAmB,EAAExB,MAAM;QAC3B,sBAAsB,EAAED,SAAS;QACjC,qBAAqB,EAAEE,QAAQ;QAC/B,sBAAsB,EAAEhB,qDAAW,CAAC,wBAAwB,EAAEyB,EAAE,CAAC;QACjE,YAAY,EAAEzB,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACyB,EAAE,CAAC;QACjD,kBAAkB,EAAEzB,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACyB,EAAE,CAAC;QAC9D,iBAAiB,EAAEY,SAAS;QAC5B,eAAe,EAAEA;MACrB,CAAC;IAAE,CAAC,EAAEhD,qDAAC,CAACiD,OAAO,EAAEQ,MAAM,CAACC,MAAM,CAAC;MAAEL,GAAG,EAAE;IAA2C,CAAC,EAAEF,KAAK,EAAE;MAAEK,KAAK,EAAE,mBAAmB;MAAEG,IAAI,EAAE,QAAQ;MAAEhC,QAAQ,EAAEA,QAAQ;MAAEE,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,EAAEP,mBAAmB,CAAC,EAAExB,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE5D,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAErD,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEd,sBAAsB,IAAK9C,qDAAC,CAAC,QAAQ,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE,qBAAqB;MAAE,YAAY,EAAE,uBAAuB;MAAEL,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACtB,uBAAuB,CAAC,CAAC;MAAE6B,GAAG,EAAGC,WAAW,IAAM,IAAI,CAAC5B,YAAY,GAAG4B,WAAY;MAAEN,KAAK,EAAE;QACvsB,iCAAiC,EAAE;MACvC;IAAE,CAAC,EAAExD,qDAAC,CAAC,UAAU,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEU,IAAI,EAAEhD,iDAAkB;MAAEiD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAE,EAAEZ,aAAa;IAC1J;AACR;AACA;AACA;AACA;IACQpD,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,sBAAsB;MAAEG,IAAI,EAAE,WAAW;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE3D,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAY,CAAC,EAAEV,IAAI,KAAK,KAAK,GAAIlD,qDAAC,CAAC,UAAU,EAAE;MAAE+D,IAAI,EAAElD,iDAAqB;MAAEmD,IAAI,EAAE,KAAK;MAAE,UAAU,EAAE;IAAK,CAAC,CAAC,GAAKhE,qDAAC,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAC,CAAC,CAAC;EAC3V;EACA,IAAIoC,EAAEA,CAAA,EAAG;IAAE,OAAOhC,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDc,UAAU,CAAC+C,KAAK,GAAG;EACfC,GAAG,EAAElD,gBAAgB;EACrBmD,EAAE,EAAElD;AACR,CAAC;AAED,MAAMmD,iBAAiB,GAAG,wmBAAwmB;AAEloB,MAAMC,gBAAgB,GAAG,wjBAAwjB;AAEjlB,MAAMC,WAAW,GAAG,MAAM;EACtBnD,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACmD,iBAAiB,GAAG1E,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2E,mBAAmB,GAAG,CAAC;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,eAAe,GAAG,MAAM;MACzB,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,WAAW,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,qBAAqB,GAAG,MAAM;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA;MACA,MAAMC,gBAAgB,GAAGF,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAACxD,MAAM,CAAC;MAC5E,IAAIsD,gBAAgB,IAAI,IAAI,CAACG,aAAa,EAAE;QACxCH,gBAAgB,CAACtD,MAAM,GAAG,KAAK;MACnC;IACJ,CAAC;IACD,IAAI,CAACkD,WAAW,GAAG,MAAM;MACrB,MAAM;QAAEH,kBAAkB;QAAED,mBAAmB;QAAEY;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMN,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC,KAAK,MAAMG,UAAU,IAAIJ,WAAW,EAAE;QAClCI,UAAU,CAACpC,sBAAsB,GAAG,KAAK;QACzCoC,UAAU,CAACzD,SAAS,GAAG,KAAK;MAChC;MACA;MACA;MACA;MACA,MAAM4D,cAAc,GAAGD,QAAQ,KAAK7C,SAAS,IAAIuC,WAAW,CAACQ,MAAM,GAAGF,QAAQ,IAAIZ,mBAAmB,GAAGC,kBAAkB,IAAIW,QAAQ;MACtI,IAAIC,cAAc,EAAE;QAChB;QACAP,WAAW,CAACS,OAAO,CAAC,CAACL,UAAU,EAAEM,KAAK,KAAK;UACvC,IAAIA,KAAK,KAAKhB,mBAAmB,EAAE;YAC/BU,UAAU,CAACpC,sBAAsB,GAAG,IAAI;UAC5C;UACA;UACA;UACA;UACA,IAAI0C,KAAK,IAAIhB,mBAAmB,IAAIgB,KAAK,GAAGV,WAAW,CAACQ,MAAM,GAAGb,kBAAkB,EAAE;YACjFS,UAAU,CAACzD,SAAS,GAAG,IAAI;UAC/B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACkD,sBAAsB,GAAG,MAAM;MAChC,MAAM;QAAEF,kBAAkB;QAAED,mBAAmB;QAAEY;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMN,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA,MAAMrD,MAAM,GAAGoD,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAACxD,MAAM,CAAC;MAClE;MACA,KAAK,MAAMwD,UAAU,IAAIJ,WAAW,EAAE;QAClC;QACA;QACA;QACA,MAAMnC,IAAI,GAAGyC,QAAQ,KAAK7C,SAAS,IAAIkC,kBAAkB,KAAK,CAAC,GACzDS,UAAU,KAAKJ,WAAW,CAACN,mBAAmB,CAAC,GAC/CU,UAAU,KAAKJ,WAAW,CAACA,WAAW,CAACQ,MAAM,GAAG,CAAC,CAAC;QACxDJ,UAAU,CAACvC,IAAI,GAAGA,IAAI;QACtB;QACA;QACA;QACA,MAAME,SAAS,GAAGqC,UAAU,CAACrC,SAAS,KAAKN,SAAS,GAAG2C,UAAU,CAACrC,SAAS,GAAGF,IAAI,GAAGJ,SAAS,GAAG,IAAI;QACrG2C,UAAU,CAACrC,SAAS,GAAGA,SAAS;QAChC;QACA;QACA,IAAI,CAACnB,MAAM,IAAIiB,IAAI,EAAE;UACjBuC,UAAU,CAACxD,MAAM,GAAG,IAAI;UACxB,IAAI,CAACyD,aAAa,GAAG,IAAI;QAC7B;MACJ;IACJ,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MACxB,OAAOU,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtD,EAAE,CAACuD,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,CAACf,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;IAC1B,CAAC;EACL;EACAmB,gBAAgBA,CAACtC,EAAE,EAAE;IACjB,MAAMuB,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACzC,MAAMe,oBAAoB,GAAGhB,WAAW,CAACiB,MAAM,CAAEb,UAAU,IAAKA,UAAU,CAACzD,SAAS,CAAC;IACrF,IAAI,CAAC8C,iBAAiB,CAACzC,IAAI,CAAC2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACyC,MAAM,CAAC,EAAE;MAAEF;IAAqB,CAAC,CAAC,CAAC;EACtG;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACpB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;EAC1B;EACAvC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACuC,eAAe,CAAC,CAAC;EAC1B;EACAlC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,KAAK;MAAEhB;IAAU,CAAC,GAAG,IAAI;IACjC,MAAMyB,IAAI,GAAGnD,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEmD,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAEhD,qDAAkB,CAACiC,KAAK,EAAE;QAC5F,CAACS,IAAI,GAAG,IAAI;QACZ,YAAY,EAAEvC,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACyB,EAAE,CAAC;QACjD,kBAAkB,EAAEzB,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACyB,EAAE,CAAC;QAC9D,uBAAuB,EAAEX;MAC7B,CAAC;IAAE,CAAC,EAAEzB,qDAAC,CAAC,MAAM,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAE6C,YAAY,EAAE,IAAI,CAACN;IAAY,CAAC,CAAC,CAAC;EAC7G;EACA,IAAIxD,EAAEA,CAAA,EAAG;IAAE,OAAOhC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW+F,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,qBAAqB,EAAE,CAAC,iBAAiB,CAAC;MAC1C,oBAAoB,EAAE,CAAC,iBAAiB;IAC5C,CAAC;EAAE;AACP,CAAC;AACD7B,WAAW,CAACL,KAAK,GAAG;EAChBC,GAAG,EAAEE,iBAAiB;EACtBD,EAAE,EAAEE;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-breadcrumb_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, o as openURL, h as hostContext } from './theme-DiVJyqlX.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-BLV6ykCk.js';\n\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #2d4665));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, rgba(233, 237, 243, 0.7)));font-size:clamp(16px, 1rem, 22px)}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, var(--ion-text-color-step-250, #445b78))}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-400, var(--ion-text-color-step-600, #92a0b3));font-size:min(1.125rem, 21.6px)}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #242d39))}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e9edf3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9e0ea))}.breadcrumbs-collapsed-indicator ion-icon{font-size:min(1.375rem, 22px)}\";\n\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #677483));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, var(--ion-text-color-step-200, #35404e));--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, #fff))}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-550, var(--ion-text-color-step-450, #7d8894));font-size:1.125rem}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #222d3a))}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #eef1f3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #dfe5e8))}\";\n\nconst Breadcrumb = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n        this.inheritedAttributes = {};\n        /** @internal */\n        this.collapsed = false;\n        /**\n         * If `true`, the breadcrumb will take on a different look to show that\n         * it is the currently active breadcrumb. Defaults to `true` for the\n         * last breadcrumb if it is not set on any.\n         */\n        this.active = false;\n        /**\n         * If `true`, the user cannot interact with the breadcrumb.\n         */\n        this.disabled = false;\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.collapsedIndicatorClick = () => {\n            this.collapsedClick.emit({ ionShadowTarget: this.collapsedRef });\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    isClickable() {\n        return this.href !== undefined;\n    }\n    render() {\n        const { color, active, collapsed, disabled, download, el, inheritedAttributes, last, routerAnimation, routerDirection, separator, showCollapsedIndicator, target, } = this;\n        const clickable = this.isClickable();\n        const TagType = this.href === undefined ? 'span' : 'a';\n        // Links can still be tabbed to when set to disabled if they have an href\n        // in order to truly disable them we can keep it as an anchor but remove the href\n        const href = disabled ? undefined : this.href;\n        const mode = getIonMode(this);\n        const attrs = TagType === 'span'\n            ? {}\n            : {\n                download,\n                href,\n                target,\n            };\n        // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n        // to show the separator as long as it isn't also the last breadcrumb\n        // otherwise if not collapsed use the value in separator\n        const showSeparator = last ? false : collapsed ? (showCollapsedIndicator && !last ? true : false) : separator;\n        return (h(Host, { key: '32ca61c83721dff52b5e97171ed449dce3584a55', onClick: (ev) => openURL(href, ev, routerDirection, routerAnimation), \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                'breadcrumb-active': active,\n                'breadcrumb-collapsed': collapsed,\n                'breadcrumb-disabled': disabled,\n                'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': clickable,\n                'ion-focusable': clickable,\n            }) }, h(TagType, Object.assign({ key: '479feb845f4a6d8009d5422b33eb423730b9722b' }, attrs, { class: \"breadcrumb-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"slot\", { key: '3c5dcaeb0d258235d1b7707868026ff1d1404099', name: \"start\" }), h(\"slot\", { key: 'f1cfb934443cd97dc220882c5e3596ea879d66cf' }), h(\"slot\", { key: '539710121b5b1f3ee8d4c24a9651b67c2ae08add', name: \"end\" })), showCollapsedIndicator && (h(\"button\", { key: 'ed53a95ccd89022c8b7bee0658a221ec62a5c73b', part: \"collapsed-indicator\", \"aria-label\": \"Show more breadcrumbs\", onClick: () => this.collapsedIndicatorClick(), ref: (collapsedEl) => (this.collapsedRef = collapsedEl), class: {\n                'breadcrumbs-collapsed-indicator': true,\n            } }, h(\"ion-icon\", { key: 'a849e1142a86f06f207cf11662fa2a560ab7fc6a', \"aria-hidden\": \"true\", icon: ellipsisHorizontal, lazy: false }))), showSeparator && (\n        /**\n         * Separators should not be announced by narrators.\n         * We add aria-hidden on the span so that this applies\n         * to any custom separators too.\n         */\n        h(\"span\", { key: 'fc3c741cb01fafef8b26046c7ee5b190efc69a7c', class: \"breadcrumb-separator\", part: \"separator\", \"aria-hidden\": \"true\" }, h(\"slot\", { key: '4871932ae1dae520767e0713e7cee2d11b0bba6d', name: \"separator\" }, mode === 'ios' ? (h(\"ion-icon\", { icon: chevronForwardOutline, lazy: false, \"flip-rtl\": true })) : (h(\"span\", null, \"/\")))))));\n    }\n    get el() { return getElement(this); }\n};\nBreadcrumb.style = {\n    ios: breadcrumbIosCss,\n    md: breadcrumbMdCss\n};\n\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\n\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\n\nconst Breadcrumbs = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n        /**\n         * The number of breadcrumbs to show before the collapsed indicator.\n         * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n         * the breadcrumbs will not be collapsed.\n         */\n        this.itemsBeforeCollapse = 1;\n        /**\n         * The number of breadcrumbs to show after the collapsed indicator.\n         * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n         * the breadcrumbs will not be collapsed.\n         */\n        this.itemsAfterCollapse = 1;\n        this.breadcrumbsInit = () => {\n            this.setBreadcrumbSeparator();\n            this.setMaxItems();\n        };\n        this.resetActiveBreadcrumb = () => {\n            const breadcrumbs = this.getBreadcrumbs();\n            // Only reset the active breadcrumb if we were the ones to change it\n            // otherwise use the one set on the component\n            const activeBreadcrumb = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            if (activeBreadcrumb && this.activeChanged) {\n                activeBreadcrumb.active = false;\n            }\n        };\n        this.setMaxItems = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            for (const breadcrumb of breadcrumbs) {\n                breadcrumb.showCollapsedIndicator = false;\n                breadcrumb.collapsed = false;\n            }\n            // If the number of breadcrumbs exceeds the maximum number of items\n            // that should show and the items before / after collapse do not\n            // exceed the maximum items then we need to collapse the breadcrumbs\n            const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n            if (shouldCollapse) {\n                // Show the collapsed indicator in the first breadcrumb that collapses\n                breadcrumbs.forEach((breadcrumb, index) => {\n                    if (index === itemsBeforeCollapse) {\n                        breadcrumb.showCollapsedIndicator = true;\n                    }\n                    // Collapse all breadcrumbs that have an index greater than or equal to\n                    // the number before collapse and an index less than the total number\n                    // of breadcrumbs minus the items that should show after the collapse\n                    if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n                        breadcrumb.collapsed = true;\n                    }\n                });\n            }\n        };\n        this.setBreadcrumbSeparator = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            // Check if an active breadcrumb exists already\n            const active = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            // Set the separator on all but the last breadcrumb\n            for (const breadcrumb of breadcrumbs) {\n                // The only time the last breadcrumb changes is when\n                // itemsAfterCollapse is set to 0, in this case the\n                // last breadcrumb will be the collapsed indicator\n                const last = maxItems !== undefined && itemsAfterCollapse === 0\n                    ? breadcrumb === breadcrumbs[itemsBeforeCollapse]\n                    : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n                breadcrumb.last = last;\n                // If the breadcrumb has defined whether or not to show the\n                // separator then use that value, otherwise check if it's the\n                // last breadcrumb\n                const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n                breadcrumb.separator = separator;\n                // If there is not an active breadcrumb already\n                // set the last one to active\n                if (!active && last) {\n                    breadcrumb.active = true;\n                    this.activeChanged = true;\n                }\n            }\n        };\n        this.getBreadcrumbs = () => {\n            return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n        };\n        this.slotChanged = () => {\n            this.resetActiveBreadcrumb();\n            this.breadcrumbsInit();\n        };\n    }\n    onCollapsedClick(ev) {\n        const breadcrumbs = this.getBreadcrumbs();\n        const collapsedBreadcrumbs = breadcrumbs.filter((breadcrumb) => breadcrumb.collapsed);\n        this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), { collapsedBreadcrumbs }));\n    }\n    maxItemsChanged() {\n        this.resetActiveBreadcrumb();\n        this.breadcrumbsInit();\n    }\n    componentWillLoad() {\n        this.breadcrumbsInit();\n    }\n    render() {\n        const { color, collapsed } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'fe64e9cdf597ede2db140bf5fa05a0359d82db57', class: createColorClasses(color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'breadcrumbs-collapsed': collapsed,\n            }) }, h(\"slot\", { key: 'a2c99b579e339055c50a613d5c6b61032f5ddffe', onSlotchange: this.slotChanged })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"maxItems\": [\"maxItemsChanged\"],\n        \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n        \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n    }; }\n};\nBreadcrumbs.style = {\n    ios: breadcrumbsIosCss,\n    md: breadcrumbsMdCss\n};\n\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "i", "inheritAriaAttributes", "c", "createColorClasses", "o", "openURL", "hostContext", "m", "chevronForwardOutline", "n", "ellipsisHorizontal", "breadcrumbIosCss", "breadcrumbMdCss", "Breadcrumb", "constructor", "hostRef", "ionFocus", "ionBlur", "collapsedClick", "inheritedAttributes", "collapsed", "active", "disabled", "routerDirection", "onFocus", "emit", "onBlur", "collapsedIndicatorClick", "ionShadowTarget", "collapsedRef", "componentWillLoad", "el", "isClickable", "href", "undefined", "render", "color", "download", "last", "routerAnimation", "separator", "showCollapsedIndicator", "target", "clickable", "TagType", "mode", "attrs", "showSeparator", "key", "onClick", "ev", "class", "Object", "assign", "part", "name", "ref", "collapsedEl", "icon", "lazy", "style", "ios", "md", "breadcrumbsIosCss", "breadcrumbsMdCss", "Breadcrumbs", "ionCollapsedClick", "itemsBeforeCollapse", "itemsAfterCollapse", "breadcrumbsInit", "setBreadcrumbSeparator", "setMaxItems", "resetActiveBreadcrumb", "breadcrumbs", "getBreadcrumbs", "activeBreadcrumb", "find", "breadcrumb", "activeChanged", "maxItems", "shouldCollapse", "length", "for<PERSON>ach", "index", "Array", "from", "querySelectorAll", "slotChanged", "onCollapsedClick", "collapsedBreadcrumbs", "filter", "detail", "maxItemsChanged", "onSlotchange", "watchers", "ion_breadcrumb", "ion_breadcrumbs"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}