{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6I;AAChE;AAC5B;AACyB;AAC8J;AACjL;AACQ;AACjC;AACG;AACJ;AACA;AACa;AACE;AACF;;AAE1C;AACA;AACA;AACA,MAAMiC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMS,iBAAiB,GAAIZ,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMU,gBAAgB,GAAIb,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMW,gBAAgB,GAAId,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMY,iBAAiB,GAAG,kvSAAkvS;AAE5wS,MAAMC,gBAAgB,GAAG,6oLAA6oL;AAEtqL,MAAMC,WAAW,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjBpD,qDAAgB,CAAC,IAAI,EAAEoD,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGnD,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAACoD,WAAW,GAAGpD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAACqD,WAAW,GAAGrD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAACsD,UAAU,GAAGtD,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAACuD,mBAAmB,GAAGvD,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACwD,oBAAoB,GAAGxD,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACyD,oBAAoB,GAAGzD,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC0D,mBAAmB,GAAG1D,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC2D,kBAAkB,GAAG7C,wDAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAAC8C,cAAc,GAAG/C,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAACgD,iBAAiB,GAAG9C,wDAAuB,CAAC,CAAC;IAClD,IAAI,CAAC+C,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAChD,OAAO,CAACiD,SAAS,EAAEtD,oDAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACuD,qBAAqB,GAAIC,EAAE,IAAK;MACjC,MAAMC,IAAI,GAAGD,EAAE,CAACE,MAAM,CAACD,IAAI;MAC3B,IAAIvD,wDAAQ,CAACuD,IAAI,CAAC,EAAE;QAChB,MAAME,YAAY,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,IAAI,KAAK,QAAQ,CAAC;QACvE,IAAI,CAACM,iBAAiB,CAACJ,YAAY,CAAC;MACxC;IACJ,CAAC;EACL;EACAK,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAAC/D,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAI8D,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAC7D,OAAO,CAAC,CAAC;IAClB;EACJ;EACA8D,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,OAAO;MAAEC,EAAE;MAAEzB;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIwB,OAAO,EAAE;MACTxB,iBAAiB,CAAC0B,gBAAgB,CAACD,EAAE,EAAED,OAAO,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACUjE,OAAOA,CAAA,EAAG;IAAA,IAAAoE,KAAA;IAAA,OAAAC,yMAAA;MACZ,MAAMC,MAAM,SAASF,KAAI,CAAC5B,cAAc,CAAC+B,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAAC7B,kBAAkB,CAACiC,eAAe,CAAC,CAAC;MAC/C,MAAMxE,wDAAO,CAACoE,KAAI,EAAE,kBAAkB,EAAE1D,iBAAiB,EAAEc,gBAAgB,CAAC;MAC5E8C,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUpE,OAAOA,CAACuE,IAAI,EAAEnB,IAAI,EAAE;IAAA,IAAAoB,MAAA;IAAA,OAAAL,yMAAA;MACtB,MAAMC,MAAM,SAASI,MAAI,CAAClC,cAAc,CAAC+B,IAAI,CAAC,CAAC;MAC/C,MAAMI,SAAS,SAASzE,wDAAO,CAACwE,MAAI,EAAED,IAAI,EAAEnB,IAAI,EAAE,kBAAkB,EAAE/B,iBAAiB,EAAEE,gBAAgB,CAAC;MAC1G,IAAIkD,SAAS,EAAE;QACXD,MAAI,CAACnC,kBAAkB,CAACqC,iBAAiB,CAAC,CAAC;MAC/C;MACAN,MAAM,CAAC,CAAC;MACR,OAAOK,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAO1E,wDAAW,CAAC,IAAI,CAAC+D,EAAE,EAAE,0BAA0B,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACIY,aAAaA,CAAA,EAAG;IACZ,OAAO3E,wDAAW,CAAC,IAAI,CAAC+D,EAAE,EAAE,2BAA2B,CAAC;EAC5D;EACMa,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAZ,yMAAA;MACtB,MAAMf,IAAI,GAAG0B,MAAM,CAAC1B,IAAI;MACxB,IAAIvD,wDAAQ,CAACuD,IAAI,CAAC,EAAE;QAChB,OAAO2B,MAAI,CAAC/E,OAAO,CAAC8E,MAAM,CAACP,IAAI,EAAEnB,IAAI,CAAC;MAC1C;MACA,MAAM4B,aAAa,SAASD,MAAI,CAACrB,iBAAiB,CAACoB,MAAM,CAAC;MAC1D,IAAIE,aAAa,EAAE;QACf,OAAOD,MAAI,CAAC/E,OAAO,CAAC8E,MAAM,CAACP,IAAI,EAAEO,MAAM,CAAC1B,IAAI,CAAC;MACjD;MACA,OAAO6B,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC7B;EACMxB,iBAAiBA,CAACoB,MAAM,EAAE;IAAA,OAAAX,yMAAA;MAC5B,IAAIW,MAAM,EAAE;QACR;QACA;QACA,MAAMK,GAAG,SAAShF,wDAAQ,CAAC2E,MAAM,CAACM,OAAO,CAAC;QAC1C,IAAID,GAAG,KAAK,KAAK,EAAE;UACf;UACA,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACA5B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACZ,OAAO,CAAC0C,GAAG,CAAE5B,CAAC,IAAK;MAC3B,OAAO,OAAOA,CAAC,KAAK,QAAQ,GAAG;QAAE6B,IAAI,EAAE7B;MAAE,CAAC,GAAGA,CAAC;IAClD,CAAC,CAAC;EACN;EACA8B,iBAAiBA,CAAA,EAAG;IAChBnF,wDAAc,CAAC,IAAI,CAAC4D,EAAE,CAAC;IACvB,IAAI,CAACF,cAAc,CAAC,CAAC;EACzB;EACA0B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACD,OAAO,GAAGxC,SAAS;IAC5B;IACA,IAAI,CAACV,iBAAiB,CAACoD,mBAAmB,CAAC,CAAC;EAChD;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,IAAI,EAAE,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,EAAE,CAAC,EAAE;MAC1E1F,wDAAY,CAAC,IAAI,CAAC2D,EAAE,CAAC;IACzB;EACJ;EACAgC,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAG,IAAI;IACnC,IAAI,CAAC,IAAI,CAACT,OAAO,IAAI7G,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAIsH,SAAS,IAAID,OAAO,EAAE;MACrEnH,qDAAQ,CAAC,MAAM;QACX,MAAMqH,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,YAAY;QAChE,IAAI,CAACF,YAAY,EAAE;UACf,IAAI,CAACV,OAAO,GAAGpG,6DAAyB,CAAC6G,SAAS,EAAGI,KAAK,IAAKA,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;UAC/G,IAAI,CAACf,OAAO,CAACgB,MAAM,CAAC,IAAI,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC1D,MAAM,KAAK,IAAI,EAAE;MACtBzD,uDAAG,CAAC,MAAM,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgE,cAAc,CAAC,CAAC;EACzB;EACA4C,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,MAAM;MAAEb,cAAc;MAAEc;IAAa,CAAC,GAAG,IAAI;IACrD,MAAMC,IAAI,GAAGjI,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkI,UAAU,GAAG,IAAI,CAACvD,UAAU,CAAC,CAAC;IACpC,MAAMD,YAAY,GAAGwD,UAAU,CAACtD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,IAAI,KAAK,QAAQ,CAAC;IAChE,MAAMT,OAAO,GAAGmE,UAAU,CAACC,MAAM,CAAEtD,CAAC,IAAKA,CAAC,CAACL,IAAI,KAAK,QAAQ,CAAC;IAC7D,MAAM4D,QAAQ,GAAG,gBAAgBJ,YAAY,SAAS;IACtD,OAAQ7H,qDAAC,CAACE,iDAAI,EAAEgI,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE,0CAA0C;MAAE/D,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEuD,MAAM,KAAK1D,SAAS,GAAG+D,QAAQ,GAAG,IAAI;MAAEI,QAAQ,EAAE;IAAK,CAAC,EAAEtB,cAAc,EAAE;MAAEuB,KAAK,EAAE;QACrNC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACV,YAAY;MACxC,CAAC;MAAEW,KAAK,EAAEN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE,CAACL,IAAI,GAAG;MAAK,CAAC,EAAEvG,qDAAW,CAAC,IAAI,CAACkH,QAAQ,CAAC,CAAC,EAAE;QAAE,gBAAgB,EAAE,IAAI;QAAE,0BAA0B,EAAE,IAAI,CAAC3E;MAAY,CAAC,CAAC;MAAE4E,2BAA2B,EAAE,IAAI,CAACvE,qBAAqB;MAAEwE,gBAAgB,EAAE,IAAI,CAAC1E;IAAc,CAAC,CAAC,EAAEjE,qDAAC,CAAC,cAAc,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEQ,QAAQ,EAAE,IAAI,CAAC/E;IAAgB,CAAC,CAAC,EAAE7D,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,EAAErI,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,0CAA0C;MAAEK,GAAG,EAAG5D,EAAE,IAAM,IAAI,CAACkC,SAAS,GAAGlC;IAAI,CAAC,EAAEjF,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAyB,CAAC,EAAExI,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,oBAAoB;MAAEK,GAAG,EAAG5D,EAAE,IAAM,IAAI,CAACiC,OAAO,GAAGjC;IAAI,CAAC,EAAE2C,MAAM,KAAK1D,SAAS,IAAKlE,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEpB,EAAE,EAAEiB,QAAQ;MAAEO,KAAK,EAAE;QACt6B,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAE,IAAI,CAACM,SAAS,KAAK5E;MACrD;IAAE,CAAC,EAAE0D,MAAM,EAAE,IAAI,CAACkB,SAAS,IAAI9I,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAyB,CAAC,EAAE,IAAI,CAACM,SAAS,CAAC,CAAE,EAAElF,OAAO,CAAC0C,GAAG,CAAE5B,CAAC,IAAM1E,qDAAC,CAAC,QAAQ,EAAEkI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzD,CAAC,CAACqC,cAAc,EAAE;MAAEgC,IAAI,EAAE,QAAQ;MAAE/B,EAAE,EAAEtC,CAAC,CAACsC,EAAE;MAAEwB,KAAK,EAAEQ,WAAW,CAACtE,CAAC,CAAC;MAAEuE,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACnD,WAAW,CAACpB,CAAC,CAAC;MAAEwE,QAAQ,EAAExE,CAAC,CAACwE;IAAS,CAAC,CAAC,EAAElJ,qDAAC,CAAC,MAAM,EAAE;MAAEwI,KAAK,EAAE;IAA4B,CAAC,EAAE9D,CAAC,CAACyE,IAAI,IAAInJ,qDAAC,CAAC,UAAU,EAAE;MAAEmJ,IAAI,EAAEzE,CAAC,CAACyE,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEZ,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE9D,CAAC,CAAC6B,IAAI,CAAC,EAAEuB,IAAI,KAAK,IAAI,IAAI9H,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC,CAAC,EAAEuE,YAAY,IAAKvE,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA+C,CAAC,EAAExI,qDAAC,CAAC,QAAQ,EAAEkI,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE;IAA2C,CAAC,EAAE7D,YAAY,CAACwC,cAAc,EAAE;MAAEgC,IAAI,EAAE,QAAQ;MAAEP,KAAK,EAAEQ,WAAW,CAACzE,YAAY,CAAC;MAAE0E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACnD,WAAW,CAACvB,YAAY;IAAE,CAAC,CAAC,EAAEvE,qDAAC,CAAC,MAAM,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA4B,CAAC,EAAEjE,YAAY,CAAC4E,IAAI,IAAKnJ,qDAAC,CAAC,UAAU,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEe,IAAI,EAAE5E,YAAY,CAAC4E,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEZ,KAAK,EAAE;IAAoB,CAAC,CAAE,EAAEjE,YAAY,CAACgC,IAAI,CAAC,EAAEuB,IAAI,KAAK,IAAI,IAAI9H,qDAAC,CAAC,mBAAmB,EAAE;MAAEoI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,EAAEpI,qDAAC,CAAC,KAAK,EAAE;MAAEoI,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,GAAG;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC;EACh3C;EACA,IAAIpD,EAAEA,CAAA,EAAG;IAAE,OAAO7E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAML,WAAW,GAAIjD,MAAM,IAAK;EAC5B,OAAOmC,MAAM,CAACC,MAAM,CAAC;IAAE,qBAAqB,EAAE,IAAI;IAAE,iBAAiB,EAAE,CAACpC,MAAM,CAACmD,QAAQ;IAAE,eAAe,EAAE,CAACnD,MAAM,CAACmD,QAAQ;IAAE,CAAC,gBAAgBnD,MAAM,CAAC1B,IAAI,EAAE,GAAG0B,MAAM,CAAC1B,IAAI,KAAKH;EAAU,CAAC,EAAE3C,qDAAW,CAACwE,MAAM,CAAC0C,QAAQ,CAAC,CAAC;AAC3N,CAAC;AACD9F,WAAW,CAAC2F,KAAK,GAAG;EAChBgB,GAAG,EAAE7G,iBAAiB;EACtB8G,EAAE,EAAE7G;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-action-sheet.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, f as readTask, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createButtonActiveGesture } from './button-active-Bxcnevju.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, f as present, g as dismiss, h as eventMethod, s as safeCall, j as prepareOverlay, k as setOverlayId } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\nimport './index-CfgBF1SE.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\n\n/**\n * iOS Action Sheet Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Action Sheet Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst actionSheetIosCss = \".sc-ion-action-sheet-ios-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-ios-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-ios{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-ios{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-ios::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-ios{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-ios{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--backdrop-opacity:var(--ion-backdrop-opacity, 0.4);--button-background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent;--button-background-activated:var(--ion-text-color, #000);--button-background-activated-opacity:.08;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-background-selected:var(--ion-color-step-150, var(--ion-background-color-step-150, var(--ion-background-color, #fff)));--button-background-selected-opacity:1;--button-color:var(--ion-color-primary, #0054e9);--button-color-disabled:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));text-align:center}.action-sheet-wrapper.sc-ion-action-sheet-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);padding-bottom:var(--ion-safe-area-bottom, 0);-webkit-box-sizing:content-box;box-sizing:content-box}.action-sheet-container.sc-ion-action-sheet-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}.action-sheet-group.sc-ion-action-sheet-ios{border-radius:13px;margin-bottom:8px}.action-sheet-group.sc-ion-action-sheet-ios:first-child{margin-top:10px}.action-sheet-group.sc-ion-action-sheet-ios:last-child{margin-bottom:10px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-group.sc-ion-action-sheet-ios{background-color:transparent;-webkit-backdrop-filter:saturate(280%) blur(20px);backdrop-filter:saturate(280%) blur(20px)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-title.sc-ion-action-sheet-ios,.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.sc-ion-action-sheet-ios{background-color:transparent;background-image:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8))), -webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background-image:linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%), linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4) 50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 50%);background-repeat:no-repeat;background-position:top, bottom;background-size:100% calc(100% - 1px), 100% 1px;-webkit-backdrop-filter:saturate(120%);backdrop-filter:saturate(120%)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.ion-activated.sc-ion-action-sheet-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.7);background-image:none}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-cancel.sc-ion-action-sheet-ios{background:var(--button-background-selected)}}.action-sheet-title.sc-ion-action-sheet-ios{background:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, transparent)) bottom/100% 1px no-repeat transparent;background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent}.action-sheet-title.sc-ion-action-sheet-ios{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:14px;padding-bottom:13px;color:var(--color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-size:max(13px, 0.8125rem);font-weight:400;text-align:center}.action-sheet-title.action-sheet-has-sub-title.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-sub-title.sc-ion-action-sheet-ios{padding-left:0;padding-right:0;padding-top:6px;padding-bottom:0;font-size:max(13px, 0.8125rem);font-weight:400}.action-sheet-button.sc-ion-action-sheet-ios{-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:14px;padding-bottom:14px;min-height:56px;font-size:max(20px, 1.25rem);contain:content}.action-sheet-button.sc-ion-action-sheet-ios .action-sheet-icon.sc-ion-action-sheet-ios{-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:max(28px, 1.75rem);pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios:last-child{background-image:none}.action-sheet-selected.sc-ion-action-sheet-ios{font-weight:bold}.action-sheet-cancel.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-cancel.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-destructive.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-activated.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-focused.sc-ion-action-sheet-ios{color:var(--ion-color-danger, #c5000f)}@media (any-hover: hover){.action-sheet-destructive.sc-ion-action-sheet-ios:hover{color:var(--ion-color-danger, #c5000f)}}\";\n\nconst actionSheetMdCss = \".sc-ion-action-sheet-md-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-md-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-md{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-md{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-md{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-md::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-md{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-md{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-md::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);--button-background:transparent;--button-background-selected:currentColor;--button-background-selected-opacity:0;--button-background-activated:transparent;--button-background-activated-opacity:0;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--button-color-disabled:var(--button-color);--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54)}.action-sheet-wrapper.sc-ion-action-sheet-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:0}.action-sheet-title.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:20px;padding-bottom:17px;min-height:60px;color:var(--color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54));font-size:1rem;text-align:start}.action-sheet-sub-title.sc-ion-action-sheet-md{padding-left:0;padding-right:0;padding-top:16px;padding-bottom:0;font-size:0.875rem}.action-sheet-group.sc-ion-action-sheet-md:first-child{padding-top:0}.action-sheet-group.sc-ion-action-sheet-md:last-child{padding-bottom:var(--ion-safe-area-bottom)}.action-sheet-button.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;position:relative;min-height:52px;font-size:1rem;text-align:start;contain:content;overflow:hidden}.action-sheet-icon.sc-ion-action-sheet-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:0;margin-bottom:0;color:var(--color);font-size:1.5rem}.action-sheet-button-inner.sc-ion-action-sheet-md{-ms-flex-pack:start;justify-content:flex-start}.action-sheet-selected.sc-ion-action-sheet-md{font-weight:bold}\";\n\nconst ActionSheet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionActionSheetDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionActionSheetWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionActionSheetWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionActionSheetDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * An array of buttons for the action sheet.\n         */\n        this.buttons = [];\n        /**\n         * If `true`, the action sheet will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, the action sheet will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the action sheet will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the action sheet will open. If `false`, the action sheet will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the actionSheetController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the action sheet dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.getButtons().find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    /**\n     * Present the action sheet overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'actionSheetEnter', iosEnterAnimation, mdEnterAnimation);\n        unlock();\n    }\n    /**\n     * Dismiss the action sheet overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the action sheet.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the action sheet. Some examples include:\n     * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        const dismissed = await dismiss(this, data, role, 'actionSheetLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the action sheet did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionActionSheetDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the action sheet will dismiss.\n     *\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionActionSheetWillDismiss');\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        if (isCancel(role)) {\n            return this.dismiss(button.data, role);\n        }\n        const shouldDismiss = await this.callButtonHandler(button);\n        if (shouldDismiss) {\n            return this.dismiss(button.data, button.role);\n        }\n        return Promise.resolve();\n    }\n    async callButtonHandler(button) {\n        if (button) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const rtn = await safeCall(button.handler);\n            if (rtn === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n        }\n        return true;\n    }\n    getButtons() {\n        return this.buttons.map((b) => {\n            return typeof b === 'string' ? { text: b } : b;\n        });\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        var _a;\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * Only create gesture if:\n         * 1. A gesture does not already exist\n         * 2. App is running in iOS mode\n         * 3. A wrapper ref exists\n         * 4. A group ref exists\n         */\n        const { groupEl, wrapperEl } = this;\n        if (!this.gesture && getIonMode(this) === 'ios' && wrapperEl && groupEl) {\n            readTask(() => {\n                const isScrollable = groupEl.scrollHeight > groupEl.clientHeight;\n                if (!isScrollable) {\n                    this.gesture = createButtonActiveGesture(wrapperEl, (refEl) => refEl.classList.contains('action-sheet-button'));\n                    this.gesture.enable(true);\n                }\n            });\n        }\n        /**\n         * If action sheet was rendered with isOpen=\"true\"\n         * then we should open action sheet immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    render() {\n        const { header, htmlAttributes, overlayIndex } = this;\n        const mode = getIonMode(this);\n        const allButtons = this.getButtons();\n        const cancelButton = allButtons.find((b) => b.role === 'cancel');\n        const buttons = allButtons.filter((b) => b.role !== 'cancel');\n        const headerID = `action-sheet-${overlayIndex}-header`;\n        return (h(Host, Object.assign({ key: '9fef156b2a1f09ca4a6c1fe1f37c374139bde03c', role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": header !== undefined ? headerID : null, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign(Object.assign({ [mode]: true }, getClassMap(this.cssClass)), { 'overlay-hidden': true, 'action-sheet-translucent': this.translucent }), onIonActionSheetWillDismiss: this.dispatchCancelHandler, onIonBackdropTap: this.onBackdropTap }), h(\"ion-backdrop\", { key: '81cf3f7d19864e041813987b46d2d115b8466819', tappable: this.backdropDismiss }), h(\"div\", { key: '791c6a976683646fc306a42c15c5078b6f06a45f', tabindex: \"0\", \"aria-hidden\": \"true\" }), h(\"div\", { key: 'a350b489ef7852eab9dc2227ce6d92da27dd9bf9', class: \"action-sheet-wrapper ion-overlay-wrapper\", ref: (el) => (this.wrapperEl = el) }, h(\"div\", { key: '69ba51ee13510c1a411d87cb4845b11b7302a36f', class: \"action-sheet-container\" }, h(\"div\", { key: 'bded15b8306c36591e526f0f99e1eeabcbab3915', class: \"action-sheet-group\", ref: (el) => (this.groupEl = el) }, header !== undefined && (h(\"div\", { key: '06b5147c0f6d9180fe8f12e75c9b4a0310226adc', id: headerID, class: {\n                'action-sheet-title': true,\n                'action-sheet-has-sub-title': this.subHeader !== undefined,\n            } }, header, this.subHeader && h(\"div\", { key: '54874362a75c679aba803bf4f8768f5404d2dd28', class: \"action-sheet-sub-title\" }, this.subHeader))), buttons.map((b) => (h(\"button\", Object.assign({}, b.htmlAttributes, { type: \"button\", id: b.id, class: buttonClass(b), onClick: () => this.buttonClick(b), disabled: b.disabled }), h(\"span\", { class: \"action-sheet-button-inner\" }, b.icon && h(\"ion-icon\", { icon: b.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" }), b.text), mode === 'md' && h(\"ion-ripple-effect\", null))))), cancelButton && (h(\"div\", { key: '67b0de298eb424f3dea846a841b7a06d70e3930d', class: \"action-sheet-group action-sheet-group-cancel\" }, h(\"button\", Object.assign({ key: 'e7e3f9a5495eea9b97dbf885ef36944f2e420eff' }, cancelButton.htmlAttributes, { type: \"button\", class: buttonClass(cancelButton), onClick: () => this.buttonClick(cancelButton) }), h(\"span\", { key: 'f889d29ed6c3d14bbc1d805888351d87f5122377', class: \"action-sheet-button-inner\" }, cancelButton.icon && (h(\"ion-icon\", { key: '7c05cf424b38c37fd40aaeb42a494387291571fb', icon: cancelButton.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" })), cancelButton.text), mode === 'md' && h(\"ion-ripple-effect\", { key: 'bed927b477dc2708a5123ef560274fca9819b3d6' })))))), h(\"div\", { key: 'c5df1b11dc15a93892d57065d3dd5fbe02e43b39', tabindex: \"0\", \"aria-hidden\": \"true\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'action-sheet-button': true, 'ion-activatable': !button.disabled, 'ion-focusable': !button.disabled, [`action-sheet-${button.role}`]: button.role !== undefined }, getClassMap(button.cssClass));\n};\nActionSheet.style = {\n    ios: actionSheetIosCss,\n    md: actionSheetMdCss\n};\n\nexport { ActionSheet as ion_action_sheet };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "f", "readTask", "h", "j", "Host", "k", "getElement", "c", "createButtonActiveGesture", "raf", "createLockController", "createDelegateController", "createTriggerController", "B", "BACKDROP", "i", "isCancel", "present", "g", "dismiss", "eventMethod", "s", "safeCall", "prepareOverlay", "setOverlayId", "getClassMap", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "actionSheetIosCss", "actionSheetMdCss", "ActionSheet", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "presented", "hasController", "keyboardClose", "buttons", "<PERSON><PERSON><PERSON><PERSON>", "translucent", "animated", "isOpen", "onBackdropTap", "undefined", "dispatchCancelHandler", "ev", "role", "detail", "cancelButton", "getButtons", "find", "b", "callButtonHandler", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "trigger", "el", "addClickListener", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "data", "_this2", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "buttonClick", "button", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "rtn", "handler", "map", "text", "connectedCallback", "disconnectedCallback", "gesture", "destroy", "removeClickListener", "componentWillLoad", "_a", "htmlAttributes", "id", "componentDidLoad", "groupEl", "wrapperEl", "isScrollable", "scrollHeight", "clientHeight", "refEl", "classList", "contains", "enable", "render", "header", "overlayIndex", "mode", "allButtons", "filter", "headerID", "Object", "assign", "key", "tabindex", "style", "zIndex", "class", "cssClass", "onIonActionSheetWillDismiss", "onIonBackdropTap", "tappable", "ref", "subHeader", "type", "buttonClass", "onClick", "disabled", "icon", "lazy", "watchers", "ios", "md", "ion_action_sheet"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}