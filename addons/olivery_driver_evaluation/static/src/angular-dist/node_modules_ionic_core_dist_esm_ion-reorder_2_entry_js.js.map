{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8H;AACzC;AACG;AACvC;AACsE;AACtF;AACJ;AAE7B,MAAMuB,aAAa,GAAG,iLAAiL;AAEvM,MAAMC,YAAY,GAAG,kLAAkL;AAEvM,MAAMC,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB1B,qDAAgB,CAAC,IAAI,EAAE0B,OAAO,CAAC;EACnC;EACAC,OAAOA,CAACC,EAAE,EAAE;IACR,MAAMC,YAAY,GAAG,IAAI,CAACC,EAAE,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACzDH,EAAE,CAACI,cAAc,CAAC,CAAC;IACnB;IACA;IACA,IAAI,CAACH,YAAY,IAAI,CAACA,YAAY,CAACI,QAAQ,EAAE;MACzCL,EAAE,CAACM,wBAAwB,CAAC,CAAC;IACjC;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGlC,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmC,WAAW,GAAGD,IAAI,KAAK,KAAK,GAAG1B,iDAAmB,GAAGC,iDAAe;IAC1E,OAAQR,qDAAC,CAACE,iDAAI,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEH;IAAK,CAAC,EAAEjC,qDAAC,CAAC,MAAM,EAAE;MAAEmC,GAAG,EAAE;IAA2C,CAAC,EAAEnC,qDAAC,CAAC,UAAU,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAEE,IAAI,EAAEH,WAAW;MAAEI,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE,cAAc;MAAEG,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC;EACrT;EACA,IAAIZ,EAAEA,CAAA,EAAG;IAAE,OAAOvB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDiB,OAAO,CAACmB,KAAK,GAAG;EACZC,GAAG,EAAEtB,aAAa;EAClBuB,EAAE,EAAEtB;AACR,CAAC;AAED,MAAMuB,eAAe,GAAG,m2BAAm2B;AAE33B,MAAMC,YAAY,GAAG,MAAM;EACvBtB,WAAWA,CAACC,OAAO,EAAE;IACjB1B,qDAAgB,CAAC,IAAI,EAAE0B,OAAO,CAAC;IAC/B,IAAI,CAACsB,cAAc,GAAGvC,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACwC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACvB,QAAQ,GAAG,IAAI;EACxB;EACAwB,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAAC1B,QAAQ,CAAC;IACvC;EACJ;EACM2B,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MACtB,MAAMC,SAAS,GAAGlD,qDAAqB,CAACgD,KAAI,CAAC/B,EAAE,CAAC;MAChD,IAAIiC,SAAS,EAAE;QACXF,KAAI,CAACG,QAAQ,SAASjD,qDAAgB,CAACgD,SAAS,CAAC;MACrD;MACAF,KAAI,CAACH,OAAO,GAAG,OAAO,qHAA6B,EAAEO,aAAa,CAAC;QAC/DnC,EAAE,EAAE+B,KAAI,CAAC/B,EAAE;QACXoC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAGC,MAAM,IAAKX,KAAI,CAACU,QAAQ,CAACC,MAAM,CAAC;QAC3CC,OAAO,EAAG7C,EAAE,IAAKiC,KAAI,CAACY,OAAO,CAAC7C,EAAE,CAAC;QACjC8C,MAAM,EAAG9C,EAAE,IAAKiC,KAAI,CAACa,MAAM,CAAC9C,EAAE,CAAC;QAC/B+C,KAAK,EAAEA,CAAA,KAAMd,KAAI,CAACc,KAAK,CAAC;MAC5B,CAAC,CAAC;MACFd,KAAI,CAACJ,eAAe,CAAC,CAAC;IAAC;EAC3B;EACAmB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAACjB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACmB,OAAO,CAAC,CAAC;MACtB,IAAI,CAACnB,OAAO,GAAGoB,SAAS;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAACC,aAAa,EAAE;IACpB,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACC,eAAe,CAACH,aAAa,CAAC,CAAC;EAC/D;EACAT,QAAQA,CAAC3C,EAAE,EAAE;IACT,IAAI,IAAI,CAACwD,cAAc,IAAI,IAAI,CAAC5B,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACtE,OAAO,KAAK;IAChB;IACA,MAAM6B,MAAM,GAAGzD,EAAE,CAAC0D,KAAK,CAACD,MAAM;IAC9B,MAAME,SAAS,GAAGF,MAAM,CAACtD,OAAO,CAAC,aAAa,CAAC;IAC/C,IAAI,CAACwD,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,MAAMC,IAAI,GAAGC,eAAe,CAACF,SAAS,EAAE,IAAI,CAACzD,EAAE,CAAC;IAChD,IAAI,CAAC0D,IAAI,EAAE;MACP,OAAO,KAAK;IAChB;IACA5D,EAAE,CAAC8D,IAAI,GAAGF,IAAI;IACd,OAAO,IAAI;EACf;EACAf,OAAOA,CAAC7C,EAAE,EAAE;IACRA,EAAE,CAAC0D,KAAK,CAACtD,cAAc,CAAC,CAAC;IACzB,MAAMwD,IAAI,GAAI,IAAI,CAACJ,cAAc,GAAGxD,EAAE,CAAC8D,IAAK;IAC5C,MAAMC,OAAO,GAAG,IAAI,CAACzC,aAAa;IAClCyC,OAAO,CAACC,MAAM,GAAG,CAAC;IAClB,MAAM9D,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAM+D,QAAQ,GAAG/D,EAAE,CAAC+D,QAAQ;IAC5B,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACD,MAAM,KAAK,CAAC,EAAE;MACpC;IACJ;IACA,IAAIE,GAAG,GAAG,CAAC;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;MACtC,MAAMC,KAAK,GAAGH,QAAQ,CAACE,CAAC,CAAC;MACzBD,GAAG,IAAIE,KAAK,CAACC,YAAY;MACzBN,OAAO,CAACO,IAAI,CAACJ,GAAG,CAAC;MACjBE,KAAK,CAACG,SAAS,GAAGJ,CAAC;IACvB;IACA,MAAMK,GAAG,GAAGtE,EAAE,CAACuE,qBAAqB,CAAC,CAAC;IACtC,IAAI,CAAC/C,YAAY,GAAG8C,GAAG,CAACE,GAAG;IAC3B,IAAI,CAAC/C,eAAe,GAAG6C,GAAG,CAACG,MAAM;IACjC,IAAI,IAAI,CAACvC,QAAQ,EAAE;MACf,MAAMwC,SAAS,GAAG,IAAI,CAACxC,QAAQ,CAACqC,qBAAqB,CAAC,CAAC;MACvD,IAAI,CAAChD,eAAe,GAAG,IAAI,CAACW,QAAQ,CAACyC,SAAS;MAC9C,IAAI,CAACtD,WAAW,GAAGqD,SAAS,CAACF,GAAG,GAAGI,kBAAkB;MACrD,IAAI,CAACtD,cAAc,GAAGoD,SAAS,CAACD,MAAM,GAAGG,kBAAkB;IAC/D,CAAC,MACI;MACD,IAAI,CAACrD,eAAe,GAAG,CAAC;MACxB,IAAI,CAACF,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,cAAc,GAAG,CAAC;IAC3B;IACA,IAAI,CAACH,WAAW,GAAG0D,YAAY,CAACnB,IAAI,CAAC;IACrC,IAAI,CAACoB,kBAAkB,GAAGpB,IAAI,CAACS,YAAY;IAC3C,IAAI,CAACzC,KAAK,GAAG,CAAC,CAAC;IACfgC,IAAI,CAACqB,SAAS,CAACC,GAAG,CAACC,qBAAqB,CAAC;IACzC7F,sDAAoB,CAAC,CAAC;EAC1B;EACAwD,MAAMA,CAAC9C,EAAE,EAAE;IACP,MAAMoF,YAAY,GAAG,IAAI,CAAC5B,cAAc;IACxC,IAAI,CAAC4B,YAAY,EAAE;MACf;IACJ;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACC,UAAU,CAACtF,EAAE,CAACuF,QAAQ,CAAC;IAC3C;IACA,MAAMb,GAAG,GAAG,IAAI,CAAChD,YAAY,GAAG2D,MAAM;IACtC,MAAMV,MAAM,GAAG,IAAI,CAAChD,eAAe,GAAG0D,MAAM;IAC5C,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACf,GAAG,EAAEc,IAAI,CAACE,GAAG,CAAC1F,EAAE,CAACuF,QAAQ,EAAEZ,MAAM,CAAC,CAAC;IAC7D,MAAMgB,MAAM,GAAGN,MAAM,GAAGE,QAAQ,GAAGvF,EAAE,CAAC4F,MAAM;IAC5C,MAAMC,WAAW,GAAGN,QAAQ,GAAGb,GAAG;IAClC,MAAMoB,OAAO,GAAG,IAAI,CAACC,eAAe,CAACF,WAAW,CAAC;IACjD,IAAIC,OAAO,KAAK,IAAI,CAACzE,WAAW,EAAE;MAC9B,MAAM2E,SAAS,GAAGjB,YAAY,CAACK,YAAY,CAAC;MAC5C,IAAI,CAAC/D,WAAW,GAAGyE,OAAO;MAC1BtG,sDAAsB,CAAC,CAAC;MACxB,IAAI,CAACyG,WAAW,CAACD,SAAS,EAAEF,OAAO,CAAC;IACxC;IACA;IACAV,YAAY,CAACrE,KAAK,CAACmF,SAAS,GAAG,cAAcP,MAAM,KAAK;EAC5D;EACA5C,KAAKA,CAAA,EAAG;IACJ,MAAMS,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC4B,cAAc,EAAE;MACjB,IAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;MACf;IACJ;IACA,MAAMkE,OAAO,GAAG,IAAI,CAACzE,WAAW;IAChC,MAAM2E,SAAS,GAAGjB,YAAY,CAACvB,cAAc,CAAC;IAC9C,IAAIsC,OAAO,KAAKE,SAAS,EAAE;MACvB,IAAI,CAACzC,eAAe,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACnC,cAAc,CAAC+E,IAAI,CAAC;QACrBC,IAAI,EAAEJ,SAAS;QACfK,EAAE,EAAEP,OAAO;QACX3C,QAAQ,EAAE,IAAI,CAACI,eAAe,CAAC+C,IAAI,CAAC,IAAI;MAC5C,CAAC,CAAC;IACN;IACA7G,sDAAkB,CAAC,CAAC;EACxB;EACA8D,eAAeA,CAACH,aAAa,EAAE;IAC3B,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAIA,cAAc,IAAI,IAAI,CAAC5B,KAAK,KAAK,CAAC,CAAC,kCAAkC;MACrE,MAAMqC,QAAQ,GAAG,IAAI,CAAC/D,EAAE,CAAC+D,QAAQ;MACjC,MAAMsC,GAAG,GAAGtC,QAAQ,CAACD,MAAM;MAC3B,MAAM8B,OAAO,GAAG,IAAI,CAACzE,WAAW;MAChC,MAAM2E,SAAS,GAAGjB,YAAY,CAACvB,cAAc,CAAC;MAC9C;AACZ;AACA;AACA;AACA;AACA;AACA;MACYpE,uDAAG,CAAC,MAAM;QACN,IAAI0G,OAAO,KAAKE,SAAS,KAAK5C,aAAa,KAAKF,SAAS,IAAIE,aAAa,KAAK,IAAI,CAAC,EAAE;UAClF,MAAMoD,GAAG,GAAGR,SAAS,GAAGF,OAAO,GAAG7B,QAAQ,CAAC6B,OAAO,GAAG,CAAC,CAAC,GAAG7B,QAAQ,CAAC6B,OAAO,CAAC;UAC3E,IAAI,CAAC5F,EAAE,CAACuG,YAAY,CAACjD,cAAc,EAAEgD,GAAG,CAAC;QAC7C;QACA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;UAC1BF,QAAQ,CAACE,CAAC,CAAC,CAACpD,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QACvC;MACJ,CAAC,CAAC;MACF,IAAI2F,KAAK,CAACC,OAAO,CAACvD,aAAa,CAAC,EAAE;QAC9BA,aAAa,GAAGwD,YAAY,CAACxD,aAAa,EAAE4C,SAAS,EAAEF,OAAO,CAAC;MACnE;MACAtC,cAAc,CAACzC,KAAK,CAAC8F,UAAU,GAAG,EAAE;MACpCrD,cAAc,CAACyB,SAAS,CAAC6B,MAAM,CAAC3B,qBAAqB,CAAC;MACtD,IAAI,CAAC3B,cAAc,GAAGN,SAAS;MAC/B,IAAI,CAACtB,KAAK,GAAG,CAAC,CAAC;IACnB;IACA,OAAOwB,aAAa;EACxB;EACA2C,eAAeA,CAACJ,MAAM,EAAE;IACpB,MAAM5B,OAAO,GAAG,IAAI,CAACzC,aAAa;IAClC,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;MACrC,IAAIJ,OAAO,CAACI,CAAC,CAAC,GAAGwB,MAAM,EAAE;QACrB,OAAOxB,CAAC;MACZ;IACJ;IACA,OAAOJ,OAAO,CAACC,MAAM,GAAG,CAAC;EAC7B;EACA;EACAiC,WAAWA,CAACD,SAAS,EAAEF,OAAO,EAAE;IAC5B,MAAMiB,UAAU,GAAG,IAAI,CAAC/B,kBAAkB;IAC1C,MAAMf,QAAQ,GAAG,IAAI,CAAC/D,EAAE,CAAC+D,QAAQ;IACjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;MACtC,MAAMpD,KAAK,GAAGkD,QAAQ,CAACE,CAAC,CAAC,CAACpD,KAAK;MAC/B,IAAIiG,KAAK,GAAG,EAAE;MACd,IAAI7C,CAAC,GAAG6B,SAAS,IAAI7B,CAAC,IAAI2B,OAAO,EAAE;QAC/BkB,KAAK,GAAG,cAAc,CAACD,UAAU,KAAK;MAC1C,CAAC,MACI,IAAI5C,CAAC,GAAG6B,SAAS,IAAI7B,CAAC,IAAI2B,OAAO,EAAE;QACpCkB,KAAK,GAAG,cAAcD,UAAU,KAAK;MACzC;MACAhG,KAAK,CAAC,WAAW,CAAC,GAAGiG,KAAK;IAC9B;EACJ;EACA1B,UAAUA,CAAC2B,IAAI,EAAE;IACb,IAAI,CAAC,IAAI,CAAC7E,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,IAAI8E,MAAM,GAAG,CAAC;IACd,IAAID,IAAI,GAAG,IAAI,CAAC1F,WAAW,EAAE;MACzB2F,MAAM,GAAG,CAAC,EAAE;IAChB,CAAC,MACI,IAAID,IAAI,GAAG,IAAI,CAACzF,cAAc,EAAE;MACjC0F,MAAM,GAAGC,WAAW;IACxB;IACA,IAAID,MAAM,KAAK,CAAC,EAAE;MACd,IAAI,CAAC9E,QAAQ,CAACgF,QAAQ,CAAC,CAAC,EAAEF,MAAM,CAAC;IACrC;IACA,OAAO,IAAI,CAAC9E,QAAQ,CAACyC,SAAS,GAAG,IAAI,CAACpD,eAAe;EACzD;EACAlB,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGlC,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACH,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE,CAAC,IAAI,CAACH,QAAQ;QACjC,qBAAqB,EAAE,IAAI,CAACuB,KAAK,KAAK,CAAC,CAAC;MAC5C;IAAE,CAAC,CAAC;EACZ;EACA,IAAI1B,EAAEA,CAAA,EAAG;IAAE,OAAOvB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0I,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,MAAMtC,YAAY,GAAIuC,OAAO,IAAK;EAC9B,OAAOA,OAAO,CAAC,WAAW,CAAC;AAC/B,CAAC;AACD,MAAMzD,eAAe,GAAGA,CAAC0D,IAAI,EAAEC,SAAS,KAAK;EACzC,IAAIC,MAAM;EACV,OAAOF,IAAI,EAAE;IACTE,MAAM,GAAGF,IAAI,CAACG,aAAa;IAC3B,IAAID,MAAM,KAAKD,SAAS,EAAE;MACtB,OAAOD,IAAI;IACf;IACAA,IAAI,GAAGE,MAAM;EACjB;EACA,OAAOvE,SAAS;AACpB,CAAC;AACD,MAAM4B,kBAAkB,GAAG,EAAE;AAC7B,MAAMqC,WAAW,GAAG,EAAE;AACtB,MAAMhC,qBAAqB,GAAG,kBAAkB;AAChD,MAAMyB,YAAY,GAAGA,CAACe,KAAK,EAAEvB,IAAI,EAAEC,EAAE,KAAK;EACtC,MAAMiB,OAAO,GAAGK,KAAK,CAACvB,IAAI,CAAC;EAC3BuB,KAAK,CAACC,MAAM,CAACxB,IAAI,EAAE,CAAC,CAAC;EACrBuB,KAAK,CAACC,MAAM,CAACvB,EAAE,EAAE,CAAC,EAAEiB,OAAO,CAAC;EAC5B,OAAOK,KAAK,CAACE,KAAK,CAAC,CAAC;AACxB,CAAC;AACD1G,YAAY,CAACJ,KAAK,GAAGG,eAAe", "sources": ["./node_modules/@ionic/core/dist/esm/ion-reorder_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement, d as createEvent } from './index-B_U9CtaY.js';\nimport { j as reorderThreeOutline, k as reorderTwoSharp } from './index-BLV6ykCk.js';\nimport { f as findClosestIonContent, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { b as hapticSelectionStart, a as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\n\nconst reorderIosCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:2.125rem;opacity:0.4}\";\n\nconst reorderMdCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:1.9375rem;opacity:0.3}\";\n\nconst Reorder = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    onClick(ev) {\n        const reorderGroup = this.el.closest('ion-reorder-group');\n        ev.preventDefault();\n        // Only stop event propagation if the reorder is inside of an enabled\n        // reorder group. This allows interaction with clickable children components.\n        if (!reorderGroup || !reorderGroup.disabled) {\n            ev.stopImmediatePropagation();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const reorderIcon = mode === 'ios' ? reorderThreeOutline : reorderTwoSharp;\n        return (h(Host, { key: 'e6807bb349725682e99e791ac65e729a360d64e8', class: mode }, h(\"slot\", { key: '1c691cdbffa6427ba08dc12184c69559ed5d5506' }, h(\"ion-icon\", { key: '8b4150302cdca475379582b2251737b5e74079b1', icon: reorderIcon, lazy: false, class: \"reorder-icon\", part: \"icon\", \"aria-hidden\": \"true\" }))));\n    }\n    get el() { return getElement(this); }\n};\nReorder.style = {\n    ios: reorderIosCss,\n    md: reorderMdCss\n};\n\nconst reorderGroupCss = \".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}\";\n\nconst ReorderGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionItemReorder = createEvent(this, \"ionItemReorder\", 7);\n        this.lastToIndex = -1;\n        this.cachedHeights = [];\n        this.scrollElTop = 0;\n        this.scrollElBottom = 0;\n        this.scrollElInitial = 0;\n        this.containerTop = 0;\n        this.containerBottom = 0;\n        this.state = 0 /* ReorderGroupState.Idle */;\n        /**\n         * If `true`, the reorder will be hidden.\n         */\n        this.disabled = true;\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    async connectedCallback() {\n        const contentEl = findClosestIonContent(this.el);\n        if (contentEl) {\n            this.scrollEl = await getScrollElement(contentEl);\n        }\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el: this.el,\n            gestureName: 'reorder',\n            gesturePriority: 110,\n            threshold: 0,\n            direction: 'y',\n            passive: false,\n            canStart: (detail) => this.canStart(detail),\n            onStart: (ev) => this.onStart(ev),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: () => this.onEnd(),\n        });\n        this.disabledChanged();\n    }\n    disconnectedCallback() {\n        this.onEnd();\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /**\n     * Completes the reorder operation. Must be called by the `ionItemReorder` event.\n     * If a list of items is passed, the list will be reordered and returned in the\n     * proper order.\n     *\n     * If no parameters are passed or if `true` is passed in, the reorder will complete\n     * and the item will remain in the position it was dragged to. If `false` is passed,\n     * the reorder will complete and the item will bounce back to its original position.\n     *\n     * @param listOrReorder A list of items to be sorted and returned in the new order or a\n     * boolean of whether or not the reorder should reposition the item.\n     */\n    complete(listOrReorder) {\n        return Promise.resolve(this.completeReorder(listOrReorder));\n    }\n    canStart(ev) {\n        if (this.selectedItemEl || this.state !== 0 /* ReorderGroupState.Idle */) {\n            return false;\n        }\n        const target = ev.event.target;\n        const reorderEl = target.closest('ion-reorder');\n        if (!reorderEl) {\n            return false;\n        }\n        const item = findReorderItem(reorderEl, this.el);\n        if (!item) {\n            return false;\n        }\n        ev.data = item;\n        return true;\n    }\n    onStart(ev) {\n        ev.event.preventDefault();\n        const item = (this.selectedItemEl = ev.data);\n        const heights = this.cachedHeights;\n        heights.length = 0;\n        const el = this.el;\n        const children = el.children;\n        if (!children || children.length === 0) {\n            return;\n        }\n        let sum = 0;\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            sum += child.offsetHeight;\n            heights.push(sum);\n            child.$ionIndex = i;\n        }\n        const box = el.getBoundingClientRect();\n        this.containerTop = box.top;\n        this.containerBottom = box.bottom;\n        if (this.scrollEl) {\n            const scrollBox = this.scrollEl.getBoundingClientRect();\n            this.scrollElInitial = this.scrollEl.scrollTop;\n            this.scrollElTop = scrollBox.top + AUTO_SCROLL_MARGIN;\n            this.scrollElBottom = scrollBox.bottom - AUTO_SCROLL_MARGIN;\n        }\n        else {\n            this.scrollElInitial = 0;\n            this.scrollElTop = 0;\n            this.scrollElBottom = 0;\n        }\n        this.lastToIndex = indexForItem(item);\n        this.selectedItemHeight = item.offsetHeight;\n        this.state = 1 /* ReorderGroupState.Active */;\n        item.classList.add(ITEM_REORDER_SELECTED);\n        hapticSelectionStart();\n    }\n    onMove(ev) {\n        const selectedItem = this.selectedItemEl;\n        if (!selectedItem) {\n            return;\n        }\n        // Scroll if we reach the scroll margins\n        const scroll = this.autoscroll(ev.currentY);\n        // // Get coordinate\n        const top = this.containerTop - scroll;\n        const bottom = this.containerBottom - scroll;\n        const currentY = Math.max(top, Math.min(ev.currentY, bottom));\n        const deltaY = scroll + currentY - ev.startY;\n        const normalizedY = currentY - top;\n        const toIndex = this.itemIndexForTop(normalizedY);\n        if (toIndex !== this.lastToIndex) {\n            const fromIndex = indexForItem(selectedItem);\n            this.lastToIndex = toIndex;\n            hapticSelectionChanged();\n            this.reorderMove(fromIndex, toIndex);\n        }\n        // Update selected item position\n        selectedItem.style.transform = `translateY(${deltaY}px)`;\n    }\n    onEnd() {\n        const selectedItemEl = this.selectedItemEl;\n        this.state = 2 /* ReorderGroupState.Complete */;\n        if (!selectedItemEl) {\n            this.state = 0 /* ReorderGroupState.Idle */;\n            return;\n        }\n        const toIndex = this.lastToIndex;\n        const fromIndex = indexForItem(selectedItemEl);\n        if (toIndex === fromIndex) {\n            this.completeReorder();\n        }\n        else {\n            this.ionItemReorder.emit({\n                from: fromIndex,\n                to: toIndex,\n                complete: this.completeReorder.bind(this),\n            });\n        }\n        hapticSelectionEnd();\n    }\n    completeReorder(listOrReorder) {\n        const selectedItemEl = this.selectedItemEl;\n        if (selectedItemEl && this.state === 2 /* ReorderGroupState.Complete */) {\n            const children = this.el.children;\n            const len = children.length;\n            const toIndex = this.lastToIndex;\n            const fromIndex = indexForItem(selectedItemEl);\n            /**\n             * insertBefore and setting the transform\n             * needs to happen in the same frame otherwise\n             * there will be a duplicate transition. This primarily\n             * impacts Firefox where insertBefore and transform operations\n             * are happening in two separate frames.\n             */\n            raf(() => {\n                if (toIndex !== fromIndex && (listOrReorder === undefined || listOrReorder === true)) {\n                    const ref = fromIndex < toIndex ? children[toIndex + 1] : children[toIndex];\n                    this.el.insertBefore(selectedItemEl, ref);\n                }\n                for (let i = 0; i < len; i++) {\n                    children[i].style['transform'] = '';\n                }\n            });\n            if (Array.isArray(listOrReorder)) {\n                listOrReorder = reorderArray(listOrReorder, fromIndex, toIndex);\n            }\n            selectedItemEl.style.transition = '';\n            selectedItemEl.classList.remove(ITEM_REORDER_SELECTED);\n            this.selectedItemEl = undefined;\n            this.state = 0 /* ReorderGroupState.Idle */;\n        }\n        return listOrReorder;\n    }\n    itemIndexForTop(deltaY) {\n        const heights = this.cachedHeights;\n        for (let i = 0; i < heights.length; i++) {\n            if (heights[i] > deltaY) {\n                return i;\n            }\n        }\n        return heights.length - 1;\n    }\n    /********* DOM WRITE ********* */\n    reorderMove(fromIndex, toIndex) {\n        const itemHeight = this.selectedItemHeight;\n        const children = this.el.children;\n        for (let i = 0; i < children.length; i++) {\n            const style = children[i].style;\n            let value = '';\n            if (i > fromIndex && i <= toIndex) {\n                value = `translateY(${-itemHeight}px)`;\n            }\n            else if (i < fromIndex && i >= toIndex) {\n                value = `translateY(${itemHeight}px)`;\n            }\n            style['transform'] = value;\n        }\n    }\n    autoscroll(posY) {\n        if (!this.scrollEl) {\n            return 0;\n        }\n        let amount = 0;\n        if (posY < this.scrollElTop) {\n            amount = -10;\n        }\n        else if (posY > this.scrollElBottom) {\n            amount = SCROLL_JUMP;\n        }\n        if (amount !== 0) {\n            this.scrollEl.scrollBy(0, amount);\n        }\n        return this.scrollEl.scrollTop - this.scrollElInitial;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'dfcdc3a6aa1b2fba15f861ec868d6a11e667c9de', class: {\n                [mode]: true,\n                'reorder-enabled': !this.disabled,\n                'reorder-list-active': this.state !== 0 /* ReorderGroupState.Idle */,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst indexForItem = (element) => {\n    return element['$ionIndex'];\n};\nconst findReorderItem = (node, container) => {\n    let parent;\n    while (node) {\n        parent = node.parentElement;\n        if (parent === container) {\n            return node;\n        }\n        node = parent;\n    }\n    return undefined;\n};\nconst AUTO_SCROLL_MARGIN = 60;\nconst SCROLL_JUMP = 10;\nconst ITEM_REORDER_SELECTED = 'reorder-selected';\nconst reorderArray = (array, from, to) => {\n    const element = array[from];\n    array.splice(from, 1);\n    array.splice(to, 0, element);\n    return array.slice();\n};\nReorderGroup.style = reorderGroupCss;\n\nexport { Reorder as ion_reorder, ReorderGroup as ion_reorder_group };\n"], "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "d", "createEvent", "reorderThreeOutline", "reorderTwoSharp", "f", "findClosestIonContent", "g", "getScrollElement", "raf", "b", "hapticSelectionStart", "a", "hapticSelectionChanged", "hapticSelectionEnd", "reorderIosCss", "reorderMdCss", "Reorder", "constructor", "hostRef", "onClick", "ev", "reorderGroup", "el", "closest", "preventDefault", "disabled", "stopImmediatePropagation", "render", "mode", "reorderIcon", "key", "class", "icon", "lazy", "part", "style", "ios", "md", "reorderGroupCss", "ReorderGroup", "ionItemReorder", "lastToIndex", "cachedHeights", "scrollElTop", "scrollElBottom", "scrollElInitial", "containerTop", "containerBottom", "state", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "_this", "_asyncToGenerator", "contentEl", "scrollEl", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "direction", "passive", "canStart", "detail", "onStart", "onMove", "onEnd", "disconnectedCallback", "destroy", "undefined", "complete", "listOr<PERSON>eorder", "Promise", "resolve", "completeReorder", "selectedItemEl", "target", "event", "reorderEl", "item", "findReorderItem", "data", "heights", "length", "children", "sum", "i", "child", "offsetHeight", "push", "$ionIndex", "box", "getBoundingClientRect", "top", "bottom", "scrollBox", "scrollTop", "AUTO_SCROLL_MARGIN", "indexForItem", "selectedItemHeight", "classList", "add", "ITEM_REORDER_SELECTED", "selectedItem", "scroll", "autoscroll", "currentY", "Math", "max", "min", "deltaY", "startY", "normalizedY", "toIndex", "itemIndexForTop", "fromIndex", "reorderMove", "transform", "emit", "from", "to", "bind", "len", "ref", "insertBefore", "Array", "isArray", "reorderArray", "transition", "remove", "itemHeight", "value", "posY", "amount", "SCROLL_JUMP", "scrollBy", "watchers", "element", "node", "container", "parent", "parentElement", "array", "splice", "slice", "ion_reorder", "ion_reorder_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}