{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-textarea_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACgK;AACpF;AAC0D;AACnC;AACnB;AACnD;AAE7B,MAAM4B,cAAc,GAAG,y6cAAy6c;AAEh8c,MAAMC,aAAa,GAAG,oswBAAoswB;AAE1twB,MAAMC,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjB/B,qDAAgB,CAAC,IAAI,EAAE+B,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG9B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC+B,QAAQ,GAAG/B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACgC,OAAO,GAAGhC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACiC,QAAQ,GAAGjC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkC,OAAO,GAAG,gBAAgBC,WAAW,EAAE,EAAE;IAC9C,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACF,OAAO,cAAc;IACjD,IAAI,CAACG,WAAW,GAAG,GAAG,IAAI,CAACH,OAAO,aAAa;IAC/C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACI,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,MAAM;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACX,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACY,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACL,KAAK,GAAGK,KAAK,CAACL,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACO,eAAe,CAACH,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACI,QAAQ,GAAIJ,EAAE,IAAK;MACpB,IAAI,CAACK,eAAe,CAACL,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACM,OAAO,GAAIN,EAAE,IAAK;MACnB,IAAI,CAACd,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACqB,YAAY,GAAG,IAAI,CAACX,KAAK;MAC9B,IAAI,CAACjB,QAAQ,CAAC6B,IAAI,CAACR,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACS,MAAM,GAAIT,EAAE,IAAK;MAClB,IAAI,CAACd,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACqB,YAAY,KAAK,IAAI,CAACX,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACS,eAAe,CAACL,EAAE,CAAC;MAC5B;MACA,IAAI,CAAChB,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACN,OAAO,CAAC8B,IAAI,CAACR,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACU,SAAS,GAAIV,EAAE,IAAK;MACrB,IAAI,CAACW,gBAAgB,CAACX,EAAE,CAAC;IAC7B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACY,YAAY,GAAIZ,EAAE,IAAK;MACxB;MACA;MACA,IAAIA,EAAE,CAACE,MAAM,KAAKF,EAAE,CAACa,aAAa,EAAE;QAChCb,EAAE,CAACc,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;EACL;EACAC,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEtC,QAAQ;MAAEuC,QAAQ;MAAEC;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACxC,QAAQ,GAAGuC,QAAQ,KAAKE,SAAS,GAAGD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGxC,QAAQ,GAAGjB,uDAAa,CAACiB,QAAQ,EAAEuC,QAAQ,CAAC;EACvK;EACA;AACJ;AACA;EACIG,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMxB,KAAK,GAAG,IAAI,CAACyB,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAACxB,KAAK,KAAKA,KAAK,EAAE;MAC5CwB,WAAW,CAACxB,KAAK,GAAGA,KAAK;IAC7B;IACA,IAAI,CAAC0B,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,YAAYA,CAACC,QAAQ,EAAE;IACnB,IAAI,CAACvC,mBAAmB,GAAGwC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACzC,mBAAmB,CAAC,EAAE;MAAE0C,GAAG,EAAEH;IAAS,CAAC,CAAC;IACxG5E,qDAAW,CAAC,IAAI,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIgF,cAAcA,CAAC5B,EAAE,EAAE;IACf,MAAMoB,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIA,WAAW,IAAIpB,EAAE,CAACE,MAAM,KAAKkB,WAAW,EAAE;MAC1CpB,EAAE,CAACc,eAAe,CAAC,CAAC;MACpB,IAAI,CAACe,EAAE,CAACC,KAAK,CAAC,CAAC;IACnB;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEF;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACG,sBAAsB,GAAGlE,2DAA4B,CAAC+D,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAMjF,qDAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAACqF,eAAe,GAAG1E,gEAAqB,CAACsE,EAAE,EAAE,MAAM,IAAI,CAACK,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAACpB,eAAe,CAAC,CAAC;IACtB;MACIqB,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAEV;MACZ,CAAC,CAAC,CAAC;IACP;EACJ;EACAW,oBAAoBA,CAAA,EAAG;IACnB;MACIJ,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAACV;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACS,OAAO,CAAC,CAAC;MACrC,IAAI,CAACT,sBAAsB,GAAGd,SAAS;IAC3C;IACA,IAAI,IAAI,CAACe,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACQ,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACR,eAAe,GAAGf,SAAS;IACpC;EACJ;EACAwB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACzD,mBAAmB,GAAGwC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhE,uDAAqB,CAAC,IAAI,CAACmE,EAAE,CAAC,CAAC,EAAEjE,uDAAiB,CAAC,IAAI,CAACiE,EAAE,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;EAC3K;EACAc,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACxC,QAAQ;IACrC,IAAI,CAAC6C,WAAW,CAAC,CAAC;EACtB;EACAsB,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACZ,eAAe,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MACb,IAAID,KAAI,CAAC5B,WAAW,EAAE;QAClB4B,KAAI,CAAC5B,WAAW,CAAC8B,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,yMAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAAChC,WAAW,EAAE;QACnB,MAAM,IAAIiC,OAAO,CAAEC,OAAO,IAAKzF,uDAAgB,CAACuF,MAAI,CAACvB,EAAE,EAAEyB,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAAChC,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIf,eAAeA,CAACkD,KAAK,EAAE;IACnB,MAAM;MAAE3D;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAM4B,QAAQ,GAAG5B,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAAC4D,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACjD,YAAY,GAAGiB,QAAQ;IAC5B,IAAI,CAAChD,SAAS,CAACgC,IAAI,CAAC;MAAEZ,KAAK,EAAE4B,QAAQ;MAAE+B;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIpD,eAAeA,CAACoD,KAAK,EAAE;IACnB,MAAM;MAAE3D;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACnB,QAAQ,CAAC+B,IAAI,CAAC;MAAEZ,KAAK;MAAE2D;IAAM,CAAC,CAAC;EACxC;EACAjC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACF,WAAW,IAAI,IAAI,CAACzB,QAAQ,EAAE;MACnC7C,qDAAS,CAAC,MAAM;QACZ,IAAI+F,EAAE;QACN,IAAI,IAAI,CAACY,eAAe,EAAE;UACtB;UACA;UACA,IAAI,CAACA,eAAe,CAACC,OAAO,CAACC,eAAe,GAAG,CAACd,EAAE,GAAG,IAAI,CAACjD,KAAK,MAAM,IAAI,IAAIiD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QACxG;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIlC,gBAAgBA,CAACX,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACnB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMuE,YAAY,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IAC/D,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAAC9D,EAAE,CAAC+D,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAAC/E,sBAAsB,IAAI,IAAI,CAACgF,QAAQ,CAAC,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACvE,IAAI,CAACjE,KAAK,GAAG,EAAE;MACf,IAAI,CAACO,eAAe,CAACH,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6D,iBAAiB,EAAE;MACpB,IAAI,CAAC7E,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAgF,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3C,QAAQ,CAAC,CAAC,KAAK,EAAE;EACjC;EACAA,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzB,KAAK,IAAI,EAAE;EAC3B;EACAqE,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQnH,qDAAC,CAAC,KAAK,EAAE;MAAEoH,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC;IAAE,CAAC,EAAEF,KAAK,KAAKhD,SAAS,GAAGnE,qDAAC,CAAC,MAAM,EAAE;MAAEwC,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGxC,qDAAC,CAAC,KAAK,EAAE;MAAEoH,KAAK,EAAE;IAAa,CAAC,EAAED,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAI/B,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,EAAE,CAACwC,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,KAAK,KAAKhD,SAAS,IAAI,IAAI,CAACiB,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;EACImC,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAGtH,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuH,cAAc,GAAGD,IAAI,KAAK,IAAI,IAAI,IAAI,CAACE,IAAI,KAAK,SAAS;IAC/D,IAAID,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHzH,qDAAC,CAAC,KAAK,EAAE;QAAEoH,KAAK,EAAE;MAA6B,CAAC,EAAEpH,qDAAC,CAAC,KAAK,EAAE;QAAEoH,KAAK,EAAE;MAAyB,CAAC,CAAC,EAAEpH,qDAAC,CAAC,KAAK,EAAE;QAAEoH,KAAK,EAAE;UAC3G,wBAAwB,EAAE,IAAI;UAC9B,+BAA+B,EAAE,CAAC,IAAI,CAACC;QAC3C;MAAE,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;QAAEoH,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEO,GAAG,EAAG7C,EAAE,IAAM,IAAI,CAACK,aAAa,GAAGL;MAAI,CAAC,EAAE,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAEnH,qDAAC,CAAC,KAAK,EAAE;QAAEoH,KAAK,EAAE;MAAuB,CAAC,CAAC,CAAC,EACtK,IAAI,CAACF,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIU,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,UAAU;MAAEC,SAAS;MAAE/F,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE,OAAO,CACHhC,qDAAC,CAAC,KAAK,EAAE;MAAE+H,EAAE,EAAEhG,YAAY;MAAEqF,KAAK,EAAE;IAAc,CAAC,EAAES,UAAU,CAAC,EAChE7H,qDAAC,CAAC,KAAK,EAAE;MAAE+H,EAAE,EAAE/F,WAAW;MAAEoF,KAAK,EAAE;IAAa,CAAC,EAAEU,SAAS,CAAC,CAChE;EACL;EACAE,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAElD,EAAE;MAAE+C,UAAU;MAAEC,SAAS;MAAE/F,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAI8C,EAAE,CAACmD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIpD,EAAE,CAACmD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIJ,SAAS,EAAE;MAC3F,OAAO9F,WAAW;IACtB;IACA,IAAI6F,UAAU,EAAE;MACZ,OAAO9F,YAAY;IACvB;IACA,OAAOoC,SAAS;EACpB;EACAgE,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAErF,OAAO;MAAEsF,SAAS;MAAEC,gBAAgB;MAAExF;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAIC,OAAO,KAAK,IAAI,IAAIsF,SAAS,KAAKjE,SAAS,EAAE;MAC7C;IACJ;IACA,OAAOnE,qDAAC,CAAC,KAAK,EAAE;MAAEoH,KAAK,EAAE;IAAU,CAAC,EAAEnG,2DAAc,CAAC4B,KAAK,EAAEuF,SAAS,EAAEC,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAExF,OAAO;MAAE+E,UAAU;MAAEC,SAAS;MAAEM;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAMG,WAAW,GAAG,CAAC,CAACV,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,MAAMU,UAAU,GAAG1F,OAAO,KAAK,IAAI,IAAIsF,SAAS,KAAKjE,SAAS;IAC9D,IAAI,CAACoE,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQxI,qDAAC,CAAC,KAAK,EAAE;MAAEoH,KAAK,EAAE;IAAkB,CAAC,EAAE,IAAI,CAACQ,cAAc,CAAC,CAAC,EAAE,IAAI,CAACO,aAAa,CAAC,CAAC,CAAC;EAC/F;EACAM,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE5G,OAAO;MAAEU,QAAQ;MAAEmF,IAAI;MAAEgB,KAAK;MAAE3F,cAAc;MAAE+B,EAAE;MAAE3C;IAAS,CAAC,GAAG,IAAI;IAC7E,MAAMqF,IAAI,GAAGtH,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2C,KAAK,GAAG,IAAI,CAACyB,QAAQ,CAAC,CAAC;IAC7B,MAAMqE,MAAM,GAAGzH,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC4D,EAAE,CAAC;IAC/C,MAAM8D,qBAAqB,GAAGpB,IAAI,KAAK,IAAI,IAAIE,IAAI,KAAK,SAAS,IAAI,CAACiB,MAAM;IAC5E,MAAM1B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAM4B,gBAAgB,GAAG/D,EAAE,CAACwC,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMwB,gBAAgB,GAAG/F,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKkE,QAAQ,IAAI9E,QAAQ,IAAI0G,gBAAgB,CAAE;IACtI,OAAQ7I,qDAAC,CAACI,iDAAI,EAAE;MAAE4G,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAEjG,qDAAkB,CAAC,IAAI,CAAC4H,KAAK,EAAE;QACjG,CAACvB,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEP,QAAQ;QACrB,WAAW,EAAE9E,QAAQ;QACrB,gBAAgB,EAAE2G,gBAAgB;QAClC,CAAC,iBAAiBpB,IAAI,EAAE,GAAGA,IAAI,KAAKvD,SAAS;QAC7C,CAAC,kBAAkBuE,KAAK,EAAE,GAAGA,KAAK,KAAKvE,SAAS;QAChD,CAAC,4BAA4BpB,cAAc,EAAE,GAAG,IAAI;QACpD,mBAAmB,EAAER;MACzB,CAAC;IAAE,CAAC,EAAEvC,qDAAC,CAAC,OAAO,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,kBAAkB;MAAE4B,OAAO,EAAEnH,OAAO;MAAEoH,OAAO,EAAE,IAAI,CAACpF;IAAa,CAAC,EAAE,IAAI,CAAC0D,oBAAoB,CAAC,CAAC,EAAEvH,qDAAC,CAAC,KAAK,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAyB,CAAC,EAAEpH,qDAAC,CAAC,KAAK,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAqB,CAAC,EAAEpH,qDAAC,CAAC,MAAM,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAExE,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAExC,qDAAC,CAAC,KAAK,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,gBAAgB;MAAEO,GAAG,EAAG7C,EAAE,IAAM,IAAI,CAAC4B,eAAe,GAAG5B;IAAI,CAAC,EAAE9E,qDAAC,CAAC,UAAU,EAAE0E,MAAM,CAACC,MAAM,CAAC;MAAEqC,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,iBAAiB;MAAEO,GAAG,EAAG7C,EAAE,IAAM,IAAI,CAACT,WAAW,GAAGS,EAAG;MAAEiD,EAAE,EAAElG,OAAO;MAAEU,QAAQ,EAAEA,QAAQ;MAAE2G,cAAc,EAAE,IAAI,CAAC9G,cAAc;MAAE+G,SAAS,EAAE,IAAI,CAAC9G,SAAS;MAAE+G,YAAY,EAAE,IAAI,CAACC,YAAY;MAAEC,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACtB,SAAS;MAAE5F,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEmH,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAEC,QAAQ,EAAE,IAAI,CAACnH,QAAQ;MAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEkH,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAE/G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEK,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEI,SAAS,EAAE,IAAI,CAACA,SAAS;MAAE,kBAAkB,EAAE,IAAI,CAACqE,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAK,IAAI,CAAChG;IAAY,CAAC,EAAE,IAAI,CAACE,mBAAmB,CAAC,EAAEW,KAAK,CAAC,CAAC,EAAE7C,qDAAC,CAAC,KAAK,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAmB,CAAC,EAAEpH,qDAAC,CAAC,MAAM,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAExE,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,EAAEoG,qBAAqB,IAAI5I,qDAAC,CAAC,KAAK,EAAE;MAAEgH,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAqB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkB,mBAAmB,CAAC,CAAC,CAAC;EACppD;EACA,IAAIxD,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0J,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,KAAK,EAAE,CAAC,cAAc;IAC1B,CAAC;EAAE;AACP,CAAC;AACD,IAAIlI,WAAW,GAAG,CAAC;AACnBR,QAAQ,CAAC2I,KAAK,GAAG;EACbC,GAAG,EAAE9I,cAAc;EACnB+I,EAAE,EAAE9I;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-textarea.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, n as forceUpdate, w as writeTask, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { d as debounceEvent, i as inheritAriaAttributes, b as inheritAttributes, c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-zWijNCrx.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport './index-ZjP4CjeZ.js';\n\nconst textareaIosCss = \".sc-ion-textarea-ios-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0;--padding-end:0;--padding-bottom:8px;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.textarea-label-placement-floating.sc-ion-textarea-ios-h,.textarea-label-placement-stacked.sc-ion-textarea-ios-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-ios-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.ion-color.sc-ion-textarea-ios-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-ios-h,ion-item .sc-ion-textarea-ios-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item[slot=start].sc-ion-textarea-ios-h,ion-item [slot=start].sc-ion-textarea-ios-h,ion-item[slot=end].sc-ion-textarea-ios-h,ion-item [slot=end].sc-ion-textarea-ios-h{width:auto}.native-textarea.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-ios::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-textarea-ios{inset-inline-start:0}.cloned-input.sc-ion-textarea-ios:disabled{opacity:1}[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{height:100%}[auto-grow].sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{overflow:hidden}.textarea-wrapper.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-ios{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-ios{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-ios::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-ios::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-ios{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-textarea-ios-h,.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:block}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:none}.textarea-bottom.sc-ion-textarea-ios .counter.sc-ion-textarea-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-ios,.sc-ion-textarea-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-ios,.textarea-outline-notch-hidden.sc-ion-textarea-ios{display:none}.textarea-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text.sc-ion-textarea-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.has-value.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:1}.label-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-ios,.end-slot-wrapper.sc-ion-textarea-ios{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-s>[slot=end]{margin-top:0}.sc-ion-textarea-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-textarea-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--padding-top:10px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;--highlight-height:0px;font-size:inherit}.textarea-disabled.sc-ion-textarea-ios-h{opacity:0.3}.sc-ion-textarea-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\n\nconst textareaMdCss = \".sc-ion-textarea-md-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0;--padding-end:0;--padding-bottom:8px;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.textarea-label-placement-floating.sc-ion-textarea-md-h,.textarea-label-placement-stacked.sc-ion-textarea-md-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-md-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.ion-color.sc-ion-textarea-md-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-md-h,ion-item .sc-ion-textarea-md-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item[slot=start].sc-ion-textarea-md-h,ion-item [slot=start].sc-ion-textarea-md-h,ion-item[slot=end].sc-ion-textarea-md-h,ion-item [slot=end].sc-ion-textarea-md-h{width:auto}.native-textarea.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-md::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-textarea-md{inset-inline-start:0}.cloned-input.sc-ion-textarea-md:disabled{opacity:1}[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{height:100%}[auto-grow].sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{overflow:hidden}.textarea-wrapper.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-md{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-md{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-md::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-md::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-md{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-textarea-md-h,.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:block}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:none}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-md,.sc-ion-textarea-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-md,.textarea-outline-notch-hidden.sc-ion-textarea-md{display:none}.textarea-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text.sc-ion-textarea-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.has-value.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:1}.label-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-md,.end-slot-wrapper.sc-ion-textarea-md{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-s>[slot=end]{margin-top:0}.sc-ion-textarea-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.textarea-fill-solid.sc-ion-textarea-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.textarea-fill-solid.ion-valid.sc-ion-textarea-md-h,.textarea-fill-solid.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}@media (any-hover: hover){.textarea-fill-solid.sc-ion-textarea-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.textarea-fill-solid.has-focus.sc-ion-textarea-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.textarea-fill-solid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{max-width:calc(100% / 0.75)}.textarea-fill-outline.sc-ion-textarea-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-outline.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.textarea-fill-outline.ion-valid.sc-ion-textarea-md-h,.textarea-fill-outline.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.textarea-fill-outline.sc-ion-textarea-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.textarea-fill-outline.has-focus.sc-ion-textarea-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:none}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{position:relative}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc(\\n    (100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75\\n  )}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-fill-outline.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:12px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:12px}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-container.sc-ion-textarea-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{pointer-events:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.textarea-fill-outline.sc-ion-textarea-md-h .notch-spacer.sc-ion-textarea-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{border-top:none}.sc-ion-textarea-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--padding-top:18px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;--highlight-height:2px;font-size:inherit}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{letter-spacing:0.0333333333em}.textarea-label-placement-floating.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.has-focus.textarea-label-placement-floating.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.has-focus.textarea-label-placement-stacked.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.textarea-disabled.sc-ion-textarea-md-h{opacity:0.38}.textarea-highlight.sc-ion-textarea-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}.has-focus.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{bottom:0}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:16px}.sc-ion-textarea-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\n\nconst Textarea = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inputId = `ion-textarea-${textareaIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        /**\n         * `true` if the textarea was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the textarea loses focus.\n         */\n        this.didTextareaClearOnEdit = false;\n        this.inheritedAttributes = {};\n        /**\n         * The `hasFocus` state ensures the focus class is\n         * added regardless of how the element is focused.\n         * The `ion-focused` class only applies when focused\n         * via tabbing, not by clicking.\n         * The `has-focus` logic was added to ensure the class\n         * is applied in both cases.\n         */\n        this.hasFocus = false;\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'none';\n        /**\n         * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.\n         *\n         * This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.\n         */\n        this.autofocus = false;\n        /**\n         * If `true`, the value will be cleared after focus upon edit.\n         */\n        this.clearOnEdit = false;\n        /**\n         * If `true`, the user cannot interact with the textarea.\n         */\n        this.disabled = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the user cannot modify the value.\n         */\n        this.readonly = false;\n        /**\n         * If `true`, the user must fill in a value before submitting a form.\n         */\n        this.required = false;\n        /**\n         * If `true`, the element will have its spelling and grammar checked.\n         */\n        this.spellcheck = false;\n        /**\n         * If `true`, the textarea container will grow and shrink based\n         * on the contents of the textarea.\n         */\n        this.autoGrow = false;\n        /**\n         * The value of the textarea.\n         */\n        this.value = '';\n        /**\n         * If `true`, a character counter will display the ratio of characters used and the total character limit.\n         * Developers must also set the `maxlength` property for the counter to be calculated correctly.\n         */\n        this.counter = false;\n        /**\n         * Where to place the label relative to the textarea.\n         * `\"start\"`: The label will appear to the left of the textarea in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the textarea in LTR and to the left in RTL.\n         * `\"floating\"`: The label will appear smaller and above the textarea when the textarea is focused or it has a value. Otherwise it will appear on top of the textarea.\n         * `\"stacked\"`: The label will appear smaller and above the textarea regardless even when the textarea is blurred or has no value.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         */\n        this.labelPlacement = 'start';\n        // `Event` type is used instead of `InputEvent`\n        // since the types from Stencil are not derived\n        // from the element (e.g. textarea and input\n        // should be InputEvent, but all other elements\n        // should be Event).\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the textarea value\n                 * is different than the value when the textarea was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didTextareaClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onKeyDown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        /**\n         * Stops propagation when the label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onLabelClick = (ev) => {\n            // Only stop propagation if the click was directly on the label\n            // and not on the input or other child elements\n            if (ev.target === ev.currentTarget) {\n                ev.stopPropagation();\n            }\n        };\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value) {\n            nativeInput.value = value;\n        }\n        this.runAutoGrow();\n    }\n    /**\n     * dir is a globally enumerated attribute.\n     * As a result, creating these as properties\n     * can have unintended side effects. Instead, we\n     * listen for attribute changes and inherit them\n     * to the inner `<textarea>` element.\n     */\n    onDirChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { dir: newValue });\n        forceUpdate(this);\n    }\n    /**\n     * This prevents the native input from emitting the click event.\n     * Instead, the click event from the ion-textarea is emitted.\n     */\n    onClickCapture(ev) {\n        const nativeInput = this.nativeInput;\n        if (nativeInput && ev.target === nativeInput) {\n            ev.stopPropagation();\n            this.el.click();\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: el,\n            }));\n        }\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['data-form-type', 'title', 'tabindex', 'dir']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.runAutoGrow();\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Sets focus on the native `textarea` in `ion-textarea`. Use this method instead of the global\n     * `textarea.focus()`.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<textarea>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    runAutoGrow() {\n        if (this.nativeInput && this.autoGrow) {\n            writeTask(() => {\n                var _a;\n                if (this.textareaWrapper) {\n                    // Replicated value is an attribute to be used in the stylesheet\n                    // to set the inner contents of a pseudo element.\n                    this.textareaWrapper.dataset.replicatedValue = (_a = this.value) !== null && _a !== void 0 ? _a : '';\n                }\n            });\n        }\n    }\n    /**\n     * Check if we need to clear the text input if clearOnEdit is enabled\n     */\n    checkClearOnEdit(ev) {\n        if (!this.clearOnEdit) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the textarea.\n         *\n         * We can't check to see if the value of the textarea\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the textarea.\n         *\n         * Unlike ion-input, the \"Enter\" key does modify the\n         * textarea by adding a new line, so \"Enter\" is not\n         * included in the IGNORED_KEYS array.\n         */\n        const IGNORED_KEYS = ['Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the textarea if the control has not been previously cleared\n         * during focus.\n         */\n        if (!this.didTextareaClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didTextareaClearOnEdit = true;\n        }\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    getValue() {\n        return this.value || '';\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the textarea and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"textarea-outline-container\" }, h(\"div\", { class: \"textarea-outline-start\" }), h(\"div\", { class: {\n                        'textarea-outline-notch': true,\n                        'textarea-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"textarea-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        return [\n            h(\"div\", { id: helperTextId, class: \"helper-text\" }, helperText),\n            h(\"div\", { id: errorTextId, class: \"error-text\" }, errorText),\n        ];\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"textarea-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    render() {\n        const { inputId, disabled, fill, shape, labelPlacement, el, hasFocus } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the textarea.\n         * For floating labels, the label should move above the textarea if\n         * the textarea has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the textarea is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { key: 'd9f2ede0107987fc42c99e310cd2336bad5a5755', class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`textarea-fill-${fill}`]: fill !== undefined,\n                [`textarea-shape-${shape}`]: shape !== undefined,\n                [`textarea-label-placement-${labelPlacement}`]: true,\n                'textarea-disabled': disabled,\n            }) }, h(\"label\", { key: '9de598b95237462bb3bccffaefe83afbb43554b8', class: \"textarea-wrapper\", htmlFor: inputId, onClick: this.onLabelClick }, this.renderLabelContainer(), h(\"div\", { key: 'e33c426c6541d723ccc246bb404c03687726ff83', class: \"textarea-wrapper-inner\" }, h(\"div\", { key: '521e11af9d54d281b0a2b1c25bcfc6f742c18296', class: \"start-slot-wrapper\" }, h(\"slot\", { key: '515523f6ca3ce0e5dd08f3275c21a190fb1ca177', name: \"start\" })), h(\"div\", { key: '916e01e00de8400ae00ef06bc1fb62d8be2eee08', class: \"native-wrapper\", ref: (el) => (this.textareaWrapper = el) }, h(\"textarea\", Object.assign({ key: '810271e6532d90e27dab1fcb26546113c1ce9cb0', class: \"native-textarea\", ref: (el) => (this.nativeInput = el), id: inputId, disabled: disabled, autoCapitalize: this.autocapitalize, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, minLength: this.minlength, maxLength: this.maxlength, name: this.name, placeholder: this.placeholder || '', readOnly: this.readonly, required: this.required, spellcheck: this.spellcheck, cols: this.cols, rows: this.rows, wrap: this.wrap, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeyDown, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId }, this.inheritedAttributes), value)), h(\"div\", { key: '80aca9ea9546dca9d38efd291a6b0be384bb6978', class: \"end-slot-wrapper\" }, h(\"slot\", { key: '407fab16c66a9f4a542369bfecc0d9afa0065977', name: \"end\" }))), shouldRenderHighlight && h(\"div\", { key: 'f00523a6698fac8a1996e04303487bef01d10f25', class: \"textarea-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"value\": [\"valueChanged\"],\n        \"dir\": [\"onDirChanged\"]\n    }; }\n};\nlet textareaIds = 0;\nTextarea.style = {\n    ios: textareaIosCss,\n    md: textareaMdCss\n};\n\nexport { Textarea as ion_textarea };\n"], "names": ["r", "registerInstance", "d", "createEvent", "n", "forceUpdate", "w", "writeTask", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "c", "createNotchController", "debounceEvent", "i", "inheritAriaAttributes", "b", "inheritAttributes", "componentOnReady", "createSlotMutationController", "g", "getCounterText", "hostContext", "createColorClasses", "textareaIosCss", "textareaMdCss", "Textarea", "constructor", "hostRef", "ionChange", "ionInput", "ionBlur", "ionFocus", "inputId", "textareaIds", "helperTextId", "errorTextId", "didTextareaClearOnEdit", "inheritedAttributes", "hasFocus", "autocapitalize", "autofocus", "clearOnEdit", "disabled", "name", "readonly", "required", "spellcheck", "autoGrow", "value", "counter", "labelPlacement", "onInput", "ev", "input", "target", "emitInputChange", "onChange", "emitValueChange", "onFocus", "focusedValue", "emit", "onBlur", "onKeyDown", "checkClearOnEdit", "onLabelClick", "currentTarget", "stopPropagation", "debounce<PERSON><PERSON>ed", "debounce", "originalIonInput", "undefined", "valueChanged", "nativeInput", "getValue", "runAutoGrow", "onDirChanged", "newValue", "Object", "assign", "dir", "onClickCapture", "el", "click", "connectedCallback", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "disconnectedCallback", "destroy", "componentWillLoad", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "setFocus", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "event", "toString", "textareaWrapper", "dataset", "replicatedValue", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "hasValue", "renderLabel", "label", "class", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "renderLabelContainer", "mode", "hasOutlineFill", "fill", "ref", "renderHintText", "helperText", "errorText", "id", "getHintTextID", "classList", "contains", "renderCounter", "maxlength", "counterFormatter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "render", "shape", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "labelShouldFloat", "color", "htmlFor", "onClick", "autoCapitalize", "autoFocus", "enterKeyHint", "enterkeyhint", "inputMode", "inputmode", "<PERSON><PERSON><PERSON><PERSON>", "minlength", "max<PERSON><PERSON><PERSON>", "placeholder", "readOnly", "cols", "rows", "wrap", "watchers", "style", "ios", "md", "ion_textarea"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}