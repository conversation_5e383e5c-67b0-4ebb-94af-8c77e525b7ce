{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-toggle_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4J;AACjE;AAC/B;AACb;AACiC;AACqB;AACpE;AACJ;AAE7B,MAAM4B,YAAY,GAAG,4pSAA4pS;AAEjrS,MAAMC,WAAW,GAAG,+4PAA+4P;AAEn6P,MAAMC,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBhC,qDAAgB,CAAC,IAAI,EAAE+B,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAG/B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACgC,QAAQ,GAAGhC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiC,OAAO,GAAGjC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACkC,OAAO,GAAG,UAAUC,SAAS,EAAE,EAAE;IACtC,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACF,OAAO,MAAM;IACzC,IAAI,CAACG,YAAY,GAAG,GAAG,IAAI,CAACH,OAAO,cAAc;IACjD,IAAI,CAACI,WAAW,GAAG,GAAG,IAAI,CAACJ,OAAO,aAAa;IAC/C,IAAI,CAACK,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACT,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACU,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG7C,iDAAM,CAAC8C,GAAG,CAAC,mBAAmB,CAAC;IACxD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,YAAY,gBAAAC,yMAAA,CAAG,aAAY;MAC5B,MAAM;QAAEC;MAAY,CAAC,GAAGvB,KAAI;MAC5B,IAAIuB,WAAW,EAAE;QACbvB,KAAI,CAACwB,OAAO,GAAG,OAAO,qHAA6B,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,QAAQ;UACrBC,eAAe,EAAE,GAAG;UACpBC,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEA,CAAA,KAAM/B,KAAI,CAAC+B,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAKjC,KAAI,CAACgC,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAKjC,KAAI,CAACkC,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACFjC,KAAI,CAACmC,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACC,SAAS,GAAIH,EAAE,IAAK;MACrB,IAAIA,EAAE,CAACI,GAAG,KAAK,GAAG,EAAE;QAChBJ,EAAE,CAACK,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,CAACvB,QAAQ,EAAE;UAChB,IAAI,CAACwB,aAAa,CAAC,CAAC;QACxB;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAIP,EAAE,IAAK;MACnB;AACZ;AACA;AACA;AACA;MACY,MAAMQ,aAAa,GAAGnE,qDAAU,CAAC,KAAK,CAAC;MACvC,IAAI,IAAI,CAACyC,QAAQ,EAAE;QACf;MACJ;MACAkB,EAAE,CAACK,cAAc,CAAC,CAAC;MACnB,IAAI,IAAI,CAAC7B,QAAQ,GAAG,GAAG,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAClC,IAAI,CAACJ,aAAa,CAAC,CAAC;QACpBE,aAAa,IAAIvD,sDAAe,CAAC,CAAC;MACtC;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAAC0D,eAAe,GAAIX,EAAE,IAAK;MAC3BA,EAAE,CAACY,eAAe,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC5C,QAAQ,CAAC6C,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC7C,OAAO,CAAC4C,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,kBAAkB,GAAG,CAACC,IAAI,EAAEpC,OAAO,KAAK;MACzC,IAAIoC,IAAI,KAAK,IAAI,EAAE;QACf,OAAOpC,OAAO,GAAGvB,iDAAgB,GAAGC,iDAAa;MACrD;MACA,OAAOsB,OAAO,GAAGtB,iDAAa,GAAGE,iDAAc;IACnD,CAAC;EACL;EACAyC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACX,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC2B,MAAM,CAAC,CAAC,IAAI,CAACpC,QAAQ,CAAC;IACvC;EACJ;EACAwB,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEzB,OAAO;MAAEE;IAAM,CAAC,GAAG,IAAI;IAC/B,MAAMoC,YAAY,GAAG,CAACtC,OAAO;IAC7B,IAAI,CAACA,OAAO,GAAGsC,YAAY;IAC3B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACpD,SAAS,CAAC8C,IAAI,CAAC;MAChBjC,OAAO,EAAEsC,YAAY;MACrBpC;IACJ,CAAC,CAAC;EACN;EACMsC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjC,yMAAA;MACtB;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIiC,MAAI,CAAC5C,OAAO,EAAE;QACd4C,MAAI,CAAClC,YAAY,CAAC,CAAC;MACvB;IAAC;EACL;EACAmC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACnC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACV,OAAO,GAAG,IAAI;EACvB;EACA8C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACjC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACkC,OAAO,CAAC,CAAC;MACtB,IAAI,CAAClC,OAAO,GAAGmC,SAAS;IAC5B;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAClD,mBAAmB,GAAGmD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,uDAAqB,CAAC,IAAI,CAAC2C,EAAE,CAAC,CAAC;EAChF;EACAK,OAAOA,CAAA,EAAG;IACN,IAAI,CAACnB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACyC,QAAQ,CAAC,CAAC;EACnB;EACArB,MAAMA,CAAC+B,MAAM,EAAE;IACX,IAAIC,YAAY,CAAC7E,mDAAK,CAAC,IAAI,CAACuC,EAAE,CAAC,EAAE,IAAI,CAACZ,OAAO,EAAEiD,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE;MAChE,IAAI,CAAC1B,aAAa,CAAC,CAAC;MACpBrD,sDAAe,CAAC,CAAC;IACrB;EACJ;EACAgD,KAAKA,CAACD,EAAE,EAAE;IACN,IAAI,CAACrB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,QAAQ,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1BV,EAAE,CAACiC,KAAK,CAAC5B,cAAc,CAAC,CAAC;IACzBL,EAAE,CAACiC,KAAK,CAACC,wBAAwB,CAAC,CAAC;EACvC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpD,KAAK,IAAI,EAAE;EAC3B;EACAqC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACgB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACAC,uBAAuBA,CAACrB,IAAI,EAAEpC,OAAO,EAAE;IACnC,MAAM0D,IAAI,GAAG,IAAI,CAACvB,kBAAkB,CAACC,IAAI,EAAEpC,OAAO,CAAC;IACnD,OAAQvC,qDAAC,CAAC,UAAU,EAAE;MAAEkG,KAAK,EAAE;QACvB,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAE3D;MAClC,CAAC;MAAE0D,IAAI,EAAEA,IAAI;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC;EAC/C;EACAE,mBAAmBA,CAAA,EAAG;IAClB,MAAMxB,IAAI,GAAGzE,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEwC,iBAAiB;MAAEH;IAAQ,CAAC,GAAG,IAAI;IAC3C,OAAQvC,qDAAC,CAAC,KAAK,EAAE;MAAEkG,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,OAAO;MAAEC,GAAG,EAAGlD,EAAE,IAAM,IAAI,CAACH,WAAW,GAAGG;IAAI,CAAC,EAAET,iBAAiB,IAC7GiC,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,CAACqB,uBAAuB,CAACrB,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAACqB,uBAAuB,CAACrB,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE3E,qDAAC,CAAC,KAAK,EAAE;MAAEkG,KAAK,EAAE;IAAsB,CAAC,EAAElG,qDAAC,CAAC,KAAK,EAAE;MAAEkG,KAAK,EAAE,cAAc;MAAEE,IAAI,EAAE;IAAS,CAAC,EAAE1D,iBAAiB,IAAIiC,IAAI,KAAK,IAAI,IAAI,IAAI,CAACqB,uBAAuB,CAACrB,IAAI,EAAEpC,OAAO,CAAC,CAAC,CAAC,CAAC;EACpS;EACA,IAAI+D,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnD,EAAE,CAACoD,WAAW,KAAK,EAAE;EACrC;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAErD,EAAE;MAAEsD,UAAU;MAAEC,SAAS;MAAE1E,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAIkB,EAAE,CAACwD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIzD,EAAE,CAACwD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIF,SAAS,EAAE;MAC3F,OAAOzE,WAAW;IACtB;IACA,IAAIwE,UAAU,EAAE;MACZ,OAAOzE,YAAY;IACvB;IACA,OAAOoD,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACIyB,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEJ,UAAU;MAAEC,SAAS;MAAE1E,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE;AACR;AACA;AACA;IACQ,MAAM6E,WAAW,GAAG,CAAC,CAACL,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,IAAI,CAACI,WAAW,EAAE;MACd;IACJ;IACA,OAAQ9G,qDAAC,CAAC,KAAK,EAAE;MAAEkG,KAAK,EAAE;IAAgB,CAAC,EAAElG,qDAAC,CAAC,KAAK,EAAE;MAAE+G,EAAE,EAAE/E,YAAY;MAAEkE,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE;IAA8B,CAAC,EAAEK,UAAU,CAAC,EAAEzG,qDAAC,CAAC,KAAK,EAAE;MAAE+G,EAAE,EAAE9E,WAAW;MAAEiE,KAAK,EAAE,YAAY;MAAEE,IAAI,EAAE;IAA6B,CAAC,EAAEM,SAAS,CAAC,CAAC;EAC1P;EACAM,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3E,SAAS;MAAE4E,SAAS;MAAE1E,OAAO;MAAE2E,KAAK;MAAE1E,QAAQ;MAAEW,EAAE;MAAElB,WAAW;MAAEqE,QAAQ;MAAEnE,mBAAmB;MAAEN,OAAO;MAAEE,YAAY;MAAEoF,OAAO;MAAEvE,cAAc;MAAEN,IAAI;MAAEO;IAAU,CAAC,GAAG,IAAI;IAChL,MAAM8B,IAAI,GAAGzE,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuC,KAAK,GAAG,IAAI,CAACoD,QAAQ,CAAC,CAAC;IAC7B,MAAMuB,GAAG,GAAGxG,mDAAK,CAACuC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC1C,uDAAiB,CAAC,IAAI,EAAE0C,EAAE,EAAEb,IAAI,EAAEC,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAED,QAAQ,CAAC;IACjE,OAAQxC,qDAAC,CAACI,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEuD,IAAI,EAAE,QAAQ;MAAE,cAAc,EAAE,GAAG9E,OAAO,EAAE;MAAE,kBAAkB,EAAE,IAAI,CAACiE,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAKvE,WAAW;MAAEgC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,iBAAiB,EAAEqC,QAAQ,GAAGvE,YAAY,GAAG,IAAI;MAAE,YAAY,EAAEI,mBAAmB,CAAC,YAAY,CAAC,IAAI,IAAI;MAAE,eAAe,EAAEK,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE8E,QAAQ,EAAE9E,QAAQ,GAAG4C,SAAS,GAAG,CAAC;MAAEvB,SAAS,EAAE,IAAI,CAACA,SAAS;MAAEqC,KAAK,EAAErF,qDAAkB,CAACqG,KAAK,EAAE;QACvd,CAACvC,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE7D,qDAAW,CAAC,UAAU,EAAEqC,EAAE,CAAC;QACtC,kBAAkB,EAAEd,SAAS;QAC7B,gBAAgB,EAAEE,OAAO;QACzB,iBAAiB,EAAEC,QAAQ;QAC3B,CAAC,kBAAkB2E,OAAO,EAAE,GAAGA,OAAO,KAAK/B,SAAS;QACpD,CAAC,oBAAoB6B,SAAS,EAAE,GAAGA,SAAS,KAAK7B,SAAS;QAC1D,CAAC,0BAA0BxC,cAAc,EAAE,GAAG,IAAI;QAClD,CAAC,UAAUwE,GAAG,EAAE,GAAG;MACvB,CAAC;IAAE,CAAC,EAAEpH,qDAAC,CAAC,OAAO,EAAE;MAAE8D,GAAG,EAAE,0CAA0C;MAAEoC,KAAK,EAAE,gBAAgB;MAAEqB,OAAO,EAAE1F;IAAQ,CAAC,EAAE7B,qDAAC,CAAC,OAAO,EAAEsF,MAAM,CAACC,MAAM,CAAC;MAAEzB,GAAG,EAAE,0CAA0C;MAAE0D,IAAI,EAAE,UAAU;MAAEH,IAAI,EAAE,QAAQ;MAAE,cAAc,EAAE,GAAG9E,OAAO,EAAE;MAAEA,OAAO,EAAEA,OAAO;MAAEC,QAAQ,EAAEA,QAAQ;MAAEuE,EAAE,EAAElF,OAAO;MAAE0C,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEE,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE4B,GAAG,EAAGP,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA,OAAQ;MAAEjD,QAAQ,EAAEA;IAAS,CAAC,EAAEV,mBAAmB,CAAC,CAAC,EAAEnC,qDAAC,CAAC,KAAK,EAAE;MAAE8D,GAAG,EAAE,0CAA0C;MAAEoC,KAAK,EAAE;QACpgB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACI;MAClC,CAAC;MAAEF,IAAI,EAAE,OAAO;MAAEW,EAAE,EAAEhF,YAAY;MAAEkC,OAAO,EAAE,IAAI,CAACI;IAAgB,CAAC,EAAErE,qDAAC,CAAC,MAAM,EAAE;MAAE8D,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE,IAAI,CAAC+C,cAAc,CAAC,CAAC,CAAC,EAAE7G,qDAAC,CAAC,KAAK,EAAE;MAAE8D,GAAG,EAAE,0CAA0C;MAAEoC,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzR;EACA,IAAIhD,EAAEA,CAAA,EAAG;IAAE,OAAO7C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWmH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,MAAMhC,YAAY,GAAGA,CAAC2B,GAAG,EAAE7E,OAAO,EAAEmD,MAAM,EAAEgC,MAAM,KAAK;EACnD,IAAInF,OAAO,EAAE;IACT,OAAQ,CAAC6E,GAAG,IAAIM,MAAM,GAAGhC,MAAM,IAAM0B,GAAG,IAAI,EAAE,GAAG1B,MAAO;EAC5D,CAAC,MACI;IACD,OAAQ,CAAC0B,GAAG,IAAI,EAAE,GAAG1B,MAAM,IAAM0B,GAAG,IAAIM,MAAM,GAAGhC,MAAO;EAC5D;AACJ,CAAC;AACD,IAAI5D,SAAS,GAAG,CAAC;AACjBR,MAAM,CAACqG,KAAK,GAAG;EACXC,GAAG,EAAExG,YAAY;EACjByG,EAAE,EAAExG;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, a as isPlatform, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { c as hapticSelection } from './haptic-DzAMWJuk.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-BLV6ykCk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\n\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #0054e9);--border-radius:15.5px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 4px rgba(0, 0, 0, 0.06), 0 3px 8px rgba(0, 0, 0, 0.06);--handle-height:calc(31px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(31px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}.native-wrapper .toggle-icon{width:51px;height:31px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark, #222428)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}\";\n\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #0054e9);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}\";\n\nconst Toggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-tg-${toggleIds++}`;\n        this.inputLabelId = `${this.inputId}-lbl`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.lastDrag = 0;\n        this.inheritedAttributes = {};\n        this.didLoad = false;\n        this.activated = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the toggle is selected.\n         */\n        this.checked = false;\n        /**\n         * If `true`, the user cannot interact with the toggle.\n         */\n        this.disabled = false;\n        /**\n         * The value of the toggle does not mean if it's checked or not, use the `checked`\n         * property for that.\n         *\n         * The value of a toggle is analogous to the value of a `<input type=\"checkbox\">`,\n         * it's only used when the toggle participates in a native `<form>`.\n         */\n        this.value = 'on';\n        /**\n         * Enables the on/off accessibility switch labels within the toggle.\n         */\n        this.enableOnOffLabels = config.get('toggleOnOffLabels');\n        /**\n         * Where to place the label relative to the input.\n         * `\"start\"`: The label will appear to the left of the toggle in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the toggle in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the toggle regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n         */\n        this.labelPlacement = 'start';\n        /**\n         * If true, screen readers will announce it as a required field. This property\n         * works only for accessibility purposes, it will not prevent the form from\n         * submitting if the value is invalid.\n         */\n        this.required = false;\n        this.setupGesture = async () => {\n            const { toggleTrack } = this;\n            if (toggleTrack) {\n                this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n                    el: toggleTrack,\n                    gestureName: 'toggle',\n                    gesturePriority: 100,\n                    threshold: 5,\n                    passive: false,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.disabledChanged();\n            }\n        };\n        this.onKeyDown = (ev) => {\n            if (ev.key === ' ') {\n                ev.preventDefault();\n                if (!this.disabled) {\n                    this.toggleChecked();\n                }\n            }\n        };\n        this.onClick = (ev) => {\n            /**\n             * The haptics for the toggle on tap is\n             * an iOS-only feature. As such, it should\n             * only trigger on iOS.\n             */\n            const enableHaptics = isPlatform('ios');\n            if (this.disabled) {\n                return;\n            }\n            ev.preventDefault();\n            if (this.lastDrag + 300 < Date.now()) {\n                this.toggleChecked();\n                enableHaptics && hapticSelection();\n            }\n        };\n        /**\n         * Stops propagation when the display label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onDivLabelClick = (ev) => {\n            ev.stopPropagation();\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.getSwitchLabelIcon = (mode, checked) => {\n            if (mode === 'md') {\n                return checked ? checkmarkOutline : removeOutline;\n            }\n            return checked ? removeOutline : ellipseOutline;\n        };\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    toggleChecked() {\n        const { checked, value } = this;\n        const isNowChecked = !checked;\n        this.checked = isNowChecked;\n        this.setFocus();\n        this.ionChange.emit({\n            checked: isNowChecked,\n            value,\n        });\n    }\n    async connectedCallback() {\n        /**\n         * If we have not yet rendered\n         * ion-toggle, then toggleTrack is not defined.\n         * But if we are moving ion-toggle via appendChild,\n         * then toggleTrack will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n    }\n    componentDidLoad() {\n        this.setupGesture();\n        this.didLoad = true;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n    onStart() {\n        this.activated = true;\n        // touch-action does not work in iOS\n        this.setFocus();\n    }\n    onMove(detail) {\n        if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n            this.toggleChecked();\n            hapticSelection();\n        }\n    }\n    onEnd(ev) {\n        this.activated = false;\n        this.lastDrag = Date.now();\n        ev.event.preventDefault();\n        ev.event.stopImmediatePropagation();\n    }\n    getValue() {\n        return this.value || '';\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    renderOnOffSwitchLabels(mode, checked) {\n        const icon = this.getSwitchLabelIcon(mode, checked);\n        return (h(\"ion-icon\", { class: {\n                'toggle-switch-icon': true,\n                'toggle-switch-icon-checked': checked,\n            }, icon: icon, \"aria-hidden\": \"true\" }));\n    }\n    renderToggleControl() {\n        const mode = getIonMode(this);\n        const { enableOnOffLabels, checked } = this;\n        return (h(\"div\", { class: \"toggle-icon\", part: \"track\", ref: (el) => (this.toggleTrack = el) }, enableOnOffLabels &&\n            mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", { class: \"toggle-icon-wrapper\" }, h(\"div\", { class: \"toggle-inner\", part: \"handle\" }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked)))));\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    /**\n     * Responsible for rendering helper text and error text.\n     * This element should only be rendered if hint text is set.\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return (h(\"div\", { class: \"toggle-bottom\" }, h(\"div\", { id: helperTextId, class: \"helper-text\", part: \"supporting-text helper-text\" }, helperText), h(\"div\", { id: errorTextId, class: \"error-text\", part: \"supporting-text error-text\" }, errorText)));\n    }\n    render() {\n        const { activated, alignment, checked, color, disabled, el, errorTextId, hasLabel, inheritedAttributes, inputId, inputLabelId, justify, labelPlacement, name, required, } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { key: '21037ea2e8326f58c84becadde475f007f931924', role: \"switch\", \"aria-checked\": `${checked}`, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === errorTextId, onClick: this.onClick, \"aria-labelledby\": hasLabel ? inputLabelId : null, \"aria-label\": inheritedAttributes['aria-label'] || null, \"aria-disabled\": disabled ? 'true' : null, tabindex: disabled ? undefined : 0, onKeyDown: this.onKeyDown, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'toggle-activated': activated,\n                'toggle-checked': checked,\n                'toggle-disabled': disabled,\n                [`toggle-justify-${justify}`]: justify !== undefined,\n                [`toggle-alignment-${alignment}`]: alignment !== undefined,\n                [`toggle-label-placement-${labelPlacement}`]: true,\n                [`toggle-${rtl}`]: true,\n            }) }, h(\"label\", { key: '4d153679d118d01286f6633d1c19558a97745ff6', class: \"toggle-wrapper\", htmlFor: inputId }, h(\"input\", Object.assign({ key: '0dfcd4df15b8d41bec5ff5f8912503afbb7bec53', type: \"checkbox\", role: \"switch\", \"aria-checked\": `${checked}`, checked: checked, disabled: disabled, id: inputId, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl), required: required }, inheritedAttributes)), h(\"div\", { key: 'ffed3a07ba2ab70e5b232e6041bc3b6b34be8331', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\", id: inputLabelId, onClick: this.onDivLabelClick }, h(\"slot\", { key: 'd88e1e3dcdd8293f6b61f237cd7a0511dcbce300' }), this.renderHintText()), h(\"div\", { key: '0e924225f5f0caf3c88738acb6c557bd8c1b68f6', class: \"native-wrapper\" }, this.renderToggleControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n    if (checked) {\n        return (!rtl && margin > deltaX) || (rtl && 10 < deltaX);\n    }\n    else {\n        return (!rtl && 10 < deltaX) || (rtl && margin > deltaX);\n    }\n};\nlet toggleIds = 0;\nToggle.style = {\n    ios: toggleIosCss,\n    md: toggleMdCss\n};\n\nexport { Toggle as ion_toggle };\n"], "names": ["r", "registerInstance", "d", "createEvent", "l", "config", "a", "isPlatform", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "i", "inheritAriaAttributes", "renderHiddenInput", "c", "hapticSelection", "isRTL", "createColorClasses", "hostContext", "f", "checkmarkOutline", "removeOutline", "g", "ellipseOutline", "toggleIosCss", "toggleMdCss", "Toggle", "constructor", "hostRef", "_this", "ionChange", "ionFocus", "ionBlur", "inputId", "toggleIds", "inputLabelId", "helperTextId", "errorTextId", "lastDrag", "inheritedAttributes", "didLoad", "activated", "name", "checked", "disabled", "value", "enableOnOffLabels", "get", "labelPlacement", "required", "setupGesture", "_asyncToGenerator", "toggleTrack", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "passive", "onStart", "onMove", "ev", "onEnd", "disabled<PERSON><PERSON>ed", "onKeyDown", "key", "preventDefault", "toggleChecked", "onClick", "enableHaptics", "Date", "now", "onDivLabelClick", "stopPropagation", "onFocus", "emit", "onBlur", "getSwitchLabelIcon", "mode", "enable", "isNowChecked", "setFocus", "connectedCallback", "_this2", "componentDidLoad", "disconnectedCallback", "destroy", "undefined", "componentWillLoad", "Object", "assign", "detail", "shouldToggle", "deltaX", "event", "stopImmediatePropagation", "getValue", "focusEl", "focus", "renderOnOffSwitchLabels", "icon", "class", "renderToggleControl", "part", "ref", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "getHintTextID", "helperText", "errorText", "classList", "contains", "renderHintText", "hasHintText", "id", "render", "alignment", "color", "justify", "rtl", "role", "tabindex", "htmlFor", "type", "watchers", "margin", "style", "ios", "md", "ion_toggle"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}