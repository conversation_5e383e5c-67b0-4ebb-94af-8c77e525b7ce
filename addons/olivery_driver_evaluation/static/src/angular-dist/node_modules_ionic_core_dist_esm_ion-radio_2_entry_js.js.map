{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-radio_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8H;AACd;AACvC;AACO;AAEhF,MAAMsB,WAAW,GAAG,6uKAA6uK;AAEjwK,MAAMC,UAAU,GAAG,g+LAAg+L;AAEn/L,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGxB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyB,OAAO,GAAGzB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC0B,OAAO,GAAG,UAAUC,cAAc,EAAE,EAAE;IAC3C,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACM,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,IAAI,CAACN,UAAU,EAAE;QACjB,MAAM;UAAEO,WAAW;UAAEC,KAAK,EAAEC;QAAgB,CAAC,GAAG,IAAI,CAACT,UAAU;QAC/D,IAAI,CAACC,OAAO,GAAGd,kEAAgB,CAACsB,eAAe,EAAE,IAAI,CAACD,KAAK,EAAED,WAAW,CAAC;MAC7E;IACJ,CAAC;IACD,IAAI,CAACG,OAAO,GAAG,MAAM;MACjB,MAAM;QAAEV,UAAU;QAAEC,OAAO;QAAEG;MAAS,CAAC,GAAG,IAAI;MAC9C,IAAIA,QAAQ,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIH,OAAO,KAAKD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,mBAAmB,CAAC,EAAE;QACrG,IAAI,CAACV,OAAO,GAAG,KAAK;MACxB,CAAC,MACI;QACD,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;IACJ,CAAC;IACD,IAAI,CAACW,OAAO,GAAG,MAAM;MACjB,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACjB,OAAO,CAACgB,IAAI,CAAC,CAAC;IACvB,CAAC;EACL;EACAE,YAAYA,CAAA,EAAG;IACX;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACT,WAAW,CAAC,CAAC;EACtB;EACAU,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACV,WAAW,CAAC,CAAC;EACtB;EACA;EACMW,QAAQA,CAACC,EAAE,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MACf,IAAIF,EAAE,KAAKG,SAAS,EAAE;QAClBH,EAAE,CAACI,eAAe,CAAC,CAAC;QACpBJ,EAAE,CAACK,cAAc,CAAC,CAAC;MACvB;MACAJ,KAAI,CAACK,EAAE,CAACC,KAAK,CAAC,CAAC;IAAC;EACpB;EACA;EACMC,iBAAiBA,CAAClB,KAAK,EAAE;IAAA,IAAAmB,MAAA;IAAA,OAAAP,yMAAA;MAC3BO,MAAI,CAACzB,cAAc,GAAGM,KAAK;IAAC;EAChC;EACAoB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACpB,KAAK,KAAKa,SAAS,EAAE;MAC1B,IAAI,CAACb,KAAK,GAAG,IAAI,CAACV,OAAO;IAC7B;IACA,MAAME,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG,IAAI,CAACwB,EAAE,CAACK,OAAO,CAAC,iBAAiB,CAAE;IACzE,IAAI7B,UAAU,EAAE;MACZ,IAAI,CAACM,WAAW,CAAC,CAAC;MAClBzB,uDAAgB,CAACmB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACM,WAAW,CAAC;IACpE;EACJ;EACAwB,oBAAoBA,CAAA,EAAG;IACnB,MAAM9B,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAIA,UAAU,EAAE;MACZjB,uDAAmB,CAACiB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACM,WAAW,CAAC;MACnE,IAAI,CAACN,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA,IAAI+B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,EAAE,CAACQ,WAAW,KAAK,EAAE;EACrC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAQ5D,qDAAC,CAAC,KAAK,EAAE;MAAE6D,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAY,CAAC,EAAE9D,qDAAC,CAAC,KAAK,EAAE;MAAE6D,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC,EAAE9D,qDAAC,CAAC,KAAK,EAAE;MAAE6D,KAAK,EAAE;IAAe,CAAC,CAAC,CAAC;EACvJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEnC,OAAO;MAAEG,QAAQ;MAAEiC,KAAK;MAAEb,EAAE;MAAEc,OAAO;MAAEjC,cAAc;MAAE0B,QAAQ;MAAE7B,cAAc;MAAEqC;IAAU,CAAC,GAAG,IAAI;IAC3G,MAAMC,IAAI,GAAGjE,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkE,MAAM,GAAGrD,qDAAW,CAAC,UAAU,EAAEoC,EAAE,CAAC;IAC1C,OAAQnD,qDAAC,CAACI,iDAAI,EAAE;MAAEiE,GAAG,EAAE,0CAA0C;MAAE9B,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwB,KAAK,EAAE5C,qDAAkB,CAAC+C,KAAK,EAAE;QAC/J,CAACG,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEC,MAAM;QACjB,eAAe,EAAExC,OAAO;QACxB,gBAAgB,EAAEG,QAAQ;QAC1B,CAAC,iBAAiBkC,OAAO,EAAE,GAAGA,OAAO,KAAKjB,SAAS;QACnD,CAAC,mBAAmBkB,SAAS,EAAE,GAAGA,SAAS,KAAKlB,SAAS;QACzD,CAAC,yBAAyBhB,cAAc,EAAE,GAAG,IAAI;QACjD;QACA,iBAAiB,EAAE,CAACoC,MAAM;QAC1B,eAAe,EAAE,CAACA;MACtB,CAAC,CAAC;MAAEE,IAAI,EAAE,OAAO;MAAE,cAAc,EAAE1C,OAAO,GAAG,MAAM,GAAG,OAAO;MAAE,eAAe,EAAEG,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEwC,QAAQ,EAAE1C;IAAe,CAAC,EAAE7B,qDAAC,CAAC,OAAO,EAAE;MAAEqE,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;IAAgB,CAAC,EAAE7D,qDAAC,CAAC,KAAK,EAAE;MAAEqE,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;QAC7R,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACH;MAClC,CAAC;MAAEI,IAAI,EAAE;IAAQ,CAAC,EAAE9D,qDAAC,CAAC,MAAM,EAAE;MAAEqE,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAErE,qDAAC,CAAC,KAAK,EAAE;MAAEqE,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACD,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAChN;EACA,IAAIT,EAAEA,CAAA,EAAG;IAAE,OAAO7C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAI9C,cAAc,GAAG,CAAC;AACtBN,KAAK,CAACqD,KAAK,GAAG;EACVC,GAAG,EAAExD,WAAW;EAChByD,EAAE,EAAExD;AACR,CAAC;AAED,MAAMyD,gBAAgB,GAAG,wjBAAwjB;AAEjlB,MAAMC,eAAe,GAAG,wjBAAwjB;AAEhlB,MAAMC,UAAU,GAAG,MAAM;EACrBzD,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACyD,SAAS,GAAGhF,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACiF,cAAc,GAAGjF,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC0B,OAAO,GAAG,UAAUwD,aAAa,EAAE,EAAE;IAC1C,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACzD,OAAO,cAAc;IACjD,IAAI,CAAC0D,WAAW,GAAG,GAAG,IAAI,CAAC1D,OAAO,aAAa;IAC/C,IAAI,CAAC2D,OAAO,GAAG,GAAG,IAAI,CAAC3D,OAAO,MAAM;IACpC;AACR;AACA;IACQ,IAAI,CAACa,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;IACQ,IAAI,CAACR,IAAI,GAAG,IAAI,CAACL,OAAO;IACxB,IAAI,CAAC4D,gBAAgB,GAAIlD,KAAK,IAAK;MAC/B,MAAMmD,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAC/B;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAK,CAACA,KAAK,CAAC3D,QAAQ,CAAC;MACrD,MAAMH,OAAO,GAAG0D,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACvD,KAAK,KAAKA,KAAK,IAAI,CAACuD,KAAK,CAAC3D,QAAQ,CAAC;MAChF,IAAI,CAACyD,KAAK,IAAI,CAAC5D,OAAO,EAAE;QACpB;MACJ;MACA;MACA;MACA,MAAM+D,SAAS,GAAG/D,OAAO,IAAI4D,KAAK;MAClC,KAAK,MAAME,KAAK,IAAIJ,MAAM,EAAE;QACxB,MAAMf,QAAQ,GAAGmB,KAAK,KAAKC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7CD,KAAK,CAACrC,iBAAiB,CAACkB,QAAQ,CAAC;MACrC;IACJ,CAAC;IACD,IAAI,CAAClC,OAAO,GAAIQ,EAAE,IAAK;MACnBA,EAAE,CAACK,cAAc,CAAC,CAAC;MACnB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAM0C,aAAa,GAAG/C,EAAE,CAACgD,MAAM,IAAIhD,EAAE,CAACgD,MAAM,CAACrC,OAAO,CAAC,WAAW,CAAC;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIoC,aAAa,IAAI,CAACA,aAAa,CAAC7D,QAAQ,EAAE;QAC1C,MAAM+D,YAAY,GAAG,IAAI,CAAC3D,KAAK;QAC/B,MAAM4D,QAAQ,GAAGH,aAAa,CAACzD,KAAK;QACpC,IAAI4D,QAAQ,KAAKD,YAAY,EAAE;UAC3B,IAAI,CAAC3D,KAAK,GAAG4D,QAAQ;UACrB,IAAI,CAACC,eAAe,CAACnD,EAAE,CAAC;QAC5B,CAAC,MACI,IAAI,IAAI,CAACP,mBAAmB,EAAE;UAC/B,IAAI,CAACH,KAAK,GAAGa,SAAS;UACtB,IAAI,CAACgD,eAAe,CAACnD,EAAE,CAAC;QAC5B;MACJ;IACJ,CAAC;EACL;EACAH,YAAYA,CAACP,KAAK,EAAE;IAChB,IAAI,CAACkD,gBAAgB,CAAClD,KAAK,CAAC;IAC5B,IAAI,CAAC6C,cAAc,CAACxC,IAAI,CAAC;MAAEL;IAAM,CAAC,CAAC;EACvC;EACAQ,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACD,YAAY,CAAC,IAAI,CAACP,KAAK,CAAC;EACjC;EACMoB,iBAAiBA,CAAA,EAAG;IAAA,IAAA0C,MAAA;IAAA,OAAAlD,yMAAA;MACtB;MACA;MACA,MAAMmD,MAAM,GAAGD,MAAI,CAAC9C,EAAE,CAACgD,aAAa,CAAC,iBAAiB,CAAC,IAAIF,MAAI,CAAC9C,EAAE,CAACgD,aAAa,CAAC,kBAAkB,CAAC;MACpG,IAAID,MAAM,EAAE;QACR,MAAME,KAAK,GAAIH,MAAI,CAACG,KAAK,GAAGF,MAAM,CAACC,aAAa,CAAC,WAAW,CAAE;QAC9D,IAAIC,KAAK,EAAE;UACPH,MAAI,CAACb,OAAO,GAAGgB,KAAK,CAACC,EAAE,GAAGJ,MAAI,CAACnE,IAAI,GAAG,MAAM;QAChD;MACJ;IAAC;EACL;EACAyD,SAASA,CAAA,EAAG;IACR,OAAOe,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpD,EAAE,CAACqD,gBAAgB,CAAC,WAAW,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,eAAeA,CAACS,KAAK,EAAE;IACnB,MAAM;MAAEtE;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC4C,SAAS,CAACvC,IAAI,CAAC;MAAEL,KAAK;MAAEsE;IAAM,CAAC,CAAC;EACzC;EACAC,SAASA,CAAC7D,EAAE,EAAE;IACV;IACA;IACA,MAAM8D,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAACxD,EAAE,CAACK,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,CAACL,EAAE,CAACK,OAAO,CAAC,kBAAkB,CAAC;IAC1G,IAAIX,EAAE,CAACgD,MAAM,IAAI,CAAC,IAAI,CAAC1C,EAAE,CAACyD,QAAQ,CAAC/D,EAAE,CAACgD,MAAM,CAAC,EAAE;MAC3C;IACJ;IACA;IACA;IACA,MAAMP,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAACsB,MAAM,CAAEnB,KAAK,IAAK,CAACA,KAAK,CAAC3D,QAAQ,CAAC;IAClE;IACA,IAAIc,EAAE,CAACgD,MAAM,IAAIP,MAAM,CAACwB,QAAQ,CAACjE,EAAE,CAACgD,MAAM,CAAC,EAAE;MACzC,MAAMkB,KAAK,GAAGzB,MAAM,CAAC0B,SAAS,CAAEtB,KAAK,IAAKA,KAAK,KAAK7C,EAAE,CAACgD,MAAM,CAAC;MAC9D,MAAMoB,OAAO,GAAG3B,MAAM,CAACyB,KAAK,CAAC;MAC7B,IAAIG,IAAI;MACR;MACA;MACA,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACJ,QAAQ,CAACjE,EAAE,CAACwB,GAAG,CAAC,EAAE;QAC9C6C,IAAI,GAAGH,KAAK,KAAKzB,MAAM,CAAC6B,MAAM,GAAG,CAAC,GAAG7B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACyB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACD,QAAQ,CAACjE,EAAE,CAACwB,GAAG,CAAC,EAAE;QAC3C6C,IAAI,GAAGH,KAAK,KAAK,CAAC,GAAGzB,MAAM,CAACA,MAAM,CAAC6B,MAAM,GAAG,CAAC,CAAC,GAAG7B,MAAM,CAACyB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA,IAAIG,IAAI,IAAI5B,MAAM,CAACwB,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC/BA,IAAI,CAACtE,QAAQ,CAACC,EAAE,CAAC;QACjB,IAAI,CAAC8D,iBAAiB,EAAE;UACpB,IAAI,CAACxE,KAAK,GAAG+E,IAAI,CAAC/E,KAAK;UACvB,IAAI,CAAC6D,eAAe,CAACnD,EAAE,CAAC;QAC5B;MACJ;MACA;MACA;MACA,IAAI,CAAC,GAAG,CAAC,CAACiE,QAAQ,CAACjE,EAAE,CAACwB,GAAG,CAAC,EAAE;QACxB,MAAM+C,aAAa,GAAG,IAAI,CAACjF,KAAK;QAChC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACG,mBAAmB,IAAI,IAAI,CAACH,KAAK,KAAKa,SAAS,GAAGA,SAAS,GAAGiE,OAAO,CAAC9E,KAAK;QAC7F,IAAIiF,aAAa,KAAK,IAAI,CAACjF,KAAK,IAAI,IAAI,CAACG,mBAAmB,EAAE;UAC1D;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAI,CAAC0D,eAAe,CAACnD,EAAE,CAAC;QAC5B;QACA;QACA;QACAA,EAAE,CAACK,cAAc,CAAC,CAAC;MACvB;IACJ;EACJ;EACA;EACMN,QAAQA,CAAA,EAAG;IAAA,IAAAyE,MAAA;IAAA,OAAAtE,yMAAA;MACb,MAAMuE,YAAY,GAAGD,MAAI,CAAC9B,SAAS,CAAC,CAAC,CAACE,IAAI,CAAE7F,CAAC,IAAKA,CAAC,CAAC2H,QAAQ,KAAK,CAAC,CAAC,CAAC;MACpED,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC1E,QAAQ,CAAC,CAAC;IAAC;EACxF;EACA;AACJ;AACA;EACI4E,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,UAAU;MAAEC,SAAS;MAAExC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE,MAAMwC,WAAW,GAAG,CAAC,CAACF,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,IAAI,CAACC,WAAW,EAAE;MACd;IACJ;IACA,OAAQ3H,qDAAC,CAAC,KAAK,EAAE;MAAE6D,KAAK,EAAE;IAAkB,CAAC,EAAE7D,qDAAC,CAAC,KAAK,EAAE;MAAEqG,EAAE,EAAEnB,YAAY;MAAErB,KAAK,EAAE;IAAc,CAAC,EAAE4D,UAAU,CAAC,EAAEzH,qDAAC,CAAC,KAAK,EAAE;MAAEqG,EAAE,EAAElB,WAAW;MAAEtB,KAAK,EAAE;IAAa,CAAC,EAAE6D,SAAS,CAAC,CAAC;EACnL;EACAE,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEzE,EAAE;MAAEsE,UAAU;MAAEC,SAAS;MAAExC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAIhC,EAAE,CAAC0E,SAAS,CAACjB,QAAQ,CAAC,aAAa,CAAC,IAAIzD,EAAE,CAAC0E,SAAS,CAACjB,QAAQ,CAAC,aAAa,CAAC,IAAIc,SAAS,EAAE;MAC3F,OAAOvC,WAAW;IACtB;IACA,IAAIsC,UAAU,EAAE;MACZ,OAAOvC,YAAY;IACvB;IACA,OAAOlC,SAAS;EACpB;EACAe,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEqC,KAAK;MAAEhB,OAAO;MAAEjC,EAAE;MAAErB,IAAI;MAAEK;IAAM,CAAC,GAAG,IAAI;IAChD,MAAMgC,IAAI,GAAGjE,qDAAU,CAAC,IAAI,CAAC;IAC7BU,uDAAiB,CAAC,IAAI,EAAEuC,EAAE,EAAErB,IAAI,EAAEK,KAAK,EAAE,KAAK,CAAC;IAC/C,OAAQnC,qDAAC,CAACI,iDAAI,EAAE;MAAEiE,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,YAAY;MAAE,iBAAiB,EAAE8B,KAAK,GAAGhB,OAAO,GAAG,IAAI;MAAE,kBAAkB,EAAE,IAAI,CAACwC,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAK,IAAI,CAACzC,WAAW;MAAE9C,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwB,KAAK,EAAEM;IAAK,CAAC,EAAE,IAAI,CAACqD,cAAc,CAAC,CAAC,EAAExH,qDAAC,CAAC,KAAK,EAAE;MAAEqE,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;IAAsB,CAAC,EAAE7D,qDAAC,CAAC,MAAM,EAAE;MAAEqE,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACpc;EACA,IAAIlB,EAAEA,CAAA,EAAG;IAAE,OAAO7C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIS,aAAa,GAAG,CAAC;AACrBH,UAAU,CAACL,KAAK,GAAG;EACfC,GAAG,EAAEE,gBAAgB;EACrBD,EAAE,EAAEE;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-radio_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as addEventListener, m as removeEventListener, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { i as isOptionSelected } from './compare-with-utils-sObYyvOy.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst radioIosCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color-checked:var(--ion-color-primary, #0054e9)}:host(.ion-color.radio-checked) .radio-inner{border-color:var(--ion-color-base)}.item-radio.item-ios ion-label{-webkit-margin-start:0;margin-inline-start:0}.radio-inner{width:33%;height:50%}:host(.radio-checked) .radio-inner{-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--color-checked)}:host(.radio-disabled){opacity:0.3}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);top:-8px;display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}:host(.ion-focused) .radio-icon::after{inset-inline-start:-9px}.native-wrapper .radio-icon{width:0.9375rem;height:1.5rem}\";\n\nconst radioMdCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--border-width:0.125rem;--border-style:solid;--border-radius:50%}:host(.ion-color) .radio-inner{background:var(--ion-color-base)}:host(.ion-color.radio-checked) .radio-icon{border-color:var(--ion-color-base)}.radio-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--color)}.radio-inner{border-radius:var(--inner-border-radius);width:calc(50% + var(--border-width));height:calc(50% + var(--border-width));-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background:var(--color-checked)}:host(.radio-checked) .radio-icon{border-color:var(--color-checked)}:host(.radio-checked) .radio-inner{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}:host(.radio-disabled) .label-text-wrapper{opacity:0.38}:host(.radio-disabled) .native-wrapper{opacity:0.63}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}.native-wrapper .radio-icon{width:1.25rem;height:1.25rem}\";\n\nconst Radio = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-rb-${radioButtonIds++}`;\n        this.radioGroup = null;\n        /**\n         * If `true`, the radio is selected.\n         */\n        this.checked = false;\n        /**\n         * The tabindex of the radio button.\n         * @internal\n         */\n        this.buttonTabindex = -1;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the user cannot interact with the radio.\n         */\n        this.disabled = false;\n        /**\n         * Where to place the label relative to the radio.\n         * `\"start\"`: The label will appear to the left of the radio in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the radio in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the radio regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n         */\n        this.labelPlacement = 'start';\n        this.updateState = () => {\n            if (this.radioGroup) {\n                const { compareWith, value: radioGroupValue } = this.radioGroup;\n                this.checked = isOptionSelected(radioGroupValue, this.value, compareWith);\n            }\n        };\n        this.onClick = () => {\n            const { radioGroup, checked, disabled } = this;\n            if (disabled) {\n                return;\n            }\n            /**\n             * The modern control does not use a native input\n             * inside of the radio host, so we cannot rely on the\n             * ev.preventDefault() behavior above. If the radio\n             * is checked and the parent radio group allows for empty\n             * selection, then we can set the checked state to false.\n             * Otherwise, the checked state should always be set\n             * to true because the checked state cannot be toggled.\n             */\n            if (checked && (radioGroup === null || radioGroup === void 0 ? void 0 : radioGroup.allowEmptySelection)) {\n                this.checked = false;\n            }\n            else {\n                this.checked = true;\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n    }\n    valueChanged() {\n        /**\n         * The new value of the radio may\n         * match the radio group's value,\n         * so we see if it should be checked.\n         */\n        this.updateState();\n    }\n    componentDidLoad() {\n        /**\n         * The value may be `undefined` if it\n         * gets set before the radio is\n         * rendered. This ensures that the radio\n         * is checked if the value matches. This\n         * happens most often when Angular is\n         * rendering the radio.\n         */\n        this.updateState();\n    }\n    /** @internal */\n    async setFocus(ev) {\n        if (ev !== undefined) {\n            ev.stopPropagation();\n            ev.preventDefault();\n        }\n        this.el.focus();\n    }\n    /** @internal */\n    async setButtonTabindex(value) {\n        this.buttonTabindex = value;\n    }\n    connectedCallback() {\n        if (this.value === undefined) {\n            this.value = this.inputId;\n        }\n        const radioGroup = (this.radioGroup = this.el.closest('ion-radio-group'));\n        if (radioGroup) {\n            this.updateState();\n            addEventListener(radioGroup, 'ionValueChange', this.updateState);\n        }\n    }\n    disconnectedCallback() {\n        const radioGroup = this.radioGroup;\n        if (radioGroup) {\n            removeEventListener(radioGroup, 'ionValueChange', this.updateState);\n            this.radioGroup = null;\n        }\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    renderRadioControl() {\n        return (h(\"div\", { class: \"radio-icon\", part: \"container\" }, h(\"div\", { class: \"radio-inner\", part: \"mark\" }), h(\"div\", { class: \"radio-ripple\" })));\n    }\n    render() {\n        const { checked, disabled, color, el, justify, labelPlacement, hasLabel, buttonTabindex, alignment } = this;\n        const mode = getIonMode(this);\n        const inItem = hostContext('ion-item', el);\n        return (h(Host, { key: '3353b28172b7f837d4b38964169b5b5f4ba02788', onFocus: this.onFocus, onBlur: this.onBlur, onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': inItem,\n                'radio-checked': checked,\n                'radio-disabled': disabled,\n                [`radio-justify-${justify}`]: justify !== undefined,\n                [`radio-alignment-${alignment}`]: alignment !== undefined,\n                [`radio-label-placement-${labelPlacement}`]: true,\n                // Focus and active styling should not apply when the radio is in an item\n                'ion-activatable': !inItem,\n                'ion-focusable': !inItem,\n            }), role: \"radio\", \"aria-checked\": checked ? 'true' : 'false', \"aria-disabled\": disabled ? 'true' : null, tabindex: buttonTabindex }, h(\"label\", { key: '418a0a48366ff900e97da123abf665bbbda87fb7', class: \"radio-wrapper\" }, h(\"div\", { key: '6e5acdd8c8f5d0ad26632a65396afef8094153d1', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, h(\"slot\", { key: '10b157162cd283d624153c747679609cf0bbf11e' })), h(\"div\", { key: '4c45cca95cb105cd6df1025a26e3c045272184a0', class: \"native-wrapper\" }, this.renderRadioControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioButtonIds = 0;\nRadio.style = {\n    ios: radioIosCss,\n    md: radioMdCss\n};\n\nconst radioGroupIosCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\n\nconst radioGroupMdCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\n\nconst RadioGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.inputId = `ion-rg-${radioGroupIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.labelId = `${this.inputId}-lbl`;\n        /**\n         * If `true`, the radios can be deselected.\n         */\n        this.allowEmptySelection = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        this.setRadioTabindex = (value) => {\n            const radios = this.getRadios();\n            // Get the first radio that is not disabled and the checked one\n            const first = radios.find((radio) => !radio.disabled);\n            const checked = radios.find((radio) => radio.value === value && !radio.disabled);\n            if (!first && !checked) {\n                return;\n            }\n            // If an enabled checked radio exists, set it to be the focusable radio\n            // otherwise we default to focus the first radio\n            const focusable = checked || first;\n            for (const radio of radios) {\n                const tabindex = radio === focusable ? 0 : -1;\n                radio.setButtonTabindex(tabindex);\n            }\n        };\n        this.onClick = (ev) => {\n            ev.preventDefault();\n            /**\n             * The Radio Group component mandates that only one radio button\n             * within the group can be selected at any given time. Since `ion-radio`\n             * is a shadow DOM component, it cannot natively perform this behavior\n             * using the `name` attribute.\n             */\n            const selectedRadio = ev.target && ev.target.closest('ion-radio');\n            /**\n             * Our current disabled prop definition causes Stencil to mark it\n             * as optional. While this is not desired, fixing this behavior\n             * in Stencil is a significant breaking change, so this effort is\n             * being de-risked in STENCIL-917. Until then, we compromise\n             * here by checking for falsy `disabled` values instead of strictly\n             * checking `disabled === false`.\n             */\n            if (selectedRadio && !selectedRadio.disabled) {\n                const currentValue = this.value;\n                const newValue = selectedRadio.value;\n                if (newValue !== currentValue) {\n                    this.value = newValue;\n                    this.emitValueChange(ev);\n                }\n                else if (this.allowEmptySelection) {\n                    this.value = undefined;\n                    this.emitValueChange(ev);\n                }\n            }\n        };\n    }\n    valueChanged(value) {\n        this.setRadioTabindex(value);\n        this.ionValueChange.emit({ value });\n    }\n    componentDidLoad() {\n        /**\n         * There's an issue when assigning a value to the radio group\n         * within the Angular primary content (rendering within the\n         * app component template). When the template is isolated to a route,\n         * the value is assigned correctly.\n         * To address this issue, we need to ensure that the watcher is\n         * called after the component has finished loading,\n         * allowing the emit to be dispatched correctly.\n         */\n        this.valueChanged(this.value);\n    }\n    async connectedCallback() {\n        // Get the list header if it exists and set the id\n        // this is used to set aria-labelledby\n        const header = this.el.querySelector('ion-list-header') || this.el.querySelector('ion-item-divider');\n        if (header) {\n            const label = (this.label = header.querySelector('ion-label'));\n            if (label) {\n                this.labelId = label.id = this.name + '-lbl';\n            }\n        }\n    }\n    getRadios() {\n        return Array.from(this.el.querySelectorAll('ion-radio'));\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        this.ionChange.emit({ value, event });\n    }\n    onKeydown(ev) {\n        // We don't want the value to automatically change/emit when the radio group is part of a select interface\n        // as this will cause the interface to close when navigating through the radio group options\n        const inSelectInterface = !!this.el.closest('ion-select-popover') || !!this.el.closest('ion-select-modal');\n        if (ev.target && !this.el.contains(ev.target)) {\n            return;\n        }\n        // Get all radios inside of the radio group and then\n        // filter out disabled radios since we need to skip those\n        const radios = this.getRadios().filter((radio) => !radio.disabled);\n        // Only move the radio if the current focus is in the radio group\n        if (ev.target && radios.includes(ev.target)) {\n            const index = radios.findIndex((radio) => radio === ev.target);\n            const current = radios[index];\n            let next;\n            // If hitting arrow down or arrow right, move to the next radio\n            // If we're on the last radio, move to the first radio\n            if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n                next = index === radios.length - 1 ? radios[0] : radios[index + 1];\n            }\n            // If hitting arrow up or arrow left, move to the previous radio\n            // If we're on the first radio, move to the last radio\n            if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n                next = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n            }\n            if (next && radios.includes(next)) {\n                next.setFocus(ev);\n                if (!inSelectInterface) {\n                    this.value = next.value;\n                    this.emitValueChange(ev);\n                }\n            }\n            // Update the radio group value when a user presses the\n            // space bar on top of a selected radio\n            if ([' '].includes(ev.key)) {\n                const previousValue = this.value;\n                this.value = this.allowEmptySelection && this.value !== undefined ? undefined : current.value;\n                if (previousValue !== this.value || this.allowEmptySelection) {\n                    /**\n                     * Value change should only be emitted if the value is different,\n                     * such as selecting a new radio with the space bar or if\n                     * the radio group allows for empty selection and the user\n                     * is deselecting a checked radio.\n                     */\n                    this.emitValueChange(ev);\n                }\n                // Prevent browsers from jumping\n                // to the bottom of the screen\n                ev.preventDefault();\n            }\n        }\n    }\n    /** @internal */\n    async setFocus() {\n        const radioToFocus = this.getRadios().find((r) => r.tabIndex !== -1);\n        radioToFocus === null || radioToFocus === void 0 ? void 0 : radioToFocus.setFocus();\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return (h(\"div\", { class: \"radio-group-top\" }, h(\"div\", { id: helperTextId, class: \"helper-text\" }, helperText), h(\"div\", { id: errorTextId, class: \"error-text\" }, errorText)));\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    render() {\n        const { label, labelId, el, name, value } = this;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, name, value, false);\n        return (h(Host, { key: '81b8ebc96b2f383c36717f290d2959cc921ad6e8', role: \"radiogroup\", \"aria-labelledby\": label ? labelId : null, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId, onClick: this.onClick, class: mode }, this.renderHintText(), h(\"div\", { key: '45b09efc10776b889a8f372cba80d25a3fc849da', class: \"radio-group-wrapper\" }, h(\"slot\", { key: '58714934542c2fdd7396de160364f3f06b32e8f8' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioGroupIds = 0;\nRadioGroup.style = {\n    ios: radioGroupIosCss,\n    md: radioGroupMdCss\n};\n\nexport { Radio as ion_radio, RadioGroup as ion_radio_group };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "f", "addEventListener", "m", "removeEventListener", "a", "renderHiddenInput", "i", "isOptionSelected", "hostContext", "c", "createColorClasses", "radioIosCss", "radioMdCss", "Radio", "constructor", "hostRef", "ionFocus", "ionBlur", "inputId", "radioButtonIds", "radioGroup", "checked", "buttonTabindex", "name", "disabled", "labelPlacement", "updateState", "compareWith", "value", "radioGroupValue", "onClick", "allowEmptySelection", "onFocus", "emit", "onBlur", "valueChanged", "componentDidLoad", "setFocus", "ev", "_this", "_asyncToGenerator", "undefined", "stopPropagation", "preventDefault", "el", "focus", "setButtonTabindex", "_this2", "connectedCallback", "closest", "disconnectedCallback", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "renderRadioControl", "class", "part", "render", "color", "justify", "alignment", "mode", "inItem", "key", "role", "tabindex", "watchers", "style", "ios", "md", "radioGroupIosCss", "radioGroupMdCss", "RadioGroup", "ionChange", "ionValueChange", "radioGroupIds", "helperTextId", "errorTextId", "labelId", "setRadioTabindex", "radios", "getRadios", "first", "find", "radio", "focusable", "selectedRadio", "target", "currentValue", "newValue", "emitValueChange", "_this3", "header", "querySelector", "label", "id", "Array", "from", "querySelectorAll", "event", "onKeydown", "inSelectInterface", "contains", "filter", "includes", "index", "findIndex", "current", "next", "length", "previousValue", "_this4", "radioToFocus", "tabIndex", "renderHintText", "helperText", "errorText", "hasHintText", "getHintTextID", "classList", "ion_radio", "ion_radio_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}