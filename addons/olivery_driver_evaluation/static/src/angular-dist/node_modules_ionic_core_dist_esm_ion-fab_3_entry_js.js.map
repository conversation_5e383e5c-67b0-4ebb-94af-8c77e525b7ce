{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-fab_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8H;AAC3D;AAC2B;AAC7C;AAEjD,MAAMoB,MAAM,GAAG,42EAA42E;AAE33E,MAAMC,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACAC,gBAAgBA,CAAA,EAAG;IACf,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAME,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;IACzB,IAAID,GAAG,EAAE;MACLA,GAAG,CAACF,SAAS,GAAGA,SAAS;IAC7B;IACAI,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,EAAE,CAACC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;MACnEA,IAAI,CAACT,SAAS,GAAGA,SAAS;IAC9B,CAAC,CAAC;EACN;EACAU,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACV,SAAS,EAAE;MAChB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA;AACJ;AACA;EACUP,KAAKA,CAAA,EAAG;IAAA,IAAAiB,KAAA;IAAA,OAAAC,yMAAA;MACVD,KAAI,CAACX,SAAS,GAAG,KAAK;IAAC;EAC3B;EACAG,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACG,EAAE,CAACO,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACUC,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,yMAAA;MACX,MAAMI,OAAO,GAAG,CAAC,CAACD,MAAI,CAACT,EAAE,CAACO,aAAa,CAAC,cAAc,CAAC;MACvD,IAAIG,OAAO,EAAE;QACTD,MAAI,CAACf,SAAS,GAAG,CAACe,MAAI,CAACf,SAAS;MACpC;IAAC;EACL;EACAiB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,UAAU;MAAEC,QAAQ;MAAEpB;IAAK,CAAC,GAAG,IAAI;IAC3C,MAAMqB,IAAI,GAAG1C,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,CAAC,kBAAkBF,UAAU,EAAE,GAAGA,UAAU,KAAKK,SAAS;QAC1D,CAAC,gBAAgBJ,QAAQ,EAAE,GAAGA,QAAQ,KAAKI,SAAS;QACpD,UAAU,EAAExB;MAChB;IAAE,CAAC,EAAEpB,qDAAC,CAAC,MAAM,EAAE;MAAE0C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAOvB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWyC,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,WAAW,EAAE,CAAC,kBAAkB;IACpC,CAAC;EAAE;AACP,CAAC;AACD5B,GAAG,CAAC6B,KAAK,GAAG9B,MAAM;AAElB,MAAM+B,eAAe,GAAG,mqOAAmqO;AAE3rO,MAAMC,cAAc,GAAG,yqMAAyqM;AAEhsM,MAAMC,SAAS,GAAG,MAAM;EACpB/B,WAAWA,CAACC,OAAO,EAAE;IACjBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAAC+B,QAAQ,GAAG5C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC6C,OAAO,GAAG7C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACiB,GAAG,GAAG,IAAI;IACf,IAAI,CAAC6B,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAAC/B,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACgC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG3C,iDAAK;IACtB,IAAI,CAAC4C,OAAO,GAAG,MAAM;MACjB,IAAI,CAACT,QAAQ,CAACU,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACV,OAAO,CAACS,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,OAAO,GAAG,MAAM;MACjB,MAAM;QAAEvC;MAAI,CAAC,GAAG,IAAI;MACpB,IAAI,CAACA,GAAG,EAAE;QACN;MACJ;MACAA,GAAG,CAACY,MAAM,CAAC,CAAC;IAChB,CAAC;EACL;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxC,GAAG,GAAG,IAAI,CAACI,EAAE,CAACqC,OAAO,CAAC,SAAS,CAAC;EACzC;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACb,mBAAmB,GAAG5C,uDAAqB,CAAC,IAAI,CAACmB,EAAE,CAAC;EAC7D;EACAW,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEX,EAAE;MAAE0B,QAAQ;MAAEa,KAAK;MAAEC,IAAI;MAAE9C,SAAS;MAAEkC,IAAI;MAAEC,WAAW;MAAEY,IAAI;MAAEhB;IAAoB,CAAC,GAAG,IAAI;IACnG,MAAMiB,MAAM,GAAG5D,qDAAW,CAAC,cAAc,EAAEkB,EAAE,CAAC;IAC9C,MAAMc,IAAI,GAAG1C,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuE,OAAO,GAAGH,IAAI,KAAKvB,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAM2B,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAEb,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACEe,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBL,IAAI;MACJM,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;IACL,OAAQ1E,qDAAC,CAACE,iDAAI,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEoB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAET,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEV,KAAK,EAAE9B,qDAAkB,CAACqD,KAAK,EAAE;QAC9J,CAACzB,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE4B,MAAM;QAC5B,gCAAgC,EAAEA,MAAM,IAAIb,WAAW;QACvD,yBAAyB,EAAEnC,SAAS;QACpC,iBAAiB,EAAEkC,IAAI;QACvB,qBAAqB,EAAEF,QAAQ;QAC/B,wBAAwB,EAAEG,WAAW;QACrC,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,CAAC,cAAcY,IAAI,EAAE,GAAGA,IAAI,KAAKxB;MACrC,CAAC;IAAE,CAAC,EAAE5C,qDAAC,CAACsE,OAAO,EAAEK,MAAM,CAACC,MAAM,CAAC;MAAElC,GAAG,EAAE;IAA2C,CAAC,EAAE6B,KAAK,EAAE;MAAE5B,KAAK,EAAE,eAAe;MAAEkC,IAAI,EAAE,QAAQ;MAAExB,QAAQ,EAAEA,QAAQ;MAAEM,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,OAAO,EAAGgB,EAAE,IAAKnE,qDAAO,CAACwD,IAAI,EAAEW,EAAE,EAAE,IAAI,CAACxB,eAAe,EAAE,IAAI,CAACyB,eAAe;IAAE,CAAC,EAAE3B,mBAAmB,CAAC,EAAEpD,qDAAC,CAAC,UAAU,EAAE;MAAE0C,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEsC,IAAI,EAAE,IAAI,CAACtB,SAAS;MAAEmB,IAAI,EAAE,YAAY;MAAElC,KAAK,EAAE,YAAY;MAAEsC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAEjF,qDAAC,CAAC,MAAM,EAAE;MAAE0C,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAE3C,qDAAC,CAAC,MAAM,EAAE;MAAE0C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAED,IAAI,KAAK,IAAI,IAAIzC,qDAAC,CAAC,mBAAmB,EAAE;MAAE0C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACjtB;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAOvB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD6C,SAAS,CAACH,KAAK,GAAG;EACdoC,GAAG,EAAEnC,eAAe;EACpBoC,EAAE,EAAEnC;AACR,CAAC;AAED,MAAMoC,UAAU,GAAG,4tDAA4tD;AAE/uD,MAAMC,OAAO,GAAG,MAAM;EAClBnE,WAAWA,CAACC,OAAO,EAAE;IACjBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACiE,IAAI,GAAG,QAAQ;EACxB;EACAhE,gBAAgBA,CAACD,SAAS,EAAE;IACxB,MAAMkE,IAAI,GAAG9D,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,EAAE,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACnE;IACA,MAAM4D,OAAO,GAAGnE,SAAS,GAAG,EAAE,GAAG,CAAC;IAClCkE,IAAI,CAAC1D,OAAO,CAAC,CAACN,GAAG,EAAEhB,CAAC,KAAK;MACrBkF,UAAU,CAAC,MAAOlE,GAAG,CAACgC,IAAI,GAAGlC,SAAU,EAAEd,CAAC,GAAGiF,OAAO,CAAC;IACzD,CAAC,CAAC;EACN;EACAlD,MAAMA,CAAA,EAAG;IACL,MAAMG,IAAI,GAAG1C,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE,IAAI,CAACpB,SAAS;QACjC,CAAC,iBAAiB,IAAI,CAACiE,IAAI,EAAE,GAAG;MACpC;IAAE,CAAC,EAAEtF,qDAAC,CAAC,MAAM,EAAE;MAAE0C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAOvB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWyC,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,WAAW,EAAE,CAAC,kBAAkB;IACpC,CAAC;EAAE;AACP,CAAC;AACDwC,OAAO,CAACvC,KAAK,GAAGsC,UAAU", "sources": ["./node_modules/@ionic/core/dist/esm/ion-fab_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement, d as createEvent } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { h as hostContext, o as openURL, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { t as close } from './index-BLV6ykCk.js';\n\nconst fabCss = \":host{position:absolute;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;z-index:999}:host(.fab-horizontal-center){left:0px;right:0px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}:host(.fab-horizontal-start){left:calc(10px + var(--ion-safe-area-left, 0px));}:host-context([dir=rtl]):host(.fab-horizontal-start),:host-context([dir=rtl]).fab-horizontal-start{right:calc(10px + var(--ion-safe-area-right, 0px));left:unset}@supports selector(:dir(rtl)){:host(.fab-horizontal-start:dir(rtl)){right:calc(10px + var(--ion-safe-area-right, 0px));left:unset}}:host(.fab-horizontal-end){right:calc(10px + var(--ion-safe-area-right, 0px));}:host-context([dir=rtl]):host(.fab-horizontal-end),:host-context([dir=rtl]).fab-horizontal-end{left:calc(10px + var(--ion-safe-area-left, 0px));right:unset}@supports selector(:dir(rtl)){:host(.fab-horizontal-end:dir(rtl)){left:calc(10px + var(--ion-safe-area-left, 0px));right:unset}}:host(.fab-vertical-top){top:10px}:host(.fab-vertical-top.fab-edge){top:0}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-button){margin-top:-50%}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-button.fab-button-small){margin-top:calc((-100% + 16px) / 2)}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-start),:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-end){margin-top:-50%}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-top),:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-bottom){margin-top:calc(50% + 10px)}:host(.fab-vertical-bottom){bottom:10px}:host(.fab-vertical-bottom.fab-edge){bottom:0}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-button){margin-bottom:-50%}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-button.fab-button-small){margin-bottom:calc((-100% + 16px) / 2)}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-start),:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-end){margin-bottom:-50%}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-top),:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-bottom){margin-bottom:calc(50% + 10px)}:host(.fab-vertical-center){top:0px;bottom:0px;margin-top:auto;margin-bottom:auto}\";\n\nconst Fab = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the fab will display on the edge of the header if\n         * `vertical` is `\"top\"`, and on the edge of the footer if\n         * it is `\"bottom\"`. Should be used with a `fixed` slot.\n         */\n        this.edge = false;\n        /**\n         * If `true`, both the `ion-fab-button` and all `ion-fab-list` inside `ion-fab` will become active.\n         * That means `ion-fab-button` will become a `close` icon and `ion-fab-list` will become visible.\n         */\n        this.activated = false;\n    }\n    activatedChanged() {\n        const activated = this.activated;\n        const fab = this.getFab();\n        if (fab) {\n            fab.activated = activated;\n        }\n        Array.from(this.el.querySelectorAll('ion-fab-list')).forEach((list) => {\n            list.activated = activated;\n        });\n    }\n    componentDidLoad() {\n        if (this.activated) {\n            this.activatedChanged();\n        }\n    }\n    /**\n     * Close an active FAB list container.\n     */\n    async close() {\n        this.activated = false;\n    }\n    getFab() {\n        return this.el.querySelector('ion-fab-button');\n    }\n    /**\n     * Opens/Closes the FAB list container.\n     * @internal\n     */\n    async toggle() {\n        const hasList = !!this.el.querySelector('ion-fab-list');\n        if (hasList) {\n            this.activated = !this.activated;\n        }\n    }\n    render() {\n        const { horizontal, vertical, edge } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '8a310806d0e748d7ebb0ed3d9a2652038e0f2960', class: {\n                [mode]: true,\n                [`fab-horizontal-${horizontal}`]: horizontal !== undefined,\n                [`fab-vertical-${vertical}`]: vertical !== undefined,\n                'fab-edge': edge,\n            } }, h(\"slot\", { key: '9394ef6d6e5b0410fa6ba212171f687fb178ce2d' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"activated\": [\"activatedChanged\"]\n    }; }\n};\nFab.style = fabCss;\n\nconst fabButtonIosCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #0054e9);--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transition:0.2s transform cubic-bezier(0.25, 1.11, 0.78, 1.59);--close-icon-font-size:28px}:host(.ion-activated){--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transform:scale(1.1);--transition:0.2s transform ease-out}::slotted(ion-icon){font-size:28px}:host(.fab-button-in-list){--background:var(--ion-color-light, #f4f5f8);--background-activated:var(--ion-color-light-shade, #d7d8da);--background-focused:var(--background-activated);--background-hover:var(--ion-color-light-tint, #f5f6f9);--color:var(--ion-color-light-contrast, #000);--color-activated:var(--ion-color-light-contrast, #000);--color-focused:var(--color-activated);--transition:transform 200ms ease 10ms, opacity 200ms ease 10ms}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}:host(.ion-color.ion-focused) .button-native,:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after,:host(.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent){--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.9);--background-hover:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.8);--background-focused:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.82);--backdrop-filter:saturate(180%) blur(20px)}:host(.fab-button-translucent-in-list){--background:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.9);--background-hover:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.8);--background-focused:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.82)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){@media (any-hover: hover){:host(.fab-button-translucent.ion-color:hover) .button-native{background:rgba(var(--ion-color-base-rgb), 0.8)}}:host(.ion-color.fab-button-translucent) .button-native{background:rgba(var(--ion-color-base-rgb), 0.9)}:host(.ion-color.ion-focused.fab-button-translucent) .button-native,:host(.ion-color.ion-activated.fab-button-translucent) .button-native{background:var(--ion-color-base)}}\";\n\nconst fabButtonMdCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #0054e9);--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);--transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), background-color 280ms cubic-bezier(0.4, 0, 0.2, 1), color 280ms cubic-bezier(0.4, 0, 0.2, 1), opacity 15ms linear 30ms, transform 270ms cubic-bezier(0, 0, 0.2, 1) 0ms;--close-icon-font-size:24px}:host(.ion-activated){--box-shadow:0 7px 8px -4px rgba(0, 0, 0, 0.2), 0 12px 17px 2px rgba(0, 0, 0, 0.14), 0 5px 22px 4px rgba(0, 0, 0, 0.12)}::slotted(ion-icon){font-size:24px}:host(.fab-button-in-list){--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-activated:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-focused:var(--color-activated);--background:var(--ion-color-light, #f4f5f8);--background-activated:transparent;--background-focused:var(--ion-color-light-shade, #d7d8da);--background-hover:var(--ion-color-light-tint, #f5f6f9)}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native::after{background:transparent}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}}\";\n\nconst FabButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.fab = null;\n        this.inheritedAttributes = {};\n        /**\n         * If `true`, the fab button will be show a close icon.\n         */\n        this.activated = false;\n        /**\n         * If `true`, the user cannot interact with the fab button.\n         */\n        this.disabled = false;\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        /**\n         * If `true`, the fab button will show when in a fab-list.\n         */\n        this.show = false;\n        /**\n         * If `true`, the fab button will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        /**\n         * The icon name to use for the close icon. This will appear when the fab button\n         * is pressed. Only applies if it is the main button inside of a fab containing a\n         * fab list.\n         */\n        this.closeIcon = close;\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.onClick = () => {\n            const { fab } = this;\n            if (!fab) {\n                return;\n            }\n            fab.toggle();\n        };\n    }\n    connectedCallback() {\n        this.fab = this.el.closest('ion-fab');\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    render() {\n        const { el, disabled, color, href, activated, show, translucent, size, inheritedAttributes } = this;\n        const inList = hostContext('ion-fab-list', el);\n        const mode = getIonMode(this);\n        const TagType = href === undefined ? 'button' : 'a';\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download: this.download,\n                href,\n                rel: this.rel,\n                target: this.target,\n            };\n        return (h(Host, { key: '4eee204d20b0e2ffed49a88f6cb3e04b6697965c', onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                'fab-button-in-list': inList,\n                'fab-button-translucent-in-list': inList && translucent,\n                'fab-button-close-active': activated,\n                'fab-button-show': show,\n                'fab-button-disabled': disabled,\n                'fab-button-translucent': translucent,\n                'ion-activatable': true,\n                'ion-focusable': true,\n                [`fab-button-${size}`]: size !== undefined,\n            }) }, h(TagType, Object.assign({ key: '914561622c0c6bd41453e828a7d8a39f924875ac' }, attrs, { class: \"button-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur, onClick: (ev) => openURL(href, ev, this.routerDirection, this.routerAnimation) }, inheritedAttributes), h(\"ion-icon\", { key: '2c8090742a64c62a79243667027a195cca9d5912', \"aria-hidden\": \"true\", icon: this.closeIcon, part: \"close-icon\", class: \"close-icon\", lazy: false }), h(\"span\", { key: 'c3e55291e4c4d306d34a4b95dd2e727e87bdf39c', class: \"button-inner\" }, h(\"slot\", { key: 'f8e57f71d8f8878d9746cfece82f57f19ef9e988' })), mode === 'md' && h(\"ion-ripple-effect\", { key: 'a5e94fa0bb9836072300617245ed0c1b4887bac6' }))));\n    }\n    get el() { return getElement(this); }\n};\nFabButton.style = {\n    ios: fabButtonIosCss,\n    md: fabButtonMdCss\n};\n\nconst fabListCss = \":host{margin-left:0;margin-right:0;margin-top:calc(100% + 10px);margin-bottom:calc(100% + 10px);display:none;position:absolute;top:0;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;min-width:56px;min-height:56px}:host(.fab-list-active){display:-ms-flexbox;display:flex}::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:8px;margin-bottom:8px;width:40px;height:40px;-webkit-transform:scale(0);transform:scale(0);opacity:0;visibility:hidden}:host(.fab-list-side-top) ::slotted(.fab-button-in-list),:host(.fab-list-side-bottom) ::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px}:host(.fab-list-side-start) ::slotted(.fab-button-in-list),:host(.fab-list-side-end) ::slotted(.fab-button-in-list){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted(.fab-button-in-list.fab-button-show){-webkit-transform:scale(1);transform:scale(1);opacity:1;visibility:visible}:host(.fab-list-side-top){top:auto;bottom:0;-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.fab-list-side-start){-webkit-margin-start:calc(100% + 10px);margin-inline-start:calc(100% + 10px);-webkit-margin-end:calc(100% + 10px);margin-inline-end:calc(100% + 10px);margin-top:0;margin-bottom:0;-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.fab-list-side-start){inset-inline-end:0}:host(.fab-list-side-end){-webkit-margin-start:calc(100% + 10px);margin-inline-start:calc(100% + 10px);-webkit-margin-end:calc(100% + 10px);margin-inline-end:calc(100% + 10px);margin-top:0;margin-bottom:0;-ms-flex-direction:row;flex-direction:row}:host(.fab-list-side-end){inset-inline-start:0}\";\n\nconst FabList = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the fab list will show all fab buttons in the list.\n         */\n        this.activated = false;\n        /**\n         * The side the fab list will show on relative to the main fab button.\n         */\n        this.side = 'bottom';\n    }\n    activatedChanged(activated) {\n        const fabs = Array.from(this.el.querySelectorAll('ion-fab-button'));\n        // if showing the fabs add a timeout, else show immediately\n        const timeout = activated ? 30 : 0;\n        fabs.forEach((fab, i) => {\n            setTimeout(() => (fab.show = activated), i * timeout);\n        });\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '64b33366447f66c7f979cfac56307fbb1a6fac1c', class: {\n                [mode]: true,\n                'fab-list-active': this.activated,\n                [`fab-list-side-${this.side}`]: true,\n            } }, h(\"slot\", { key: 'd9f474f7f20fd7e813db358fddc720534ca05bb6' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"activated\": [\"activatedChanged\"]\n    }; }\n};\nFabList.style = fabListCss;\n\nexport { Fab as ion_fab, FabButton as ion_fab_button, FabList as ion_fab_list };\n"], "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "d", "createEvent", "i", "inheritAriaAttributes", "hostContext", "o", "openURL", "c", "createColorClasses", "t", "close", "fabCss", "Fab", "constructor", "hostRef", "edge", "activated", "activatedChanged", "fab", "getFab", "Array", "from", "el", "querySelectorAll", "for<PERSON>ach", "list", "componentDidLoad", "_this", "_asyncToGenerator", "querySelector", "toggle", "_this2", "hasList", "render", "horizontal", "vertical", "mode", "key", "class", "undefined", "watchers", "style", "fabButtonIosCss", "fabButtonMdCss", "FabButton", "ionFocus", "ionBlur", "inheritedAttributes", "disabled", "routerDirection", "show", "translucent", "type", "closeIcon", "onFocus", "emit", "onBlur", "onClick", "connectedCallback", "closest", "componentWillLoad", "color", "href", "size", "inList", "TagType", "attrs", "download", "rel", "target", "Object", "assign", "part", "ev", "routerAnimation", "icon", "lazy", "ios", "md", "fabListCss", "FabList", "side", "fabs", "timeout", "setTimeout", "ion_fab", "ion_fab_button", "ion_fab_list"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}