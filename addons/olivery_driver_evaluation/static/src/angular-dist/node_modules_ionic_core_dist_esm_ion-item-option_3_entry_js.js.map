{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACoJ;AACtF;AACP;AACgE;AACpD;AAEnE,MAAMuB,gBAAgB,GAAG,uhFAAuhF;AAEhjF,MAAMC,eAAe,GAAG,o6EAAo6E;AAE57E,MAAMC,UAAU,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACjB1B,qDAAgB,CAAC,IAAI,EAAE0B,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,EAAE,GAAGD,EAAE,CAACE,MAAM,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAC/C,IAAIF,EAAE,EAAE;QACJD,EAAE,CAACI,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;EACL;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAET,QAAQ;MAAEC,UAAU;MAAES;IAAK,CAAC,GAAG,IAAI;IAC3C,MAAMC,OAAO,GAAGD,IAAI,KAAKE,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAMC,IAAI,GAAGtC,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuC,KAAK,GAAGH,OAAO,KAAK,QAAQ,GAC5B;MAAET,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACEa,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBL,IAAI,EAAE,IAAI,CAACA,IAAI;MACfJ,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;IACL,OAAQ9B,qDAAC,CAACE,iDAAI,EAAE;MAAEsC,GAAG,EAAE,0CAA0C;MAAEb,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEc,KAAK,EAAE/B,qDAAkB,CAAC,IAAI,CAACgC,KAAK,EAAE;QACxH,CAACL,IAAI,GAAG,IAAI;QACZ,sBAAsB,EAAEb,QAAQ;QAChC,wBAAwB,EAAEC,UAAU;QACpC,iBAAiB,EAAE;MACvB,CAAC;IAAE,CAAC,EAAEzB,qDAAC,CAACmC,OAAO,EAAEQ,MAAM,CAACC,MAAM,CAAC;MAAEJ,GAAG,EAAE;IAA2C,CAAC,EAAEF,KAAK,EAAE;MAAEG,KAAK,EAAE,eAAe;MAAEI,IAAI,EAAE,QAAQ;MAAErB,QAAQ,EAAEA;IAAS,CAAC,CAAC,EAAExB,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAEzC,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE9C,qDAAC,CAAC,KAAK,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAqB,CAAC,EAAEzC,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE9C,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE9C,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAExC,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAE9C,qDAAC,CAAC,MAAM,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC,EAAET,IAAI,KAAK,IAAI,IAAIrC,qDAAC,CAAC,mBAAmB,EAAE;MAAEwC,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACj4B;EACA,IAAIX,EAAEA,CAAA,EAAG;IAAE,OAAOzB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDiB,UAAU,CAAC0B,KAAK,GAAG;EACfC,GAAG,EAAE7B,gBAAgB;EACrB8B,EAAE,EAAE7B;AACR,CAAC;AAED,MAAM8B,iBAAiB,GAAG,8pFAA8pF;AAExrF,MAAMC,gBAAgB,GAAG,kmFAAkmF;AAE3nF,MAAMC,WAAW,GAAG,MAAM;EACtB9B,WAAWA,CAACC,OAAO,EAAE;IACjB1B,qDAAgB,CAAC,IAAI,EAAE0B,OAAO,CAAC;IAC/B,IAAI,CAAC8B,QAAQ,GAAG/C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD;AACR;AACA;AACA;IACQ,IAAI,CAACgD,IAAI,GAAG,KAAK;EACrB;EACA;EACMC,cAAcA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,yMAAA;MACnBD,KAAI,CAACH,QAAQ,CAACK,IAAI,CAAC;QACfJ,IAAI,EAAEE,KAAI,CAACF;MACf,CAAC,CAAC;IAAC;EACP;EACArB,MAAMA,CAAA,EAAG;IACL,MAAMI,IAAI,GAAGtC,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4D,KAAK,GAAG/C,uDAAS,CAAC,IAAI,CAAC0C,IAAI,CAAC;IAClC,OAAQtD,qDAAC,CAACE,iDAAI,EAAE;MAAEsC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,gBAAgBA,IAAI,EAAE,GAAG,IAAI;QAC9B;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,oBAAoB,EAAE,CAACsB,KAAK;QAC5B,kBAAkB,EAAEA;MACxB;IAAE,CAAC,CAAC;EACZ;EACA,IAAI9B,EAAEA,CAAA,EAAG;IAAE,OAAOzB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDgD,WAAW,CAACL,KAAK,GAAG;EAChBC,GAAG,EAAEE,iBAAiB;EACtBD,EAAE,EAAEE;AACR,CAAC;AAED,MAAMS,cAAc,GAAG,++DAA++D;AAEtgE,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,cAAc,GAAG,IAAI;AAC3B,IAAIC,eAAe;AACnB,MAAMC,WAAW,GAAG,MAAM;EACtB1C,WAAWA,CAACC,OAAO,EAAE;IACjB1B,qDAAgB,CAAC,IAAI,EAAE0B,OAAO,CAAC;IAC/B,IAAI,CAAC0C,OAAO,GAAG3D,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC4D,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACnD,QAAQ,GAAG,KAAK;EACzB;EACAoD,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACtD,QAAQ,CAAC;IACvC;EACJ;EACMuD,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvB,yMAAA;MACtB,MAAM;QAAE5B;MAAG,CAAC,GAAGmD,MAAI;MACnBA,MAAI,CAACd,IAAI,GAAGrC,EAAE,CAACoD,aAAa,CAAC,UAAU,CAAC;MACxCD,MAAI,CAACP,SAAS,GAAG3D,qDAAqB,CAACe,EAAE,CAAC;MAC1C;AACR;AACA;AACA;AACA;AACA;AACA;MACQmD,MAAI,CAACE,gBAAgB,GAAGhE,6DAAe,CAACW,EAAE,EAAE,iBAAiB,eAAA4B,yMAAA,CAAE,aAAY;QACvE,MAAMuB,MAAI,CAACG,aAAa,CAAC,CAAC;MAC9B,CAAC,EAAC;MACF,MAAMH,MAAI,CAACG,aAAa,CAAC,CAAC;MAC1BH,MAAI,CAACH,OAAO,GAAG,OAAO,qHAA6B,EAAEO,aAAa,CAAC;QAC/DvD,EAAE;QACFwD,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAG5D,EAAE,IAAKoD,MAAI,CAACQ,QAAQ,CAAC5D,EAAE,CAAC;QACnC6D,OAAO,EAAEA,CAAA,KAAMT,MAAI,CAACS,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAG9D,EAAE,IAAKoD,MAAI,CAACU,MAAM,CAAC9D,EAAE,CAAC;QAC/B+D,KAAK,EAAG/D,EAAE,IAAKoD,MAAI,CAACW,KAAK,CAAC/D,EAAE;MAChC,CAAC,CAAC;MACFoD,MAAI,CAACJ,eAAe,CAAC,CAAC;IAAC;EAC3B;EACAgB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACf,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACgB,OAAO,CAAC,CAAC;MACtB,IAAI,CAAChB,OAAO,GAAGzC,SAAS;IAC5B;IACA,IAAI,CAAC8B,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC4B,WAAW,GAAG,IAAI,CAACC,YAAY,GAAG3D,SAAS;IAChD,IAAI2B,eAAe,KAAK,IAAI,CAAClC,EAAE,EAAE;MAC7BkC,eAAe,GAAG3B,SAAS;IAC/B;IACA,IAAI,IAAI,CAAC8C,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACc,UAAU,CAAC,CAAC;MAClC,IAAI,CAACd,gBAAgB,GAAG9C,SAAS;IACrC;EACJ;EACA;AACJ;AACA;EACI6D,aAAaA,CAAA,EAAG;IACZ,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAChC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiC,eAAeA,CAAA,EAAG;IACd,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC;EACtD;EACA;AACJ;AACA;AACA;AACA;EACUC,IAAIA,CAAChD,IAAI,EAAE;IAAA,IAAAiD,MAAA;IAAA,OAAA9C,yMAAA;MACb,IAAI+C,EAAE;MACN;AACR;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,MAAMtC,IAAI,GAAIqC,MAAI,CAACrC,IAAI,GAAG,CAACsC,EAAE,GAAGD,MAAI,CAACrC,IAAI,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,MAAI,CAAC1E,EAAE,CAACoD,aAAa,CAAC,UAAU,CAAE;MAC9G,IAAIf,IAAI,KAAK,IAAI,EAAE;QACf;MACJ;MACA,MAAMuC,aAAa,GAAGF,MAAI,CAACG,UAAU,CAACpD,IAAI,CAAC;MAC3C,IAAI,CAACmD,aAAa,EAAE;QAChB;MACJ;MACA;AACR;AACA;AACA;MACQ,IAAInD,IAAI,KAAKlB,SAAS,EAAE;QACpBkB,IAAI,GAAGmD,aAAa,KAAKF,MAAI,CAACT,WAAW,GAAG,OAAO,GAAG,KAAK;MAC/D;MACA;MACAxC,IAAI,GAAG1C,uDAAS,CAAC0C,IAAI,CAAC,GAAG,KAAK,GAAG,OAAO;MACxC,MAAMqD,WAAW,GAAGJ,MAAI,CAACpC,UAAU,GAAG,CAAC;MACvC,MAAMyC,SAAS,GAAGL,MAAI,CAACpC,UAAU,GAAG,CAAC;MACrC;AACR;AACA;AACA;MACQ,IAAIwC,WAAW,IAAIF,aAAa,KAAKF,MAAI,CAACT,WAAW,EAAE;QACnD;MACJ;MACA,IAAIc,SAAS,IAAIH,aAAa,KAAKF,MAAI,CAACR,YAAY,EAAE;QAClD;MACJ;MACAQ,MAAI,CAACM,WAAW,CAAC,CAAC;MAClBN,MAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;MACfmC,qBAAqB,CAAC,MAAM;QACxBP,MAAI,CAACQ,kBAAkB,CAAC,CAAC;QACzB,MAAMC,KAAK,GAAG1D,IAAI,KAAK,KAAK,GAAGiD,MAAI,CAAClC,kBAAkB,GAAG,CAACkC,MAAI,CAACjC,iBAAiB;QAChFP,eAAe,GAAGwC,MAAI,CAAC1E,EAAE;QACzB0E,MAAI,CAACU,aAAa,CAACD,KAAK,EAAE,KAAK,CAAC;QAChCT,MAAI,CAAC5B,KAAK,GAAGrB,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC,yBAAyB,EAAE,CAAC;MAChE,CAAC,CAAC;IAAC;EACP;EACA;AACJ;AACA;EACU4D,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA1D,yMAAA;MACV0D,MAAI,CAACF,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC;IAAC;EAChC;EACA;AACJ;AACA;EACUJ,WAAWA,CAAA,EAAG;IAAA,OAAApD,yMAAA;MAChB,IAAIM,eAAe,KAAK3B,SAAS,EAAE;QAC/B2B,eAAe,CAACmD,KAAK,CAAC,CAAC;QACvBnD,eAAe,GAAG3B,SAAS;QAC3B,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsE,UAAUA,CAACpD,IAAI,EAAE;IACb,IAAIA,IAAI,KAAKlB,SAAS,EAAE;MACpB,OAAO,IAAI,CAAC0D,WAAW,IAAI,IAAI,CAACC,YAAY;IAChD,CAAC,MACI,IAAIzC,IAAI,KAAK,OAAO,EAAE;MACvB,OAAO,IAAI,CAACwC,WAAW;IAC3B,CAAC,MACI;MACD,OAAO,IAAI,CAACC,YAAY;IAC5B;EACJ;EACMZ,aAAaA,CAAA,EAAG;IAAA,IAAAiC,MAAA;IAAA,OAAA3D,yMAAA;MAClB,IAAI+C,EAAE;MACN,MAAMa,OAAO,GAAGD,MAAI,CAACvF,EAAE,CAACyF,gBAAgB,CAAC,kBAAkB,CAAC;MAC5D,IAAI/C,KAAK,GAAG,CAAC;MACb;MACA6C,MAAI,CAACtB,WAAW,GAAGsB,MAAI,CAACrB,YAAY,GAAG3D,SAAS;MAChD,KAAK,IAAImF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAMrD,IAAI,GAAGmD,OAAO,CAACnD,IAAI,CAACqD,CAAC,CAAC;QAC5B;AACZ;AACA;AACA;AACA;QACY;QACA,MAAME,MAAM,GAAGvD,IAAI,CAACwD,gBAAgB,KAAKtF,SAAS,SAAS8B,IAAI,CAACwD,gBAAgB,CAAC,CAAC,GAAGxD,IAAI;QACzF,MAAMZ,IAAI,GAAG1C,uDAAS,CAAC,CAAC4F,EAAE,GAAGiB,MAAM,CAACnE,IAAI,MAAM,IAAI,IAAIkD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGiB,MAAM,CAACE,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO;QACzH,IAAIrE,IAAI,KAAK,OAAO,EAAE;UAClB8D,MAAI,CAACtB,WAAW,GAAG2B,MAAM;UACzBlD,KAAK,IAAI,CAAC,CAAC;QACf,CAAC,MACI;UACD6C,MAAI,CAACrB,YAAY,GAAG0B,MAAM;UAC1BlD,KAAK,IAAI,CAAC,CAAC;QACf;MACJ;MACA6C,MAAI,CAAC5C,SAAS,GAAG,IAAI;MACrB4C,MAAI,CAAC7C,KAAK,GAAGA,KAAK;IAAC;EACvB;EACAiB,QAAQA,CAACX,OAAO,EAAE;IACd;AACR;AACA;AACA;AACA;IACQ,MAAM+C,GAAG,GAAGC,QAAQ,CAACC,GAAG,KAAK,KAAK;IAClC,MAAMC,MAAM,GAAGH,GAAG,GAAGI,MAAM,CAACC,UAAU,GAAGpD,OAAO,CAACqD,MAAM,GAAG,EAAE,GAAGrD,OAAO,CAACqD,MAAM,GAAG,EAAE;IAClF,IAAIH,MAAM,EAAE;MACR,OAAO,KAAK;IAChB;IACA,MAAMI,QAAQ,GAAGpE,eAAe;IAChC,IAAIoE,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACtG,EAAE,EAAE;MAClC,IAAI,CAACgF,WAAW,CAAC,CAAC;IACtB;IACA,OAAO,CAAC,EAAE,IAAI,CAACd,YAAY,IAAI,IAAI,CAACD,WAAW,CAAC;EACpD;EACAL,OAAOA,CAAA,EAAG;IACN;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACvB,IAAI,GAAG,IAAI,CAACrC,EAAE,CAACoD,aAAa,CAAC,UAAU,CAAC;IAC7C,MAAM;MAAER;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAACC,qBAAqB,GAAG3D,qDAAqB,CAAC0D,SAAS,CAAC;IACjE;IACAV,eAAe,GAAG,IAAI,CAAClC,EAAE;IACzB,IAAI,IAAI,CAACuG,GAAG,KAAKhG,SAAS,EAAE;MACxBiG,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC;MACtB,IAAI,CAACA,GAAG,GAAGhG,SAAS;IACxB;IACA,IAAI,IAAI,CAAC+B,UAAU,KAAK,CAAC,EAAE;MACvB,IAAI,CAACK,SAAS,GAAG,IAAI;MACrB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IACnB;IACA,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAACD,UAAU;IACxC,IAAI,IAAI,CAACD,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACnB,KAAK,CAACuF,UAAU,GAAG,MAAM;IACvC;EACJ;EACA5C,MAAMA,CAACb,OAAO,EAAE;IACZ,IAAI,IAAI,CAACL,SAAS,EAAE;MAChB,IAAI,CAACuC,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAI5C,UAAU,GAAG,IAAI,CAACC,iBAAiB,GAAGS,OAAO,CAAC0D,MAAM;IACxD,QAAQ,IAAI,CAAChE,KAAK;MACd,KAAK,CAAC,CAAC;QACHJ,UAAU,GAAGqE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEtE,UAAU,CAAC;QACpC;MACJ,KAAK,CAAC,CAAC;QACHA,UAAU,GAAGqE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvE,UAAU,CAAC;QACpC;MACJ,KAAK,CAAC,CAAC;QACH;MACJ,KAAK,CAAC,CAAC;QACH;MACJ;QACI3D,qDAAe,CAAC,kDAAkD,EAAE,IAAI,CAAC+D,KAAK,CAAC;QAC/E;IACR;IACA,IAAIoE,SAAS;IACb,IAAIxE,UAAU,GAAG,IAAI,CAACE,kBAAkB,EAAE;MACtCsE,SAAS,GAAG,IAAI,CAACtE,kBAAkB;MACnCF,UAAU,GAAGwE,SAAS,GAAG,CAACxE,UAAU,GAAGwE,SAAS,IAAI7E,cAAc;IACtE,CAAC,MACI,IAAIK,UAAU,GAAG,CAAC,IAAI,CAACG,iBAAiB,EAAE;MAC3CqE,SAAS,GAAG,CAAC,IAAI,CAACrE,iBAAiB;MACnCH,UAAU,GAAGwE,SAAS,GAAG,CAACxE,UAAU,GAAGwE,SAAS,IAAI7E,cAAc;IACtE;IACA,IAAI,CAACmD,aAAa,CAAC9C,UAAU,EAAE,KAAK,CAAC;EACzC;EACAwB,KAAKA,CAACd,OAAO,EAAE;IACX,MAAM;MAAEJ,SAAS;MAAEC;IAAsB,CAAC,GAAG,IAAI;IACjD,IAAID,SAAS,EAAE;MACXzD,qDAAmB,CAACyD,SAAS,EAAEC,qBAAqB,CAAC;IACzD;IACA,MAAMkE,QAAQ,GAAG/D,OAAO,CAACgE,SAAS;IAClC,IAAIC,YAAY,GAAG,IAAI,CAAC3E,UAAU,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,GAAG,CAAC,IAAI,CAACC,iBAAiB;IAC1F;IACA;IACA,MAAMyE,gBAAgB,GAAG,IAAI,CAAC5E,UAAU,GAAG,CAAC,KAAK,EAAEyE,QAAQ,GAAG,CAAC,CAAC;IAChE,MAAMI,YAAY,GAAGR,IAAI,CAACS,GAAG,CAACL,QAAQ,CAAC,GAAG,GAAG;IAC7C,MAAMM,aAAa,GAAGV,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC9E,UAAU,CAAC,GAAGqE,IAAI,CAACS,GAAG,CAACH,YAAY,GAAG,CAAC,CAAC;IAC5E,IAAIK,gBAAgB,CAACJ,gBAAgB,EAAEC,YAAY,EAAEE,aAAa,CAAC,EAAE;MACjEJ,YAAY,GAAG,CAAC;IACpB;IACA,MAAMnE,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACsC,aAAa,CAAC6B,YAAY,EAAE,IAAI,CAAC;IACtC,IAAI,CAACnE,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC,IAAI,IAAI,CAACoB,YAAY,EAAE;MACrE,IAAI,CAACA,YAAY,CAACxC,cAAc,CAAC,CAAC;IACtC,CAAC,MACI,IAAI,CAACoB,KAAK,GAAG,EAAE,CAAC,mCAAmC,CAAC,IAAI,IAAI,CAACmB,WAAW,EAAE;MAC3E,IAAI,CAACA,WAAW,CAACvC,cAAc,CAAC,CAAC;IACrC;EACJ;EACAwD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC1C,kBAAkB,GAAG,CAAC;IAC3B,IAAI,IAAI,CAAC0B,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAChD,KAAK,CAACqG,OAAO,GAAG,MAAM;MACxC,IAAI,CAAC/E,kBAAkB,GAAG,IAAI,CAAC0B,YAAY,CAACsD,WAAW;MACvD,IAAI,CAACtD,YAAY,CAAChD,KAAK,CAACqG,OAAO,GAAG,EAAE;IACxC;IACA,IAAI,CAAC9E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACwB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC/C,KAAK,CAACqG,OAAO,GAAG,MAAM;MACvC,IAAI,CAAC9E,iBAAiB,GAAG,IAAI,CAACwB,WAAW,CAACuD,WAAW;MACrD,IAAI,CAACvD,WAAW,CAAC/C,KAAK,CAACqG,OAAO,GAAG,EAAE;IACvC;IACA,IAAI,CAAC5E,SAAS,GAAG,KAAK;EAC1B;EACAyC,aAAaA,CAAC9C,UAAU,EAAEmF,OAAO,EAAE;IAC/B,IAAI,IAAI,CAAClB,GAAG,KAAKhG,SAAS,EAAE;MACxBiG,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC;MACtB,IAAI,CAACA,GAAG,GAAGhG,SAAS;IACxB;IACA,IAAI,CAAC,IAAI,CAAC8B,IAAI,EAAE;MACZ;IACJ;IACA,MAAM;MAAErC;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMkB,KAAK,GAAG,IAAI,CAACmB,IAAI,CAACnB,KAAK;IAC7B,IAAI,CAACoB,UAAU,GAAGA,UAAU;IAC5B,IAAImF,OAAO,EAAE;MACTvG,KAAK,CAACuF,UAAU,GAAG,EAAE;IACzB;IACA,IAAInE,UAAU,GAAG,CAAC,EAAE;MAChB,IAAI,CAACQ,KAAK,GACNR,UAAU,IAAI,IAAI,CAACE,kBAAkB,GAAGR,YAAY,GAC9C,CAAC,CAAC,yBAAyB,EAAE,CAAC,8BAC9B,CAAC,CAAC;IAChB,CAAC,MACI,IAAIM,UAAU,GAAG,CAAC,EAAE;MACrB,IAAI,CAACQ,KAAK,GACNR,UAAU,IAAI,CAAC,IAAI,CAACG,iBAAiB,GAAGT,YAAY,GAC9C,EAAE,CAAC,2BAA2B,EAAE,CAAC,gCACjC,EAAE,CAAC;IACjB,CAAC,MACI;MACD;AACZ;AACA;AACA;MACYhC,EAAE,CAAC0H,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAC3E,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;MAC9B;MACA,IAAI,CAACsD,GAAG,GAAGqB,UAAU,CAAC,MAAM;QACxB,IAAI,CAAC9E,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAACyD,GAAG,GAAGhG,SAAS;QACpB,IAAI,IAAI,CAACyC,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACtD,QAAQ,CAAC;QACvC;QACAK,EAAE,CAAC0H,SAAS,CAACG,MAAM,CAAC,sBAAsB,CAAC;MAC/C,CAAC,EAAE,GAAG,CAAC;MACP3F,eAAe,GAAG3B,SAAS;MAC3BW,KAAK,CAAC4G,SAAS,GAAG,EAAE;MACpB;IACJ;IACA5G,KAAK,CAAC4G,SAAS,GAAG,eAAe,CAACxF,UAAU,SAAS;IACrD,IAAI,CAACF,OAAO,CAACP,IAAI,CAAC;MACdkG,MAAM,EAAEzF,UAAU;MAClB0F,KAAK,EAAE,IAAI,CAACxD,mBAAmB,CAAC;IACpC,CAAC,CAAC;EACN;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAClC,UAAU,GAAG,CAAC,EAAE;MACrB,OAAO,IAAI,CAACA,UAAU,GAAG,IAAI,CAACE,kBAAkB;IACpD,CAAC,MACI,IAAI,IAAI,CAACF,UAAU,GAAG,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACA,UAAU,GAAG,IAAI,CAACG,iBAAiB;IACnD,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACArC,MAAMA,CAAA,EAAG;IACL,MAAMI,IAAI,GAAGtC,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEsC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ,2BAA2B,EAAE,IAAI,CAACsC,KAAK,KAAK,CAAC,CAAC;QAC9C,iCAAiC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC,4BAA4B,CAAC;QAChF,mCAAmC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,8BAA8B,CAAC;QACrF,+BAA+B,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC;QACpF,iCAAiC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,mCAAmC;MAC3F;IAAE,CAAC,CAAC;EACZ;EACA,IAAI9C,EAAEA,CAAA,EAAG;IAAE,OAAOzB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0J,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,MAAMX,gBAAgB,GAAGA,CAACJ,gBAAgB,EAAEC,YAAY,EAAEe,aAAa,KAAK;EACxE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAQ,CAACf,YAAY,IAAIe,aAAa,IAAMhB,gBAAgB,IAAIC,YAAa;AACjF,CAAC;AACDhF,WAAW,CAACjB,KAAK,GAAGa,cAAc", "sources": ["./node_modules/@ionic/core/dist/esm/ion-item-option_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement, d as createEvent, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { o as isEndSide } from './helpers-1O4D2b7y.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-BlJTBdxG.js';\nimport { w as watchForOptions } from './watch-options-Dtdm8lKC.js';\n\nconst itemOptionIosCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:clamp(16px, 1rem, 35.2px)}:host(.ion-activated){background:var(--ion-color-primary-shade, #004acd)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}\";\n\nconst itemOptionMdCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:0.875rem;font-weight:500;text-transform:uppercase}\";\n\nconst ItemOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the user cannot interact with the item option.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the option will expand to take up the available width and cover any other options.\n         */\n        this.expandable = false;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        this.onClick = (ev) => {\n            const el = ev.target.closest('ion-item-option');\n            if (el) {\n                ev.preventDefault();\n            }\n        };\n    }\n    render() {\n        const { disabled, expandable, href } = this;\n        const TagType = href === undefined ? 'button' : 'a';\n        const mode = getIonMode(this);\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download: this.download,\n                href: this.href,\n                target: this.target,\n            };\n        return (h(Host, { key: '189a0040b97163b2336bf216baa71d584c5923a8', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'item-option-disabled': disabled,\n                'item-option-expandable': expandable,\n                'ion-activatable': true,\n            }) }, h(TagType, Object.assign({ key: '5a7140eb99da5ec82fe2ea3ea134513130763399' }, attrs, { class: \"button-native\", part: \"native\", disabled: disabled }), h(\"span\", { key: '9b8577e612706b43e575c9a20f2f9d35c0d1bcb1', class: \"button-inner\" }, h(\"slot\", { key: '9acb82f04e4822bfaa363cc2c4d29d5c0fec0ad6', name: \"top\" }), h(\"div\", { key: '66f5fb4fdd0c39f205574c602c793dcf109c7a17', class: \"horizontal-wrapper\" }, h(\"slot\", { key: '3761a32bca7c6c41b7eb394045497cfde181a62a', name: \"start\" }), h(\"slot\", { key: 'a96a568955cf6962883dc6771726d3d07462da00', name: \"icon-only\" }), h(\"slot\", { key: 'af5dfe5eb41456b9359bafe3615b576617ed7b57' }), h(\"slot\", { key: '00426958066ab7b949ff966fabad5cf8a0b54079', name: \"end\" })), h(\"slot\", { key: 'ae66c8bd536a9f27865f49240980d7b4b831b229', name: \"bottom\" })), mode === 'md' && h(\"ion-ripple-effect\", { key: '30df6c935ef8a3f28a6bc1f3bb162ca4f80aaf26' }))));\n    }\n    get el() { return getElement(this); }\n};\nItemOption.style = {\n    ios: itemOptionIosCss,\n    md: itemOptionMdCss\n};\n\nconst itemOptionsIosCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}\";\n\nconst itemOptionsMdCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}\";\n\nconst ItemOptions = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSwipe = createEvent(this, \"ionSwipe\", 7);\n        /**\n         * The side the option button should be on. Possible values: `\"start\"` and `\"end\"`. If you have multiple `ion-item-options`, a side must be provided for each.\n         *\n         */\n        this.side = 'end';\n    }\n    /** @internal */\n    async fireSwipeEvent() {\n        this.ionSwipe.emit({\n            side: this.side,\n        });\n    }\n    render() {\n        const mode = getIonMode(this);\n        const isEnd = isEndSide(this.side);\n        return (h(Host, { key: '05a22a505e043c2715e3805e5e26ab4668940af0', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`item-options-${mode}`]: true,\n                /**\n                 * Note: The \"start\" and \"end\" terms refer to the\n                 * direction ion-item-option instances within ion-item-options flow.\n                 * They do not refer to how ion-item-options flows within ion-item-sliding.\n                 * As a result, \"item-options-start\" means the ion-item-options container\n                 * always appears on the left, and \"item-options-end\" means the ion-item-options\n                 * container always appears on the right.\n                 */\n                'item-options-start': !isEnd,\n                'item-options-end': isEnd,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nItemOptions.style = {\n    ios: itemOptionsIosCss,\n    md: itemOptionsMdCss\n};\n\nconst itemSlidingCss = \"ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}\";\n\nconst SWIPE_MARGIN = 30;\nconst ELASTIC_FACTOR = 0.55;\nlet openSlidingItem;\nconst ItemSliding = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionDrag = createEvent(this, \"ionDrag\", 7);\n        this.item = null;\n        this.openAmount = 0;\n        this.initialOpenAmount = 0;\n        this.optsWidthRightSide = 0;\n        this.optsWidthLeftSide = 0;\n        this.sides = 0 /* ItemSide.None */;\n        this.optsDirty = true;\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        this.state = 2 /* SlidingState.Disabled */;\n        /**\n         * If `true`, the user cannot interact with the sliding item.\n         */\n        this.disabled = false;\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.item = el.querySelector('ion-item');\n        this.contentEl = findClosestIonContent(el);\n        /**\n         * The MutationObserver needs to be added before we\n         * call updateOptions below otherwise we may miss\n         * ion-item-option elements that are added to the DOM\n         * while updateOptions is running and before the MutationObserver\n         * has been initialized.\n         */\n        this.mutationObserver = watchForOptions(el, 'ion-item-option', async () => {\n            await this.updateOptions();\n        });\n        await this.updateOptions();\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el,\n            gestureName: 'item-swipe',\n            gesturePriority: 100,\n            threshold: 5,\n            canStart: (ev) => this.canStart(ev),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.disabledChanged();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.item = null;\n        this.leftOptions = this.rightOptions = undefined;\n        if (openSlidingItem === this.el) {\n            openSlidingItem = undefined;\n        }\n        if (this.mutationObserver) {\n            this.mutationObserver.disconnect();\n            this.mutationObserver = undefined;\n        }\n    }\n    /**\n     * Get the amount the item is open in pixels.\n     */\n    getOpenAmount() {\n        return Promise.resolve(this.openAmount);\n    }\n    /**\n     * Get the ratio of the open amount of the item compared to the width of the options.\n     * If the number returned is positive, then the options on the right side are open.\n     * If the number returned is negative, then the options on the left side are open.\n     * If the absolute value of the number is greater than 1, the item is open more than\n     * the width of the options.\n     */\n    getSlidingRatio() {\n        return Promise.resolve(this.getSlidingRatioSync());\n    }\n    /**\n     * Open the sliding item.\n     *\n     * @param side The side of the options to open. If a side is not provided, it will open the first set of options it finds within the item.\n     */\n    async open(side) {\n        var _a;\n        /**\n         * It is possible for the item to be added to the DOM\n         * after the item-sliding component was created. As a result,\n         * if this.item is null, then we should attempt to\n         * query for the ion-item again.\n         * However, if the item is already defined then\n         * we do not query for it again.\n         */\n        const item = (this.item = (_a = this.item) !== null && _a !== void 0 ? _a : this.el.querySelector('ion-item'));\n        if (item === null) {\n            return;\n        }\n        const optionsToOpen = this.getOptions(side);\n        if (!optionsToOpen) {\n            return;\n        }\n        /**\n         * If side is not set, we need to infer the side\n         * so we know which direction to move the options\n         */\n        if (side === undefined) {\n            side = optionsToOpen === this.leftOptions ? 'start' : 'end';\n        }\n        // In RTL we want to switch the sides\n        side = isEndSide(side) ? 'end' : 'start';\n        const isStartOpen = this.openAmount < 0;\n        const isEndOpen = this.openAmount > 0;\n        /**\n         * If a side is open and a user tries to\n         * re-open the same side, we should not do anything\n         */\n        if (isStartOpen && optionsToOpen === this.leftOptions) {\n            return;\n        }\n        if (isEndOpen && optionsToOpen === this.rightOptions) {\n            return;\n        }\n        this.closeOpened();\n        this.state = 4 /* SlidingState.Enabled */;\n        requestAnimationFrame(() => {\n            this.calculateOptsWidth();\n            const width = side === 'end' ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n            openSlidingItem = this.el;\n            this.setOpenAmount(width, false);\n            this.state = side === 'end' ? 8 /* SlidingState.End */ : 16 /* SlidingState.Start */;\n        });\n    }\n    /**\n     * Close the sliding item. Items can also be closed from the [List](./list).\n     */\n    async close() {\n        this.setOpenAmount(0, true);\n    }\n    /**\n     * Close all of the sliding items in the list. Items can also be closed from the [List](./list).\n     */\n    async closeOpened() {\n        if (openSlidingItem !== undefined) {\n            openSlidingItem.close();\n            openSlidingItem = undefined;\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Given an optional side, return the ion-item-options element.\n     *\n     * @param side This side of the options to get. If a side is not provided it will\n     * return the first one available.\n     */\n    getOptions(side) {\n        if (side === undefined) {\n            return this.leftOptions || this.rightOptions;\n        }\n        else if (side === 'start') {\n            return this.leftOptions;\n        }\n        else {\n            return this.rightOptions;\n        }\n    }\n    async updateOptions() {\n        var _a;\n        const options = this.el.querySelectorAll('ion-item-options');\n        let sides = 0;\n        // Reset left and right options in case they were removed\n        this.leftOptions = this.rightOptions = undefined;\n        for (let i = 0; i < options.length; i++) {\n            const item = options.item(i);\n            /**\n             * We cannot use the componentOnReady helper\n             * util here since we need to wait for all of these items\n             * to be ready before we set `this.sides` and `this.optsDirty`.\n             */\n            // eslint-disable-next-line custom-rules/no-component-on-ready-method\n            const option = item.componentOnReady !== undefined ? await item.componentOnReady() : item;\n            const side = isEndSide((_a = option.side) !== null && _a !== void 0 ? _a : option.getAttribute('side')) ? 'end' : 'start';\n            if (side === 'start') {\n                this.leftOptions = option;\n                sides |= 1 /* ItemSide.Start */;\n            }\n            else {\n                this.rightOptions = option;\n                sides |= 2 /* ItemSide.End */;\n            }\n        }\n        this.optsDirty = true;\n        this.sides = sides;\n    }\n    canStart(gesture) {\n        /**\n         * If very close to start of the screen\n         * do not open left side so swipe to go\n         * back will still work.\n         */\n        const rtl = document.dir === 'rtl';\n        const atEdge = rtl ? window.innerWidth - gesture.startX < 15 : gesture.startX < 15;\n        if (atEdge) {\n            return false;\n        }\n        const selected = openSlidingItem;\n        if (selected && selected !== this.el) {\n            this.closeOpened();\n        }\n        return !!(this.rightOptions || this.leftOptions);\n    }\n    onStart() {\n        /**\n         * We need to query for the ion-item\n         * every time the gesture starts. Developers\n         * may toggle ion-item elements via *ngIf.\n         */\n        this.item = this.el.querySelector('ion-item');\n        const { contentEl } = this;\n        if (contentEl) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        openSlidingItem = this.el;\n        if (this.tmr !== undefined) {\n            clearTimeout(this.tmr);\n            this.tmr = undefined;\n        }\n        if (this.openAmount === 0) {\n            this.optsDirty = true;\n            this.state = 4 /* SlidingState.Enabled */;\n        }\n        this.initialOpenAmount = this.openAmount;\n        if (this.item) {\n            this.item.style.transition = 'none';\n        }\n    }\n    onMove(gesture) {\n        if (this.optsDirty) {\n            this.calculateOptsWidth();\n        }\n        let openAmount = this.initialOpenAmount - gesture.deltaX;\n        switch (this.sides) {\n            case 2 /* ItemSide.End */:\n                openAmount = Math.max(0, openAmount);\n                break;\n            case 1 /* ItemSide.Start */:\n                openAmount = Math.min(0, openAmount);\n                break;\n            case 3 /* ItemSide.Both */:\n                break;\n            case 0 /* ItemSide.None */:\n                return;\n            default:\n                printIonWarning('[ion-item-sliding] - invalid ItemSideFlags value', this.sides);\n                break;\n        }\n        let optsWidth;\n        if (openAmount > this.optsWidthRightSide) {\n            optsWidth = this.optsWidthRightSide;\n            openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n        }\n        else if (openAmount < -this.optsWidthLeftSide) {\n            optsWidth = -this.optsWidthLeftSide;\n            openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n        }\n        this.setOpenAmount(openAmount, false);\n    }\n    onEnd(gesture) {\n        const { contentEl, initialContentScrollY } = this;\n        if (contentEl) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        const velocity = gesture.velocityX;\n        let restingPoint = this.openAmount > 0 ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n        // Check if the drag didn't clear the buttons mid-point\n        // and we aren't moving fast enough to swipe open\n        const isResetDirection = this.openAmount > 0 === !(velocity < 0);\n        const isMovingFast = Math.abs(velocity) > 0.3;\n        const isOnCloseZone = Math.abs(this.openAmount) < Math.abs(restingPoint / 2);\n        if (swipeShouldReset(isResetDirection, isMovingFast, isOnCloseZone)) {\n            restingPoint = 0;\n        }\n        const state = this.state;\n        this.setOpenAmount(restingPoint, true);\n        if ((state & 32 /* SlidingState.SwipeEnd */) !== 0 && this.rightOptions) {\n            this.rightOptions.fireSwipeEvent();\n        }\n        else if ((state & 64 /* SlidingState.SwipeStart */) !== 0 && this.leftOptions) {\n            this.leftOptions.fireSwipeEvent();\n        }\n    }\n    calculateOptsWidth() {\n        this.optsWidthRightSide = 0;\n        if (this.rightOptions) {\n            this.rightOptions.style.display = 'flex';\n            this.optsWidthRightSide = this.rightOptions.offsetWidth;\n            this.rightOptions.style.display = '';\n        }\n        this.optsWidthLeftSide = 0;\n        if (this.leftOptions) {\n            this.leftOptions.style.display = 'flex';\n            this.optsWidthLeftSide = this.leftOptions.offsetWidth;\n            this.leftOptions.style.display = '';\n        }\n        this.optsDirty = false;\n    }\n    setOpenAmount(openAmount, isFinal) {\n        if (this.tmr !== undefined) {\n            clearTimeout(this.tmr);\n            this.tmr = undefined;\n        }\n        if (!this.item) {\n            return;\n        }\n        const { el } = this;\n        const style = this.item.style;\n        this.openAmount = openAmount;\n        if (isFinal) {\n            style.transition = '';\n        }\n        if (openAmount > 0) {\n            this.state =\n                openAmount >= this.optsWidthRightSide + SWIPE_MARGIN\n                    ? 8 /* SlidingState.End */ | 32 /* SlidingState.SwipeEnd */\n                    : 8 /* SlidingState.End */;\n        }\n        else if (openAmount < 0) {\n            this.state =\n                openAmount <= -this.optsWidthLeftSide - SWIPE_MARGIN\n                    ? 16 /* SlidingState.Start */ | 64 /* SlidingState.SwipeStart */\n                    : 16 /* SlidingState.Start */;\n        }\n        else {\n            /**\n             * The sliding options should not be\n             * clickable while the item is closing.\n             */\n            el.classList.add('item-sliding-closing');\n            /**\n             * Item sliding cannot be interrupted\n             * while closing the item. If it did,\n             * it would allow the item to get into an\n             * inconsistent state where multiple\n             * items are then open at the same time.\n             */\n            if (this.gesture) {\n                this.gesture.enable(false);\n            }\n            this.tmr = setTimeout(() => {\n                this.state = 2 /* SlidingState.Disabled */;\n                this.tmr = undefined;\n                if (this.gesture) {\n                    this.gesture.enable(!this.disabled);\n                }\n                el.classList.remove('item-sliding-closing');\n            }, 600);\n            openSlidingItem = undefined;\n            style.transform = '';\n            return;\n        }\n        style.transform = `translate3d(${-openAmount}px,0,0)`;\n        this.ionDrag.emit({\n            amount: openAmount,\n            ratio: this.getSlidingRatioSync(),\n        });\n    }\n    getSlidingRatioSync() {\n        if (this.openAmount > 0) {\n            return this.openAmount / this.optsWidthRightSide;\n        }\n        else if (this.openAmount < 0) {\n            return this.openAmount / this.optsWidthLeftSide;\n        }\n        else {\n            return 0;\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd812322c9fb5da4ee16e99dc38bfb24cb4590d03', class: {\n                [mode]: true,\n                'item-sliding-active-slide': this.state !== 2 /* SlidingState.Disabled */,\n                'item-sliding-active-options-end': (this.state & 8 /* SlidingState.End */) !== 0,\n                'item-sliding-active-options-start': (this.state & 16 /* SlidingState.Start */) !== 0,\n                'item-sliding-active-swipe-end': (this.state & 32 /* SlidingState.SwipeEnd */) !== 0,\n                'item-sliding-active-swipe-start': (this.state & 64 /* SlidingState.SwipeStart */) !== 0,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst swipeShouldReset = (isResetDirection, isMovingFast, isOnResetZone) => {\n    // The logic required to know when the sliding item should close (openAmount=0)\n    // depends on three booleans (isResetDirection, isMovingFast, isOnResetZone)\n    // and it ended up being too complicated to be written manually without errors\n    // so the truth table is attached below: (0=false, 1=true)\n    // isResetDirection | isMovingFast | isOnResetZone || shouldClose\n    //         0        |       0      |       0       ||    0\n    //         0        |       0      |       1       ||    1\n    //         0        |       1      |       0       ||    0\n    //         0        |       1      |       1       ||    0\n    //         1        |       0      |       0       ||    0\n    //         1        |       0      |       1       ||    1\n    //         1        |       1      |       0       ||    1\n    //         1        |       1      |       1       ||    1\n    // The resulting expression was generated by resolving the K-map (Karnaugh map):\n    return (!isMovingFast && isOnResetZone) || (isResetDirection && isMovingFast);\n};\nItemSliding.style = itemSlidingCss;\n\nexport { ItemOption as ion_item_option, ItemOptions as ion_item_options, ItemSliding as ion_item_sliding };\n"], "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "d", "createEvent", "m", "printIonWarning", "c", "createColorClasses", "o", "isEndSide", "f", "findClosestIonContent", "disableContentScrollY", "resetContentScrollY", "w", "watchForOptions", "itemOptionIosCss", "itemOptionMdCss", "ItemOption", "constructor", "hostRef", "disabled", "expandable", "type", "onClick", "ev", "el", "target", "closest", "preventDefault", "render", "href", "TagType", "undefined", "mode", "attrs", "download", "key", "class", "color", "Object", "assign", "part", "name", "style", "ios", "md", "itemOptionsIosCss", "itemOptionsMdCss", "ItemOptions", "ionSwipe", "side", "fireSwipeEvent", "_this", "_asyncToGenerator", "emit", "isEnd", "itemSlidingCss", "SWIPE_MARGIN", "ELASTIC_FACTOR", "openSlidingItem", "ItemSliding", "ionDrag", "item", "openAmount", "initialOpenAmount", "optsWidthRightSide", "optsWidthLeftSide", "sides", "optsDirty", "contentEl", "initialContentScrollY", "state", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "_this2", "querySelector", "mutationObserver", "updateOptions", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "canStart", "onStart", "onMove", "onEnd", "disconnectedCallback", "destroy", "leftOptions", "rightOptions", "disconnect", "getOpenAmount", "Promise", "resolve", "getSlidingRatio", "getSlidingRatioSync", "open", "_this3", "_a", "optionsToOpen", "getOptions", "isStartOpen", "isEndOpen", "closeOpened", "requestAnimationFrame", "calculateOptsWidth", "width", "setOpenAmount", "close", "_this4", "_this5", "options", "querySelectorAll", "i", "length", "option", "componentOnReady", "getAttribute", "rtl", "document", "dir", "atEdge", "window", "innerWidth", "startX", "selected", "tmr", "clearTimeout", "transition", "deltaX", "Math", "max", "min", "optsWidth", "velocity", "velocityX", "restingPoint", "isResetDirection", "isMovingFast", "abs", "isOnCloseZone", "swipeShouldReset", "display", "offsetWidth", "isFinal", "classList", "add", "setTimeout", "remove", "transform", "amount", "ratio", "watchers", "isOnResetZone", "ion_item_option", "ion_item_options", "ion_item_sliding"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}