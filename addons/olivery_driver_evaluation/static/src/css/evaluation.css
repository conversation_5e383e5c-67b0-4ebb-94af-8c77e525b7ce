/* Driver Evaluation Styles */

.evaluation-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.evaluation-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.evaluation-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 300;
}

.evaluation-header p {
    margin: 5px 0;
    opacity: 0.9;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin-bottom: 30px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Evaluation Steps */
.evaluation-step {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.evaluation-step:first-child {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
ion-card {
    margin: 20px 0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

ion-card-header {
    padding: 20px 20px 10px 20px;
}

ion-card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

ion-card-subtitle {
    color: #666;
    font-size: 1rem;
    margin-top: 5px;
}

ion-card-content {
    padding: 10px 20px 20px 20px;
}

/* Form Elements */
ion-item {
    --border-color: #e0e0e0;
    --background: transparent;
    margin-bottom: 15px;
}

ion-label {
    font-weight: 500;
    color: #333;
}

ion-input, ion-textarea {
    --background: #f8f9fa;
    --border-radius: 8px;
    --padding-start: 15px;
    --padding-end: 15px;
}

/* Rating System */
.category-rating {
    margin-bottom: 30px;
    text-align: center;
}

.category-rating h3 {
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
}

.rating-container {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin: 15px 0;
}

.rating-star {
    --color: #ddd;
    --color-hover: #ffc107;
    --color-selected: #ffc107;
    font-size: 1.5rem;
    transition: all 0.2s ease;
}

.rating-star:hover {
    --color: #ffc107;
    transform: scale(1.1);
}

.rating-star.selected {
    --color: #ffc107;
}

.rating-star ion-icon {
    font-size: 2rem;
}

/* Questions */
.detailed-questions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.detailed-questions h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
}

.question-item {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.question-item h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-weight: 500;
    font-size: 1.1rem;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 30px;
    padding: 20px 0;
}

.step-actions ion-button {
    flex: 1;
    height: 50px;
    font-weight: 600;
    --border-radius: 25px;
}

.step-actions ion-button[fill="outline"] {
    --color: #667eea;
    --border-color: #667eea;
}

/* Review Section */
.review-section h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.review-section h4 {
    color: #555;
    font-weight: 500;
    margin: 20px 0 10px 0;
}

.score-display {
    font-weight: 600;
    color: #667eea;
    font-size: 1.2rem;
}

/* Loading States */
.loading-container {
    text-align: center;
    padding: 60px 20px;
}

.loading-container ion-spinner {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
}

.loading-container p {
    color: #666;
    font-size: 1.1rem;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
}

.loading-spinner ion-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 15px;
}

/* Success/Error States */
ion-card[color="success"] {
    --background: #d4edda;
    --color: #155724;
    border: 1px solid #c3e6cb;
}

ion-card[color="danger"] {
    --background: #f8d7da;
    --color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .evaluation-container {
        padding: 10px;
    }
    
    .evaluation-header {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .evaluation-header h1 {
        font-size: 2rem;
    }
    
    .step-actions {
        flex-direction: column;
    }
    
    .step-actions ion-button {
        margin-bottom: 10px;
    }
    
    .rating-container {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .rating-star ion-icon {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .evaluation-header h1 {
        font-size: 1.5rem;
    }
    
    ion-card-content {
        padding: 10px 15px 15px 15px;
    }
    
    .question-item {
        padding: 10px;
    }
}

/* Animations */
.step-actions ion-button {
    transition: all 0.3s ease;
}

.step-actions ion-button:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.step-actions ion-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Custom Ionic Overrides */
ion-button {
    --border-radius: 8px;
    font-weight: 500;
}

ion-button[color="success"] {
    --background: #28a745;
    --background-hover: #218838;
}

ion-list {
    background: transparent;
}

ion-item:last-child {
    --border-width: 0;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-20 {
    padding: 20px;
}
