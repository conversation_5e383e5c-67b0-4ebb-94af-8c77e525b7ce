# -*- coding: utf-8 -*-

import json
import logging
import os
from datetime import datetime
from odoo import http, _
from odoo.http import request
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class EvaluationController(http.Controller):

    @http.route('/evaluate/<string:token>', type='http', auth='public', website=True)
    def evaluation_page(self, token, **kwargs):
        try:
            # Validate token
            EvaluationLink = request.env['olivery_driver_evaluation.link'].sudo()
            validation_result = EvaluationLink.validate_token(token)
            
            if not validation_result or 'error' in validation_result:
                if validation_result and validation_result.get('code') == 'USED':
                    return request.render('olivery_driver_evaluation.evaluation_used_template', {
                        'message': _('This evaluation link has already been used.')
                    })
                elif validation_result and validation_result.get('code') == 'EXPIRED':
                    return request.render('olivery_driver_evaluation.evaluation_expired_template', {
                        'message': _('This evaluation link has expired.')
                    })
                else:
                    return request.not_found()
            
            link = validation_result['link']
            user = validation_result['user']
            
            return request.render('olivery_driver_evaluation.evaluation_app_template', {
                'token': token,
                'driver_name': user.username,
                'driver_id': user.id,
                'link_id': link.id,
                'expiry_date': link.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
            })
            
        except Exception as e:
            _logger.error(f"Error serving evaluation page for token {token}: {str(e)}")
            return request.not_found()

    @http.route('/api/evaluation/validate', type='json', auth='public', methods=['POST'], csrf=False)
    def validate_evaluation_token(self, **kwargs):
        try:
            token = kwargs.get('token', {})

            if not token:
                return {'success': False, 'error': f'Token is required {kwargs} {request.params}'}
            
            EvaluationLink = request.env['olivery_driver_evaluation.link'].sudo()
            validation_result = EvaluationLink.validate_token(token)
            
            if not validation_result or 'error' in validation_result:
                return {
                    'success': False,
                    'error': validation_result.get('error', 'Invalid token') if validation_result else 'Invalid token',
                    'code': validation_result.get('code', 'INVALID') if validation_result else 'INVALID'
                }
            
            link = validation_result['link']
            user = validation_result['user']
            
            return {
                'success': True,
                'data': {
                    'driver_name': user.username,
                    'driver_id': user.id,
                    'link_id': link.id,
                    'expiry_date': link.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'token': token
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': 'Internal server error'}

    @http.route('/api/evaluation/submit', type='json', auth='public', methods=['POST'], csrf=False)
    def submit_evaluation(self, **kwargs):
        try:
            token = kwargs.get('token', {})
            evaluation_data = kwargs.get('evaluation_data', {})

            if not token:
                return {'success': False, 'error': 'Token is required'}

            if not evaluation_data:
                return {'success': False, 'error': 'Evaluation data is required'}
            
            EvaluationLink = request.env['olivery_driver_evaluation.link'].sudo()
            validation_result = EvaluationLink.validate_token(token)
            
            if not validation_result or 'error' in validation_result:
                return {
                    'success': False,
                    'error': validation_result.get('error', 'Invalid token') if validation_result else 'Invalid token'
                }
            
            link = validation_result['link']
            user = validation_result['user']
            
            scores = evaluation_data.get('scores', {})
            evaluator = evaluation_data.get('evaluator', {})
            feedback = evaluation_data.get('feedback', '')
            
            individual_scores = [
                scores.get('driving_skills', 0),
                scores.get('punctuality', 0),
                scores.get('customer_service', 0),
                scores.get('vehicle_maintenance', 0),
                scores.get('professionalism', 0)
            ]
            overall_score = (sum(individual_scores) / len([s for s in individual_scores if s > 0])) * 10 if any(individual_scores) else 0
            
            EvaluationResult = request.env['olivery_driver_evaluation.result'].sudo()
            result = EvaluationResult.create({
                'link_id': link.id,
                'user_id': user.id,
                'score': overall_score,
                'feedback': feedback,
                'submission_date': datetime.now(),
                'evaluation_data': json.dumps(evaluation_data),
                'question_1_score': scores.get('question_1', 0),
                'question_2_score': scores.get('question_2', 0),
                'question_3_score': scores.get('question_3', 0),
                'question_4_score': scores.get('question_4', 0),
                'question_5_score': scores.get('question_5', 0),
                'driving_skills_score': scores.get('driving_skills', 0),
                'punctuality_score': scores.get('punctuality', 0),
                'customer_service_score': scores.get('customer_service', 0),
                'vehicle_maintenance_score': scores.get('vehicle_maintenance', 0),
                'professionalism_score': scores.get('professionalism', 0),
            })
            
            return {
                'success': True,
                'message': 'Evaluation submitted successfully',
                'result_id': result.id,
                'overall_score': overall_score
            }
            
        except ValidationError as e:
            _logger.error(f"Validation error in evaluation submission: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            _logger.error(f"Error submitting evaluation: {str(e)}")
            return {'success': False, 'error': 'Internal server error'}

    @http.route('/api/evaluation/validate-http', type='http', auth='public', methods=['POST'], csrf=False)
    def validate_evaluation_token_http(self, **kwargs):
        """HTTP endpoint to validate evaluation token (for testing)"""
        try:
            token = kwargs.get('token')
            _logger.info(f"HTTP endpoint received token: {token}")
            _logger.info(f"HTTP endpoint received kwargs: {kwargs}")

            if not token:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Token is required'}),
                    headers=[('Content-Type', 'application/json')]
                )

            # Validate token
            EvaluationLink = request.env['olivery_driver_evaluation.link'].sudo()
            validation_result = EvaluationLink.validate_token(token)

            if not validation_result or 'error' in validation_result:
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'error': validation_result.get('error', 'Invalid token') if validation_result else 'Invalid token',
                        'code': validation_result.get('code', 'INVALID') if validation_result else 'INVALID'
                    }),
                    headers=[('Content-Type', 'application/json')]
                )

            link = validation_result['link']
            user = validation_result['user']

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'driver_name': user.username,
                        'driver_id': user.id,
                        'link_id': link.id,
                        'expiry_date': link.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
                        'token': token
                    }
                }),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            _logger.error(f"Error validating token via HTTP: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': 'Internal server error'}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/evaluation/config', type='json', auth='public', methods=['POST'], csrf=False)
    def get_evaluation_config(self, **kwargs):
        """API endpoint to get evaluation configuration"""
        try:
            # Get token from params (Angular sends it this way)
            token = kwargs.get('token', {})

            if not token:
                return {'success': False, 'error': 'Token is required'}
            
            # Validate token first
            EvaluationLink = request.env['olivery_driver_evaluation.link'].sudo()
            validation_result = EvaluationLink.validate_token(token)

            if not validation_result or 'error' in validation_result:
                return {'success': False, 'error': 'Invalid token'}

            # Get active configuration
            config = request.env['olivery_driver_evaluation.config'].sudo().get_active_config()
            
            # Default evaluation criteria if not configured
            default_criteria = {
                'categories': [
                    {
                        'id': 'driving_skills',
                        'name': 'Driving Skills',
                        'description': 'Overall driving ability and safety',
                        'max_score': 10
                    },
                    {
                        'id': 'punctuality',
                        'name': 'Punctuality',
                        'description': 'Timeliness and reliability',
                        'max_score': 10
                    },
                    {
                        'id': 'customer_service',
                        'name': 'Customer Service',
                        'description': 'Interaction with customers',
                        'max_score': 10
                    },
                    {
                        'id': 'vehicle_maintenance',
                        'name': 'Vehicle Maintenance',
                        'description': 'Care and maintenance of vehicle',
                        'max_score': 10
                    },
                    {
                        'id': 'professionalism',
                        'name': 'Professionalism',
                        'description': 'Professional behavior and appearance',
                        'max_score': 10
                    }
                ]
            }
            
            # Get dynamic criteria first, fallback to JSON if needed
            criteria = config.get_dynamic_criteria()
            _logger.info(f"Dynamic criteria: {criteria}")

            # If no dynamic criteria or all questions disabled, use JSON criteria or default
            if not criteria.get('categories'):
                _logger.info("No dynamic categories found, using fallback")
                if config.evaluation_criteria:
                    try:
                        criteria = json.loads(config.evaluation_criteria)
                        _logger.info(f"Using JSON criteria: {criteria}")
                    except (json.JSONDecodeError, TypeError):
                        criteria = default_criteria
                        _logger.info("Using default criteria due to JSON error")
                else:
                    criteria = default_criteria
                    _logger.info("Using default criteria - no JSON config")
            else:
                _logger.info(f"Using dynamic criteria with {len(criteria.get('categories', []))} categories")
            
            return {
                'success': True,
                'data': {
                    'evaluation_duration': config.evaluation_duration,
                    'max_attempts': config.max_attempts,
                    'criteria': criteria
                }
            }
            
        except Exception as e:
            _logger.error(f"Error getting evaluation config: {str(e)}")
            return {'success': False, 'error': 'Internal server error'}

    @http.route('/static/src/angular/<path:filename>', type='http', auth='public')
    def serve_angular_files(self, filename, **kwargs):
        """Serve Angular application static files"""
        try:
            import odoo
            addon_path = odoo.modules.get_module_path('olivery_driver_evaluation')
            file_path = os.path.join(addon_path, 'static', 'src', 'angular', filename)
            
            if os.path.exists(file_path) and os.path.isfile(file_path):
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # Set appropriate content type
                content_type = 'text/html'
                if filename.endswith('.js'):
                    content_type = 'application/javascript'
                elif filename.endswith('.css'):
                    content_type = 'text/css'
                elif filename.endswith('.json'):
                    content_type = 'application/json'
                
                return request.make_response(content, headers=[('Content-Type', content_type)])
            else:
                return request.not_found()
                
        except Exception as e:
            _logger.error(f"Error serving Angular file {filename}: {str(e)}")
            return request.not_found()
