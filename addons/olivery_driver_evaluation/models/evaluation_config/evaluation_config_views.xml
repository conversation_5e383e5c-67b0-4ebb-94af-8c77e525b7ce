<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Evaluation Configuration Tree View -->
        <record id="view_evaluation_config_tree" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.config.tree</field>
            <field name="model">olivery_driver_evaluation.config</field>
            <field name="arch" type="xml">
                <tree string="Evaluation Configurations">
                    <field name="name"/>
                    <field name="evaluation_duration"/>
                    <field name="max_attempts"/>
                    <field name="active"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Evaluation Configuration Form View -->
        <record id="view_evaluation_config_form" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.config.form</field>
            <field name="model">olivery_driver_evaluation.config</field>
            <field name="arch" type="xml">
                <form string="Evaluation Configuration">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object"
                                    class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button"
                                       options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="Configuration Name"/>
                            </h1>
                        </div>
                        <group>
                            <group name="basic_settings" string="Basic Settings">
                                <field name="evaluation_duration" 
                                       widget="integer"
                                       help="Number of days before evaluation link expires"/>
                                <field name="max_attempts"
                                       widget="integer"
                                       help="Maximum number of evaluation attempts per user"/>
                                <field name="notification_whatsapp"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="role_settings" string="Role Settings">
                                <field name="eligible_role_ids" 
                                       widget="many2many_tags"
                                       help="User roles that are eligible for evaluation"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Dynamic Questions" name="dynamic_questions">
                                <group>
                                    <group string="Question 1">
                                        <field name="question_1_active"/>
                                        <field name="question_1_title" attrs="{'invisible': [('question_1_active', '=', False)]}"/>
                                        <field name="question_1_description" attrs="{'invisible': [('question_1_active', '=', False)]}"/>
                                    </group>
                                    <group string="Question 2">
                                        <field name="question_2_active"/>
                                        <field name="question_2_title" attrs="{'invisible': [('question_2_active', '=', False)]}"/>
                                        <field name="question_2_description" attrs="{'invisible': [('question_2_active', '=', False)]}"/>
                                    </group>
                                </group>
                                <group>
                                    <group string="Question 3">
                                        <field name="question_3_active"/>
                                        <field name="question_3_title" attrs="{'invisible': [('question_3_active', '=', False)]}"/>
                                        <field name="question_3_description" attrs="{'invisible': [('question_3_active', '=', False)]}"/>
                                    </group>
                                    <group string="Question 4">
                                        <field name="question_4_active"/>
                                        <field name="question_4_title" attrs="{'invisible': [('question_4_active', '=', False)]}"/>
                                        <field name="question_4_description" attrs="{'invisible': [('question_4_active', '=', False)]}"/>
                                    </group>
                                </group>
                                <group>
                                    <group string="Question 5">
                                        <field name="question_5_active"/>
                                        <field name="question_5_title" attrs="{'invisible': [('question_5_active', '=', False)]}"/>
                                        <field name="question_5_description" attrs="{'invisible': [('question_5_active', '=', False)]}"/>
                                    </group>
                                    <group></group>
                                </group>
                            </page>
                            <page string="Advanced Criteria (JSON)" name="criteria">
                                <field name="evaluation_criteria"
                                       widget="text"
                                       placeholder="JSON configuration for evaluation criteria and questions (Advanced users only)"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Evaluation Configuration Search View -->
        <record id="view_evaluation_config_search" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.config.search</field>
            <field name="model">olivery_driver_evaluation.config</field>
            <field name="arch" type="xml">
                <search string="Search Evaluation Configurations">
                    <field name="name"/>
                    <field name="eligible_role_ids"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                        <filter string="Active Status" name="active_status" domain="[]" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Evaluation Configuration Action -->
        <record id="action_evaluation_config" model="ir.actions.act_window">
            <field name="name">Evaluation Configuration</field>
            <field name="res_model">olivery_driver_evaluation.config</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_evaluation_config_tree"/>
            <field name="search_view_id" ref="view_evaluation_config_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first evaluation configuration!
                </p>
                <p>
                    Configure evaluation settings including duration, maximum attempts,
                    eligible roles, and evaluation criteria.
                </p>
            </field>
        </record>

    </data>
</odoo>
