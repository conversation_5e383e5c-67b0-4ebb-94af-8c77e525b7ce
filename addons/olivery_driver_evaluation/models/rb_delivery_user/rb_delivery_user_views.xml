<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- <record id="view_users_form_evaluation" model="ir.ui.view">
            <field name="name">res.users.form.evaluation</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                
                <notebook position="inside">
                    <page string="Driver Evaluation" name="driver_evaluation" groups="base.group_user">
                        <group>
                            <group name="evaluation_stats" string="Evaluation Statistics">
                                <field name="total_evaluations" readonly="1"/>
                                <field name="last_evaluation_date" readonly="1"/>
                                <field name="last_evaluation_score" readonly="1" widget="progressbar"/>
                                <field name="average_evaluation_score" readonly="1" widget="progressbar"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Evaluation Links" name="evaluation_links">
                                <field name="evaluation_link_ids" readonly="1">
                                    <tree string="Evaluation Links" decoration-success="state == 'active'" 
                                          decoration-muted="state == 'used'" decoration-danger="state == 'expired'">
                                        <field name="token" groups="base.group_system"/>
                                        <field name="created_date"/>
                                        <field name="expiry_date"/>
                                        <field name="state"/>
                                        <field name="is_used"/>
                                        <field name="used_date"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Evaluation Results" name="evaluation_results">
                                <field name="evaluation_result_ids" readonly="1">
                                    <tree string="Evaluation Results">
                                        <field name="submission_date"/>
                                        <field name="score" widget="progressbar"/>
                                        <field name="evaluator_name"/>
                                        <field name="driving_skills_score"/>
                                        <field name="punctuality_score"/>
                                        <field name="customer_service_score"/>
                                        <field name="vehicle_maintenance_score"/>
                                        <field name="professionalism_score"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </page>
                </notebook>
            </field>
        </record> -->

    </data>
</odoo>
