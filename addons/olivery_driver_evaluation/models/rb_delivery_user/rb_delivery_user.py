# -*- coding: utf-8 -*-

import uuid
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class rb_delivery_user_evaluation(models.Model):
    _inherit = 'rb_delivery.user'

    evaluation_link_ids = fields.One2many(
        'olivery_driver_evaluation.link',
        'user_id',
        string='Evaluation Links',
        help='Evaluation links generated for this user'
    )

    evaluation_result_ids = fields.One2many(
        'olivery_driver_evaluation.result',
        'user_id',
        string='Evaluation Results',
        help='Evaluation results for this user'
    )
    
    last_evaluation_date = fields.Datetime(
        string='Last Evaluation Date',
        compute='_compute_last_evaluation_date',
        store=True,
        help='Date of the most recent evaluation'
    )
    
    last_evaluation_score = fields.Float(
        string='Last Evaluation Score',
        compute='_compute_last_evaluation_score',
        store=True,
        help='Score from the most recent evaluation'
    )
    
    average_evaluation_score = fields.Float(
        string='Average Evaluation Score',
        compute='_compute_average_evaluation_score',
        store=True,
        help='Average score across all evaluations'
    )
    
    total_evaluations = fields.Integer(
        string='Total Evaluations',
        compute='_compute_total_evaluations',
        store=True,
        help='Total number of completed evaluations'
    )

    @api.depends('evaluation_result_ids')
    def _compute_last_evaluation_date(self):
        for user in self:
            if user.evaluation_result_ids:
                user.last_evaluation_date = max(user.evaluation_result_ids.mapped('submission_date'))
            else:
                user.last_evaluation_date = False

    @api.depends('evaluation_result_ids')
    def _compute_last_evaluation_score(self):
        for user in self:
            if user.evaluation_result_ids:
                latest_result = user.evaluation_result_ids.sorted('submission_date', reverse=True)[0]
                user.last_evaluation_score = latest_result.score
            else:
                user.last_evaluation_score = 0.0

    @api.depends('evaluation_result_ids')
    def _compute_average_evaluation_score(self):
        for user in self:
            if user.evaluation_result_ids:
                scores = user.evaluation_result_ids.mapped('score')
                user.average_evaluation_score = sum(scores) / len(scores)
            else:
                user.average_evaluation_score = 0.0

    @api.depends('evaluation_result_ids')
    def _compute_total_evaluations(self):
        for user in self:
            user.total_evaluations = len(user.evaluation_result_ids)
            
    def test_button_action(self):
        """Simple test method to verify button is working"""
        self.ensure_one()
        raise UserError(_('Button is working! User: %s') % self.username)

    def generate_evaluation_link(self):
        """Generate a new evaluation link for this user"""
        self.ensure_one()

        # Create new evaluation link
        EvaluationLink = self.env['olivery_driver_evaluation.link']
        link = EvaluationLink.create({
            'user_id': self.id,
        })

        # Send notification if configured
        config = self.env['olivery_driver_evaluation.config'].get_active_config()
        if config.notification_whatsapp:
            self._send_evaluation_notification(link)

        return link

    def _send_evaluation_notification(self, evaluation_link):
        """Send email notification about new evaluation link"""
        # This method can be extended to send actual email notifications
        # For now, we'll just log the action
        self.env['mail.message'].create({
            'subject': _('New Evaluation Link Generated'),
            'body': _(
                'A new evaluation link has been generated for user %s. '
                'Link expires on %s.'
            ) % (self.username, evaluation_link.expiry_date),
            'res_id': self.id,
            'model': self._name,
            'message_type': 'notification',
        })
