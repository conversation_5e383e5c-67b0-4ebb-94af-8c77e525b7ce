<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Evaluation Result Tree View -->
        <record id="view_evaluation_result_tree" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.result.tree</field>
            <field name="model">olivery_driver_evaluation.result</field>
            <field name="arch" type="xml">
                <tree string="Evaluation Results">
                    <field name="user_id"/>
                    <field name="submission_date"/>
                    <field name="score" widget="progressbar"/>
                    <field name="driving_skills_score"/>
                    <field name="punctuality_score"/>
                    <field name="customer_service_score"/>
                    <field name="vehicle_maintenance_score"/>
                    <field name="professionalism_score"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Evaluation Result Form View -->
        <record id="view_evaluation_result_form" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.result.form</field>
            <field name="model">olivery_driver_evaluation.result</field>
            <field name="arch" type="xml">
                <form string="Evaluation Result">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="display_name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="user_id"/>
                                <field name="link_id"/>
                                <field name="submission_date"/>
                                <field name="score" widget="progressbar"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Dynamic Question Scores" name="dynamic_scores">
                                <group>
                                    <group name="questions_1_3" string="Questions 1-3">
                                        <field name="question_1_score" widget="float"/>
                                        <field name="question_2_score" widget="float"/>
                                        <field name="question_3_score" widget="float"/>
                                    </group>
                                    <group name="questions_4_5" string="Questions 4-5">
                                        <field name="question_4_score" widget="float"/>
                                        <field name="question_5_score" widget="float"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Legacy Scores" name="legacy_scores" groups="base.group_system">
                                <group>
                                    <group name="skill_scores" string="Skill Scores">
                                        <field name="driving_skills_score" widget="float"/>
                                        <field name="punctuality_score" widget="float"/>
                                        <field name="customer_service_score" widget="float"/>
                                    </group>
                                    <group name="other_scores" string="Other Scores">
                                        <field name="vehicle_maintenance_score" widget="float"/>
                                        <field name="professionalism_score" widget="float"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Feedback" name="feedback">
                                <field name="feedback" widget="text" placeholder="General feedback about the driver..."/>
                            </page>
                            <page string="Detailed Data" name="detailed_data">
                                <field name="evaluation_data" widget="text" 
                                       placeholder="JSON data containing detailed evaluation responses"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Evaluation Result Search View -->
        <record id="view_evaluation_result_search" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.result.search</field>
            <field name="model">olivery_driver_evaluation.result</field>
            <field name="arch" type="xml">
                <search string="Search Evaluation Results">
                    <field name="user_id"/>
                    <filter string="High Scores (80+)" name="high_scores" domain="[('score', '&gt;=', 80)]"/>
                    <filter string="Medium Scores (60-79)" name="medium_scores" domain="[('score', '&gt;=', 60), ('score', '&lt;', 80)]"/>
                    <filter string="Low Scores (&lt;60)" name="low_scores" domain="[('score', '&lt;', 60)]"/>
                    <separator/>
                    <filter string="This Month" name="this_month" 
                            domain="[('submission_date', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('submission_date', '&gt;=', (context_today() - relativedelta(weeks=1)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Driver" name="driver" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="Submission Date" name="submission_date" domain="[]" context="{'group_by': 'submission_date'}"/>
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Evaluation Result Action -->
        <record id="action_evaluation_result" model="ir.actions.act_window">
            <field name="name">Evaluation Results</field>
            <field name="res_model">olivery_driver_evaluation.result</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_evaluation_result_tree"/>
            <field name="search_view_id" ref="view_evaluation_result_search"/>
            <field name="context">{'search_default_this_month': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No evaluation results found!
                </p>
                <p>
                    Evaluation results will appear here once drivers complete their evaluations.
                    You can view detailed scores, feedback, and track performance over time.
                </p>
            </field>
        </record>

    </data>
</odoo>
