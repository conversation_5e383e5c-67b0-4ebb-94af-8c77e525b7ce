# -*- coding: utf-8 -*-

import json
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class evaluation_result(models.Model):
    _name = 'olivery_driver_evaluation.result'
    _description = 'Driver Evaluation Result'
    _rec_name = 'display_name'
    _order = 'submission_date desc'

    link_id = fields.Many2one(
        'olivery_driver_evaluation.link',
        string='Evaluation Link',
        required=True,
        ondelete='cascade',
        help='Associated evaluation link'
    )
    
    user_id = fields.Many2one(
        'rb_delivery.user',
        string='Driver',
        required=True,
        help='Driver who was evaluated'
    )
    
    score = fields.Float(
        string='Overall Score',
        required=True,
        help='Overall evaluation score (0-100)'
    )
    
    feedback = fields.Text(
        string='General Feedback',
        help='General feedback about the driver'
    )
    
    submission_date = fields.Datetime(
        string='Submission Date',
        default=fields.Datetime.now,
        required=True
    )
    
    evaluation_data = fields.Text(
        string='Evaluation Data',
        help='JSON data containing detailed evaluation responses',
        groups='base.group_system'
    )
    
    question_1_score = fields.Float(
        string='Question 1 Score',
        help='Score for question 1 (0-10)'
    )

    question_2_score = fields.Float(
        string='Question 2 Score',
        help='Score for question 2 (0-10)'
    )

    question_3_score = fields.Float(
        string='Question 3 Score',
        help='Score for question 3 (0-10)'
    )

    question_4_score = fields.Float(
        string='Question 4 Score',
        help='Score for question 4 (0-10)'
    )

    question_5_score = fields.Float(
        string='Question 5 Score',
        help='Score for question 5 (0-10)'
    )

    driving_skills_score = fields.Float(
        string='Driving Skills Score',
        help='Score for driving skills (0-10)'
    )

    punctuality_score = fields.Float(
        string='Punctuality Score',
        help='Score for punctuality (0-10)'
    )

    customer_service_score = fields.Float(
        string='Customer Service Score',
        help='Score for customer service (0-10)'
    )

    vehicle_maintenance_score = fields.Float(
        string='Vehicle Maintenance Score',
        help='Score for vehicle maintenance (0-10)'
    )

    professionalism_score = fields.Float(
        string='Professionalism Score',
        help='Score for professionalism (0-10)'
    )
    
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.user.company_id
    )

    @api.depends('user_id', 'submission_date')
    def _compute_display_name(self):
        for record in self:
            if record.user_id and record.submission_date:
                date_str = record.submission_date.strftime('%Y-%m-%d')
                record.display_name = f"{record.user_id.username} - {date_str}"
            else:
                record.display_name = "Evaluation Result"

    @api.constrains('score')
    def _check_score_range(self):
        for record in self:
            if not (0 <= record.score <= 100):
                raise ValidationError(_('Overall score must be between 0 and 100.'))

    @api.constrains('driving_skills_score', 'punctuality_score', 'customer_service_score', 
                    'vehicle_maintenance_score', 'professionalism_score')
    def _check_individual_scores(self):
        for record in self:
            scores = [
                record.driving_skills_score,
                record.punctuality_score,
                record.customer_service_score,
                record.vehicle_maintenance_score,
                record.professionalism_score
            ]
            for score in scores:
                if score and not (0 <= score <= 10):
                    raise ValidationError(_('Individual scores must be between 0 and 10.'))

    @api.model
    def create(self, vals):
        # Calculate overall score if individual scores are provided
        if not vals.get('score'):
            # Try dynamic scores first
            dynamic_scores = [
                vals.get('question_1_score', 0),
                vals.get('question_2_score', 0),
                vals.get('question_3_score', 0),
                vals.get('question_4_score', 0),
                vals.get('question_5_score', 0)
            ]

            # If no dynamic scores, use legacy scores
            if not any(dynamic_scores):
                individual_scores = [
                    vals.get('driving_skills_score', 0),
                    vals.get('punctuality_score', 0),
                    vals.get('customer_service_score', 0),
                    vals.get('vehicle_maintenance_score', 0),
                    vals.get('professionalism_score', 0)
                ]
                dynamic_scores = individual_scores

            if any(dynamic_scores):
                valid_scores = [s for s in dynamic_scores if s > 0]
                if valid_scores:
                    vals['score'] = (sum(valid_scores) / len(valid_scores)) * 10
        
        result = super(evaluation_result, self).create(vals)
        
        if result.link_id and not result.link_id.is_used:
            result.link_id.mark_as_used()
        
        return result

    def get_evaluation_data_json(self):
        """Return evaluation data as parsed JSON"""
        self.ensure_one()
        if self.evaluation_data:
            try:
                return json.loads(self.evaluation_data)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    def set_evaluation_data_json(self, data):
        """Set evaluation data from dictionary"""
        self.ensure_one()
        self.evaluation_data = json.dumps(data) if data else False

    @api.model
    def get_driver_evaluation_history(self, user_id):
        """Get evaluation history for a specific driver"""
        return self.search([('user_id', '=', user_id)], order='submission_date desc')

    @api.model
    def get_evaluation_statistics(self):
        """Get overall evaluation statistics"""
        results = self.search([])
        if not results:
            return {}
        
        return {
            'total_evaluations': len(results),
            'average_score': sum(results.mapped('score')) / len(results),
            'average_driving_skills': sum(results.mapped('driving_skills_score')) / len(results),
            'average_punctuality': sum(results.mapped('punctuality_score')) / len(results),
            'average_customer_service': sum(results.mapped('customer_service_score')) / len(results),
            'average_vehicle_maintenance': sum(results.mapped('vehicle_maintenance_score')) / len(results),
            'average_professionalism': sum(results.mapped('professionalism_score')) / len(results),
        }
