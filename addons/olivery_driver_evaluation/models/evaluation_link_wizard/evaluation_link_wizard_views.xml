<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Evaluation Link Generation Wizard Form -->
        <record id="view_evaluation_link_wizard_form" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.wizard.form</field>
            <field name="model">olivery_driver_evaluation.wizard</field>
            <field name="arch" type="xml">
                <form string="Generate Evaluation Links">
                    <div class="alert alert-info" role="alert">
                        <p><strong>Generate Evaluation Links</strong></p>
                        <p>This wizard will generate evaluation links for the selected drivers. Each link will be valid for the configured duration and can be used once.</p>
                    </div>
                    
                    <group>
                        <field name="user_ids" widget="many2many_tags"
                               options="{'no_create': True, 'no_create_edit': True}"/>
                    </group>
                    
                    <group>
                        <field name="force_generate"/>
                        <field name="send_notification"/>
                    </group>
                    
                    <div class="alert alert-warning" role="alert" attrs="{'invisible': [('force_generate', '=', False)]}">
                        <p><strong>Warning:</strong> Force generate is enabled. This will create new links even for users who already have active evaluation links.</p>
                    </div>
                    
                    <footer>
                        <button name="action_generate_links" string="Generate Links" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Wizard Action for Context Menu (Users List View) -->
        <record id="action_evaluation_link_wizard_context" model="ir.actions.act_window">
            <field name="name">Generate Evaluation Links</field>
            <field name="res_model">olivery_driver_evaluation.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="base.model_res_users"/>
            <field name="binding_view_types">list</field>
        </record>

        <!-- Wizard Action for Menu -->
        <record id="action_evaluation_link_wizard" model="ir.actions.act_window">
            <field name="name">Generate Evaluation Links</field>
            <field name="res_model">olivery_driver_evaluation.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
