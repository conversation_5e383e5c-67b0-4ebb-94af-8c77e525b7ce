# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class evaluation_link_wizard(models.TransientModel):
    _name = 'olivery_driver_evaluation.wizard'
    _description = 'Generate Evaluation Links Wizard'

    user_ids = fields.Many2many(
        'rb_delivery.user',
        string='Drivers',
        required=True,
        help='Select drivers for whom to generate evaluation links'
    )
    
    force_generate = fields.Boolean(
        string='Force Generate',
        default=False,
        help='Generate links even if active links already exist for the user'
    )
    
    send_notification = fields.Boolean(
        string='Send Email Notification',
        default=True,
        help='Send email notification to administrators about generated links'
    )

    @api.model
    def default_get(self, fields_list):
        res = super(evaluation_link_wizard, self).default_get(fields_list)
        
        if self.env.context.get('active_model') == 'rb_delivery.user' and self.env.context.get('active_ids'):
            res['user_ids'] = [(6, 0, self.env.context.get('active_ids'))]
        
        return res

    def action_generate_links(self):
        self.ensure_one()
        
        if not self.user_ids:
            raise UserError(_('Please select at least one driver.'))
        
        generated_links = []
        failed_users = []
        
        for user in self.user_ids:
            try:
                if not self.force_generate:
                    active_links = user.evaluation_link_ids.filtered(
                        lambda l: not l.is_used and not l.is_expired
                    )
                    if active_links:
                        failed_users.append({
                            'user': user,
                            'reason': _('User already has active evaluation links')
                        })
                        continue
                
                evaluation_link = user.generate_evaluation_link()
                generated_links.append(evaluation_link)
                
            except UserError as e:
                failed_users.append({
                    'user': user,
                    'reason': str(e)
                })
        
        message_parts = []
        
        if generated_links:
            message_parts.append(
                _('Successfully generated %d evaluation links.') % len(generated_links)
            )
        
        if failed_users:
            failed_list = '\n'.join([
                f"• {fail['user'].name}: {fail['reason']}" 
                for fail in failed_users
            ])
            message_parts.append(
                _('Failed to generate links for %d users:\n%s') % (len(failed_users), failed_list)
            )
        
        if generated_links:
            return {
                'type': 'ir.actions.act_window',
                'name': _('Generated Evaluation Links'),
                'res_model': 'olivery_driver_evaluation.link',
                'view_mode': 'tree,form',
                'domain': [('id', 'in', [link.id for link in generated_links])],
                'context': {
                    'search_default_active': 1,
                    'generation_summary': '\n'.join(message_parts)
                }
            }
        else:
            # Show error message
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Generation Failed'),
                    'message': '\n'.join(message_parts),
                    'type': 'danger',
                    'sticky': True,
                }
            }

    @api.onchange('user_ids')
    def _onchange_user_ids(self):
        """Update domain to show only eligible users"""
        if not self.user_ids:
            return
        
        # Get active configuration
        config = self.env['olivery_driver_evaluation.config'].get_active_config()
        
        # Check eligibility for selected users
        ineligible_users = []
        for user in self.user_ids:
            user_groups = user.group_id
            eligible_roles = config.eligible_role_ids
            if eligible_roles and not any(group in eligible_roles for group in user_groups):
                ineligible_users.append(user.name)
        
        if ineligible_users:
            return {
                'warning': {
                    'title': _('Ineligible Users Selected'),
                    'message': _(
                        'The following users are not eligible for evaluation based on current configuration:\n%s'
                    ) % '\n'.join(ineligible_users)
                }
            }
