# -*- coding: utf-8 -*-

import uuid
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class evaluation_link(models.Model):
    _name = 'olivery_driver_evaluation.link'
    _description = 'Driver Evaluation Link'
    _rec_name = 'token'
    _order = 'created_date desc'

    user_id = fields.Many2one(
        'rb_delivery.user',
        string='Driver',
        required=True,
        help='Driver to be evaluated'
    )
    
    token = fields.Char(
        string='Evaluation Token',
        required=True,
        index=True,
        copy=False,
        help='Unique token for evaluation access',
        groups='base.group_system'
    )
    
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        required=True
    )
    
    expiry_date = fields.Datetime(
        string='Expiry Date',
        required=True,
        help='Date when the evaluation link expires'
    )
    
    is_used = fields.Boolean(
        string='Is Used',
        default=False,
        help='Whether the evaluation link has been used'
    )
    
    used_date = fields.Datetime(
        string='Used Date',
        help='Date when the evaluation was completed'
    )
    
    evaluator_info = fields.Text(
        string='Evaluator Information',
        help='Information about the person conducting the evaluation'
    )
    
    evaluation_url = fields.Char(
        string='Evaluation URL',
        compute='_compute_evaluation_url',
        help='Complete URL for the evaluation'
    )
    
    is_expired = fields.Boolean(
        string='Is Expired',
        compute='_compute_is_expired',
        help='Whether the evaluation link has expired'
    )
    
    state = fields.Selection([
        ('active', 'Active'),
        ('used', 'Used'),
        ('expired', 'Expired'),
    ], string='State', compute='_compute_state', store=True)
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.user.company_id
    )

    @api.depends('expiry_date')
    def _compute_is_expired(self):
        now = fields.Datetime.now()
        for record in self:
            record.is_expired = record.expiry_date and record.expiry_date < now

    @api.depends('is_used', 'is_expired')
    def _compute_state(self):
        for record in self:
            if record.is_used:
                record.state = 'used'
            elif record.is_expired:
                record.state = 'expired'
            else:
                record.state = 'active'

    @api.depends('token')
    def _compute_evaluation_url(self):
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        for record in self:
            if record.token:
                record.evaluation_url = f"{base_url}/evaluate/{record.token}"
            else:
                record.evaluation_url = False

    @api.model
    def create(self, vals):
        if not vals.get('token'):
            vals['token'] = str(uuid.uuid4())
        
        if not vals.get('expiry_date'):
            config = self.env['olivery_driver_evaluation.config'].get_active_config()
            created_date = fields.Datetime.from_string(vals.get('created_date', fields.Datetime.now()))
            vals['expiry_date'] = created_date + timedelta(days=config.evaluation_duration)
        
        return super(evaluation_link, self).create(vals)

    @api.constrains('token')
    def _check_token_unique(self):
        for record in self:
            if self.search_count([('token', '=', record.token), ('id', '!=', record.id)]) > 0:
                raise ValidationError(_('Evaluation token must be unique.'))

    def mark_as_used(self):
        self.ensure_one()
        if self.is_used:
            raise UserError(_('This evaluation link has already been used.'))
        if self.is_expired:
            raise UserError(_('This evaluation link has expired.'))
        
        self.write({
            'is_used': True,
            'used_date': fields.Datetime.now(),
        })

    @api.model
    def validate_token(self, token):
        link = self.search([('token', '=', token)], limit=1)
        if not link:
            return False
        
        if link.is_used:
            return {'error': 'Link already used', 'code': 'USED'}
        
        if link.is_expired:
            return {'error': 'Link expired', 'code': 'EXPIRED'}
        
        return {'link': link, 'user': link.user_id}

    def regenerate_token(self):
        self.ensure_one()
        if self.is_used:
            raise UserError(_('Cannot regenerate token for used evaluation link.'))
        
        config = self.env['olivery_driver_evaluation.config'].get_active_config()
        self.write({
            'token': str(uuid.uuid4()),
            'created_date': fields.Datetime.now(),
            'expiry_date': fields.Datetime.now() + timedelta(days=config.evaluation_duration),
            'is_used': False,
            'used_date': False,
            'evaluator_info': False
        })
