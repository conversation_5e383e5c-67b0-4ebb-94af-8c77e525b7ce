<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Evaluation Link Tree View -->
        <record id="view_evaluation_link_tree" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.link.tree</field>
            <field name="model">olivery_driver_evaluation.link</field>
            <field name="arch" type="xml">
                <tree string="Evaluation Links" decoration-success="state == 'active'" 
                      decoration-muted="state == 'used'" decoration-danger="state == 'expired'">
                    <field name="user_id"/>
                    <field name="token"/>
                    <field name="created_date"/>
                    <field name="expiry_date"/>
                    <field name="state"/>
                    <field name="is_used"/>
                    <field name="used_date"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Evaluation Link Form View -->
        <record id="view_evaluation_link_form" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.link.form</field>
            <field name="model">olivery_driver_evaluation.link</field>
            <field name="arch" type="xml">
                <form string="Evaluation Link">
                    <header>
                        <field name="state" widget="statusbar" statusbar_visible="active,used,expired"/>
                        <button name="regenerate_token" type="object" string="Regenerate Token"
                                attrs="{'invisible': [('is_used', '=', True)]}"
                                class="btn-primary"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="token" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="user_id"/>
                                <field name="created_date"/>
                                <field name="expiry_date"/>
                                <field name="is_expired"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="usage_info" string="Usage Information">
                                <field name="is_used"/>
                                <field name="used_date" attrs="{'invisible': [('is_used', '=', False)]}"/>
                            </group>
                        </group>
                        <group string="Evaluation URL">
                            <field name="evaluation_url" widget="url" readonly="1"/>
                        </group>
                        <group string="Evaluator Information" 
                               attrs="{'invisible': [('evaluator_info', '=', False)]}">
                            <field name="evaluator_info" widget="text" readonly="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Evaluation Link Search View -->
        <record id="view_evaluation_link_search" model="ir.ui.view">
            <field name="name">olivery_driver_evaluation.link.search</field>
            <field name="model">olivery_driver_evaluation.link</field>
            <field name="arch" type="xml">
                <search string="Search Evaluation Links">
                    <field name="user_id"/>
                    <field name="token"/>
                    <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                    <filter string="Used" name="used" domain="[('state', '=', 'used')]"/>
                    <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                    <separator/>
                    <filter string="This Month" name="this_month" 
                            domain="[('created_date', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('created_date', '&gt;=', (context_today() - relativedelta(weeks=1)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Driver" name="driver" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="State" name="state_group" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Creation Date" name="creation_date" domain="[]" context="{'group_by': 'created_date'}"/>
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Evaluation Link Action -->
        <record id="action_evaluation_link" model="ir.actions.act_window">
            <field name="name">Evaluation Links</field>
            <field name="res_model">olivery_driver_evaluation.link</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_evaluation_link_tree"/>
            <field name="search_view_id" ref="view_evaluation_link_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No evaluation links found!
                </p>
                <p>
                    Evaluation links are generated automatically when requested for drivers.
                    You can manage and monitor all evaluation links from here.
                </p>
            </field>
        </record>

    </data>
</odoo>
