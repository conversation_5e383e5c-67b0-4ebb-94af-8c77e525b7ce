# -*- coding: utf-8 -*-
from odoo import http,_
from odoo.http import request
import json
import logging

from openerp.http import request, Response

from openerp import http
from openerp.addons.auth_signup.controllers.main import AuthSignupHome
from openerp.addons.web.controllers.main import ensure_db


_logger = logging.getLogger(__name__)

class olivery_vhub(http.Controller):

    @http.route('/get_partner_status', auth='none', type="json")
    def get_partner_status(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login.lower(), password)

        if uid == False:
            Response.status = 403
            return False
        returned_vals = []
        if 'order_ids' in rec and rec['order_ids']:
            order_ids = rec['order_ids']
            for order_id in order_ids:
                order = request.env['rb_delivery.order'].sudo().search([('sequence','=',order_id),'|',('active','=',False),('active','=',True)])
                if order:
                    returned_val = {'order_id':order_id,'internal_partner_status':order.state}
                    state = request.env['rb_delivery.status'].search([('name','=',order.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
                    if state:
                        returned_val['partner_status'] = state.title
                    returned_vals.append(returned_val)
        if len(returned_vals)>0:
            args = {'code':200,'success': True, 'orders': returned_vals}
        else:
            args = {'code':401,'fail': True, 'message': "No orders were found"}
        return args

    @http.route('/get_partner_collection', auth='none', type="json")
    def get_partner_collection(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login.lower(), password)

        if uid == False:
            Response.status = 403
            return False
        returned_vals = []
        domain = ['|',('active','=',False),('active','=',True)]
        if 'collection_number' in rec and rec['collection_number']:
            collection_number = rec['collection_number']
            domain.append(('sequence','=',collection_number))
        if 'exclude_collections' in rec and rec['exclude_collections']:
            exclude_collections = rec['exclude_collections']
            domain.append(('sequence','not in',exclude_collections))
        collections = request.env['rb_delivery.multi_print_orders_money_collector'].search(domain)
        if len(collections)==0:
            args = {'code':401,'fail': True, 'message': "No collections found"}
        else:
            for collection in collections:
                orders = collection.order_ids
                for order_id in orders:
                    returned_val = {'order_reference':order_id.reference_id,'paid_cost':order_id.required_from_business,'company_collection_sequence':collection.sequence}
                    if order_id.returned_reason:
                        returned_val['returned_reason']=order_id.returned_reason.name
                    returned_vals.append(returned_val)
            if len(returned_vals)>0:
                args = {'code':200,'success': True, 'orders': returned_vals}

            else:
                args = {'code':401,'fail': True, 'message': "No orders were found"}

        return args

    @http.route('/sync_vhub_orders', auth='none', type="json")
    def sync_vhub_orders(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login.lower(), password)

        agent = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])
        if not agent:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}

        if rec.get('reference_ids'):
            ref_ids = rec.get('reference_ids',[])
            username = agent.username
            failed_orders = []
            success_orders = []
            success_refs = []
            vals_list = []
            for ref_id in ref_ids:
                order = request.env['rb_delivery.order'].sudo().search([('sequence','=',ref_id)])
                if order:
                    vhub_message = _("Order has been successfully synced with %s")%(username)
                    values = {'assign_to_agent':agent.id,'is_sender_partner_order':True,'partner_reference_id':ref_id,'vhub_logs':vhub_message}

                    message = _("Order has been updated by %s through function sync_order_vhub.")%(username)
                    data = {'uid':request._uid,'message':message,'records':order,'values':values,'update':True}
                    request.env['rb_delivery.utility'].olivery_sudo(data)
                    success_orders.append(order.sequence)
                    success_refs.append(ref_id)
                    vhub_conf = request.env['rb_delivery.vhub_configuration'].sudo().search([])
                    vhub_fields = vhub_conf.fields_reflected_on_create
                    vals = request.env['rb_delivery.vhub_transaction'].get_vhub_default_fields({},False,vhub_fields,order,vhub_conf)
                    vals_list.append(vals)
                else:
                    failed_orders.append(ref_id)
            args = {'code':200,'success': True,'sequences':success_orders,'success_refs':success_refs,'vals_list':vals_list,'not_found_sequences':failed_orders}

        else:
            args = {'code':403,'success': False, 'message': _('Make sure reference ID is added')}

        return args
