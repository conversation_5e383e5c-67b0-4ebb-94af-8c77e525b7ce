<odoo >
    <data noupdate="1">
        <record id="sub_area_vhub_default" model="rb_delivery.sub_area">
            <field name="name">VHub Default</field>
            <field name="code">vhub_default</field>
            <field name="show_in_register">False</field>
            <field name="show_in_create">False</field>
            <field name="parent_id" eval="ref('rb_delivery.area_vhub_default')"/>
        </record>
        <record id="business_follower_map_username" model="rb_delivery.vhub_follower_field_map">
            <field name="user_field_id" ref="rb_delivery.field_rb_delivery_user__username"></field>
            <field name="order_field_id" ref="rb_delivery.field_rb_delivery_order__follower_store_name"></field>
        </record>
        <record id="business_follower_map_mobile_number" model="rb_delivery.vhub_follower_field_map">
            <field name="user_field_id" ref="rb_delivery.field_rb_delivery_user__mobile_number"></field>
            <field name="order_field_id" ref="rb_delivery.field_rb_delivery_order__follower_mobile_number"></field>
        </record>
        <record id="business_follower_map_area" model="rb_delivery.vhub_follower_field_map">
            <field name="user_field_id" ref="rb_delivery.field_rb_delivery_user__area_id"></field>
            <field name="order_field_id" ref="rb_delivery.field_rb_delivery_order__follower_area"></field>
        </record>
        <record id="business_follower_map_address" model="rb_delivery.vhub_follower_field_map">
            <field name="user_field_id" ref="rb_delivery.field_rb_delivery_user__address"></field>
            <field name="order_field_id" ref="rb_delivery.field_rb_delivery_order__follower_address"></field>
        </record>
        <record id="vhub_configuration_item" model="rb_delivery.vhub_configuration">
            <field name="fields_reflected_on_create" eval="[(6,0,[
            ref('rb_delivery.field_rb_delivery_order__customer_address'),ref('rb_delivery.field_rb_delivery_order__customer_mobile'),
            ref('rb_delivery.field_rb_delivery_order__customer_name'),ref('rb_delivery.field_rb_delivery_order__customer_area'),
            ref('rb_delivery.field_rb_delivery_order__note'),ref('rb_delivery.field_rb_delivery_order__second_mobile_number'),
            ref('rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile'),ref('rb_delivery.field_rb_delivery_order__cus_second_whatsapp_mobile'),
            ref('rb_delivery.field_rb_delivery_order__order_weight'),ref('rb_delivery.field_rb_delivery_order__product_note'),
            ref('rb_delivery.field_rb_delivery_order__longitude'),ref('rb_delivery.field_rb_delivery_order__latitude'),])]"/>
            <field name="fields_reflected_to_delivery" eval="[(6,0,[
            ref('rb_delivery.field_rb_delivery_order__customer_address'),ref('rb_delivery.field_rb_delivery_order__customer_mobile'),
            ref('rb_delivery.field_rb_delivery_order__customer_name'),ref('rb_delivery.field_rb_delivery_order__customer_area'),
            ref('rb_delivery.field_rb_delivery_order__note'),ref('rb_delivery.field_rb_delivery_order__second_mobile_number'),
            ref('rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile'),ref('rb_delivery.field_rb_delivery_order__cus_second_whatsapp_mobile'),
            ref('rb_delivery.field_rb_delivery_order__order_weight'),ref('rb_delivery.field_rb_delivery_order__product_note'),
            ref('rb_delivery.field_rb_delivery_order__longitude'),ref('rb_delivery.field_rb_delivery_order__latitude'),])]"/>
            <field name="fields_reflected_to_business" eval="[(6,0,[
            ref('rb_delivery.field_rb_delivery_order__note'),ref('rb_delivery.field_rb_delivery_order__reject_reason'),
            ref('rb_delivery.field_rb_delivery_order__product_note'),ref('rb_delivery.field_rb_delivery_order__stuck_comment')])]"/>
            <field name="business_state_ids" eval="[(6,0,[
            ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch'),
            ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up')])]"/>
            <field name="business_follower_fields" eval="[(6,0,[
            ref('olivery_vhub.business_follower_map_address'),ref('olivery_vhub.business_follower_map_mobile_number'),
            ref('olivery_vhub.business_follower_map_area'),ref('olivery_vhub.business_follower_map_username')])]"/>
            <field name="send_business_info_as_follower">True</field>
        </record>
    </data>
</odoo>