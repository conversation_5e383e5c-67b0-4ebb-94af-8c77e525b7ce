# -*- coding: utf-8 -*-
{
    'name': "olivery_vhub",
    'summary': """
        <PERSON><PERSON> Vhub from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.3.17',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/module_view.xml',
        'models/status/status_view.xml',
        'models/order/order_view.xml',
        'models/order/order_wizard_view.xml',
        'demo/demo.xml',
        'demo/cron_job.xml',
        'demo/status.xml',
        'models/vhub_field_security/vhub_field_security_view.xml',
        'models/vhub_configuration/vhub_configuration_view.xml',
        'models/vhub_status_map/vhub_status_map_view.xml',
        'models/vhub_status_logs/vhub_status_logs_view.xml',
        'models/user/user_view.xml',
        'models/sub_area_map/sub_area_map_view.xml',
        'models/vhub_follower_field_map/vhub_follower_field_map_view.xml',
        'models/vhub_transaction/vhub_transaction_view.xml',
        'models/vhub_transaction_item/vhub_transaction_item_view.xml',
        'demo/mobile_action_items.xml',
        'demo/error_log_demo.xml',
        'demo/client_conf.xml',
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
