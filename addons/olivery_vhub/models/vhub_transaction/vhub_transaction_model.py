# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
import requests
import json
from odoo.exceptions import Warning


class rb_delivery_vhub_transaction(models.Model):

    _name = 'rb_delivery.vhub_transaction'
    _inherit = 'mail.thread'
    _order = 'create_date DESC'

    name = fields.Char('Name',track_visibility='on_change', readonly=True)

    vhub_transaction_item_ids = fields.One2many('rb_delivery.vhub_transaction_item',string='Vhub transaction item',inverse_name="vhub_transaction_id")

    company_user = fields.Many2one('rb_delivery.user',track_visibility="on_change",string="Company",default="", readonly=True)

    job_queue_uuid = fields.Char('Queue job uuid',readonly=True)

    def set_all_as_edited(self):
        self.vhub_transaction_item_ids.write({'edited':True})
        return

    def get_jq_records(self):
        address_form_id = self.env.ref('queue_job.view_queue_job_tree').id
        domain = [('uuid', 'in', self.job_queue_uuid.split(','))]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Job queue',
            'res_model': 'queue.job',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        order_ids = []
        if self._context.get('orders'):
            if self._context.get('orders') == 'all':
                order_ids = [vhub_transaction_item.order_id.id for vhub_transaction_item in self.vhub_transaction_item_ids]
            elif self._context.get('orders') == 'failed':
                order_ids = [vhub_transaction_item.order_id.id for vhub_transaction_item in self.vhub_transaction_item_ids.filtered(lambda x:x.success==False)]
            elif self._context.get('orders') == 'success':
                order_ids = [vhub_transaction_item.order_id.id for vhub_transaction_item in self.vhub_transaction_item_ids.filtered(lambda x:x.success==True)]

        domain = [('id', 'in', order_ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Orders',
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def send_to_company(self):
        failed_transactions = self.vhub_transaction_item_ids.filtered(lambda x:x.success==False and x.edited == True)
        if len(failed_transactions) == 0:
            raise Warning(_("Issue: There is no failed orders that are edited.\n What to do next: make sure to edit orders that are failed, and check the Edited field True."))
        queue_id = self.with_delay(channel="root.vhub",max_retries=2).prepare_orders(failed_transactions,self.company_user)
        self.job_queue_uuid = str(self.job_queue_uuid) + ',' + str(queue_id.uuid)

    @api.model
    def create(self,values):
        if values.get('vhub_transaction_item_ids'):
            vhub_transaction_item_ids = values.get('vhub_transaction_item_ids')[0][2]
            vhub_transaction_items = self.env['rb_delivery.vhub_transaction_item'].browse(vhub_transaction_item_ids)
            company = self.env['rb_delivery.user'].browse(values.get('company_user'))
            if vhub_transaction_items:
                queue_id = self.with_delay(channel="root.vhub",max_retries=2).prepare_orders(vhub_transaction_items,company)
                values['job_queue_uuid'] = queue_id.uuid
        return super(rb_delivery_vhub_transaction, self).create(values)

    def get_fields_values(self,vals,business,fields):
        for field in fields:
            if field.order_field_id and field.user_field_id:
                user_field =  field.user_field_id
                order_field_name = field.order_field_id.name
                user_field_name = field.user_field_id.name
                if business[user_field_name]:
                    if user_field.ttype == "many2one":
                        if user_field.relation == 'rb_delivery.user':
                            vals[order_field_name] = business[user_field_name].username
                        else:
                            vals[order_field_name] = business[user_field_name].name
                    elif user_field.ttype == 'datetime':
                        vals[order_field_name] = business[user_field_name].strftime('%Y-%m-%d %H:%M:%S')
                    elif user_field.ttype == 'date':
                        vals[order_field_name] = business[user_field_name].strftime('%Y-%m-%d')
                    elif user_field.ttype != "many2many":
                        vals[order_field_name] = business[user_field_name]
        return vals

    def get_vhub_default_fields(self,vals,transaction_item,vhub_fields,order,vhub_conf):
        for vhub_field in vhub_fields:
            field_name = vhub_field.name
            if order[field_name]:
                if vhub_field.ttype == "many2one":
                    if vhub_field.relation == 'rb_delivery.user':
                        vals[field_name] = order[field_name].sudo().username
                    else:
                        vals[field_name] = order[field_name].name
                elif vhub_field.ttype == 'datetime':
                    vals[field_name] = order[field_name].strftime('%Y-%m-%d %H:%M:%S')
                elif vhub_field.ttype == 'date':
                    vals[field_name] = order[field_name].strftime('%Y-%m-%d')
                elif vhub_field.ttype != "many2many":
                    vals[field_name] = order[field_name]
        if vhub_conf.reference_id_field and order.reference_id:
            vals[vhub_conf.reference_id_field] = order.reference_id
        if order.sudo().assign_to_business and order.sudo().assign_to_business.inclusive_delivery:
            vals['cost'] = order.copy_total_cost
        else:
            vals['cost'] = order.money_collection_cost
        if order.second_mobile_number:
            vals['second_mobile_number'] = order.second_mobile_number
        if transaction_item:
            vals['partner_vhub_transaction_item_id'] = transaction_item.id
        return vals

    def prepare_orders(self,vhub_transaction_items,company):
        jq_messages = []
        vals_list = []
        area_field  = self.env['ir.model.fields'].search([('name','=','customer_area'),('model','=','rb_delivery.order')],limit=1)
        sub_area_field  = self.env['ir.model.fields'].search([('name','=','customer_sub_area'),('model','=','rb_delivery.order')],limit=1)
        for vhub_transaction_item in vhub_transaction_items:
            order = vhub_transaction_item.order_id
            vhub_conf = self.env['rb_delivery.vhub_configuration'].sudo().search([])
            default_vhub_status = vhub_conf.default_vhub_status
            fields_changed = ['assign_to_agent','partner_reference_id']
            if default_vhub_status:
                fields_changed.append('state')
                self.env['rb_delivery.order'].authorize_change_status(default_vhub_status.name,False,order.state)
            self.env['rb_delivery.order'].authorize_edit(fields_changed,order.state)

            vals = {'reference_id':order.sequence,'is_partner_order':True}
            vhub_fields = vhub_conf.fields_reflected_on_create
            if sub_area_field in vhub_fields and area_field not in vhub_fields:
                vhub_fields |= area_field

            send_sales_info_as_business = vhub_conf.send_sales_info_as_business
            send_sales_info_as_follower = vhub_conf.send_sales_info_as_follower

            if send_sales_info_as_business or send_sales_info_as_follower:
                user_create = order.create_uid.id
                user = self.env['rb_delivery.user'].search([('user_id', '=', user_create)])
                if user and user.group_id:
                    group = user.group_id
                    if group.code == 'rb_delivery.role_sales':
                        business = self.env['rb_delivery.user'].search([('default_sale_user','=',user.id)],limit=1)
                        if business:
                            if send_sales_info_as_business:
                                vals['assign_to_business'] = business.mobile_number
                            elif send_sales_info_as_follower:
                                if vhub_conf.sales_follower_fields:
                                    vals = self.get_fields_values(vals,business,vhub_conf.sales_follower_fields)

            send_business_info_as_follower = vhub_conf.send_business_info_as_follower
            if (order.sudo().assign_to_business and order.sudo().assign_to_business.show_follower_vhub) or send_business_info_as_follower:
                if vhub_conf.business_follower_fields:
                    business = order.sudo().assign_to_business
                    vals = self.get_fields_values(vals,business,vhub_conf.business_follower_fields)

            vals = self.get_vhub_default_fields(vals,vhub_transaction_item,vhub_fields,order,vhub_conf)
            vals_list.append(vals)
        jq_message = self.send_orders(vals_list,company,vhub_transaction_items)
        jq_messages.append(jq_message)
        return jq_messages

    def failed_order(self,failed_reference_ids,failed_messages,transaction_items,name,company,jq_messages=[]):
        for reference in failed_reference_ids:
            index = failed_reference_ids.index(reference)
            fail_message = failed_messages[index]
            order = self.env['rb_delivery.order'].search([('sequence','=',reference)],limit=1)
            transaction_item = transaction_items.filtered(lambda transaction: transaction.order_id.id == order.id)
            message = _("Issue in sending vhub order: %s. To company: %s")%(str(fail_message),name)
            order.write({'vhub_sync':False,'vhub_company':company.id,'vhub_logs':message})
            order.message_post(body=message)
            transaction_item.write({'vhub_message':message,'success':False})
            jq_message = {'sequence':order.sequence,'success':False,'message':message}
            jq_messages.append(jq_message)

    def success_orders(self,reference_ids,transaction_items,partner_refs,company,name,jq_messages=[]):
        for reference in reference_ids:
            index = reference_ids.index(reference)
            partner_reference = partner_refs[index]
            order = self.env['rb_delivery.order'].search([('sequence','=',reference)],limit=1)
            transaction_item = transaction_items.filtered(lambda transaction: transaction.order_id.id == order.id)
            message = _("Order has been successfully sent to %s")%(name)
            order_vals = self.get_order_vals(company,partner_reference,message,transaction_item)
            try:
                order.write(order_vals)
                transaction_item.write({'vhub_message':message,'success':True})
                jq_message = {'sequence':order.sequence,'success':True,'message':message}
                jq_messages.append(jq_message)
            except Exception as e:
                message = _("Issue in sending vhub order: %s. To company: %s")%(str(e),name)
                self.failed_order([reference],[message],transaction_items,name,company,jq_messages)



    #inherit module [storex_modules]
    def get_order_vals(self,company,partner_sequence,message,transaction_item):
        order_vals = {'assign_to_agent':company.id,'is_sender_partner_order':True,'partner_reference_id':partner_sequence,'vhub_logs':message}
        vhub_conf = self.env['rb_delivery.vhub_configuration'].sudo().search([])
        default_vhub_status = vhub_conf.default_vhub_status
        if default_vhub_status:
            order_vals['state'] = default_vhub_status.name
        return order_vals

    def send_orders(self,vals,company,transaction_items):
        params = {'login':company.sudo().company_username,'password':company.sudo().company_password,'db':company.sudo().company_db,'lang':self.env.user.lang,'orders_list':vals}
        jq_message = ''
        params = {
            "jsonrpc": "2.0",
            "params": params}
        headers = {'content-type': 'application/json'}
        company_url = company.sudo().company_url
        response = requests.post(company_url+'/create_multi_orders', data=json.dumps(params), headers=headers)
        name = ''
        if company.sudo().commercial_name:
            name =  company.sudo().commercial_name
        else:
            name =  company.sudo().company_db
        jq_messages = []
        if response.status_code == 200:
            json_res = response.json()
            if json_res.get('result'):
                result = json_res.get('result')
                if result.get('code') and result.get('code') == 200:
                    if result.get('Sequences') and result.get('References'):
                        reference_ids = result.get('References')
                        partner_refs = result.get('Sequences')
                        self.success_orders(reference_ids,transaction_items,partner_refs,company,name,jq_messages)

                    if result.get('existing_records') and result.get('existing_sequence_records'):
                        exist_reference_ids = result.get('existing_records')
                        exist_partner_refs = result.get('existing_sequence_records')
                        self.success_orders(exist_reference_ids,transaction_items,exist_partner_refs,company,name,jq_messages)
                    if result.get('fail_references') and result.get('fail_message'):
                        failed_reference_ids = result.get('fail_references')
                        failed_messages = result.get('fail_message')
                        self.failed_order(failed_reference_ids,failed_messages,transaction_items,name,company,jq_messages)
                else:
                    if result.get('existing_records') and result.get('existing_sequence_records'):
                        exist_reference_ids = result.get('existing_records')
                        exist_partner_refs = result.get('existing_sequence_records')
                        self.success_orders(exist_reference_ids,transaction_items,exist_partner_refs,company,name,jq_messages)
                    if result.get('fail_references') and result.get('message'):
                        failed_reference_ids = result.get('fail_references')
                        failed_messages = result.get('message')
                        self.failed_order(failed_reference_ids,failed_messages,transaction_items,name,company,jq_messages)
            else:
                return response.text
        else:
            return response.text

        return jq_message


