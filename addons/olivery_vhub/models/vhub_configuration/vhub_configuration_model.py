# -*- coding: utf-8 -*-
from datetime import date
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import requests
import json
import os

class rb_delivery_vhub_configuration(models.Model):

    _name = 'rb_delivery.vhub_configuration'
    _inherit = 'mail.thread'

    def get_fields(self):
        field_names = ['customer_name','customer_mobile','customer_area','customer_address','customer_sub_area','customer_country','note','product_note','reject_reason','stuck_comment','cus_whatsapp_mobile','cus_second_whatsapp_mobile','second_mobile_number','order_weight','longitude','latitude','follower_address','follower_ref_id','follower_area','follower_store_name','follower_mobile_number','follower_second_mobile_number','follower_longitude','follower_latitude','cost','copy_total_cost','customer_payment','reschedule_date','extra_cost','replacement_order']
        fields = self.env['ir.model.fields'].search([('model','=','rb_delivery.order'),('name','in',field_names)])
        return [('id', 'in', fields.ids)]

    def _get_reflect_driver_details_desc(self):
        self.reflect_driver_details_desc = _('If this configuration is turned to "True" it will cause the "driver information" to be reflected from delivery company to business company.')

    def _get_fields_reflected_to_delivery_desc(self):
        self.fields_reflected_to_delivery_desc = _('(These fields will be filled only if the system is business company)\n Fields added to field "Business fields" should be filled with fields that will be reflected from business company to delivery company.\n Fields will be reflected on change only when the actual “Delivery company” status field is in one of the statuses in field "Delivery company states" or there is no change happened on the status from delivery company.')

    def _get_fields_reflected_to_business_desc(self):
        self.fields_reflected_to_business_desc =  _('(These fields will be filled only if the system is delivery company)\nFields added to field "Delivery fields" should be filled with fields that will be reflected from delivery company to business company, the fields will be reflected when changed in order.')

    def _get_fields_reflected_on_creates_desc(self):
        self.fields_reflected_on_create_desc = _('Fields added to field "VHub fields" should be filled with fields that will be reflected from business company to delivery company when first send the order through VHub.')

    def _get_default_vhub_status_desc(self):
        self.default_vhub_status_desc = _('The "Olivery" order in business company will be set to the status in field "Default Storex Vhub Status" when the order is sent successfully to "Delivery company".')

    def _get_default_storex_vhub_status_desc(self):
        self.default_storex_vhub_status_desc = _('The "Storex" order in business company will be set to the status in field "Default Vhub Status" when the order is sent successfully to "Delivery company".')

    def _get_default_fields_desc(self):
        self.default_fields_desc = _('(These fields will be filled only if the system is "Business company")')

    def _get_send_sales_info_as_business_desc(self):
        self.send_sales_info_as_business_desc = _('When field "Send Sales Info as Business" is set to True, the mobile number of "Sender\'s sales user" will be sent to "Delivery company" as a business, so in this case you\'ll have to make sure the mobile number of the sales user is added as a business as a child under the main business created for the "Business company" in "Delivery company"')

    def _get_send_sales_info_as_follower_desc(self):
        self.send_send_sales_info_as_follower_desc = _('When field "Send Sales Info as Follower" is set to True, the fields in user column (Sales user of the business) mapped with Order (Follower fields) in field "Sales follower fields" will be sent as follower to the "Delivery company"')

    def _get_send_business_info_as_follower_desc(self):
        self.send_send_business_info_as_follower_desc = _('When field "Send Business Info as Follower" is set to True, the fields in user column (Business user of the order) mapped with Order (Follower fields) in field "Business follower fields" will be sent as follower to the "Delivery company"')

    fields_reflected_to_delivery_desc = fields.Text('Business fields Description',compute="_get_fields_reflected_to_delivery_desc",readonly=True)

    fields_reflected_to_business_desc = fields.Text('Delivery fields Description',compute="_get_fields_reflected_to_business_desc",readonly=True)

    fields_reflected_on_create_desc = fields.Text('Create VHub fields Description',compute="_get_fields_reflected_on_creates_desc",readonly=True)

    default_vhub_status_desc = fields.Text('Default VHub status Description',compute="_get_default_vhub_status_desc",readonly=True)

    default_storex_vhub_status_desc = fields.Text('Default storex VHub status Description',compute="_get_default_storex_vhub_status_desc",readonly=True)

    default_fields_desc = fields.Text('Default fields description',compute="_get_default_fields_desc")

    send_sales_info_as_business_desc = fields.Text('Sales info as business description',compute="_get_send_sales_info_as_business_desc")

    send_send_sales_info_as_follower_desc = fields.Text('Sales info as follower description',compute="_get_send_sales_info_as_follower_desc")

    send_send_business_info_as_follower_desc = fields.Text('Business info as follower description',compute="_get_send_business_info_as_follower_desc")

    fields_reflected_to_delivery = fields.Many2many(
        comodel_name = 'ir.model.fields',
        string = 'Business fields',
        relation = 'delivery_com_fields_field_ids',
        column1 = 'delivery_com_field',
        column2 = 'field_id',domain=get_fields)

    fields_reflected_to_business = fields.Many2many(
        comodel_name = 'ir.model.fields',
        string = 'Delivery fields',
        relation = 'business_com_fields_field_ids',
        column1 = 'business_com_field',
        column2="field_id",domain=get_fields)

    fields_reflected_on_create = fields.Many2many(
        comodel_name = 'ir.model.fields',
        string = 'VHub fields',
        relation = 'vhub_fields_field_ids',
        column1 = 'vhub_field',
        column2="field_id",domain=get_fields)

    business_state_ids = fields.Many2many(comodel_name = 'rb_delivery.status',
        string = 'Delivery company states',
        relation = 'vhub_conf_status_table',
        column1 = 'status_id',
        column2="vhub_conf_id",
        track_visibility="on_change",domain="[('status_type','=','olivery_order')]")

    business_follower_fields = fields.Many2many(comodel_name = 'rb_delivery.vhub_follower_field_map',
        string = 'Business follower fields',
        relation = 'vhub_conf_business_follower_table',
        column1 = 'business_follower_id',
        column2="vhub_conf_id",
        track_visibility="on_change")

    sales_follower_fields = fields.Many2many(comodel_name = 'rb_delivery.vhub_follower_field_map',
        string = 'Sales follower fields',
        relation = 'vhub_conf_sales_follower_table',
        column1 = 'sales_follower_id',
        column2="vhub_conf_id",
        track_visibility="on_change")

    default_vhub_status = fields.Many2one('rb_delivery.status','Default Vhub Status',domain="[('status_type','=','olivery_order')]")

    default_storex_vhub_status = fields.Many2one('rb_delivery.status','Default Storex Vhub Status',domain="[('status_type','=','storex_order')]")

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'order_client_configuration',
        column1 = 'client_configuration_id',
        column2 = 'order_id')

    user_id = fields.Many2one('rb_delivery.user',string="User")

    vhub_completed_statuses = fields.Many2many(comodel_name = 'rb_delivery.status',
        string = 'Sync Partner Status excluding these states',
        relation = 'vhub_completed_status_table',
        column1 = 'status_id',
        column2="vhub_conf_id",
        track_visibility="on_change",domain="[('status_type','=','olivery_order')]")

    send_sales_info_as_business = fields.Boolean("Send Sales Info as Business", track_visibility="on_change")

    send_sales_info_as_follower = fields.Boolean("Send Sales Info as Follower", track_visibility="on_change")

    send_business_info_as_follower = fields.Boolean("Send Business Info as Follower", track_visibility="on_change")

    reference_id_field = fields.Char('Reference ID field',track_visibility="on_change",default="follower_ref_id")

    reflect_driver_details = fields.Boolean('Reflect driver details',track_visibility="on_change")

    reflect_driver_details_desc = fields.Text('Reflect driver details Description',compute="_get_reflect_driver_details_desc",readonly=True)

    def send_status(self):
        if len(self.order_ids):
            orders = self.order_ids
            for order in orders:
                order.write({"state":"in_progress"})
        else:
            raise ValidationError(_("Please add orders."))

    def send_order(self):
        if self.user_id and len(self.order_ids):
            user = self.user_id
            group = user.group_id
            order_ids = self.order_ids
            self.env['rb_delivery.select_company_user'].get_orders(order_ids,user)

    def reset_configuration(self):
        fields_reflected_to_delivery = [(6,0,[
            self.env.ref('rb_delivery.field_rb_delivery_order__customer_address').id,self.env.ref('rb_delivery.field_rb_delivery_order__customer_mobile').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__customer_name').id,self.env.ref('rb_delivery.field_rb_delivery_order__customer_area').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__note').id,self.env.ref('rb_delivery.field_rb_delivery_order__second_mobile_number').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile').id,self.env.ref('rb_delivery.field_rb_delivery_order__cus_second_whatsapp_mobile').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__order_weight').id,self.env.ref('rb_delivery.field_rb_delivery_order__product_note').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__longitude').id,self.env.ref('rb_delivery.field_rb_delivery_order__latitude').id])]

        fields_reflected_to_business = [(6,0,[
            self.env.ref('rb_delivery.field_rb_delivery_order__note').id,self.env.ref('rb_delivery.field_rb_delivery_order__reject_reason').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__product_note').id,self.env.ref('rb_delivery.field_rb_delivery_order__stuck_comment').id])]

        business_state_ids = [(6,0,[
            self.env.ref('rb_delivery.status_waiting').id,self.env.ref('rb_delivery.status_in_branch').id,
            self.env.ref('rb_delivery.status_picked_up').id,self.env.ref('rb_delivery.status_picking_up').id])]

        fields_reflected_on_create = [(6,0,[
            self.env.ref('rb_delivery.field_rb_delivery_order__customer_address').id,self.env.ref('rb_delivery.field_rb_delivery_order__customer_mobile').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__customer_name').id,self.env.ref('rb_delivery.field_rb_delivery_order__customer_area').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__note').id,self.env.ref('rb_delivery.field_rb_delivery_order__second_mobile_number').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile').id,self.env.ref('rb_delivery.field_rb_delivery_order__cus_second_whatsapp_mobile').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__order_weight').id,self.env.ref('rb_delivery.field_rb_delivery_order__product_note').id,
            self.env.ref('rb_delivery.field_rb_delivery_order__longitude').id,self.env.ref('rb_delivery.field_rb_delivery_order__latitude').id])]

        business_follower_fields = [(6,0,[self.env.ref('olivery_vhub.business_follower_map_address').id,self.env.ref('olivery_vhub.business_follower_map_mobile_number').id,
            self.env.ref('olivery_vhub.business_follower_map_area').id,self.env.ref('olivery_vhub.business_follower_map_username').id])]

        vals = {'fields_reflected_on_create':fields_reflected_on_create,'business_state_ids':business_state_ids,'fields_reflected_to_business':fields_reflected_to_business,'fields_reflected_to_delivery':fields_reflected_to_delivery,'business_follower_fields':business_follower_fields,'send_business_info_as_follower':True}
        self.write(vals)
        return

    def log_changes(self, values):
        messages = []
        for field in values:
            field_definition = self._fields[field]
            model_name = field_definition.comodel_name
            field_label = field_definition.string

            if isinstance(values[field], list) and len(values[field]):
                if len(values[field][0]) > 2:
                    ids = values[field][0][2]

                    if model_name == 'rb_delivery.vhub_follower_field_map':
                        old_values = [{'user_field':rec_field.user_field_id.display_name,'order_field':rec_field.order_field_id.display_name} for rec_field in self[field]]
                    else:
                        old_values = [rec_field.name for rec_field in self[field]]
                    new_values = self.get_fields_names(model_name, ids)

                    message = f'{field_label} changed from {old_values or None} to {new_values or None} <br />'
                    messages.append(message)

        messages_str = ''.join(messages)
        if len(messages_str):
            self.message_post(body=messages_str)

    def get_fields_names(self, model_name, ids):

        field_records = self.env[model_name].browse(ids)
        if model_name == 'rb_delivery.vhub_follower_field_map':
            return [{'user_field':field.user_field_id.display_name,'order_field':field.order_field_id.display_name} for field in field_records]
        return [field.name for field in field_records]

    def write(self,values):
        self.log_changes(values)
        return super(rb_delivery_vhub_configuration, self).write(values)