# -*- coding: utf-8 -*-

import json
import logging
from openerp import models, fields, api, _
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
import time
from datetime import datetime, timedelta
import requests
import json
import os
import re
import urllib3
requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
_logger = logging.getLogger(__name__)


class olivery_vhub_user(models.Model):

    _inherit = 'rb_delivery.user'

    show_follower_vhub = fields.Boolean('Show follower Vhub')

    state_map_ids = fields.Many2many(comodel_name = 'rb_delivery.vhub_status_map',
        string = 'Status Map',
        relation = 'vhub_status_map_user_rel',
        column1 = 'vhub_status_map_id',
        column2="user_id",
        track_visibility="on_change")

    order_id = fields.Many2one('rb_delivery.order',"Vhub order", track_visibility="on_change")

    vhub_messages = fields.Text('Vhub Messages', track_visibility="on_change",readonly=True)

    server_url = fields.Char("Server URL", track_visibility="on_change")

    server_db = fields.Char("Server DB", track_visibility="on_change")

    @api.multi
    def write(self, values):
      
        if 'password' in values and not self.env.user.has_group('rb_delivery.role_configuration_manager') and self.is_company:
                raise UserError(_("You are not allowed to change the password for a VHub user, only users with Configuration manager role can do so. What to do next: You can contact support in order to change this user’s password."))
        if 'company_url' in values and values['company_url'] and values['company_url'].endswith('/'):
            values['company_url'] = values['company_url'][:-len('/')]
        return super(olivery_vhub_user, self).write(values)


    def test_send_status(self):
        messages = []
        if not self.server_db or not self.server_url:
            raise Warning(_("Please add server DB and server URL"))
        if self.order_id:
            for state_map in self.state_map_ids:
                vhub_vals = {}
                order_id = self.order_id.sequence
                if self.role_code in ['rb_delivery.role_driver','rb_delivery.role_delivery_company','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative']:
                    vhub_vals = {'internal_partner_status':state_map.name}
                elif self.role_code == 'rb_delivery.role_business':
                    vhub_vals = {'internal_sender_partner_status':state_map.name}

                if vhub_vals:
                    data =  {
                        "jsonrpc": "2.0",
                        "params": {
                            "login":self.mobile_number,
                            "password": self.password,
                            "db":self.server_db,
                            "order_id":order_id,
                            "vals":vhub_vals}}
                    headers = {'content-type': 'application/json'}

                    response = requests.post(self.server_url + '/edit_order', data=json.dumps(data), headers=headers)
                    if response.status_code == 200:
                        json_response = response.json()
                        if 'result' in json_response and json_response['result']:
                            result = json_response['result']
                            if 'code' in result and result['code']:
                                code = result['code']
                                if code == 200:
                                    message = _('Order with status '+ state_map.name+' was updated successfully')
                                    messages.append(message)
                                else:
                                    if 'message' in result and result['message']:
                                        message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + result['message'].replace("\n", "")
                                        messages.append(message)
                                    else:
                                        message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + result
                                        messages.append(message)
                        else:
                            json_response = response.json()
                            if 'message' in json_response and json_response['message']:
                                message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + json_response['message'].replace("\n","")
                                messages.append(message)
                            elif 'error' in json_response and json_response['error'] and 'data' in json_response['error'] and json_response['error']['data'] and 'message' in json_response['error']['data'] and json_response['error']['data']['message']:
                                message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + json_response['error']['data']['message']
                                messages.append(message)
                            else:
                                message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + response.text
                                messages.append(message)
                    else:
                        message = _("Order failed to be updated to status ") + state_map.name + _(" because of ") + response.text
                        messages.append(message)
                    time.sleep(5)
        if len(messages) > 0:
            self.vhub_messages = '\n'.join(messages)

    def sync_sub_areas(self):
        sub_areas_arr = []
        sub_areas = self.env['rb_delivery.sub_area'].search([])
        sub_area_map = self.sub_area_map
        for sub_area in sub_areas:
            sub_areas_arr.append(sub_area.name)
        for sub_area in sub_area_map:
            sub_areas_arr.append(sub_area.name)
        partner_sub_areas = {}
        partner_sub_area_names = []
        params = {
            "jsonrpc": "2.0",
            "params": {
                "login":self.company_username,
                "password": self.company_password,
                "db":self.company_db}}
        headers = {'content-type': 'application/json'}
        response = requests.post(self.company_url+'/get_sub_areas', data=json.dumps(params), headers=headers)
        if response.status_code == 200:
            response_json = response.json()
            for sub_area in response_json.get('result', []):
                if sub_area.get('name'):
                    partner_sub_area_names.append(sub_area['name'])
                    if sub_area.get('area') and sub_area.get('area').get('name'):
                        if sub_area['name'] in partner_sub_areas:
                            partner_sub_areas[sub_area['name']].append(sub_area['area']['name'])
                        else:
                            partner_sub_areas[sub_area['name']] = [sub_area['area']['name']]

        if partner_sub_area_names:
            default_area = self.env.ref('olivery_vhub.sub_area_vhub_default')
            if default_area:
                default_area_id = default_area.id

                sub_area_maps = []
                for sub_area_name in partner_sub_areas.keys():
                    sub_area_val = default_area_id
                    if partner_sub_areas.get(sub_area_name):
                        for area in partner_sub_areas.get(sub_area_name):
                            if sub_area_name in sub_areas_arr:
                                area_id = self.env['rb_delivery.area'].search([('name', '=', area)], limit=1).id
                                sub_area_val = self.env['rb_delivery.sub_area'].search([('name', '=', sub_area_name), ('parent_id', '=', area_id)], limit=1).id
                            if not sub_area_val:
                                sub_area_val = default_area_id
                            vals = {'sub_area_id': sub_area_val, 'name': sub_area_name}
                            vals['imported_area'] = area
                            sub_area_maps.append(vals)
                    else:
                        vals = {'sub_area_id': sub_area_val, 'name': sub_area_name}
                        sub_area_maps.append(vals)
                created_sub_area_maps = self.env['rb_delivery.sub_area_map'].sudo().create(sub_area_maps)

                if created_sub_area_maps:
                    self.sub_area_map = [(4, sub_area_map.id) for sub_area_map in created_sub_area_maps]

        return