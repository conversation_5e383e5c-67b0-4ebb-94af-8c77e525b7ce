# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_vhub
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-10 09:49+0000\n"
"PO-Revision-Date: 2024-01-10 09:49+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/user/user_model.py:83
#: code:addons/olivery_vhub/models/user/user_model.py:86
#: code:addons/olivery_vhub/models/user/user_model.py:91
#: code:addons/olivery_vhub/models/user/user_model.py:94
#: code:addons/olivery_vhub/models/user/user_model.py:97
#: code:addons/olivery_vhub/models/user/user_model.py:100
#, python-format
msgid " beacsue of "
msgstr "بسبب "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:345
#, python-format
msgid "%s %s"
msgstr ""

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:40
#, python-format
msgid "(These fields will be filled only if the system is \"Business company\")"
msgstr "(هذه الحقول سوف يتم تعبئتها في حال كان النظام  \"شركة التاجر\")"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:25
#, python-format
msgid "(These fields will be filled only if the system is business company)\n"
" Fields added to field \"Business fields\" should be filled with fields that will be reflected from business company to delivery company.\n"
" Fields will be reflected on change only when the actual “Delivery company” status field is in one of the statuses in field \"Delivery company states\" or there is no change happened on the status from delivery company."
msgstr "(هذه الحقول سوف يتم تعبئتها في حال كان النظام المرسل(التاجر))\n"
" الحقول المضافة للحقل \"حقول التاجر\" يجب ان يتم تعبئتها بالحقول التي شوف تنعكس من نظام شركة التاجر لنظام شركة المرسل.\n"
" الحقول ستنعكس عند تغييرها فقط في حال قيمة الحالة في  “شركة التوصيل ” هي احد الحالات في حقل  \"حالات شركة التوصيل\" او لم يحدث اي تغيير على حالة الطلب في شركة التوصيل."

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:28
#, python-format
msgid "(These fields will be filled only if the system is delivery company)\n"
"Fields added to field \"Delivery fields\" should be filled with fields that will be reflected from delivery company to business company, the fields will be reflected when changed in order."
msgstr "(هذه الحقول سيتم تعبئتها في نظام شركة التوصيل)\n"
"الحقول المضافة \"لحقول التوصيل\" يجب ان تحتوي على الحقول التي سوف تعكس من شركة التوصيل لشركة التاجر، الحقول ستنعكس عند تغييرها"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_transaction
msgid "<span style=\"color:green;\">Show success orders</span>"
msgstr "<span style=\"color:green;\">عرض الطلبيات المرسلة</span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_transaction
msgid "<span style=\"color:red;\">Show failed orders</span>"
msgstr "<span style=\"color:red;\">عرض الطلبيات الغير مرسلة</span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_transaction
msgid "<span>Job Queue</span>"
msgstr ""

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_kanban_vhub_status_map
msgid "<span>Reflected Status: </span>"
msgstr "<span>الحالة المعكوسة: </span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_transaction
msgid "<span>Send to company</span>"
msgstr "<span>ابعث الطلبيات للشركة</span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_transaction
msgid "<span>Show all orders</span>"
msgstr "<span>عرض جميع الطلبيات</span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_kanban_vhub_status_map
msgid "<span>Status : </span>"
msgstr "<span>الحالة : </span>"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_olivery_vhub_order
msgid "<span>VHub Status logs</span>"
msgstr "<span>سجلات حالة VHub</span>"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_needaction
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_needaction
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_needaction
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_attachment_count
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_attachment_count
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_attachment_count
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
#: selection:rb_delivery.refresh_partner_status,company_type:0
msgid "Business company"
msgstr "التاجر (المرسل)"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_to_delivery
msgid "Business fields"
msgstr "حقول التاجر (المرسل)"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_to_delivery_desc
msgid "Business fields Description"
msgstr "وصف حقول التاجر (المرسل)"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__business_follower_fields
msgid "Business follower fields"
msgstr "حقول التاجر التابع"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_send_business_info_as_follower_desc
msgid "Business info as follower description"
msgstr "وصف معلومات التاجر كتابع"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_order_select_company_user
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_refresh_partner_status
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__company_id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__company_user
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__company_user
msgid "Company"
msgstr "المؤسسة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__company_type
msgid "Company partner type"
msgstr "نوع شركة الشريك"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_on_create_desc
msgid "Create VHub fields Description"
msgstr "وصف حقول انشاء VHub"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Fields to be sent"
msgstr "حقول المرسلة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__create_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__create_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__default_storex_vhub_status
msgid "Default Storex Vhub Status"
msgstr " الافتراضية  Vhub  حالات ستوريكس "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__default_vhub_status_desc
msgid "Default VHub status Description"
msgstr  "الافتراضية VHub  وصف حالات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__default_vhub_status
msgid "Default Vhub Status"
msgstr "الافتراضية  Vhub  حالات   "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__default_fields_desc
msgid "Default fields description"
msgstr "وصف الحقول الافتراضية "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__default_storex_vhub_status_desc
msgid "Default storex VHub status Description"
msgstr "وصف حالات ستوريكس VHUB الافتراضية "

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Defaults"
msgstr "الافتراضي  "

#. module: olivery_vhub
#: selection:rb_delivery.refresh_partner_status,company_type:0
msgid "Delivery Company"
msgstr "شركة التوصيل"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Delivery company"
msgstr "شركة التوصيل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__business_state_ids
msgid "Delivery company states"
msgstr "حالة شركة التوصيل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_to_business
msgid "Delivery fields"
msgstr "حقول التوصيل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_to_business_desc
msgid "Delivery fields Description"
msgstr "وصف حقول التوصيل "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__display_name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__edited
msgid "Edited"
msgstr "معدل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__field_ids
msgid "Fields"
msgstr "الحقول"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:31
#, python-format
msgid "Fields added to field \"VHub fields\" should be filled with fields that will be reflected from business company to delivery company when first send the order through VHub."
msgstr "الحقول المضافة لحقل \"حقول Vhub\" يجب ان تحتوي على الحقول التي ستنعكس من شركةالتاجر لشركة التوصيل عند بعث الطلبية اول مرة من خلال VHub"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Follower Info"
msgstr "معلومات التابع "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_follower_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_follower_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_follower_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_channel_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_channel_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_channel_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_partner_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_partner_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_partner_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_search_rb_delivery_vhub_field_security
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_unread
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_unread
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_unread
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_needaction
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_needaction
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_needaction
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_has_error
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_has_error
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_has_error
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__internal_sender_partner_status
msgid "Internal Sender Partner Status"
msgstr "حالة شريك المرسل الداخلي"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_is_follower
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_is_follower
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_is_follower
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:212
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:226
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:230
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:234
#, python-format
msgid "Issue in sending vhub order: %s. To company: %s"
msgstr "مشكلة في ارسال طلبيات الربط السريع: %s. للشركة: %"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:60
#, python-format
msgid "Issue: There is no failed orders that are edited.\n"
" What to do next: make sure to edit orders that are failed, and check the Edited field True."
msgstr "المشكلة: لا يوجد طلبيات غير مرسلة و معدلة.\n"
" ما يمكنك فعله: تاكد من تعديل المشاكل في الطلبيات التي لم ترسل، و عدل الحقل معدل."

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction____last_update
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__write_uid
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_refresh_partner_status__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__write_date
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:53
#, python-format
msgid "Make sure to add username, password, and DB to company user"
msgstr "قم بالتأكد مناضافة اسم المستخدم ,كلمة المرور , واسم قاعدة البيانات لشركة التوثيل "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__name
msgid "Message"
msgstr "الرسالة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_has_error
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_has_error
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_has_error
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_follower_field_map
msgid "Model to map between user fields and order fields"
msgstr ""

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__name
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__name
msgid "Name"
msgstr "الاسم"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:109
#, python-format
msgid "No orders were received"
msgstr "لا يوجد طلبات مستلمة "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_needaction_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_needaction_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_needaction_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_has_error_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_has_error_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_has_error_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__message_unread_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__message_unread_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__message_unread_counter
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__order_id
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__order_id
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_tree_rb_delivery_vhub_transaction_item
msgid "Order"
msgstr "الأمر"

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/user/user_model.py:83
#: code:addons/olivery_vhub/models/user/user_model.py:86
#: code:addons/olivery_vhub/models/user/user_model.py:91
#: code:addons/olivery_vhub/models/user/user_model.py:94
#: code:addons/olivery_vhub/models/user/user_model.py:97
#: code:addons/olivery_vhub/models/user/user_model.py:100
#, python-format
msgid "Order failed to be updated to status "
msgstr "فشل تحديث الطلب إلى الحالة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__order_field_id
msgid "Order field"
msgstr "حقل الطلبية"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_transaction/vhub_transaction_model.py:203
#, python-format
msgid "Order has been successfully sent to %s"
msgstr "تم ارسال الطلبية بنجاح ل  %s"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:99
#, python-format
msgid "Order has been successfully synced with "
msgstr "تم مزامنة الطلب بنجاح مع "

#. module: olivery_vhub
#: code:addons/olivery_vhub/controllers/controllers.py:110
#, python-format
msgid "Order has been successfully synced with %s"
msgstr "تم مزامنة الطلبية بنجاح مع %s"

#. module: olivery_vhub
#: code:addons/olivery_vhub/controllers/controllers.py:113
#, python-format
msgid "Order has been updated by %s through function sync_order_vhub."
msgstr "تم تعديل الطلبية من خلال %s باستخدام sync_order_vhub."

#. module: olivery_vhub
#: code:addons/olivery_vhub/controllers/controllers.py:127
#, python-format
msgid "Make sure reference ID is added"
msgstr "تاكد من اضافة الرقم المرجعي"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:116
#: code:addons/olivery_vhub/models/order/order_model.py:120
#, python-format
msgid "Issue in sync vhub orders %s"
msgstr "هناك مشكلة في مزامنة طلبيات ال vhub %s"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:118
#, python-format
msgid "Issue in sync vhub orders %s %s"
msgstr "هناك مشكلة في مزامنة طلبيات ال vhub %s %s"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:103
#, python-format
msgid "Order of sequence  partner status was not refreshed because of "
msgstr "الطلب ذات رقم التسلسل في حالة الشريك لم يتم اعادة تحميله بسبب "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:97
#, python-format
msgid "Order of sequence  partner status was successfully refreshed"
msgstr "الطلب ذات رقم التسلسل ف يحالة الشريك تم اعادة تحميله بنجاح"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:189
#, python-format
msgid "Order of sequence \"%s\" is set as a partner order but the \"Sender\" field is not set. What you can do next: Go to the order of sequence \"%s\" and add the business related to the company."
msgstr "الطلبية للرقم التسلسلي \"%s\" و اضف التاجر التابع للشركة المرسلة \"%s\" هي طلبية شريك لكن الحقل \"التاجر\" غير مضبوط. ماذا يمكن ان تفعل: الذهاب الى الطلبية للرقم التسلسلي"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:191
#, python-format
msgid "Order of sequence \"%s\" is set as a partner order but the sender \"%s\" added to the order does not have VHub Username or VHub password. What you can do next: Go to the Sender \"%s\" in order of sequence \"%s\" and add VHub configuration to the user."
msgstr "الطلبية للرقم التسلسلي \"%s\" هي طلبية شريكة لكن التاجر \"%s\" المضاف للطلبية لا يحتوي على VHub اسم المستخدم او كلمة السر. ماذا تسطيع ان تفعل: اذهب الى التاجر \"%s\" في الطلبية للرقم التسلسلي \"%s\" و عبئ اعدادات VHub للمستخدم."

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:194
#, python-format
msgid "Order of sequence \"%s\" is set as a sender partner order but the \"Agent\" field is not set. What you can do next: Send the order of sequence \"%s\" once again to the company \"%s\"."
msgstr "الطلبية للرقم التسلسلي \"%s\" هي طلبية شريك لكن \"السائق\" الحقل غير مضبوط. ماذا تستطيع ان تفعل: ابعث الطلبية \"%s\" مرة اخرى للشركة \"%s\"."

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:196
#, python-format
msgid "Order of sequence \"%s\" is set as a sender partner order but the agent \"%s\" added to the order does not have VHub Username or VHub password. What you can do next: Go to the Agent \"%s\" in order of sequence \"%s\" and add VHub configuration to the user."
msgstr "الطلبية للرقم التسلسلي \"%s\" هي طلبية شريك لكن السائق \"%s\" المضاف للطلبية لا يحتوي علي VHub اسم المستخدم او كلمة السر.ماذا تستطيع ان تفعل: اذهب الى السائق \"%s\" في الطلبية للرقم التسلسلي \"%s\" و عبئ اعدادات VHub."

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:265
#, python-format
msgid "Order was not updated due to "
msgstr "لم يتم تحديث الطلب بسبب"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:249
#, python-format
msgid "Order was updated successfully"
msgstr "تم تحديث الطلب بنجاح"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/user/user_model.py:79
#, python-format
msgid "Order with status  was updated successfully"
msgstr "تم تحديث الطلب مع الحالة بنجاح"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__order_ids
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:142
#, python-format
msgid "Please Set The Company Url For The Selected Company!"
msgstr "الرجاء تعيين رابط شركه التوصيل المختاره"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:145
#, python-format
msgid "Please add orders."
msgstr "يرجى إضافة الطلبيات "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/user/user_model.py:50
#, python-format
msgid "Please add server DB and server URL"
msgstr "يرجى ادخال رابط واسم قاعدة البيانات للنظام "


#. module: olivery_vhub
#: code:addons/olivery_vhub/models/user/user_model.py:44
#, python-format
msgid "You are not allowed to change the password for a VHub user, only users with Configuration manager role can do so. What to do next: You can contact support in order to change this user’s password."
msgstr "لا يُسمح لك بتغيير كلمة المرور لمستخدم VHub، فقط المستخدمون الذين لديهم دور مدير الاعدادات يمكنهم القيام بذلك. ما يجب عليك فعله بعد ذلك: يمكنك الاتصال بالدعم لتغيير كلمة مرور هذا المستخدم."

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__job_queue_uuid
msgid "Queue job uuid"
msgstr "Queue job uuid"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__name
msgid "Received Status"
msgstr "الحالة المستلمة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__reference_id_field
msgid "Reference ID field"
msgstr "حقل الرقم المرجعي"

#. module: olivery_vhub
#. openerp-web
#: code:addons/olivery_vhub/static/src/xml/button.xml:6
#, python-format
msgid "Refresh Partner Status"
msgstr "تحديث حالة الشريك"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Reset configurations"
msgstr "إعادة ضبط الاعدادات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__group_id
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_search_rb_delivery_vhub_field_security
msgid "Role"
msgstr "صلاحية"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__sales_follower_fields
msgid "Sales follower fields"
msgstr "حقول التابع لمندوب المبيعات"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_sales_info_as_business_desc
msgid "Sales info as business description"
msgstr "معلومات المندوب  ك وصف المرسل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_send_sales_info_as_follower_desc
msgid "Sales info as follower description"
msgstr "معلومات المندوب  ك وصف التابع"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_order_select_company_user
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_refresh_partner_status
msgid "Save"
msgstr "حفظ"

#. module: olivery_vhub
#: model:ir.actions.act_window,name:olivery_vhub.action_rb_delivery_order_select_company_user
msgid "Select Company"
msgstr "اختر المؤسسة"

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_select_company_user
msgid "Select company user"
msgstr "اختيار مؤسسة "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_business_info_as_follower
msgid "Send Business Info as Follower"
msgstr "ارسال معلومات التاجر ك تابع "

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Send Order"
msgstr "ارسال الطلب"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_sales_info_as_business
msgid "Send Sales Info as Business"
msgstr " ارسال معلومات المندوب ك مرسل "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__send_sales_info_as_follower
msgid "Send Sales Info as Follower"
msgstr "ارسال معلومات المندوب ك تابع "

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Send Status"
msgstr "ارسال الحالات"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__sender_partner_status
msgid "Sender Partner Status"
msgstr " حالات الشريك المرسل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_status__sender_partner_status
msgid "Sender Partner status"
msgstr " حالات الشريك المرسل "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__server_db
msgid "Server DB"
msgstr "اسم قاعدة بيانات النظام "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__server_url
msgid "Server URL"
msgstr "رابط النظام "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_select_company_user__session_id
msgid "Session Id"
msgstr "رقم تعريف الجلسة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__show_follower_vhub
msgid "Show follower Vhub"
msgstr "ارسال بيانات متجر التابع"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_tree_rb_delivery_vhub_transaction_item
msgid "Show order"
msgstr "اظهار الطلبية"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:175
#, python-format
msgid "Some orders has country dismatch with this company: "
msgstr "تحتوي بعض الطلبات على عدم تطابق البلد مع هذه الشركة:"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_logs__state
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_status_map__state_id
msgid "Status"
msgstr "الحالة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__state_map_ids
msgid "Status Map"
msgstr "ربط الحالات "

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_status
msgid "Status Model"
msgstr "مودل الحالات"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__success
msgid "Success"
msgstr "إلغاء "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__vhub_completed_statuses
msgid "Sync Partner Status excluding these states"
msgstr "مزامنة حالات طلبيات الشرك باستناء هذه الحالات "

#. module: olivery_vhub
#: model:ir.actions.server,name:olivery_vhub.cron_sync_vhub_orders_ir_actions_server
#: model:ir.cron,cron_name:olivery_vhub.cron_sync_vhub_orders
#: model:ir.cron,name:olivery_vhub.cron_sync_vhub_orders
msgid "Sync VHub orders"
msgstr " Vhub  مزامنة طلبيات  "


#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_refresh_partner_status
msgid "Sync partner status"
msgstr "مزامنة حالات الشريك "

#. module: olivery_vhub
#. openerp-web
#: code:addons/olivery_vhub/static/src/js/button.js:22
#, python-format
msgid "Sync with partner"
msgstr "مزامنة مع الشريك "

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_olivery_vhub_user
msgid "Test Reflect Status"
msgstr "اختبار عكس الحالة "

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_olivery_vhub_user
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "Test VHub"
msgstr " Vhub اختبار "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:37
#, python-format
msgid "The \"Storex\" order in business company will be set to the status in field \"Default Storex Vhub Status\" when the order is sent successfully to \"Delivery company\"."
msgstr "حالة الطلبية في \"ستوركس"\ في شركة المرسل سوف تتحول للحالة في الحقل \"حالة ستوركس VHub الافتراضية\" عندارسال الطلبية بنجاح \"لشركة التوصيل\" "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:34
#, python-format
msgid "The \"Olivery\" order in business company will be set to the status in field \"Default Vhub Status\" when the order is sent successfully to \"Delivery company\"."
msgstr "حالة طلبية \"اوليفري\" في شركة التاجر سوف تتحول للحالة في حقل \"حالة VHub الافتراضية\" عند ارسال الطلبية بنجاح \"لشركة التوصيل\"."

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:60
#: code:addons/olivery_vhub/models/order/order_wizard.py:149
#, python-format
msgid "The user you are trying to connect to in {} is not a company user"
msgstr "المستخدم الذي تحاول الربط معه {} ليس حساب شركة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_unread
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_unread
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_unread
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__message_unread_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__message_unread_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__message_unread_counter
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__user_id
msgid "User"
msgstr "المستخدم"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_follower_field_map__user_field_id
msgid "User field"
msgstr "حقول المستخدم "

#. module: olivery_vhub
#: model:ir.actions.act_window,name:olivery_vhub.action_rb_delivery_vhub_configuration
#: model:ir.ui.menu,name:olivery_vhub.menu_rb_delivery_vhub_configuration
msgid "VHub Configuration"
msgstr " Vhub اعدادات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__vhub_logs
msgid "VHub Logs"
msgstr "Vhub سجلات "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:272
#, python-format
msgid "VHub Message: "
msgstr ": Vhub رسائل "

#. module: olivery_vhub
#: model:ir.actions.act_window,name:olivery_vhub.action_rb_delivery_vhub_field_security
#: model:ir.ui.menu,name:olivery_vhub.menu_rb_delivery_vhub_field_security
msgid "VHub Security Field"
msgstr " Vhub حقول أمان "

#. module: olivery_vhub
#: model:ir.actions.act_window,name:olivery_vhub.action_rb_delivery_vhub_transaction
#: model:ir.ui.menu,name:olivery_vhub.menu_rb_delivery_vhub_transaction
msgid "VHub Transaction"
msgstr "حركات الربط السريع"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__fields_reflected_on_create
msgid "VHub fields"
msgstr "Vhub حقول "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__vhub_message
msgid "VHub message"
msgstr "رسالة الربط السريع"

#. module: olivery_vhub
#: model_terms:ir.ui.view,arch_db:olivery_vhub.view_form_rb_delivery_vhub_configuration
msgid "VHub statuses"
msgstr " Vhub حالات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__vhub_transaction_item_id
msgid "VHub transaction"
msgstr "حركات الربط السريع"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__vhub_company
msgid "Vhub Company"
msgstr " Vhub شركة "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__vhub_messages
msgid "Vhub Messages"
msgstr " Vhub رسائل "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__vhub_sync
msgid "Vhub Sync"
msgstr "Vhub مزامنة طلبيات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__vhub_transaction_id
msgid "Vhub Transaction"
msgstr "حركات الربط السريع"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_user__order_id
msgid "Vhub order"
msgstr " Vhub طلبيات "

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_status_logs
msgid "Vhub status logs"
msgstr " Vhub سجل حالات "

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_status_map
msgid "Vhub status map"
msgstr " Vhub تعيين حالات "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__vhub_transaction_item_ids
msgid "Vhub transaction item"
msgstr "حركات الربط السريع"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:174
#, python-format
msgid "Warning"
msgstr "تحذير "

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__website_message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_field_security__website_message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction__website_message_ids
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_transaction_item__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: olivery_vhub
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_configuration__website_message_ids
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_field_security__website_message_ids
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction__website_message_ids
#: model:ir.model.fields,help:olivery_vhub.field_rb_delivery_vhub_transaction_item__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:49
#, python-format
msgid "When field \"Send Business Info as Follower\" is set to True, the fields in user column (Business user of the order) mapped with Order (Follower fields) in field \"Business follower fields\" will be sent as follower to the \"Delivery company\""
msgstr ""/عند تعيين حقل  "\ارسال معلومات المرسل كتابع"\ الى نشط , الحقول في عمود المستخدم (المرسل في الطلبية ) تم تعيينه للطلبية  (حقول التابع ) في حقول  \"حقول التابع للمرسل \" سيتم تعيينها معلومات التابع في نظام /"شركة التوصيل "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:43
#, python-format
msgid "When field \"Send Sales Info as Business\" is set to True, the mobile number of \"Sender's sales user\" will be sent to \"Delivery company\" as a business, so in this case you'll have to make sure the mobile number of the sales user is added as a business as a child under the main business created for the \"Business company\" in \"Delivery company\""
msgstr " "\ عند تعيين  \"ارسال معلومات المندوب كمرسل \" الى نشط , رقم المحمول  \"مندوب المرسل \"سيتم ارسالها  \"الى شركة التوصيل \" كمرسل , في هذه الحالة يجب عليك التأكد بأن رقم موبايل المندوب  موجود كحساب تاجر في \"نظام شركة التوصيل\" كحساب تاجر فرعي للحساب الرئيسي \"لنظام المرسل"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:46
#, python-format
msgid "When field \"Send Sales Info as Follower\" is set to True, the fields in user column (Sales user of the business) mapped with Order (Follower fields) in field \"Sales follower fields\" will be sent as follower to the \"Delivery company\""
msgstr "عند تعيين الحقل  \"ارسال معلومات المندوب كتابع \" الى نشط , الحقل في عمود المرسل  (المندوب التابع للمرسل (التاجر) ) التي تم تعيينها مع الطلبية  مع الطلبية  (حقول التابع ) في الحقول  \"حقول التابع للمندوب \" سيتم ارسالها ك معلومات التابع  \"عند شركة التوصيل \""

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_model.py:335
#, python-format
msgid "You are not allowed to edit field"
msgstr "غير مسموح لك بالتعديل على الحقول "

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/order/order_wizard.py:145
#, python-format
msgid "errordatamessage"
msgstr ""

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_user
msgid "inherit rb_delivery.user model"
msgstr ""

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_configuration
msgid "rb_delivery.vhub_configuration"
msgstr ""

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_field_security
msgid "rb_delivery.vhub_field_security"
msgstr ""

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_transaction
msgid "rb_delivery.vhub_transaction"
msgstr ""

#. module: olivery_vhub
#: model:ir.model,name:olivery_vhub.model_rb_delivery_vhub_transaction_item
msgid "rb_delivery.vhub_transaction_item"
msgstr ""

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__delivery_agent_number
msgid "Delivery agent mobile number"
msgstr "رقم موبايل سائق شركة التوصيل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__delivery_agent_name
msgid "Delivery agent name"
msgstr "اسم سائق شركة التوصيل"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__reflect_driver_details
msgid "Reflect driver details"
msgstr "عكس تفاصيل السائق"

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_vhub_configuration__reflect_driver_details_desc
msgid "Reflect driver details Description"
msgstr "وصف عكس تفاصيل السائق"

#. module: olivery_vhub
#: code:addons/olivery_vhub/models/vhub_configuration/vhub_configuration_model.py:20
#, python-format
msgid "If this configuration is turned to \"True\" it will cause the \"driver information\" to be reflected from delivery company to business company."
msgstr "اذا كان هذا الاعداد \"True\" سوف يعكس \"تفاصيل السائق\" من شركة التوصيل لشركة التاجر."

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:46
#: code:addons/rb_delivery/models/error_log/error_log_model.py:46
#, python-format
msgid "Error while authorizing user"
msgstr "خطأ اثناء التحقق من المستخدم"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "The user with mobile number {mobile_number} has a wrong password on system {company_name}."
msgstr "المستخدم صاحب رقم الموبايل {mobile_number} الرقم السري لديه خاطئ في نظام {company_name}."

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Please contact the {company_name} admin to reset your password or provide you with a new one."
msgstr "الرجاء التواصل مع الدعم عند نظام {company_name} لتغيير الرقم السري."

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "Your user with mobile number {mobile_number} is not confirmed on system {company_name}."
msgstr "المستخدم صاحب رقم الموبايل {mobile_number} ليس موافق عليه من النظام {company_name}."

#. module: olivery_vhub
#: model:ir.model.fields,field_description:olivery_vhub.field_rb_delivery_order__partner_vhub_transaction_item_id
msgid "Partner Vhub transaction"
msgstr "معاملة الشريك V-Hub"