# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api
from openerp.exceptions import ValidationError
from onesignal_sdk.client import Client
import re

_logger = logging.getLogger(__name__)


class olivery_auto_distribute_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
            
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    def _get_drivers_from_zones(self, zones, current_drivers, users, players):
        for zone in zones:
            for driver in zone.driver_ids:
                if driver not in users:
                    current_drivers.add(driver.id)
                    users.add(driver)
                    if driver.player_id:
                        players.append(driver.player_id)

    def _get_drivers_from_areas(self, areas, type_key, current_drivers, users, players):
        for area in areas:
            if getattr(area, type_key, False):
                for driver in getattr(area, type_key):
                    if driver not in users:
                        current_drivers.add(driver.id)
                        users.add(driver)
                        if driver.player_id:
                            players.append(driver.player_id)

    def get_current_drivers(self, next_state):
        action = self.env['rb_delivery.action'].search([
            ('order_state', '=', next_state),
            '|', ('action_type', '=', 'distribution_area_action'),
            ('action_type', '=', 'dispatch_area_action')
        ])

        current_drivers = set()
        users = set()
        players = []

        if action and action.action_type == 'distribution_area_action' and self.sudo().assign_to_business:
            
            area_types = ['area_id', 'sub_area']
            self.check_areas_dispatching(area_types,'drivers','zone_id',current_drivers,users,players,'distribution_area_action')

        elif action and action.action_type == 'dispatch_area_action' and self.customer_area:
            
            area_types = ['customer_area', 'customer_sub_area']
            self.check_areas_dispatching(area_types,'drop_off_driver_ids','zone_id',current_drivers,users,players,'dispatch_area_action')
            
        if current_drivers:
            self.current_drivers = [(6, 0, list(current_drivers))]

        if players:
            conf = self.env['rb_delivery.one_signal'].sudo().search([], limit=1)
            header, message = action.header, action.message
            self.with_delay(channel="root.notification", max_retries=2).notify_current_drivers(conf, message, header, players, list(users))

        return True
    
    def check_areas_dispatching(self,area_types,driver_field,zone_key,current_drivers,users,players,action_type):
        if action_type == 'dispatch_area_action':
            record = self
            zone_type = 'distribute'
        else:
            record = self.sudo().assign_to_business
            zone_type = 'pickup'
        for area_type in area_types:
            area = getattr(record, area_type, None)
            if area:
                self._get_drivers_from_areas([area], driver_field, current_drivers, users, players)
                if getattr(area, zone_key, None):
                    self._get_drivers_from_zones(getattr(area, zone_key).filtered(lambda x:x.zone_type==zone_type), current_drivers, users, players)

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    def notify_current_drivers(self,conf,message,header,players,users):
        variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
        client = Client(app_id=str(conf.app_id), rest_api_key=str(conf.app_auth_key), user_auth_key=str(conf.user_auth_key))
        index = 0
        if not header:
            header = ''
        for player in players:
            message = self.env['rb_delivery.notification_center'].get_message(message,variables,users[index],self.id,'rb_delivery.order')

            notification_body = {
                'headings': {'en': header},
                'contents': {'en': message},

                'include_player_ids': [player],
                'filters': [],
                'data': {'order_id':self.id,'sequence':self.sequence,'model':'rb_delivery.order'}
                }
            try:
                client.send_notification(notification_body)
                self.create_notification_record(users[index].id,message,header,self.id,'rb_delivery.order',self.sequence,'text')

            except Exception as e:
                _logger.info("!!!!!!!!! In create and send notification: "+str(e) )
                pass
            index += 1

    def guard_function(self,values):
        for rec in self:
            if 'state' in values and values['state']:
                rec.get_current_drivers(values['state'])
        return super(olivery_auto_distribute_order, self).guard_function(values)

    @api.model
    def create(self,values):
        order = super(olivery_auto_distribute_order, self).create(values)
        order.get_current_drivers(order.state)
        return order

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
