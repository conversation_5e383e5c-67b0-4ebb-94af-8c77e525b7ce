# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api

_logger = logging.getLogger(__name__)


class olivery_auto_distribute_area(models.Model):

    _inherit = 'rb_delivery.area'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    drop_off_driver_ids = fields.Many2many(
        comodel_name = 'rb_delivery.user',
        string = 'Drop-off Drivers',
        relation = 'area_drop_off_driver_item',
        column1 = 'area_id',
        column2 = 'user_id',domain=_get_driver_users)
    

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
