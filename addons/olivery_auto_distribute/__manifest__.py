# -*- coding: utf-8 -*-
{
    'name': "olivery_auto_distribute",
    'summary': """
        Olivery Auto Distribute App from olivery.app""",

    'description': """
        App used for drivers distribution depending on area based on status actions for drop off and/or dispatch
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.13',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail','website','rb_delivery'],

    # always loaded
    'data': [
        'models/area/area_view.xml',
        'models/sub_area/sub_area_view.xml',
        'models/action/action_view.xml',
        # 'data/order_sequence.xml',
        # 'models/barcode/barcode_view.xml',
        #
        # 'models/area/area_view.xml',
        # 'models/generator_barcode/generator_barcode_view.xml',
        # 'views/barcode_print.xml',
        # 'demo/client_conf.xml',
        # 'models/general_configuration/general_configuration_view.xml',
    ],
    'qweb': [
        'static/src/xml/*.xml',
    ],
}
