# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class olivery_cargo_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    returned_reason = fields.Many2one('olivery_cargo.returned_reason', 'Returned Reason',track_visibility="on_change")

    cloner_order_id = fields.Many2one('rb_delivery.order',string='Cloner order', track_visibility="on_change", readonly=True)

    cloned_order_ids = fields.One2many('rb_delivery.order',string='Cloned orders', inverse_name="cloner_order_id", track_visibility="on_change", readonly=True)

    is_cloned = fields.<PERSON><PERSON><PERSON>("Is cloned",readonly=True)

    is_cloner = fields.<PERSON>olean("Is cloner",readonly=True)

    delivery_trials = fields.Integer("Delivery Trials",readonly=True)

    returned_discount_type = fields.Selection([('default', 'Default'),('fixed', 'Fixed Discount'),('pricelist', 'Based on pricelist')], readonly=1)

    business_state = fields.Char('Business Status', track_visibility="on_change",compute='_get_business_status',copy=False,store=True)

    @api.depends('state','assign_to_business','returned_reason')
    def _get_business_status(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for rec in self:
            status_related_to_reason = False
            business_state = False
            if rec.returned_reason and rec.sudo().assign_to_business:
                status_related_to_reason = self.env['rb_delivery.status'].sudo().search([('returned_reason','=',rec.returned_reason.id)],limit=1)
                if status_related_to_reason:
                    business_state = status_related_to_reason.business_status_name

            if not business_state and rec.sudo().assign_to_business and rec.sudo().assign_to_business.sudo().user_id:
                business_state = rec.sudo().with_context(uid=rec.sudo().assign_to_business.sudo().user_id.id).state_id.display_name
            rec.business_state = business_state



    def get_returned_discount(self,values):
        status = self.env['rb_delivery.status'].sudo().search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')])
        if status and status.apply_discount:
            reject_reason_ids = status.reject_reason_ids.ids
            for rec in self:
                returned_discount_type_user = rec.sudo().assign_to_business.discount_type
                if returned_discount_type_user:
                    rec.get_discount_type(returned_discount_type_user,values,rec.sudo().assign_to_business.fixed_discount,reject_reason_ids,rec.sudo().assign_to_business.pricelist_discount_type)
                else:
                    rec.get_discount_type(status.discount_type,values,status.fixed_discount,reject_reason_ids,status.pricelist_discount_type)

    def get_discount_type(self,discount_type,values,value,reject_reason_ids,pricelist_discount_type):
        updated_values = {}
        if len(reject_reason_ids) == 0 or (self.reject_reason.id in reject_reason_ids) or ('reject_reason' in values and values['reject_reason'] and values['reject_reason'] in reject_reason_ids):
            if discount_type == 'default' and not self.returned_value:
                updated_values['returned_discount'] = '100%'
                updated_values['returned_value'] = 0
                if (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                    updated_values['required_from_business'] = 0
                    updated_values['customer_payment'] = '0'

            elif discount_type == 'fixed' and not self.returned_value:
                discount_value = float(value)
                updated_values['returned_discount'] = discount_value
                updated_values['returned_value'] = self.delivery_cost - discount_value
                if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                    updated_values['required_from_business'] =  discount_value - self.delivery_cost
                    updated_values['customer_payment'] = '0'

            elif discount_type == 'pricelist' and not self.returned_value:
                if self.customer_sub_area:
                    data = {
                        'sender_id': self.sudo().assign_to_business.id,
                        'to_area_id': self.customer_area.id,
                        'order_type_id': self.order_type_id.id,
                        'sub_area_id':self.customer_sub_area.id
                        }
                else:
                    data = {
                        'sender_id': self.sudo().assign_to_business.id,
                        'to_area_id': self.customer_area.id,
                        'order_type_id': self.order_type_id.id,
                        'sub_area_id':False
                        }

                if self.show_alt_address:
                    discount_value , fixed_value = self.env['rb_delivery.pricelist'].get_discount_value(data,self.business_alt_area)
                else:
                    discount_value , fixed_value  = self.env['rb_delivery.pricelist'].get_discount_value(data)

                if pricelist_discount_type == 'percentage':
                    updated_values['returned_discount'] = round((self.delivery_cost * float(discount_value))/100,2)
                    updated_values['returned_value'] = self.delivery_cost - round((self.delivery_cost * float(discount_value))/100,2)
                    if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                        updated_values['required_from_business'] =  round((self.delivery_cost * float(discount_value))/100,2)-	self.delivery_cost
                        updated_values['customer_payment'] = '0'
                elif pricelist_discount_type == 'fixed_value':
                    updated_values['returned_discount'] = self.delivery_cost - fixed_value
                    updated_values['returned_value'] = self.delivery_cost - updated_values['returned_discount']
                    if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                        updated_values['required_from_business'] =  updated_values['returned_discount'] -	self.delivery_cost
                        updated_values['customer_payment'] = '0'
                else:
                    updated_values['returned_discount'] = float(discount_value)
                    updated_values['returned_value'] = self.delivery_cost - float(discount_value)
                    if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                        updated_values['required_from_business'] =  float(discount_value) - self.delivery_cost
                        updated_values['customer_payment'] = '0'
            updated_values['returned_discount_type'] = discount_type
        if updated_values:
            self.write(updated_values)
    @api.multi
    def write(self,values):
        if 'state' in values and values['state']:
            self.check_if_clone(values)
        res = super(olivery_cargo_order, self).write(values)
        if 'state' in values and values['state']:
            self.get_returned_discount(values)
        return res
    def guard_function(self,values):
        for rec in self:
            if 'state' in values and values['state']:
                if rec.is_cloner:
                    rec.check_update_cloned_order_status(values['state'])
                rec.get_returned_reasons(values)
                rec.cargo_do_action(values['state'])

            if rec.is_cloner:
                rec.update_clone_orders(values)
            rec.cargo_pre_action(values)

        if 'is_cloner' in values and values['is_cloner'] and 'state' in values and values['state']:
            if 'cloner_state' in values and values['cloner_state']:
                values['state'] = values['cloner_state']
                del values['cloner_state']
            else:
                del values['state']
        return super(olivery_cargo_order, self).guard_function(values)

    # inherit module[olivery_branch_collection]
    def get_clone_values(self,values):
        clone_values = {}
        if 'assign_to_agent' in values and values['assign_to_agent']:
            clone_values['assign_to_agent'] = values['assign_to_agent']
        if 'stuck_comment' in values and values['stuck_comment']:
            clone_values['stuck_comment'] = values['stuck_comment']
        if 'reject_reason' in values and values['reject_reason']:
            clone_values['reject_reason'] = values['reject_reason']
        if 'returned_reason' in values and values['returned_reason']:
            clone_values['returned_reason'] = values['returned_reason']
        return clone_values

    def update_clone_orders(self,values):
        clone_values = self.get_clone_values(values)
        if self.is_cloner and clone_values:
            message = _('This action will be refelcted on all cloned orders')
            self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
            statuses_dont_allowed_to_edit =self.env['rb_delivery.client_configuration'].get_param('prevent_update_agent_on_cloned_order')
            edit_cloned_orders = True
            state=self.state
            if 'state' in values and values['state'] :
                state=values['state']
            if statuses_dont_allowed_to_edit:
                status_id=self.env['rb_delivery.status'].search([('name','=',state),'|',('status_type','=',False),('status_type','=','olivery_order')]).id
                edit_cloned_orders=status_id not in statuses_dont_allowed_to_edit
            if edit_cloned_orders==True:
                self.cloned_order_ids.write(clone_values)


    def check_update_cloned_order_status(self,order_state):
        statuses_to_update_ids = self.env['rb_delivery.client_configuration'].get_param('reflect_clone_order_status')
        if statuses_to_update_ids:
            for status_to_update_id in statuses_to_update_ids:
                status = self.env['rb_delivery.status'].sudo().browse(status_to_update_id)
                if order_state == status.name:
                    message = _('This action will be refelcted on all cloned orders')
                    self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
                    self.cloned_order_ids.sudo().write({'state':order_state})
                    for clone_order in self.cloned_order_ids:
                        username = self.env.user.name
                        clone_order.sudo().message_post(body=_("Clone order has been updated by %s automatically through the system based on the configuration of allowed statuses to update clone orders, for further details you can check with support.")%(username))
    @api.depends('customer_area', 'assign_to_business', 'extra_cost', 'order_type_id', 'discount','customer_sub_area', 'service.cost','business_alt_area','show_alt_address','returned_discount')
    @api.multi
    def get_customer_price(self):
        super(olivery_cargo_order, self).get_customer_price()
        for rec in self:
            try:
                if rec.returned_discount:
                    if rec.returned_discount_type and rec.returned_discount_type == 'default':
                        rec.delivery_cost = 0
                    else:
                        rec.delivery_cost = rec.delivery_cost - float(rec.returned_discount)
            except:
                pass

    @api.model
    def create(self,values):
        if 'state' in values and values['state']:
            self.get_returned_reasons(values)
        if values.get('is_cloned'):
            values['discount'] = 0
        order = super(olivery_cargo_order, self).create(values)
        if values.get('is_cloned'):
            order.write({'discount':order.delivery_cost})
        return order

    # Post actions methods
    def clear_returned_reason(self,next_state):
        self.write({'returned_reason':''})

    def replacement_order_if_order_cloned(self,next_state):
        self.write({'replacement_order':True})

    def check_if_clone(self,values):
        not_replacement_orders = self.filtered(lambda x: x.replacement_order == False)
        status_clone=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')])
        clone = status_clone.clone
        if clone and not_replacement_orders:
            cloned_status = status_clone.cloned_status
            cloner_status = status_clone.cloner_status
            values = self.clone_order(not_replacement_orders,cloned_status,cloner_status,values)
        return values

    # inherit module[olivery_branch_collection]
    def get_clone_order_values(self,order,cloned_status,cloner_status,values):
        prefix_clone_sequence = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix')
        postfix_clone_sequence = self.env['rb_delivery.client_configuration'].get_param('clone_order_postfix')
        reference_id_prefix = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix_reference_id')
        reference_id_suffix = self.env['rb_delivery.client_configuration'].get_param('clone_order_suffix_reference_id')
        scanned_reference_id = False
        if 'scanned_reference_id' in values and values['scanned_reference_id']:
            scanned_reference_id = values['scanned_reference_id']

        new_sequence = order.sequence
        if prefix_clone_sequence:
            new_sequence = prefix_clone_sequence + new_sequence
        if postfix_clone_sequence:
            new_sequence = new_sequence + postfix_clone_sequence
        delivery_cost = order.delivery_cost
        if order.discount != 0:
            delivery_cost = delivery_cost + order.discount
        if order.extra_cost != 0:
            delivery_cost = delivery_cost - order.extra_cost
        clone_values = {
                'assign_to_business': order.sudo().assign_to_business.id,
                'cost':0.00,
                'copy_total_cost':0.00,
                'customer_name':order.customer_name,
                'customer_address':order.customer_address,
                'customer_mobile':order.customer_mobile,
                'customer_area':order.customer_area.name,
                'second_mobile_number':order.second_mobile_number,
                'cloner_order_id':order.id,
                'state':cloned_status.name,
                'discount':delivery_cost,
                'stuck_comment':order.stuck_comment,
                'note': order.note,
                'reference_id': values['reversed_waybill'] if 'reversed_waybill' in values else '',
                'is_cloned':True}

        new_ref_id = order.reference_id
        if reference_id_prefix and self.reference_id:
            new_ref_id = str(reference_id_prefix) + str(new_ref_id)
        if reference_id_suffix and self.reference_id:
            new_ref_id =  str(new_ref_id) + str(reference_id_suffix)
        if new_sequence != order.sequence:
            clone_values['sequence'] = new_sequence
        if new_ref_id != order.reference_id:
            clone_values['reference_id'] = new_ref_id
        if self.returned_reason:
            clone_values['returned_reason'] = self.returned_reason.id
        if self.reject_reason:
            clone_values['reject_reason'] = self.reject_reason.id
        if 'reject_reason' in values and values['reject_reason']:
            clone_values['reject_reason'] = values['reject_reason']
        if values.get('stuck_comment'):
            clone_values['stuck_comment'] = values.get('stuck_comment')
        if order.customer_sub_area:
            clone_values['customer_sub_area'] = order.customer_sub_area.id
        if order.assign_to_agent:
            clone_values['assign_to_agent'] = order.assign_to_agent.id

        if scanned_reference_id:
            clone_values['reference_id'] = scanned_reference_id
        return clone_values

    @api.model
    def clone_order(self,orders,cloned_status,cloner_status,values):
        for rec in orders:
            clone_order_values = rec.get_clone_order_values(rec,cloned_status,cloner_status,values)
            uid = self.env.user.partner_id.id
            clone_order = self.env['rb_delivery.order'].with_context(original_uid=uid).sudo().create(clone_order_values)
            username = self.env.user.name
            clone_order.message_post(body=_("Clone order has been created by %s automatically through the system based on the configuration of the cloning, for further details you can check with support.")%(username))
        values['is_cloner'] =  True
        if cloner_status:
            values['cloner_state'] = cloner_status.name

        return values


    def cargo_do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_cargo_order,action.name)
                method_to_call(self,next_state)
            except Exception as e:
                if action.name == "reset_returned_cancelled_discount":
                    raise ValidationError(e)
                else:
                    pass

    def cargo_pre_action(self,values):
        status_pre_actions=self.env['rb_delivery.status'].search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_pre_action_ids
        for action in status_pre_actions:
            try:
                method_to_call=getattr(olivery_cargo_order,action.name)
                method_to_call(self,values)
            except Exception as e:
                if action.name == "reset_returned_cancelled_discount":
                    raise ValidationError(e)
                else:
                    pass

    def reset_returned_cancelled_discount(self,next_state):
        vals = {'returned_discount_type':False,'returned_discount':False,'returned_value':False,'customer_payment':False,'returned_reason':'','cloned_order_ids':[(6,0,[])],'delivery_trials':self.delivery_trials + 1}
        if (self.returned_discount and self.returned_value):
            self.write(vals)

    def reset_customer_payment(self,next_state):
        if (not self.returned_discount or not self.returned_value):
            self.write({'customer_payment':False})

    def get_returned_reasons(self,values):
        returned_reason =self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).returned_reason
        if returned_reason:
            values['returned_reason'] = returned_reason.id

    def get_clone_order(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        if self.is_cloner:
            domain = [('cloner_order_id', '=', self.id),('state', '!=','deleted')]
        else:
            domain = [('id', '=', self.cloner_order_id.id),('state', '!=','deleted')]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

