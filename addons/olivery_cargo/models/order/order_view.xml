<?xml version="1.0" encoding="UTF-8"?>

<odoo>
  <data>
      <!--inherit form view-->
      <record id="view_form_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_form_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order" />
         <field name="arch" type="xml">
         <xpath expr="//sheet//button" position="after">
               <field name="is_cloner" invisible="1"/>
               <field name="is_cloned" invisible="1"/>
               <button type="object" name="get_clone_order" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('is_cloner', '=',False),('is_cloned','=',False)]}" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system">
                  <div class="fa fa-fw  fa-clone  o_button_icon"/>
                     <div class="o_form_field o_stat_info" data-original-title="" title="">
                     <span>Clone order</span>
                  </div>
               </button>
            </xpath>
         </field>
      </record>

      <!--inherit search view-->
      <record id="view_search_olivery_cargo_order" model="ir.ui.view">
         <field name="name">view_search_olivery_cargo_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_search_rb_delivery_order" />
         <field name="arch" type="xml">
         <search>
            <filter name="returned_orders" string="Returned Orders" domain="[('returned_reason','!=',False)]"/>
            <filter name="cloned_orders" string="Cloned Orders" domain="['|',('is_cloned','=',True),('is_cloner','=',True)]"/>
            <filter name="exclude_cloned_orders" string="Exclude Cloned Orders" domain="[('is_cloned','=',False)]"/>
            <field name="returned_reason"/>
            <field name="cloner_order_id" />
            <field name="cloned_order_ids" />
            <filter name="group_by_returned_reason" string="By Returned Reason" icon="terp-partner" context="{'group_by':'returned_reason'}"/>
            <filter name="group_by_cloner_order_id" string="By Cloner Order" icon="terp-partner" context="{'group_by':'cloner_order_id'}"/>
         </search>
         </field>
      </record>

      <record id="view_tree_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_tree_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_tree_rb_delivery_order" />
         <field name="arch" type="xml">
         <xpath expr="//field[@name='state']" position="after">
            <field name="business_state" groups="rb_delivery.role_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>
        </xpath>
    </field>
</record>





   </data>
</odoo>
