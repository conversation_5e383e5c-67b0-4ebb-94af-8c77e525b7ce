# -*- coding: utf-8 -*-
{
    'name': "olivery_cargo",
    'summary': """
        Olivery Cargo App from olivery.app""",

    'description': """
	Cargo module for last mile delivery specilized in delivering cargos packages
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.2.52',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/returned_reason/returned_reason_view.xml',
        'demo/returned_reason.xml',
        'views/module_view.xml',
        'models/order/order_view.xml',
        'models/status/status_view.xml',
        'models/pricelist/pricelist_view.xml',
        'models/pricelist_item/pricelist_item_view.xml',
        'models/client_configuration/client_configuration_view.xml',
        'demo/status.xml',
        'demo/client_conf.xml',
        'models/user/user_view.xml',
        'views/print/collection_money_collection_report.xml',
        'models/order/select_state_view.xml'
    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
    'auto_install': True
}
