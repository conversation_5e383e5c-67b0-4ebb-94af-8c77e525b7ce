# -*- coding: utf-8 -*-
{
    'name': "olivery_dynamic_integration",
    'summary': """
        Olivery Dynamic Integration App from olivery.app""",

    'description': """
        Long description of module's purpose
        Olivery Dynamic Integration
    """,

    'author': "<PERSON>y",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.1.6',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail','website','rb_delivery'],

    # always loaded
    'data': [
        "security/ir.model.access.csv",
        "views/module_view.xml",
        "models/integration/integration_view.xml",
        "models/integration_logs/integration_logs_view.xml",
        "models/order/order_wizard_view.xml",
        "models/status_map/status_map_view.xml",
        "models/field_map/field_map_view.xml",
        "models/area/area_view.xml",
        "models/sub_area/sub_area_view.xml",
        "models/country/country_view.xml",
        "demo/status.xml",
        "demo/archive_logs.xml",
        "demo/area.xml"
    ],
    'qweb': [
        'static/src/xml/*.xml',
    ],
}
