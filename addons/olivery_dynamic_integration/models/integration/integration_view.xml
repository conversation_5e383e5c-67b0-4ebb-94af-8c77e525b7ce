
<odoo>
  <data>
    <record id="view_form_rb_delivery_integration" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_integration</field>
      <field name="model">rb_delivery.integration</field>

      <field name="arch" type="xml">
        <form>

          <sheet>
          <div class="oe_title">
                <label for="name" string="Name" class="oe_edit_only"/>
                    <h1>
                        <field name="name"/>
                    </h1>
            </div>
           <group name="group_top">
            <group name="group_right">
                <field name="content_type"/>
                <field name="active"/>
                <field name="field_map_ids" attrs="{'invisible':[('content_type','=','application/json')]}" widget="one2many_list"/>
                <field name="integration_body" attrs="{'invisible':[('content_type','!=','application/json')]}"/>
                <field name="integration_url"/>
                <field name="integration_header"/>
                <field name="integration_type"/>
                <field name="integration_method"/>
                <field name="integration_success_code"/>
                <field name="additional_integration_success_code"/>
                <field name="get_returned_id"/>
                <field name="reference_id_field"/>
              </group>
              <group name="group_left">
                <field name="needs_authenticate"/>
                <field name="auth_content_type" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="auth_field_map_ids" attrs="{'invisible':[('needs_authenticate','=',False),('auth_content_type','!=','application/x-www-form-urlencoded')]}" widget="one2many_list"/>
                <field name="authenticate_body" attrs="{'invisible':['|',('needs_authenticate','=',False),('auth_content_type','=','application/x-www-form-urlencoded')]}"/>
                <field name="authenticate_url" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="authenticate_header" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="authenticate_method" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="authenticate_success_code" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="auth_token_prefix" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
                <field name="get_auth_token" attrs="{'invisible':[('needs_authenticate','=',False)]}"/>
              </group>
           </group>

           <group name="group_bottom">
            <group name="group_right">
                <field name="assign_to_business" attrs="{'invisible':[('integration_type','=','create_order')]}"/>
                <field name="assign_to_agent" attrs="{'invisible':[('integration_type','=','update_status')]}"/>
              </group>
              <group name="group_left">
                <field name="date_format"/>
                <field name="status_map_ids" attrs="{'invisible':[('integration_type','=','create_order')]}" widget="one2many_list"/>

              </group>
           </group>

          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_integration" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_integration</field>
      <field name="model">rb_delivery.integration</field>

      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="integration_body"/>
          <field name="integration_url"/>
          <field name="integration_header"/>
          <field name="integration_type"/>
        </tree>
      </field>

    </record>

  </data>
</odoo>
