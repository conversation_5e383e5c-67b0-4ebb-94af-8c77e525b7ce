# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class rb_delivery_integration(models.Model):

    _name = 'rb_delivery.integration'

    _inherit = 'mail.thread'
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char("Company Name",track_visibility="on_change")

    assign_to_business = fields.Many2one('rb_delivery.user', 'Business', track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_business')]")

    assign_to_agent = fields.Many2one('rb_delivery.user', 'Agent', track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_driver')]")

    integration_body = fields.Text('Integration Body',track_visibility="on_change", default="{}")

    integration_url = fields.Char('Integration URL',track_visibility="on_change")

    integration_header = fields.Text('Integration Header',track_visibility="on_change", default="{}")

    integration_method = fields.Char("Integration method",track_visibility="on_change")

    integration_success_code = fields.Integer("Integration success code",track_visibility="on_change")

    additional_integration_success_code = fields.Char("Additional Integration success code",track_visibility="on_change")

    get_returned_id = fields.Text('Get returned ID',track_visibility="on_change")

    integration_type = fields.Selection([('update_status','Update Status'),('create_order','Create order')],'Integration Type',track_visibility="on_change")

    needs_authenticate = fields.Boolean('Needs Authenticate',track_visibility="on_change")

    authenticate_body = fields.Text('Authenticate Body',track_visibility="on_change", default="{}")

    authenticate_url = fields.Char('Authenticate URL',track_visibility="on_change")

    authenticate_header = fields.Text('Authenticate Header',track_visibility="on_change", default="{}")

    authenticate_method = fields.Char("Authenticate method",track_visibility="on_change")

    authenticate_success_code = fields.Integer("Authenticate success code",track_visibility="on_change")

    auth_token_prefix = fields.Char("Auth token prefix",track_visibility="on_change")

    get_auth_token = fields.Text('Authenticate Token',track_visibility="on_change")

    active = fields.Boolean("Active",default=True,track_visibility="on_change")

    status_map_ids = fields.Many2many(
        comodel_name = 'rb_delivery.status_map',
        string = 'Status Map',
        relation = 'status_map_integration_replation',
        column1 = 'integration_id',
        column2 = 'status_map_id')

    reference_id_field = fields.Many2one("ir.model.fields","Reference ID field",track_visibility="on_change",domain=[('model','=','rb_delivery.order')])

    date_format = fields.Char("Date format",track_visibility="on_change")

    content_type = fields.Selection(selection=[('application/x-www-form-urlencoded','URl encoded'),('application/json','JSON'),('multipart/form-data','Form data')],string='Content Type',default='application/json')

    auth_content_type = fields.Selection(selection=[('application/x-www-form-urlencoded','URl encoded'),('application/json','JSON')],string='Authentication Content Type',default='application/json')

    field_map_ids = fields.Many2many(
        comodel_name = 'rb_delivery.field_map',
        string = 'Field Map',
        relation = 'field_map_integration_replation',
        column1 = 'integration_id',
        column2 = 'field_map_id'
    )

    auth_field_map_ids = fields.Many2many(
        comodel_name = 'rb_delivery.field_map',
        string = 'Authentication Field Map',
        relation = 'auth_field_map_integration_replation',
        column1 = 'integration_id',
        column2 = 'auth_field_map_id'
    )