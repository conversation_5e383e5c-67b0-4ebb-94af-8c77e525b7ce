# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import requests
import json
import os
import re

_logger = logging.getLogger(__name__)


class order_send_orders_in_integration_wizard(models.TransientModel):
    _name = 'rb_delivery.send_orders_in_integration'
    _description = 'Send orders in integration'

    company_id = fields.Many2one('rb_delivery.integration','Company',domain="[('integration_type','=','create_order')]")

    def get_orders(self):
        recs = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        if len(recs)>0:
            batch_list = [recs[i:i + 20] for i in range(0, len(recs), 20)]
            for batch in batch_list:
                self.with_delay(channel="root.integration",max_retries=2).get_integration_info(batch)
        return True

    def get_integration_info(self,orders):
        messages = []
        integration_info = self.company_id
        if integration_info and integration_info.integration_type == 'create_order':
            for order in orders:
                if integration_info.needs_authenticate:
                    auth_token, message = self.env['rb_delivery.order'].authenticate_integration(integration_info,order)
                    if auth_token:
                        messages = self.create_order_webhook(integration_info,order,messages,auth_token)
                    else:
                        messages.append(message)
                else:
                    messages = self.create_order_webhook(integration_info,order,messages)

        if len(messages)>0:
            self.env['rb_delivery.utility'].send_toast('for_user', messages , str(self._uid))

    def create_order_webhook(self,integration_info,order,messages,auth_token=False):
        success =False
        integration_logs_vals = {"user_id":self._uid,"order_id":order.id,"integration_type":"Create Order","name":integration_info.name}
        integration_header = json.loads(integration_info.integration_header)
        if auth_token:
            integration_header['Authorization'] = auth_token
        integration_url = integration_info.integration_url

        integration_method = integration_info.integration_method
        if not integration_method:
            integration_method = "POST"

        if integration_info.content_type and integration_info.content_type == 'application/x-www-form-urlencoded':
            integration_body = self.env['rb_delivery.order'].get_urlencoded_body(integration_info,order)
        elif integration_info.content_type and integration_info.content_type == 'multipart/form-data':
            integration_body = self.env['rb_delivery.order'].get_multipart_form_data_body(integration_info,integration_info.field_map_ids,order)
        else:
            integration_body = self.env['rb_delivery.order'].get_integration_body(integration_info,order)

        integration_logs_vals['integration_body'] = integration_body
        if integration_body and integration_info.content_type and integration_info.content_type != 'multipart/form-data':
            integration_body = integration_body.replace('\n','')
            sent_integration = integration_body.encode('utf-8')
        else:
            sent_integration = integration_body
        integration_response = requests.request(integration_method.upper(),integration_url, data=sent_integration, headers=integration_header)
        integration_status_code = integration_info.integration_success_code
        if not integration_status_code:
            integration_status_code = 200
        if integration_response.status_code == integration_status_code:
            integration_json_res = integration_response.json()
            if integration_info.additional_integration_success_code:
                integration_status_code_res = integration_json_res
                additional_integration_status_success_code = ''
                additional_integration_success_code_list = integration_info.additional_integration_success_code.split('=')
                if len(additional_integration_success_code_list)>0:
                    additional_integration_success_code_vals = additional_integration_success_code_list[0]
                    additional_integration_status_success_code = additional_integration_success_code_list[1]
                    additional_integration_success_code_vals_list = additional_integration_success_code_vals.split('/')
                    for additional_integration_success_code_vals_item in additional_integration_success_code_vals_list:
                        if additional_integration_success_code_vals_item in integration_status_code_res and integration_status_code_res[additional_integration_success_code_vals_item]:
                            integration_status_code_res = integration_status_code_res[additional_integration_success_code_vals_item]
                    if additional_integration_status_success_code and int(additional_integration_status_success_code) == integration_status_code_res:
                        success = True
                    else:
                        success = False
            else:
                success = True
        else:
            success = False


        if success:
            integration_logs_vals['success'] = True
            integration_logs_vals['returned_message'] = bytes(integration_response.text, 'utf-8').decode('unicode_escape')
            ref_id = ''
            if integration_info.get_returned_id:
                integration_response_list = integration_info.get_returned_id.split('/')
                for integration_response_item in integration_response_list:
                    if integration_response_item.isdigit():
                        if integration_json_res[int(integration_response_item)]:
                            integration_json_res = integration_json_res[int(integration_response_item)]
                    else:
                        if integration_response_item in integration_json_res and integration_json_res[integration_response_item]:
                            integration_json_res = integration_json_res[integration_response_item]
                ref_id = integration_json_res

                order_vals = {}
                if integration_info.assign_to_agent:
                    order_vals['assign_to_agent'] = integration_info.assign_to_agent.id
                if ref_id:
                    if integration_info.reference_id_field:
                        order_vals[integration_info.reference_id_field.name] = str(ref_id)
                    else:
                        order_vals['reference_id'] = str(ref_id)
                if order_vals:
                    try:
                        order.write(order_vals)
                        message = _("Order was successfully sent to %s")%(integration_info.name)
                        messages.append(message)
                        order.message_post(body=message)
                    except Exception as e:
                        message = _("Order was successfully sent to %s but was not able to update Olivery order because of %s")%(integration_info.name,str(e))
                        messages.append(message)
                        order.message_post(body=message)

        else:
            message = _("order failed to be sent to "+integration_info.name+" because of "+integration_response.text)
            integration_logs_vals['success'] = False
            integration_logs_vals['returned_message'] = bytes(message, 'utf-8').decode('unicode_escape')
            messages.append(message)
            order.message_post(body=message)
        integration_logs = self.env['rb_delivery.integration_logs'].sudo().create(integration_logs_vals)
        return messages