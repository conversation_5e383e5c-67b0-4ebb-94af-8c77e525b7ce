# -*- coding: utf-8 -*-
import json
from openerp import models,fields, api,_
import requests
import json
import re
from datetime import datetime


class olivery_dynamic_integration_order(models.Model):
    _inherit = 'rb_delivery.order'

    integration_status = fields.Char("Integration status",readonly=True)

    @api.multi
    def write(self, values):
        if values.get('integration_status'):
            self.check_status(values)
        if 'state' in values and values['state']:
            self.olivery_dynamic_integration_do_action(self,values['state'])
        return super(olivery_dynamic_integration_order, self).write(values)

    def authorize_edit(self,values,status=False):
        self.SUDO_FIELDS = self.SUDO_FIELDS + ['integration_status']
        return super(olivery_dynamic_integration_order, self).authorize_edit(values,status)

    def check_area(self, values,area_value,rb_delivery_user):
        try:
            res  = super(olivery_dynamic_integration_order, self).check_area(values,area_value,rb_delivery_user)
            return res
        except Exception as e:
            if not self._context.get('import_file') and not self._context.get('from_api'):
                area = self.env.ref('olivery_dynamic_integration.default_integration_area')
                if area and area.id :
                    return area.id
                else:
                    raise Warning(_(str(e)))
            else:
                raise Warning(_(str(e)))

    def check_status(self,values):
        order_vals = {}
        update_order_ids = []
        for rec in self:
            integration_info = self.env['rb_delivery.integration'].sudo().search(['|',('assign_to_business','=',rec.assign_to_business.id),('assign_to_agent','=',rec.assign_to_agent.id)],limit=1)
            if integration_info.status_map_ids:
                status_map_id = integration_info.status_map_ids.filtered(lambda x:x.name == values['integration_status'])
                if status_map_id and status_map_id.state != rec.state:
                    order_vals['state'] = status_map_id.state
                    update_order_ids.append(rec.id)
        if order_vals and len(update_order_ids)>0:
            orders = self.env['rb_delivery.order'].browse(update_order_ids)
            orders.write(order_vals)

    @api.model
    def create(self, values):
        order = super(olivery_dynamic_integration_order, self).create(values)
        if order.state:
            self.olivery_dynamic_integration_do_action_create(order,order.state)
        return order

    def olivery_dynamic_integration_do_action_create(self,order,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_dynamic_integration_order,action.name)
                method_to_call(self,order,next_state)
            except:
                pass

    def olivery_dynamic_integration_do_action(self,order,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_dynamic_integration_order,action.name)
                method_to_call(self,order,next_state)
            except:
                pass

    def authenticate_integration(self,integration_info,order):
        authenticate_url = integration_info.authenticate_url
        if integration_info.auth_content_type == 'application/x-www-form-urlencoded':
            authenticate_body = self.get_urlencoded_body(integration_info,order,integration_info.auth_field_map_ids)
        else:
            authenticate_body = integration_info.authenticate_body
        authenticate_header = json.loads(integration_info.authenticate_header)
        authenticate_method = integration_info.authenticate_method
        auth_token = ''
        message = ''
        if authenticate_url and authenticate_body:
            if not authenticate_method:
                authenticate_method = "POST"
            response = requests.request(authenticate_method.upper(),authenticate_url, data=authenticate_body, headers=authenticate_header)

            auth_status_code = integration_info.authenticate_success_code
            if not auth_status_code:
                auth_status_code = 200

            if response.status_code == auth_status_code:
                json_res = response.json()
                auth_token = ''
                auth_token_gen = integration_info.get_auth_token.split('/')
                for auth_token_gen_item in auth_token_gen:
                    if auth_token_gen_item in json_res and json_res[auth_token_gen_item]:
                        json_res = json_res[auth_token_gen_item]

                auth_token_prefix = integration_info.auth_token_prefix
                auth_token = json_res
                if auth_token_prefix:
                    auth_token = auth_token_prefix + ' ' + auth_token
            else:
                message = _("order failed to be authenticated in "+integration_info.name+" because of "+response.text)
                order.message_post(body=message)
        return auth_token,message

    def send_external_delivery_update(self,order,next_state):
        for rec in self:
            rec.with_delay(channel="root.integration",max_retries=2).update_order_status(rec)

    def update_order_status(self,order):
        if order.assign_to_business:
            messages = []
            integration_infos = self.env['rb_delivery.integration'].sudo().search([('assign_to_business','=',order.assign_to_business.id),('integration_type','=','update_status')])
            if integration_infos and integration_infos[0]:
                integration_info = integration_infos[0]

                if integration_info.needs_authenticate:
                    auth_token, message = self.authenticate_integration(integration_info,order)
                    if auth_token:
                        messages = self.update_order_status_webhook(order,integration_info,messages,auth_token)
                    else:
                        messages.append(message)
                else:
                    messages = self.update_order_status_webhook(order,integration_info,messages)
            if len(messages)>0:
                self.env['rb_delivery.utility'].send_toast('for_user', messages , str(self._uid))

    def update_order_status_webhook(self,order,integration_info,messages,auth_token=False):
        success = False
        integration_url = integration_info.integration_url
        integration_header = json.loads(integration_info.integration_header)
        integration_logs_vals = {"user_id":self._uid,"order_id":order.id,"integration_type":"Update Status","name":integration_info.name}
        if auth_token:
            integration_header['Authorization'] = auth_token
        integration_method = integration_info.integration_method
        if not integration_method:
            integration_method = "POST"
        if integration_info.content_type and integration_info.content_type == 'application/x-www-form-urlencoded':
            integration_body = self.get_urlencoded_body(integration_info,order,integration_info.field_map_ids)
        else:
            integration_body = self.get_integration_body(integration_info,order)
        integration_logs_vals['integration_body'] = integration_body
        integration_response = requests.request(integration_method.upper(),integration_url, data=integration_body.encode('utf-8'), headers=integration_header)
        integration_status_code = integration_info.integration_success_code
        if not integration_status_code:
            integration_status_code = 200
        if integration_response.status_code == integration_status_code:
            try:
                integration_json_res = integration_response.json()
                integration_status_code_res = integration_json_res
                if integration_info.additional_integration_success_code:
                    additional_integration_status_success_code = ''
                    additional_integration_success_code_list = integration_info.additional_integration_success_code.split('=')
                    if len(additional_integration_success_code_list)>0:
                        additional_integration_success_code_vals = additional_integration_success_code_list[0]
                        additional_integration_status_success_code = additional_integration_success_code_list[1]
                        additional_integration_success_code_vals_list = additional_integration_success_code_vals.split('/')
                        for additional_integration_success_code_vals_item in additional_integration_success_code_vals_list:
                            if additional_integration_success_code_vals_item in integration_status_code_res and integration_status_code_res[additional_integration_success_code_vals_item]:
                                integration_status_code_res = integration_status_code_res[additional_integration_success_code_vals_item]
                        if additional_integration_status_success_code and int(additional_integration_status_success_code) == integration_status_code_res:
                            success = True
                        else:
                            success = False
                    else:
                        success = False
                else:
                    success = True
            except Exception as e:
                success = False
        else:
            success = False
        if success:
            integration_logs_vals['success'] = True
            integration_logs_vals['returned_message'] = integration_response.text
            message = _("Order was successfully updated in "+integration_info.name)
            messages.append(message)
            order.message_post(body=message)
        else:
            message = _("order failed to be updated in "+integration_info.name+" because of "+integration_response.text)
            integration_logs_vals['success'] = False
            integration_logs_vals['returned_message'] = message
            messages.append(message)
            order.message_post(body=message)
        integration_logs = self.env['rb_delivery.integration_logs'].sudo().create(integration_logs_vals)
        return messages

    def get_integration_status(self, integration_info,order):
        variable_value = order['state']
        if integration_info.status_map_ids:
            variable_value = '""'
            for status_map_id in integration_info.status_map_ids:
                if status_map_id.state == order['state']:
                    variable_value = status_map_id.name
                    break
        return variable_value

    def normalize_integration_string(self,variable_str,order,integration_info):
        value = ''
        variable = re.findall(r'\(.*?\)', variable_str)
        if len(variable)>0:
            variable = variable[0]
            variable_str = variable.strip('()')
            variable_list = variable_str.split("+")
            value = ''
            variable_value = ''
            for variable_string in variable_list:
                variable_items = variable_string.split('.')
                variable_value = order
                for variable_item in variable_items[1:]:
                    field = self.env['ir.model.fields'].sudo().search([('name','=',variable_item),('model', '=', 'rb_delivery.order')])
                    if field.ttype == 'datetime' or field.ttype == 'date':
                        date_format = integration_info.date_format
                        if date_format:
                            if date_format == 'isoformat_8601':
                                variable_value = variable_value[variable_item].isoformat() + ".000Z"
                            else:
                                variable_value = getattr(variable_value[variable_item], date_format)()
                        else:
                            variable_value = variable_value[variable_item].isoformat()
                    else:
                        if variable_item == 'state':
                            variable_value = self.get_integration_status(integration_info,order)
                        else:
                            if variable_item.strip() in variable_value and (variable_value[variable_item.strip()] or variable_value[variable_item.strip()] == 0):
                                variable_value = variable_value[variable_item.strip()]

                            else:
                                variable_value = '""'
                if variable_value and not isinstance(variable_value,float) and len(variable_list)>1:
                    if variable_value != '""':
                        value = str(variable_value) + ', ' + str(value)
                else:
                    value = variable_value
        else:
            value = variable_str
        return value

    def get_urlencoded_body(self,integration_info,order,integration_fields):
        returned_string = ''
        for field in integration_fields:
            variable = self.normalize_integration_string(field.field,order,integration_info)
            if returned_string:
                returned_string = returned_string +'&'+ str(field.name) +'='+str(variable)
            else:
                returned_string = str(field.name) +'=' + str(variable)
        return returned_string

    def get_integration_body(self,integration_info,order, integration_body=None):
        integration_body = integration_body or integration_info.integration_body
        variables = re.findall(r'\(.*?\)', integration_body)
        for variable in variables:
            variable_str = variable.strip('()')
            variable_list = variable_str.split("+")
            value = ''
            for variable_string in variable_list:
                variable_string = variable_string.strip()
                if (variable_string.startswith('"') and variable_string.endswith('"')) or \
                (variable_string.startswith("'") and variable_string.endswith("'")):
                    literal_value = variable_string[1:-1]
                    if value:
                        value = str(value) + literal_value
                    else:
                        value = literal_value
                else:
                    variable_items = variable_string.split('.')
                    variable_value = order
                    for variable_item in variable_items[1:]:
                        field = self.env['ir.model.fields'].sudo().search([('name','=',variable_item),('model', '=', 'rb_delivery.order')])
                        if field.ttype == 'datetime' or field.ttype == 'date':
                            date_format = integration_info.date_format
                            if date_format:
                                if date_format == 'isoformat_8601':
                                    variable_value = variable_value[variable_item].isoformat() + ".000Z"
                                else:
                                    variable_value = getattr(variable_value[variable_item], date_format)()
                            else:
                                variable_value = variable_value[variable_item].isoformat()
                        else:
                            if variable_item == 'state':
                                variable_value = self.get_integration_status(integration_info,order)
                            else:
                                if variable_item.strip() in variable_value and (variable_value[variable_item.strip()] or variable_value[variable_item.strip()] == 0):
                                    variable_value = variable_value[variable_item.strip()]
                                else:
                                    variable_value = '""'
                    
                    if value and variable_value != '""':
                        value = str(value) + str(variable_value)
                    elif not value:
                        value = variable_value

            if value != '""':
                integration_body = integration_body.replace(variable, '"'+str(value)+'"' if value and not isinstance(value, bool) else str(value))
            else:
                integration_body = integration_body.replace(variable, '""')
        return integration_body



    def get_multipart_form_data_body(self, integration_info, integration_fields, order):
        multipart_data = {}
        for field in integration_fields:
            variable = self.get_integration_body(integration_info, order, field.field)
            if isinstance(variable, str):
                variable = variable.strip('"')
            multipart_data[str(field.name)] =str(variable)
        return multipart_data