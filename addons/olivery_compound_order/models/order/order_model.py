# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api, _
from openerp.exceptions import ValidationError,Warning
import time
from datetime import datetime, timedelta
import requests
import json
import os
import re
_logger = logging.getLogger(__name__)
import barcode
from barcode.writer import ImageWriter
import base64
import io



class olivery_compound_order_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    order_id = fields.Char('Order ID',track_visibility="on_change")

    product_name = fields.Char('Product Name',track_visibility="on_change")

    order_ids = fields.One2many(
        comodel_name = 'rb_delivery.order', inverse_name="compound_order_id",
        string = 'Orders',
        relation = 'order_order_item',
        column1 = 'order_item_id',
        column2 = 'order_id')

    compound_order_id = fields.Many2one('rb_delivery.order',"Compound order")

    compound_order = fields.Boolean('Is compound order', default=False)

    compound_refrence = fields.Char('compound Reference', readonly=True,track_visibility=False,copy=False)

    compound_refrence_barcode = fields.Binary('Barcode', compute="create_compound_ref_barcode",copy=False)


    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------


    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    def filter_values(self,values):
        self.SUDO_FIELDS = self.SUDO_FIELDS + ['compound_order_id','compound_order']

        return super(olivery_compound_order_order, self).filter_values(values)

    @api.multi
    def write(self,values):
        order = super(olivery_compound_order_order, self).write(values)

        if not self._context.get('child_update') :

            parent_records_to_update = self.filtered(lambda rec: rec.compound_order)
            for rec in parent_records_to_update:
                edited_values = {}
                reflected_fields_ids = self.env['rb_delivery.client_configuration'].get_param('field_to_be_reflected_in_child_compound_order')
                if len(reflected_fields_ids) > 0 :
                    reflected_fields = self.env['ir.model.fields'].search([('id', 'in',reflected_fields_ids)])
                    for field in reflected_fields:
                        if field.name in values :
                            if field.ttype == 'many2one':
                                edited_values[field.name] = rec[field.name].id
                            elif field.ttype == 'many2many' or field.ttype == 'one2many':
                                edited_values[field.name] = [(6,0,rec[field.name].ids)]
                            elif rec[field.name]:
                                edited_values[field.name] = rec[field.name]
                    if len(edited_values.keys()) > 0:
                        rec.order_ids.with_context(child_update=True).write(edited_values)
        return order

    def check_if_clone(self,values):
        for rec in self:
            if rec.compound_order_id:
                continue
            else:
                return super(olivery_compound_order_order,self).check_if_clone(values)

    def check_clone_compound_orders(self,orders):
            if len(orders) > 1:
                compound_orders_sequences = ''
                for order in orders:
                    if order.compound_order:
                        compound_orders_sequences = compound_orders_sequences + ' , ' +order.sequence

                if compound_orders_sequences :
                    raise ValidationError(_('You are Trying to clone orders that contain compound orders of sequence '+compound_orders_sequences+',to clone compound order it should be one by one'))


    @api.model
    def create(self,values):
        int_no_of_items=False

        if 'no_of_items' in values and values['no_of_items']:
            try:
                int_no_of_items=int(values['no_of_items'])
                values['compound_order'] = True
            except :
                raise ValidationError (_('Number of Items just take numbers'))

        order = super(olivery_compound_order_order, self).create(values)

        if 'is_compound' in values and values['is_compound']:
            return order
        else:

            if 'no_of_items' in values and values['no_of_items'] and int(values['no_of_items']) > 1:
                if 'reference_id' in values and values['reference_id']:
                    del values['reference_id']
                self.generate_order_items(values,order.sequence,order)

        return order

    def generate_order_items(self,values,sequence,order):
        order_ids = []
        no_of_items = int(values['no_of_items'])
        for x in range(no_of_items):
            if 'message_attachment_count' in values and values['message_attachment_count']:
                del values['message_attachment_count']
            if '__last_update' in values and values['__last_update']:
                del values['__last_update']
            if 'message_follower_ids' in values and values['message_follower_ids']:
                del values['message_follower_ids']

            order_values = values
            order_values['no_of_items'] = "1"
            postfix = x+1
            order_values['sequence'] = sequence + '-' + str('%03d' % postfix)
            order_values['order_type_id'] = self.env.ref('olivery_compound_order.item_order').id
            order_values['compound_order_id'] = order.id
            order_values['compound_order'] = False
            order_values['cost'] = 0
            order_values['copy_total_cost'] = 0
            order_item = self.env['rb_delivery.order'].create(order_values)

        return True

    @api.one
    @api.depends('compound_refrence')
    def create_compound_ref_barcode(self):
        if (self.compound_refrence):
            barcode.base.Barcode.default_writer_options['write_text'] = False

            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.compound_refrence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.compound_refrence_barcode = encoded

    def get_child_compound_orders(self):
        order_tree_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        domain = [('id', 'in', self.order_ids.ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(order_tree_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain
            }

    def get_compound_orders(self):
        order_tree_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        domain = [('id', '=', self.compound_order_id.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(order_tree_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain
            }

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):

        is_compound = self._context.get('is_compound')
        if is_compound :
            domain = [('compound_order_id','!=',False)]
        return super(olivery_compound_order_order,self).search_read(domain, fields, offset, limit, order)

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------


class order_multi_merge_orders(models.TransientModel):
    _name = 'rb_delivery.merge_orders'

    def get_same_id_orders(self, docs):
        new_doc = []
        new_docs = []
        ids_list = []
        for doc in docs:
            if doc.order_id not in ids_list:
                ids_list.append(doc.order_id)
        for order_id in ids_list:
            for doc in docs:
                if doc.order_id == order_id:
                    new_doc.append(doc)
            new_docs.append(new_doc)
            new_doc= []
        print(new_docs)
        return new_docs

    @api.multi
    def merge_orders(self):
        recs = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        orders = self.get_same_id_orders(recs)
        for order in orders:
            valid = False
            order_items_no = int(order[0].no_of_items)
            if len(order) < order_items_no:
                canceled_orders = 0
                order_items = self.env['rb_delivery.order'].sudo().search([('order_id','=',order[0].order_id)])
                for item in order_items:
                    if item.state == 'canceled':
                        canceled_orders = canceled_orders + 1
                if canceled_orders == (order_items_no - len(order)):
                    valid = True
                    cost = 0
                    vals = {'compound_order':True,'reference_id':order[0].order_id,'assign_to_business':order[0].assign_to_business.id,'customer_area':order[0].customer_area.id,'customer_sub_area':order[0].customer_sub_area.id,'customer_address':order[0].customer_address,'customer_mobile':order[0].customer_mobile,'second_mobile_number':order[0].second_mobile_number,'state':'in_progress'}
                    if order[0].service:
                        vals['service'] = [(6,0,order[0].second_mobile_number.ids)]
                    order_item_ids = []
                    for order_item in order:
                        if order_item.state != 'in_branch':
                            raise ValidationError(_('Order Item status must be In Branch'))
                        order_item.write({'state':'merged'})
                        order_item_ids.append(order_item.id)
                        if order_item.assign_to_business.inclusive_delivery:
                            vals['copy_total_cost'] = cost + order_item.copy_total_cost
                        else:
                            vals['cost'] = cost + order_item.cost
                    vals['order_ids'] = [(6,0,order_item_ids)]
                    compound_order = self.env['rb_delivery.order'].sudo().search([('reference_id','=',order[0].order_id),('state','!=','deleted'),('state','!=','canceled')])
                    if compound_order:
                        raise ValidationError(_('Compound order alread exists with sequence ') + compound_order.sequence)
                    else:
                        new_order = self.env['rb_delivery.order'].create(vals)
                else:
                    raise ValidationError(_("Orders selected do not match the number of orders."))
            elif len(order) == order_items_no:
                valid = True
                cost = 0
                vals = {'compound_order':True,'reference_id':order[0].order_id,'assign_to_business':order[0].assign_to_business.id,'customer_area':order[0].customer_area.id,'customer_sub_area':order[0].customer_sub_area.id,'customer_address':order[0].customer_address,'customer_mobile':order[0].customer_mobile,'second_mobile_number':order[0].second_mobile_number,'state':'in_progress'}
                if order[0].service:
                    vals['service'] = [(6,0,order[0].second_mobile_number.ids)]
                order_item_ids = []
                for order_item in order:
                    if order_item.state != 'in_branch':
                        raise ValidationError(_('Order Item status must be In Branch'))
                    order_item.write({'state':'merged'})
                    order_item_ids.append(order_item.id)
                    if order_item.assign_to_business.inclusive_delivery:
                        vals['copy_total_cost'] = cost + order_item.copy_total_cost
                    else:
                        vals['cost'] = cost + order_item.cost
                vals['order_ids'] = [(6,0,order_item_ids)]
                compound_order = self.env['rb_delivery.order'].sudo().search([('reference_id','=',order[0].order_id),('state','!=','deleted'),('state','!=','canceled')])
                if compound_order:
                    raise ValidationError(_('Compound order alread exists with sequence ') + compound_order.sequence)
                else:
                    new_order = self.env['rb_delivery.order'].create(vals)
        return True

class order_select_state_wizard_custom(models.TransientModel):
    _inherit = 'rb_delivery.select_state'


    @api.multi
    def btn_show_dialog_box(self):

        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))

        show_scan_box = self.env['rb_delivery.client_configuration'].get_param('show_scan_order_wizard')

        status=self.env['rb_delivery.status'].search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')])

        if status.clone or show_scan_box:
            self.env['rb_delivery.order'].check_clone_compound_orders(orders)

            returned_wizard = super(order_select_state_wizard_custom, self).btn_show_dialog_box()
            context = {}
            if 'context' in returned_wizard and returned_wizard['context']:
                context = returned_wizard['context']

                compounds_orders = []
                for order in orders:
                    if order.compound_order:
                        compounds_orders.append(order.id)
                if len(compounds_orders) > 0:
                    returned_wizard['res_model'] = "rb_delivery.scan_compound_orders"
                    returned_wizard['name'] = _("Scan order items")
                    context['res_id'] = returned_wizard['res_id']
                    del returned_wizard['res_id']
                    context['compound_orders'] = compounds_orders
                    context['state'] = status.name

            return returned_wizard
        else:
            return super(order_select_state_wizard_custom, self).btn_show_dialog_box()

class order_multi_scan_compound_orders(models.TransientModel):
    _name = 'rb_delivery.scan_compound_orders'

    def _get_order_ids(self):
        orders = self.env['rb_delivery.order'].browse(self._context.get('compound_orders'))
        return [('id','in',orders.order_ids.ids)]

    order_barcode = fields.Char("Order Barcode")

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'scan_order_order_item',
        column1 = 'order_id',
        column2 = 'scan_order_id',domain = _get_order_ids)

    message = fields.Char("Message",readonly=1)

    @api.onchange('order_barcode')
    def change_barcode(self):
        self.message = ""
        if self.order_barcode:
            order_item = self.env['rb_delivery.order'].search([('sequence','=',self.order_barcode)])
            if order_item:
                self.order_ids = [(4, order_item.id)]
                self._context['orders'].append(order_item.id)
                self.order_barcode = ""
            else:
                self.message = _("Order of sequence "+self.order_barcode+" was not found.")

    def change_state(self):
        if 'state' in self._context and self._context['state']:
            status=self.env['rb_delivery.status'].search([('name','=',self._context['state']),'|',('status_type','=',False),('status_type','=','olivery_order')])

            if status.clone:
                parent_order = self.env['rb_delivery.order'].browse(self._context['compound_orders'])
                money_collection_cost = 0
                for order in self.order_ids:
                    if order.assign_to_business.inclusive_delivery:
                        money_collection_cost += order.copy_total_cost
                    else:
                        money_collection_cost += order.cost

                if parent_order.assign_to_business.inclusive_delivery:
                    parent_order.copy_total_cost = parent_order.copy_total_cost - money_collection_cost
                else:
                    parent_order.cost = parent_order.cost - money_collection_cost

                new_parent_orders_ids = list(filter(lambda id: id not in self.order_ids.ids, parent_order.order_ids.ids))

                values = {
                    'order_ids':[(6,0,new_parent_orders_ids)],
                    'no_of_items' : len(new_parent_orders_ids),
                    'state' : status.name
                }

                parent_order.write(values)

                cloned_order = self.env['rb_delivery.order'].search([('cloner_order_id','=',parent_order.id)])
                clone_order_values = {
                    'no_of_items' : len(self.order_ids.ids),
                    'order_ids' : [(6,0,self.order_ids.ids)],
                    'compound_order' : True,
                }
                cloned_order.write(clone_order_values)
                self.order_ids.write({'compound_order_id' : cloned_order.id,'state' : cloned_order.state})
                self.env.context = dict(self.env.context)
                self.env.context.update({'state': False})

            else:

                if 'compound_orders' in self._context and self._context['compound_orders']:
                    compound_orders = self._context['compound_orders']
                    order_item_ids = []
                    for compound_order_id in compound_orders:
                        compound_order = self.env['rb_delivery.order'].search([('id','=',compound_order_id)])
                        order_item_ids = compound_order.order_ids.ids
                        if not all(order_item in self.order_ids.ids for order_item in order_item_ids):
                            raise Warning(_("Not all order items of compound order "+compound_order.sequence + " were scanned."))

            return{
                'type':'ir.actions.act_window',
                'name':_('Message'),
                'res_model':'display.dialog.box',
                'view_type':'form',
                'view_mode':'form',
                'target':'new',
                'res_id':self._context['res_id'],
                'context':self._context
            }


class order_compound_orders(models.TransientModel):

    _name = 'rb_delivery.select_compound_orders'
    _description = "Select Compound Orders"

    def compound_selected_orders(self):

        orders = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        assign_to_businesses_orders = []
        assign_to_businesses = []
        for order in orders:
            if order.compound_order_id:
                raise ValidationError(_('You alredy have parent order of sequence ' +order.compound_order_id.sequence))
            assign_to_businesses.append(order.assign_to_business)

        assign_to_businesses = list(set(assign_to_businesses))

        for assign_to_business in assign_to_businesses:
            business_orders = self.env['rb_delivery.order'].search([('id','in',self._context.get('active_ids')),('assign_to_business','=',assign_to_business.id)])
            assign_to_businesses_orders.append(business_orders)

        default_compound_order_status_id = self.env['rb_delivery.client_configuration'].get_param('default_status_compound_order')
        default_child_compound_order_status_id = self.env['rb_delivery.client_configuration'].get_param('default_status_child_compound_order')
        default_child_compound_order = False
        default_compound_order_status = False
        if default_child_compound_order_status_id :
            default_child_compound_order = self.env['rb_delivery.status'].browse(default_child_compound_order_status_id)[0]
        if default_compound_order_status_id:
            default_compound_order_status = self.env['rb_delivery.status'].browse(default_compound_order_status_id)[0]

        for business_orders in assign_to_businesses_orders:
            matching_orders = []
            matching_group_of_orders = []

            for order in business_orders:

                if order.customer_mobile and order.customer_area and order not in matching_group_of_orders:
                    matching_group_of_orders = self.env['rb_delivery.order'].search([('customer_mobile','=',order.customer_mobile),('customer_area','=',order.customer_area.id),('id','in',business_orders.ids)])
                    if len(matching_group_of_orders)>0 :
                        matching_orders.append(matching_group_of_orders)


            for matching_group in matching_orders :
                if len(matching_group) > 1:
                    money_collection_cost = 0

                    for order in matching_group:
                        if order.assign_to_business.inclusive_delivery:
                            money_collection_cost += order.copy_total_cost
                        else:
                            money_collection_cost += order.cost
                    parent_state = False
                    if default_compound_order_status:
                        parent_state = default_compound_order_status.name
                    else:
                        parent_state = 'in_branch'

                    values = {
                        'compound_order' : True,
                        'order_ids' : [(6,0,matching_group.ids)],
                        'assign_to_business' : matching_group[0].assign_to_business.id,
                        'customer_address' : matching_group[0].customer_address,
                        'customer_area' : matching_group[0].customer_area.id,
                        'customer_mobile' : matching_group[0].customer_mobile,
                        'customer_name' : matching_group[0].customer_name,
                        'no_of_items' : len(matching_group.ids),
                        'state' : parent_state,
                        'is_compound' : True,

                    }
                    if matching_group[0].assign_to_business.inclusive_delivery:
                        values['copy_total_cost'] = money_collection_cost
                    else:
                        values['cost'] = money_collection_cost

                    if matching_group[0].customer_sub_area:
                        values['customer_sub_area'] = matching_group[0].customer_sub_area.id

                    parent_compound_order = self.env['rb_delivery.order'].create(values)
                    child_status = False
                    if default_child_compound_order :
                        child_status = default_child_compound_order.name
                    else:
                        child_status = 'compound'

                    matching_group.write({'compound_order_id' : parent_compound_order.id,'state' : child_status})

                    postfix = 1
                    for order in matching_group :

                        order.compound_refrence = parent_compound_order.sequence + '-' + str('%03d' % postfix)
                        postfix +=1
