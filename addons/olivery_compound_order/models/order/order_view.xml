<?xml version="1.0" encoding="UTF-8"?>

<odoo>
  <data>


      <record id="view_tree_rb_delivery_order_compound_order" model="ir.ui.view">
         <field name="name">view_tree_rb_delivery_order_compound_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_tree_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
               <field name="no_of_items" sum="total items"/>
            </xpath>
         </field>
      </record>

      <!--inherit form view-->
      <record id="view_form_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_form_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order" />
         <field name="arch" type="xml">

            <xpath expr="//button[@name='get_notification']" position="after">
               <field name="compound_order_id" invisible="1" />
               <button type="object" name="get_child_compound_orders" style="width: 165px !important" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('compound_order', '=', False)]}">
                  <div class="fa fa-fw fa fa-tags o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                      <span>Child Compound Orders</span>
                  </div>
              </button>
              <button type="object" name="get_compound_orders" class="btn btn-sm oe_stat_button o_form_invisible"   attrs="{'invisible':[('compound_order_id', '=', False)]}">
               <div class="fa fa-fw fa fa-tags o_button_icon"/>
               <div class="o_form_field o_stat_info" data-original-title="" title="">
                   <span>Compound Order</span>
               </div>
           </button>
            </xpath>
            <field name="financial_state" position="before">
               <div style="width:100%">
                  <div style="width: 50% !important;display: flex;flex-direction: column;align-items: center;" attrs="{'invisible':['|',('id','=',False),('compound_refrence','=',False)]}">
                     <field name="compound_refrence_barcode" string="Compound Reference" style="display:block;text-align:center;width:200px;height:75px !important;" height="75" width="200" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|',('id','=',False),('compound_refrence','=',False)]}"/>
                     <label for="compound_refrence" string="Compound Reference" style="width:100% !important;text-align:center !important;" attrs="{'invisible':['|',('id','=',False),('compound_refrence','=',False)]}"/>
                     <field name="compound_refrence" style="display:block;text-align:center;width:200px;" attrs="{'invisible':['|',('id','=',False),('compound_refrence','=',False)]}" readonly="True"/>
                  </div>
               </div>
            </field>
            <field name="service" position="after">
               <field name="no_of_items"/>
            </field>
            <field name="order_type_id" position="after">
               <field name="order_id" readonly="1"/>
               <field name="product_name" readonly="1"/>
               <field name="compound_order" invisible="1"/>
            </field>
            <notebook position="inside">
               <page string="Order Items" attrs="{'invisible':[('compound_order', '=', False)]}">
               <group>
               <field name="order_ids" readonly="1">
                  <tree delete="1">
                     <field name="sequence" />
                     <field name="order_id"/>
                     <field name="product_name" />
                     <field name="no_of_items"/>
                     <field name="customer_name"/>
                     <field name="customer_mobile"/>
                     <field name="customer_area"/>
                     <field name="customer_address"/>
                 </tree>
               </field>
               </group>
               </page>
            </notebook>
         </field>
      </record>

      <record id="view_search_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_search_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_search_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//filter[@name='archived_orders']" position="after">
               <filter name="compound_orders" string="Compound Orders" domain="[('compound_order','=',True)]"/>
               <filter name="child_compound_orders" string="Child Compound Orders" context="{'is_compound' : True}" domain="[('compound_order_id','=',False)]"/>
            </xpath>
         </field>
      </record>

      <record id="view_form_rb_delivery_merge_orders" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_merge_orders</field>
            <field name="model">rb_delivery.merge_orders</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>

                    </sheet>
                    <footer>
                        <button name="merge_orders" type="object" string="Merge"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>
        <record id="view_form_rb_delivery_scan_compound_orders" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_scan_compound_orders</field>
            <field name="model">rb_delivery.scan_compound_orders</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>
                    <field name="message"/>
                     <group name="group_top">
                        <field name="order_barcode"/>
                        <field name="order_ids">
                           <tree delete="1">
                              <field name="note" class="note"/>
                              <field name="sequence"/>
                              <field name="state"/>
                              <field name="assign_to_agent"/>
                           </tree>
                        </field>
                     </group>

                    </sheet>
                    <footer>
                        <button name="change_state" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_select_compound_orders" model="ir.ui.view">

         <field name="name">view_form_rb_delivery_select_compound_orders</field>
         <field name="model">rb_delivery.select_compound_orders</field>

         <field name="arch" type="xml">

             <form create="false" edit="false">

                 <header>
                 </header>

                 <sheet>
                  <group name="group_top">
                     <separator string="Are you sure that you want to compound ? "/>
                   </group>
                 </sheet>
                 <footer>
                     <button name="compound_selected_orders" type="object" string="Compound"/>
                     <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                 </footer>

             </form>

         </field>
     </record>

   </data>
</odoo>
