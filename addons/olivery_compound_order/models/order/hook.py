from openerp.api import Environment, SUPERUSER_ID

def uninstall_hook(cr, registry):
    with Environment.manage():

        env = Environment(cr, SUPERUSER_ID, {})    
    action = env.ref('rb_delivery.action_rb_delivery_order', raise_if_not_found=False)

    if action:
        domain = eval(action.domain)            
        if ('compound_order_id', '=', False) in domain:
            domain.remove(('compound_order_id', '=', False))
        action.write({'domain': str(domain)})
