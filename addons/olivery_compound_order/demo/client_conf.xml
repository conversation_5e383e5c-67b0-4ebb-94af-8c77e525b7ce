<odoo>
    <data>
        <record id="client_configuration_default_status_compound_order" model="rb_delivery.client_configuration">
            <field name="key">default_status_compound_order</field>
            <field name="value">False</field>
            <field name="description">This configuration should have one status and this status will be the default status for parent compound order when compound orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_branch')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_default_status_child_compound_order" model="rb_delivery.client_configuration">
            <field name="key">default_status_child_compound_order</field>
            <field name="value">False</field>
            <field name="description">This configuration should have one status and this status will be the default status for child compound order when compound orders</field>
            <field name="status" eval="[(6, 0, [ref('olivery_compound_order.status_compound')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_field_to_be_refelcted_on_child_compound_order" model="rb_delivery.client_configuration">
            <field name="key">field_to_be_reflected_in_child_compound_order</field>
            <field name="value">False</field>
            <field name="related_to_field_ids">True</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_order" />
            <field name="field_ids" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__state')])]" />
            <field name="platform_type" >web</field>
            <field name="description">Any field included in this configuration will be refelcted on child compound order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail" />
        </record>

        <record id="client_configuration_show_scan_order_wizard" model="rb_delivery.client_configuration">
            <field name="key">show_scan_order_wizard</field>
            <field name="value">False</field>
            <field name="description">This to show scan wizard box when change status and this will be showen when status is colne</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
    </data>
</odoo>