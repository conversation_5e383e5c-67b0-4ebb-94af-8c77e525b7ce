<odoo >
    <data noupdate="1">

        <record id="status_mobile_action_select_child_compound_orders" model="rb_delivery.status_mobile_action">
            <field name="name">select_child_compound_orders</field>
            <field name="description">Show child compound orders on mobile</field>
        </record>

        <record id="status_merged" model="rb_delivery.status">
            <field name="sequence">33</field>
            <field name="name">merged</field>
            <field name="title">Merged</field>
            <field name="title_ar">مندمجة</field>
            <field name="default">False</field>
            <field name="description">This is when order items are merged in compund order</field>
            <field name="group_ids" eval="[(6, 0, [ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_action_status_ids" eval="[(6, 0, [ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6, 0, [ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record> 

        <record id="status_compound" model="rb_delivery.status">
            <field name="sequence">34</field>
            <field name="name">compound</field>
            <field name="title">Compound</field>
            <field name="title_ar">مجمع</field>
            <field name="default">False</field>
            <field name="description">This is when compund order</field>
            <field name="group_ids" eval="[(6, 0, [ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_action_status_ids" eval="[(6, 0, [ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6, 0, [ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record> 
    </data>
</odoo>