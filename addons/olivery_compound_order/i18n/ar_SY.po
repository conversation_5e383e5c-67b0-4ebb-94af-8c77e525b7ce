# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_compound_order
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-16 06:26+0000\n"
"PO-Revision-Date: 2022-06-16 06:26+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_compound_order
#: model_terms:ir.ui.view,arch_db:olivery_compound_order.view_form_rb_delivery_merge_orders
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_order__compound_order
msgid "Compound order"
msgstr "طلب مركب"

#. module: olivery_compound_order
#: code:addons/olivery_compound_order/models/order/order_model.py:130
#: code:addons/olivery_compound_order/models/order/order_model.py:154
#, python-format
msgid "Compound order alread exists with sequence "
msgstr "الطلب المركب موجود بالفعل مع رقم تسلسلي "

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_merge_orders__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_compound_order
#: model_terms:ir.ui.view,arch_db:olivery_compound_order.view_form_rb_delivery_merge_orders
msgid "Merge"
msgstr "دمج"

#. module: olivery_compound_order
#: model:ir.actions.act_window,name:olivery_compound_order.action_rb_delivery_merge_orders
msgid "Merge Orders"
msgstr "دمج الطلبيات"

#. module: olivery_compound_order
#: model:rb_delivery.status,title:olivery_compound_order.status_merged
msgid "Merged"
msgstr "مندمجة"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_order__no_of_items
msgid "Number of items"
msgstr "عدد الوحدات"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_order__order_id
msgid "Order ID"
msgstr "رقم التعريفي للطلبية"

#. module: olivery_compound_order
#: code:addons/olivery_compound_order/models/order/order_model.py:120
#: code:addons/olivery_compound_order/models/order/order_model.py:144
#, python-format
msgid "Order Item status must be In Branch"
msgstr "حالة عناصر الطلبية يجب ان تكون في الفرع"

#. module: olivery_compound_order
#: model:ir.actions.act_window,name:olivery_compound_order.action_rb_delivery_order_item
#: model:ir.ui.menu,name:olivery_compound_order.menu_rb_delivery_order_item
#: model_terms:ir.ui.view,arch_db:olivery_compound_order.view_form_rb_delivery_order
msgid "Order Items"
msgstr "عناصر الطلب"

#. module: olivery_compound_order
#: code:addons/olivery_compound_order/models/order/order_model.py:59
#: code:addons/olivery_compound_order/models/order/order_model.py:65
#, python-format
msgid "Order type does not exist"
msgstr "نوع الطلب غير موجود"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_order__order_ids
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_compound_order
#: code:addons/olivery_compound_order/models/order/order_model.py:134
#, python-format
msgid "Orders selected do not match the number of orders."
msgstr "الطلبيات المحددة لا تتوافق مع عدد الطلبيات"

#. module: olivery_compound_order
#: model:ir.model.fields,field_description:olivery_compound_order.field_rb_delivery_order__product_name
msgid "Product Name"
msgstr "اسم المنتج"

#. module: olivery_compound_order
#: model:ir.model,name:olivery_compound_order.model_rb_delivery_merge_orders
msgid "rb_delivery.merge_orders"
msgstr ""

#. module: olivery_compound_order
#: model:ir.model,name:olivery_compound_order.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""

