<?xml version="1.0"?>
<odoo>
    <data>

    <!-- Order Items -->    
    <act_window id="action_rb_delivery_order_item" context="{'group_by': ['compound_order_id']}" domain="[('order_type_id','=','Item')]"  name="Order Items" res_model="rb_delivery.order" view_mode="tree,form,graph"/>
    <menuitem id="menu_rb_delivery_order_item" name="Order Items" parent="rb_delivery.menu_rb_delivery_orders" sequence="15" action="action_rb_delivery_order_item" />

    <act_window id="action_rb_delivery_merge_orders" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry" name="Merge Orders" src_model="rb_delivery.order" res_model="rb_delivery.merge_orders" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_select_compound_orders" groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry" name="Compound Orders" src_model="rb_delivery.order" res_model="rb_delivery.select_compound_orders" view_mode="form" target="new" multi="True" />

    <record id="rb_delivery.action_rb_delivery_order" model="ir.actions.act_window">
        <field name="domain">[('state','!=','deleted'),('state','!=','completed'),('state','!=','delivered_completed'),('state','!=','completed_returned'),('state','!=','canceled'),('order_type_id','!=','Item'),('compound_order_id','=',False)]</field>
    </record>

    <act_window id="action_rb_delivery_order_compound_order" context="{'group_by': ['compound_order_id']}" domain="[('compound_order_id','!=',False)]"  name="Compound Order" res_model="rb_delivery.order" view_mode="tree,form,graph"/>
    <menuitem id="menu_rb_delivery_order_compound" name="Compound Order" parent="rb_delivery.menu_rb_delivery_orders" sequence="15" action="action_rb_delivery_order_compound_order" />

    </data>
</odoo>
