<odoo>
	<data>
      <record model="report.paperformat" id="rb_delivery.paperformat_attendance_recap_report">
        <field name="name">paperformat.attendance.recap.report</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="page_width">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">30</field>
        <field name="margin_right">0</field>
        <field name="margin_bottom">5</field>
        <field name="margin_left">0</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">25</field>
        <field name="dpi">90</field>
    </record>
		<template id="run_sheet_rb_deilvery_compound_order" inherit_id="rb_delivery.dist">
			<xpath expr="//div[@class='article']" position="replace">
				<div class="article" style="font-size:13px">
				<table class="table table-condensed" style="font-size:0.8em;width:100%;border:1px solid black">
						<thead>
							<tr>
							    <th style="border:1px black solid">#</th>
								<th style="border:1px black solid">Barcode</th>
								<th style="border:1px black solid">Sender's name</th>
								<th style="border:1px black solid">Recipient's name</th>
								<th style="border:1px black solid">Address</th>
								<th style="border:1px black solid">COD</th>
								<th style="border:1px black solid">Notes</th>
								<th style="border:1px black solid">Total Items</th>
							</tr>
						</thead>
						<tbody class="sale_tbody">
							<t t-foreach="docs" t-as="doc">
								<tr style="page-break-inside: avoid;">
									<td>
									    <span t-esc="doc_index+1"/>
									</td>
									<td >
									<img style="width:200px;height:25px" t-attf-src="data:image/*;base64,{{doc.sudo().barcode}}" />
									<p style="margin: 0px !important" t-field="doc.sudo().sequence" />
									<p style="margin: 0px !important" t-field="doc.sudo().reference_id" />
									</td>
									<td style="border:1px solid black;">
									<t t-if="doc.sudo().assign_to_business.commercial_name">
										<span t-field="doc.sudo().assign_to_business.commercial_name" />
									</t>
									<t t-else="else">
										<span t-field="doc.sudo().assign_to_business" />
									</t>
									<p t-if="doc.sudo().commercial_number">
										<span t-field="doc.sudo().commercial_number" />
									</p>
									</td>

									<td style="border:1px solid black;">
									<span t-field="doc.sudo().customer_name" />
									<p t-field="doc.sudo().customer_mobile" />
									<p t-if="doc.sudo().second_mobile_number" t-field="doc.sudo().second_mobile_number" />
								</td>
									<td style="border:1px solid black;">
									<span t-field="doc.sudo().customer_area" />
									<span t-if="doc.sudo().customer_sub_area">
										,
									</span>
									<span t-if="doc.sudo().customer_sub_area" t-field="doc.sudo().customer_sub_area" />
									<span t-if="doc.sudo().customer_address">
										,
									</span>
									<span t-field="doc.sudo().customer_address" />
								</td>
									<td style="border:1px solid black;">
									<span t-field="doc.sudo().money_collection_cost" />
								</td>
								<td style="border:1px solid black;">
									<t t-set="note" t-value="request.env['rb_delivery.order'].sudo().append_fields_to_note(doc.sudo())"/>
									<span t-esc="note"/>
								</td>
                                <td style="border:1px solid black;">
									<span t-field="doc.sudo().no_of_items" />
								</td>

								</tr>

								<tr t-if="len(docs) == doc_index+1" style="font-weight:bold;page-break-inside: avoid;">
								<td>Total</td>
								<td>	</td>
								<td>	</td>
								<td>	</td>
							    <td>	</td>
							    <td>
									<t t-esc=" '%.2f'%  sum(l.sudo().money_collection_cost for l in docs) "></t>
								</td>
								<td>
								</td>
								<td >
                                    <t t-set="total_items" t-value=" '%.f'%  sum(float(l.sudo().no_of_items) for l in docs)" />
									<t t-esc="total_items" />
								</td>


								</tr>
							</t>
						</tbody>
					</table>
                    					<div class="oe_structure"/>

				</div>
			</xpath>
		</template>
	</data>
</odoo>
