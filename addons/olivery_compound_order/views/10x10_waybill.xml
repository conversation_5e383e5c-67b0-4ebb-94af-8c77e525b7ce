<odoo>
	<data>
		<record id="paperformat_order_detail_compound_10x10_waybill" model="report.paperformat">
			<field name="name">Delivery_Distribution</field>
			<field name="default" eval="True" />
			<field name="format">custom</field>
			<field name="orientation">Landscape</field>
			<field name="margin_top">1</field>
			<field name="margin_bottom">2</field>
			<field name="margin_left">0</field>
			<field name="margin_right">0</field>
			<field name="header_line" eval="False" />
			<field name="page_height">100</field>
			<field name="page_width">100</field>
			<field name="header_spacing">11</field>
			<field name="dpi">90</field>
		</record>
		<template id="10x10waybill_custom" inherit_id="olivery_templates.10x10_waybill">
			<xpath expr="//div[@class='header']" position="replace">
					<div class="header" style="display:none;direction:rtl;font-size:12px !important"></div>
			</xpath>
			<xpath expr="//div[@class='page']" position="replace">
				<div class="page" style="page-break-after:always;margin-bottom:0px !important">
					<div class="row" style="margin: 0 0 0 0;height:50px">
						<div class="col-6" style="padding-right: 0px; padding-left: 0px">
							<img class="d-flex justify-content-start" t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="height: 50px;max-wight: 100px;" />
						</div>
						<div class="col-6" style="margin-top:2.5px;padding-right: 0px; padding-left: 0px">
							<h5 style="font-size:1em;margin:0 auto !important;" >
								<span style="color:black" t-field="company.company_registry" />
							</h5>
							<h5 style="font-size:0.9em !important;margin-bottom:0px !important">
								Pickup Date:
								<span style="font-size:0.9em" t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')" />
							</h5>
						</div>
					</div>
					<div class="row" style="margin: 0 0 10px 0;padding: 0 0.1em 0 0.1em">
						<div class="col-5"
							style="height:75px;padding-right: 0px; padding-left: 0px;margin-bottom:5px">
							<fieldset class="scheduler-border"
								style="height:75px;border: 1px groove #ddd !important;padding: 0 0.1em 0.7em 0.1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
								<legend
									style="width:auto;border-bottom:none;font-weight: bold;font-size:14px;color:black;text-align: center;">Sender's
									details</legend>
								<div class="control-group">
									<tbody>
										<td>
											<div
												style="font-size: 11px; height: 58px; max-height: 48px; overflow: hidden;width:110px">
												<t t-set="business"
													t-value="doc.sudo().assign_to_business" />
												<t
													t-if="doc.sudo().show_alt_address and doc.sudo().alt_business_name">
													<t t-set="business_name"
														t-value="doc.sudo().alt_business_name" />
												</t>
												<t
													t-elif="doc.sudo().show_follower_info_in_waybill and doc.sudo().show_follower_info and doc.sudo().follower_store_name">
													<t t-set="business_name"
														t-value="doc.sudo().follower_store_name" />
												</t>
												<t t-elif="business">
													<t t-if="business.commercial_name">
														<t t-set="business_name"
															t-value="business.commercial_name" />

													</t>

												</t>
												<t t-else="else">
													<t t-set="business_name"
														t-value="business" />
												</t>

												<span
													t-esc="business_name[:100] + '...' if business_name and len(business_name) > 100 else (business_name if business_name else '')" />


											</div>
											<br />
										</td>
										<tr>

										</tr>
									</tbody>
								</div>
							</fieldset>
						</div>
						<div class="col-7" style="height:75px;padding-right: 0px; padding-left: 0px;margin-bottom:10px">
							<fieldset class="scheduler-auto" style="height:75px;border: 1px groove #ddd !important; padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
								<legend style="width:auto;border-bottom:none;color:black;text-align: center;font-size:14px;font-weight: bold;">
									Notes
								</legend>
								<div class="control-group">
									<tbody>
										<tr>
											<td style="">


												<div style="max-width: 198px; font-size: 11px;">
													<t t-if="doc.sudo().note">
														<p>
															<span t-esc="doc.sudo().note[:55]" />
															<t t-if="len(doc.sudo().note) > 55">...</t>
														</p>
													</t>
												</div>
											</td>
										</tr>
									</tbody>
								</div>
							</fieldset>
						</div>
					</div>
					<fieldset class="scheduler-border" style="border: 1px groove #ddd !important; padding: 0 0.1em 0.7em 0.1em !important; margin: 0 0 10px 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;height: 180px;max-wight: 200px;">
						<legend style="width:auto;border-bottom:none;font-size:14px;color:black;text-align: center;font-weight: bold;">
							Recipient's details
						</legend>
						<div class="control-group">
							<tbody>
								<tr>
									<td colspan="2" style="text-align:left ">
										<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
											<t t-if="doc.sudo().customer_name">
												<span t-field="doc.sudo().customer_name" />
											</t>
										</div>
									</td>
								</tr>
								<tr>
									<td colspan="2" style="text-align:left">
										<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
											<span t-field="doc.sudo().customer_area" />
											<span t-if="doc.sudo().customer_sub_area">
												/
											</span>
											<span t-if="doc.sudo().customer_sub_area" t-field="doc.sudo().customer_sub_area" />
										</div>
									</td>
								</tr>
								<tr>
									<td colspan="2" style="text-align:left">
										<div style="font-size:16px;border-bottom: 1px solid #d0d0db;line-height:17px;height: 36px;overflow: hidden;">
											<span t-field="doc.sudo().customer_address" />
										</div>
									</td>
								</tr>
								<tr>
									<td colspan="2" style="text-align:left">
										<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
											<span t-field="doc.sudo().customer_mobile" />
										</div>
									</td>
								</tr>
								<tr>
									<td colspan="2" style="text-align:left">
										<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
											Money collection cost:
											<t t-if="doc.sudo().assign_to_business.inclusive_delivery and not doc.sudo().delivery_cost_on_sender" t-set="total" t-value="doc.sudo().copy_total_cost" />
											<t t-else="else">
												<t t-if="doc.sudo().assign_to_business.inclusive_delivery and doc.sudo().delivery_cost_on_sender" t-set="total" t-value="doc.sudo().copy_total_cost - doc.sudo().delivery_cost" />
												<t t-else="else">
													<t t-if="not doc.sudo().assign_to_business.inclusive_delivery and doc.sudo().delivery_cost_on_sender" t-set="total" t-value="doc.sudo().cost" />
													<t t-else="else" t-set="total" t-value="doc.sudo().cost + doc.sudo().delivery_cost" />
												</t>
											</t>
											<t t-esc="total" />
										</div>
									</td>
								</tr>
							</tbody>
						</div>
					</fieldset>
					<div class="row" style="max-height: 200px;max-wight: 200px;">
						<div class="col align-self-center">
							<img style="width:350px !important;height:60px;display:block !important;margin:0 auto !important" t-attf-src="data:image/*;base64,{{doc.sudo().barcode}}" />
							<h5 class="text-center" style="font-size:0.7em !important;font-weight:bold;margin-bottom:0px !important">
								<span t-field="doc.sudo().sequence" />
							</h5>
						</div>
					</div>
					<div class="row" style="font-size: 10px !important;">
						<div class="col align-self-center">
								<span t-if="company.report_footer" style="display: inline-block; max-height: 2.0em; overflow: hidden; line-height: 1em; word-wrap: break-word; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical;"
                                    t-esc="company.report_footer" />
						</div>
					</div>
				</div>
			</xpath>
			<xpath t-if="len(doc.sudo().order_ids)>0" expr="//div[@class='page']" position="after">
				<t t-set="counter" t-value="0" />
				<t t-foreach="doc.sudo().order_ids.sorted(key=lambda b: b.sequence)" t-as="compound_order">
					<t t-set="counter" t-value="counter+1" />
					<div class="page2" style="page-break-after:always;margin-bottom:0px !important">
						<div class="row" style="margin: 0 0 0 0;height:50px">
							<div class="col-6" style="padding-right: 0px; padding-left: 0px">
								<img class="d-flex justify-content-start" t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="height: 50px;max-wight: 100px;" />
							</div>
							<div class="col-6" style="margin-top:9px;padding-right: 0px; padding-left: 0px">
								<h5 style="font-size:1em;font-weight:bold;margin:0 auto !important;">

								<span style="color:black" t-field="company.company_registry" />
								&#160;&#160;
									<t t-set="count" t-value="len(doc.sudo().order_ids)" />
									<t t-esc="counter" />
									/
									<t t-esc="count" />


								</h5>
								<h5 style="font-size:0.9em !important;margin-bottom:0px !important">
									Pickup Date:
									<span style="font-size:0.9em" t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')" />
								</h5>
							</div>
						</div>
						<div class="row" style="margin: 0 0 10px 0;">
							<div class="col-6" style="height:75px;padding-right: 0px; padding-left: 0px;margin-bottom:10px">
								<fieldset class="scheduler-border" style="height:75px;border: 1px groove #ddd !important;padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
									<legend style="width:auto;border-bottom:none;font-weight: bold;font-size:14px;color:black;text-align: center;">
										Sender's details
									</legend>
									<div class="control-group">
										<tbody>
											<tr>
												<td colspan="2" style="text-align:left;">
													<div style="font-size:20px;padding: 5px 5px 5px 5px">
														<t t-if="compound_order.show_follower_info_in_waybill">
															<t t-if="compound_order.show_follower_info">
																<t t-if="compound_order.follower_store_name">
																	<span t-field="compound_order.follower_store_name" />
																</t>
																<t t-else="else">
																	<t t-if="compound_order.sudo().assign_to_business.commercial_name">
																		<span t-field="compound_order.sudo().assign_to_business.commercial_name" style="font-size:15px" />
																	</t>
																	<t t-else="else">
																		<span t-field="compound_order.sudo().assign_to_business" style="font-size:15px" />
																	</t>
																</t>
															</t>
															<t t-else="else">
																<t t-if="compound_order.sudo().assign_to_business.commercial_name" style="font-size:15px">
																	<span t-field="compound_order.sudo().assign_to_business.commercial_name" style="font-size:15px" />
																</t>
																<t t-else="else">
																	<span t-field="compound_order.sudo().assign_to_business" style="font-size:15px" />
																</t>
															</t>
														</t>
														<t t-else="else">
															<t t-if="compound_order.sudo().assign_to_business.commercial_name" style="font-size:15px">
																<span t-field="compound_order.sudo().assign_to_business.commercial_name" style="font-size:15px" />
															</t>
															<t t-else="else">
																<span t-field="compound_order.sudo().assign_to_business" style="font-size:15px" />
															</t>
														</t>
													</div>
												</td>
											</tr>
										</tbody>
									</div>
								</fieldset>
							</div>
							<div class="col-6" style="height:75px;padding-right: 0px; padding-left: 0px;margin-bottom:10px">
								<fieldset class="scheduler-auto" style="height:75px;border: 1px groove #ddd !important; padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
									<legend style="width:auto;border-bottom:none;color:black;text-align: center;font-size:14px;font-weight: bold;">
										Notes
									</legend>
									<div class="control-group">
										<tbody>
											<tr>
												<td>
													<span style="font-size:20px;line-height:20px;height: 40px;overflow: hidden;display: inline-block;" t-field="compound_order.note" />
													<br />
												</td>
											</tr>
										</tbody>
									</div>
								</fieldset>
							</div>
						</div>
						<fieldset class="scheduler-border" style="border: 1px groove #ddd !important; padding: 0 0.1em 0.7em 0.1em !important; margin: 0 0 10px 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;height: 180px;max-wight: 200px;">
							<legend style="width:auto;border-bottom:none;font-size:14px;color:black;text-align: center;font-weight: bold;">
								Recipient's details
							</legend>
							<div class="control-group">
								<tbody>
									<tr>
										<td colspan="2" style="text-align:left ">
											<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
												<span t-field="compound_order.customer_name" />
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="2" style="text-align:left">
											<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
												<span t-field="compound_order.customer_area" />
												<span t-if="compound_order.customer_sub_area">
													/
												</span>
												<span t-if="compound_order.customer_sub_area" t-field="compound_order.customer_sub_area" />
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="2" style="text-align:left">
											<div style="font-size:16px;border-bottom: 1px solid #d0d0db;line-height:17px;height: 36px;overflow: hidden;">
												<span t-field="compound_order.customer_address" />
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="2" style="text-align:left">
											<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
												<span t-field="compound_order.customer_mobile" />
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="2" style="text-align:left">
											<div style="font-size:18px;border-bottom: 1px solid #d0d0db">
												Money collection cost:
												<t t-if="compound_order.sudo().assign_to_business.inclusive_delivery and not compound_order.delivery_cost_on_sender" t-set="total" t-value="compound_order.copy_total_cost" />
												<t t-else="else">
													<t t-if="compound_order.sudo().assign_to_business.inclusive_delivery and compound_order.delivery_cost_on_sender" t-set="total" t-value="compound_order.copy_total_cost - compound_order.delivery_cost" />
													<t t-else="else">
														<t t-if="not compound_order.sudo().assign_to_business.inclusive_delivery and compound_order.delivery_cost_on_sender" t-set="total" t-value="compound_order.cost" />
														<t t-else="else" t-set="total" t-value="compound_order.cost + compound_order.delivery_cost" />
													</t>
												</t>
												<t t-esc="total" />
											</div>
										</td>
									</tr>
								</tbody>
							</div>
						</fieldset>
						<div class="row" style="max-height: 200px;max-wight: 200px;">
							<div class="col align-self-center">
								<img style="width:350px !important;height:60px;display:block !important;margin:0 auto !important" t-attf-src="data:image/*;base64,{{compound_order.barcode}}" />
								<h5 class="text-center" style="font-size:0.7em !important;font-weight:bold;margin-bottom:0px !important">
									<span t-field="compound_order.sequence" />
								</h5>
							</div>
						</div>
					<div class="row" style="font-size: 10px !important;">
						<div class="col align-self-center">
								<span t-if="company.report_footer" style="display: inline-block; max-height: 2.0em; overflow: hidden; line-height: 1em; word-wrap: break-word; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical;"
                                    t-esc="company.report_footer" />
						</div>
					</div>
					</div>
				</t>
			</xpath>
		</template>
		<record id="olivery_templates.report_rb_delivery_order_detail_10x10_action" model="ir.actions.report">
			<field name="paperformat_id" ref="olivery_compound_order.paperformat_order_detail_compound_10x10_waybill"/>
		</record>
	</data>
</odoo>
