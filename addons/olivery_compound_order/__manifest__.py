# -*- coding: utf-8 -*-
{
    'name': "olivery_compound_orders",
    'summary': """
        Olivery Compound Orders App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': '1.0.15',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery', 'olivery_templates'] ,

    # always loaded
    'data': [
        'demo/order_type.xml',
        'demo/status.xml',
        # 'models/company/company_view.xml',
        'models/order/order_view.xml',
        # 'models/area/area_view.xml',
        'views/module_view.xml',
        'views/10x10_waybill.xml',
        'views/run_sheet.xml',
        'demo/client_conf.xml'
        

    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
        'uninstall_hook': 'uninstall_hook',

}