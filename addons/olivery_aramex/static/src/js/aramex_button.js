odoo.define('rb_delivery.action_aramex_button', function (require) {

    "use strict";
    
    var core = require('web.core');
    var ListController = require('web.ListController');
    var rpc = require('web.rpc');

    ListController.include({
       renderButtons: function($node) {
       this._super.apply(this, arguments);
           if (this.$buttons) {
             this.$buttons.find('.oe_action_button_refresh_aramex_orders').click(this.proxy('refresh_aramex_orders'));
             }
       },
       refresh_aramex_orders:function(){
        rpc.query({
            model: 'rb_delivery.order',
            method: 'retrieve_aramex_status',
            args: [[]]
        }).then(function (data){
            if(data){
                return true
            }
        })

       }

       
})
})
