# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request

import json
import logging

from openerp.http import request, Response

from openerp import http
from openerp.addons.auth_signup.controllers.main import AuthSignupHome
from openerp.addons.web.controllers.main import ensure_db


_logger = logging.getLogger(__name__)

# class WebInherit(AuthSignupHome):
#     @http.route()
#     def web_login(self, *args, **kw):
#         ensure_db()
#         response = super(WebInherit, self).web_login(*args, **kw)
#         response.qcontext.update({'redirect': '/web#action=115&model=rb_delivery.order&view_type=list&menu_id=83'})

#         return response
