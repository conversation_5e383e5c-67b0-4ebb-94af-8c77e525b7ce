<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="ir_cron_retrieve_aramex_status" model="ir.cron">
            <field name="name">Retrieve Aramex Status</field>
            <field name="interval_number">5</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field ref="model_rb_delivery_order" name="model_id"/>
            <field name="state">code</field>
            <field name="code">model.retrieve_aramex_status()</field>
        </record>

        

    </data>
</odoo>
