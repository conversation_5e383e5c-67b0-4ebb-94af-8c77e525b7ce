# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api, _
from openerp.exceptions import ValidationError
import time
from datetime import datetime, timedelta
import requests
import json
import os
import re
_logger = logging.getLogger(__name__)


class olivery_aramex_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------




    shipping_date = fields.Date('Shipping Date',track_visibility="on_change")

    due_date = fields.Date('Due date',track_visibility="on_change")

    aramex_status = fields.Char("Aramex Status", track_visibility="on_change")

    is_aramex_order = fields.Boolean("Is Aramex Order", default=False)
        
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    def retrieve_aramex_status(self):
        
        orders = self.env['rb_delivery.order'].sudo().search_read([('is_aramex_order','=',True)],['reference_id'])
        ref_ids=[]
        for order in orders:
            ref_ids.append(order['reference_id'])
        if len(ref_ids)>0:
            url = "https://ws.aramex.net/ShippingAPI.V2/Tracking/Service_1_0.svc/json/TrackShipments"

            company = self.env['res.company'].sudo().search([])[0]
            payload = json.dumps({
            "ClientInfo": {
                "UserName": company.aramex_username,
                "Password": company.aramex_password,
                "Version": "v1",
                "AccountNumber": company.account_number,
                "AccountPin": company.account_pin,
                "AccountEntity": company.account_entity,
                "AccountCountryCode": company.account_country_code,
                "Source": 20
            },
            "GetLastTrackingUpdateOnly": False,
            "Shipments": ref_ids,
            "Transaction": {
                "Reference1": "",
                "Reference2": "",
                "Reference3": "",
                "Reference4": "",
                "Reference5": ""
            }
            })
            headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)    
            if response.status_code == 200:
                json_response = response.json()
                if 'TrackingResults' in json_response and json_response['TrackingResults']:
                    tracking_results = json_response['TrackingResults']
                    for tracking_result in tracking_results:
                        order_ref = tracking_result["Key"]
                        order_to_write = self.env['rb_delivery.order'].sudo().search([('reference_id','=',order_ref)])
                        if 'Value' in tracking_result and tracking_result['Value']:
                            value = tracking_result['Value'][0]
                            if 'UpdateDescription' in value and value['UpdateDescription']:
                                order_to_write.aramex_status = value['UpdateDescription']
            return True                    
    
    def send_order_to_aramex(self,next_state):
        customer_area = ""
        second_mobile = ""
        due_date = ""
        shipping_date = ""  
        
        if self.customer_area and self.customer_area.english_name:
            customer_area = self.customer_area.english_name
        if self.second_mobile_number:
            second_mobile = self.second_mobile_number   
        if self.due_date and self.shipping_date:
            due_date = datetime.strptime(self.due_date.strftime("%d.%m.%Y %H:%M:%S,%f"),'%d.%m.%Y %H:%M:%S,%f')    
            due_date = due_date.timestamp() * 1000
            shipping_date = datetime.strptime(self.shipping_date.strftime("%d.%m.%Y %H:%M:%S,%f"),'%d.%m.%Y %H:%M:%S,%f')    
            shipping_date = shipping_date.timestamp() * 1000
            due_date = "/Date(" + str(due_date).replace('.0','') + ")/"
            shipping_date = "/Date(" + str(shipping_date).replace('.0','') + ")/"   
        cost = self.money_collection_cost
        if self.delivery_cost_on_sender:
            if self.service:
                for i in self.service:
                    cost = cost - i.cost    
            
        url = "https://ws.aramex.net/ShippingAPI.V2/Shipping/Service_1_0.svc/json/CreateShipments"
        # remove comment if you want to test on localhost
        # os.environ['NO_PROXY'] = 'localhost:8078'
        company = self.env['res.company'].sudo().search([])[0]
        payload = json.dumps({
            "ClientInfo": {
                "UserName": company.aramex_username,
                "Password": company.aramex_password,
                "Version": "v1",
                "AccountNumber": company.account_number,
                "AccountPin": company.account_pin,
                "AccountEntity": company.account_entity,
                "AccountCountryCode": company.account_country_code,
                "Source": 20
                },
            "LabelInfo": None,
            "Shipments": [{
                "Reference1": self.sequence,
                "Reference2": "",
                "Reference3": "",
                "Shipper": {
                    "Reference1": "",
                    "Reference2": "",
                    "AccountNumber": company.account_number,
                    "PartyAddress": {
                        "Line1": self.assign_to_business.address,
                        "Line2": "",
                        "Line3": "",
                        "City": self.assign_to_business.area_id.english_name,
                        "StateOrProvinceCode": "",
                        "PostCode": "",
                        "CountryCode": company.account_country_code,
                        "Longitude": 0,
                        "Latitude": 0,
                        "BuildingNumber": None,
                        "BuildingName": None,
                        "Floor": None,
                        "Apartment": None,
                        "POBox": None,
                        "Description": None},
                    "Contact": {
                        "Department": "",
                        "PersonName": self.assign_to_business.username,
                        "Title": "",
                        "CompanyName": self.assign_to_business.commercial_name,
                        "PhoneNumber1": self.assign_to_business.mobile_number,
                        "PhoneNumber1Ext": "",
                        "PhoneNumber2": "",
                        "PhoneNumber2Ext": "",
                        "FaxNumber": "",
                        "CellPhone": self.assign_to_business.mobile_number,
                        "EmailAddress": self.assign_to_business.email,
                        "Type": ""}},
                "Consignee": {
                    "Reference1": "",
                    "Reference2": "",
                    "AccountNumber": "",
                    "PartyAddress": {
                        "Line1": self.customer_address,
                        "Line2": "",
                        "Line3": "",
                        "City": customer_area,
                        "StateOrProvinceCode": "",
                        "PostCode": "",
                        "CountryCode": company.account_country_code,
                        "Longitude": 0,
                        "Latitude": 0,
                        "BuildingNumber": "",
                        "BuildingName": "",
                        "Floor": "",
                        "Apartment": "",
                        "POBox": None,
                        "Description": ""},
                    "Contact": {
                        "Department": "",
                        "PersonName": self.customer_name,
                        "Title": "",
                        "CompanyName": self.customer_name,
                        "PhoneNumber1": self.customer_mobile,
                        "PhoneNumber1Ext": "",
                        "PhoneNumber2": second_mobile,
                        "PhoneNumber2Ext": "",
                        "FaxNumber": "",
                        "CellPhone": self.customer_mobile,
                        "EmailAddress": "",
                        "Type": ""}},
                "ShippingDateTime": shipping_date,
                "DueDate": due_date,
                "Comments": "",
                "PickupLocation": "",
                "OperationsInstructions": "",
                "AccountingInstrcutions": "",
                "Details": {
                    "Dimensions": None,
                    "ActualWeight": {
                        "Unit": "KG",
                        "Value": 0.5},
                    "ChargeableWeight": None,
                    "DescriptionOfGoods": self.product_note,
                    "GoodsOriginCountry": company.account_country_code,
                    "NumberOfPieces": 1,
                    "ProductGroup": "DOM",
                    "ProductType": "ONP",
                    "PaymentType": "P",
                    "PaymentOptions": "Cash",
                    "CustomsValueAmount": {"CurrencyCode": "SAR","Value": cost},
                    "CashOnDeliveryAmount": {"CurrencyCode": "SAR","Value": cost},
                    "InsuranceAmount": None,
                    "CashAdditionalAmount": None,
                    "CashAdditionalAmountDescription": "",
                    "CollectAmount": None,
                    "Services": "CODS",
                    "Items": []},
            "Attachments": [],
            "ForeignHAWB": "",
            "TransportType ": 0,
            "PickupGUID": "",
            "Number": None,
            "ScheduledDelivery": None}],
            "Transaction": {
            "Reference1": "",
            "Reference2": "",
            "Reference3": "",
            "Reference4": "",
            "Reference5": ""}})
        headers = {'Content-Type': 'application/json','Accept': 'application/json'}
        response = requests.request("POST", url, headers=headers, data=payload)
        
        if response.status_code == 200:
            json_response = response.json()
            if json_response.get('Shipments')[0].get("ID"):
                self.reference_id = json_response.get('Shipments')[0].get("ID")
                self.is_aramex_order = True
                rec = self.env['rb_delivery.order'].browse(self.id)  
                rec.message_post(body=_("Order was updated to aramex with reference id " + json_response.get('Shipments')[0].get("ID")))
                return True
            else: 
                rec = self.env['rb_delivery.order'].browse(self.id)  
                rec.message_post(body=_("Order was not updated in aramex"))       
        else:
            rec = self.env['rb_delivery.order'].browse(self.id)  
            rec.message_post(body=_("Order was not updated in aramex"))
  
            return False

    def client_do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            from_to_jeddah = False
            from_to_riyadh = False
            if self.customer_area.code == '001' and self.assign_to_business.area_id.code == '001':
                from_to_jeddah = True
            if self.customer_area.code == '003' and self.assign_to_business.area_id.code == '003':
                from_to_riyadh = True    
            if action.name=='send_order_to_aramex' and not from_to_jeddah and not from_to_riyadh:
                
                try:
                    method_to_call=getattr(olivery_aramex_order,action.name)
                    method_to_call(self,next_state)
                except:
                    pass        


          

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    @api.one
    def write(self,values):
        if 'state' in values and values['state']:
            self.client_do_action(values['state']) 

        return super(olivery_aramex_order, self).write(values) 

    @api.model
    def create(self,values):
        if 'customer_address' not in values or values['customer_address'] == False:
            area_name = self.env['rb_delivery.area'].search_read([('id','=',values['customer_area'])],['name'])
            values['customer_address'] = area_name[0]['name']
        if 'shipping_date' not in values or values['shipping_date'] == False and 'due_date' not in values or values['due_date'] == False:
            values['shipping_date']= datetime.today()   
            values['due_date'] = datetime.today() + timedelta(days=3)   
        if 'state' in values and values['state']:
            self.client_do_action(values['state']) 

        return super(olivery_aramex_order, self).create(values)     

        

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
