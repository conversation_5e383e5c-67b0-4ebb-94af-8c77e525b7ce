# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_aramex_company(models.Model):

    _inherit = 'res.company'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    aramex_username = fields.Char('Aramex Username',track_visibility="on_change")

    aramex_password = fields.Char('Aramex Password',track_visibility="on_change")

    account_number = fields.Char('Aramex Account Number',track_visibility="on_change")

    account_pin = fields.Char('Aramex account PIN',track_visibility="on_change")

    account_entity = fields.Char("Aramex Account Entity",track_visibility="on_change")

    account_country_code = fields.Char("Account Country Code", track_visibility="on_change")

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------                      

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ---------------------------------------------------------- 


    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------