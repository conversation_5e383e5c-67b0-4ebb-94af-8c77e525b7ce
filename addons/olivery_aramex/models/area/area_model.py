# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_client_area(models.Model):

    _inherit = 'rb_delivery.area'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    english_name = fields.Char('English Name')
    
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
