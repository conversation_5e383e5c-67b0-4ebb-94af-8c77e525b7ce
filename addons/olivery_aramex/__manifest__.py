# -*- coding: utf-8 -*-
{
    'name': "olivery_aramex",
    'summary': """
        Olivery Aramex Integration App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': '1.0.2',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'models/company/company_view.xml',
        'models/order/order_view.xml',
        'models/area/area_view.xml',
        'views/aramex_cron_job.xml',
        'demo/status.xml',

    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
}