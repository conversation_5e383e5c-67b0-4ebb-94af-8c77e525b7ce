# -*- coding: utf-8 -*-
{
    'name': "olivery_delivery_repetition",
    'summary': """
        Olivery Delivery Repetition App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.17',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'demo/client_conf.xml',
        'models/delivery_attempts/delivery_attempts_view.xml',
        'security/ir.model.access.csv',
        'models/order/order_view.xml',
        'views/module_view.xml',
        'security/delivery_repetition_security.xml'
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
