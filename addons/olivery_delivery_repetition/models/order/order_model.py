# -*- coding: utf-8 -*-
from datetime import date,datetime
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError


class olivery_repeating_delivery_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
    
    stuck_count = fields.Integer(string='Stuck Count',track_visibility="on_change",readonly=True)

    returned_to_branch_count = fields.Integer(string='Returned to branch Count',track_visibility="on_change",readonly=True)
    
    delivery_attempts = fields.One2many('rb_delivery.delivery_attempts',string='Delivery Attempts',inverse_name="order_id", readonly=True)
    
    first_delivery_attempt = fields.Many2one('rb_delivery.delivery_attempts',string='First Delivery Attempts', readonly=True)
    
    first_delivery_attempt_date = fields.Datetime('First Delivery Attempt Date', readonly=True,related="first_delivery_attempt.delivery_attempt_date",store=True)

    number_of_deliveries = fields.Integer('Number of Deliveries',compute='_compute_number_of_delivery',store=True)
    
    def filter_values(self,values):
        self.SUDO_FIELDS = self.SUDO_FIELDS + ['first_delivery_attempt','delivery_attempts']

        return super(olivery_repeating_delivery_order, self).filter_values(values)
    
    def create_delivery_attempts(self,values):
        delivery_attept_values = {}
        if self.assign_to_business:
            delivery_attept_values['business'] = self.assign_to_business.id
        if values.get('stuck_comment'):
            delivery_attept_values['stuck_comment'] = values['stuck_comment']
        if values.get('state'):
            delivery_attept_values['status'] = values['state']
        if values.get('assign_to_agent'):
            delivery_attept_values['driver'] = values['assign_to_agent']
        if values.get('reject_reason'):
            delivery_attept_values['reject_reason'] = values['reject_reason']
        fmt = "%Y-%m-%d %H:%M:%S"
        delivery_attept_values['delivery_attempt_date'] = datetime.strftime(datetime.today(), fmt)
        delivery_attept_values['order_id'] = self.id
        if 'dirver' not in delivery_attept_values:
            delivery_attept_values['driver'] = self.assign_to_agent.id
        delivery_attempt_id = self.env['rb_delivery.delivery_attempts'].create(delivery_attept_values)
        if len(self.delivery_attempts) == 1:
            self.write({'first_delivery_attempt':delivery_attempt_id.id})
    
    @api.multi
    @api.depends('delivery_attempts')
    def _compute_number_of_delivery(self):
        for record in self:
            record.number_of_deliveries = len(record.delivery_attempts)
    @api.multi
    def write(self, values):
        
        if 'state' not in values or not values['state']:
            return super(olivery_repeating_delivery_order, self).write(values)
        
        repetition_conf=self.env['rb_delivery.client_configuration'].get_param(['consider_order_as_delivery_attempts','delivery_repetition_threshold','delivery_repetition_status'])
        delivery_attempts_state_ids=repetition_conf['consider_order_as_delivery_attempts']
        delivery_repetition_threshold=int(repetition_conf['delivery_repetition_threshold'] or 0)
        delivery_repetition_status = repetition_conf['delivery_repetition_status']
        if not delivery_attempts_state_ids or not delivery_repetition_threshold or not delivery_repetition_status:
            return super(olivery_repeating_delivery_order, self).write(values)


        delivery_attempts_statuses = self.env['rb_delivery.status'].browse(
            delivery_attempts_state_ids
        ).mapped('name')

        update_orders = self.env['rb_delivery.order']
        move_orders = self.env['rb_delivery.order']

        for order in self:
            if values['state'] in delivery_attempts_statuses:
                if values['state'] in delivery_attempts_statuses and order.state != values['state']:
                    order.create_delivery_attempts(values)
                if len(order.delivery_attempts) >= delivery_repetition_threshold:
                    move_orders |= order
                else:
                    update_orders |= order
            else:
                update_orders |= order

        
        if update_orders:
            res = super(olivery_repeating_delivery_order, update_orders).write(values)
        else:
            res = True

        if move_orders:
            move_orders.update_status_order_one_by_one(delivery_repetition_status)

        return res

    def update_status_order_one_by_one(self,delivery_repetition_status):

        state = self.env['rb_delivery.status'].sudo().browse(delivery_repetition_status[0]) if len(delivery_repetition_status) == 1 else None

        if not state:
            return

        values_arr = []
        values_copy = {'state': state.name}
        values_arr = [values_copy] * len(self)

        self.with_delay(channel="root.basic", max_retries=2).write_jq(values_arr, self, self._context)
    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------