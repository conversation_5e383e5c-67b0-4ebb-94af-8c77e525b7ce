<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="1">

        <record id="delivery_repetition_business_see_only_his_records" model="ir.rule">
            <field name="name">Business see only his record for Delivery attempts</field>
            <field name="model_id" ref="model_rb_delivery_delivery_attempts"/>
            <field name="domain_force">[('business.user_id','=',user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
        </record>

        <record id="delivery_repetition_driver_see_only_his_records" model="ir.rule">
            <field name="name">Driver see only his record for Delivery attempts</field>
            <field name="model_id" ref="model_rb_delivery_delivery_attempts"/>
            <field name="domain_force">[('driver.user_id','=',user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
        </record>
  </data>
</odoo>
