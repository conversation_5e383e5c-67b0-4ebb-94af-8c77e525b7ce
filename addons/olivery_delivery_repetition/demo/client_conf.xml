<odoo >
    <data noupdate="1">

        <record id="client_configuration_type_olivery_repetition" model="rb_delivery.client_configuration_type">
            <field name="name">Olivery Delivery Repetition</field>
            <field name="description">Any thing related to Olivery Repetition</field>
        </record> 

        <!-- order detail coniguration -->
        <record id="client_configuration_consider_order_as_delivery_attempts" model="rb_delivery.client_configuration">
            <field name="key">consider_order_as_delivery_attempts</field>
            <field name="value">False</field>
            <field name="description">the statuses that are related to operation orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_progress')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="olivery_delivery_repetition.client_configuration_type_olivery_repetition"/>
        </record>
               
        <record id="client_configuration_repetition_threshold" model="rb_delivery.client_configuration">
            <field name="key">delivery_repetition_threshold</field>
            <field name="value">True</field>
            <field name="text">3</field>
            <field name="description">Maximum number of delivery attempts allowed before changing the order status.</field>
            <field name="related_to_text">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="olivery_delivery_repetition.client_configuration_type_olivery_repetition"/>
        </record>

        <record id="client_configuration_repetition_status" model="rb_delivery.client_configuration">
            <field name="key">delivery_repetition_status</field>
            <field name="description">The status to assign when the delivery repetition threshold is reached.</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_canceled')])]" />
            <field name="related_to_text">False</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="olivery_delivery_repetition.client_configuration_type_olivery_repetition"/>
        </record>
    </data>
</odoo>