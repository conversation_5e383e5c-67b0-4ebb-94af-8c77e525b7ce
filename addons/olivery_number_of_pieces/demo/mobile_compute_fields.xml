<odoo>
    <data>
        <record id="compute_compute_number_of_orders" model="rb_delivery.mobile_compute_functions">
            <field name="name">compute_number_of_orders</field>
            <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
            <field name="fields" eval="[(6,0,[ref('olivery_number_of_pieces.field_rb_delivery_order__no_of_pieces')])]" />
        </record>
        <record id="rb_delivery.compute_delivery_fee" model="rb_delivery.mobile_compute_functions">
            <field name="name">compute_delivery_fee</field>
            <field name="model" ref="rb_delivery.model_rb_delivery_order"/>
            <field name="fields" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_order__customer_area'),ref('rb_delivery.field_rb_delivery_order__customer_sub_area'),ref('rb_delivery.field_rb_delivery_order__order_type_id'),ref('rb_delivery.field_rb_delivery_order__assign_to_business'),ref('rb_delivery.field_rb_delivery_order__extra_cost'),ref('rb_delivery.field_rb_delivery_order__discount'),ref('olivery_number_of_pieces.field_rb_delivery_order__no_of_orders')])]" />
        </record>
    </data>
</odoo>