# -*- coding: utf-8 -*-
{
    'name': "olivery_number_of_pieces",
    'summary': """
        Olivery Number Of pieces App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.24',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery' , 'olivery_templates'],

    # always loaded
    'data': [
        'models/pricelist_item/pricelist_item_view.xml',
        'models/pricelist/pricelist_view.xml',
        'models/order/order_view.xml',
        'views/print/waybill_a4.xml',
        'views/print/waybill_a5.xml',
        'demo/client_config.xml',
        'demo/mobile_compute_fields.xml',
        'views/print/waybill_75X100.xml',
        'views/print/10x10_waybill.xml',
        'views/print/money_collection_with_no_of_item.xml'

    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
