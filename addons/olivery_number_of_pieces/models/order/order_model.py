# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, fields, api
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_number_of_pieces_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    no_of_orders = fields.Integer('No of orders', compute="get_no_of_orders")

    no_of_pieces = fields.Integer('Number of pieces',track_visibility="on_change")

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    @api.depends('no_of_pieces')
    @api.one
    def get_no_of_orders(self):
        no_of_items = int(self.no_of_pieces)
        data = {
            'sender_id': self.assign_to_business.id,
            'to_area_id': self.customer_area.id,
            'order_type_id': self.order_type_id.id,
            'sub_area_id':self.customer_sub_area.id}
        no_of_items_per_order_info = self.env['rb_delivery.pricelist'].sudo().get_no_of_items(data)
        if 'no_of_items' in no_of_items_per_order_info and no_of_items_per_order_info['no_of_items']:
            no_of_items_per_order = no_of_items_per_order_info['no_of_items']
            if no_of_items > no_of_items_per_order:
                # get number of unites above order weight
                import math
                no_of_orders=math.ceil(no_of_items/no_of_items_per_order)
                self.no_of_orders = no_of_orders
            else:
                self.no_of_orders = 1


    @api.depends('customer_area', 'assign_to_business', 'extra_cost', 'order_type_id', 'discount','customer_sub_area', 'service','no_of_orders')
    @api.one
    def get_customer_price(self):
        res = super(olivery_number_of_pieces_order, self).get_customer_price()
        if self.no_of_orders > 1:
            self.delivery_cost = (self.delivery_cost * self.no_of_orders)
        return res

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------


    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------