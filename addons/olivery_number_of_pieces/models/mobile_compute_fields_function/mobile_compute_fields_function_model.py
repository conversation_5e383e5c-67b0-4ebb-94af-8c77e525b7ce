from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class olivery_number_of_pieces_mobile_compute_fields_function(models.Model):

    _inherit = 'rb_delivery.mobile_compute_functions'

    def call_function(self,input,formValues):
        try:
            method_to_call=getattr(olivery_number_of_pieces_mobile_compute_fields_function,input['compute_function_name'])
            return method_to_call(self,formValues,input)
        except:
            return super(olivery_number_of_pieces_mobile_compute_fields_function,self).call_function(input,formValues)


    @api.model
    def compute_number_of_orders(self, values, input):
        if 'no_of_pieces' in values:
            no_of_items = int(values['no_of_pieces'])
            customer_area = values.get('customer_area', False)
            customer_sub_area = values.get('customer_sub_area', False)
            assign_to_business = values.get('assign_to_business', False)
            order_type_id = values.get('order_type_id', False)
            if not assign_to_business or not customer_area or not order_type_id:
                return input
            data = {
                'sender_id': assign_to_business,
                'to_area_id': customer_area,
                'order_type_id': order_type_id,
                'sub_area_id': customer_sub_area
            }
            no_of_items_per_order_info = self.env['rb_delivery.pricelist'].sudo().get_no_of_items(data)
            if 'no_of_items' in no_of_items_per_order_info and no_of_items_per_order_info['no_of_items']:
                no_of_items_per_order = no_of_items_per_order_info['no_of_items']
                if no_of_items > no_of_items_per_order:
                    import math
                    no_of_orders = math.ceil(no_of_items / no_of_items_per_order)
                    input['value'] = no_of_orders
                else:
                    input['value'] = 1
        return input

    @api.model
    def compute_delivery_fee(self,values,input):
        input = super(olivery_number_of_pieces_mobile_compute_fields_function,self).compute_delivery_fee(values,input)
        if 'no_of_orders' in values and int(values['no_of_orders']) > 1:
            input['value'] = (int(values['no_of_orders']) * input['value'])
        return input