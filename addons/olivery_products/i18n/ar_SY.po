# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_products
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-24 13:03+0000\n"
"PO-Revision-Date: 2025-04-24 13:03+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_products
#: code:addons/olivery_products/models/product_line/product_line.py:47
#, python-format
msgid "would go negative (current %d, trying to remove %d)"
msgstr "هنالك شيئ خاطئ (الحالي %d, جرب ان تحذف %d)"

#. module: olivery_products
#: model_terms:ir.ui.view,arch_db:olivery_products.view_form_rb_delivery_order_button_products
msgid "<span>Products:</span>"
msgstr "المنتجات"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__amount
msgid "Amount"
msgstr "الكمية"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__attachment_ids
#: model_terms:ir.ui.view,arch_db:olivery_products.olivery_product_product_form_view
msgid "Attachments"
msgstr "المرفقات"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__create_uid
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__create_uid
msgid "Created by"
msgstr "انشئ من خلال "

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__create_date
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__create_date
msgid "Created on"
msgstr "انشئ في "

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__delivery_cost_product
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__delivery_cost_product
msgid "Delivery Cost"
msgstr "تكلفة التوصيل"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_rb_delivery_order__delivery_cost_total
msgid "Delivery Cost (total)"
msgstr "تكلفة التوصيل الكلية"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_rb_delivery_order__delivery_cost
msgid "Delivery Fee"
msgstr "تكلفة التوصيل"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__description
msgid "Description"
msgstr "الوصف"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__display_name
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__display_name
msgid "Display Name"
msgstr "الاسم"


#. module: olivery_products
#: model:ir.model.fields,help:olivery_products.field_olivery_product_product__attachment_ids
msgid "Files attached to this product"
msgstr "الملفات المرفقة لهذا المنتج"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__id
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__id
msgid "ID"
msgstr "الهوية"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product____last_update
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line____last_update
msgid "Last Modified on"
msgstr "اخر تعديل"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__write_uid
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__write_uid
msgid "Last Updated by"
msgstr "اخر تحديث عن طريق"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__write_date
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__write_date
msgid "Last Updated on"
msgstr "اخر تحديث على"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__name
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__name
msgid "Name"
msgstr "الاسم"

#. module: olivery_products
#: model:ir.model,name:olivery_products.model_olivery_product_product
msgid "Olivery Product"
msgstr ""

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__order_id
msgid "Order"
msgstr "الطلب"

#. module: olivery_products
#: model:ir.model,name:olivery_products.model_rb_delivery_order
msgid "Order Model"
msgstr ""

#. module: olivery_products
#: model:ir.model,name:olivery_products.model_olivery_product_product_line
msgid "Order Product Line"
msgstr ""

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__price
msgid "Price"
msgstr "السعر"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__product_id
#: model_terms:ir.ui.view,arch_db:olivery_products.olivery_product_product_form_view
msgid "Product"
msgstr "المنتج"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_rb_delivery_order__product_counter
msgid "Products Count"
msgstr "عدد المنتج"

#. module: olivery_products
#: model_terms:ir.ui.view,arch_db:olivery_products.rb_delivery_order_tab_products
msgid "Product Line"
msgstr "خط المنتجات"

#. module: olivery_products
#: model:ir.actions.act_window,name:olivery_products.action_olivery_product_product
#: model:ir.model.fields,field_description:olivery_products.field_rb_delivery_order__product_line_ids
#: model:ir.ui.menu,name:olivery_products.menu_olivery_product_product
#: model_terms:ir.ui.view,arch_db:olivery_products.rb_delivery_order_tab_products
msgid "Products"
msgstr "المنتجات"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_rb_delivery_order__amount_total_products
msgid "Products Total"
msgstr "المنتجات الكلية"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__qty
msgid "Quantity"
msgstr "الكمية"

#. module: olivery_products
#: code:addons/olivery_products/models/product_line/product_line.py:36
#, python-format
msgid "Quantity cannot be negative."
msgstr "القيمة لا يمكن ان تكون سالبة"

#. module: olivery_products
#: model:ir.model.fields,help:olivery_products.field_olivery_product_product__amount
msgid "Quantity in stock"
msgstr ""

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__reference_id
msgid "Reference id"
msgstr "المعرف"

#. module: olivery_products
#: model_terms:ir.ui.view,arch_db:olivery_products.view_search_olivery_product_product
msgid "Search Products"
msgstr "بحث عن منتجات"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__sequence
msgid "Sequence"
msgstr "الرقم التسلسلي"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__subtotal
msgid "Subtotal"
msgstr "المجموع"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__assign_to_business
msgid "Business"
msgstr "التاجر"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product_line__price_unit
msgid "Unit Price"
msgstr "سعر الواحدة"

#. module: olivery_products
#: model:ir.model.fields,help:olivery_products.field_olivery_product_product__price
#: model:ir.model.fields,help:olivery_products.field_olivery_product_product_line__price_unit
msgid "Unit price"
msgstr "سعر الواحدة"

#. module: olivery_products
#: model:ir.model.fields,field_description:olivery_products.field_olivery_product_product__sequence
msgid "sequence"
msgstr "الرقم التسلسل"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:46
#: code:addons/rb_delivery/models/error_log/error_log_model.py:46
#, python-format
msgid "Error while updating order"
msgstr "خطا اثناء تعديل الطلب"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "quantity entered does not match  what's available in stock."
msgstr "الكمية التي ادخلتها لا تتوافق مع كمية المنتج"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Only {quantity} units are available. Please update the quantity of your order or adjust the product stock."
msgstr "فقط {quantity} كميات موجودة , رجاء قم بتعديل كمية الطلب او تعديل كمية المنتج"

#. module: olivery_products
#: model:ir.actions.report,name:olivery_products.report_rb_delivery_client_inovice_a4_action_one
msgid "client inovice"
msgstr "الفاتورة"