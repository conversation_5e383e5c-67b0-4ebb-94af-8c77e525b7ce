<?xml version="1.0"?>

<odoo>

	<data>
		<record id="paperformat_client_inovice_a4" model="report.paperformat">
			<field name="name">Delivery_Distribution</field>
			<field name="default" eval="True" />
			<field name="format">A4</field>
			<field name="orientation">Portrait</field>
			<field name="margin_top">3</field>
			<field name="margin_bottom">18</field>
			<field name="margin_left">7</field>
			<field name="margin_right">7</field>
			<field name="header_line" eval="False" />
			<field name="header_spacing">1</field>
			<field name="dpi">90</field>
		</record>

		<report id="report_rb_delivery_client_inovice_a4_action_one" string="client inovice" model="rb_delivery.order" report_type="qweb-pdf" name="olivery_products.template_client_inovice_a4" paperformat="paperformat_client_inovice_a4" />


		<template id="template_client_inovice_a4">
			<t t-call="web.basic_layout">
				<t t-foreach="docs" t-as="doc">
					<t t-if="not o" t-set="o" t-value="doc" />
					<t t-if="not company">
						<t t-if="company_id">
							<t t-set="company" t-value="company_id" />
						</t>
						<t t-elif="o and 'company_id' in o">
							<t t-set="company" t-value="o.company_id.sudo()" />
						</t>
						<t t-else="else">
							<t t-set="company" t-value="res_company" />
						</t>
					</t>
					<!-- this is to prevent taking size -->
					<div class="header" style="display:none;font-size:12px !important"> </div>
					<div class="page"
						style="page-break-before:always;border: 2px solid gray; border-radius: 14px; padding:30px;direction:ltr; text-align:left !important;">
						<div class="row text-center">
							<t t-if="company.logo">
								<div class="col-2">
									<t
										t-if="doc.sudo().assign_to_business.replace_company_logo_with_business_logo">
										<img t-if="doc.sudo().assign_to_business.user_image"
											t-att-src="image_data_uri(doc.sudo().assign_to_business.user_image)"
											style="height: 50px; max-width: 100px;" />
									</t>
									<t t-else="">
										<img t-if="company.logo"
											t-att-src="image_data_uri(company.logo)"
											style="height: 50px; max-width: 100px;" />
									</t>
								</div>
								<div class="col-1"></div>
								<div class="col-9" style="padding: 0; margin: 0;">
									<div style="width: 100%;">
										<h1 style="padding: 0; margin: 0; font-weight: bold; color:#2389C9; font-size: 24px;"
											t-field="company.name" />
										<p style="font-weight: bold; color:#555555; font-size: 18px; padding: 0; margin: 0;">
											Address: <span t-if="company.city" t-field="company.city">,</span>
											<span
												t-if="company.street" t-field="company.street"></span>
										</p>
										<p style="font-weight: bold; color:#555555; font-size: 18px; padding: 0; margin: 0;">
											<span t-field="company.email" /> | <span
												t-field="company.website" />
										</p>
									</div>
								</div>
							</t>
							<t t-else="">
								<div class="col-12" style="padding: 0; margin: 0;">
									<div style="width: 100%;">
										<h1 style="font-weight: bold; color:#2389C9; font-size: 24px; padding: 0; margin: 0;"
											t-field="company.name" />
										<p style="font-weight: bold; color:#555555; font-size: 18px; padding: 0; margin: 0;">
											Address: <span t-if="company.city" t-field="company.city">,</span>
											<span
												t-if="company.street" t-field="company.street"></span>
										</p>
										<p style="font-weight: bold; color:#555555; font-size: 18px; padding: 0; margin: 0;">
											<span t-field="company.email" /> | <span
												t-field="company.website" />
										</p>
									</div>
								</div>
							</t>
						</div>
						<div style="border: 2px solid gray; border-radius: 14px; padding:5px;">
							<span>
								<span style="font-weight: bold;">Date: </span>
								<span
									t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')" />
							</span>
						</div>
						<div class="row"
							style="border:10px solid #F5F7FA; border-radius:14px; margin-top:14px;">
							<div class="col-6" style="border-right:2px solid gray;">
								<div style="background-color:#d9e5ff; width:100%; padding:10px;">
									<strong>From | من</strong>
								</div>
								<div style="padding:10px;">
									<h5 style="margin:0;">
										<strong>
											<t t-set="business"
												t-value="doc.sudo().assign_to_business" />
											<t
												t-if="doc.sudo().show_alt_address and doc.sudo().alt_business_name">
												<t t-set="business_name"
													t-value="doc.sudo().alt_business_name" />
											</t>
											<t
												t-elif="doc.sudo().show_follower_info_in_waybill and doc.sudo().show_follower_info and doc.sudo().follower_store_name">
												<t t-set="business_name"
													t-value="doc.sudo().follower_store_name" />
											</t>
											<t t-elif="business and business.commercial_name">
												<t t-set="business_name"
													t-value="business.commercial_name" />
											</t>
											<t t-else="">
												<t t-set="business_name" t-value="business" />
											</t>
											<span
												t-esc="business_name[:24] + '...' if business_name and len(business_name) > 24 else (business_name if business_name else '')" />
										</strong>
									</h5>
									<p style="margin:0;" t-field="doc.sudo().business_area" />
									<p style="margin:0; color:#007bff;"
										t-field="doc.sudo().business_mobile_number" />
								</div>
							</div>
							<div class="col-6">
								<div style="background-color:#d9e5ff; width:100%; padding:10px;">
									<strong style="background-color:#d9e5ff;">To | إلى</strong>
								</div>
								<div style="padding:10px;">
									<p style="margin:0;">
										<strong>Name:</strong>
										<span
											t-esc="doc.sudo().customer_name[:35] + '...' if doc.sudo().customer_name and len(doc.sudo().customer_name) > 35 else (doc.sudo().customer_name if doc.sudo().customer_name else '')" />
									</p>
									<p style="margin:0;">
										<strong>Mobile:</strong>
										<span t-field="doc.sudo().customer_mobile" />
									</p>
								</div>
							</div>
						</div>
						<t t-if="doc.product_line_ids">
							<table
								style="width:100%; border-collapse:collapse; text-align:center; font-family:Arial, sans-serif; font-size:12px; margin-top: 16px;direction:ltr">
								<thead>
									<tr style="border:1px solid #ddd; background-color:#F9F9F9;">
										<th style="padding:8px; border:1px solid #ddd;">Image | الصورة</th>
										<th style="padding:8px; border:1px solid #ddd;">Product | المنتج</th>
										<th style="padding:8px; border:1px solid #ddd;">Details |
											التفاصيل</th>
										<th style="padding:8px; border:1px solid #ddd;">Quantity |
											الكمية</th>
										<th style="padding:8px; border:1px solid #ddd;">Price | السعر</th>
										<th style="padding:8px; border:1px solid #ddd;">Total Without
											Shipping | الإجمالي بدون الشحن</th>
										<th style="padding:8px; border:1px solid #ddd;">Shipping Cost PP
											| تكلفة الشحن لكل منتج</th>
									</tr>
								</thead>
								<tbody>
									<t t-set="subtotal" t-value="0"/>
									<t t-set="shipping_total" t-value="0"/>
									<t t-foreach="doc.product_line_ids" t-as="product">
										<t t-set="subtotal" t-value="subtotal + (product.subtotal or 0.0)"/>
										<t t-set="shipping_total" t-value="shipping_total + (product.delivery_cost_product or 0.0)"/>
										<tr style="border:1px solid #ddd;">
											<td style="padding:8px; border:1px solid #ddd;">
												<t t-if="product.product_id.attachment_ids and product.product_id.attachment_ids[0] and product.product_id.attachment_ids[0].datas">
													<img t-att-src="image_data_uri(product.product_id.attachment_ids[0].datas)"
													alt="Product Image"
													style="width:75px; height:50px;" />										
												</t>
											</td>
											<td style="padding:8px; border:1px solid #ddd;">
												<span t-esc="product.product_id.name"/>
											</td>
											<td
												style="padding:8px; border:1px solid #ddd; text-align:left;">
												<t t-if="product.product_id and product.product_id.description">
													<span t-esc="product.product_id.description[:35]"/>
													<t t-if="len(product.product_id.description) > 35">...</t>

												</t>
												
											</td>
											<td style="padding:8px; border:1px solid #ddd;">
												<span t-esc="product.qty"/>
											</td>
											<td style="padding:8px; border:1px solid #ddd;">
												<span t-esc="product.product_id.price"/>
											</td>
											<td style="padding:8px; border:1px solid #ddd;">
												<span t-esc="product.subtotal"/>
											</td>
											<td style="padding:8px; border:1px solid #ddd;">
												<span t-esc="product.product_id.delivery_cost_product"/>
											</td>
										</tr>
									</t>
								</tbody>						
							</table>
						</t>
						<table style="width:100%; border:1px solid #ddd; border-collapse:collapse;">
							<tbody>
								<tr style="border:1px solid #ddd;">
									<td style="padding:8px; font-weight:bold; text-align:left; border:1px solid #ddd;">
										Subtotal | الإجمالي الفرعي
									</td>
									<td style="padding:8px; text-align:right; border:1px solid #ddd;">
										<span t-esc="subtotal"/>
									</td>
								</tr>
						
								<tr style="border:1px solid #ddd;">
									<td style="padding:8px; font-weight:bold; text-align:left; border:1px solid #ddd;">
										General Shipping Cost | تكاليف الشحن العامة
									</td>
									<td style="padding:8px; text-align:right; border:1px solid #ddd;">
										<span t-esc="doc.extra_cost"/>
									</td>
								</tr>
						
								<tr style="border:1px solid #ddd;">
									<td style="padding:8px; font-weight:bold; text-align:left; border:1px solid #ddd;">
										Total Shipping Cost Per Product | مجموع تكاليف الشحن لكل المنتجات
									</td>
									<td style="padding:8px; text-align:right; border:1px solid #ddd;">
										<span t-esc="shipping_total"/>
									</td>
								</tr>
						
								<tr style="border:1px solid #ddd;">
									<td style="padding:8px; font-weight:bold; text-align:left; border:1px solid #ddd;">
										Transfer and Packaging Costs | تكاليف النقل + التغليف
									</td>
									<td style="padding:8px; text-align:right; border:1px solid #ddd;">
										<span t-esc="doc.service_fee"/>
									</td>
								</tr>
						
								<tr style="border:1px solid #ddd; background-color:#f9f9f9;">
									<td style="padding:8px; font-weight:bold; text-align:left; border:1px solid #ddd; color:#d9534f;">
										Grand Total | الإجمالي الكلي
									</td>
									<td style="padding:8px; text-align:right; font-weight:bold; color:#d9534f; border:1px solid #ddd;">
										<span t-esc="(doc.service_fee or 0.0) + (shipping_total or 0.0) + (doc.extra_cost or 0.0) + (subtotal or 0.0)"/>
									</td>
								</tr>
							</tbody>
						</table>
						<div style="border: 2px solid gray; background-color:#F9F9F9; padding:20px; page-break-inside: avoid;">
							<p style="margin: 0;direction:ltr !important">
								<p>ملاحظات عامة | general Notes:</p>
								
								<t t-if="company and company.report_footer">
									<span style="font-size:12px;" t-field="company.report_footer"/>
								</t>					
							</p>
						</div>
					</div>
				</t>
			</t>
		</template>
	</data>
</odoo>