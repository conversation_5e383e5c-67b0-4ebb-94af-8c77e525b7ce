<odoo>
    <data noupdate="1">
        <record id="demo_nearest_driver" model="rb_delivery.nearest_driver">
            <field name="nearest_driver_algorithem">one_by_one</field>
            <field name="number_of_trials">3</field>
            <field name="timer_duration">30</field>
            <field name="time_allowed_to_cancel_the_order">0.00</field>
            <field name="shipment_statuses" eval="[(6, 0, [ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_canceled')])]"/>
            <field name="number_of_shipments">1</field>
            <field name="nearest_driver_state" eval="[(6, 0, [ref('olivery_nearest_driver.status_pending_nearest_driver')])]"/>
            <field name="manual_assigned_driver_statuses" eval="[(6, 0, [ref('olivery_nearest_driver.status_no_answer') ,ref('olivery_nearest_driver.status_manual_assigned_driver'),ref('rb_delivery.status_waiting')])]"/>
        </record>


        <record id="cron_check_waiting_scheduled_orders" forcecreate="True" model="ir.cron">
			<field name="name">Check scheduled orders</field>
			<field name="model_id" ref="model_rb_delivery_order"/>
			<field name="state">code</field>
			<field name="active">True</field>
            <field name="code">model.check_waiting_scheduled_orders()</field>
			<field name="interval_number">1</field>
			<field name="interval_type">hours</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1)).replace(minute=0, second=0).strftime('%Y-%m-%d %H:%M:00')"/>
            <field name="numbercall">-1</field>
		</record>


        <record id="ir_cron_reset_no_answer_orders" model="ir.cron">
            <field name="name">Reset No Answer Orders</field>
            <field name="model_id" ref="model_rb_delivery_order"/>
            <field name="state">code</field>
            <field name="code">model.action_rb_delivery_no_answer_order()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).replace(minute=0, second=0).strftime('%Y-%m-%d %H:%M:00')"/>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>