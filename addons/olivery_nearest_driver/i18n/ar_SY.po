# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_nearest_driver
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-14 12:30+0000\n"
"PO-Revision-Date: 2022-06-14 12:30+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_action__actions
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.view_form_olivery_nearest_driver_action
msgid "Actions"
msgstr "الإجراءات"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_client_configuration__attachment
msgid "Attachment"
msgstr "مرفق"

#. module: olivery_nearest_driver
#: model:ir.model,name:olivery_nearest_driver.model_rb_delivery_client_configuration
msgid "Client Parameter"
msgstr "متغيرات العميل"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__is_notified
msgid "Is notified"
msgstr "تم اخطاره؟"

#. module: olivery_nearest_driver
#: selection:rb_delivery.action,actions:0
msgid "Nearest driver"
msgstr "السائق الاقرب"

#. module: olivery_nearest_driver
#: selection:rb_delivery.action,actions:1
msgid "Manual Assign Driver"
msgstr "تعيين السائق يدويا"


#. module: olivery_nearest_driver
#: selection:rb_delivery.action,actions:2
msgid "Driver Holding Expired"
msgstr "إنهاء انتظار السائق"


#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.view_form_rb_delivery_olivery_nearest_driver_order
msgid "Nerarest Driver"
msgstr "السائق الاقرب"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__next_drivers
msgid "Next Drivers"
msgstr "السائق التالي"

#. module: olivery_nearest_driver
#: model:rb_delivery.status,title:olivery_nearest_driver.status_no_answer
msgid "No Answer"
msgstr "لا يوجد رد"

#. module: olivery_nearest_driver
#: model:ir.actions.act_window,name:olivery_nearest_driver.action_rb_delivery_no_answer_order
#: model:ir.ui.menu,name:olivery_nearest_driver.menu_rb_delivery_no_answer_order
msgid "No Answered Orders"
msgstr "الطلبيات لا يوجد رد"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__notification_timer
msgid "Notification Timer"
msgstr "مؤقت الاشعار"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__number_of_retrials
msgid "Number of retrials"
msgstr "عدد المحاولات"

#. module: olivery_nearest_driver
#: model:rb_delivery.status,title:olivery_nearest_driver.status_pending_driver
msgid "Pending Driver"
msgstr "انتظار موافقة سائق"

#. module: olivery_nearest_driver
#: model:rb_delivery.status,title:olivery_nearest_driver.status_rejected_by_driver
msgid "Rejected By Driver"
msgstr "رفضت من السائق"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__rejected_user_id
msgid "Rejected User"
msgstr "المستخدم المرفوض"

#. module: olivery_nearest_driver
#: selection:rb_delivery.action,actions:0
msgid "Send on registration"
msgstr "ارسال عند تسجيل الدخول"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__show_waiting_time
msgid "Show Waiting time"
msgstr ""

#. module: olivery_nearest_driver
#: code:addons/olivery_nearest_driver/models/order/order_model.py:369
#, python-format
msgid "The time allowed to cancel the order has expired"
msgstr "وقت الغاء الطلبية قد انتهى"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__waiting_time
msgid "Waiting Time"
msgstr "وقت الانتظار"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.view_form_rb_delivery_olivery_nearest_driver_order
msgid "Waiting time"
msgstr "وقت الانتظار"

#. module: olivery_nearest_driver
#: model:ir.model,name:olivery_nearest_driver.model_rb_delivery_action
msgid "rb_delivery.action"
msgstr ""

#. module: olivery_nearest_driver
#: model:ir.model,name:olivery_nearest_driver.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""


#. module: olivery_nearest_driver
#: code:addons/olivery_nearest_driver/models/order/order_model.py:449
#, python-format
msgid "There are no actions available for this action type: %s and algorithm type: %s"
msgstr "لا توجد إجراءات متاحة لنوع الإجراء: %s ونوع الخوارزمية: %s"

#. module: olivery_nearest_driver
#: code:addons/olivery_nearest_driver/models/order/order_model.py:467
#, python-format
msgid "Failed to update order through nearest driver search with values: %s \n - Error: %s"
msgstr "فشل تحديث الطلبية خلال البحث عن اقرب سائق بالقيم: %s \n - الخظأ: \n %s"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_user__last_pickup_time
msgid "Last Pickup Time"
msgstr "آخر وقت استلام"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_user__hold_until
msgid "Hold Until"
msgstr "الانتظار حتى"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.view_form_olivery_nearest_driver_model
msgid "Holding Settings"
msgstr "إعدادات الانتظار"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_nearest_driver__default_holding_time
msgid "Default Holding Time(By Minute)"
msgstr "الوقت الافتراضي للانتظار (بالدقائق)"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_nearest_driver__use_holding_time
msgid "Use Holding Time"
msgstr "تفعيل وقت الانتظار"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_nearest_driver__start_hold_after_status_id
msgid "Start Holding After Status"
msgstr "بدء الانتظار بعد الحالة"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_nearest_driver__post_hold_status_id
msgid "Post Hold Status"
msgstr "الحالة بعد انتهاء الانتظار"


#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__picked_up_time
msgid "Picked Up Time"
msgstr "وقت الاستلام"


#. module: olivery_nearest_driver
#: code:addons/olivery_nearest_driver/models/order/order_model.py:467
#, python-format
msgid "Order status changed automatically from %s to %s after holding expired."
msgstr "تم تغيير حالة الطلب تلقائيًا من %s إلى %s بعد انتهاء فترة الانتظار."

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Business Agent:"
msgstr "السائق التابع للمرسل:"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Business Name:"
msgstr "اسم التاجر"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Business mobile number:"
msgstr "رقم المحمول للتاجر :"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Address  :"
msgstr "العنوان : "

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Collection Report"
msgstr "كشف التحصيل"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Company Registry:"
msgstr "المشغل مرخص: "

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Date:"
msgstr "التاريخ:"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Delivery cost"
msgstr "تكلفة التوصيل"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Distance (Km)"
msgstr "المسافة (كم)"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Logo"
msgstr "الشعار"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Money collection cost"
msgstr "مبلغ التحصيل"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Order Creation Date"
msgstr "تاريخ انشاء الطلبية"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Page:"
msgstr "صفحة"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Recipient's Info"
msgstr " معلومات الزبون"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Recipient's address"
msgstr "عنوان المستلم"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Sequence number"
msgstr "رقم المتسلسل"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Total"
msgstr "الإجمالي"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "Total net value"
msgstr "مجموع مستحق التاجر"

#. module: olivery_nearest_driver
#: model_terms:ir.ui.view,arch_db:olivery_nearest_driver.multi_print_orders_money_collector_print_distance_report
msgid "signature:"
msgstr "التوقيع:"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__heuristic_distance
msgid "Heuristic Distance (Km)"
msgstr "المسافة الهوائية (كم)"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__driving_distance
msgid "Driving Distance (Km)"
msgstr "المسافة القيادة (كم)"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_order__calculate_driving_distance
msgid "Calculate driving distance"
msgstr "احسب مسافة القيادة"

#. module: olivery_nearest_driver
#: model:ir.actions.report,name:olivery_nearest_driver.report_rb_delivery_order_multi_print_orders_money_collector_distance_action
msgid "Money Collection with distances"
msgstr "كشف تحصيل مع المسافات"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_user__agent_sequence
msgid "Agent Sequence"
msgstr "متسلسل السائق"

#. module: olivery_nearest_driver
#: model:ir.model.fields,field_description:olivery_nearest_driver.field_rb_delivery_user__black_list_drivers
msgid "Black List Drivers"
msgstr "السائقون المحظورون"