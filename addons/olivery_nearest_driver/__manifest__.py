# -*- coding: utf-8 -*-
{
    'name': "olivery_nearest_driver",
    'summary': """
        <PERSON>y Nearest driver  App from olivery.app""",

    'description': """
        Long description of module's purpose
        
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.0.76',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail','website','rb_delivery'],

    # always loaded
    'data': [
        'models/action/action_view.xml',
        'models/client_configuration/client_configuration_view.xml',
        'models/order/order_view.xml',
        'views/module_view.xml',
        'demo/status.xml',
        'demo/client_conf.xml',
        'demo/nearest_driver.xml',
        'models/notification_range/notification_range_view.xml',
        'models/nearest_driver/nearest_driver_view.xml',
        'security/ir.model.access.csv',
        'models/user/user_view.xml',
        'views/print/money_collection_with_distance.xml'

    ], 
    'qweb': [
        'static/src/xml/*.xml',
    ],
    'demo': [
        'demo/demo.xml',
    ],
}
