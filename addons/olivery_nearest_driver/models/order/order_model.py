# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
import googlemaps
from datetime import datetime
from onesignal_sdk.client import Client
from openerp import sql_db as sql_db
import math
from datetime import datetime , timedelta
from time import sleep
import logging
import re
import json
import pytz
_logger = logging.getLogger(__name__)


class olivery_nearest_driver_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------


    @api.model
    def _default_show_waiting_time(self):
        default_show_waiting_time= self.env['rb_delivery.client_configuration'].get_param('order_show_waiting_time_visibility')
        return default_show_waiting_time

    @api.multi
    def _compute_show_waiting_time(self):
        show_waiting_time = self.env['rb_delivery.client_configuration'].get_param('order_show_waiting_time_visibility')
        for rec in self:
            rec.show_waiting_time = show_waiting_time

    def _default_calculate_driving_distance(self):
        calculate_driving_distance = self.env['rb_delivery.client_configuration'].get_param('calculate_driving_distance')
        return calculate_driving_distance

    picked_up_time = fields.Datetime(string="Picked Up Time" ,track_visibility="on_change",readonly=True,copy=False)

    is_notified = fields.Boolean('Is notified', default=False, track_visibility="on_change")

    is_notifying = fields.Boolean('Is notifying', default=False, track_visibility="on_change")

    next_drivers = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'Next Drivers',
        relation = 'next_driver_items',
        column1 = 'next_driver_id',
        column2 = 'next_driver_order_id',track_visibility="on_change")

    looped_drivers = fields.Many2many(comodel_name = 'rb_delivery.user',
        string = 'Looped Drivers',
        relation = 'looped_driver_items',
        column1 = 'looped_driver_id',
        column2 = 'looped_driver_order_id',track_visibility="on_change")
    

    is_delayed = fields.Boolean('Is dalayed', default=False, track_visibility="on_change")

    waiting_time = fields.Char('Waiting Time')

    show_waiting_time= fields.Boolean('Show Waiting time', default=_default_show_waiting_time, compute="_compute_show_waiting_time", readonly=True)

    number_of_retrials = fields.Integer('Number of retrials',track_visibility="on_change",default=0,readonly=True)

    active_notification_data = fields.Char('Active Notification Data')

    count_number_of_loops= fields.Integer('Counter', default=1, readonly=True)

    vehicle_type = fields.Selection([('car', 'Car'),('motorcycle', 'Motorcycle')], track_visibility="on_change",copy=False)

    driving_distance = fields.Float('Driving Distance (Km)', compute='_compute_driving_distance', readonly=True, store=True)

    heuristic_distance = fields.Float('Heuristic Distance (Km)', compute='_compute_heuristic_distance', readonly=True, store=True)
    
    calculate_driving_distance = fields.Boolean('Calculate driving distance', default=_default_calculate_driving_distance, compute="_compute_calculate_driving_distance", readonly=True)

    @api.multi
    def _compute_calculate_driving_distance(self):
        calculate_driving_distance = self.env['rb_delivery.client_configuration'].get_param('calculate_driving_distance')
        for rec in self:
            rec.calculate_driving_distance = calculate_driving_distance

    @api.depends('assign_to_business','assign_to_business.longitude','assign_to_business.latitude','longitude','latitude')
    def _compute_driving_distance(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        api_key = self.env['rb_delivery.client_configuration'].get_param('google_map_key')
        for rec in self:
            if not rec.calculate_driving_distance:
                continue
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.longitude and rec.sudo().assign_to_business.latitude and rec.sudo().longitude and rec.sudo().latitude:
                
                if not api_key:
                    rec._compute_heuristic_distance()
                    rec.driving_distance = rec.heuristic_distance
                    continue

                try:
                    gmaps = googlemaps.Client(key=api_key)

                    origin = (float(rec.sudo().assign_to_business.latitude), float(rec.sudo().assign_to_business.longitude))
                    destination = (float(rec.latitude), float(rec.longitude))

                    directions_result = gmaps.directions(
                        origin,
                        destination,
                        mode="driving",
                        units="metric"
                    )

                    if directions_result and len(directions_result) > 0:
                        distance_meters = directions_result[0]['legs'][0]['distance']['value']
                        distance_km = distance_meters / 1000.0

                        rec.driving_distance = round(distance_km, 2)
                    else:
                        rec._compute_heuristic_distance()
                        rec.driving_distance = rec.heuristic_distance

                except Exception as e:
                    _logger.error("Error calculating driving distance: %s", str(e))
                    rec._compute_heuristic_distance()
                    rec.driving_distance = rec.heuristic_distance
            else:
                rec.driving_distance = False

    @api.depends('assign_to_business','assign_to_business.longitude','assign_to_business.latitude','longitude','latitude')
    def _compute_heuristic_distance(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return
        for rec in self:
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.longitude and rec.sudo().assign_to_business.latitude and rec.longitude and rec.latitude:
                lon1 = math.radians(float(rec.sudo().assign_to_business.longitude))
                lat1 = math.radians(float(rec.sudo().assign_to_business.latitude))
                lon2 = math.radians(float(rec.longitude))
                lat2 = math.radians(float(rec.latitude))
                R =  6373.0 #3956  in miles # 6373.0 in km

                dlon = lon2 - lon1

                dlat = lat2 - lat1

                a = math.sin(dlat / 2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2)**2

                c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

                distance = R * c
                rec.heuristic_distance = round(distance, 2)


    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------






    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------
    @api.model
    def create(self,values):
        order = super(olivery_nearest_driver_order, self).create(values)
        values['state']=order.state
        values['id']=order.id
        #This is if we have different state e.g manual assignment
        new_values = order.do_action_create_nearest_driver_custom(values)
        return order

    def guard_function(self,values):
        manual_assign_driver_statuses_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one','manual_assigned_driver_statuses')
        manual_assign_driver_statuses = self.env['rb_delivery.status'].browse(manual_assign_driver_statuses_ids).mapped('name')
        for rec in self:
            if 'state' in values and values['state']:
                rec.do_nearest_driver_action(values)
                if values['state'] == 'canceled' :
                    rec.check_if_it_is_allowed_to_cancel_the_order()
                if values['state'] not in ['pending_nearest_driver','manual_assigned_driver'] :
                    if rec.active_notification_data and 'active_notification_data' not in values:
                        values['active_notification_data'] = False
                if values['state'] == 'no_answer' and 'assign_to_agent' not in values and self._uid !=1:
                    values['assign_to_agent'] = False
                if values['state'] == 'rejected_by_driver' :
                    rec.notify_drivers(values)
            if rec.state in  manual_assign_driver_statuses and 'assign_to_agent' in values and values['assign_to_agent'] and self._uid !=1:
                rec.to_manual_assigned_driver(values)

        return super(olivery_nearest_driver_order, self).guard_function(values)




    def get_nearest_driver(self, sender, order_id):
        nearest_drivers = []
        unsort_nearest_drivers = []
        group = self.env.ref('rb_delivery.role_driver')
        drivers=[]
        black_list_driver_ids = sender.black_list_drivers.ids if sender.black_list_drivers else []
        order = self.sudo().browse([order_id])

        use_client_location =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_client_location')
        use_alt_location =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_alt_location')
        
        # Configurable
        try:
            if use_client_location and order.longitude and order.latitude:

                lon1 = math.radians(float(order.longitude))
                lat1 = math.radians(float(order.latitude))
            elif use_alt_location and order.business_alt_latitude and order.business_alt_longitude:
                lon1 = math.radians(float(order.business_alt_longitude))
                lat1 = math.radians(float(order.business_alt_latitude))
            else:
                lon1 = math.radians(float(sender.longitude))
                lat1 = math.radians(float(sender.latitude))
        except:
            pass

        

        number_of_shipments_allowed_with_driver = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'number_of_shipments') or 0
        depend_on_cod = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'depend_on_cod')
        max_cod_with_driver = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'max_cod') or 0
        canceled_shipment_statuses_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')
        canceled_shipment_statuses = self.env['rb_delivery.status'].browse(canceled_shipment_statuses_ids).mapped('name')
        use_business_drivers =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_business_drivers')
        use_business_system_drivers =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_business_system_drivers')
        max_distance =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'max_distance')
        all_orders = self.env['rb_delivery.order'].sudo().search([('assign_to_agent', '!=', False)])

        use_nearest_drivers_by_same_vehicle_type =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_nearest_drivers_by_same_vehicle_type')
        if use_nearest_drivers_by_same_vehicle_type:
            
            if order and order.vehicle_type:
                drivers = self.env['rb_delivery.user'].sudo().search([
                    ('group_id', '=', group.id),
                    ('online', '=', True),
                    ('longitude', '!=', False),
                    ('latitude', '!=', False),
                    ('player_id','!=',False),
                    ('vehicle_type','=',order.vehicle_type),
                    ('id', 'not in', black_list_driver_ids),
                    '|', ('state', '=', 'confirmed'), ('state', '=', 'reconfirmed')
                ])
            else:
                drivers = self.env['rb_delivery.user'].sudo().search([
                ('group_id', '=', group.id),
                ('online', '=', True),
                ('longitude', '!=', False),
                ('latitude', '!=', False),
                ('player_id','!=',False),
                ('id', 'not in', black_list_driver_ids),

                '|', ('state', '=', 'confirmed'), ('state', '=', 'reconfirmed')
            ])
        elif(use_business_drivers):
            sender.nearest_drivers_for_business.read(['id'])
            drivers = sender.nearest_drivers_for_business.filtered(lambda driver: driver.online and driver.longitude and driver.latitude and driver.player_id and driver.state in ['confirmed', 'reconfirmed'] and driver.id not in black_list_driver_ids)
        elif(use_business_system_drivers):
            business_users = self.env['rb_delivery.user'].sudo().search([
                ('role_code', '=', 'rb_delivery.role_business')
            ])

            all_drivers = self.env['rb_delivery.user']

            for biz in business_users:
                all_drivers |= biz.sudo().nearest_drivers_for_business

            rest_of_driver = self.env['rb_delivery.user'].sudo().search([
                ('group_id', '=', group.id),
                ('online', '=', True),
                ('id', 'not in', all_drivers.ids),
                ('id', 'not in', black_list_driver_ids),
                ('longitude', '!=', False),
                ('latitude', '!=', False),
                ('player_id','!=',False),
                '|', ('state', '=', 'confirmed'), ('state', '=', 'reconfirmed')
            ])
            return self.sort_by_sequence(all_orders, sender.nearest_drivers_for_business.filtered(lambda driver: driver.sudo().online and driver.sudo().longitude and driver.sudo().latitude and driver.sudo().player_id and driver.sudo().state in ['confirmed', 'reconfirmed'] and driver.id not in black_list_driver_ids), canceled_shipment_statuses, number_of_shipments_allowed_with_driver, depend_on_cod, max_cod_with_driver) + self.get_drivers_sorted(all_orders.sudo(), rest_of_driver.sudo(), canceled_shipment_statuses, number_of_shipments_allowed_with_driver, depend_on_cod, max_cod_with_driver, lon1, lat1, max_distance)
        else:
            drivers = self.env['rb_delivery.user'].sudo().search([
                ('group_id', '=', group.id),
                ('online', '=', True),
                ('longitude', '!=', False),
                ('latitude', '!=', False),
                ('player_id','!=',False),
                ('id', 'not in', black_list_driver_ids),
                '|', ('state', '=', 'confirmed'), ('state', '=', 'reconfirmed')
            ])
            
        return self.get_drivers_sorted(all_orders, drivers.sudo(), canceled_shipment_statuses, number_of_shipments_allowed_with_driver, depend_on_cod, max_cod_with_driver, lon1, lat1, max_distance)

    def get_drivers_sorted(self, all_orders, drivers, canceled_shipment_statuses, number_of_shipments_allowed_with_driver, depend_on_cod, max_cod_with_driver, lon1, lat1, max_distance):
        nearest_drivers = []
        unsort_nearest_drivers = []
        R =  6373.0 #3956  in miles # 6373.0 in km
        holding_enabled, post_status_name = self.get_holding_status()
        for driver in drivers:
            assigned_orders = [order for order in all_orders if order.assign_to_agent.id == driver.id and order.state in canceled_shipment_statuses]
            total_cod = sum(order.money_collection_cost for order in assigned_orders if order.money_collection_cost)
            #pending_orders = [order for order in all_orders if order.assign_to_agent.id == driver.id and order.state == 'pending_nearest_driver']
            
            #if len(pending_orders) > 0 or len(assigned_orders) >= int(number_of_shipments_allowed_with_driver):
            if len(assigned_orders) >= int(number_of_shipments_allowed_with_driver) or (depend_on_cod and total_cod >= max_cod_with_driver):
                _logger.info("####******* moza error in getting driver in allowed")
                _logger.info(len(assigned_orders))
                _logger.info(driver.id)
                continue

            if holding_enabled and post_status_name:
                has_unfinished = any(order.assign_to_agent.id == driver.id and order.state != post_status_name for order in all_orders)
                if has_unfinished:
                    continue

            try:
                lon2 = math.radians(float(driver.longitude))
                lat2 = math.radians(float(driver.latitude))
            except Exception as e:
                _logger.info("####******* bandora error in getting driver location")
                _logger.info(driver.id)
                _logger.info(e)
                continue

            dlon = lon2 - lon1
            dlat = lat2 - lat1

            a = math.sin(dlat / 2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            distance = R * c
            if float(distance) > float(max_distance) and float(max_distance)>0:
                continue
            unsort_nearest_drivers.append({'driver':driver.id,'distance':distance})
            _logger.info('#####*************** Olivery Neraest drivers dsitance #### ****')
        unsort_nearest_drivers.sort(key=lambda x: x['distance'])
        _logger.info(unsort_nearest_drivers)
        for unsort_driver in unsort_nearest_drivers:
            nearest_drivers.append(unsort_driver['driver'])

        return nearest_drivers
    

    def sort_by_sequence(self, all_orders, drivers, canceled_shipment_statuses, number_of_shipments_allowed_with_driver, depend_on_cod, max_cod_with_driver):
        nearest_drivers = self.env['rb_delivery.user']
        holding_enabled, post_status_name = self.get_holding_status()

        for driver in drivers:
            if holding_enabled and post_status_name:
                has_unfinished = any(order.assign_to_agent.id == driver.id and order.state != post_status_name for order in all_orders )
                if has_unfinished:
                    continue
            assigned_orders = [order for order in all_orders if order.sudo().assign_to_agent.id == driver.id and order.state in canceled_shipment_statuses]
            total_cod = sum(order.sudo().money_collection_cost for order in assigned_orders if order.sudo().money_collection_cost)
            if len(assigned_orders) >= int(number_of_shipments_allowed_with_driver) or (depend_on_cod and total_cod >= max_cod_with_driver):
                continue

            nearest_drivers += driver.sudo()

        return nearest_drivers.sudo().sorted('agent_sequence').ids
    

    def notify_drivers(self, values=None):
        # Ensure values dictionary exists
        values = values or {}

        # Check the 'assign_to_agent' field logic
        if not values.get('assign_to_agent'):
            # Use a unique identifier for the lock (e.g., a constant string)
            lock_id = 'notify_nearest_driver_per_order_lock'

            # Acquire the advisory lock (using pg_try_advisory_lock for non-blocking)
            self._cr.execute("SELECT pg_try_advisory_lock(hashtext(%s))", (lock_id,))
            lock_acquired = self._cr.fetchall()[0][0]

            if lock_acquired:
                try:
                    job_exists = self.sudo().env['queue.job'].search([
                        ('method_name', '=', 'notify_nearest_driver_per_order'),
                        ('state', 'in', ['enqueued', 'pending', 'started'])
                    ], limit=1)

                    if not job_exists:
                        self.with_delay(channel="root.notify_orders_one_by_one", priority=2, eta=5 * 2, max_retries=1).notify_nearest_driver_per_order(created_on=fields.Datetime.now())
                finally:
                    # Release the advisory lock
                    self._cr.execute("SELECT pg_advisory_unlock(hashtext(%s))", (lock_id,))
            else:
                _logger.info("Advisory lock not acquired; another process is likely handling this already.")
        else:
            self.to_manual_assigned_driver(values)


    def check_schedule_date(self, time_zone):
        tz = pytz.timezone(time_zone)
        
        now_dt = fields.Datetime.from_string(fields.Datetime.now())
        now_dt = now_dt.replace(tzinfo=pytz.UTC).astimezone(tz)
        
        scheduled_dt = fields.Datetime.from_string(self.reschedule_date)
        scheduled_dt = scheduled_dt.replace(tzinfo=pytz.UTC).astimezone(tz)
        
        time_difference = scheduled_dt - now_dt
        
        return time_difference < timedelta(hours=3) or scheduled_dt < now_dt


    def check_waiting_scheduled_orders(self):
        delay_schedule =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'delay_schedule')
        if not delay_schedule:
            return
        orders = self.sudo().search([('reschedule_date', '!=', False), ('state', 'in', ['waiting', 'reschedule'])])
        if not orders:
            return
        
        orders.notify_drivers()

    def to_manual_assigned_driver(self,values):
        if 'assign_to_agent' in values and values['assign_to_agent']:
            for rec in self:
                rec.write({'state':'manual_assigned_driver'})
                rec.notify_manual_assigned_driver(rec.id,values)


    def notify_manual_assigned_driver(self,order_id,values):
        driver = self.env['rb_delivery.user'].search([('id','=',values['assign_to_agent'])])
        order = self.env['rb_delivery.order'].sudo().search([('id','=',order_id),('state','=','manual_assigned_driver')])
        if driver.player_id:
            active_notification_data = self.notify_driver(order,driver,'per_order','manual_assign_driver')
            order.write({'active_notification_data':active_notification_data})
            
        return True



    def notify_nearest_driver_per_order(self,number_of_errors=0,created_on=False):
        period = self._get_notification_period()
        counters = {}
        retry_count=0
        delay_schedule =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'delay_schedule')
        time_zone =self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'time_zone')

        orders_with_issues = self.env['rb_delivery.order']

        try:
            skiped_orders = self.env['rb_delivery.order']
            while self._has_pending_orders():
                orders = self._get_pending_orders()


                states = [order.read(['state']) for order in orders ]
                _logger.info(f'orders statuses: {states}')
                skiped_orders_batch = self.env['rb_delivery.order']
                job_age = datetime.now() - fields.Datetime.from_string(created_on)
                if len(orders) <= number_of_errors:
                    return
                elif job_age > timedelta(minutes=30):
                    self.with_delay(channel="root.notify_orders_one_by_one", priority=2, eta=5 * 2, max_retries=1).notify_nearest_driver_per_order(created_on=fields.Datetime.now())
                    return
                for order in orders:
                    if delay_schedule and order.reschedule_date and not order.check_schedule_date(time_zone):
                        skiped_orders_batch += order
                        continue
                    values , reset_counter , start_counter = self._prepare_order_values(order, counters, period,orders.filtered(lambda x:x.active_notification_data and x.state=='pending_nearest_driver'))
                    _logger.info(f'reset_counter: {reset_counter}')
                    if reset_counter or start_counter:
                        counters[order.id]=0
                    order_updated = self._update_order(order, values, order in orders_with_issues)
                    if order_updated == 'error' and order not in orders_with_issues:
                        _logger.info(f'number_of_errors: {number_of_errors}')
                        number_of_errors += 1
                        orders_with_issues += order
                    elif order_updated == 'pass':
                        _logger.info(f'order updated with values: {values}')
                        
                        if order.id in counters:
                            del counters[order.id]
                    if order_updated == 'no_vals' or order_updated == 'error':
                        counters[order.id] = counters[order.id]+1 if order.id in counters and counters[order.id] < period  else 0
                if skiped_orders_batch:
                    skiped_orders_batch.sudo().write({'is_delayed': True})
                    skiped_orders += skiped_orders_batch
                _logger.info(f'counters: {counters}')
                self.env.cr.commit()
                sleep(1)
            skiped_orders.sudo().write({'is_delayed': False})
                    
        except Exception as e:
            _logger.info('Error while looping on driver')
            _logger.info(e)
            self.env.cr.rollback()
            
            if retry_count>=3:
                raise ValidationError(_(str(e)))
            else:
                self.notify_nearest_driver_per_order(number_of_errors,created_on)
            retry_count+=1

    def _get_notification_period(self):
        return int(self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'timer_duration'))



    def _has_pending_orders(self):
        return bool(self.env['rb_delivery.order'].sudo().search_count([
            ('state', 'in', ['waiting', 'rejected_by_driver', 'pending_nearest_driver']),
            ('is_delayed', '=', False)
        ]))

    def _get_pending_orders(self):
        return self.env['rb_delivery.order'].sudo().search([
            ('state', 'in', ['waiting', 'rejected_by_driver', 'pending_nearest_driver'])
        ])



    def _prepare_order_values(self, order, counters, period,looping_orders):
        values = {}
        duration = counters[order.id] if order.id in counters else 0
        reset_counter = False
        start_counter = False
        force_assign_to_nearest = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'force_assign_to_nearest')
        if order.assign_to_agent or order.state in ['rejected_by_driver', 'waiting'] and not force_assign_to_nearest:
            values = self._handle_assigned_agent(order, duration, period)
            if values :
                reset_counter = True
        else:
            nearest_drivers = self.get_nearest_driver(order.sudo().assign_to_business, order.id)
            _logger.info('#####*************** Olivery Neraest drivers are#### ****')
            _logger.info(nearest_drivers)
            busy_drivers = [order.assign_to_agent.id for order in looping_orders if order.assign_to_agent]
            _logger.info(f'busy drivers: {busy_drivers}')
            not_looped_drivers = [driver_id for driver_id in nearest_drivers if driver_id not in busy_drivers and driver_id not in order.sudo().looped_drivers.ids]
            if not_looped_drivers:
                values = self._assign_to_next_driver(order, not_looped_drivers[0])
                start_counter = True
            else:
                values = self._handle_no_answer(order)

        return values, reset_counter , start_counter

    def _handle_assigned_agent(self, order, duration, period):
        values = {}
        if order.state != 'pending_nearest_driver' and order.state != 'rejected_by_driver':
            values['state'] = 'pending_nearest_driver'
        if duration >= period or order.state in ['rejected_by_driver', 'waiting']:
            values.update({
                'assign_to_agent': False,
                'state': 'pending_nearest_driver',
                'is_notifying': False
            })
        return values

    def _assign_to_next_driver(self, order, next_driver):
        force_assign_to_nearest = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'force_assign_to_nearest')
        state = 'pending_nearest_driver'
        if force_assign_to_nearest:
            state = 'picking_up'
        values = {
            'state': state,
            'assign_to_agent': next_driver,
            'is_notifying': True,
            'is_notified': True,
            'looped_drivers':[(4,next_driver)]
        }
        driver_to_notify = self.env['rb_delivery.user'].sudo().browse([next_driver])
        if driver_to_notify:
            values['active_notification_data'] = self.notify_driver(order, driver_to_notify, 'per_order', 'nearest_driver', force_assign_to_nearest)
        return values

    def _handle_no_answer(self, order):
        values = {
            'is_notifying': False,
            'assign_to_agent': False,
            'number_of_retrials': order.number_of_retrials + 1,
        }
        number_of_retrials = int(self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'number_of_trials'))
        if order.number_of_retrials + 1 < number_of_retrials:
            values.update({
                'state': 'waiting',
                'looped_drivers': [(6, 0, [])],
            })
        else:
            values.update({
                'state': 'no_answer',
                'looped_drivers': [(6, 0, [])],
            })
        return values

    def _update_order(self, order, values, already_checked):
        if not values:
            return 'no_vals'

        lock_id = f"pg_advisory_xact_lock({order.sequence})"
        self.env.cr.execute(f"SELECT {lock_id}")

        error_message = None

        try:
            _logger.info(f'Updating order {order.sequence} with values: {values}')
            order.sudo().write(values)
            self.env.cr.commit()
            result = 'pass'
        except Exception as e:
            error_message = str(e)
            _logger.error(f'Failed to update order {order.sequence} with values: {values} - Error: {error_message}')
            self.env.cr.rollback()
            result = 'error'
        finally:
            pass

        if result == 'error' and not already_checked:
            order.sudo().message_post(_('Failed to update order through nearest driver search with values: %s \n - Error: %s') % (values, error_message))

        return result


    def notify_driver(self,order,driver,algorithmType,type,force_assign=False):
        _logger.info("###################### In Notify Driver #################################")
        group, header, message,is_sms,is_email,is_notification,notification_sound_id , show_notification_timer , notification_timer_duration= self.get_driver_notification_action(type,algorithmType)
        if(group and header and message and show_notification_timer and notification_timer_duration):
            players = []
            users = []
            mobile_numbers = []
            emails = []
            distributor_user = driver
            if group and group.id:
                group = self.env['res.groups'].sudo().search([('id', '=', group.id)])
                if group.code == 'rb_delivery.role_driver':
                    mobile_numbers.append(distributor_user.mobile_number)
                    if is_sms:
                        users.append(distributor_user)
                    elif distributor_user.id and distributor_user.player_id:
                        users.append(distributor_user)
                        players.append(str(distributor_user.player_id))
                    elif distributor_user.email and is_email:
                        users.append(distributor_user)
                        emails.append(distributor_user.email)
            active_notification_data = {
                        'accept_status': "picking_up",
                        'create_date': str(datetime.now()),
                        'notification_timer': notification_timer_duration,
                        'order_id': order.id,
                        'reject_status': "rejected_by_driver",
                    }
            notification_group_type ='NearestDriver'
            if force_assign:
                notification_group_type='ForceNearestDriver'
            self.env['rb_delivery.notification_center'].notification(users, emails, players, mobile_numbers, header, message, is_sms, is_email, is_notification,order,order.sequence,'rb_delivery.order',False,'rb_delivery.order',notification_sound_id,show_notification_timer,notification_timer_duration,notification_group_type=notification_group_type)

            return json.dumps(active_notification_data)

    def check_if_it_is_allowed_to_cancel_the_order(self):
        cancel_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one','time_allowed_to_cancel_the_order')
        if cancel_time:
            fmt = '%Y-%m-%d %H:%M:%S'
            end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            start_date = self.create_date.strftime('%Y-%m-%d %H:%M:%S')
            d1 = datetime.strptime(start_date, fmt)
            d2 = datetime.strptime(end_date, fmt)
            date_difference = d2 - d1
            seconds = date_difference.seconds
            minutes = (seconds / 60)
            hours = (minutes/60)
            if hours > int(cancel_time) :
                raise ValidationError(_('The time allowed to cancel the order has expired'))



    def do_nearest_driver_action(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_nearest_driver_order,action.name)
                method_to_call(self,values)
            except:
                pass

    def do_action_create_nearest_driver_custom(self,values):
        new_values=values
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_nearest_driver_order,action.name)
                to_values = method_to_call(self,values)
                if to_values and isinstance(to_values, str):
                    new_values=to_values
            except:
                _logger.info("in the value erorr in do_action_create")
                pass
        # to return values if state is changed
        return new_values


    def get_driver_notification_action(self, action_type,algorithem_type):
        actions = self.env['rb_delivery.action'].search(
            [('actions', '=', action_type),('action_type','=','for_action')])
        if not actions:
            raise ValidationError(_("There are no actions available for this action type: %s and algorithm type: %s") % (action_type, algorithem_type))

        group=''
        message=''
        is_email=False
        is_sms=False
        is_notification=False
        notification_sound_id =False
        show_notification_timer =False
        notification_timer_duration =False
        for action in actions:
            group = action.group_id
            message = ''
            header= ''
            if action.action_template:
                message = action.action_template.body_html
                header = action.action_template.subject
            elif action.order_user_template:
                message = action.order_user_template.body_html
                header = action.order_user_template.subject
            elif action.collection_template:
                message = action.collection_template.body_html
                header = action.collection_template.subject
            elif action.message:
                message = action.message
                header = action.header
            is_sms = action.is_sms
            is_email = action.is_email
            is_notification = action.is_notification
            show_notification_timer = action.show_notification_timer
            if algorithem_type == 'per_order':
                notification_timer_duration = int(self.env['rb_delivery.nearest_driver'].get_param('one_by_one','timer_duration'))
            elif algorithem_type == 'by_ranges':
                notification_timer_duration = int(self.env['rb_delivery.nearest_driver'].get_param('by_range','timer_duration'))
            notification_sound_id = action.notification_sound
            return group, header, message,is_sms,is_email,is_notification,notification_sound_id , show_notification_timer , notification_timer_duration

    def action_rb_delivery_no_answer_order(self):
        time_value = self.env['rb_delivery.nearest_driver'].get_param('one_by_one','time_allowed_to_cancel_the_order')
        rec = self.env.ref('olivery_nearest_driver.action_rb_delivery_no_answer_order')
        time = int(time_value)
        domain = []
        order_ids = []
        date_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        no_answer_orders = self.env['rb_delivery.order'].search([('state','=','no_answer')])
        if len(no_answer_orders) > 0:
            for order in no_answer_orders:
                order_date = datetime.strftime(order.write_date,'%Y-%m-%d %H:%M:%S')
                diff = datetime.strptime(date_now, '%Y-%m-%d %H:%M:%S') - datetime.strptime(order_date, '%Y-%m-%d %H:%M:%S')
                seconds = diff.seconds
                if seconds > (time * 60):
                    order_ids.append(order.id)

        domain = [('id','in',order_ids)]

        rec.sudo().write({'domain':domain})
        return True

    def notify_drivers_by_range(self,values):
        self.with_delay(channel="root.notify_drivers_by_range",max_retries=1).notify_drivers_by_ranges()

    def hold_order(self, values):
        use_holding_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_holding_time')
        if not use_holding_time:
            return

        start_status_name = values.get('state')
        default_holding_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'default_holding_time')
        if not start_status_name or not default_holding_time:
            return
        
        now = fields.Datetime.now()

        for order in self:
            driver = order.assign_to_agent
            if not driver:
                continue
            order.picked_up_time = now
            if not driver.hold_until:
                driver.hold_until = now + timedelta(minutes=default_holding_time)
            driver.last_pickup_time = now
            order.with_delay(channel="root.order_hold_timeout", max_retries=1, eta=driver.hold_until).release_order(driver.id, start_status_name)


    def release_order(self, driver_id, start_status_name):
        driver = self.env['rb_delivery.user'].browse(driver_id)

        post_hold_status_id = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'post_hold_status_id')
        if not post_hold_status_id:
            return

        post_status_name = self.env['rb_delivery.status'].browse(post_hold_status_id[0]).mapped('name')[0]
        if self.state != start_status_name:
            _logger.warning("Skip release: Order %s state is already changed from %s. Current state: %s", self.name, start_status_name, self.state)
            return
        
        if self.state and post_status_name and self.state != post_status_name:
            self.write({'state': post_status_name})
            self.sudo().message_post(body=_('Order status changed automatically from %s to %s after holding expired.') % (start_status_name, post_status_name))
            if driver.hold_until:
                driver.hold_until = False

    def notify_drivers_by_ranges(self):
        period = int(self.env['rb_delivery.nearest_driver'].get_param('by_range','timer_duration'))
        default_nearest_driver_status = self.env['rb_delivery.nearest_driver'].get_param('by_range','nearest_driver_state')
        default_nearest_driver_status_name='pending_nearest_driver'
        if default_nearest_driver_status and len(default_nearest_driver_status) > 0:
            default_nearest_driver_status_name=self.env['rb_delivery.status'].sudo().search([['id','=',default_nearest_driver_status[0]]]).name
        max_total_period=400
        total_period=0
        range =0
        notification_range_ids = self.env['rb_delivery.nearest_driver'].get_param('by_range','notification_range')
        with api.Environment.manage():
            new_cr = sql_db.db_connect(self.env.cr.dbname).cursor()
            uid, context = self.env.uid, self.env.context
            self.env = api.Environment(new_cr, uid, context)
            limit_reached=False
            # orders_arr = self.env['rb_delivery.order'].search([('state','in',['waiting','rejected_by_driver',default_nearest_driver_status_name])],order="id asc")
            # should recalculate driver distance again ( nearest drive)
            datetime1 = datetime.now()
            datetime2 = datetime.now()
            count_down=0
            orders_arr = self.env['rb_delivery.order'].search([('state','in',['waiting',default_nearest_driver_status_name,'rejected_by_driver'])],order="id asc")
            try:
             while len(orders_arr) != 0:
                orders_arr = self.env['rb_delivery.order'].search([('state','in',['waiting',default_nearest_driver_status_name,'rejected_by_driver'])],order="id asc")
                    # TODO also need to check if new drivers are here

                _logger.info("##### Orders list are.. %s and period is %s", orders_arr,period)
                datetime2=datetime.now()
                # we added one for the sleep value
                duration=round((datetime2 - datetime1).seconds)+1
                sleep(1)

                _logger.info("##### total_period is %s from %s", total_period,max_total_period)
                if total_period <= max_total_period:
                    total_period=total_period+duration
                else :
                    raise TimeoutError()
                datetime1=datetime.now()
                for order in orders_arr:
                    _logger.info("#### Checking order number  %s", order)
                    #_logger.info("#### Drivers list are.. %s", order.next_drivers)

                    if order.current_drivers:
                        _logger.info("#### Drivers is . %s", order.current_drivers)
                        # then we need to check if we should go to no_answer or next driver or tick

                        # will enter most of the time
                        _logger.info("#### notification is less than period then incrmenet and %s , %s",count_down + duration,period)


                        if (count_down+duration) >= period or order.state=='rejected_by_driver' or order.state=='waiting':
                            _logger.info("#### notification is more than period then remove agent and move the order back to waiting to assign to next driver")
                            count_down=0
                            order.active_notification_data = False
                            order.state='waiting'
                            # remove the current agent
                            order.current_drivers=[(6,0,[])]
                            order.number_of_retrials= order.number_of_retrials +1
                            # in the next loop it will be assigned to new agent

                        elif (count_down + duration) < period :
                            # then we need to increase notification timer by duration
                            # the commit here to commit all actions while the clock is ticking
                            _logger.info("#### notification is less than period then incrmenet")
                            count_down=duration + count_down
                        self.env.cr.commit()
                        self.env.cr.close()

                    elif not order.current_drivers :
                        _logger.info("#### No Driver is assigned try to get next nearest driver")
                        range+=1
                        next_driver=self.get_nearest_driver_by_range(order.sudo().assign_to_business,order,range)
                        drivers_ids = []
                        if next_driver and len(next_driver) > 0:
                            for driver in next_driver:
                                drivers_ids = drivers_ids + [driver.id]
                                active_notification_data = self.notify_driver(order,driver,'by_ranges','nearest_driver')
                                order.write({'active_notification_data':active_notification_data})
                            order.state="pending_nearest_driver"
                            order.current_drivers=[(6,0,drivers_ids)]
                            order.next_drivers = [(6,0,drivers_ids)]
                            count_down=0

                        else:
                            if range < len(notification_range_ids):
                                continue
                            else:
                                order.state='no_answer'
                                count_down=0
                                order.active_notification_data = False
                                order.number_of_retrials = order.number_of_retrials+1
                                order.next_drivers = [(6,0,[])]
                                number_of_retrials = self.env['rb_delivery.nearest_driver'].get_param('by_range','number_of_trials')
                                if int(order.number_of_retrials)< int(number_of_retrials):
                                    order.state='waiting'
                        self.env.cr.commit()
                if limit_reached==True:
                    break
            except TimeoutError:
                # job_is_exist = self.sudo().env['queue.job'].search(['&','|',('method_name', '=', 'notify_nearest_driver_per_order'),('state','=','pending'),('state','=','started')])
                self.with_delay(channel="root",priority=2, eta=5*2,max_retries=1).notify_drivers_by_range()
                # if job_is_exist.id:
                    #  job_is_exist.unlink_job(job_is_exist.id)
                self.env.cr.commit()
                self.env.cr.close()

    def get_nearest_driver_by_range(self, sender,order,range):
        nearest_drivers = []
        drivers = []
        credit_limit = self.env['rb_delivery.client_configuration'].get_param('driver_credit_limit')
        group = self.env.ref('rb_delivery.role_driver')
        if credit_limit != False:
            drivers = self.env['rb_delivery.user'].sudo().search([('group_id','=',group.id),('online','=',True),('longitude','!=',False),('latitude','!=',False),'|',('state','=','confirmed'),('state','=','reconfirmed'),('credit_lock','!=',True)])
        else:
            drivers = self.env['rb_delivery.user'].sudo().search([('group_id','=',group.id),('online','=',True),('longitude','!=',False),('latitude','!=',False),'|',('state','=','confirmed'),('state','=','reconfirmed')])
        try:
            lon1 = math.radians(float(sender.longitude))
            lat1 = math.radians(float(sender.latitude))
        except:
            pass

        R =  6373.0 #3956  in miles # 6373.0 in km
        number_of_shipments_allowed_with_driver = self.env['rb_delivery.nearest_driver'].get_param('by_range','number_of_shipments')
        depend_on_cod = self.env['rb_delivery.nearest_driver'].get_param('by_range','depend_on_cod')
        max_cod_with_driver = self.env['rb_delivery.nearest_driver'].get_param('by_range','max_cod_with_driver')
        if not number_of_shipments_allowed_with_driver or number_of_shipments_allowed_with_driver == '':
            number_of_shipments_allowed_with_driver = 0
        number_of_trials = self.env['rb_delivery.nearest_driver'].get_param('by_range','number_of_trials')
        default_nearest_driver_status = self.env['rb_delivery.nearest_driver'].get_param('by_range','nearest_driver_state')
        default_nearest_driver_status_name='pending_nearest_driver'
        if default_nearest_driver_status and len(default_nearest_driver_status) > 0:
            default_nearest_driver_status_name=self.env['rb_delivery.status'].sudo().search([['id','=',default_nearest_driver_status[0]]]).name

        for driver in drivers:
            assigned_order=self.env['rb_delivery.order'].sudo().search([('assign_to_agent','=',driver.id),('state','in',['picking_up','in_progress',default_nearest_driver_status_name,'picked_up'])])
            total_cod = sum(order.sudo().money_collection_cost for order in assigned_order if order.sudo().money_collection_cost)
            pending_order_existed = False
            if 'next_drivers' in order and order['next_drivers'] and len(order['next_drivers']) > 0 and driver.id in order['next_drivers'].ids:
                pending_order_existed = True
            if pending_order_existed:
                continue
            if len(assigned_order) >= int(number_of_shipments_allowed_with_driver) or (depend_on_cod and total_cod >= max_cod_with_driver):
                continue
            try:
                lon2 = math.radians(float(driver.longitude))
                lat2 = math.radians(float(driver.latitude))
            except:
                continue
            dlon = lon2 - lon1
            dlat = lat2 - lat1
            a = math.sin(dlat / 2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            distance = R * c
            nearest_drivers.append({'driver':driver,'distance':distance})
        nearest_drivers.sort(key=lambda x: x['distance'], reverse=False)
        if len(nearest_drivers) == 0:
            return False
        else:
            drivers_ranges=self.sort_drivers_by_range(nearest_drivers)
            if len(drivers_ranges) > 0:
                for driver_range in drivers_ranges:
                    if driver_range['range'] == range:
                        return driver_range['drivers']



    def sort_drivers_by_range(self,nearest_drivers):
        notification_range_ids = self.env['rb_delivery.nearest_driver'].get_param('by_range','notification_range')
        notification_ranges = self.env['rb_delivery.notification_range'].browse(notification_range_ids)
        drivers_ranges = []
        count=0
        if notification_ranges and len(notification_ranges) > 0:
            for range in notification_ranges:
                drivers_to_add=[]
                count +=1
                for driver in nearest_drivers:
                    if driver['distance'] >= range['from_km'] and driver['distance'] <= range['to_km']:
                        drivers_to_add.append(driver['driver'])
                    else:
                        continue
                drivers_ranges.append({'drivers':drivers_to_add,'range':count})
        return drivers_ranges
    

    @api.multi
    def write(self, values):
        use_holding_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_holding_time')
        start_hold_after_status_id = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'start_hold_after_status_id')
        default_holding_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'default_holding_time')

        if not (use_holding_time and default_holding_time and start_hold_after_status_id and start_hold_after_status_id[0] and 'state' in values):
            return super(olivery_nearest_driver_order, self).write(values)
        start_hold_after_status = self.env['rb_delivery.status'].browse(start_hold_after_status_id[0]).mapped('name')
        if values.get('state') and start_hold_after_status and values['state'] != start_hold_after_status[0]:
            return super(olivery_nearest_driver_order, self).write(values)
        now = datetime.now()
        values['picked_up_time'] = now.strftime('%Y-%m-%d %H:%M:%S')
        for order in self:
            driver = order.assign_to_agent
            if not driver:
                continue

            holding_minutes = default_holding_time
            new_hold_until = now + timedelta(minutes=holding_minutes)

            driver.last_pickup_time = now

            if not driver.hold_until or new_hold_until < driver.hold_until:
                driver.hold_until = new_hold_until

        return super(olivery_nearest_driver_order, self).write(values)

    def get_holding_status(self):
        use_holding_time = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'use_holding_time')
        post_status_name = ''
        if use_holding_time:
            post_hold_status_id = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'post_hold_status_id')
            if post_hold_status_id:
                post_status_name = self.env['rb_delivery.status'].browse(post_hold_status_id[0]).name
        return bool(use_holding_time and post_status_name), post_status_name

    def action_rb_delivery_no_answer_order(self):
        time_value = self.env['rb_delivery.nearest_driver'].get_param('one_by_one','move_to_waiting_after')
        if time_value < 1:
            return True
        time = int(time_value)
        date_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        no_answer_orders = self.env['rb_delivery.order'].search([('state','=','no_answer')])
        if len(no_answer_orders) > 0:
            for order in no_answer_orders:
                order_date = datetime.strftime(order.write_date,'%Y-%m-%d %H:%M:%S')
                diff = datetime.strptime(date_now, '%Y-%m-%d %H:%M:%S') - datetime.strptime(order_date, '%Y-%m-%d %H:%M:%S')
                seconds = diff.seconds
                if seconds > (time * 60):
                    order.write({'state': 'waiting'})
        no_answer_orders.notify_drivers()
        return True

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
