<?xml version="1.0" encoding="UTF-8"?>

<odoo>
    <data>
        <record id="view_form_olivery_nearest_driver_user" model="ir.ui.view"> 
            <field name="name">view_form_olivery_nearest_driver_user</field>
            <field name="model">rb_delivery.user</field>
            <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_user" />
            <field name="arch" type="xml">
                <field name="address" position="after">
                <field name="nearest_drivers_for_business" widget="many2many_tags" attrs="{'invisible':[('role_code','!=','rb_delivery.role_business')]}"/>
                <field name="black_list_drivers" widget="many2many_tags" attrs="{'invisible':[('role_code','!=','rb_delivery.role_business')]}"/>
                <field name="vehicle_type" attrs="{'invisible':[('user_is_driver','=',False)]}"/>   
                <field name="last_pickup_time" attrs="{'invisible':[('user_is_driver','=',False)]}"/>
                <field name="hold_until"  attrs="{'invisible':[('user_is_driver','=',False)]}"/>
                <field name="agent_sequence" attrs="{'invisible':[('user_is_driver','=',False)]}"/>
             </field>
            </field>
        </record>
    </data>
</odoo>
