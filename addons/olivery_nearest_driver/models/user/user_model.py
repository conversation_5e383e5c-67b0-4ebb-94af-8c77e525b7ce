# -*- coding: utf-8 -*-
import logging

from openerp import models, fields

_logger = logging.getLogger(__name__)


class olivery_nearest_driver_order_user(models.Model):

    _inherit = 'rb_delivery.user'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    nearest_drivers_for_business =  fields.Many2many('rb_delivery.user','business_driver_rel','business_id','driver_id',string="Drivers",domain="[('role_code','=','rb_delivery.role_driver')]")
    black_list_drivers = fields.Many2many( 'rb_delivery.user', 'business_driver_blacklist_rel', 'business_id', 'driver_id', string="Black List Drivers", domain="[('role_code','=','rb_delivery.role_driver')]" )
    vehicle_type = fields.Selection([
        ('car', 'Car'),
        ('motorcycle', 'Motorcycle')], track_visibility="on_change",copy=False)
    
    last_pickup_time = fields.Datetime(string="Last Pickup Time")

    hold_until = fields.Datetime(string="Hold Until")

    agent_sequence = fields.Char(string="Agent Sequence")
