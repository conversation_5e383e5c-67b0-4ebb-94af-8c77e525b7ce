# -*- coding: utf-8 -*-
from openerp import models, fields, api
from openerp.exceptions import ValidationError
import pytz

class olivery_nearest_driver_model(models.Model):

    _name = 'rb_delivery.nearest_driver'
    _inherit = 'mail.thread'

    def get_status(self):
        next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        return [('id','in',next_statuses.ids)]
    
    def get_time_zones(self):
        return [(tz, tz) for tz in sorted(pytz.all_timezones, key=lambda tz: tz if not tz.startswith('Etc/') else '_')]


    nearest_driver_algorithem = fields.Selection([('one_by_one','One By One'),('by_range','By range')],track_visibility="on_change")

    number_of_trials = fields.Integer('Number Of Trials',track_visibility="on_change")

    timer_duration = fields.Integer('Timer Duration' , default = 30,track_visibility="on_change")

    number_of_shipments = fields.Integer('Number Of Shipments',track_visibility="on_change")

    depend_on_cod = fields.Boolean('Depend also on COD when assign drivers',track_visibility="on_change")

    max_cod = fields.Integer('Max COD',track_visibility="on_change")

    nearest_driver_state = fields.Many2many(comodel_name = 'rb_delivery.status', 
        string = 'Nearest Driver Status',
        relation = 'nearest_driver_status_table',
        column1 = 'nearest_driver_status_id',track_visibility="on_change",domain=get_status)
    
    notification_range = fields.One2many(comodel_name='rb_delivery.notification_range',
                                         string = 'Notification range',
                                         inverse_name='nearest_driver',
                                         track_visibility="on_change")
    
    time_allowed_to_cancel_the_order = fields.Float('Time to cancel order',track_visibility="on_change")


    shipment_statuses = fields.Many2many(comodel_name = 'rb_delivery.status', 
        string = 'Busy Shipment Statuses',
        relation = 'shipment_status_table',
        column1 = 'shipment_status_id',track_visibility="on_change",domain=get_status)
    
    manual_assigned_driver_statuses = fields.Many2many(comodel_name = 'rb_delivery.status', 
        string = 'Manual Assign Driver Status',
        relation = 'manual_assign_driver_status_table',
        column1 = 'manual_assign_driver_status_id',track_visibility="on_change",domain=get_status)
    
    use_business_drivers = fields.Boolean(string="Use Business Drivers",track_visibility="on_change")

    use_business_system_drivers = fields.Boolean(string="Use Business Drivers Then System Drivers",track_visibility="on_change")

    use_client_location = fields.Boolean(string="Use Client location",track_visibility="on_change")

    use_alt_location = fields.Boolean(string="Use business alternative location",track_visibility="on_change")

    delay_schedule = fields.Boolean(string="Delay scheduled orders",track_visibility="on_change")

    time_zone = fields.Selection(get_time_zones, string='Timezone', default=lambda self: self._context.get('tz'))

    use_nearest_drivers_by_same_vehicle_type = fields.Boolean(string="Use Nearest Drivers with Same Order Type",track_visibility="on_change")

    max_distance = fields.Integer('Max distance (km)',track_visibility="on_change")

    force_assign_to_nearest = fields.Boolean('Force assign to nearest driver',track_visibility="on_change")
    
    default_holding_time = fields.Integer(string="Default Holding Time(By Minute)",default=0)

    move_to_waiting_after = fields.Integer(string='Move to Waiting After time (min)',track_visibility='on_change')

    use_holding_time = fields.Boolean(string="Use Holding Time", default=False,help="Enable to use the default holding time during driver assignment.")

    start_hold_after_status_id = fields.Many2one(comodel_name='rb_delivery.status',string='Start Holding After Status',domain=get_status,track_visibility='on_change',help="The status after which holding time begins for the driver.")

    post_hold_status_id = fields.Many2one(comodel_name='rb_delivery.status',string='Post Hold Status',domain=get_status,track_visibility='on_change',help="Status to assign to orders after driver holding time ends.")
    
    @api.model
    def get_param(self,algorithem,key):
        params = self.search_read([('nearest_driver_algorithem','=',algorithem)],[key], limit=1)
        if len(params) > 0:
           return params[0][key]
        else:
            return False
        
    @api.onchange('use_business_drivers')
    def disable_nearest_drivers_by_same_vehicle_type(self):
        if self.use_business_drivers:
            self.use_nearest_drivers_by_same_vehicle_type = False
            self.use_business_system_drivers = False

    @api.onchange('use_business_system_drivers')
    def disable_nearest_drivers_by_same_vehicle_type(self):
        if self.use_business_system_drivers:
            self.use_nearest_drivers_by_same_vehicle_type = False
            self.use_business_drivers = False

    @api.onchange('use_nearest_drivers_by_same_vehicle_type')
    def disable_business_drivers(self):
        if self.use_nearest_drivers_by_same_vehicle_type:
            self.use_business_drivers = False
            self.use_business_system_drivers = False

    @api.onchange('use_client_location')
    def _onchange_use_client_location(self):
        if self.use_client_location:
            self.use_alt_location = False

    @api.onchange('use_alt_location')
    def _onchange_use_alt_location(self):
        if self.use_alt_location:
            self.use_client_location = False

    @api.one
    def write(self,values):
        old_values = {field: self[field].ids if isinstance(self[field], models.BaseModel) else self[field] for field in values}


        result = super(olivery_nearest_driver_model, self).write(values)

        # Now log the changes
        self.log_changes(old_values, values)

        return result


    def log_changes(self, old_values, new_values):
        messages = []
        for field in new_values:
            field_definition = self._fields.get(field)
            if not field_definition:
                continue

            model_name = field_definition.comodel_name
            field_label = field_definition.string

            old_value = old_values[field]
            new_value = self[field].ids if isinstance(self[field], models.BaseModel) else self[field]

            if isinstance(old_value, list) and isinstance(new_value, list):
                old_status_names = self.get_status_names(model_name, old_value)
                new_status_names = self.get_status_names(model_name, new_value)
                message = f'{field_label} changed from {old_status_names or None} to {new_status_names or None} <br />'

            else:
                continue

            messages.append(message)

        messages_str = ''.join(messages)
        if messages_str:
            self.message_post(body=messages_str)

    def get_status_names(self, model_name, ids):
        status_records = self.env[model_name].browse(ids)
        return [status.name for status in status_records]
