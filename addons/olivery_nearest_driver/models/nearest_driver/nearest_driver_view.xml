<odoo>
    <data>
        <record id="view_form_olivery_nearest_driver_model" model="ir.ui.view"> 
         <field name="name">view_form_olivery_nearest_driver_model</field>
         <field name="model">rb_delivery.nearest_driver</field>
         <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group name="group_top">
                        <field name="nearest_driver_algorithem" />
                        <field name="number_of_trials" attrs="{'invisible':[('force_assign_to_nearest','=',True)]}" />
                        <field name="max_distance" attrs="{'invisible':[('nearest_driver_algorithem','=','by_range')]}"/>
                        <field name="force_assign_to_nearest" attrs="{'invisible':[('nearest_driver_algorithem','=','by_range')]}"/>
                        <field name="timer_duration" string="Timer Duration (second)" attrs="{'invisible':[('force_assign_to_nearest','=',True)]}"/>
                        <field name="time_allowed_to_cancel_the_order" string="Time to cancel order (Hour)" attrs="{'invisible':[('force_assign_to_nearest','=',True)]}"/>
                        <field name="shipment_statuses" widget="many2many_tags"/>
                        <field name="move_to_waiting_after" string="Move to Waiting After (min)" />
                        <field name="depend_on_cod" />
                        <field name="max_cod" attrs="{'invisible':[('depend_on_cod','=',False)]}" />
                        <field name="number_of_shipments"/>
                        <field name="nearest_driver_state" widget="many2many_tags"/>
                        <field name="manual_assigned_driver_statuses" widget="many2many_tags"/>
                        <field name="notification_range"  attrs="{'invisible':[('nearest_driver_algorithem','!=','by_range')]}">
                            <tree string="Notification range" editable="bottom">
                                <field name="from_km" />
                                <field name="to_km" />
                            </tree>
                        </field>
                        <field name="use_business_drivers" />
                        <field name="use_business_system_drivers"/>
                        <field name="use_client_location"/>
                        <field name="use_alt_location"/>
                        <field name="use_nearest_drivers_by_same_vehicle_type" />
                        <field name="delay_schedule" />
                        <field name="time_zone" attrs="{'invisible':[('delay_schedule','!=',True)]}"/>
                    </group>
                    <notebook>
                        <page string="Holding Settings">
                            <group>
                                <field name="use_holding_time"/>
                            </group>
                    
                            <group attrs="{'invisible': [('use_holding_time', '=', False)]}">
                                <field name="default_holding_time"/>                                
                                
                                <field name="start_hold_after_status_id"
                                       options="{'no_create': True}"
                                       />
                                
                                <field name="post_hold_status_id"   options="{'no_create': True}"/>
                            </group>
                        </page>
                    </notebook>
                    
                    
                </sheet>
                <!-- History and communication: -->
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
      </record>

      <record id="view_tree_olivery_nearest_driver_model" model="ir.ui.view"> 
         <field name="name">view_tree_olivery_nearest_driver_model</field>
         <field name="model">rb_delivery.nearest_driver</field>
         <field name="arch" type="xml">
            <tree>
                <field name="nearest_driver_algorithem" />
                <field name="number_of_trials" />
                <field name="timer_duration" />
                <field name="max_distance"/>
                <field name="time_allowed_to_cancel_the_order" />
                <field name="shipment_statuses"/>
                <field name="number_of_shipments" />
                <field name="use_client_location"/>
                <field name="use_alt_location"/>
                <field name="delay_schedule" />
                <field name="nearest_driver_state" widget="many2many_tags"/>
                <field name="manual_assigned_driver_statuses" widget="many2many_tags"/>
                <field name="force_assign_to_nearest" />
                <field name="notification_range"  attrs="{'invisible':[('nearest_driver_algorithem','!=','by_range')]}">
                </field>
            </tree>
        </field>
      </record>
    </data>
</odoo>