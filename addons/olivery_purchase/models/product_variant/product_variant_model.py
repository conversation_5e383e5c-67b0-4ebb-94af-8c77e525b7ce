# -*- coding: utf-8 -*-
from openerp import models, fields,api


class olivery_purchase_product_variant(models.Model):

    _inherit = 'storex_modules.product_variant'

    stock_places = fields.One2many(domain=[['warehouse.is_for_damaged_products','=',False]])

    damaged_stock_places = fields.One2many(
        comodel_name='storex_modules.stock_places',
        inverse_name='product_variant',
        string='Stock Places',
        readonly=True,
        domain=[['warehouse.is_for_damaged_products','=',True]]
    )

    damaged_stock = fields.Float('Damaged Stock',compute="_compute_damaged_stock")

    @api.multi
    def _compute_damaged_stock(self):
        for rec in self:
            sum=0
            if len(rec.damaged_stock_places)>0:
                for stock_place in rec.damaged_stock_places:
                    sum+=stock_place.stock
            rec.damaged_stock=sum