# -*- coding: utf-8 -*-
import webbrowser
from openerp import models, fields,api
from odoo import _

class olivery_purchase_product(models.Model):

    _inherit = 'storex_modules.product'

    average_purchase_cost = fields.Float('Average Purchase Cost',compute = '_compute_average_purchase_cost')

    damaged_count = fields.Integer('Damaged Count',compute='_compute_damaged_count')

    warehouse = fields.Many2one(domain=[['is_for_damaged_products','=',False]])

    @api.multi
    @api.depends('products_variant_ids')
    def _compute_damaged_count(self):
        damaged_stock = 0
        if self.products_variant_ids:
            for product_variant in self.products_variant_ids:
                damaged_stock += product_variant.damaged_stock
        self.damaged_count = damaged_stock


    def _compute_average_purchase_cost(self):
        for rec in self:
            variant_ids = rec.products_variant_ids.ids
            package_items = self.env['olivery_purchase.package_item'].sudo().search_read([['status','=','moved_to_stock'],['product_variant_id','in',variant_ids]],['total_cost','received_quantity'],0,0)
            total_cost = sum(package_item['total_cost'] for package_item in package_items)
            received_quantity = sum(package_item['received_quantity'] for package_item in package_items)
            rec.average_purchase_cost = total_cost / received_quantity if received_quantity else rec.cost

class olivery_purchase_add_stock_wizard(models.TransientModel):
    _inherit = 'storex_modules.add_stock'

    @api.onchange('warehouse_id')
    def location_domain(self):
        if self.warehouse_id:
            return {'domain':{'product_location_id':[('warehouse_id.is_for_damaged_products','=',False),('warehouse_id','=',self.warehouse_id.id)]}}
        else:
            return {'domain':{'product_location_id':[('warehouse_id.is_for_damaged_products','=',False)]}}
    
    @api.onchange('product_location_id')
    def warehouse_domain(self):
        if self.product_location_id:
            return {'domain':{'warehouse_id':[('is_for_damaged_products','=',False),('locations','in',self.product_location_id.id)]}}
        else:
            return {'domain':{'warehouse_id':[('is_for_damaged_products','=',False)]}}