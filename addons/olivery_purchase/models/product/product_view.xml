<odoo>
    <data>
  
    <record id="view_form_storex_product" model="ir.ui.view">
  
        <field name="name">view_form_storex_product</field>
        <field name="model">storex_modules.product</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="storex_modules.view_form_storex_product"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//field[@name='cost']" position="after">
                    <field name="average_purchase_cost"/>  
                </xpath>  
                <xpath expr="//field[@name='stock']" position="after">
                    <field name="damaged_count"/>  
                </xpath> 
            </data>
        </field>
    </record>
    </data>
</odoo>