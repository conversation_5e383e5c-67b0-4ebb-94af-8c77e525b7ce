# -*- coding: utf-8 -*-
from openerp import models, fields,api,_
from odoo.exceptions import UserError




class olivery_purchase_package_item(models.Model):

    _name = 'olivery_purchase.package_item'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
    
    package_id = fields.Many2one('olivery_purchase.package',string='Package')

    custom_cost_per_unit = fields.Char('Custom Cost per unit')

    cost_per_unit = fields.Float(compute="_compute_unit_cost")

    total_cost = fields.Float('Actual Cost',compute="_compute_product_cost")

    expected_total_cost = fields.Float('Expected Cost',compute="_compute_product_cost")

    match = fields.Boolean('Match Expected',compute="_compute_match")

    product_variant_id = fields.Many2one('storex_modules.product_variant')

    product_average_purchase_cost = fields.Float('Average Purchase Cost',compute="_compute_average_purchase_cost")

    expected_quantity = fields.Integer()

    received_quantity = fields.Integer()

    is_damaged = fields.Boolean()

    moved_to_stock = fields.Boolean()

    status = fields.Selection(selection=[('pending','Pending'),('partial_approved','Partial Approved'),('approved','Approved'),('moved_to_stock','Moved To Stock')],compute="_compute_status",store=True)

    warehouse_id = fields.Many2one(related='package_id.warehouse_id')

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    def _compute_average_purchase_cost(self):
        for rec in self:
            rec.product_average_purchase_cost = rec.product_variant_id.product_id.average_purchase_cost

    @api.depends('expected_total_cost','total_cost','expected_quantity','received_quantity')
    def _compute_match(self):
        for rec in self:
            rec.match = rec.expected_quantity == rec.received_quantity and rec.expected_total_cost == rec.total_cost

    @api.depends('product_variant_id')
    def _compute_unit_cost(self):
        for rec in self:
            rec.cost_per_unit = rec.product_variant_id.product_id.cost

    @api.depends('cost_per_unit','product_variant_id','received_quantity','expected_quantity')
    def _compute_product_cost(self):
        for rec in self:
            try:
                cost = float(rec.custom_cost_per_unit) if rec.custom_cost_per_unit is not False and rec.custom_cost_per_unit is not None and rec.custom_cost_per_unit!='' else rec.product_variant_id.product_id.cost
                rec.total_cost = rec.received_quantity * cost
            except:
                rec.total_cost = rec.received_quantity * rec.product_variant_id.product_id.cost

            rec.expected_total_cost = rec.expected_quantity * rec.product_variant_id.product_id.cost

    @api.one
    @api.depends('received_quantity','expected_quantity','moved_to_stock')
    def _compute_status(self):
        if self.moved_to_stock:
            self.status='moved_to_stock'
        elif self.received_quantity==0:
            self.status='pending'
        elif self.received_quantity < self.expected_quantity:
            self.status='partial_approved'
        else:
            self.status='approved'

    @api.one
    def move_to_stock(self):
        if self.status == 'approved':
            self.env['storex_modules.warehouse'].add_stock(self.received_quantity,self.product_variant_id.id,self.warehouse_id.id)
            self.moved_to_stock = True
        else:
            raise UserError(_('All Package Items Must Be Approved Before Moving To Stock'))

    
        
    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ---------------------------------------------------------- 
    @api.model   
    def create(self,values):
        if 'expected_quantity' in values and values['expected_quantity'] <= 0:
            raise UserError(_('Expected Quantity Must Be More Than 0'))
        return super(olivery_purchase_package_item,self).create(values)
    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------