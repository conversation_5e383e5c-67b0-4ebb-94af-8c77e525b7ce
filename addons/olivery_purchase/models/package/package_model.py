# -*- coding: utf-8 -*-

from openerp import models, fields, api,_
import barcode
from barcode.writer import ImageWriter
import io
import base64


class olivery_purchase_package(models.Model):

    _name = 'olivery_purchase.package'
    _order = "sequence DESC"

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    sequence = fields.Char()

    stock_provider = fields.Many2one('olivery_purchase.stock_provider')

    barcode = fields.Binary('Barcode', compute="create_barcode")
    
    package_items = fields.One2many('olivery_purchase.package_item','package_id')

    warehouse_id = fields.Many2one('storex_modules.warehouse',required=True)

    package_type = fields.Selection([('purchase','Purchase'),('returned','Returned')],default='purchase')

    order_id = fields.Many2one('storex_modules.order')

    status = fields.Selection(selection=[('pending','Pending'),('partial_approved','Partial Approved'),('approved','Approved'),('moved_to_stock','Moved To Stock')],compute="_compute_status",store=True)


    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded

    @api.one
    @api.depends('package_items')
    def _compute_status(self):
        pending_items = self.package_items.filtered(lambda x:x.status=='pending')
        if len(pending_items) == len(self.package_items):
            self.status='pending'
            return
        partial_approved_items = self.package_items.filtered(lambda x:x.status=='partial_approved')
        if len(partial_approved_items) > 0:
            self.status='partial_approved'
            return
        approved_items = self.package_items.filtered(lambda x:x.status=='approved')
        if len(approved_items) == len(self.package_items):
            self.status='approved'
            return
        elif len(pending_items)>0:
            self.status='partial_approved'
            return
        moved_to_stock_items = self.package_items.filtered(lambda x:x.status=='moved_to_stock')
        if len(moved_to_stock_items) == len(self.package_items):
            self.status='moved_to_stock'
            return
        else:
            self.status='partial_approved'
            return

    @api.one    
    def move_package_to_stock(self):
        for package_item in self.package_items:
            package_item.move_to_stock()
        self._compute_status()

    @api.one
    def approve_whole_package(self):
        for package_item in self.package_items:
            package_item.received_quantity=package_item.expected_quantity
        self._compute_status()
    
    @api.one
    def move_unapproved_to_another_package(self):
        unapproved_items = self.package_items.filtered(lambda x:x.status!='approved' and x.status!='moved_to_stock')
        if len(unapproved_items)>0:
            new_package_items=[]
            for package_item in unapproved_items:
                if package_item.status=='partial_approved':
                    new_package_items.append((0,0,{
                        'expected_quantity':package_item.expected_quantity-package_item.received_quantity,
                        'product_variant_id':package_item.product_variant_id.id
                        }
                    ))
                    package_item.expected_quantity=package_item.received_quantity
                elif package_item.status=='pending':
                    new_package_items.append((0,0,{
                        'expected_quantity':package_item.expected_quantity,
                        'product_variant_id':package_item.product_variant_id.id
                        }
                    ))
                    package_item.unlink()
            self.env['olivery_purchase.package'].create({'package_items':new_package_items,'warehouse_id':self.warehouse_id.id})
        self._compute_status()
    
    @api.model
    def create(self,values):
        values['sequence']=self.env['ir.sequence'].next_by_code('olivery_purchase.package')
        return super(olivery_purchase_package,self).create(values)

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ---------------------------------------------------------- 

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------