<odoo>
  <data>

    <template id="assets_backend" inherit_id="web.assets_backend">
        <xpath expr="." position="inside">
            <script src="/olivery_purchase/static/src/js/list_controller.js" type="text/javascript"/>
        </xpath>
    </template>

   


    <record id="view_form_olivery_returned_package" model="ir.ui.view">

      <field name="name">view_form_olivery_returned_package</field>
      <field name="model">olivery_purchase.package</field>

      <field name="arch" type="xml">

        <form>
      
          <header>
            <button attrs="{'invisible':[['status','in',['moved_to_stock','pending','partial_approved']]]}" name="move_package_to_stock" confirm="Do you want to proceed?" type="object" string="Move All Items To Stock" class="oe_highlight" />
            <button attrs="{'invisible':[['status','in',['moved_to_stock','approved','partial_approved']]]}"  name="approve_whole_package" confirm="Do you want to proceed?" type="object" string="Approve All Items" class="oe_highlight" />
            <button attrs="{'invisible':[['status','in',['moved_to_stock','approved','pending']]]}"  name="move_unapproved_to_another_package" confirm="Do you want to proceed?" type="object" string="Move Unapproved To Another Package" class="oe_highlight" />
          </header>

          <sheet> 
          <field name="write_date" invisible="1"/>
          <field name="id" invisible="1"/>
          <field name="sequence" invisible="1"/>
          <div style="display:inline-block !important; width:50% !important">
            <field name="barcode" style="display:block;text-align:center;width:100%;  height:75px !important" height="75" width="40%" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('write_date', '=', False)]}"/>
            <label for="seqeuence" string="Seqeuence" style="width:100% !important;text-align:center !important;" attrs="{'invisible': ['|',('id', '=', False),('sequence','=',False)]}"/>
            <field name="sequence" string="Sequence Number" style="display:block;text-align:center;" attrs="{'invisible':[('write_date', '=', False)]}" readonly="1"/>

          </div>
            <group>
              <field name="warehouse_id" attrs="{'readonly':[['status','=','moved_to_stock']]}"/>
              <field name="order_id" readonly="1"/>
              <field name="status"/>
              <field name="package_items" attrs="{'readonly':[['status','=','moved_to_stock']]}">
                <tree editable="bottom" decoration-danger ="not match" decoration-success ="match">
                  <field name="match" invisible="1"/>
                  <field name="product_variant_id"/>
                  <field name="expected_quantity"/>
                  <field name="received_quantity"/>
                  <field name="status"/>
                </tree>
              </field>
            </group>
          </sheet>
      </form>

      </field>
    </record>


    <record id="view_tree_olivery_returned_package" model="ir.ui.view">

      <field name="name">view_tree_olivery_returned_package</field>
      <field name="model">olivery_purchase.package</field>

      <field name="arch" type="xml">

        <tree decoration-danger="status=='pending'" decoration-info="status=='moved_to_stock'" decoration-success="status=='approved'">
          <field name="sequence" string="Sequence Number" readonly="1"/>
          <field name="order_id" readonly="1"/>
          <field name="warehouse_id"/>
          <field name="package_items"/>
          <field name="status"/>
        </tree>

      </field>
    </record>




    <record id="view_form_olivery_purchase_package" model="ir.ui.view">
      <field name="name">view_form_olivery_purchase_package</field>
      <field name="model">olivery_purchase.package</field>

      <field name="arch" type="xml">

        <form>
      
          <header>
            <button attrs="{'invisible':[['status','in',['moved_to_stock','pending','partial_approved']]]}" name="move_package_to_stock" confirm="Do you want to proceed?" type="object" string="Move All Items To Stock" class="oe_highlight" />
            <button attrs="{'invisible':[['status','in',['moved_to_stock','approved','partial_approved']]]}"  name="approve_whole_package" confirm="Do you want to proceed?" type="object" string="Approve All Items" class="oe_highlight" />
            <button attrs="{'invisible':[['status','in',['moved_to_stock','approved','pending']]]}"  name="move_unapproved_to_another_package" confirm="Do you want to proceed?" type="object" string="Move Unapproved To Another Package" class="oe_highlight" />
          </header>

          <sheet> 
          <field name="write_date" invisible="1"/>
          <field name="id" invisible="1"/>
          <field name="sequence" invisible="1"/>
          <div style="display:inline-block !important; width:50% !important">
            <field name="barcode" style="display:block;text-align:center;width:100%;  height:75px !important" height="75" width="40%" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('write_date', '=', False)]}"/>
            <label for="seqeuence" string="Seqeuence" style="width:100% !important;text-align:center !important;" attrs="{'invisible': ['|',('id', '=', False),('sequence','=',False)]}"/>
            <field name="sequence" string="Sequence Number" style="display:block;text-align:center;" attrs="{'invisible':[('write_date', '=', False)]}" readonly="1"/>

          </div>
            <group>
              <field name="warehouse_id" attrs="{'readonly':[['status','=','moved_to_stock']]}"/>
              <field name="stock_provider" attrs="{'readonly':[['status','=','moved_to_stock']]}"/>
              <field name="status"/>
              <field name="package_items" attrs="{'readonly':[['status','=','moved_to_stock']]}">
                <tree editable="bottom" decoration-danger ="not match" decoration-success ="match">
                  <field name="match" invisible="1"/>
                  <field name="product_variant_id"/>
                  <field name="product_average_purchase_cost"/>
                  <field name="cost_per_unit"/>
                  <field name="expected_quantity"/>
                  <field name="expected_total_cost" sum="Expected Total Cost For All Products"/>
                  <field name="custom_cost_per_unit"/>
                  <field name="received_quantity"/>
                  <field name="total_cost" sum="Total Cost For All Products"/>
                  <field name="status"/>
                </tree>
              </field>
            </group>
          </sheet>
      </form>

      </field>

    </record>


    <record id="view_tree_olivery_purchase_package" model="ir.ui.view">
      <field name="name">view_tree_olivery_purchase_package</field>
      <field name="model">olivery_purchase.package</field>

      <field name="arch" type="xml">

        <tree decoration-danger="status=='pending'" decoration-info="status=='moved_to_stock'" decoration-success="status=='approved'" >
          <field name="sequence" string="Sequence Number" readonly="1"/>
          <field name="stock_provider"/>
          <field name="warehouse_id"/>
          <field name="package_items"/>
          <field name="status"/>
        </tree>


      </field>

    </record>

  </data>

</odoo>