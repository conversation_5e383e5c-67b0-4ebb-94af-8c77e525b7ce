# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_purchase
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-29 13:25+0000\n"
"PO-Revision-Date: 2024-08-29 13:25+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "<span>Date:..................</span>"
msgstr "<span>التاريخ:..................</span>"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "<span>Notes:</span>"
msgstr "<span>ملاحظات:</span>"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "<span>Received By:</span>"
msgstr "<span>المستلم:</span>"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "<span>Sent By:</span>"
msgstr "<span>المرسل:</span>"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "<span>Signiture:..................</span>"
msgstr "التوقيع:..................</span>"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__total_cost
msgid "Actual Cost"
msgstr "التكلفه الحقيقيه"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/models/package_item/package_item_model.py:91
#: code:addons/olivery_purchase/models/package_item/package_item_model.py:91
#, python-format
msgid "All Package Items Must Be Approved Before Moving To Stock"
msgstr "جميع عناصر البكج يجب ان تكون موافق عليها قبل الاضافة الى المخزون"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
msgid "Approve All Items"
msgstr "قبول جميع العناصر"

#. module: olivery_purchase
#: selection:olivery_purchase.package,status:0
#: selection:olivery_purchase.package_item,status:0
msgid "Approved"
msgstr "مقبول"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__product_average_purchase_cost
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_product__average_purchase_cost
msgid "Average Purchase Cost"
msgstr "معدل سعر الشراء"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__barcode
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Barcode"
msgstr "باركود"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_proccess_returned
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__cost_per_unit
msgid "Cost Per Unit"
msgstr "تكلفه الوحدة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__create_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__create_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__create_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__create_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__create_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__create_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__custom_cost_per_unit
msgid "Custom Cost per unit"
msgstr "التكلفه المعدلة للوحدة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_order_line__damaged_count
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_product__damaged_count
msgid "Damaged Count"
msgstr "عدد العناصر التالفه"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__warehouse_to_return_damaged
msgid "Damaged Products Warehouse"
msgstr "مخزن المنتجات التالفه"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Date:"
msgstr "التاريخ:"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__display_name
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__display_name
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__display_name
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
msgid "Do you want to proceed?"
msgstr "هل تريد اتمام العملية؟"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Driver:\n"
"                                <span>...................</span>"
msgstr "السائق:\n"
""

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__expected_total_cost
msgid "Expected Cost"
msgstr "التكلفة المتوقعة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__expected_quantity
msgid "Expected Quantity"
msgstr "الكمية المتوقعة"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/models/package_item/package_item_model.py:101
#: code:addons/olivery_purchase/models/package_item/package_item_model.py:101
#, python-format
msgid "Expected Quantity Must Be More Than 0"
msgstr "الكمية المتوقعة يجب ان تكون اكثر من 0"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
msgid "Expected Total Cost For All Products"
msgstr "التكلفة الاجمالية المتوقعة لجميع المنتجات"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__id
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__id
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__id
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__is_damaged
msgid "Is Damaged"
msgstr "هل هو تالف؟"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_warehouse__is_for_damaged_products
msgid "Is for damaged products"
msgstr "هل هو للعناصر التالفه؟"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package____last_update
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item____last_update
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned____last_update
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__write_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__write_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__write_uid
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__write_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__write_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__write_date
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Logo"
msgstr "الشعار"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/models/storex_order_wizard/storex_order_wizard_model.py:24
#: code:addons/olivery_purchase/models/storex_order_wizard/storex_order_wizard_model.py:24
#, python-format
msgid "Make sure all the orders are in Processing Returned status and their stock is decremented"
msgstr "تاكد من ان جميه الطرود هي في حاله جاري معالجه المرجع وتم انقاص كميتها من المخزن"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__match
msgid "Match Expected"
msgstr "مماثل للمتوقع"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
msgid "Move All Items To Stock"
msgstr "اضافة جميع العناصر الى المخزون"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
msgid "Move Unapproved To Another Package"
msgstr "اضافة الغير موافق عليهم لبكج اخر"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__moved_to_stock
#: selection:olivery_purchase.package,status:0
#: selection:olivery_purchase.package_item,status:0
msgid "Moved To Stock"
msgstr "اضافة الى المخزون"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_stock_provider__name
msgid "Name"
msgstr "الاسم"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__order_id
msgid "Order"
msgstr "الأمر"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__order_line_ids
msgid "Order Line"
msgstr "بنو الطلبية"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_proccess_returned
msgid "Ordered Quantity"
msgstr "الكمية المطلوبة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__package_id
msgid "Package"
msgstr "حزمه"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__package_items
msgid "Package Items"
msgstr "عناصر البكج"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/controllers/controllers.py:30
#: code:addons/olivery_purchase/controllers/controllers.py:30
#, python-format
msgid "Package Items/Expected Quantity"
msgstr "عناصر الطلبيه/الكمية المتوقعة"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/controllers/controllers.py:30
#: code:addons/olivery_purchase/controllers/controllers.py:30
#, python-format
msgid "Package Items/Product Varian"
msgstr "عناصر الطلبية/تصنيف المنتج"

#. module: olivery_purchase
#: model:ir.actions.report,name:olivery_purchase.report_storex_modules_package_detail_storex
msgid "Package Report"
msgstr "تقرير البضائع"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__package_type
msgid "Package Type"
msgstr "نوع الحزمه"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Package report"
msgstr "تقرير البضائع"

#. module: olivery_purchase
#: selection:olivery_purchase.package,status:0
#: selection:olivery_purchase.package_item,status:0
msgid "Partial Approved"
msgstr "مقبول جزئي"

#. module: olivery_purchase
#: selection:olivery_purchase.package,status:0
#: selection:olivery_purchase.package_item,status:0
msgid "Pending"
msgstr "في الانتظار"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_proccess_returned
msgid "Please choose quantities to return from bellow"
msgstr "يرجى اختيار الكميات المرجعة "

#. module: olivery_purchase
#: model:ir.actions.act_window,name:olivery_purchase.action_rb_delivery_proccess_returned
msgid "Proccess Returned"
msgstr "معالجة المرجعات"

#. module: olivery_purchase
#: model:rb_delivery.status,title:olivery_purchase.status_storex_processing_returned
msgid "Processing  Returned"
msgstr "جاري معالجه المرجع"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Product"
msgstr "المنتج"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_proccess_returned
msgid "Product Image"
msgstr "صورة المنتج"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__product_variant_id
msgid "Product Variant"
msgstr "موديل المنتج"

#. module: olivery_purchase
#: selection:olivery_purchase.package,package_type:0
msgid "Purchase"
msgstr "مشتريات"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Quantity"
msgstr "الكمية"

#. module: olivery_purchase
#: model:ir.actions.act_window,name:olivery_purchase.action_olivery_purchase_package
#: model:ir.ui.menu,name:olivery_purchase.menu_olivery_purchase_package
msgid "Received Packages"
msgstr "البضائع المستلمة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__received_quantity
msgid "Received Quantity"
msgstr "الكمية المستلمة"

#. module: olivery_purchase
#: selection:olivery_purchase.package,package_type:0
msgid "Returned"
msgstr "مرجعات"

#. module: olivery_purchase
#: model:rb_delivery.status,title:olivery_purchase.status_returned_to_storex
msgid "Returned To Storex"
msgstr "مرجع لستوركس"

#. module: olivery_purchase
#: model:ir.actions.act_window,name:olivery_purchase.action_olivery_returned_products
#: model:ir.ui.menu,name:olivery_purchase.menu_olivery_returned_package
msgid "Returned products"
msgstr "المنتجات الراجعه"

#. module: olivery_purchase
#. openerp-web
#: code:addons/olivery_market/olivery_purchase/static/src/xml/excel_template.xml:7
#: code:addons/olivery_purchase/static/src/xml/excel_template.xml:7
#, python-format
msgid "Sample File"
msgstr ""

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_proccess_returned
msgid "Save"
msgstr "حفظ"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
msgid "Seqeuence"
msgstr "رقم المتسلسل"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_returned_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_tree_olivery_purchase_package
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_tree_olivery_returned_package
msgid "Sequence Number"
msgstr "الرقم المتسلسل"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__status
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__status
msgid "Status"
msgstr "الحالة"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__stock_provider
msgid "Stock Provider"
msgstr "المزود"

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_order__stock_warehouse
msgid "Stock Warehouse"
msgstr "المخزن"

#. module: olivery_purchase
#: model:ir.ui.menu,name:olivery_purchase.menu_olivery_supplies
msgid "Supplies"
msgstr "واردات"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/models/warehouse/warehouse_model.py:22
#: code:addons/olivery_purchase/models/warehouse/warehouse_model.py:22
#, python-format
msgid "This warehouse is for damaged products and can't be set as default"
msgstr "هذا المخزن مخصص للمنتجات التالفه ولا يمكن استخدانه كافتراضي"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.view_form_olivery_purchase_package
msgid "Total Cost For All Products"
msgstr "السعر الاجمالي لجميع المنتجات"

#. module: olivery_purchase
#: model_terms:ir.ui.view,arch_db:olivery_purchase.package_detail_storex
msgid "Unit Cost"
msgstr "تكلفة الوحدة"

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_rb_delivery_user
msgid "User Model"
msgstr "المستخدم"

#. module: olivery_purchase
#: code:addons/olivery_market/olivery_purchase/controllers/controllers.py:30
#: code:addons/olivery_purchase/controllers/controllers.py:30
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package__warehouse_id
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_package_item__warehouse_id
#: model:ir.model.fields,field_description:olivery_purchase.field_olivery_purchase_proccess_returned__warehouse_to_return
#: model:ir.model.fields,field_description:olivery_purchase.field_rb_delivery_user__warehouse
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_product__warehouse
#, python-format
msgid "Warehouse"
msgstr "المخزن"

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_olivery_purchase_package
msgid "olivery_purchase.package"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_olivery_purchase_package_item
msgid "olivery_purchase.package_item"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_olivery_purchase_proccess_returned
msgid "olivery_purchase.proccess_returned"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_olivery_purchase_stock_provider
msgid "olivery_purchase.stock_provider"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_storex_modules_order
msgid "storex_modules.order"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_storex_modules_order_line
msgid "storex_modules.order.line"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_storex_modules_product
msgid "storex_modules.product"
msgstr ""

#. module: olivery_purchase
#: model:ir.model,name:olivery_purchase.model_storex_modules_warehouse
msgid "storex_modules.warehouse"
msgstr ""

#. module: olivery_purchase
#: model:ir.model.fields,field_description:olivery_purchase.field_storex_modules_product_variant__damaged_stock
msgid "Damaged Stock"
msgstr "المخزون التالف"

