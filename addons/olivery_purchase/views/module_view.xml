<?xml version="1.0"?>

<odoo>
    <data>
        <menuitem id="menu_olivery_supplies" name="Supplies" parent="storex_modules.storex_top_menu" sequence="10" groups="rb_delivery.role_super_manager,rb_delivery.role_warehouse_manager,rb_delivery.role_manager,base.group_system" />

        <record model="ir.actions.act_window" id="action_olivery_purchase_package">
            <field name="name">Received Packages</field>
            <field name="res_model">olivery_purchase.package</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[
                [6,0,[]],
                [0,0,{
                    'view_mode':'form',
                    'view_id':ref('olivery_purchase.view_form_olivery_purchase_package'),
                    'sequence':2
                }],
                [0,0,{
                    'view_mode':'tree',
                    'view_id':ref('olivery_purchase.view_tree_olivery_purchase_package'),
                    'sequence':1
                }],
            ]"></field>
            <field name="limit" eval="150"/>
            <field name="domain">[['package_type','=','purchase']]</field>
        </record>
        <menuitem id="menu_olivery_purchase_package" name="Received Packages" parent="menu_olivery_supplies" sequence="10" action="action_olivery_purchase_package" groups="rb_delivery.role_super_manager,rb_delivery.role_warehouse_manager,rb_delivery.role_manager,base.group_system" />

        <record model="ir.actions.act_window" id="action_olivery_returned_products">
            <field name="name">Returned products</field>
            <field name="res_model">olivery_purchase.package</field>
            <field name="view_ids" eval="[
                [6,0,[]],
                [0,0,{
                    'view_mode':'form',
                    'view_id':ref('olivery_purchase.view_form_olivery_returned_package'),
                    'sequence':2
                }],
                [0,0,{
                    'view_mode':'tree',
                    'view_id':ref('olivery_purchase.view_tree_olivery_returned_package'),
                    'sequence':1
                }],
            ]"></field>
            <field name="limit" eval="150"/>
            <field name="context">{'group_by': ['order_id']}</field>
            <field name="domain">[['package_type','=','returned']]</field>
        </record>
        <menuitem id="menu_olivery_returned_package" name="Returned products" parent="menu_olivery_supplies" sequence="10" action="action_olivery_returned_products" groups="rb_delivery.role_super_manager,rb_delivery.role_warehouse_manager,rb_delivery.role_manager,base.group_system" />

        <act_window id="action_rb_delivery_proccess_returned" name="Proccess Returned" src_model="storex_modules.order" res_model="olivery_purchase.proccess_returned" view_type="form" view_mode="form" key2="client_action_multi" target="new"/>

    </data>
</odoo>