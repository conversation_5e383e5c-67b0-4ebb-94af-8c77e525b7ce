# -*- coding: utf-8 -*-
{
    'name': "olivery_purchase",
    'summary': """
        <PERSON>y Purchase from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.0.8',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery','storex_modules'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/package/package_view.xml',
        'data/package_sequence.xml',
        'views/module_view.xml',
        'views/report/package_report.xml',
        'models/product/product_view.xml',
        'models/product_variant/product_variant_view.xml',
        'models/warehouse/warehouse_view.xml',
        'models/storex_order_wizard/storex_order_wizard_view.xml',
        'demo/status.xml',
        'demo/warehouse.xml'
    ],
    'qweb': [
        'static/src/xml/*.xml'
    ]
}