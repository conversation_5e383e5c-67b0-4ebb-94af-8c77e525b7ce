odoo.define('olivery_purchase.list_controller_custom_packages', function (require) {
	"use strict";

    var ListController = require('web.ListController');
    var session = require('web.session');
    var globalSelf;
    var WebClient = require('web.WebClient');
    WebClient.include({
        init: function(parent, client_options){
            globalSelf = this;
            this._super(parent, client_options);
        }});
    ListController.include({



        renderButtons: function($node) {
            this._super.apply(this, arguments);
            var lang = session.user_context.lang
            if (this.$buttons) {
                //Tree view buttons

                this.$buttons.find('.oe_action_button_olivery_purchase_sample_excel_template').click(this.proxy('sample_excel_action_purchase_def'));

            }
        },
         
        sample_excel_action_purchase_def: function () {
            debugger
            var action = {
                'type': 'ir.actions.act_url',
                'url': '/purchase-invoicing/excel_report/'+this.modelName,
                'target': 'new',
            }
            this.do_action(action)


        }
    });
})






