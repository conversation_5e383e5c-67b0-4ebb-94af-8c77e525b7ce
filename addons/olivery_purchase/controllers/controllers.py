# -*- coding: utf-8 -*-

import odoo
from odoo.http import request
from openerp.http import request
from openerp import http,_
from odoo.http import content_disposition
import xlsxwriter
from io import BytesIO


class olivery_purchase(http.Controller):

    @http.route(['/purchase-invoicing/excel_report/<string:model_name>',], type='http', auth="user", csrf=False)
    def get_excel_report_custome(self,model_name, **args):
        if model_name == 'olivery_purchase.package':
            file_name = 'purchase_package_template'
            response = request.make_response(
                None,
                headers=[
                ('Content-Type', 'application/vnd.ms-excel'),
                ('Content-Disposition', content_disposition(file_name + '.xls'))
            ]
            )
            output = BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet("Template")
            header = []
            warehouse_list = request.env['storex_modules.warehouse'].search([]).mapped('name')
            header = [_("Warehouse"),_("Package Items/Product Varian"),_("Package Items/Expected Quantity")]
            sheet.data_validation('A2:A1048576', {'validate': 'list','source': warehouse_list})
            


        index = 0
        for item in header:
            sheet.write(0, index, _(item))
            index += 1



        workbook.close()
        output.seek(0)
        response.stream.write(output.read())
        output.close()
        return response

