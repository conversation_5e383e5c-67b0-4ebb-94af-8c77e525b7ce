<?xml version="1.0" encoding="UTF-8"?>
  <template xml:space="preserve">
    <t t-extend="ListView.buttons">
      <t t-jquery="div.o_list_buttons" t-operation="prepend">
         <button  t-if=" widget.modelName == 'rb_delivery.branch_returned_collection' || widget.modelName == 'rb_delivery.branch_financial_collection'||widget.modelName == 'rb_delivery.branch_collection'||widget.modelName == 'rb_delivery.branch_financial'"  class="btn btn-secondary oe_action_branch_collection_button_show_orders"
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager">Show orders</button>

         <button  t-if="widget.modelName == 'rb_delivery.branch_financial'"  class="btn btn-secondary oe_action_create_branch_budget"
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager">Branch budget</button>

        </t>

    </t>

  </template>
