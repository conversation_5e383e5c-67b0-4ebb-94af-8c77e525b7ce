<odoo>
    <data>
        <!-- inherit module [olivery_accounting] -->
        <record id="view_form_olivery_branch_collection_branch_financial_collection" model="ir.ui.view">

            <field name="name">view_form_olivery_branch_collection_branch_financial_collection</field>
            <field name="model">rb_delivery.branch_financial_collection</field>

            <field name="arch" type="xml">

                <form create="false">

                    <header>
                        <button name="wkf_action_change_status" string="Change status" type="object" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_business,base.group_system"/>
                        <field name="state" widget="statusbar" statusbar_visible=" "/>
                    </header>

                    <sheet>
                    <div class="oe_button_box o_full" name="button_box" attrs="{'invisible':[('write_date', '=', False)]}">
                        <button type="object" name="get_orders" class="btn btn-sm oe_stat_button o_form_invisible">
                            <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                            <div class="o_form_field o_stat_info" data-original-title="" title="">
                                <span>Orders</span>
                            </div>
                        </button>
                    </div>
                        <group name="group_top">
                            <group name="group-right">
                            <field name="write_date" invisible='1'/>
                                <field name="name"/>
                                <field name="create_date" readonly="1"/>
                                <field name="state" readonly="1"/>
                                <field name="create_uid" readonly="1"/>
                                <field name="creator_branch" />
                                <field name="branch_id" />
                                <separator string="Totals:"/>
                                <field name="branch_cost" readonly="1"/>
                            </group>
                            <group name="group-left">
                            <div class="oe_right" >
                            <field name="barcode" style="display:block;text-align:center;width:200px" height="100" width="200" widget="image" class="oe_center" nolabel="1"/>
                            <field name="sequence" string="Sequence Number" style="display:block;text-align:center;width:200px" />
                            </div>
                            </group>
                        </group>
                        <group name="group_top">
                            <field name="is_branch_financial_manager" invisible="1"/>
                            <field name="branch_financial_ids" attrs="{'readonly': [('is_branch_financial_manager', '=', False)]}">
                                <tree editable="false" delete="1" create="0">
                                    <field name="origin_branch" readonly="1"/>
                                    <field name="destination_branch" readonly="1"/>
                                    <field name="to_area" readonly="1"/>
                                    <field name="cost" readonly="1"/>
                                    <field name="branch_revenue" sum="revenue_sum" readonly="1"/>
                                    <field name="destination_branch_revenue" sum="revenue_sum" readonly="1"/>
                                    <field name="money_collection_cost" sum="money_collection_sum" readonly="1"/>
                                    <field name="extra_fee" sum="extra_fee_sum" readonly="1"/>
                                    <field name="discount" sum="discount_sum" readonly="1"/>
                                    <field name="status" readonly="1"/>
                                    <field name="cod" sum="cod_sum" readonly="1"/>
                                    <field name="origin_branch_display" readonly="1"/>
                                    </tree>
                            </field>
                        </group>
                    </sheet>
                    <!-- History and communication: -->
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                    <footer></footer>

                </form>


            </field>
        </record>
        <record id="view_tree_rb_delivery_branch_financial_collection" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_branch_financial_collection</field>
            <field name="model">rb_delivery.branch_financial_collection</field>
            <field name="arch" type="xml">
                <tree create="false">
                    <field name="sequence" readonly="1"/>
                    <field name="name"/>
                    <field name="state" readonly="1"/>
                    <field name="branch_id" />
                    <field name="origin_branch"/>
                    <field name="destination_branch"/>
                    <field name="create_uid" readonly="1"/>
                    <field name="create_date" readonly="1"/>
                    <field name="branch_cost_view" readonly="1" sum="Total cost"/>
                </tree>
            </field>
        </record>

        <record id="view_search_rb_delivery_branch_financial_collection" model="ir.ui.view">
            <field name="name">view_search_rb_delivery_branch_financial_collection</field>
            <field name="model">rb_delivery.branch_financial_collection</field>
            <field name="arch" type="xml">
                <search>
                    <group>
                        <field name="sequence"/>
                        <field name="create_uid"/>
                        <field name="create_date"/>
                        <field name="state"/>
                        <field name="branch_id" />
                    </group>
                    <group string="Filters">
                        <filter name="paid_collections" string="Paid Collections" domain="[('state','=','paid')]"/>
                        <filter name="unpaid_collections" string="Unpaid Collections" domain="[('state','!=','paid')]"/>
                    </group>
                    <group string="Groups">
                        <filter name="group_by_branch_id" string="By Branch" icon="terp-partner" context="{'group_by':'branch_id'}"/>
                        <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
                        <filter name="group_by_day" string="By Date" icon="terp-partner" context="{'group_by':'create_date:day'}"/>
                        <filter name="group_by_state" string="By State" icon="terp-partner" context="{'group_by':'state'}"/>
                    </group>
                </search>
            </field>
        </record>
    </data>
</odoo>