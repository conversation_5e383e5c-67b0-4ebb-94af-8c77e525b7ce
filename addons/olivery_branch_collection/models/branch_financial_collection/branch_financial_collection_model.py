# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import ValidationError, Warning
from datetime import datetime
import base64
import io
import barcode
from barcode.writer import ImageWriter
from datetime import datetime

class branch_financial_collection(models.Model):
    _name = 'rb_delivery.branch_financial_collection'
    _inherit = 'mail.thread'
    _order = "create_date DESC"
    _description = "Branch financial collection Model"

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','branch_financial_collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None
    
    def default_is_branch_financial_manager(self):
        user = self.env['res.users'].browse(self._uid)
        is_branch_financial_manager = user.has_group('olivery_branch_collection.role_branch_financial_manager')
        return is_branch_financial_manager
    
    @api.multi
    def compute_branch_financial_manager(self):
        user = self.env['res.users'].browse(self._uid)
        is_branch_financial_manager = user.has_group('olivery_branch_collection.role_branch_financial_manager')
        for rec in self:
            rec.is_branch_financial_manager = is_branch_financial_manager

    is_branch_financial_manager = fields.Boolean('Is branch financial manager',default=default_is_branch_financial_manager,compute="compute_branch_financial_manager")

    name = fields.Char('Note')

    branch_money_collection_cost_total = fields.Float('Total money collection cost',track_visibility=False,copy=False,compute="compute_branch_revenue_total")

    branch_cost_total = fields.Float('Branch cost total',track_visibility=False,copy=False,compute="compute_branch_revenue_total")

    branch_revenue_total = fields.Float('Branch revenue total',track_visibility="on_change",copy=False,compute="compute_branch_revenue_total")

    branch_cost = fields.Float('Required to Branch',track_visibility="on_change",copy=False)

    branch_financial_ids = fields.Many2many(
        comodel_name = 'rb_delivery.branch_financial',
        string = 'Branch financial',
        relation = 'branch_financial_collection_branch_financial_item',
        column1 = 'branch_financial_collection_id',
        column2 = 'branch_financial_id',readonly=False)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'branch_financial_collection_order_item',
        column1 = 'branch_financial_collection_id',
        column2 = 'order_id',compute="compute_branch_revenue_total",store=True)

    sequence = fields.Char('Sequence', readonly=True,track_visibility=False,copy=False)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    origin_branch = fields.Many2one('rb_delivery.branch','Origin branch', track_visibility="on_change",copy=False,readonly=True)

    branch_id = fields.Many2one('rb_delivery.branch','Branch', track_visibility="on_change",copy=False,readonly=True)

    destination_branch = fields.Many2one('rb_delivery.branch','Destination branch', track_visibility="on_change",copy=False,readonly=True)

    creator_branch = fields.Many2one('rb_delivery.branch','Creator branch', track_visibility="on_change",copy=False,readonly=True)

    active = fields.Boolean('Active', default=True ,track_visibility="on_change")

    branch_cost_view = fields.Float('Branch cost view', compute="compute_branch_revenue_total")

    @api.multi
    @api.depends('branch_financial_ids')
    def compute_branch_revenue_total(self):
        logged_user = self.env.user
        user_branch = logged_user.partner_id.branch_id
        for rec in self:
            if rec.sudo().branch_financial_ids:
                branch_cost_total = sum(branch_financial_rec.sudo().cost for branch_financial_rec in rec.sudo().branch_financial_ids)
                branch_revenue_total = sum(branch_financial_rec.sudo().branch_revenue for branch_financial_rec in rec.sudo().branch_financial_ids)
                branch_money_collection_cost_total = sum(branch_financial_rec.sudo().money_collection_cost for branch_financial_rec in rec.sudo().branch_financial_ids)
                times = 1
                if user_branch == rec.origin_branch:
                    times = -1
                rec.branch_revenue_total = branch_revenue_total * times
                rec.branch_cost_total = branch_cost_total * times
                rec.branch_money_collection_cost_total = branch_money_collection_cost_total * times
                rec.branch_cost_view = rec.branch_cost * times
                rec.order_ids = [(6,0,[branch_financial_rec.order_id.sudo().id for branch_financial_rec in rec.sudo().branch_financial_ids])]

    @api.model
    def get_status(self):
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')])
        status_list = [(status.name, status.title) for status in next_statuses]
        return status_list

    @api.multi
    def name_get(self):
        name_list = []
        for collection in self:
            name = collection.name or ""
            name = "{} {}".format(name, collection.sequence)
            name_list.append((collection.id, name))
        return name_list
        
        
    def update_orders(self,values):
        user = self.env.user
        self.message_post(body=_("Orders were changed from %s to %s by %s") % (self.order_ids.mapped('sequence'),[],user.name))
        current_financial_ids = values.get('branch_financial_ids')[0][2]
        if len(current_financial_ids) == 0:
            self.branch_financial_ids.write({'branch_financial_collection_id':False})
            self.order_ids.write({'branch_financial_collection_id':False})
            self.write({'order_ids':[(6,0,[])], 'active':False,'branch_cost':0})
            self.message_post(body=_("Orders were changed from %s to %s by %s") % (self.order_ids.mapped('sequence'),[],user.name))
            return
        all_financial_ids = self.branch_financial_ids.ids
        current_financial_ids_set  = set(current_financial_ids)
        financial_ids = set(all_financial_ids) - current_financial_ids_set
        if not financial_ids:
            return
        financial_recs = self.env['rb_delivery.branch_financial'].browse(financial_ids)
        if not financial_recs:
            return
        all_order_ids = self.order_ids.ids
        removed_order_ids = self.env['rb_delivery.order']
        total_money_collection_cost = 0
        user_branch = self.creator_branch.id
        for financial in self.branch_financial_ids:
            if financial.destination_branch.id == user_branch:
                total_money_collection_cost -= financial.money_collection_cost
            elif financial.origin_branch.id == user_branch:
                total_money_collection_cost += financial.money_collection_cost
            if total_money_collection_cost < 0:
                total_money_collection_cost = -total_money_collection_cost
        for financial_rec in financial_recs:
            removed_order_ids += financial_rec.order_id
        order_ids = set(all_order_ids) - set(removed_order_ids.ids)
        old_sequences = self.order_ids.mapped('sequence')
        removed_sequences = removed_order_ids.mapped('sequence')
        new_sequences = set(old_sequences) - set(removed_sequences)
        self.write({'order_ids':[(6,0,order_ids)],'branch_cost':total_money_collection_cost})
        removed_order_ids.write({'branch_financial_collection_id':False})
        financial_recs.write({'branch_financial_collection_id':False})
        self.message_post(body=_("Orders were changed from %s to %s by %s") % (old_sequences,new_sequences,user.name))

    def guard_function(self,values):
        for rec in self:
            if 'state' in values and values['state']:
                if values['state'] == 'paid' and rec.branch_id and rec.branch_id.id != rec.env.user.partner_id.branch_id.id:
                    raise ValidationError(_("You can not change to status Paid unless your branch is %s") %(rec.branch_id.name))

                rec.authorize_change_status(values['state'])
                rec.do_action(values)
            if values.get('branch_financial_ids') and len(values.get('branch_financial_ids'))>0 and len(values.get('branch_financial_ids')[0])>1:
                rec.update_orders(values)

    @api.multi
    def write(self, values):
        self.guard_function(values)
        return super(branch_financial_collection, self).write(values)

    def set_orders_branch_collection(self,order_list,collection):
        order_list.write({'branch_financial_collection_id':collection.id})
    
    @api.model
    def create(self, values):
        if not values.get('state'):
            values['state'] = self.get_default_status()

        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.do_action_create(values)

        new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.branch_financial_collection')
        values['sequence'] = new_sequence
        order_report = super(branch_financial_collection, self).create(values)
        batch_size = 1000
        for i in range(0, len(order_report.order_ids), batch_size):
            batch_ids = order_report.order_ids[i:i + batch_size]
            self.with_delay(channel="root.basic",max_retries=2).set_orders_branch_collection(batch_ids,order_report)
        order_report.branch_financial_ids.sudo().write({'branch_financial_collection_id':order_report.id})
        return order_report



    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            raise Warning(_("You are not allowed to change to this state"))

        return

    def do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state['state']),('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(branch_financial_collection,action.name)
                method_to_call(self,next_state)
            except:
                pass

    def do_action_create(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(branch_financial_collection,action.name)
                method_to_call(self,values)
            except:
                pass


    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','branch_financial_collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return

        # check if there is exception
        allowed_group=current_status_record.pass_lock_allowed_group_ids
        user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
        if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
        elif next_status_record.id in current_status_record.next_state_ids.ids :return
        else :
            # check if the next status is in the next status of the record
            raise Warning(_("You are not allowed to change from this status "+current_status_record.title+" to this status " + next_status_record.title))

    @api.multi
    @api.depends('sequence')
    def create_barcode(self):
        for rec in self:
            if (rec.sequence):
                barcode.base.Barcode.default_writer_options['write_text'] = False
                EAN = barcode.get_barcode_class('code39')
                ean = EAN(rec.sequence, writer=ImageWriter(), add_checksum=False)
                image_output = io.BytesIO()
                ean.write(image_output)
                encoded = base64.b64encode(image_output.getvalue())
                rec.barcode = encoded

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('olivery_branch_collection.view_form_olivery_branch_collection_order_select_branch_financial_collection_state').id
        context = {"parent_obj":self.id}
        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_branch_financial_collection_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    def change_financial_record_to_paid(self,values):
        if self.branch_financial_ids:
            self.branch_financial_ids.write({'status':'paid'})

    def change_financial_record_to_money_in_progress(self,values):
        if self.branch_financial_ids:
            self.branch_financial_ids.write({'status':'money_in_progress'})

    def change_financial_records(self,branch_financial_ids,length_batches):
        branch_financials = self.env['rb_delivery.branch_financial'].browse(branch_financial_ids)
        branch_financials.write({'status':'prepared'})

    
    def change_financial_record_to_prepared(self,values):
        if values.get('branch_financial_ids') and values.get('branch_financial_ids')[0] and len(values.get('branch_financial_ids')[0])>1 and values.get('branch_financial_ids')[0][2]:
            branch_financial_ids = values.get('branch_financial_ids')[0][2]
            batch_size = 1000
            for i in range(0, len(branch_financial_ids), batch_size):
                batch_ids = branch_financial_ids[i:i + batch_size]
                self.with_delay(channel="root.basic",max_retries=2).change_financial_records(batch_ids,len(batch_ids))

    
    def get_orders(self):
        order_tree_view_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id

        domain = [('id', 'in', self.order_ids.ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(order_tree_view_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
