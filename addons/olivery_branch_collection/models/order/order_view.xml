<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <data>
      <!--inherit form view-->
      <record id="view_form_branch_collection_order" model="ir.ui.view">
         <field name="name">view_form_branch_collection_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//notebook" position="before">
                <group groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                    <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;">
                        <group string="Transfer">
                            <group name="group-right">
                                <field name="current_branch"/>
                                <field name="to_branch"/>
                            </group>
                        </group>
                    </div>
                </group>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="Branch financial" groups="rb_delivery.role_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                    <group name="group_top">
                        <field name="branch_financial_ids" widget="one2many_list"/>
                    </group>
                </page>
            </xpath>
            <xpath expr="//field[@name='previous_area']" position="after">
                <field name="previous_branch"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <field name="branch_returned_collection_id" invisible="1"/>
                <button type="object"
                        name="get_branch_returned_collection"
                        class="btn btn-sm oe_stat_button o_form_invisible"
                        attrs="{'invisible': [('branch_returned_collection_id', '=', False)]}"
                        groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system">
                    <div class="fa fa-fw fa-archive o_button_icon" style="margin:0px !important; padding:0px !important; width:33px !important;" />
                    <div class="o_form_field o_stat_info" style="white-space:pre-wrap">
                        <span><t>Branch  Returned Collection</t></span>
                    </div>
                </button>
            </xpath>            
         </field>
      </record>

    <!--inherit search view-->
    <record id="view_search_branch_collection_order" model="ir.ui.view">
        <field name="name">view_search_branch_collection_order</field>
        <field name="model">rb_delivery.order</field>
        <field name="inherit_id" ref="rb_delivery.view_search_rb_delivery_order" />
        <field name="arch" type="xml">
        <xpath expr="//field[@name='reference_id']" position="after">
            <field name="current_branch"/>
            <field name="business_branch_id"/>
            <field name="branch_collection_id" filter_domain="[('branch_collection_id.sequence','ilike',self)]"/>
        </xpath>
        <xpath expr="//filter[@name='group_by_status_last_updated_on']" position="after">
            <filter name="group_by_current_branch" string="By Current Branch" icon="terp-partner" context="{'group_by':'current_branch'}"/>
            <filter name="group_by_to_branch" string="By To Branch" icon="terp-partner" context="{'group_by':'to_branch'}"/>
            <filter name="group_by_previous_branch" string="By Previous Branch" icon="terp-partner" context="{'group_by':'previous_branch'}"/>
            <filter name="group_by_sender_branch" string="By Sender Branch" icon="terp-partner" context="{'group_by':'business_branch_id'}"/>
        </xpath>
        </field>
    </record>

    <!--inherit tree view-->
    <record id="view_tree_branch_collection_order" model="ir.ui.view">
        <field name="name">view_tree_branch_collection_order</field>
        <field name="model">rb_delivery.order</field>
        <field name="inherit_id" ref="rb_delivery.view_tree_rb_delivery_order" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='customer_sub_area']" position="after">
                <field name="business_branch_id"/>
                <field name="current_branch"/>
                <field name="to_branch"/>
            </xpath>
         </field>
      </record>
   </data>
</odoo>