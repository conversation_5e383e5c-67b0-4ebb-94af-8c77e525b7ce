# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.osv import osv


class order_change_branch_wizard(models.TransientModel):
    _name = 'rb_delivery.change_branch_wizard'
    _description = "Change Branch Wizard Model"

    @api.multi
    def change_branch(self):
        orders = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        for order in orders:
            if self.change_current_branch:
                order.wkf_action_received_in_branch()
            if self.change_to_branch:
                if not self.to_branch or not self.to_branch.id:
                    raise ValidationError("يجب تحديد الفرع الذي ستذهب اليه الشحنة")
                order.write({'to_branch': self.to_branch.id})
                order.wkf_action_to_branch()


    change_current_branch = fields.Boolean("Move To Current Branch")
    change_to_branch = fields.Boolean("Move To Another Branch")
    to_branch = fields.Many2one('rb_delivery.branch', 'To Branch')


class order_refresh_financial_records_wizard(models.TransientModel):
    _name = 'rb_delivery.refresh_financial_records'
    _description = "Refresh Financial Records Wizard Model"

    @api.multi
    def refresh_financial_records(self):
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        for order in orders:
            order.create_financial_records(order.customer_area,order.customer_area,order.state_id,True)

class olivery_branch_collection_order_select_state_wizard_custom(models.TransientModel):
    _inherit = 'rb_delivery.select_state'

    current_branch = fields.Many2one('rb_delivery.branch', 'Current Branch',track_visibility="on_change")

    to_branch = fields.Many2one('rb_delivery.branch', 'To Branch',track_visibility="on_change")

    show_current_branch = fields.Boolean('Show Current Branch', default=False)

    show_to_branch = fields.Boolean('Show To Branch', default=False)

    required_to_branch = fields.Boolean('Show Current Branch Required', default=False)

    required_current_branch = fields.Boolean('Show To Branch Required', default=False)

    @api.onchange('state')
    def change_state(self):
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
        optional_status_actions = order_state.status_action_optional_related_fields
        required_status_actions = order_state.status_action_required_aditional_fields
        self.show_current_branch = False
        self.show_to_branch = False
        self.required_to_branch = False
        self.required_current_branch = False
        if order_state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'show_current_branch':
                    self.show_current_branch = True
                if status_action.name == 'show_to_branch':
                    self.show_to_branch = True
        if order_state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'show_current_branch':
                    self.show_current_branch = True
                    self.required_current_branch = True
                if status_action.name == 'show_to_branch':
                    self.show_to_branch = True
                    self.required_to_branch = True
        return super(olivery_branch_collection_order_select_state_wizard_custom, self).change_state()

    @api.multi
    def btn_show_dialog_box(self):
        self.env.context = dict(self.env.context)
        if self.to_branch:
            self.env.context.update({'to_branch': self.to_branch.id})
        if self.current_branch:
            self.env.context.update({'current_branch': self.current_branch.id})
        self.env.context.update({'state': self.state})
        return super(olivery_branch_collection_order_select_state_wizard_custom, self).btn_show_dialog_box()


class cargo_display_dialog_box_custom(osv.osv):
    _inherit = "display.dialog.box"

    current_branch = fields.Many2one('rb_delivery.branch', 'Current Branch',track_visibility="on_change")

    to_branch = fields.Many2one('rb_delivery.branch', 'To Branch',track_visibility="on_change")

    clone_text = fields.Text('Clone Text')

    @api.model
    def create(self, values):
        if 'current_branch' in self._context:
            values['current_branch'] = self._context.get('current_branch')
        if 'to_branch' in self._context:
            values['to_branch'] = self._context.get('to_branch')

        state = self.env['rb_delivery.status'].sudo().search([('name','=',self._context.get('state')),'|',('status_type','=',False),('status_type','=','olivery_order')])
        if state and state.clone: values['clone_text'] = _("Warning: Changing to this status will create a clone order")

        return super(cargo_display_dialog_box_custom, self).create(values)

    @api.multi
    def select_state(self):
        res = super(cargo_display_dialog_box_custom, self).select_state()
        recs = self._context.get('orders')
        orders = self.env['rb_delivery.order'].browse(recs)
        values = {}
        if self.current_branch:
            values['current_branch'] = self.current_branch.id
        if self.to_branch:
            values['to_branch'] = self.to_branch.id
        if values:
            orders.write(values)
        return res

class order_create_branch_returned_collection_records_wizard(models.TransientModel):
    _name = 'rb_delivery.create_branch_returned_collection'
    _description = "Create Branch Returned Collection Wizard Model"


    to_branch = fields.Many2one('rb_delivery.branch', 'To Branch',track_visibility="on_change")
    current_branch = fields.Many2one('rb_delivery.branch', 'Current Branch',track_visibility="on_change")
    @api.multi
    def create_branch_returned_collection(self):
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        create_branch_returned_collection_statuses = self.env['rb_delivery.client_configuration'].get_param('branch_return_collection_order_statuses')
        status_titles = self.env['rb_delivery.status'].browse(create_branch_returned_collection_statuses).mapped('name')

        for order in orders:
            if order.state_id.id not in create_branch_returned_collection_statuses:
                raise ValidationError(
                    _("Order %s does not have a valid status for this operation. Order status must be in these statuses: %s") %
                    (order.sequence, ', '.join(status_titles))
                )
        branch_returned_collection = self.env['rb_delivery.branch_returned_collection'].create({
            'name': 'Branch Returned Collection',  # You can customize the name as needed
            'order_ids': [(6, 0, orders.ids)],
            'to_branch': self.to_branch.id,
            'current_branch': self.current_branch.id,
        })



        return {'type': 'ir.actions.act_window_close'}


class order_check_cod_value_wizard(models.TransientModel):
    _name = 'rb_delivery.check_cod_value'
    _description = "Check COD value Wizard Model"


    def check_mismatch_orders(self):
        orders = self.env['rb_delivery.order'].browse(self._context.get('active_ids'))
        mismatch_orders = []
        for order in orders:
            if order.branch_financial_ids:
                for branch_financial_id in order.branch_financial_ids:
                    collection_cost = branch_financial_id.money_collection_cost + branch_financial_id.cost
                    if order.money_collection_cost != collection_cost:
                        mismatch_orders.append(order.sequence)
        if len(mismatch_orders):
            sequences = ', '.join(mismatch_orders)
            return _("Orders of sequences %s have mismatch COD value.")%(sequences)

    text_field = fields.Char('Message',default=check_mismatch_orders,readonly=True)
