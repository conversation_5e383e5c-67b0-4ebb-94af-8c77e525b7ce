# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from odoo.exceptions import ValidationError
from collections import defaultdict
import logging
_logger = logging.getLogger(__name__)

class branch_collection_order(models.Model):
    _inherit = 'rb_delivery.order'

    branch_collection_id = fields.Many2one('rb_delivery.branch_collection','Branch collection',copy=False)

    branch_financial_collection_id = fields.Many2one('rb_delivery.branch_financial_collection','Branch Financial collection',copy=False)

    branch_returned_collection_id = fields.Many2one('rb_delivery.branch_returned_collection','Branch Returned collection',copy=False)

    current_branch = fields.Many2one('rb_delivery.branch', 'Current Branch',track_visibility="on_change",copy=False)

    to_branch = fields.Many2one('rb_delivery.branch', 'To Branch',track_visibility="on_change",copy=False)

    branch_financial_ids = fields.One2many('rb_delivery.branch_financial','order_id','Branch financials',readonly=False,copy=False)

    is_in_branch_financial_collection = fields.Text('Is in branch Collection',compute='_compute_is_in_branch_financial_collection')

    business_branch_id = fields.Many2one('rb_delivery.branch',string="Business Branch",compute='_compute_sender_area', readonly=True, store=True)

    previous_branch = fields.Many2one('rb_delivery.branch', 'Previous Branch',track_visibility="on_change",copy=False)

    def authorize_edit(self,values,status=False):
        self.SUDO_FIELDS = self.SUDO_FIELDS + ['branch_financial_collection_id','branch_returned_collection_id']
        return super(branch_collection_order, self).authorize_edit(values,status)

    @api.depends('assign_to_business')
    @api.multi
    def _compute_sender_area(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return 0
        for rec in self:
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.branch_id and rec.sudo().assign_to_business.branch_id:
                rec.business_branch_id = rec.sudo().assign_to_business.branch_id.id
        return super(branch_collection_order, self)._compute_sender_area()

    @api.multi
    @api.depends('branch_financial_collection_id')
    def _compute_is_in_branch_financial_collection(self):
        for rec in self:
            if rec.branch_financial_collection_id:
                rec.is_in_branch_financial_collection = '<img src="/rb_delivery/static/src/img/successAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'
            else:
                rec.is_in_branch_financial_collection = '<img src="/rb_delivery/static/src/img/errorAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'

    def get_clone_replacement_values(self,order):
        values = super(branch_collection_order, self).get_clone_replacement_values(order)
        values['to_branch'] = order.to_branch.id
        values['current_branch']=order.current_branch.id
        return values

    def get_clone_returned_values(self,order):
        values = super(branch_collection_order, self).get_clone_returned_values(order)
        values['to_branch'] = order.to_branch.id
        values['current_branch']=order.current_branch.id
        return values

    def do_action_branch_collection_update(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            if 'prevent' not in action.name:
                try:
                    method_to_call=getattr(branch_collection_order,action.name)
                    method_to_call(self,values)
                except:
                    pass

    def remove_financial_records(self,values):
        if self.branch_financial_ids:
            self.branch_financial_ids.sudo().unlink()

    def check_to_branch(self,branch_id,area_id):
        area = self.env['rb_delivery.area'].sudo().browse(area_id)
        branch = self.env['rb_delivery.branch'].sudo().browse(branch_id)
        if area.branch_id.id != branch_id:
            raise ValidationError(_("You can not choose to branch '%s' because customer's area is '%s' is not related to the branch.")%(branch.name,area.name))
        return

    def prepare_values(self,values):
        set_current_branch = self.env['rb_delivery.client_configuration'].get_param('set_current_branch_to_business_branch')
        if set_current_branch and not values.get('current_branch'):
            if values.get('assign_to_business'):
                rb_delivery_user = self.env['rb_delivery.user'].sudo().search_read([('id', '=',values['assign_to_business'] )],limit=1,fields=['branch_id'])
            else :
                rb_delivery_user = self.env['rb_delivery.user'].sudo().search_read([('user_id.id', '=', self._uid),('role_code','=','rb_delivery.role_business')],limit=1,fields=['branch_id'])

            if rb_delivery_user and rb_delivery_user[0]['branch_id']:
                values['current_branch'] = rb_delivery_user[0]['branch_id']

        values  = super(branch_collection_order, self).prepare_values(values)
        if values.get('state'):
            self.branch_do_action_create(values)
        disallow_to_branch_and_customer_branch_to_be_different = self.env['rb_delivery.client_configuration'].get_param('disallow_to_branch_and_customer_branch_to_be_different')
        if values.get('customer_area') and values.get('to_branch') and disallow_to_branch_and_customer_branch_to_be_different:
            self.check_to_branch(values.get('to_branch'),values.get('customer_area'))
        return values

    def update_one_by_one_previous_branch(self,values):
        values_arr = []
        orders = []
        for rec in self:
            values_copy = {}
            if 'current_branch' in values and values['current_branch']:
                values_copy['previous_branch'] = rec.current_branch.id
            if values_copy:
                values_arr.append(values_copy)
                orders.append(rec)
        if len(values_arr)>0 and len(orders)>0:
            self.with_delay(channel="root.basic",max_retries=2).write_jq(values_arr,orders,self._context)

    @api.multi
    def write(self, values):
        confs = self.env['rb_delivery.client_configuration'].get_param(['disallow_to_branch_and_customer_branch_to_be_different','update_financial_records'])
        disallow_to_branch_and_customer_branch_to_be_different = confs['disallow_to_branch_and_customer_branch_to_be_different']
        status_ids = confs['update_financial_records'] or []
        for rec in self:
            if (values.get('to_branch') or values.get('customer_area')) and disallow_to_branch_and_customer_branch_to_be_different:
                area_id = values.get('customer_area') if values.get('customer_area') else rec.customer_area.id
                to_branch_id = values.get('to_branch') if values.get('to_branch') else rec.to_branch.id
                if area_id and to_branch_id:
                    self.check_to_branch(to_branch_id,area_id)
            if 'state' in values and values['state']:
                rec.do_action_branch_collection_update(values)
                rec.create_sub_financial_record_status(values)
        self.update_one_by_one_previous_branch(values)
        
        old_related_collections_fields_values = self.check_related_collection_fields_values()
            
        order = super(branch_collection_order, self).write(values)

        new_related_collections_fields_values = self.check_related_collection_fields_values()

        if old_related_collections_fields_values and new_related_collections_fields_values:
            self.check_update_financial_records(old_related_collections_fields_values,new_related_collections_fields_values,status_ids)
        
        return order

    def check_update_financial_records(self, old_related_collections_fields_values, new_related_collections_fields_values,status_ids):
        differences = self.get_differences_in_related_collection_fields(old_related_collections_fields_values, new_related_collections_fields_values)
        for rec in self:
            order_diff = differences.get(rec.id)
            if not order_diff:
                continue

            if not rec.branch_financial_ids or rec.state_id.id not in status_ids:
                continue

            update_vals = {}

            delivery_cost = rec.delivery_cost
            money_collection_cost = rec.money_collection_cost

            for branch_financial in rec.branch_financial_ids:
                cost = branch_financial.cost
                update_vals = {}

                if 'delivery_cost' in order_diff:
                    update_vals['branch_revenue'] = delivery_cost - cost

                if 'money_collection_cost' in order_diff:
                    update_vals['money_collection_cost'] = money_collection_cost - cost

                if update_vals:
                    branch_financial.write(update_vals)
    
    def check_middle_branch(self, origin_branch, destination_area, destination_branch):
        def get_pricelist_item(from_branch, to_branch, to_area):
            return self.env['rb_delivery.pricelist_branch'].search_read(
                [('from_branch', '=', from_branch.id),
                ('to_branch', '=', to_branch.id),
                ('to_area', '=', to_area.id)],
                fields=['price', 'to_branch', 'from_branch', 'to_area'],
                limit=1
            )

        def get_main_branch(branch):
            return self.env['rb_delivery.branch'].search([('sub_branch', 'in', branch.id)], limit=1)
        if not self.check_if_main_branch(origin_branch.id):
            main_sub_branch = get_main_branch(origin_branch)
            pricelist_item = get_pricelist_item(origin_branch, main_sub_branch, destination_area)

            if not pricelist_item and destination_area.parent_id:
                pricelist_item = get_pricelist_item(origin_branch, main_sub_branch, destination_area.parent_id)

            if pricelist_item:
                middle_branch_pricelist_item = get_pricelist_item(main_sub_branch, destination_branch, destination_area)
                if not middle_branch_pricelist_item and destination_area.parent_id:
                    middle_branch_pricelist_item = get_pricelist_item(main_sub_branch, destination_branch, destination_area.parent_id)

                return pricelist_item, middle_branch_pricelist_item if middle_branch_pricelist_item else False

            pricelist_item = get_pricelist_item(main_sub_branch, destination_branch, destination_area)
            if not pricelist_item and destination_area.parent_id:
                pricelist_item = get_pricelist_item(main_sub_branch, destination_branch, destination_area.parent_id)

            return pricelist_item, False if pricelist_item else (False, False)
        elif self.check_if_main_branch(origin_branch.id) and not self.check_if_main_branch(destination_branch.id) and (get_main_branch(destination_branch) == origin_branch):
            if get_main_branch(destination_branch) == origin_branch:
                pricelist_item = get_pricelist_item(origin_branch, destination_branch, destination_area)
                if not pricelist_item and destination_area.parent_id:
                    pricelist_item = get_pricelist_item(origin_branch, destination_branch, destination_area.parent_id)

                return pricelist_item, False if pricelist_item else (False, False)
            main_branch = get_main_branch(destination_branch)
            if main_branch:
                pricelist_item = get_pricelist_item(origin_branch, main_branch, destination_area)
                if not pricelist_item and destination_area.parent_id:
                    pricelist_item = get_pricelist_item(origin_branch, main_branch, destination_area.parent_id)

                if pricelist_item:
                    middle_branch_pricelist_item = get_pricelist_item(main_branch, destination_branch, destination_area)
                    if not middle_branch_pricelist_item and destination_area.parent_id:
                        middle_branch_pricelist_item = get_pricelist_item(main_branch, destination_branch, destination_area.parent_id)

                    return pricelist_item, middle_branch_pricelist_item if middle_branch_pricelist_item else False

        else:
            if self.check_if_main_branch(destination_branch.id):
                pricelist_item = get_pricelist_item(origin_branch, destination_branch, destination_area)
                if not pricelist_item and destination_area.parent_id:
                    pricelist_item = get_pricelist_item(origin_branch, destination_branch, destination_area.parent_id)

                return pricelist_item, False if pricelist_item else (False, False)

            main_branch = get_main_branch(destination_branch)
            if main_branch:
                pricelist_item = get_pricelist_item(origin_branch, main_branch, destination_area)
                if not pricelist_item and destination_area.parent_id:
                    pricelist_item = get_pricelist_item(origin_branch, main_branch, destination_area.parent_id)

                if pricelist_item:
                    middle_branch_pricelist_item = get_pricelist_item(main_branch, destination_branch, destination_area)
                    if not middle_branch_pricelist_item and destination_area.parent_id:
                        middle_branch_pricelist_item = get_pricelist_item(main_branch, destination_branch, destination_area.parent_id)

                    return pricelist_item, middle_branch_pricelist_item if middle_branch_pricelist_item else False

        return False, False


    def get_origin_area(self,order):
        origin_area = False
        if order.show_alt_address and order.business_alt_area:
            origin_area = order.business_alt_area
        elif order.sudo().assign_to_business and order.sudo().assign_to_business.area_id:
            origin_area = order.sudo().assign_to_business.area_id
        return origin_area

    def check_branch_financial_exist(self,branch_financial_ids,from_branch,to_branch):
        for branch_financial in branch_financial_ids:
            if branch_financial.origin_branch.id == from_branch.id and branch_financial.destination_branch.id == to_branch.id:
                return branch_financial
        return False

    def check_if_main_branch(self,branch_id):
        branches = self.env['rb_delivery.branch'].search([('sub_branch','=',branch_id)])
        if branches:
            return False
        return True

    def create_financial_records(self,login_user,destinstion_area,current_status,is_refresh):
        refresh_statuses = self.env['rb_delivery.client_configuration'].get_param('refresh_financial_record_status')
        for rec in self:
            origin_area = self.get_origin_area(rec)
            if origin_area and origin_area.branch_id:
                origin_branch = origin_area.branch_id
                if login_user:
                    destinstion_branch = login_user.branch_id
                else:
                    destinstion_branch = rec.customer_area.branch_id
                if origin_branch.id == destinstion_branch.id and origin_branch.financially_independent:
                    return

                pricelist_between_main_branches,pricelist_between_sub_branches = self.check_middle_branch(origin_branch,destinstion_area,destinstion_branch)

                if pricelist_between_main_branches:
                    from_branch = self.env['rb_delivery.branch'].browse(pricelist_between_main_branches[0]['from_branch'][0])
                    to_branch = self.env['rb_delivery.branch'].browse(pricelist_between_main_branches[0]['to_branch'][0])
                    to_branch_status = to_branch.branches_reconciliation_status
                    if pricelist_between_main_branches and ((to_branch_status == current_status ) or  (self.state_id.id in refresh_statuses and is_refresh)):
                        exist_financial_record = False

                        exist_financial_record = self.check_branch_financial_exist(rec.branch_financial_ids,from_branch,to_branch)
                        if not exist_financial_record:
                            self.calculate_branch_financial(pricelist_between_main_branches,from_branch.id,to_branch.id,destinstion_area,rec)
                        elif is_refresh:
                            self.branch_financial_ids.sudo().unlink()
                            self.calculate_branch_financial(pricelist_between_main_branches,from_branch.id,to_branch.id,destinstion_area,rec)

                    if pricelist_between_sub_branches:
                        from_branch = self.env['rb_delivery.branch'].browse(pricelist_between_sub_branches[0]['from_branch'][0])
                        to_branch = self.env['rb_delivery.branch'].browse(pricelist_between_sub_branches[0]['to_branch'][0])
                        to_branch_status = to_branch.branches_reconciliation_status

                        if ((to_branch_status == current_status ) or  (self.state_id.id in refresh_statuses and is_refresh)):
                            exist_financial_record = False
                            # this method is wrong it should only check if from_bracnh an to_branch exist
                            exist_financial_record = self.check_branch_financial_exist(rec.branch_financial_ids,from_branch,to_branch)
                            if not exist_financial_record:
                                self.calculate_branch_financial(pricelist_between_sub_branches,from_branch.id,to_branch.id,destinstion_area,rec)
                            elif is_refresh:
                                self.branch_financial_ids.sudo().unlink()
                                self.calculate_branch_financial(pricelist_between_sub_branches,from_branch.id,to_branch.id,destinstion_area,rec)

    def create_sub_financial_record_status(self,values):
        configuration_status = self.env['rb_delivery.client_configuration'].get_param('branch_reconciliation_default_statuses')
        login_user =self.env['rb_delivery.user'].sudo().search([('user_id','=',self.env.uid)])
        current_status = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        if current_status.id not in configuration_status:
            return

        user_branch_status_id = login_user.branch_id.branches_reconciliation_status
        destinstion_area = self.customer_area

        if self.check_if_main_branch(login_user.branch_id.id):
            if user_branch_status_id and current_status and current_status.id == user_branch_status_id[0].id:
                self.create_financial_records(login_user,destinstion_area,current_status,False)

        else:
            self.create_financial_records(login_user,destinstion_area,current_status,False)

    def calculate_branch_financial(self,pricelist_item,origin_branch,to_branch,destinstion_area,rec):
        cost_between_origin_and_destination = pricelist_item[0]['price']
        branch_revenue = rec.delivery_cost - cost_between_origin_and_destination
        money_collection_cost = rec.money_collection_cost - cost_between_origin_and_destination
        branch_financial = self.env['rb_delivery.branch_financial'].sudo().create({'origin_branch': origin_branch, 'destination_branch': to_branch, 'cost': cost_between_origin_and_destination, 'order_id': rec.id, 'branch_revenue': branch_revenue, 'destination_branch_revenue': cost_between_origin_and_destination,'to_area':destinstion_area.id,'original_cost':cost_between_origin_and_destination,'original_branch_revenue':branch_revenue,'original_destination_branch_revenue':cost_between_origin_and_destination,'money_collection_cost':money_collection_cost})
        return branch_financial

    def create_branch_financial_record(self, values):
        login_user =self.env['rb_delivery.user'].sudo().search([('user_id','=',self.env.uid)])
        status_id = login_user.branch_id.branches_reconciliation_status
        status = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        is_admin = self.env.user._is_admin()
        if status_id and status and status.id == status_id[0].id:
            for rec in self:
                origin_area = self.get_origin_area(rec)
                login_user =self.env['rb_delivery.user'].sudo().search([('user_id','=',self.env.uid)])
                if login_user.area_id and origin_area and origin_area.branch_id:
                    origin_branch = origin_area.branch_id
                    destinstion_area = login_user.area_id
                    destinstion_branch = login_user.branch_id
                    pricelist_item,middle_branch_pricelist_item = self.check_middle_branch(origin_branch,destinstion_area,destinstion_branch)

                    if pricelist_item:
                        middle_from_branch = False
                        middle_to_branch = False
                        from_branch = pricelist_item[0]['from_branch'][0]
                        to_branch = pricelist_item[0]['to_branch'][0]

                        if middle_branch_pricelist_item:
                            middle_to_branch = middle_branch_pricelist_item[0]['to_branch'][0]
                            middle_from_branch = middle_branch_pricelist_item[0]['from_branch'][0]
                            if is_admin or self.env.user.partner_id.branch_id.id == middle_to_branch:
                                exist_financial_record = False
                                if rec.branch_financial_ids:
                                    exist_financial_record = self.check_branch_financial_exist(rec.branch_financial_ids,from_branch,to_branch)

                                    exist_main_financial_record = self.check_branch_financial_exist(rec.branch_financial_ids,middle_from_branch,middle_to_branch)
                                    if exist_main_financial_record:
                                        return
                                middle_branch_financial = self.calculate_branch_financial(middle_branch_pricelist_item,middle_from_branch,middle_to_branch,destinstion_area,rec)
                                if not exist_financial_record:
                                    branch_financial = self.calculate_branch_financial(pricelist_item,from_branch,to_branch,destinstion_area,rec)
                                    destinstion_revenue = branch_financial.cost - middle_branch_financial.cost
                                    branch_financial.sudo().write({'destination_branch_revenue':destinstion_revenue})
                                    middle_branch_financial.sudo().write({'branch_revenue':destinstion_revenue})
                                else:
                                    destinstion_revenue = exist_financial_record.cost - middle_branch_financial.cost
                                    exist_financial_record.sudo().write({'destination_branch_revenue':destinstion_revenue})
                                    middle_branch_financial.sudo().write({'branch_revenue':destinstion_revenue})
                        else:
                            exist_main_financial_record = self.check_branch_financial_exist(rec.branch_financial_ids,from_branch,to_branch)
                            if exist_main_financial_record:
                                return
                            self.calculate_branch_financial(pricelist_item,from_branch,to_branch,destinstion_area,rec)

        return

    def branch_do_action_create(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(branch_collection_order,action.name)
                method_to_call(self,values)
            except:
                pass

    @api.multi
    def set_to_user_current_branch(self,values):
        if not values.get('current_branch'):
            on_create = False
            uid = self._context.get('uid',self._uid)
            user_id = self.env['rb_delivery.user'].search([('user_id','=',uid)])
            if 'state' in values:
                status=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')])
                status_actions = status.status_action_on_create_ids
                if status_actions:
                    for action in status_actions:
                        if action.name == 'set_to_user_current_branch':
                            on_create = True
            else:
                status=self.env['rb_delivery.status'].search([('name','=',values),'|',('status_type','=',False),('status_type','=','olivery_order')])
            if not status.default and not on_create:
                if user_id.branch_id:
                    vals = {'current_branch':user_id.branch_id.id}
                    if self.to_branch and self.to_branch.id == user_id.branch_id.id:
                        vals['to_branch']=False
                    self.write(vals)
            else:
                values['current_branch'] = user_id.branch_id.id

    def get_clone_order_values(self,order,cloned_status,cloner_status,values):
        #This one for cargo when change status and the status has clone
        clone_values = super(branch_collection_order, self).get_clone_order_values(order,cloned_status,cloner_status,values)
        if order.to_branch:
            clone_values['to_branch'] = order.to_branch.id
        if order.current_branch:
            clone_values['current_branch'] = order.current_branch.id
        return clone_values

    @api.one
    def wkf_action_to_branch(self):
        # TODO need to add role and when this should be used
        if not self.to_branch.id:
            raise ValidationError("يجب تحديد الفرع الذي ستذهب اليه الشحنة")


    @api.one
    def wkf_action_received_in_branch(self):
        # TODO need to add role and when this should be used
        user_id = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        self.current_branch = user_id.branch_id
        self.to_branch = ""

    @api.multi
    def apply_post_write_actions(self,values):
        self.check_if_ready_for_return(values)
        return super(branch_collection_order, self).apply_post_write_actions(values)

    @api.multi
    def check_if_ready_for_return(self,values):
        if values.get('is_from_collection'):
            return
        ready_for_return_order_ids = []
        set_to_user_current_branch_status_ids = self.env['rb_delivery.status'].search([('status_type','=','olivery_order'),('status_action_ids','in',[self.env.ref('olivery_branch_collection.status_action_set_to_user_current_branch').id])])
        set_to_user_current_branch_statuses = [state.name for state in set_to_user_current_branch_status_ids if hasattr(state, 'name')]
        ready_for_return_status_ids = self.env['rb_delivery.status'].search([('status_type','=','olivery_order'),('status_action_ids','in',[self.env.ref('rb_delivery.status_action_set_ready_for_return').id])])
        ready_for_return_statuses = [state.name for state in ready_for_return_status_ids if hasattr(state, 'name')]
        if len(set_to_user_current_branch_statuses) and len(ready_for_return_statuses):
            for rec in self:
                if values.get('state') and rec.is_ready_for_return:
                    if (
                        values['state'] in set_to_user_current_branch_statuses
                        and values['state'] not in ready_for_return_statuses
                    ):
                        ready_for_return_order_ids.append(rec.id)
            if len(ready_for_return_order_ids):
                orders = self.browse(ready_for_return_order_ids)
                orders.write({'state':ready_for_return_statuses[0]})

        
    def get_branch_returned_collection(self):
        address_form_id = self.env.ref('olivery_branch_collection.view_tree_rb_delivery_branch_returned_collection').id
        domain = [('order_ids', 'in', self.id)]
        context = {
            'model':'rb_delivery.branch_returned_collection',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.branch_returned_collection',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'context':context,
            'target': 'current',
            'domain': domain}