<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <data>
      <record id="view_form_olivery_branch_collection_change_branch_wizard" model="ir.ui.view">
            <field name="name">view_form_olivery_branch_collection_change_branch_wizard</field>
            <field name="model">rb_delivery.change_branch_wizard</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">
                    <header>
                        <!-- <button name="change_branch" confirm="Do you want to proceed?" type="object" string="Change Branch" class="oe_highlight" /> -->
                    </header>
                    <sheet>
                        <group name="group_top">
                            <field name="change_current_branch" attrs="{'invisible':[('change_to_branch', '=', True)]}"/>
                        </group>
                        <group name="group_bottom">
                            <field name="change_to_branch" attrs="{'invisible':[('change_current_branch', '=', True)]}"/>
                            <field name="to_branch" attrs="{'invisible':[('change_to_branch', '=', False)]}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="change_branch" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>
            </field>
        </record>

    <record id="view_form_olivery_branch_collection_refresh_financial_records_wizard" model="ir.ui.view">
        <field name="name">view_form_olivery_branch_collection_refresh_financial_records_wizard</field>
        <field name="model">rb_delivery.refresh_financial_records</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <header>
                </header>
                <sheet>
                    <group name="group_top">
                        <separator string="Are you sure you want to refresh financial records for orders?"/>
                    </group>
 </sheet>
                <footer>
                    <button name="refresh_financial_records" type="object" string="Refresh"/>
                    <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="view_form_olivery_branch_returned_collection_wizard" model="ir.ui.view">
        <field name="name">view_form_olivery_branch_returned_collection_wizard</field>
        <field name="model">rb_delivery.create_branch_returned_collection</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <header>
                </header>
                <sheet>
                    <group name="group_top">
                        <field name="current_branch" />
                        <field name="to_branch" />
                    </group>

                </sheet>
                <footer>
                    <button name="create_branch_returned_collection" type="object" string="Create"/>
                    <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="view_form_olivery_branch_returned_collection_select_state_wizard" model="ir.ui.view">
        <field name="name">view_form_olivery_branch_returned_collection_select_state_wizard</field>
        <field name="model">rb_delivery.branch_returned_collection_select_state</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <header>
                </header>
                <sheet>
                    <group name="group_top">
                        <field name="state" />
                    </group>

                </sheet>
                <footer>
                    <button name="change_status_branch_returned_collection" type="object" string="Create"/>
                    <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                </footer>
            </form>
        </field>
    </record>



    <!--inherit form view-->
    <record id="view_form_olivery_branch_collection_order_select_state" model="ir.ui.view">
        <field name="name">view_form_olivery_branch_collection_order_select_state</field>
        <field name="model">rb_delivery.select_state</field>
        <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order_select_state" />
        <field name="arch" type="xml">
        <xpath expr="//field[@name='state']" position="after">
            <field name="show_current_branch" invisible="1"/>
            <field name="show_to_branch" invisible="1"/>
            <field name="required_current_branch" invisible="1"/>
            <field name="required_to_branch" invisible="1"/>
            <field name="current_branch" attrs="{'invisible': [('show_current_branch','=',False)],'required':[('required_current_branch','=',True)]}"/>
            <field name="to_branch" attrs="{'invisible': [('show_to_branch','=',False)],'required':[('required_to_branch','=',True)]}"/>
        </xpath>
        </field>
    </record>
    <record id="view_form_olivery_repeating_delivery_show_dialog_box" model="ir.ui.view">
         <field name="name">view_form_olivery_repeating_delivery_show_dialog_box</field>
         <field name="model">display.dialog.box</field>
         <field name="inherit_id" ref="rb_delivery.wizard_message_form" />
         <field name="arch" type="xml">
         <field name="text" position="after">
                <field name="clone_text" colspan="4" nolabel="1" readonly="1"  widget="html"/>
         </field>
         </field>
      </record>

      <record id="view_form_check_cod_value_wizard" model="ir.ui.view">
        <field name="name">view_form_check_cod_value_wizard</field>
        <field name="model">rb_delivery.check_cod_value</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <header>
                </header>
                <sheet>
                    <group name="group_top">
                        <field name="text_field"/>
                    </group>

                </sheet>
                <footer>
                    <button name="cancel" string="Close" special="cancel" class="oe_link"/>
                </footer>
            </form>
        </field>
    </record>
   </data>
</odoo>