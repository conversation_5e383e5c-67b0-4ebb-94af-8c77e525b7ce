# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_branch_collection
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-28 08:38+0000\n"
"PO-Revision-Date: 2024-02-28 08:38+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_branch_collection
#: model:res.groups,comment:olivery_branch_collection.role_branch_editor
msgid "\n"
"        Branch editor will have the ability to edit the branch for user's\n"
"      "
msgstr "\n"
"        صلاحية تعديل الافرع يمكنه التعديل على الفروع للمستخدمين \n"
"      "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "<small>\n"
"									<span>Page</span>\n"
"									<span class=\"page\"/>\n"
"									of\n"
"									<span class=\"topage\"/>\n"
"								</small>"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "<span>Orders</span>"
msgstr "<span>الطلبيات</span>"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_needaction
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_needaction
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_needaction
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__active
msgid "Active"
msgstr "نشط"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Address"
msgstr "العنوان"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__agent
msgid "Agent"
msgstr "السائق"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_agent_money_collection
msgid "Agent Money Collection Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_agent_returned_collection
msgid "Agent Returned Collection Model"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Agent's name:"
msgstr "اسم السائق: "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_create_financial_collection
msgid "Are you sure you want to create branch financial collection ?"
msgstr "هل أنت متأكد من أنك تريد انشاء كشف المالية للفرع ?"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_refresh_financial_records_wizard
msgid "Are you sure you want to refresh financial records for orders?"
msgstr "هل انت متاكد من انك تريد تحديث السجلات المالية؟"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/order/order_wizard.py:51
#, python-format
msgid "The order financial records were refreshed using Refresh financial records action by %s"
msgstr "تم تحذيث السجلات المالية من خلال الاجراء تحديث السجلات المالية من خلال %s"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_area
msgid "Area Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__area_ids
msgid "Areas"
msgstr "المناطق"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_attachment_count
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_attachment_count
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_attachment_count
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__barcode
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__barcode
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Barcode"
msgstr "باركود"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_ks_dashboard_ninja_item__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_area__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_res_partner__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_res_users__branch_id
msgid "Branch"
msgstr "الفرع"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_branch_colection
#: model:ir.actions.report,name:olivery_branch_collection.report_rb_delivery_order_branch_collection_action
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_collection
msgid "Branch Collection"
msgstr "كشف تحصيل الفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Branch Collection Report"
msgstr "كشف تحصيل الفرع "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:302
#, python-format
msgid "Branch Collection generated"
msgstr "تم انشاء كشف تحصيل الفرع "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__cost
msgid "Branch Cost"
msgstr "تكلفة الفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Branch Distribution Report"
msgstr "كشف توزيع الفرع"

#. module: olivery_branch_collection
#: model:ir.actions.report,name:olivery_branch_collection.report_olivery_branch_collection_branch_financial_collection
msgid "Branch Financial Collection"
msgstr "الكشف المالي للفرع "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Branch Financial Collection Report"
msgstr "الكشف المالي للفرع "

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_branch_financial
msgid "Branch Financial Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__branch_financial_collection_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__branch_financial_collection_id
msgid "Branch Financial collection"
msgstr "الكشف المالي للفرع "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__is_in_branch_financial_collection
msgid "Is in branch Collection"
msgstr "هل موجود في تحصيل الفرع"

#. module: olivery_branch_collection
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_financials
msgid "Branch Financials"
msgstr "محاسبة الفروع"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_branch
msgid "Branch Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__branch_collection_id
msgid "Branch collection"
msgstr "كشف الفرع"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_branch_collection
msgid "Branch collection Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_cost
msgid "Required to Branch"
msgstr "التكلفة المطلوبة للفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Required to branch"
msgstr "التكلفة المطلوبة للفرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_cost_total
msgid "Branch cost total"
msgstr "مجموع تكلفة الفرع"

#. module: olivery_branch_collection
#: model:ir.actions.report,name:olivery_branch_collection.recap_report_branch
msgid "Branch distribution report"
msgstr "كشف توزيع الفرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__branch_editor
#: model:res.groups,name:olivery_branch_collection.role_branch_editor
msgid "Branch editor"
msgstr "صلاحية تعديل الافرع "

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_branch_financial
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_financial_ids
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_financial
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_branch_collection_order
msgid "Branch financial"
msgstr "محاسبة الفرع"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_branch_financial_collection
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_financial_collection
msgid "Branch budget collection"
msgstr "كشف تحصيل الموازنة"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_branch_financial_collection
msgid "Branch financial collection Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__branch_financial_ids
msgid "Branch financials"
msgstr "حسابات الفرع "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:69
#, python-format
msgid "Branch of name %s already exist as a sub branch for the branch %s, to be able to add it as a sub branch for this branch you need to remove it from being a sub branch to another branch."
msgstr "الفرع للاسم %s موجود كفرع ثانوي للفرع ٪s، لاضافة الفرع كفرع ثانوي يجب انا لا يكون فرع ثانوي لفرع اخر"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_branch_pricelist
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_pricelist
msgid "Branch pricelist"
msgstr "قائمة الاسعار للفروع الفروع "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__branch_revenue
msgid "Branch revenue"
msgstr "تحصيل الفرع "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_revenue_total
msgid "Branch revenue total"
msgstr "مجموع تحصيل الفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_branch_collection_multi_print_orders_money_collector
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
msgid "Branch:"
msgstr "الفرع:"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_branch
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_branch_collection_user
msgid "Branches"
msgstr "الافرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
msgid "By Agent"
msgstr "السائق"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_branch_collection_user
msgid "By Branch"
msgstr "الفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "By Creator"
msgstr "المنشئ"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_branch_collection_order
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_returened_collection
msgid "By Current Branch"
msgstr "الفرع الحالي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_branch_collection_order
msgid "Financial Collection"
msgstr "موازنة"


#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "By Date"
msgstr "التاريخ"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "By Destination Branch"
msgstr "بواسطة الفرع الاخر "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
msgid "By Order"
msgstr "رقم الطلب"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "By Origin Branch"
msgstr "بواسطة الفرع الحالي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
msgid "By Payment Type"
msgstr "نوع الدفع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
msgid "By Previous Agent"
msgstr "حسب السائق السابق"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_branch_collection_user
msgid "By Previous Branch"
msgstr "بواسطة الفرع السابق"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
msgid "By Previous Status"
msgstr "الحالة السابقة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "By State"
msgstr "بواسطة الحالة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
msgid "By Status"
msgstr "الحالة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_branch_collection_order
msgid "By To Branch"
msgstr "بواسطة للفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_change_branch_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_create_financial_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_detach_financial_records
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_recalculate_branch_financial_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_select_branch_collection_state
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_select_branch_financial_collection_state
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_refersh_calculate_totals_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_refresh_financial_records_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_financial_select_branch_financial_state
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_returned_collection_select_state_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_returned_collection_wizard
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Car's number:"
msgstr "رقم السيارة"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_change_branch_wizard
msgid "Change Branch"
msgstr "تغير الفرع"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_change_branch_wizard
msgid "Change Branch Wizard Model"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Change status"
msgstr "تغير الحالات"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_client_configuration
msgid "Client Parameter"
msgstr "متغيرات العميل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_display_dialog_box__clone_text
msgid "Clone Text"
msgstr "النص المستنسخ"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_collection_barcode
msgid "Collection barcode"
msgstr "باركود التحصيل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Collection cost"
msgstr "تكلفة التحصيل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Company Registry:"
msgstr "المشغل مرخص: "

#. module: olivery_branch_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_collection_completed
msgid "Completed"
msgstr "مكتمل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "Completed Collections"
msgstr "تحصيلات مكتملة "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
msgid "Completed Financial Records"
msgstr "السجلات المالية المكتملة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
msgid "Cost"
msgstr "التكلفة"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_create_branch_financial_collection
msgid "Create Branch Financial Collection"
msgstr "انشاء كسف المالي للفرع"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_create_branch_financial_collection
msgid "Create Money Collection Model"
msgstr ""

#. module: olivery_branch_collection
#: selection:rb_delivery.branch_financial,status:0
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_financial_collection_created
msgid "Created"
msgstr "منشئ"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__create_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__create_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_display_dialog_box__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_agent_money_collection__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_agent_returned_collection__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_returned_collection__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_multi_print_orders_money_collector__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_returned_money_collection__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_runsheet__branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__current_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_olivery_branch_collection_barcode_collection
msgid "Current Branch"
msgstr "الفرع الحالي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__current_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode_collection__current_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode_item__current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__current_branch_id
msgid "Current branch"
msgstr "الفرع الحالي"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/barcode/barcode_model.py:55
#, python-format
msgid "Current branch is required. \n"
""
msgstr "الفرع الحالي مطلوب . \n"
""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_ks_dashboard_ninja_item
msgid "Dashboard Ninja items"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Date:"
msgstr "التاريخ:"

#. module: olivery_branch_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_collection_deleted
msgid "Deleted"
msgstr "محذوف"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Delivery cost"
msgstr "تكلفة التوصيل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Destination Branch"
msgstr "الفرع الاخر"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__destination_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__destination_branch
msgid "Destination branch"
msgstr "الفرع الاخر"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__creator_branch
msgid "Creator branch"
msgstr "فرع المنشئ"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_cost_view
msgid "Branch cost view"
msgstr "تكلفة الفرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__display_name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_display_dialog_box
msgid "Display dialog box management"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__driver_id
msgid "Driver"
msgstr "سائقون"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__driver_ids
msgid "Drivers"
msgstr "السائقون"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__edit_branch
msgid "Edit branch"
msgstr "تعديل الفرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__email_sent
msgid "Email Sent"
msgstr "البريد الالكتروني المرسل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__total_ammount
msgid "Expected total amount"
msgstr "الاجمالي المتوقع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "Filters"
msgstr ""

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:114
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_wizard.py:15
#, python-format
msgid "Financial record from branch %s to branch %s already exists in a branch financial record of sequence %s"
msgstr "السجل المالي من الفرع  %s للفرع  %s موجود مسبقا في سجل تحصيل مالي برقم تسلسل  %s"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_follower_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_follower_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_follower_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_channel_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_channel_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_channel_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_partner_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_partner_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_partner_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__from_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_pricelist_branch
msgid "From branch"
msgstr "من الفرع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "From:"
msgstr "من: "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__has_sub_branch
msgid "Has Sub branch"
msgstr "لديه فرع ثانوي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_unread
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_unread
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_unread
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_needaction
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_needaction
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_needaction
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_has_error
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_has_error
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_has_error
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_branch_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_financial_collection_in_progress
msgid "In progress"
msgstr "قيد التوصيل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__is_collection_manager
msgid "Is Collection Manager"
msgstr "مدير كشوفات؟"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_is_follower
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_is_follower
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_is_follower
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state____last_update
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__write_uid
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_financial_collection__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__write_date
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
msgid "Logo"
msgstr "الشعار"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_main_attachment_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__main_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch
msgid "Main Branch"
msgstr "الفرع الرئيسي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_check_cod_value__text_field
msgid "Message"
msgstr "الرسالة"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_check_cod_value
msgid "Branch reconciliation validation"
msgstr "التحقق من الموازنة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_has_error
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_has_error
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_has_error
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_branch_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_collection_money_out
msgid "Money Out"
msgstr "جاري تسليم المال للتاجر"

#. module: olivery_branch_collection
#: selection:rb_delivery.branch_financial,status:0
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_collection_money_received
msgid "Money Received"
msgstr "تم استلام المال"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Money collection cost"
msgstr "مبلغ التحصيل"

#. module: olivery_branch_collection
#: selection:rb_delivery.branch_financial,status:0
msgid "Money in progress"
msgstr "جاري توصيل المال"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__change_to_branch
msgid "Move To Another Branch"
msgstr "نقل لفرع اخر"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__change_current_branch
msgid "Move To Current Branch"
msgstr "نقل للفرع الحالي"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_multi_print_orders_money_collector
msgid "Multi Print Orders Money Collector Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__name
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch
msgid "Name"
msgstr "الاسم"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__name
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__name
msgid "Note"
msgstr "ملاحظة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Notes"
msgstr "ملاحظات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_needaction_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_has_error_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_needaction_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_needaction_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_needaction_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_has_error_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_has_error_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_has_error_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__message_unread_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__message_unread_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__message_unread_counter
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__order_id
msgid "Order"
msgstr "الطلبية"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:136
#, python-format
msgid "Order already exists in Collection %s "
msgstr "الطلبية موجودة مسبقا في كشف  %s"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__order_count
msgid "Order count"
msgstr "عدد الطلبيات"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:139
#, python-format
msgid "Order state should be "
msgstr "حالة الطلبية يجب ان تكون"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__order_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__order_ids
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_collection
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Origin Branch"
msgstr "الفرع الحالي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__origin_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__origin_branch
msgid "Origin branch"
msgstr "الفرع الحالي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "Page:"
msgstr "صفحة"

#. module: olivery_branch_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_financial_collection_paid
msgid "Paid"
msgstr "مدفوع"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_res_partner
msgid "Partner Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__payment_date
msgid "Payment Date"
msgstr "تاريخ السداد"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__payment_type
msgid "Payment Method"
msgstr "طريقة السداد"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
msgid "Payment:"
msgstr "الدفع"

#. module: olivery_branch_collection
#: selection:rb_delivery.branch_financial,status:0
#: model:rb_delivery.status,title:olivery_branch_collection.status_branch_financial_collection_prepared
msgid "Prepared"
msgstr "جاهز"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__previous_agent
msgid "Previous Agent"
msgstr "السائق السابق"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__previous_branch_id
msgid "Previous Branch"
msgstr "الفرع السابق"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__previous_status
msgid "Previous Status"
msgstr "الحالة السابقة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__previous_status_title
msgid "Previous Status Name"
msgstr "اسم الحالة السابقة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__price
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_pricelist_branch
msgid "Price"
msgstr "سعر"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_pricelist_branch
msgid "Pricelist Branch Model"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Recipient's address"
msgstr "عنوان المستلم"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Recipient's name"
msgstr "اسم المستلم"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Required from business"
msgstr "مطلوب للتاجر"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:52
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:53
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:62
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:78
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:84
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:85
#, python-format
msgid "Required from "
msgstr " مطلوب من "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:54
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:55
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:67
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:82
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:86
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:87
#, python-format
msgid "Required to "
msgstr "مطلوب ل "

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_returned_money_collection
msgid "Returned Money Collection Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_runsheet
msgid "Runsheet Model"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_change_branch_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_create_financial_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_detach_financial_records
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_recalculate_branch_financial_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_select_branch_collection_state
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_select_branch_financial_collection_state
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_financial_select_branch_financial_state
msgid "Save"
msgstr "حفظ"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_agent_money_collection_state
msgid "Select Agent Money Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_agent_returned_money_collection_state
msgid "Select Agent Returned Money Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_branch_collection_state
msgid "Select Branch Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_branch_financial_collection_state
msgid "Select Branch Financial Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_money_collection_state
msgid "Select Money Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_returned_collection_state
msgid "Select Returned Collection State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_runsheet_state
msgid "Select Runsheet State Model"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_order_select_branch_collection_state
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_order_select_branch_financial_collection_state
msgid "Select State"
msgstr "اختر الحالة"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_select_state
msgid "Select State Model"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "Sender's name"
msgstr "اسم التاجر"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__sequence
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Sequence Number"
msgstr "الرقم المتسلسل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Sequence number"
msgstr "رقم المتسلسل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__show_agent
msgid "Show Agent"
msgstr "اظهار السائق"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__required_agent
msgid "Show Agent Required"
msgstr "حقل اظهار السائق مطلوب"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__show_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__show_current_branch
msgid "Show Current Branch"
msgstr "اظهار الفرع الحالي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__required_current_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__required_to_branch
msgid "Show Current Branch Required"
msgstr "حقل اظهار الفرع الحالي مطلوب "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__show_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__show_to_branch
msgid "Show To Branch"
msgstr "اظهار للفرع "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__required_to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__required_current_branch
msgid "Show To Branch Required"
msgstr "حقل للفرع مطلوب "

#. module: olivery_branch_collection
#. openerp-web
#: code:addons/olivery_branch_collection/static/src/xml/buttons.xml:6
#, python-format
msgid "Show orders"
msgstr "اظهار الطلبيات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__state
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__status
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__state
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__state
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_financial_collection_state__state
msgid "Status"
msgstr "الحالة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__state_id
msgid "Status ID"
msgstr "رقم تعريف الحالات"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_status
msgid "Status Model"
msgstr "مودل الحالات"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__sub_branch
msgid "Sub branch"
msgstr "الفرع الثانوي"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:61
#, python-format
msgid "The branch %s can not be main branch since it is a sub branch for %s"
msgstr "الفرع %s لا يمكن ان يكون فرع رئيسي لانه فرع ثانوي للفرع %s"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:73
#, python-format
msgid "The branch %s can not be sub branch since it is a main branch."
msgstr "الفرع %s لا يمكن ان يكون فرع ثانوي لانه فرع رئيسي"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/user/user_model.py:109
#, python-format
msgid "You are trying to create/edit user with branch %s with a different branch than your branch %s. \n"
" What to do next: Change your branch to desired branch. Go to users page then setting tab then (branch section) activate edit branch then change to branch %s. Or Change your branch to Main branch. Go to users page then setting tab then (branch section) activate edit branch then change to branch (Main branch)"
msgstr "انت تحاول انشاء/تعديل فرع المستخدم ل%s بقيمة مختلفة عن الفرع الخاص بك %s.\n"
"ماذا يمكنك ان تفعل: قم بتغيير فرعك للفرع المطلوب. قم بالذهاب لصفحة المستخدمين ثم الاعدادات ثم اعدادات الافرع قم بتفعيل تعديل الفرع ثم قم بتغيير الفرع ل%s. او قم بتغيير الفرع الخاص بك للفرع الرئيسي. قم بالذهاب الى صفحة المستخدمين ثم الاعدادات ثم اعدادات الافرع قم بتفعيل تعديل الفرع ثم قم بتغيير الفرع الس الفرع الرئيسي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "The recipient's signature: .............................."
msgstr "توقيع المستلم: ............................"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:71
#, python-format
msgid "The sub branch should have at least one area."
msgstr "الفرع الثانوي يجب ان يكون لديه على الاقل مدينة واحدة"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:43
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:52
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:83
#, python-format
msgid "There must be at least one main branch."
msgstr "يجب أن يكون على الأقل فرع واحد رئيسي"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/user/user_model.py:31
#: code:addons/olivery_branch_collection/models/user/user_model.py:43
#, python-format
msgid "There must be either main branch in the system or a branch to the selected area."
msgstr "يجب أن يكون هنالك فرع رئيسي في النظام أو اختيار فرع للمناطق المختارة "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:154
#, python-format
msgid "This Order  is not allowed to be removed from the collection since it is in  status. You can remove the order in the previous status."
msgstr "غير مسموح لك بازالة الطلبية من التحصيل وهي في حالة . يمكنك ازالة الطلبية في الحالة السابقة ."

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/client_configuration/client_configuration_model.py:18
#, python-format
msgid "This configuration should only have one status"
msgstr "هذه الاعدادات يجب ان تحتوي على حالة واحدة فقط"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_display_dialog_box__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_agent_money_collection__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_agent_returned_collection__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_returned_collection__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_change_branch_wizard__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_create_branch_returned_collection__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_multi_print_orders_money_collector__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_returned_money_collection__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_runsheet__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_money_collection_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_agent_returned_money_collection_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_branch_collection_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_money_collection_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_returned_collection_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_runsheet_state__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_select_state__to_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_olivery_branch_collection_barcode_collection
#: model:rb_delivery.status,title:olivery_branch_collection.status_to_branch
msgid "To Branch"
msgstr "للفرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode_collection__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_barcode_item__to_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_collection_barcode__to_branch_id
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_pricelist_branch__to_branch
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_pricelist_branch
msgid "To branch"
msgstr "للفرع"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/barcode/barcode_model.py:61
#, python-format
msgid "To branch is required. \n"
""
msgstr "للفرع مطلوب  \n"
""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.dist_branch
msgid "To:"
msgstr "الى:"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_collection
msgid "Total"
msgstr "الإجمالي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Total Amount"
msgstr "مجموع التحصيل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__total_money_collection_cost
msgid "Total COD Value"
msgstr "مجموع قيمة التحصيل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Total Delivery Cost"
msgstr "مجموع تكلفة التوصيل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__total_delivery_cost
msgid "Total Delivery Fee"
msgstr "مجموع تكلفة التوصيل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__total_cost
msgid "Total Net Value"
msgstr "مجموع مستحق التاجر"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_collection
msgid "Total ammount"
msgstr "مجموع الاجمالي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_financial_collection
msgid "Total cost"
msgstr "الصافي للتاجر"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_collection
msgid "Total delivery fee"
msgstr "مجموع رسوم التوصيل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.multi_print_branch_collection
msgid "Total net value"
msgstr "مجموع مستحق التاجر"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_financial_collection
msgid "Total revenue"
msgstr "مجموع التحصيل "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch_financial_collection
msgid "Totals:"
msgstr "المجموع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_branch_collection_order
msgid "Transfer"
msgstr "الشحنة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_unread
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_unread
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_unread
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__message_unread_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__message_unread_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__message_unread_counter
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_branch
msgid "Update User's branches"
msgstr "تعديل افرع المستخدمين"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_user
msgid "User Model"
msgstr "المستخدم"

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_utility
msgid "Utility Model"
msgstr ""

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/order/order_wizard.py:97
#, python-format
msgid "Warning: Changing to this status will create a clone order"
msgstr "تحذير : التغيير لهذه الحالة سيتم انشاء طلبية مستنسخة "

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__website_message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_collection__website_message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__website_message_ids
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: olivery_branch_collection
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch__website_message_ids
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_collection__website_message_ids
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial__website_message_ids
#: model:ir.model.fields,help:olivery_branch_collection.field_rb_delivery_branch_financial_collection__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:491
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:180
#, python-format
msgid "You are not allowed to change from this status  to this status "
msgstr "غير مسموح لك التغيير من هذه الحالة الى هذه الحالة "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:335
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:141
#, python-format
msgid "You are not allowed to change to this state"
msgstr "غير مسموح لك بالتغيير للحالة "

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:76
#, python-format
msgid "You can not add a sub branch to this branch since it is a sub branch for %s."
msgstr "لا تستطيع اضافة فرع ثانوي لهذا الفرع لانه فرع ثانوي للفرع %s"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/area/area_model.py:18
#, python-format
msgid "You can not empty the field 'Branch' from the 'Area', to be able to remove the 'Branch' from the 'Area' make sure you add the area %s to another branch, for example the main branch."
msgstr "لا يمكن ازالة الحقل 'الفرع' من 'المنطقة'، لكي تستطيع ازالة 'الفرع' من 'المنطقة' يمكنك ان تقوم بتغيير الفرع للمنطقة %s، على سبيل المثال الفرع الرئيسي"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:161
#, python-format
msgid "You can't edit the collection when the status is Completed"
msgstr "لا يمكنك التعديل على الكشف في حالة مكتمل "

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_tree_rb_delivery_branch_financial
msgid "cost_sum"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_barcode
msgid "rb_delivery.barcode"
msgstr ""

#. module: olivery_branch_collection
#: model:ir.model,name:olivery_branch_collection.model_rb_delivery_barcode_item
msgid "rb_delivery.barcode_item"
msgstr ""

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_branch_financial_collection_report
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.olivery_branch_collection_sender_branch_financial_collection_report
msgid "signature:"
msgstr "التوقيع:"


#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:95
#, python-format
msgid "User's branch was updated through button 'Update branches' from the branch model by %s"
msgstr "تم تحديث فرع المستخدم من خلال زر 'تحديث الافرع' من موديل الافرع من خلال %s"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:98
#, python-format
msgid "You can not change to status Paid unless your branch is %s"
msgstr "لا تستطيع تغيير الحالة لمدفوع الا اذا كنت في الفرع  %s"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:122
#, python-format
msgid "You can not create branch budget unless you have only two groups of financial records."
msgstr "لا تستطيع انشاء موازنة افرع الا اذا كان لديك مجموعتين من السجلات المالية"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:128
#, python-format
msgid "To create branch budget you need to have two groups of branches that are the same from origin branch to destination branch and destination branch to origin branch."
msgstr "لانشاء موازنة الافرع يجب ان يكون هناك تجميع حسب الفرع الحالي و ان يكون لديك في المقابل سجلات لنفس الفرع مع فرع اخر"

#. module: olivery_branch_collection
#. openerp-web
#: code:addons/olivery_branch_collection/static/src/xml/buttons.xml:9
#, python-format
msgid "Branch budget"
msgstr "موازنة الافرع"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_refresh_financial_records_wizard
msgid "Refresh Financial Records"
msgstr "تحديث السجلات المالية"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:145
#, python-format
msgid "Budget Collection generated"
msgstr "تم انشاء تحصيل موازنة الافرع"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:148
#, python-format
msgid "All records already have budget collection."
msgstr "جميع السجلات موجودة في تحصيل موازنة الافرع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__extra_fee
msgid "Extra Fee"
msgstr "مصاريف اضافية"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__original_cost
msgid "Original Branch Cost"
msgstr "تكلفة الفرع الاصلية"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial__original_branch_revenue
msgid "Original Branch revenue"
msgstr "ربح الفرع الاصلي"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "Paid Collections"
msgstr "تحصيلات مدفوعة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
msgid "Paid Financial Records"
msgstr "سجلات مالية مدفوعة"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:88
#, python-format
msgid "Required to %s"
msgstr " مطلوب ل %s"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial_collection
msgid "Unpaid Collections"
msgstr "تحصيلات غير مدفوعة"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_financial
msgid "Unpaid Financial Records"
msgstr "سجلات مالية غير مدفوعة"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/user/user_model.py:64
#, python-format
msgid "You can only Set user branch to your branch!\n"
"What you can do: You should add your current branch only for the user."
msgstr "يمكنك اضافة المستخدم فقط للفرع الخاص بك"
"ماذا تستطيع ان تفعل: اختار الفرع الخاص بك للمستخدم الحالي."

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__branch_money_collection_cost_total
msgid "Total money collection cost"
msgstr "مجموع التحصيل الكلي"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_res_company__show_business_branch_order
msgid "Show orders if business is in user branch"
msgstr "اظهر طلبيات التجار في فرع المستخدم"

#. module: olivery_branch_collection
#. openerp-web
#: code:addons/olivery_branch_collection/static/src/js/buttons.js:30
#, python-format
msgid "Turning this configuration off means your users will not be able to view orders of the businesses in their branch, will only view orders that have \"Current Branch\" or \"To Branch\" as their branch. \n"
" So please make sure to add \"set_to_user_current_branch\" status action to the default status of your system, so you will not have issues with creating orders."
msgstr "الغاء هذا الاعداد يعني ان المستخدمين لديك لن يكون لديهم صلاحية للطلبيات للتجار الذين في فرعم, سيكون لديه صلاحية الوصول فقط للطلبيات التي لديها  \"الفرع الحالي\" او \"للفرع\" مثل فرع المستخدم. \n"
" تاكد ان اجراء الحالة \"set_to_user_current_branch\" موجود على الحالة الافتراضية للنظام, تفاديا للمشاكل في انشاء الطلبية للمستخدمين المختلفين."

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__branches_reconciliation_status
msgid "Branches Reconciliation Status"
msgstr "حالة موازنة الفروع"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_active_branch_returned_collection
#: model:ir.ui.menu,name:olivery_branch_collection.menu_active_branch_returned_collection
msgid "Active Branch Returned Collection"
msgstr "الكشوفات المرتجعة بين الفروع النشطة"

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_all_branch_returned_collection
#: model:ir.ui.menu,name:olivery_branch_collection.menu_all_branch_returned_collection
msgid "All Branch Returned Collection"
msgstr "جميع كشوفات المرتجعة بين الفروع"

#. module: olivery_branch_collection
#: model:ir.actions.report,name:olivery_branch_collection.olivery_branch_collection_report_branch_returned_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.branch_returned_collection_report
msgid "Branch Returned Collection"
msgstr "كشف المرتجع بين الفروع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__branch_returned_collection_id
#: model:ir.ui.menu,name:olivery_branch_collection.menu_rb_delivery_branch_returned_collection
msgid "Branch Returned collection"
msgstr "كشف المرتجع بين الفروع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.branch_returned_collection_report
msgid "Recipient Address"
msgstr "عنوان الزبون"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.branch_returned_collection_report
msgid "Recipient's Mobile"
msgstr "رقم المحمول للزبون"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.branch_returned_collection_report
msgid "Recipient's Name"
msgstr "اسم الزبون"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.branch_returned_collection_report
msgid "Sender Name"
msgstr "اسم المرسل"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_returned_collection/branch_returned_collection_wizard.py:36
#, python-format
msgid "Current status not found."
msgstr "الحالة الحالية غير موجودة"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_returned_collection/branch_returned_collection_wizard.py:39
#, python-format
msgid "The selected status is not a valid next status."
msgstr "الحالة المختارة غير صالحة كحالة تالية لتغييرها"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/order/order_wizard.py:144
#, python-format
msgid "Order %s does not have a valid status for this operation. Order status must be in these statuses: %s"
msgstr "الطلب %s لا يحتوي على حالة صالحة لهذه العملية. الطلب في هذه الحالات: %s" يجب أن تكون حالة

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/order/order_model.py:59
#, python-format
msgid "Value of to branch field must be same customer's area branch '%s'"
msgstr "قيمة الحقل للفرع يجب ان تكون مثل قيمة فرع منطقة المستلم '%s'"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_branch_collection_order
msgid "By Sender Branch"
msgstr "باستخدام فرع المرسل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_order__business_branch_id
msgid "Business Branch"
msgstr "فرع المرسل"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/order/order_model.py:59
#, python-format
msgid "You can not choose to branch '%s' because customer's area is '%s' is not related to the branch."
msgstr "لايمكن اختيار نقل للفرع '%s' لان منطقة المستلم '%s' غير تابعة لهذا الفرع."

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:46
#: code:addons/rb_delivery/models/error_log/error_log_model.py:46
#, python-format
msgid "Error while Updating Branch"
msgstr "خطأ اثناء تحديث الفرع"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "Cant remove the area {area_name} from this branch."
msgstr "لا يمكنك ازالة المنطقة {area_name} من هذا الفرع"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Please Instead of doing this you can go to the needed branch and add the area {area_name} to it"
msgstr "عوضا عن هذا يمكنك الذهاب الى الفرع المطلوب واضافة المنطقة {area_name} اليها"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__branch_financial_manager
#: model:res.groups,name:olivery_branch_collection.role_branch_financial_manager
msgid "Branch financial manager"
msgstr "ادارة محسابة الفروع"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_financial_collection__is_branch_financial_manager
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_user__is_branch_financial_manager
msgid "Is branch financial manager"
msgstr "ادارة محسابة الفروع"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_recalculate_branch_financial_collection
msgid "Are you sure you want to recalculate branch financial collection ?"
msgstr "هل أنت متأكد أنك تريد إعادة حساب التحصيل المالي للفرع؟"


#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_returned_collection_select_state_wizard
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_returned_collection_wizard
msgid "Create"
msgstr "انشاء"


#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_order_select_branch_financial_collection_recalculate
msgid "Recalculate Collection"
msgstr "اعادة حساب التحصيل"

#. module: olivery_branch_collection
#: model:ir.actions.report,name:olivery_branch_collection.report_olivery_branch_collection_sender_branch_financial_collection
msgid "Sender Branch Report"
msgstr "تقرير فرع المرسل"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_check_cod_value_wizard
msgid "Close"
msgstr "إقفال"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/user/user_model.py:80
#, python-format
msgid "You can't edit a user with a different branch than your branch since you are not in main branch.\n"
" What to do next: From settings section select the user's branch to be same as your branch."
msgstr "لا يمكنك تعديل فرع المستخدم بفرع مختلف عن فرعك بما انك لست في فرع رئيسي./n"
"ماذا بمكنك ان تفعل: من الاعدادات قم باختيار فرع المستخدم ليكون مثل فرعك."

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch_returned_collection__all
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_search_rb_delivery_branch_returened_collection
msgid "All"
msgstr "الكل"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/user/user_model.py:83
#, python-format
msgid "You can not add a branch for another user as long as you do not have a branch. \n"
"What you can do: Please add a branch to your account, go to all users then search for your username, then settings section and select a branch."
msgstr "لا تستطيع اضافة فرع للمستخدم في حال ليس لديك فرع. \n"
"ماذا تستطيع ان تفعل: قم باضافة فرع لنفسك من خلال الذهاب لجميع المستخدمين ثم قم بالبحث عن اسم المستخدم الخاص بك، ثم الاعدادات و قم باختيار فرع."

#. module: olivery_branch_collection
#: model:ir.actions.act_window,name:olivery_branch_collection.action_rb_delivery_order_detach_financial_records
msgid "Detach financial records"
msgstr "ازالة السجلات المالية"

#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_olivery_branch_collection_order_detach_financial_records
msgid "Are you sure you want to Detach branch financial records?"
msgstr "هل انت متأكد من ازالة السجلات المالية؟"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch/branch_model.py:80
#, python-format
msgid "The user was updated with main branch on install branch module."
msgstr "تم التعديل على المستخدم بقيمة الفرع الرئيسي عند تنزيل موديول الفروع"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_collection/branch_collection_model.py:194
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:105
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:111
#: code:addons/olivery_branch_collection/models/branch_financial_collection/branch_financial_collection_model.py:131
#, python-format
msgid "Orders were changed from %s to %s by %s"
msgstr "تم تعديل الطلبيات من %s الى %s من خلال %s"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_rb_delivery_branch__financially_independent
msgid "Financially Independent"
msgstr "مستقل ماليا"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:247
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:199
#, python-format
msgid "Total money collection"
msgstr "مجموع التحصيل"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:275
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:201
#, python-format
msgid "Total revenue"
msgstr "مجموع الايرادات"

#. module: olivery_branch_collection
#: code:addons/olivery_branch_collection/models/branch_financial/branch_financial_model.py:264
#: code:addons/olivery_branch_collection/models/ks_item_dashboard/ks_item_dashboard_model.py:197
#, python-format
msgid "Non profit collection"
msgstr "صافي مجموع التحصيل"

#. module: olivery_branch_collection
#: model:ir.model.fields,field_description:olivery_branch_collection.field_ks_dashboard_ninja_item__is_for_main_branch
#: model:ir.model.fields,field_description:olivery_branch_collection.field_ks_dashboard_ninja_board__for_main_branch
msgid "For main branch"
msgstr "للفرع الرئيسي"


#. module: olivery_branch_collection
#: model_terms:ir.ui.view,arch_db:olivery_branch_collection.view_form_branch_collection_order
msgid "Branch  Returned Collection"
msgstr "كشف المرتجع للفرع"
