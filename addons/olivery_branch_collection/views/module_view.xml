<?xml version="1.0"?>
<odoo>
  <data>

    <!-- Collection Report -->
    <act_window id="action_rb_delivery_branch_colection" name="Branch Collection" res_model="rb_delivery.branch_collection" view_mode="tree,form" />
    <menuitem id="menu_rb_delivery_branch_collection" name="Branch Collection" parent="rb_delivery.menu_rb_delivery_collection" sequence="15" action="action_rb_delivery_branch_colection" groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting"/>

    <act_window id="action_rb_delivery_order_select_branch_collection_state" name="Select State" groups="rb_delivery.role_accounting,rb_delivery.role_junior_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" src_model="rb_delivery.branch_collection" res_model="rb_delivery.select_branch_collection_state" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_branch_collection_refersh_calculate_totals" name="Refresh Calculate Totals" groups="rb_delivery.role_accounting,rb_delivery.role_junior_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" src_model="rb_delivery.branch_collection" res_model="rb_delivery.branch_collection_refersh_calculate_totals" view_mode="form" target="new" multi="True" />

    <!-- Branch -->
    <act_window id="action_rb_delivery_branch" name="Branches" res_model="rb_delivery.branch" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_branch" name="Branches" parent="rb_delivery.menu_rb_delivery_configuration" sequence="15" action="action_rb_delivery_branch" />

    <act_window id="action_rb_delivery_change_branch_wizard" name="Change Branch" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry,rb_delivery.role_accounting" src_model="rb_delivery.order" res_model="rb_delivery.change_branch_wizard" view_mode="form" target="new" multi="True" />
    <act_window id="action_rb_delivery_refresh_financial_records_wizard" name="Refresh Financial Records" groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting" src_model="rb_delivery.order" res_model="rb_delivery.refresh_financial_records" view_mode="form" target="new" multi="True" />

    <menuitem id="menu_rb_delivery_branch_financials" name="Branch Financials" parent="rb_delivery.rb_delivery_top_menu" sequence="18" groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting,olivery_branch_collection.role_branch_financial_manager"/>

    <!-- Branch Financial -->
    <act_window id="action_rb_delivery_branch_pricelist" name="Branch pricelist" res_model="rb_delivery.pricelist_branch" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_branch_pricelist" name="Branch pricelist" parent="menu_rb_delivery_branch_financials" sequence="13" action="action_rb_delivery_branch_pricelist" />

    <!-- Branch Financial -->
    <act_window id="action_rb_delivery_branch_financial" name="Branch financial" res_model="rb_delivery.branch_financial" view_mode="tree,form" context="{'group_by':['origin_branch'],'search_default_unpaid_collections': 1}"/>
    <menuitem id="menu_rb_delivery_branch_financial" name="Branch financial" parent="menu_rb_delivery_branch_financials" sequence="11" action="action_rb_delivery_branch_financial" />

    <!-- Branch Financial Collection -->
    <act_window id="action_rb_delivery_branch_financial_collection" name="Branch budget collection" res_model="rb_delivery.branch_financial_collection" view_mode="tree,form" context="{'search_default_unpaid_collections': 1}"/>
    <menuitem id="menu_rb_delivery_branch_financial_collection" name="Branch budget collection" parent="menu_rb_delivery_branch_financials" sequence="12" action="action_rb_delivery_branch_financial_collection" groups="olivery_branch_collection.role_branch_financial_manager,rb_delivery.role_accounting"/>


    <act_window id="action_rb_delivery_create_branch_returned_collection_wizard" name="Create Branch Returned Collection" groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting" src_model="rb_delivery.order" res_model="rb_delivery.create_branch_returned_collection" view_mode="form" target="new" multi="True" />

    <act_window id="action_rb_delivery_branch_returned_collection_select_state" name="Select State" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_call_center,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_data_entry" src_model="rb_delivery.branch_returned_collection" res_model="rb_delivery.branch_returned_collection_select_state" view_mode="form" target="new" multi="True" />

    <menuitem id="menu_rb_delivery_branch_returned_collection" name="Branch Returned collection" parent="rb_delivery.menu_rb_delivery_collection" sequence="15" />

    <!-- Active Branch Returned Collection -->
    <record model="ir.actions.act_window" id="action_active_branch_returned_collection">
      <field name="name">Active Branch Returned Collection</field>
      <field name="res_model">rb_delivery.branch_returned_collection</field>
      <field name="view_id" ref="view_tree_rb_delivery_branch_returned_collection"></field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain">[('state','!=','completed')]</field>
    </record>
    <menuitem id="menu_active_branch_returned_collection" name="Active Branch Returned Collection" parent="menu_rb_delivery_branch_returned_collection" sequence="1" action="action_active_branch_returned_collection" />

    <record model="ir.actions.act_window" id="action_all_branch_returned_collection">
      <field name="name">All Branch Returned Collection</field>
      <field name="res_model">rb_delivery.branch_returned_collection</field>
      <field name="view_id" ref="view_tree_rb_delivery_branch_returned_collection"></field>
      <field name="view_mode">tree,form,graph</field>
      <field name="limit" eval="150"/>
      <field name="domain"></field>
    </record>
    <menuitem id="menu_all_branch_returned_collection" name="All Branch Returned Collection" parent="menu_rb_delivery_branch_returned_collection" sequence="2" action="action_all_branch_returned_collection" />


    <act_window id="action_rb_delivery_order_select_branch_financial_collection_state" name="Select State" groups="rb_delivery.role_accounting,rb_delivery.role_junior_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" src_model="rb_delivery.branch_financial_collection" res_model="rb_delivery.select_branch_financial_collection_state" view_mode="form" target="new" multi="True" />

    <act_window id="action_rb_delivery_order_select_branch_financial_collection_recalculate" name="Recalculate Collection" groups="rb_delivery.role_accounting,rb_delivery.role_junior_accounting,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" src_model="rb_delivery.branch_financial_collection" res_model="rb_delivery.recalculate_branch_financial_collection" view_mode="form" target="new" multi="True" />

    <act_window id="action_rb_delivery_order_detach_financial_records" name="Detach financial records" groups="base.group_system" src_model="rb_delivery.branch_financial_collection" res_model="rb_delivery.detach_financial_records" view_mode="form" target="new" multi="True" />

    <act_window id="action_rb_delivery_check_cod_value" name="Branch reconciliation validation" groups="rb_delivery.role_super_manager,base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.check_cod_value" view_mode="form" target="new" multi="True" />

  </data>
</odoo>
