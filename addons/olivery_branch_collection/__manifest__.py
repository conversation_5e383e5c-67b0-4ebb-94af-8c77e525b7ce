# -*- coding: utf-8 -*-
{
    'name': "olivery_branch_collection",
    'summary': """
        Olivery Branch Collection App from olivery.app""",

    'description': """
        App used for creating and viewing branch collection
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.1.37',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail','website','rb_delivery','olivery_web_barcode','olivery_cargo','ks_dashboard_ninja'],

    # always loaded
    'data': [

        'data/sequence.xml',
        'security/branch_collection_security.xml',
        'security/ir.model.access.csv',
        'models/branch_collection/branch_collection_view.xml',
        'models/branch_collection/branch_collection_wizard_view.xml',
        'models/branch_collection/branch_collection_report.xml',
        'models/branch_financial_collection/branch_financial_collection_view.xml',
        'models/branch_financial_collection/branch_financial_collection_report.xml',
        'models/branch_financial_collection/branch_financial_collection_wizard_view.xml',
        'models/branch/branch_view.xml',
        'models/branch_financial/branch_financial_view.xml',
        'models/branch_financial/branch_financial_wizard_view.xml',
        'models/agent_money_collection/agent_money_collection_view.xml',
        'models/agent_money_collection/agent_money_collection_wizard_view.xml',
        'models/agent_returned_money_collection/agent_returned_money_collection_view.xml',
        'models/agent_returned_money_collection/agent_returned_money_collection_wizard_view.xml',
        'models/money_collection/money_collection_view.xml',
        'models/money_collection/money_collection_wizard_view.xml',
        'models/order/order_view.xml',
        'models/order/order_wizard_view.xml',
        'models/partner/partner_view.xml',
        'models/pricelist_branch/pricelist_branch_view.xml',
        'models/returned_money_collection/returned_money_collection_view.xml',
        'models/returned_money_collection/returned_money_collection_wizard_view.xml',
        'models/runsheet/runsheet_view.xml',
        'models/runsheet/runsheet_wizard_view.xml',
        'models/user/user_view.xml',
        'models/area/area_view.xml',
        'models/barcode/barcode_view.xml',
        'models/collection_barcode/collection_barcode_view.xml',
        'models/branch_returned_collection/branch_returned_collection_view.xml',
        'models/barcode_collection/barcode_collection_view.xml',
        'views/module_view.xml',
        'views/templates.xml',
        'views/reports/order_report_view_branch.xml',
        'views/reports/sender_branch_report.xml',
        'demo/status.xml',
        'demo/client_configuration_demo.xml',
        'demo/error_log_demo.xml',
        'demo/branch_demo.xml',
        'views/reports/branch_returned_collection.xml'
    ],
    'qweb': [
        'static/src/xml/*.xml',
    ],
    'post_init_hook': 'branch_post_init_hook',
}
