from odoo import models, fields, api


class naqel_contact(models.Model):

    _name = 'olivery_naqel.naqel_contact'
    _description = 'Naqel Contacts'

    name = fields.Char("Naqel Contact id", track_visibility="on_change",required=True)

    contact_password = fields.Char("Naqel Contact password", track_visibility="on_change",required=True)

    @api.constrains('name')
    def _check_unique_name(self):
        for rec in self:
            if not self.search_count([('name', '=', rec.name)]) > 1:
                continue
            self.env['rb_delivery.error_log'].raise_olivery_error(1980,rec.id,{'name': rec.name})
    