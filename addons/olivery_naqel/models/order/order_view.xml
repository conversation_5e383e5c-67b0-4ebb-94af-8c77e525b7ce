<?xml version="1.0" encoding="UTF-8"?>

<odoo>
  <data>
      <!--inherit form view-->
      <record id="view_form_naqel_order" model="ir.ui.view">
         <field name="name">view_form_naqel_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
               <field name="is_naqel_order" invisible="1" />
               <button attrs="{'invisible':[('is_naqel_order', '=', False)]}" type="object" name="get_naqel_logs" class="btn btn-sm oe_stat_button o_form_invisible">
                  <div class="fa fa-fw fa-pencil-square-o o_button_icon" />
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                     <span>Naqel logs</span>
                  </div>
               </button>
            </xpath>
            <xpath expr="//field[@name='assign_to_business']" position="after">
               <field name="is_naqel_order" invisible="1"/>
                <field name="scan_station_code" attrs="{'required':[('is_naqel_order','=',True)]}"/>
            </xpath>
            <xpath expr="//field[@name='money_collection_cost']" position="before">
               <field name="declare_value"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
               <page name="naqel" string="Naqel" >
                  <group>
                     <group name="group_right">
                        <field name="is_custom_duty_pay_by_consignee"/>
                        <field name="incoterm"/>
                        <field name="naqel_status"/>
                        <field name="naqel_status_code"/>
                        <field name="naqel_contact" readonly="1"/>
                     </group>
                  </group>
               </page>
         </xpath>
         </field>
      </record>

      <record id="view_search_naqel_order" model="ir.ui.view">
         <field name="name">view_search_naqel_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_search_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//filter[@name='archived_orders']" position="after">
               <filter name="naqel_orders" string="Naqel Orders" domain="[('is_naqel_order','=',True)]"/>
            </xpath>
         </field>
      </record>


   </data>
</odoo>
