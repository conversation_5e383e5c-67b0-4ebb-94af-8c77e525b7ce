<?xml version="1.0" encoding="UTF-8"?>

<odoo>
  <data>
      <record id="view_form_rb_delivery_get_naqel_orders" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_get_naqel_orders</field>
      <field name="model">rb_delivery.get_naqel_orders</field>
        <field name="arch" type="xml">

        <form create="false" edit="false">

        <header>
        </header>
        <sheet>

            <group name="group_top">
                <field name="date"/>
                <field name="file_name"/>
                <field name="directory"/>
            </group>

        </sheet>

          <footer>
            <button name="get_orders" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>


    <record id="view_form_rb_deliveryـsend_orders_to_naqel" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_send_orders_to_naqel</field>
      <field name="model">rb_delivery.send_orders_to_naqel</field>
        <field name="arch" type="xml">

        <form create="false" edit="false">

        <header>
        </header>
        <sheet>
          <group name="group_top">
            <separator string="Are you sure you want to send orders to Naqel?"/>
            <field name="naqel_contact" />
          </group>
        </sheet>

          <footer>
            <button name="send_orders" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>


   </data>
</odoo>
