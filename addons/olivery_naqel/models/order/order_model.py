# -*- coding: utf-8 -*-

from io import BytesIO
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError, Warning
import os
import pysftp
import xlrd
from datetime import datetime, timedelta
import pytz
import requests
import xml.etree.ElementTree as ET

class olivery_naqel_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------


    def _get_default_scan_station(self):
        default_area = self.env['rb_delivery.area'].search([('is_default', '=', True)])
        return default_area.id

    is_naqel_order = fields.Boolean("Is Naqel Order", readonly=True,track_visibility="on_change",copy=False)

    naqel_ref_id = fields.Char("Naqel Reference ID",readonly=True,track_visibility="on_change",copy=False)

    piece_barcode = fields.Binary('Piece Barcode', compute="create_naqel_barcode",copy=False)

    scan_station_code = fields.Many2one("rb_delivery.area","Scan Station Code",default=_get_default_scan_station,track_visibility="on_change",required=False,copy=False)

    naqel_status = fields.Char('Naqel status',track_visibility="on_change",readonly=True,copy=False)

    naqel_status_code = fields.Char('Naqel status code',track_visibility="on_change",readonly=True,copy=False)

    sent_to_naqel = fields.Boolean("Sent to Naqel", readonly=False,track_visibility="on_change",copy=False)

    schedule_pickup_date = fields.Date('Schedule pickup date',track_visibility="on_change",copy=False)

    needs_naqel_delivery = fields.Boolean("Needs Naqel Delivery", track_visibility="on_change",copy=False)

    declare_value = fields.Float('Declare value', track_visibility="on_change",copy=False)

    chargeable_weight = fields.Float('Chargeable Weight', track_visibility="on_change",copy=False)

    naqel_contact = fields.Many2one("olivery_naqel.naqel_contact","Naqel Contact", track_visibility="on_change",copy=False)

    incoterm = fields.Char('Incoterm', track_visibility="on_change",copy=False)

    is_custom_duty_pay_by_consignee = fields.Boolean('Is Custom Duty Pay By Consignee', track_visibility="on_change",copy=False)

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------


    @api.onchange('incoterm')
    def onchange_incoterm(self):
        if self.incoterm and self.incoterm == 'DDU':
            self.is_custom_duty_pay_by_consignee = True
        elif self.incoterm and self.incoterm == 'DDP':
            self.is_custom_duty_pay_by_consignee = False

    @api.onchange('is_custom_duty_pay_by_consignee')
    def onchange_is_custom_duty_pay_by_consignee(self):
        if self.is_custom_duty_pay_by_consignee:
            self.incoterm = 'DDU'
        else:
            self.incoterm = 'DDP'


    def authorize_edit(self,values, state=False):
        self.SUDO_FIELDS = self.SUDO_FIELDS + ['needs_naqel_delivery']
        return super(olivery_naqel_order, self).authorize_edit(values,state)

    @api.model
    def track_naqel_order(self, ids=[]):
        domain = [('sent_to_naqel', '=', True), ('state', 'not in', ['completed', 'deleted', 'cancelled'])]
        if ids and isinstance(ids, list) and len(ids) > 0:
            domain.append(('id', 'in', ids))
        url = "https://infotrack.naqelexpress.com/NaqelAPIServices/NaqelAPI/9.0/XMLShippingService.asmx"
        orders = self.env['rb_delivery.order'].search(domain)
        for order in orders:
            order_contact = ''
            order_contact_password = ''
            if order.naqel_contact:
                order_contact = order.naqel_contact.name
                order_contact_password = order.naqel_contact.contact_password
            else:
                confs = self.env['rb_delivery.client_configuration'].get_param(['naqel_send_orders_password','naqel_client_id'])
                order_contact = confs.get('naqel_send_orders_password')
                order_contact_password = confs.get('naqel_client_id')

            soap_envelope = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
            <soapenv:Header/>
            <soapenv:Body>
                <tem:TraceByWaybillNo>
                    <tem:ClientInfo>
                        <tem:ClientID>%s</tem:ClientID>
                        <tem:Password>%s</tem:Password>
                        <tem:Version>9.0</tem:Version>
                    </tem:ClientInfo>
                    <tem:WaybillNo>%s</tem:WaybillNo>
                </tem:TraceByWaybillNo>
            </soapenv:Body>
            </soapenv:Envelope>"""%(order_contact,order_contact_password,order.reference_id)
            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': 'http://tempuri.org/TraceByWaybillNo',  # Adjust SOAPAction according to your service
            }
            # Send the SOAP request
            response = requests.post(url, data=soap_envelope.encode('utf-8'), headers=headers)
            try:
                namespace = {'soap': 'http://schemas.xmlsoap.org/soap/envelope/', 'ns': 'http://tempuri.org/'}
                xml_res = response.content.decode('utf-8')
                root = ET.fromstring(xml_res)
                activities = root.findall('.//ns:Activity', namespace)
                activity_codes = root.findall('.//ns:ActivityCode', namespace)
                if activity_codes and activities:
                    activity = activities[-1].text
                    activity_code = activity_codes[-1].text
                    order.write({"naqel_status":activity,"naqel_status_code":activity_code})
            except Exception as e:
                order.message_post(body=_("Failed to get status from Naqel because of %s")%(str(e)))

    @api.one
    @api.depends('naqel_ref_id')
    def create_naqel_barcode(self):
        if (self.naqel_ref_id):
            import barcode
            from barcode.writer import ImageWriter
            import io
            import base64
            barcode.base.Barcode.default_writer_options['write_text'] = False

            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.naqel_ref_id, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.piece_barcode = encoded

    def get_naqel_logs(self):
        address_form_id = self.env.ref('olivery_naqel.view_tree_rb_delivery_naqel_status_logs').id
        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Naqel logs',
            'res_model': 'rb_delivery.naqel_status_logs',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
    def map_fields(self,log_vals,required_fields_ids,order):
        for field in required_fields_ids:
            if field.naqel_field_name.name == 'destination':
                area = order.customer_area
                if area.naqel_area_code:
                    log_vals[field.naqel_field_name.name] = area.naqel_area_code
            else:
                if field.required_field_id.ttype == 'date' or field.required_field_id.ttype == 'datetime':
                    if order[field.required_field_id.name]:
                        date_value = datetime(order[field.required_field_id.name].year,order[field.required_field_id.name].month,order[field.required_field_id.name].day)
                        temp = datetime(1899, 12, 30)
                        delta = date_value - temp
                        log_vals[field.naqel_field_name.name] = int(float(delta.days) + (float(delta.seconds) / 86400))
                    elif field.required_field_id.name == 'reschedule_date':
                        tomorrow = datetime.today() + timedelta(days=1)
                        date_value = datetime(tomorrow.year,tomorrow.month,tomorrow.day)
                        temp = datetime(1899, 12, 30)
                        delta = date_value - temp
                        log_vals[field.naqel_field_name.name] = int(float(delta.days) + (float(delta.seconds) / 86400))
                    else:
                        log_vals[field.naqel_field_name.name] = ''
                else:
                    log_vals[field.naqel_field_name.name] = order[field.required_field_id.name]
        return log_vals

    def create_naql_logs(self, orders, values):
        for order in orders:
            order = order.sudo()
            if order.is_naqel_order:
                self.create_log_entry(order, values)
            if order.sent_to_naqel and order.needs_naqel_delivery:
                filter_extra = [('current_server_as_supplier', '=', True)]
                self.create_log_entry(order, values, filter_extra=filter_extra)
        return True

    def create_log_entry(self, order, values, filter_extra=None):
        log_vals = {}
        log_vals['waybill_no'] = str(int(order.reference_id))
        log_vals['piece_barcode'] = order.naqel_ref_id
        log_vals['order_id'] = order.id
        if order.scan_station_code and order.scan_station_code.naqel_area_code:
            log_vals['ss_name'] = order.scan_station_code.naqel_area_code

        domain = [('olivery_status', '=', order['state'])]
        if 'reject_reason' in values and values['reject_reason']:
            domain.append(('reject_reason','=',values['reject_reason']))
        else:
            domain.append(('reject_reason', '=', False))
        if filter_extra:
            domain.extend(filter_extra)
        status_map = self.env['rb_delivery.naqel_status_map'].sudo().search(domain, limit=1)
        if status_map:
            status_map = status_map[0]
            log_vals['event_code'] = status_map.naqel_event_code
            time_fmt = "%H:%M"
            user_timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
            local_tz = pytz.timezone(user_timezone)
            today = datetime.now(local_tz)
            temp = datetime(1899, 12, 30)
            date_value = datetime(today.year, today.month, today.day)
            delta = date_value - temp
            today_time = datetime.strftime(today, time_fmt)
            log_vals['event_date'] = int(float(delta.days) + (float(delta.seconds) / 86400))
            log_vals['event_time'] = today_time
            if status_map.required_fields_ids:
                log_vals = self.map_fields(log_vals, status_map.required_fields_ids, order)

        if log_vals and status_map:
            self.env['rb_delivery.naqel_status_logs'].sudo().create(log_vals)

    def move_files_to_done(self,filename,dir_name,sftp):
        if dir_name == '/Inbound':
            dest_path = '/Inbound/Done'
            try:
                sftp.stat(dest_path)
                sftp.rename('/Inbound/' + filename, dest_path +'/'+ filename)
            except FileNotFoundError as e:
                sftp.mkdir(dest_path)  # Create remote_path
                sftp.rename('/Inbound/' + filename, dest_path +'/'+ filename)
            except Exception as e:
                print(e)
        elif dir_name == '/Inbound/Done':
            dest_path = '/Inbound/Done/Archived'
            try:
                sftp.stat(dest_path)
                sftp.rename('/Inbound/Done/' + filename, dest_path +'/'+ filename)
            except FileNotFoundError as e:
                sftp.mkdir(dest_path)  # Create remote_path
                sftp.rename('/Inbound/Done/' + filename, dest_path +'/'+ filename)
            except Exception as e:
                print(e)
        elif dir_name == '/ReWeight_N':
            dest_path = '/ReWeight_N/Done'
            try:
                sftp.stat(dest_path)
                sftp.rename('/ReWeight_N/' + filename, dest_path +'/'+ filename)
            except FileNotFoundError as e:
                sftp.mkdir(dest_path)  # Create remote_path
                sftp.rename('/ReWeight_N/' + filename, dest_path +'/'+ filename)
            except Exception as e:
                print(e)

    def get_naqel_orders(self):
        self.with_delay(channel="root.create_naqel_orders",max_retries=2).get_orders_from_server()
    
    def get_orders_from_server(self,dir_name=False,date=False,name=False,use_delay=False):
        hostname, username, password = self.env['rb_delivery.naqel_info'].get_naqel_config()
        if hostname and username and password:
            try:
                cnopts = pysftp.CnOpts()
                cnopts.hostkeys = None

                with pysftp.Connection(host=hostname, username=username, password=password, cnopts=cnopts) as sftp:

                    print("Connection succesfully established … ")
                    dirs = ['/Inbound', '/Inbound/Done', '/Inbound/Done/Archived', '/Outbound', '/Reweight_N', '/Reweight_N/Done']
                    if dir_name and dir_name != 'All':
                        dirs = [dir_name]
                    elif not dir_name:
                        dirs = ['/Inbound', '/Reweight_N']
                    for dir in dirs :
                        sftp.cwd(dir)
                        draft_vals = []
                        if name:
                            clean_filename = os.path.splitext(name)[0]
                            extension = str(os.path.splitext(name)[1]).lower()
                            self.proccess_file(filename=name, clean_filename=clean_filename, extension=extension, sftp=sftp, draft_vals=draft_vals,dir_name=dir,date=date)

                        else:
                            directory_structure = sftp.listdir_attr()
                            for unprocessed_file in directory_structure:

                                filename = unprocessed_file.filename

                                if name and not (name in filename):
                                    continue

                                clean_filename = os.path.splitext(filename)[0]
                                extension = str(os.path.splitext(filename)[1]).lower()

                                self.proccess_file(filename=filename, clean_filename=clean_filename, extension=extension, sftp=sftp, draft_vals=draft_vals,dir_name=dir,date=date)
                        if len(draft_vals)>0:
                            self.create_draft_logs(draft_vals,use_delay)
            except Exception as e:
                raise Warning(_(str(e)))
        else:
            raise ValidationError(_("Add Naqel SFTP Server Hostname, Username, and Password"))
        return True
    
    def guard_function(self,values):
        if 'state' in values and values['state']:
            state = self.env['rb_delivery.status'].sudo().search([('name', '=', values['state']), '|', ('status_type', '=', False), ('status_type', '=', 'olivery_order')], limit=1)
            if (state and state.status_action_ids and  len(state.status_action_ids)>0):
                prevent_action = state.status_action_ids.filtered(lambda action: action.name == 'prevent_change_status_for_international_orders')
                if prevent_action:
                    for record in self:
                        if record.needs_naqel_delivery and not record.sent_to_naqel:
                            raise ValidationError(_("You cannot change the status for an international order that has not been sent to Naqel."))

        return super(olivery_naqel_order, self).guard_function(values)



    def proccess_file(self, filename, clean_filename, extension, sftp, draft_vals, dir_name,date=False):
        if 'Manifest' in filename:
            file_object = BytesIO()
            sftp.getfo(filename, file_object)

            if date:
                file_name = 'Manifest_'+date
                manifest_file_name = 'Manifest_WaybillPiece_'+date
                manifest_summary_file_name = 'Manifest_Summary_'+date
            else:
                date = os.path.splitext(clean_filename)[1]
                file_name = clean_filename
            manifest_file_name = 'Manifest_WaybillPiece_'+date
            manifest_summary_file_name = 'Manifest_Summary_'+date
            if file_name in clean_filename and extension in ['.xls','.xlsx'] and  "Manifest_WaybillPiece_" not in clean_filename and "Manifest_Summary_" not in clean_filename:
                workbook = xlrd.open_workbook(file_contents=file_object.getvalue())
                first_sheet = workbook.sheet_by_index(0)
                rows = first_sheet.nrows
                columns = first_sheet.ncols

                first_row = []
                for row_index in range (rows):
                    draft_val = {}

                    customer_address = ""
                    product_note = ""
                    skip_order = False
                    for col_index in range(columns):
                        if skip_order:
                            continue
                        draft_val['file_name'] = clean_filename
                        if row_index == 0:
                            first_row.append(str(first_sheet.cell_value(row_index, col_index)).lower().strip())
                        else:
                            if first_row[col_index] == 'waybillno':
                                draft_val['reference_id'] = str(int(first_sheet.cell_value(row_index, col_index)))
                                order  = self.env['rb_delivery.order'].search([('reference_id','=',draft_val['reference_id'])])
                                if order:
                                    skip_order = True
                                    continue
                            elif first_row[col_index] == 'consigneefirstname':
                                draft_val['customer_name'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'pickupdate':
                                draft_val['schedule_pickup_date'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'isasr':
                                asr_val = first_sheet.cell_value(row_index, col_index)
                                if asr_val and isinstance(asr_val, float):
                                    asr_val = str(int(asr_val))
                                else:
                                    asr_val = str(asr_val)
                                if asr_val and asr_val == '1':
                                    order_type = self.env.ref('olivery_naqel.naqel_returned_order')
                                    draft_val['order_type_id'] = order_type.id
                            elif first_row[col_index] == 'consigneephonenumber':
                                draft_val['customer_mobile'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'consigneemobile':
                                if first_sheet.cell_value(row_index, col_index) and first_sheet.cell_value(row_index, col_index) != "null":
                                    draft_val['second_mobile_number'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'consigneestreet':
                                customer_address = customer_address + first_sheet.cell_value(row_index, col_index) +" "
                            elif first_row[col_index] == 'consigneenear':
                                draft_val['consignee_near'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'consigneecitycode':
                                draft_val['customer_area'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'consigneecountrycode':
                                draft_val['customer_country'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'piecescount':
                                draft_val['no_of_items'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'netweight':
                                draft_val['order_weight'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'codcharge':
                                draft_val['copy_total_cost'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'goodsdescription':
                                product_note = product_note + first_sheet.cell_value(row_index, col_index) +" "
                            elif first_row[col_index] == 'itemdescription':
                                product_note = product_note + first_sheet.cell_value(row_index, col_index) +" "
                    if row_index != 0:
                        if customer_address:
                            draft_val['customer_address'] = customer_address
                        else:
                            draft_val['customer_address'] = 'No address'
                        if product_note:
                            draft_val['product_note'] = product_note
                        naqel_business = self.env.ref('olivery_naqel.user_naqel_bs')
                        if naqel_business:
                            draft_val['assign_to_business'] = naqel_business.id
                        if not skip_order:
                            draft_vals.append(draft_val)
                self.move_files_to_done(filename,dir_name,sftp)
            elif manifest_file_name in clean_filename and extension in ['.xls','.xlsx'] and len(draft_vals)>0:
                workbook = xlrd.open_workbook(file_contents=file_object.getvalue())
                first_sheet = workbook.sheet_by_index(0)
                rows = first_sheet.nrows
                columns = first_sheet.ncols

                first_row = []
                for row_index in range (rows):
                    col_index = 0
                    if row_index == 0:
                        first_row.append(str(first_sheet.cell_value(row_index, col_index)).lower().strip())
                    else:
                        if first_row[col_index] == 'waybillno':
                            for draft_val in draft_vals:
                                if draft_val['reference_id'] == str(int(first_sheet.cell_value(row_index, col_index))) and first_sheet.cell_value(row_index, col_index+1):
                                    draft_val['naqel_ref_id'] = str(int(first_sheet.cell_value(row_index, col_index+1)))
                self.move_files_to_done(filename,dir_name,sftp)
            elif manifest_summary_file_name in clean_filename and extension in ['.xls','.xlsx'] and len(draft_vals)>0:
                self.move_files_to_done(filename,dir_name,sftp)
        elif 'Reweight_N' in filename:
            file_object = BytesIO()
            sftp.getfo(filename, file_object)
            val_arr = []
            order_arr = []
            if extension in ['.xls','.xlsx']:
                workbook = xlrd.open_workbook(file_contents=file_object.getvalue())
                first_sheet = workbook.sheet_by_index(0)
                rows = first_sheet.nrows
                columns = first_sheet.ncols

                first_row = []
                for row_index in range (rows):
                    draft_val = {}

                    customer_address = ""
                    product_note = ""
                    skip_order = False
                    order = False
                    for col_index in range(columns):
                        if row_index == 0:
                            first_row.append(str(first_sheet.cell_value(row_index, col_index)).lower().strip())
                        else:
                            if first_row[col_index] == 'waybillno':
                                draft_val['reference_id'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'chargeableweight':
                                order = self.env['rb_delivery.order'].search([('reference_id','=',str(int(draft_val['reference_id']))), ('state', '!=', 'deleted')])
                                if order:
                                    order_arr.append(order)
                                    val_arr.append({'chargeable_weight': first_sheet.cell_value(row_index, col_index)})
                                else:
                                    found_item = next((item for item in draft_vals if item["reference_id"] == str(int(draft_val['reference_id']))), None)
                                    if found_item:
                                        found_item['chargeable_weight'] = first_sheet.cell_value(row_index, col_index)
                            elif first_row[col_index] == 'incoterm':
                                order = self.env['rb_delivery.order'].search([('reference_id','=',str(int(draft_val['reference_id']))), ('state', '!=', 'deleted')])
                                if order:
                                    order_arr.append(order)
                                    is_custom_duty_pay_by_consignee = True if first_sheet.cell_value(row_index, col_index) == 'DDU' else False
                                    val_arr.append({'incoterm': first_sheet.cell_value(row_index, col_index), 'is_custom_duty_pay_by_consignee':is_custom_duty_pay_by_consignee})
                                    
                                else:
                                    found_item = next((item for item in draft_vals if item["reference_id"] == str(int(draft_val['reference_id']))), None)
                                    if found_item:
                                        is_custom_duty_pay_by_consignee = True if first_sheet.cell_value(row_index, col_index) == 'DDU' else False
                                        found_item['incoterm'] = first_sheet.cell_value(row_index, col_index)
                                        found_item['is_custom_duty_pay_by_consignee'] = is_custom_duty_pay_by_consignee

                if val_arr and order_arr:
                    self.with_delay(channel="root.basic",max_retries=2).write_jq(val_arr,order_arr,self._context)
                    self.move_files_to_done(filename,dir_name,sftp)




    def create_draft_logs(self,draft_vals,use_delay):
        messages = []
        draft_recs = []
        status = self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        for draft_val in draft_vals:
            file_name = draft_val['file_name']
            del draft_val['file_name']
            draft_val['is_naqel_order'] = True
            if status:
                draft_val['state'] = status.name
            draft_item_ids = []
            if 'consignee_near' in draft_val and draft_val['consignee_near']:
                customer_area = self.env['rb_delivery.area'].search([('name','=',draft_val['consignee_near'])])
                if customer_area:
                    draft_val['customer_area'] = customer_area.name
                del draft_val['consignee_near']
            for field_name in draft_val:
                if field_name == 'customer_area':
                    customer_area = self.env['rb_delivery.area'].search(['|',('naqel_area_code','=',draft_val['customer_area']),('name','=',draft_val['customer_area'])],limit=1)
                    if customer_area:
                        draft_val['customer_area'] = customer_area.id
                    else:
                        messages.append(_("Area of code "+draft_val['customer_area']+" does not exist."))
                elif field_name == 'customer_country':
                    customer_country = self.env['rb_delivery.country'].search([('naqel_country_code','=',draft_val['customer_country'])])
                    if customer_country:
                        draft_val['customer_country'] = customer_country.id
                    else:
                        messages.append(_("Country of code "+draft_val['customer_country']+" does not exist."))
                field = self.env['ir.model.fields'].sudo().search([('name','=',field_name),('model','=','rb_delivery.order')])
                draft_item = self.env['rb_delivery.order_draft_item'].sudo().create({'name':draft_val[field_name].encode('utf-8', 'ignore').decode('utf-8') if isinstance(draft_val[field_name], str) else draft_val[field_name],'field_id':field.id})
                draft_item_ids.append(draft_item.id)

            try:
                values = {'order_draft_item_ids':[(6,0,draft_item_ids)],'file_name':file_name}
                draft_rec = self.env['rb_delivery.order_draft'].sudo().create(values)
                draft_recs.append(draft_rec)
            except Exception as e:
                messages.append(_("Draft failed to be created because of "+ str(e)))
        create_orders_directly = self.env['rb_delivery.client_configuration'].get_param('directly_create_order_from_draft_orders')
        if create_orders_directly and len(draft_recs) > 0:
            batch_list = [draft_recs[i:i + 20] for i in range(0, len(draft_recs), 20)]
            for batch in batch_list:
                if use_delay:
                    self.env['rb_delivery.order_draft'].with_delay(channel="root.create_draft_orders",max_retries=2).create_orders_draft(batch)
                else:
                    self.env['rb_delivery.order_draft'].create_orders_draft(batch)

        return {"messages":messages}
    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    def check_area(self, values, area_value,rb_delivery_user):
        naqel_area = self.env['rb_delivery.area'].search([('naqel_area_code','=',area_value)])
        if naqel_area:
            return naqel_area.id
        else:
            return super(olivery_naqel_order, self).check_area(values, area_value,rb_delivery_user)


    def _set_needs_naqel_delivery(self, values):
        if not values.get('customer_country'):
            return
        country_data = self.env['rb_delivery.country'].search_read([('id', '=', values['customer_country'])],['naqel_country_code'],limit=1)
        if not values.get('is_naqel_order') and country_data and country_data[0]['naqel_country_code'] != 'JO':
            values['needs_naqel_delivery'] = True
            
                
    @api.multi
    def write(self,values):
        if 'reject_reason' in values and values['reject_reason']:
            if str(values['reject_reason']).isnumeric():
                values['reject_reason'] = int(values['reject_reason'])
        res = super(olivery_naqel_order, self).write(values)
        if 'state' in values and values['state']:
            self.with_delay(channel="root.create_naqel_logs",max_retries=2).create_naql_logs(self,values)
        if 'customer_country' in values and values['customer_country']:
            self.update_country_order_by_one(values)
        return res

    def update_country_order_by_one(self, values):
        values_arr = []
        orders = []

        country_data = self.env['rb_delivery.country'].search_read(
            [('id', '=', values['customer_country'])], ['naqel_country_code'], limit=1
        )

        for rec in self:
            values_copy = {}
            needs_naqel_delivery = False

            if not rec.is_naqel_order and country_data and country_data[0]['naqel_country_code'] != 'JO':
                needs_naqel_delivery = True

            values_copy['needs_naqel_delivery'] = needs_naqel_delivery

            if values_copy:
                values_arr.append(values_copy)
                orders.append(rec)

        if values_arr and orders:
            self.with_delay(channel="root.basic", max_retries=2).write_jq(values_arr, orders, self._context)

    @api.model
    def create(self, values):
        if 'incoterm' in values and values['incoterm']:
            if values['incoterm'] == 'DDU':
                values['is_custom_duty_pay_by_consignee'] = True
            elif values['incoterm'] == 'DDP':
                values['is_custom_duty_pay_by_consignee'] = False
        elif 'is_custom_duty_pay_by_consignee' in values:
            if values['is_custom_duty_pay_by_consignee']:
                values['incoterm'] = 'DDU'
            else:
                values['incoterm'] = 'DDP'
        self._set_needs_naqel_delivery(values)
        return super(olivery_naqel_order, self).create(values)

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
