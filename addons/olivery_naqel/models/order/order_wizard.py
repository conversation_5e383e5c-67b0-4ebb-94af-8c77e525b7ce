# -*- coding: utf-8 -*-
from logging import exception
from openerp import models, fields, api,_
from odoo.exceptions import ValidationError
from datetime import datetime,date
import requests
from openerp import sql_db as sql_db
import xml.etree.ElementTree as ET

class order_get_naqel_orders(models.TransientModel):
    _name = 'rb_delivery.get_naqel_orders'

    date = fields.Date("Date")

    directory = fields.Selection([('inbound','Inbound'),('out_bound','Outbound'),('done','Inbound/Done'),('Archive','Inbound/Done/Archived'),('reweight_n', 'Reweight_N'), ('reweight_n/done', 'Reweight_N/Done')],required=True)

    file_name = fields.Char('File Name')

    def get_orders(self):
        dir_name = ''
        if self.directory:
            if self.directory == 'done':
                dir_name = '/Inbound/Done'
            elif self.directory == 'Archive':
                dir_name = '/Inbound/Done/Archived'
            elif self.directory == 'reweight_n':
                dir_name = '/ReWeight_N'
            elif self.directory == 'reweight_n/done':
                dir_name = '/ReWeight_N/Done'
            elif self.directory == 'inbound':
                dir_name = '/Inbound'
            else:
                dir_name = 'All'
        if self.date and self.directory:
            fmt = "%d%m%Y"
            date = datetime.strftime(self.date, fmt)
            self.env['rb_delivery.order'].get_orders_from_server(dir_name,date,self.file_name,use_delay=True)
        elif self.file_name and dir_name:
            self.env['rb_delivery.order'].get_orders_from_server(dir_name,False,self.file_name,use_delay=True)


class order_send_orders_to_naqel(models.TransientModel):
    _name = 'rb_delivery.send_orders_to_naqel'

    naqel_contact = fields.Many2one("olivery_naqel.naqel_contact","Naqel Contact")

    def send_orders(self):
        naqel_client_id = ''
        naqel_pass = ''
        if not self.naqel_contact:
            confs = self.env['rb_delivery.client_configuration'].get_param(['naqel_send_orders_password','naqel_client_id'])
            naqel_pass = confs.get('naqel_send_orders_password')
            naqel_client_id = confs.get('naqel_client_id')
        else:
            naqel_pass = self.naqel_contact.contact_password
            naqel_client_id = self.naqel_contact.name
        orders_ids = self._context.get('active_ids', [])
        url = "https://infotrack.naqelexpress.com/NaqelAPIServices/NaqelAPI/9.0/XMLShippingService.asmx"
        orders = self.env['rb_delivery.order'].browse(orders_ids)
        for order in orders:
            customer_name = order.customer_name or 'Customer name'
            customer_mobile = order.customer_mobile or '0000000000'
            customer_address = order.customer_address or 'Customer address'
            customer_area_code = order.customer_area.naqel_area_code or 'AMM'
            customer_country_code = order.customer_country.naqel_country_code or 'JO'
            order_sequence = order.sequence
            cost = order.money_collection_cost
            if cost>0:
                billing_type = 5
            else:
                billing_type = 1
            declare_value = order.declare_value
            quantity = order.no_of_items or '1'
            business_area_code = order.assign_to_business.area_id.naqel_area_code or 'AMM'
            business_country_code = order.assign_to_business.country_id.naqel_country_code or 'JO'
            business_mobile = order.assign_to_business.mobile_number or '0799710001'
            business_address = order.assign_to_business.address or 'الصويفية شارع علي نصوح الظاهر عمارة رقم ٥٨'
            soap_envelope = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
            <soapenv:Header/>
            <soapenv:Body>
                <tem:CreateWaybill>
                    <tem:_ManifestShipmentDetails>
                        <tem:ClientInfo>
                        <tem:ClientAddress>
                            <tem:PhoneNumber>%s</tem:PhoneNumber>
                            <tem:FirstAddress>%s</tem:FirstAddress>
                            <tem:Location>Amman</tem:Location>
                            <tem:CountryCode>%s</tem:CountryCode>
                            <tem:CityCode>%s</tem:CityCode>
                        </tem:ClientAddress>
                        <tem:ClientContact>
                            <tem:Name>SPEX</tem:Name>
                            <tem:Email><EMAIL></tem:Email>
                            <tem:PhoneNumber>0799710001</tem:PhoneNumber>
                        </tem:ClientContact>
                        <tem:ClientID>%s</tem:ClientID>
                        <tem:Password>%s</tem:Password>
                        <tem:Version>9.0</tem:Version>
                        </tem:ClientInfo>
                        <tem:ConsigneeInfo>
                        <tem:ConsigneeNationalID>0</tem:ConsigneeNationalID>
                        <tem:ConsigneeName>%s</tem:ConsigneeName>
                        <tem:Email></tem:Email>
                        <tem:Mobile>%s</tem:Mobile>
                        <tem:PhoneNumber>%s</tem:PhoneNumber>
                        <tem:Address>%s</tem:Address>
                        <tem:CountryCode>%s</tem:CountryCode>
                        <tem:CityCode>%s</tem:CityCode>
                        </tem:ConsigneeInfo>
                        <tem:_CommercialInvoice>
                        <tem:InvoiceNo>%s</tem:InvoiceNo>
                        </tem:_CommercialInvoice>
                        <tem:CurrenyID>7</tem:CurrenyID>
                        <tem:BillingType>%s</tem:BillingType>
                        <tem:PicesCount>%s</tem:PicesCount>
                        <tem:Weight>%s</tem:Weight>
                        <tem:CODCharge>%s</tem:CODCharge>
                        <tem:CreateBooking>false</tem:CreateBooking>
                        <tem:isRTO>false</tem:isRTO>
                        <tem:GeneratePiecesBarCodes>false</tem:GeneratePiecesBarCodes>
                        <tem:PiecesCount>1</tem:PiecesCount>
                        <tem:LoadTypeID>34</tem:LoadTypeID>
                        <tem:DeclareValue>%s</tem:DeclareValue>
                        <tem:RefNo>%s</tem:RefNo>
                        <tem:InsuredValue>0</tem:InsuredValue>
                        <tem:IsInsurance>false</tem:IsInsurance>
                        <tem:GoodsVATAmount>0</tem:GoodsVATAmount>
                        <tem:IsCustomDutyPayByConsignee>%s</tem:IsCustomDutyPayByConsignee>
                        %s
                    </tem:_ManifestShipmentDetails>
                </tem:CreateWaybill>
            </soapenv:Body>
            </soapenv:Envelope>"""%(business_mobile,business_address,business_country_code,business_area_code,naqel_client_id,naqel_pass,customer_name,customer_mobile,customer_mobile,customer_address,customer_country_code,customer_area_code,order_sequence,str(billing_type),str(quantity),str(order.order_weight),str(cost),str(declare_value),order_sequence,str(order.is_custom_duty_pay_by_consignee),self.get_extra_fields(order))

            # Set the headers for the SOAP request
            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': 'http://tempuri.org/CreateWaybill',  # Adjust SOAPAction according to your service
            }
            # Send the SOAP request
            integration_logs_vals = {"user_id":self._uid,"order_id":order.id,"name":order.sequence,"integration_body":soap_envelope.encode('utf-8')}
            response = requests.post(url, data=soap_envelope.encode('utf-8'), headers=headers)
            try:
                xml_res = response.content.decode('utf-8')
                root = ET.fromstring(xml_res)
                namespaces = {
                    'soap': 'http://schemas.xmlsoap.org/soap/envelope/',
                    'tem': 'http://tempuri.org/'
                }

                # Navigate through the XML tree to find specific elements
                body = root.find('soap:Body', namespaces)
                create_waybill_response = body.find('tem:CreateWaybillResponse', namespaces)
                create_waybill_result = create_waybill_response.find('tem:CreateWaybillResult', namespaces)

                # Extract the relevant information
                has_error = create_waybill_result.find('tem:HasError', namespaces).text
                if has_error != 'true':
                    waybill_no = create_waybill_result.find('tem:WaybillNo', namespaces).text
                    key = create_waybill_result.find('tem:Key', namespaces).text
                    order.write({'reference_id':waybill_no,'sent_to_naqel':True,'naqel_contact':self.naqel_contact.id})
                    print("WaybillNo:", waybill_no)
                    print("Key:", key)
                    integration_logs_vals['success'] = True
                    integration_logs_vals['returned_message'] = response.text
                if has_error == 'true':
                    message = _("Issue in send order to Naqel system: %s")%(create_waybill_result.find('tem:Message', namespaces).text)
                    integration_logs_vals['success'] = False
                    integration_logs_vals['returned_message'] = message
                else:
                    message = create_waybill_result.find('tem:Message', namespaces).text
                    integration_logs_vals['success'] = False
                    integration_logs_vals['returned_message'] = message
                integration_logs = self.env['rb_delivery.naqel_integration_logs'].sudo().create(integration_logs_vals)
                order.message_post(body=message)
                self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
                # Print the extracted information
                print("HasError:", has_error)

                print("Message:", message)

            except Exception as e:
                order.message_post(body=str(e))
            # Print the response
            print(response.status_code)
            print(response.content.decode('utf-8'))

    @api.model
    def get_extra_fields(self, order):
        extra_fields = self.env['olivery_naqel.naqel_extra_fields'].sudo().search([])
        extra_fields_str = ''
        for extra_field in extra_fields:

            value = ''
            if extra_field.olivery_field_name.ttype == 'date' or extra_field.olivery_field_name.ttype == 'datetime' and order[extra_field.olivery_field_name.name]:
                value = order[extra_field.olivery_field_name.name].strftime('%d/%m/%Y')
            elif extra_field.olivery_field_name.ttype == 'boolean':
                value = 'true' if order[extra_field.olivery_field_name.name] else 'false'
            elif extra_field.olivery_field_name.ttype == 'many2one' and order[extra_field.olivery_field_name.name]:
                value = order[extra_field.olivery_field_name.name].display_name
            elif extra_field.olivery_field_name.ttype in ['one2many','many2many']:
                for record in order[extra_field.olivery_field_name.name]:
                    value += record.display_name + ','
            else:
                value = order[extra_field.olivery_field_name.name] if order[extra_field.olivery_field_name.name] else ''

            extra_fields_str += """<tem:%s>%s</tem:%s>\n"""%(extra_field.naqel_field_name,value,extra_field.naqel_field_name)

        return extra_fields_str