odoo.define('rb_delivery.action_naqel_button', function (require) {

    "use strict";

    var core = require('web.core');
    var ListController = require('web.ListController');
    var rpc = require('web.rpc');

    ListController.include({
       renderButtons: function($node) {
       this._super.apply(this, arguments);
           if (this.$buttons) {
             this.$buttons.find('.oe_action_button_refresh_naqel_orders').click(this.proxy('refresh_naqel_orders'));
             }
       },
       refresh_naqel_orders:function(){
        let self = this;
        var records = _.map(self.selectedRecords, function (id) {
            return self.model.localData[id];
        });
        var ids = _.pluck(records, 'res_id');
        rpc.query({
            model: 'rb_delivery.order',
            method: 'track_naqel_order',
            args: [ids]
        }).then(function (data){
            if(data){
                return true
            }
        })

       }


})
})
