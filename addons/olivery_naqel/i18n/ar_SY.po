# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_naqel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-03 08:34+0000\n"
"PO-Revision-Date: 2023-07-03 08:34+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_form_naqel_order
msgid "<span>Naqel logs</span>"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_naqel
#: code:addons/olivery_naqel/models/naqel_status_logs/naqel_status_logs_model.py:144
#: code:addons/olivery_naqel/models/order/order_model.py:248
#, python-format
msgid "Add Naqel SFTP Server Hostname, Username, and Password"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_area
msgid "Area Model"
msgstr ""

#. module: olivery_naqel
#: code:addons/olivery_naqel/models/order/order_model.py:268
#, python-format
msgid "Area of code customer_area does not exist."
msgstr ""

#. module: olivery_naqel
#: model:rb_delivery.status,title:olivery_naqel.status_at_naqel
msgid "At Naqel"
msgstr "الشحنة في منشأة الناقل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_naqel
#: model:rb_delivery.user,role_name:olivery_naqel.user_naqel_bs
msgid "Business Role"
msgstr ""

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_form_rb_delivery_get_naqel_orders
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_country
msgid "Country Model"
msgstr ""

#. module: olivery_naqel
#: code:addons/olivery_naqel/models/order/order_model.py:274
#, python-format
msgid "Country of code customer_country does not exist."
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_files__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_info__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__create_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_send_orders_to_naqel__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_files__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_info__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__create_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_send_orders_to_naqel__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__date
msgid "Date"
msgstr "التاريخ"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__date_reference
msgid "Date Reference"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__destination
msgid "Destination"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__directory
msgid "Directory"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__display_name
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__display_name
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__display_name
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_naqel
#: selection:rb_delivery.get_naqel_orders,directory:0
msgid "Done"
msgstr ""

#. module: olivery_naqel
#: code:addons/olivery_naqel/models/order/order_model.py:284
#, python-format
msgid "Draft failed to be created because of "
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__error
msgid "Error"
msgstr "خطأ"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__event_code
msgid "Event Code"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__event_date
msgid "Event Date"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__event_description
msgid "Event Description"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__event_time
msgid "Event Time"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__required_field_id
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__required_fields_ids
msgid "Fields"
msgstr "الحقول"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_naqel
#. openerp-web
#: code:addons/olivery_naqel/static/src/js/buttons.js:22
#: code:addons/olivery_naqel/static/src/xml/buttons.xml:6
#, python-format
msgid "Get Naqel Orders"
msgstr ""

#. module: olivery_naqel
#: model:ir.actions.server,name:olivery_naqel.cron_get_naqel_orders_ir_actions_server
#: model:ir.cron,cron_name:olivery_naqel.cron_get_naqel_orders
#: model:ir.cron,name:olivery_naqel.cron_get_naqel_orders
msgid "Get Naqel orders"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__id
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__id
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__id
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_naqel
#: selection:rb_delivery.get_naqel_orders,directory:0
msgid "Inbound"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_area__is_default
msgid "Is Default"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__is_naqel_order
msgid "Is Naqel Order"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_files____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_info____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map____last_update
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_send_orders_to_naqel____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_files__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_info__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__write_uid
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_send_orders_to_naqel__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_get_naqel_orders__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_files__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_info__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__write_date
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_send_orders_to_naqel__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_naqel
#: model:ir.ui.menu,name:olivery_naqel.menu_rb_delivery_naqel_configuration_top_menu
msgid "Naqel Configuration"
msgstr ""

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_search_naqel_order
msgid "Naqel Orders"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__naqel_ref_id
msgid "Naqel Reference ID"
msgstr ""

#. module: olivery_naqel
#: model:ir.actions.act_window,name:olivery_naqel.action_rb_delivery_naqel_status_logs
#: model:ir.ui.menu,name:olivery_naqel.menu_rb_delivery_naqel_status_logs
msgid "Naqel Status Logs"
msgstr ""

#. module: olivery_naqel
#: model:ir.actions.act_window,name:olivery_naqel.action_rb_delivery_naqel_status_map
#: model:ir.ui.menu,name:olivery_naqel.menu_rb_delivery_naqel_status_map
msgid "Naqel Status Map"
msgstr ""

#. module: olivery_naqel
#: model:rb_delivery.area,name:olivery_naqel.naqel_area_test
msgid "Naqel area"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_area__naqel_area_code
msgid "Naqel area code"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_country__naqel_country_code
msgid "Naqel country code"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__naqel_event_code
msgid "Naqel event code"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_field_map__naqel_field_name
msgid "Naqel field name"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_user__naqel_user_code
msgid "Naqel user code"
msgstr ""

#. module: olivery_naqel
#: selection:rb_delivery.naqel_status_logs,status:0
msgid "Not Synced"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__olivery_status
msgid "Olivery Status"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__order_id
msgid "Order"
msgstr "الأمر"

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_order
msgid "Order Model"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__piece_barcode
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__piece_barcode
msgid "Piece Barcode"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__received_by
msgid "Received By"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__reject_reason
msgid "Reject Reason"
msgstr "سبب الرفض/التعذر"

#. module: olivery_naqel
#: code:addons/olivery_naqel/models/order/order_model.py:313
#, python-format
msgid "Reschedule Date cannot be today or previous dates"
msgstr "تاريخ التأجيل لا يمكن ان يكون تاريخ اليوم او تاريخ اليوم السابق"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__ss_name
msgid "SS Name"
msgstr ""

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_form_rb_delivery_get_naqel_orders
msgid "Save"
msgstr "حفظ"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__scan_station_code
msgid "Scan Station Code"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__status
msgid "Status"
msgstr "الحالة"

#. module: olivery_naqel
#: selection:rb_delivery.naqel_status_logs,status:0
msgid "Synced"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_naqel
#: model:ir.actions.server,name:olivery_naqel.cron_upload_tracking_file_ir_actions_server
#: model:ir.cron,cron_name:olivery_naqel.cron_upload_tracking_file
#: model:ir.cron,name:olivery_naqel.cron_upload_tracking_file
msgid "Upload tracking file"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_user
msgid "User Model"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__waybill_no
msgid "Waybill no"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: olivery_naqel
#: model:ir.model.fields,help:olivery_naqel.field_rb_delivery_naqel_status_map__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_draft_create_order
msgid "rb_delivery.draft_create_order"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_get_naqel_orders
msgid "rb_delivery.get_naqel_orders"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_naqel_field_map
msgid "rb_delivery.naqel_field_map"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_naqel_status_logs
msgid "rb_delivery.naqel_status_logs"
msgstr ""

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_naqel_status_map
msgid "rb_delivery.naqel_status_map"
msgstr ""

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__naqel_status
msgid "Naqel status"
msgstr "حالة ناقل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__naqel_status_code
msgid "Naqel status code"
msgstr "كود حالة ناقل"

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_form_rb_deliveryـsend_orders_to_naqel
msgid "Are you sure you want to send orders to Naqel?"
msgstr "هل انت متاكد من ارسال الطلب لناقل؟"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__sent_to_naqel
msgid "Sent to Naqel"
msgstr "ارسلت لناقل"

#. module: olivery_naqel
#. openerp-web
#: code:addons/olivery_naqel/static/src/xml/buttons.xml:7
#, python-format
msgid "Refresh Naqel Orders"
msgstr "تحديث حالة طلبيات ناقل"

#. module: olivery_naqel
#: model:ir.actions.act_window,name:olivery_naqel.action_olivery_naqel_order_send_orders_to_naqel
msgid "Send Orders to naqel"
msgstr "ارسال الطلبيات لناقل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__schedule_pickup_date
msgid "Schedule pickup date"
msgstr "تاريخ الاستلام المجدول"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__needs_naqel_delivery
msgid "Needs Naqel Delivery"
msgstr "التوصيل مع ناقل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_map__current_server_as_supplier
msgid "Current Server As Supplier"
msgstr "النظم الحالي هو شركة شحن"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_order__declare_value
msgid "Declare value"
msgstr "القيمة المعلنة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__active
msgid "Active"
msgstr "نشط"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__integration_body
msgid "Body"
msgstr "المتن"

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_search_rb_delivery_naqel_integration_logs
msgid "By Date"
msgstr "التاريخ"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__user_id
msgid "Created By"
msgstr "انشئ بواسطة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__success
msgid "Success"
msgstr "ناجح"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__returned_message
msgid "Returned Message"
msgstr "الرسالة الراجعة"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__order_id
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_status_logs__order_id
msgid "Order"
msgstr "الطلبية"

#. module: olivery_naqel
#: model:ir.model,name:olivery_naqel.model_rb_delivery_naqel_integration_logs
msgid "Naqel Integration logs"
msgstr "سجلات الربط مع ناقل"

#. module: olivery_naqel
#: model:ir.actions.act_window,name:olivery_naqel.action_rb_delivery_naqel_integration_logs
#: model:ir.ui.menu,name:olivery_naqel.menu_rb_delivery_naqel_integration_logs
msgid "Naqel Integration Logs"
msgstr "سجلات الربط مع ناقل"

#. module: olivery_naqel
#: model:ir.model.fields,field_description:olivery_naqel.field_rb_delivery_naqel_integration_logs__name
msgid "Name"
msgstr "الاسم"

#. module: olivery_naqel
#: model_terms:ir.ui.view,arch_db:olivery_naqel.view_search_rb_delivery_naqel_integration_logs
msgid "Groups"
msgstr "المجموعات"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:46
#: code:addons/rb_delivery/models/error_log/error_log_model.py:46
#, python-format
msgid "Error while creating/updating naqel contact"
msgstr "خطأ في انشاء/تحديث الاتصال مع ناقل"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:47
#: code:addons/rb_delivery/models/error_log/error_log_model.py:47
#, python-format
msgid "The naqel contact id ({name}) you have choosen already exists."
msgstr "معرف الاتصال الذي اخترته موجود بالفعل."

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Please choose another name or make sure the name is correct."
msgstr " يرجى اختيار اسم آخر أو التأكد من صحة الاسم."
