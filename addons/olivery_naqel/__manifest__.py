# -*- coding: utf-8 -*-
{
    'name': "olivery_naqel",
    'summary': """
        Olivery Naqel Integration App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.1.48',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    'css': [
        'static/src/css/style.css',
    ],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/area/area_view.xml',
        'models/user/user_view.xml',
        'models/order/order_view.xml',
        'models/order/order_wizard_view.xml',
        'models/country/country_view.xml',
        'models/naqel_status_map/naqel_status_map_view.xml',
        'models/naqel_field_map/naqel_field_map_view.xml',
        'models/naqel_status_logs/naqel_status_logs_view.xml',
        'models/naqel_info/naqel_info_view.xml',
        'models/naqel_files/naqel_files_view.xml',
        'models/naqel_extra_fields/naqel_extra_fields_view.xml',
        'models/naqel_contacts/naqel_contact_view.xml',
        'models/naqel_integration_logs/naqel_integration_logs_view.xml',
        'views/module_view.xml',
        'views/templates.xml',
        'demo/cron_job.xml',
        'demo/client_conf.xml',
        'demo/user.xml',
        'demo/status_logs.xml',
        'demo/order_type_demo.xml',
        'demo/status_action.xml',
        'demo/archive_integration_logs.xml',
        'demo/error_log.xml'
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
