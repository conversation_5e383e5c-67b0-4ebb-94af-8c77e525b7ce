<?xml version="1.0"?>
<odoo>
  <data>

    <menuitem id="menu_rb_delivery_naqel_configuration_top_menu" name="Naqel Configuration" parent="rb_delivery.rb_delivery_top_menu" sequence="15" groups="rb_delivery.role_super_manager,base.group_system"/>

    <menuitem id="menu_rb_delivery_naqel_admin_configuration_top_menu" name="Naqel Configuration" parent="rb_delivery.menu_rb_delivery_extrconf" sequence="15" />

    <act_window id="action_rb_delivery_naqel_status_map" name="Naqel Status Map" res_model="rb_delivery.naqel_status_map" view_mode="tree,form"/>
    <menuitem id="menu_rb_delivery_naqel_status_map" name="Naqel Status Map" parent="rb_delivery.menu_rb_delivery_extrconf" sequence="15" action="action_rb_delivery_naqel_status_map" />

    <act_window id="action_rb_delivery_naqel_status_logs" name="Naqel Status Logs" res_model="rb_delivery.naqel_status_logs" view_mode="tree"/>
    <menuitem id="menu_rb_delivery_naqel_status_logs" name="Naqel Status Logs" parent="menu_rb_delivery_naqel_configuration_top_menu" sequence="1" action="action_rb_delivery_naqel_status_logs" />

    <act_window id="action_naqel_info" name="Naqel Info" target="inline" res_model="rb_delivery.naqel_info" view_mode="form"/>
    <menuitem id="menu_naqel_info" name="Naqel Info" parent="menu_rb_delivery_naqel_configuration_top_menu" sequence="15" action="action_naqel_info"/>

    <act_window id="action_rb_delivery_naqel_integration_logs" context="{'group_by':['create_date:month','create_date:day']}" name="Naqel Integration Logs" res_model="rb_delivery.naqel_integration_logs" view_mode="tree" target="current"/>
    <menuitem id="menu_rb_delivery_naqel_integration_logs"  name="Naqel Integration Logs" parent="menu_rb_delivery_naqel_configuration_top_menu" sequence="34" action="action_rb_delivery_naqel_integration_logs"/>

    <act_window id="action_olivery_naqel_order_send_orders_to_naqel" name="Send Orders to naqel" groups="base.group_system" src_model="rb_delivery.order" res_model="rb_delivery.send_orders_to_naqel" view_mode="form" target="new" multi="True" />

    <act_window id="action_olivery_naqel_naqel_extra_fields" name="Naqel field map" res_model="olivery_naqel.naqel_extra_fields" view_mode="tree" view_type="form"/>
    <menuitem id="menu_olivery_naqel_naqel_extra_fields" name="Naqel field map" parent="rb_delivery.menu_rb_delivery_configuration" sequence="15" action="action_olivery_naqel_naqel_extra_fields" groups="base.group_system"/>

    <act_window id="action_olivery_naqel_naqel_contact" name="Naqel Contacts" res_model="olivery_naqel.naqel_contact" view_mode="tree" view_type="form"/>
    <menuitem id="menu_olivery_naqel_naqel_contact" name="Naqel Contacts" parent="rb_delivery.menu_rb_delivery_configuration" sequence="15" action="action_olivery_naqel_naqel_contact" groups="base.group_system,rb_delivery.role_super_manager"/>
  </data>
  </odoo>