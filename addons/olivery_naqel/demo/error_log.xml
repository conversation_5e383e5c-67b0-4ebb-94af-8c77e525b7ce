<odoo>
    <data>
        <record id="naqel_contact_error_1980" model="rb_delivery.error_log">
            <field name="error_title">Error while creating/updating naqel contact</field>
            <field name="name">The naqel contact id ({name}) you have choosen already exists.</field>
            <field name="what_to_do">Please choose another name or make sure the name is correct.</field>
            <field name="error_code">1980</field>
            <field name="error_type">warning</field>
            <field name="model_name">olivery_naqel.naqel_contact</field>
        </record>
    </data>
</odoo>