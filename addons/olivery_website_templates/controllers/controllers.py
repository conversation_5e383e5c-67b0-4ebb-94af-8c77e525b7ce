# -*- coding: utf-8 -*-
import odoo.http as http
from odoo.http import request
from openerp.http import request, Response
from openerp import _
import re
import json

class your_class(http.Controller):
     @http.route('/home', type='http', auth='public', website=True)
     def show_home_webpage(self, **kw):
          return http.request.render('olivery_website_templates.home_page', {})

     @http.route('/about', type='http', auth='public', website=True)
     def show_about_webpage(self, **kw):
          return http.request.render('olivery_website_templates.about_page', {})
     
     @http.route('/delete_your_account', type='http', auth='public', website=True)
     def delete_your_account(self, **kw):
          return http.request.render('olivery_website_templates.delete_your_account', {})

     @http.route('/remove_user', auth='none', type='http', csrf=False)
     def remove_user(self, **rec):
        # Extract the host from the request
        host = http.request.httprequest.host
        match = re.match(r"^(.*?)\.olivery\.(app|io)$", host)
        if match:
            db = match.group(1)
        else:
            return http.Response(
                json.dumps({
                    'code': 400,
                    'fail': True,
                    'message': _('Invalid URL. Unable to determine the database name.'),
                }),
                content_type='application/json',
                status=400
            )

        password = http.request.params.get('password')
        login = http.request.params.get('login')

        if not db or not password or not login:
            return http.Response(
                json.dumps({
                    'code': 400,
                    'fail': True,
                    'message': _('Missing required parameters: db, login, or password.'),
                }),
                content_type='application/json',
                status=400
            )

        try:
            uid = request.session.authenticate(db, login, password)
        except Exception:
            return http.Response(
                json.dumps({
                    'code': 403,
                    'fail': True,
                    'message': _('Failed to authenticate. Invalid username or password.'),
                }),
                content_type='application/json',
                status=403
            )

        if not uid:
            return http.Response(
                json.dumps({
                    'code': 403,
                    'fail': True,
                    'message': _('Failed to authenticate.'),
                }),
                content_type='application/json',
                status=403
            )

        user = request.env['rb_delivery.user'].sudo().search([('user_id', '=', uid)], limit=1)
        if not user:
            return http.Response(
                json.dumps({
                    'code': 401,
                    'fail': True,
                    'message': _('User does not exist.'),
                }),
                content_type='application/json',
                status=401
            )

        try:
            user.sudo().wkf_mobile_action_deactivate()
            # Logout the session
            request.session.logout()
            return http.Response(
                json.dumps({
                    'code': 200,
                    'success': True,
                    'message': _('User and associated documents have been successfully removed. You are now logged out.'),
                }),
                content_type='application/json',
                status=200
            )
        except Exception as e:
            return http.Response(
                json.dumps({
                    'code': 500,
                    'fail': True,
                    'message': _('An error occurred while removing the user: ') + str(e),
                }),
                content_type='application/json',
                status=500
            )