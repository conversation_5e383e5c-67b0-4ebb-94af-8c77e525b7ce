# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_website_templates
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-26 15:23+0000\n"
"PO-Revision-Date: 2023-11-26 15:23+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "#{terms_and_conditions}"
msgstr " "

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "1. <PERSON><PERSON> <PERSON>, Bisan Building"
msgstr "1. رام الله - البيرة ، بناية بيسان "

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "2. Jerusalem"
msgstr "2. القدس"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "3. Hebron - Ras Al Jora, Opposite Paradise Sweets"
msgstr "3. الخليل - راس الجورة ، مقابل حلويات برادايس"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "4. Nablus - Zawata Roundabout"
msgstr "4. نابلس - مفرق زواتا"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "5. Nazareth - Ein Mahel"
msgstr "5. الناصرة - عين ماهل"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "<b>Business Information</b>"
msgstr "<b>معلومات التاجر</b>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "<span style=\"direction:ltr\">0593150120</span>"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "<strong>Created On:</strong>"
msgstr "<strong>تاريخ الانشاء:</strong>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "<strong>Previous status:</strong>"
msgstr "<strong>الحالة السابقة:</strong>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "<strong>Reference:</strong>"  
msgstr "<strong>الرقم المرجعي:</strong>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "<strong>Sequence:</strong>"
msgstr "<strong>الرقم التسلسلي:</strong>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "<strong>Status:</strong>"
msgstr "<strong>الحالة:</strong>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "About Us"
msgstr "عنا"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_background_url
msgid "About Us  Background URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_text
msgid "About Us Section Message"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "About Us!"
msgstr "عنا!"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_4
msgid "Additional About Us Image"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__values_json
msgid "Additional Tips"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Address"
msgstr "العنوان"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Advantages"
msgstr "ما يميزنا"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Already have an account?"
msgstr "ألديك حساب بالفعل؟"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__app_store_url
msgid "App Store URL"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Area"
msgstr "المنطقة"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_background
msgid "Background Image for About Us Page (1440x800)"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__login_background
msgid "Background Image for Login Page (1440x1024)"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Bank Information"
msgstr "معلومات لبنك"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Bank Name"
msgstr "اسم البنك"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Bank Number"
msgstr "رقم البنك"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "Branch Locations"
msgstr "مواقع الفروع"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Business Information"
msgstr "معلومات التاجر"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Commercial Activity"
msgstr "النشاط التجاري"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Commercial Name"
msgstr "الاسم التجاري "

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__location
msgid "Company Location Address"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__company_name
msgid "Company Name"
msgstr "اسم المؤسسة"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Configuration"
msgstr "الإعدادات"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Confirm Password"
msgstr "تأكيد كلمة المرور"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "Contact Us"
msgstr "تواصل معنا"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__contact_us_text
msgid "Contact Us Section Message"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Customer Satisfaction"
msgstr "رضا العملاء"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
msgid "Database"
msgstr "قاعدة البيانات"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Delivery between pick-up points within one or in different cities."
msgstr "احضار الطلبات داخل المدينة وخارجها"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Delivery has become easier, Get Started Today!"
msgstr "أصبحت عملية التوصيل أسهل، ابدأ اليوم!"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Delivery to the address of the recipient."
msgstr "توصيل الطرود الى العنوان الذي تريده"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "Download Now"
msgstr "تحميل الان"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Efficient and reliable logistics solutions, focused on customer satisfaction."
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_3
msgid "End Image for About Us"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_footer
msgid "Enjoy all our services"
msgstr "استمتعوا بجميع خدماتنا"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter App Store URL"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter JSON values"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter Play Store URL"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter about us text"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter company name"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter contact us WhatsApp"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter contact us text"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter first advantage description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter first advantage title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter first service description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter first service title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter home title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter location"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter main color"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second about us image description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second about us image title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second advantage description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second advantage title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second service description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter second service title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter secondary color"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third about us image description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third about us image title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third advantage description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third advantage title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third service description"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Enter third service title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Excellence"
msgstr "الخدمة بامتياز"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Express delivery"
msgstr "توصيل الطرود"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_1
msgid "First Advantage Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_title_1
msgid "First Advantage Title"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_1
msgid "First Service Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_title_1
msgid "First Service Title"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Footer"
msgstr "التذييل"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
msgid "Forgot your password?"
msgstr "نسيت كلمة المرور"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Home"
msgstr "الرئيسية"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "I have read and agree to the"
msgstr "لقد قرأ و وافقت على "

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_1_image
msgid "Image for First Advantage"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_1_image
msgid "Image for First Service"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_2_image
msgid "Image for Second Advantage"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_2_image
msgid "Image for Second Service"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_3_image
msgid "Image for Third Advantage"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_3_image
msgid "Image for Third Service"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Improve inventory efficiency and accelerate your response to changing customer demand. Activities include Label Stickers, packing, re-packing and others."
msgstr "نقدم لكم أفضل خدمة في مجال التخزين لمشاريعكم وطرودكم والاستجابة السريعة لتغييرات العملاء، خدماتنا تشمل ملصق الباركود والتعبئة وإعادة التعبئة والعديد من الخدمات الأخرى"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Kangaroo"
msgstr "كنجارو"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Kangaroo: Your special provider for delivery services. At Kangaroo, we specialize in providing first-class local  delivery services. Our commitment to quality is matched only by our dedication to innovation. By using developed technological solutions. we ensure that every package is handled with care and precision. Our operations are efficient through automated processes, ensuring efficient and reliable delivery every time. Choose Kangaroo for a smooth delivery experience that connects you quickly and safely to your destinations."
msgstr "كنجارو: مزودك المتميز لخدمات توصيل الطرود. في كنجارو، نحن متخصصون في تقديم خدمات توصيل طرود محلية من الطراز الأول. التزامنا بالجودة لا يضاهيه إلا تفانينا في الابتكار. من خلال استخدام حلول تكنولوجية متطورة، نضمن أن يتم التعامل مع كل طرد بعناية ودقة. تتميز عملياتنا بالكفاءة من خلال العمليات الآلية، مما يضمن توصيلًا فعالًا وموثوقًا في كل مرة. اختر كنجارو لتجربة توصيل سلسة تربطك بسرعة وأمان بوجهاتك"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Leading the future of delivery with innovation and excellence."
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
msgid "Log in as superuser"
msgstr "تسجيل الدخول كمستخدم خارق"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.view_form_rb_delivery_website_configuration
msgid "Login"
msgstr "تسجيل الدخول"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__login_background_url
msgid "Login Background URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__footer_logo
msgid "Logo Displayed in Footer"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "Main Menu"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Mission"
msgstr "المهمة"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Mobile Number"
msgstr "رقم الموبايل"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "No Results"
msgstr "لا يوجد نتائج"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Our services"
msgstr "خدماتنا"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Password"
msgstr "كلمة المرور"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Payment Method"
msgstr "طريقة السداد"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Personal Information"
msgstr "المعلومات الشخصية"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
msgid "Phone/Email"
msgstr "رقم الموبايل/البريد الالكتروني"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Pick-Up Points"
msgstr "احضار الطلبات"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__play_store_url
msgid "Play Store URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image
msgid "Primary About Us Image"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__main_color
msgid "Primary Theme Color"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "Register now"
msgstr "سجل الان"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Scroll down"
msgstr "التمرير للاسفل"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_2
msgid "Second Advantage Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_title_2
msgid "Second Advantage Title"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_2
msgid "Second Service Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_title_2
msgid "Second Service Title"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__secondary_color
msgid "Secondary Theme Color"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_login_container
msgid "Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" title=\"Database\"/>"
msgstr "اختيار <i class=\"fa fa-database\" role=\"img\" aria-label=\"قاعدة البيانات\" title=\"قاعدة البيانات\"/>"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Sign up"
msgstr "تسجيل"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "SignUp"
msgstr "تسجيل حساب"

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_2
msgid "Start Image for About Us"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Terms and Conditions"
msgstr "الاحكام و الشروط"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Terms and Conditions:"
msgstr "الاحكام و الشروط: "

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_3_text
msgid "Text for End Image in About Us"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_3
msgid "Third Advantage Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__advantage_title_3
msgid "Third Advantage Title"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_3
msgid "Third Service Description"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__service_title_3
msgid "Third Service Title"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_2_text
msgid "Title for End Image in About Us"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_2_title
msgid "Title for First Image "
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__home_title
msgid "Title for Home Page"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_3_title
msgid "Title for Secound Image "
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Traceable Deliveries"
msgstr "تتبع الشحنات"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "Traching Order"
msgstr "تتبع طردك"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_header
msgid "Track Order"
msgstr "تتبع الطرد"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "Track your order"
msgstr "تتبع طردك"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "User Name"
msgstr "اسم المستخدم"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Values"
msgstr "القيم"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.about_page
msgid "Vision"
msgstr "الرؤية"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Wallet Name"
msgstr "اسم المحفظة"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.custom_signup_container
msgid "Wallet Number"
msgstr "رقم المحفظة"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "Warehousing"
msgstr "خدمة التخزين"

#. module: olivery_website_templates
#: model:ir.actions.act_window,name:olivery_website_templates.action_rb_delivery_website_configuration
#: model:ir.ui.menu,name:olivery_website_templates.menu_rb_delivery_website_configuration
msgid "Website Configuration"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__contact_us_whatsapp
msgid "WhatsApp Contact Number"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_2_url
msgid "image 2 URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_3_url
msgid "image 3 URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__about_us_image_4_url
msgid "image 4 URL"
msgstr ""

#. module: olivery_website_templates
#: model:ir.model,name:olivery_website_templates.model_rb_delivery_website_configuration
msgid "rb_delivery.website_configuration"
msgstr ""

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "search"
msgstr "بحث"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.order_tracking_template_custom
msgid "sequence"
msgstr "الرقم التسلسلي"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "track order"
msgstr "تتبع الطرد"

#. module: olivery_website_templates
#: model_terms:ir.ui.view,arch_db:olivery_website_templates.home_page
msgid "رقم الطلبية"
msgstr ""


#. module: olivery_website_templates
#: model:ir.model.fields,field_description:olivery_website_templates.field_rb_delivery_website_configuration__contact_background
msgid "Contact Us Background Image (1440x1024)"
msgstr "صورة خلفية تواصل معنا (1440x1024)"