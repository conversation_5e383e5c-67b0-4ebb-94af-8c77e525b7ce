<odoo>
    <data>

        <record id="view_form_rb_delivery_website_configuration" model="ir.ui.view">
    <field name="name">view_form_rb_delivery_website_configuration</field>
    <field name="model">rb_delivery.website_configuration</field>
    <field name="arch" type="xml">
        <form>
            <sheet>
                <notebook>
                    <page string="Home">
                        <group>
                            <group name="group-left">
                                <label for="company_name"/>
                                <div>
                                    <field name="company_name" nolabel="1" placeholder="Enter company name" col="2"/>
                                </div>
                                <label for="home_title"/>
                                <div>
                                    <field name="home_title" nolabel="1" placeholder="Enter home title" col="2"/>
                                </div>
                                <label for="login_background"/>
                                <div>
                                    <div style="max-width:150px; max-height:150px;">
                                        <field name="login_background" widget='image' class="o_field_image"/>
                                    </div>
                                </div>
                                <label for="contact_background"/>
                                <div>
                                    <div style="max-width:150px; max-height:150px;">
                                        <field name="contact_background" widget='image' class="o_field_image"/>
                                    </div>
                                </div>
                            </group>

                        </group>
                    </page>

                    <page string="Configuration">
                        <group>
                            <group string="Login" name="group-right">
                                <field name="show_remove_user_button"/>
                                <label for="secondary_color"/>
                                <div>
                                    <field name="secondary_color"  widget="color" nolabel="1" placeholder="Enter secondary color" col="2"/>
                                </div>

                                <label for="first_header_color"/>
                                <div>
                                    <field name="first_header_color"  widget="color" nolabel="1" placeholder="Enter First Header Color" col="2"/>
                                </div>
                                <label for="second_header_color"/>
                                <div>
                                    <field name="second_header_color"  widget="color" nolabel="1" placeholder="Enter Second Header Color" col="2"/>
                                </div>
                            </group>
                            <group string="Footer" name="group-left">
                                <label for="location"/>
                                <div>
                                    <field name="location" nolabel="1" placeholder="Enter company Location" col="2"/>
                                </div>

                                <label for="play_store_url"/>
                                <div>
                                    <field name="play_store_url" nolabel="1" placeholder="Enter Play Store URL" col="2"/>
                                </div>
                                <label for="app_store_url"/>
                                <div>
                                    <field name="app_store_url" nolabel="1" placeholder="Enter App Store URL" col="2"/>
                                </div>
                                <label for="footer_image"/>
                                <div>
                                    <field name="footer_image" widget='image' class="o_field_image"/>
                                </div>

                                <label for="footer_image_link"/>
                                <div>
                                    <field name="footer_image_link" nolabel="1" placeholder="Enter App Store URL" col="2"/>
                                </div>
                                <label for="footer_image_text"/>
                                <div>
                                    <field name="footer_image_text" nolabel="1" placeholder="Enter App Store URL" col="2"/>
                                </div>

                                <!-- Social Media Fields -->
                                <label for="whatsapp_number"/>
                                <div>
                                    <field name="whatsapp_number" nolabel="1" placeholder="Enter WhatsApp Number" col="2"/>
                                </div>
                                <label for="facebook_url"/>
                                <div>
                                    <field name="facebook_url" nolabel="1" placeholder="Enter Facebook URL" col="2"/>
                                </div>
                                <label for="instagram_url"/>
                                <div>
                                    <field name="instagram_url" nolabel="1" placeholder="Enter Instagram URL" col="2"/>
                                </div>
                                <label for="linkedin_url"/>
                                <div>
                                    <field name="linkedin_url" nolabel="1" placeholder="Enter LinkedIn URL" col="2"/>
                                </div>
                                <label for="twitter_url"/>
                                <div>
                                    <field name="twitter_url" nolabel="1" placeholder="Enter Twitter URL" col="2"/>
                                </div>
                            </group>
                        </group>
                    </page>
                    <page string="About Us">
                        <group>
                            <group name="group-left">
                                <label for="about_us_text"/>
                                <div>
                                    <field name="about_us_text" nolabel="1" placeholder="Enter about us text" col="2"/>
                                </div>
                            </group>
                            <group name="group-right">
                                <label for="values_json"/>
                                <div>
                                    <field name="values_json" nolabel="1" placeholder="Enter JSON values" widget="json_widget_name" col="2"/>
                                </div>
                            </group>
                        </group>
                    </page>
                </notebook>
            </sheet>
        </form>
    </field>
        </record>

        <record id="view_tree_rb_delivery_website_configuration" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_website_configuration</field>
            <field name="model">rb_delivery.website_configuration</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="company_name"/>
                    <field name="about_us_text"/>
                </tree>
            </field>
        </record>
        
    </data>
</odoo>



      
    
    