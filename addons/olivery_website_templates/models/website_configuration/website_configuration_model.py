# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import  Warning
from odoo import tools

import re


class rb_delivery_website_configuration(models.Model):
    _name = 'rb_delivery.website_configuration'
    show_remove_user_button = fields.Boolean('Show Remove User Button',default=False)

    company_name = fields.Char(string='Company Name')
    home_title = fields.Char(string='Title for Home Page')
    values_json = fields.Char(string='Additional Tips', default='["Smart business solutions","Professional automative tools"]')
    about_us_text = fields.Text(string='About Us Section Text')
    play_store_url = fields.Char(string='Play Store URL')
    app_store_url = fields.Char(string='App Store URL')
    footer_image_link=fields.Char(string='Footer Image redirect URL')



    secondary_color = fields.Char(string='Secondary Theme Color')
    first_header_color= fields.Char(string='Secondary Theme Color')
    second_header_color= fields.Char(string='Second header Color')

    about_us_text = fields.Text(string='About Us Section Message')
    login_background = fields.Binary(string='main Background Image (1440x1024)')
    contact_background = fields.Binary(string='Contact Us Background Image (1440x1024)')
    footer_image = fields.Binary(string='Footer Image')
    footer_image_text= fields.Char(string='Footer Image Text')
    location=fields.Text(string='location')
    whatsapp_number = fields.Char(string='WhatsApp Number', help='Enter WhatsApp contact number')
    facebook_url = fields.Char(string='Facebook URL', help='Enter the Facebook page URL')
    instagram_url = fields.Char(string='Instagram URL', help='Enter the Instagram profile URL')
    linkedin_url = fields.Char(string='LinkedIn URL', help='Enter the LinkedIn profile URL')
    twitter_url = fields.Char(string='Twitter URL', help='Enter the Twitter (X) profile URL')

    login_background_url = fields.Char(
        string="Login Background URL",
        compute="_get_image_url",
        readonly=True,
    )
    
    footer_image_url = fields.Char(
        string="Footer Image URL",
        compute="_get_image_url",
        readonly=True,
    )

    contactus_image_url = fields.Char(
        string="Contact Us URL",
        compute="_get_image_url",
        readonly=True,
    )
  

    def _get_image_url(self):
        for rec in self:
            if rec.login_background:
                rec.login_background_url = tools.image_data_uri(rec.login_background)
            if rec.footer_image:
                rec.footer_image_url = tools.image_data_uri(rec.footer_image)
            if rec.contact_background:
                rec.contactus_image_url = tools.image_data_uri(rec.contact_background)
    






    

