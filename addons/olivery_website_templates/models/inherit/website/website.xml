<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="assets_frontend" inherit_id="web.assets_frontend">
            <xpath expr="." position="inside">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet"/>

                <link rel="stylesheet"
                    href="/olivery_website_templates/static/src/css/website_sheet.scss" />
                <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal" />
                <script src="/olivery_website_templates/static/src/js/main.js"></script>
                <!-- <script
                src="/olivery_website_templates/static/src/js/sticky_color.js"></script> -->


            </xpath>
        </template>
        <template id="custom_header" inherit_id="website.layout">


            <xpath expr="//div[@id='wrapwrap']/header" position="replace">
                <t t-set="website_config"
                    t-value="env['rb_delivery.website_configuration'].sudo().search([], limit=1)" />
                <!-- #3C4048 0%, #031829 -->
                <!-- <header id="myHeader"> -->

                <header id="myHeader"
                    t-attf-data-first-color="#{website_config.first_header_color or ''}"
                    t-attf-data-second-color="#{website_config.second_header_color or ''}">

                    <style type="text/css">
                        <!-- .sticky {
                            background: linear-gradient(to bottom, #3C4048 0%, #031829 100%)  !important;
                        } -->
                        <!-- website_config.first_header_color -->
                    </style>
                    <nav style="height:8vh">
                        <div class="website-navbar color-white">
                            <div style="display: flex; flex-direction:row">
                                <div class="nav-buttons hide-mob">
                                    <a href="/home">
                                        <button class="nav-button">
                                            <t>Home</t>
                                        </button>
                                    </a>
                                    <a href="/order_tracking">
                                        <button class="nav-button">
                                            <t>Track Order</t>
                                        </button>
                                    </a>
                                    <a href="/contactus">
                                        <button class="nav-button">
                                            <t>Contact Us</t>
                                        </button>
                                    </a>
                                    <a href="/about">
                                        <button class="nav-button">
                                            <t>About Us!</t>
                                        </button>
                                    </a>
                                </div>
                                <div class="nav-buttons hide-mob"
                                    style="text-align:end;margin-inline-end:40px;margin-left: auto; margin-right: 1vw">
                                    <a href="/web/login">
                                        <button class="nav-button signin-button">
                                            <t>Login</t>
                                        </button>
                                    </a>
                                    <a href="/olivery/sign_up/form">
                                        <button class="register-button color-white"
                                            t-attf-style="background: #{website_config.secondary_color or ''}">
                                            <t>Register now</t>
                                        </button>
                                    </a>

                                </div>

                                <!-- <div class="nav-buttons hide-mob">
                                <a href="/about"><button class="nav-button">About Us</button></a>
                                <a href="/order_tracking"><button class="nav-button">Tracking Order</button></a>
                                <a href="/web/login"><button class="nav-button signin-button">Login</button></a>
                                <a href="/olivery/sign_up/form"><button  class="register-button color-white"
                                t-attf-style="background: #00B5F5;">SignUp</button></a>
                            </div> -->
                                <button class="menu side-menu-button hide-web"
                                    aria-label="Main Menu">
                                    <svg width="55" height="55" viewBox="0 0 100 100">
                                        <path class="line line1"
                                            d="M 20,29.000046 H 80.000231 C 80.000231,29.000046 94.498839,28.817352 94.532987,66.711331 94.543142,77.980673 90.966081,81.670246 85.259173,81.668997 79.552261,81.667751 75.000211,74.999942 75.000211,74.999942 L 25.000021,25.000058" />
                                        <path class="line line2" d="M 20,50 H 80" />
                                        <path class="line line3"
                                            d="M 20,70.999954 H 80.000231 C 80.000231,70.999954 94.498839,71.182648 94.532987,33.288669 94.543142,22.019327 90.966081,18.329754 85.259173,18.331003 79.552261,18.332249 75.000211,25.000058 75.000211,25.000058 L 25.000021,74.999942" />
                                    </svg>
                                </button>
                                <div class="overlay-background" />
                                <div class="overlay is-closed">
                                    <a href="/about" style="margin-bottom:10px">
                                        <button class="nav-button color-black"
                                            style="text-align:start">
                                            <t>About Us</t>
                                        </button>
                                    </a>
                                    <a href="/order_tracking" style="margin-bottom:10px">
                                        <button class="nav-button color-black"
                                            style="text-align:start">
                                            <t>Traching Order</t>
                                        </button>
                                    </a>

                                    <a href="/web/login" style="margin-bottom:10px">
                                        <button class="nav-button signin-button color-black"
                                            style="text-align:start">
                                            <t>Login</t>
                                        </button>
                                    </a>
                                    <a href="/olivery/sign_up/form" style="margin-bottom:10px">
                                        <button class="register-button color-white"
                                            style="">
                                            <t>SignUp</t>
                                        </button>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </nav>
                </header>
            </xpath>


        </template>
        <template id="custom_footer" inherit_id="website.footer_custom">
            <xpath expr="//div[@id='footer']" position="replace">
                <t t-set="website_config"
                    t-value="env['rb_delivery.website_configuration'].sudo().search([], limit=1)" />
                <div class="row container-fluid">

                        <div class="col-12 col-md-4 text-center" style="padding-top:3.5vh">
                            <a
                                t-attf-href="{{ website_config.footer_image_link or 'https://website.olivery.app/' }}"
                                target="_blank">
                                <img class="logo"
                                    t-attf-src="{{ website_config.footer_image_url or '/olivery_website_templates/static/assets/olivery2.gif' }}" />
                            </a>
                            <br />
                            <p style="text-align:center;font-weight:bold;font-size:5pt">
                                <t t-esc="website_config.footer_image_text or 'Powered by Olivery'" />
                            </p>
                            <div class="language-selector-container"
                                style="display: flex; justify-content: center; align-items: center; max-height: 10px; font-size: 5pt;">
                                <t t-call="website.language_selector" />
                            </div>
                        </div>
                        <div class="col-12 col-md-4 text-center" style="padding-top:3.5vh">
                            <span class="font_styles_download">
                                <t>Download Now</t>
                            </span>
                            <br />
                            <span class="font_styles">
                                <t>Enjoy all our services</t>
                            </span>
                            <br />
                            <a
                                t-attf-href="#{website_config.play_store_url or 'https://play.google.com/store'}"
                                target="_blank">
                                <img class="nav-button store-img color-white"
                                    src="/olivery_website_templates/static/assets/googleplay.png" />
                            </a>
                            <a
                                t-attf-href="#{website_config.app_store_url or 'https://apps.apple.com'}"
                                target="_blank">
                                <img class="nav-button store-img color-white"
                                    src="/olivery_website_templates/static/assets/appstore.png" />
                            </a>
                        </div>
                        <div class="col-12 col-md-2 text-center" style="padding-top:3.5vh">
                            <t class="text-center" t-call="website.company_description" />
                        </div>
                        <div class="col-12 col-md-2 text-center" style="padding-top:3.5vh">
                            <!-- Contact Us Header -->
                            <t t-if="website_config.whatsapp_number or website_config.facebook_url or website_config.instagram_url or website_config.linkedin_url or website_config.twitter_url">
                                <span class="color-white" style="font-weight:bold; font-size:1.2rem;">Contact Us</span>
                                <br /><br />
                            </t>
                            
                            <!-- Social Media Links -->
                           <div class="d-flex flex-wrap justify-content-center" style="gap: 15px;">
                            <t t-if="website_config.whatsapp_number">
                                <a t-att-href="'https://wa.me/' + website_config.whatsapp_number" target="_blank" class="social-icon">
                                    <i class="fab fa-whatsapp" style="font-size:2rem; color: #25d366;"></i>
                                </a>
                            </t>
                            <t t-if="website_config.facebook_url">
                                <a t-att-href="website_config.facebook_url" target="_blank" class="social-icon">
                                    <i class="fab fa-facebook-f" style="font-size:2rem; color: #1877f2;"></i>
                                </a>
                            </t>
                            <t t-if="website_config.instagram_url">
                                <a t-att-href="website_config.instagram_url" target="_blank" class="social-icon">
                                    <i class="fab fa-instagram" style="font-size:2rem; color: #e4405f;"></i>
                                </a>
                            </t>
                            <t t-if="website_config.linkedin_url">
                                <a t-att-href="website_config.linkedin_url" target="_blank" class="social-icon">
                                    <i class="fab fa-linkedin-in" style="font-size:2rem; color: #0077b5;"></i>
                                </a>
                            </t>
                            <t t-if="website_config.twitter_url">
                                <a t-att-href="website_config.twitter_url" target="_blank" class="social-icon">
                                    <i class="fab fa-twitter" style="font-size:2rem; color: #1da1f2;"></i>
                                </a>
                            </t>
                        </div>


                            <!-- Branch Locations -->
                            <t t-if="website_config.location">
                                <br />
                                <span class="color-white" style="font-weight:bold;">Branch Locations</span>
                                <br />
                                <span class="color-white">
                                    <t t-if="website_config.location">
                                    <t t-if="len(website_config.location.split('\n')) > 1">
                                            <t t-foreach="website_config.location.split('\n')"
                                                t-as="location">
                                            <t t-esc="location" />
                                            <br />
                                        </t>
                                    </t>
                                    <t t-else="">
                                        <t t-esc="website_config.location" />
                                        </t>
                                    </t>
                                </span>
                            </t>
                        </div>                  
                </div>
            </xpath>
        </template>
    </data>
</odoo>
        
        