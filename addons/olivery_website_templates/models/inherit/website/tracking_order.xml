<odoo>
	<data>
		<template id="order_tracking_template_custom" inherit_id="rb_delivery.order_tracking_template">
            <xpath expr="//div[@id='wrap']" position="replace">
                <t t-set="website_config" t-value="env['rb_delivery.website_configuration'].sudo().search([], limit=1)"/>
                <div class="banner track-banner" t-attf-style="background:url('#{website_config.login_background_url if website_config.login_background_url else '/olivery_website_templates/static/src/css/login_background.png'}')">
                    <div class="wrap">
                        <div class="container track-container">
                <div class="search_nav" t-attf-style="color: #{website_config.secondary_color or '00B5F5'};padding-bottom:5px; padding-top:5px;">
                                <h3> <t>Track your order</t> </h3>
                                <form action="/order_tracking" method="post" class="row p-3">
                                    <input  t-attf-style="border: 1px solid #{website_config.secondary_color or '00B5F5'};" type="text" placeholder="sequence" 
                                        name="order_seq"
                                        class="form-control-track col-8" t-att-value="order_seq"/>
                                    <input  type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <button type="submit"  class="btn btn-info col-3" t-attf-style="background:#{website_config.secondary_color or '00B5F5'}">search</button>
                                </form>
                                <table class="table" t-attf-style="color: #{website_config.secondary_color or '#F3A31A'};">
                                    <thead>
                                        <tr>
                                            <th>Sequence</th>
                                            <th class="track_table_reference_col">Reference</th>
                                            <th>Status</th>
                                            <th>Previous status</th>
                                            <th>Created On</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="orders" t-as="order">
                                            <tr>
                                                <td><span t-esc="order['sequence']"/>
                                                    <t t-if="order['reference_id']">
                                                        <br/>
                                                        <span class="track_table_sequence_and_reference_col">
                                                            *<t t-esc="order['reference_id']"/>*
                                                        </span>
                                                    </t>
                                    
                                                </td>
                                                <td class="track_table_reference_col"><span t-esc="order['reference_id']"/></td>
                                                <td><span t-esc="order['status']"/></td>
                                                <td><span t-esc="order['previous_status']"/></td>
                                                <td><span class="order_datetime" t-esc="order['created_on'].strftime('%d-%m-%Y %H:%M:%S')"/></td>
                                            </tr>
                                        </t>
                                        <t t-if="len(orders) == 0">
                                            <tr>
                                                <td colspan="5"  t-attf-style="color: #{website_config.secondary_color or '00B5F5'};font-weight:bold">No Results</td>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>

                            </div>                   
                        </div>
                    </div>
                </div>
            </xpath>
		</template>
	</data>
</odoo>
