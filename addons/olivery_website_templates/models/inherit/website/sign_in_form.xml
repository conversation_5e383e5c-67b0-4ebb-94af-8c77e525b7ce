<odoo>
    <data>
        <template id="custom_login_template" inherit_id="web.login" name="Custom Login">
            <xpath expr="//form[@class='oe_login_form']" position="replace">
                <t t-set="website_config" t-value="env['rb_delivery.website_configuration'].sudo().search([], limit=1)"/>
                <div id="wrap" style="margin-top:0;" class="oe_structure oe_empty ">
                        <div class="banner-custom" t-attf-style="height: calc(100vh - 215px);background:url('#{website_config.login_background_url if website_config.login_background_url else '/olivery_website_templates/static/src/css/login_background.png'}')">
                            <form class="oe_login_form" style="max-width: 300px;position: relative;margin: 100px auto;background: white;padding: 19px;border-radius: 19px" role="form" t-attf-action="/web/login{{ '?debug' if debug else '' }}" method="post" onsubmit="this.action = this.action + location.hash">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <div class="form-group field-db" t-if="databases and len(databases) &gt; 1">
                                    <label for="db" class="col-form-label">Database</label>
                                    <div t-attf-class="input-group {{'input-group-sm' if form_small else ''}}">
                                        <input type="text" name="db" t-att-value="request.db" id="db" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" required="required" readonly="readonly"/>
                                        <span class="input-group-append">
                                            <a role="button" href="/web/database/selector" class="btn btn-secondary">Select <i class="fa fa-database" role="img" aria-label="Database" title="Database"/></a>
                                        </span>
                                    </div>
                                </div>

                                <div class="form-group field-login">
                                    <label for="login">Email</label>
                                    <input type="text" placeholder="Email" name="login" t-att-value="login" id="login" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" required="required" autofocus="autofocus" autocapitalize="off"/>
                                </div>

                                <div class="form-group field-password">
                                    <label for="password">Password</label>
                                    <input type="password" placeholder="Password" name="password" id="password" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" required="required" autocomplete="current-password" t-att-autofocus="'autofocus' if login else None" maxlength="4096"/>
                                </div>

                                <p class="alert alert-danger" t-if="error" role="alert">
                                    <t t-esc="error"/>
                                </p>
                                <p class="alert alert-success" t-if="message" role="status">
                                    <t t-esc="message"/>
                                </p>

                                <div t-attf-class="clearfix oe_login_buttons text-center mb-1 {{'pt-1' if form_small else 'pt-1'}}">
                                    <t t-if="website_config.show_remove_user_button">
                                        <a href="/delete_your_account" class="text-danger" style="font-size: 14px; display: block; margin-bottom: 10px;">
                                            Remove your account?
                                        </a>
                                    </t>
                                    <button type="submit" class="btn btn-primary btn-block log-in-btn">Log in</button>
                                    <t t-if="debug">
                                        <button type="submit" name="redirect" value="/web/become" class="btn btn-link btn-sm btn-block">Log in as superuser</button>
                                    </t>
                                    <div class="o_login_auth"/>
                                </div>
        
                                <input type="hidden" name="redirect" t-att-value="redirect"/>
                            </form>
                        </div>
                    </div>
            </xpath>
        </template>
    </data>
</odoo>