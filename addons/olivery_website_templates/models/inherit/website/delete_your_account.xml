<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="delete_your_account" name="Delete Your Account">
            <!-- Check if the model exists -->
            <t t-if="env['ir.model'].sudo().search([('model', '=', 'rb_delivery.website_configuration')], limit=1)">
                <t t-set="website_config" t-value="env['rb_delivery.website_configuration'].sudo().search([], limit=1)"/>
                <t t-call="website.layout">
                    <div id="wrap" style="margin-top:0;" class="oe_structure oe_empty">
                        <div class="banner-custom" t-attf-style="height: calc(100vh - 215px);background:url('#{website_config.login_background_url if website_config.login_background_url else '/olivery_website_templates/static/src/css/login_background.png'}')">
                            <form class="oe_login_form__2" id="deleteAccountForm" style="max-width: 300px;position: relative;margin: 100px auto;background: white;padding: 19px;border-radius: 19px">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <div class="form-group field-login">
                                    <label for="login">Email</label>
                                    <input type="text" placeholder="Email" name="login" id="login" class="form-control" required="required" autofocus="autofocus" autocapitalize="off"/>
                                </div>

                                <div class="form-group field-password">
                                    <label for="password">Password</label>
                                    <input type="password" placeholder="Password" name="password" id="password" class="form-control" required="required" autocomplete="current-password" maxlength="4096"/>
                                </div>

                                <div class="form-check text-left" style="margin-top: 10px; margin-bottom: 10px;">
                                    <input type="checkbox" class="form-check-input" id="delete_confirm" name="delete_confirm" required="required"/>
                                    <label class="form-check-label" for="delete_confirm" style="color: #000; font-weight: bold;">
                                        "Are you sure? This action will remove all documents for the user."
                                    </label>
                                </div>

                                <div class="clearfix oe_login_buttons text-center">
                                    <button type="button" id="deleteAccountButton" class="btn btn-danger" style="font-weight: bold;">
                                        Confirm
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </t>
            </t>
            <t t-else="">
                <!-- Fallback content if the model is not installed -->
                <t t-call="website.layout">
                    <div id="wrap" style="margin-top:0;" class="oe_structure oe_empty">
                        <div class="alert alert-danger" style="max-width: 300px; margin: 100px auto; text-align: center;">
                            The required module is not installed. Please contact the administrator.
                        </div>
                    </div>
                </t>
            </t>
        </template>
    </data>
</odoo>
