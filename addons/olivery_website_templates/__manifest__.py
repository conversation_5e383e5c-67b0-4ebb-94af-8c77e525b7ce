# -*- coding: utf-8 -*-
{
    'name': "<PERSON><PERSON> Website templates",

    'summary': """This module provides feature for changing layout of website.""",

    'description': "2This module provides feature for changing layout of website.",

    'author': "Rubik",
    'website': "http://www.rubik.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/12.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Theme',
    'version': 'v-s-0.22',

    # any module necessary for this one to work correctly
    'depends': ['website','rb_delivery'],



    # always loaded
    'data': [
        'models/website_configuration/website_configuration_view.xml',
        'models/inherit/website/website.xml',
        'models/inherit/website/delete_your_account.xml',
        'models/inherit/website/about-us.xml',
        'models/inherit/website/forgot_password_page.xml',
        'models/inherit/website/contactus.xml',
        'views/module_view.xml',
        'security/ir.model.access.csv',
        'models/inherit/website/tracking_order.xml',
        'models/inherit/website/home_page.xml',
        'models/inherit/website/sign_in_form.xml',
        'models/inherit/website/signup_form.xml',
        'demo/configuration.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
    ],
    'installable': True,
    'application': True,
}
