$( document ).ready(function() {


        // Handle Delete Account functionality
        $('#deleteAccountButton').on('click', function (e) {
            e.preventDefault();
    
            // Gather form inputs
            const email = $('#login').val();
            const password = $('#password').val();
            const confirm = $('#delete_confirm').is(':checked');
    
            // Validate the form
            if (!email || !password) {
                alert('Please enter both email and password.');
                return;
            }
            if (!confirm) {
                alert('You must confirm to delete your account.');
                return;
            }
    
            // Call the /remove_user endpoint
            $.ajax({
                url: '/remove_user',
                type: 'POST',
                dataType: 'json',
                data: {
                    login: email,
                    password: password
                },
                success: function (response) {
                    if (response.success) {
                        alert(response.message);
                        // Redirect to home or another page after successful deletion
                        window.location.href = '/web/login';
                    } else {
                        alert(response.message || 'An error occurred while processing your request.');
                    }
                },
                error: function (xhr, status, error) {
                    const errorMessage = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'An unexpected error occurred. Please try again.';                    
                    alert(errorMessage);
                }
            });
        });

    $("footer").attr("style","background: unset !important")
    const banner=$(".banner")
    window.onscroll = function() {myFunction()};
    window.onload = function() {myFunction()};
    var header = document.getElementById("myHeader");
    var sticky = header.offsetTop;
    var firstColor = header.getAttribute('data-first-color') ;
    var secondColor = header.getAttribute('data-second-color') ;
    if(window.location.pathname.includes("/ar_SY/web/login") || window.location.pathname.includes("/olivery/sign_up/form")  || window.location.pathname.includes("/forgot_password") ){
        header.classList.add("sticky");

        if (firstColor && secondColor) {
            $(".sticky").css({
                "background": `linear-gradient(to bottom, ${firstColor} ${' '} 0%, ${secondColor} ${' '} 100%)`
            });
        }
    }
    else{        
        $(".sticky").css("background", "");
        header.classList.remove("sticky");

    }

    if(window.location.pathname === "/ar_SY/"  ||window.location.pathname === "/ar_SY" ) {
        window.location.href = "/ar_SY/web/login";
    }
    else if(window.location.pathname === "/en_US/" ||window.location.pathname === "/en_US"){
        window.location.href = "/en_US/web/login";
    }
    else if(window.location.pathname === "/"){
        window.location.href = "/web/login";

    }



    function myFunction() {
        
        header.classList.add("sticky");
        if (firstColor && secondColor) {
            $(".sticky").css({
                "background": `linear-gradient(to bottom, ${firstColor} ${' '} 0%, ${secondColor} ${' '} 100%)`
            });
        }


    }
    var amount = '';

    function scroll() {
    $('body').animate({
        scrollTop: amount
    }, 1000, 'linear', function() {
        if (amount != '') {
        scroll();
        }
    });
    }
    $('#hover').hover(function() {
    amount = $("#hover").offset().top-108;
    scroll();
    }, function() {
    amount = '';
    });
    const THRESHOLD = 20;

    $(document).ready(function () {
    var trigger = $('.side-menu-button'),
        overlay = $('.overlay'),
        header = $('#myHeader'),
        background = $('.overlay-background')
        isClosed = false;
        
        

        trigger.click(function () {
        hamburger_cross();      
        });
        background.click(function () {
        hamburger_cross();      
        });

        function hamburger_cross() {

        if (isClosed == true) {          
            overlay.removeClass('is-open');
            overlay.addClass('is-closed');
            trigger.removeClass('opened')
            background.removeClass('block')
            isClosed = false;
        } else {   
            overlay.removeClass('is-closed');
            overlay.addClass('is-open');
            header.addClass('sticky');
            trigger.addClass('opened')
            background.addClass('block');
            isClosed = true;
        }
    }

    $('[data-toggle="offcanvas"]').click(function () {
            $('#wrapper').toggleClass('toggled');
    });  
    });





    if(window.location.pathname=="/" || window.location.pathname=="/home" || window.location.pathname=="/ar_SY/home" ||window.location.pathname=="/en_US/home"){
        var trackBtn = document.getElementById('track-btn');
        var scrollDownButton = document.getElementById('scrollDownButton');
        if(scrollDownButton){
            scrollDownButton.onclick =function(){
                window.scrollTo({
                top: window.scrollY + window.innerHeight,
                behavior: 'smooth'
            });
            }
        }

        trackBtn.onclick = function(){
            const trackingNumber = document.getElementById('tracking-number').value
            var url = "/order_tracking"
            if(trackingNumber!=''){
                url += '?sequence='+trackingNumber.toString()
            }
            window.open(url,'_self') ;
        }

        var featurelistItems = document.getElementsByClassName('list-item');
        var paragraphs = document.getElementsByClassName('slider-paragraph');
        var offset = 0
        var o
        var switchForward = () => {
            if(offset>0){
                paragraphs[offset-1].style.display = 'none'
                paragraphs[offset-1].style.opacity = '0'
                paragraphs[offset-1].style.transform = 'translateX(100px)'
            }
            
            else if(offset==0){
  
            }
                        

                
            if(offset == paragraphs.length-1){
                o=offset
                offset=0
            }else{
                offset+=1
                o=offset-1
            }
            setTimeout(()=>{
            },100)
            
            
        }
        switchForward()
        var switchInterval = setInterval(switchForward, 10000);
        var switchBackward = () => {
            if(offset>0){
                paragraphs[offset-1].style.display = 'none'
                paragraphs[offset-1].classList.remove('op100')
            }
            
            else{
                paragraphs[paragraphs.length-1].style.display = 'none'
                paragraphs[paragraphs.length-1].classList.remove('op100')
            }
            

            paragraphs[offset].style.display = 'block'
            paragraphs[offset].classList.add('op100')

            if(offset == paragraphs.length-1){
                offset=0
            }else{
                offset+=1
            }
            
            
        }
        var switchTo = (index) => {
            clearInterval(switchInterval)
            for(let paragraph of paragraphs){
                paragraph.style.display = 'none'
                paragraph.classList.remove('op100')
            }
            paragraphs[index].style.display = 'block'
            paragraphs[index].classList.add('op100')
            switchInterval = setInterval(switchForward, 7000);
            
        }

    }
    if(window.location.pathname.includes("/order_tracking")){
        const urlParams = new URLSearchParams(document.location.search);
        const sequence = urlParams.get('sequence')
    var queryString = window.location.search;
    var params = new URLSearchParams(queryString);
    var sequenceValue = params.get('sequence');

if (sequenceValue !== null) {
    console.log("Sequence value exists and its value is:", sequenceValue);
    var orderSeqInput = document.getElementsByName("order_seq")[0];
    orderSeqInput.value = ""+sequenceValue;
        console.log("Sequence value is  present in the URL after field");

} else {
    console.log("Sequence value is not present in the URL");
}

    }

     
    $('input[type="checkbox"]').on('change', function() {
         $('input[type="checkbox"]').not(this).prop('checked', false);
    });
    
    let listItems = document.getElementsByClassName('list-item')
    let downloadBtn = $('.download-button')
    let donloadTxt = $('#download-text')
    if(window.location.href.includes("ar")){
        for(let item of listItems){
            item.style.setProperty("--langnumbers" ,'counters(item, \".\", arabic-indic)\" \"')
        }
    }
    else{
        downloadBtn.attr('style','-moz-transform: scaleX(-1); -o-transform: scaleX(-1); -webkit-transform: scaleX(-1);')
        donloadTxt.attr('style','-moz-transform: scaleX(-1); -o-transform: scaleX(-1); -webkit-transform: scaleX(-1);')
        for(let item of listItems){
            item.style.setProperty("--langnumbers" ,'counters(item, \".\", english)\" \"')
        }
    }
    
        
        
    
});
function expand(elem){
    parent = elem.parentNode
    if(parent.offsetHeight == 65 )
        parent.style.height='fit-content'
    else{
        parent.style.height='65px'
    }



}


