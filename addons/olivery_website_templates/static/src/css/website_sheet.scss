@keyframes scroll {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(calc(-16vw * 6))
    }
}
@keyframes scroll-small {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(calc(-50vw * 6))
    }
}
@keyframes scroll-medium {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(calc(-30vw * 6))
    }
}
@keyframes scroll-large {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(calc(-25vw * 6))
    }
}

html *{
    direction: ltr;
    text-align: start;
    font-family: Ta<PERSON>wal;
    font-weight: unset;
    
}
.oe_website_login_container{
    margin-top: 0px;
    // height: calc(100vh - 215px);
}
html,body {
    max-width: 100%;
    height: unset;
    overflow-x: hidden;
}
#wrap{
    margin-top: 218px;
}
.o_footer_copyright{
    display: none !important;

}
.oe_signup_form {
    max-width: 22%;
    position: relative;
    margin: 20px auto;
    padding: 35px;
    background: white;
    border-radius: 20px;
}

@media (max-width: 768px) { /* Adjust the max-width breakpoint as needed for mobile */
    .oe_signup_form {
        max-width: 80%;
    }
    .logo {
        object-fit: contain;
        width: 50vw; /* Set width to 50vw for smaller screens */
    }
    .track-banner{
        height: auto;
    }
    .register-button {
        padding: 8px 15px !important
    }
    .track_table_reference_col{
        display: none;
    }
    .track_table_sequence_and_reference_col{
        display: block;
    }
}
.search_nav{
    width: 98%;
    color: white;
    h3{
      padding-left: 28px;
      padding-right: 28px;
      height: 41px;  
      font-size: 24px;
      font-weight: bold;
      border-bottom: 2px solid #5ec3d7;
      width: 100%;
    }
}
button{
    outline: none !important;
}

.sticky {
    background-color: #F3A31A ;
    
}
.first_footer {
    background: linear-gradient(to bottom, #031829 0%, #cccccc 100%);
}
.main-container{
    height: 118px;
    background-color: #5ec3d7 !important;
}
.website-navbar{
    
    top: 0;
    padding: 10px 0;
    overflow: hidden;
    margin: 0 auto;
    max-height: fit-content;
}
.free-label{
    width: 17vw;
}
header{
    z-index: 1;
    position: fixed !important;
    top: 0;
    width: 100%;
    -webkit-transition: all .5s linear;
    -moz-transition: all .5s linear;
    -o-transition: all .5s linear;
    transition: all .5s linear;
}

button{
    width: 162px;
    border: none;
    cursor: pointer;
    overflow: hidden;
    outline: none;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
}
.nav-buttons{
    display: inline-block;
    width: 80vw;
    text-align: left;
    // position: absolute;
    right: 10vw;
    top: 30px;
    
}
.track-banner{
    height: calc(100vh - 215px) !important;
}

.nav-buttons_right{
    display: inline-block;
    width: 80vw;
    text-align: right;
    position: absolute;
    right: 10vw;
    top: 30px;
    
}
.nav-buttons button{
        max-width: 14vw;
        max-height: 5vw;
}

.nav-button{
    background-color: transparent;
    background-repeat: no-repeat;
    color: #fff;
    
}
.download-button {
    width: 186px;
    background-color: transparent;
    background: url(/olivery_website_templates/static/assets/download.png);
    background-repeat: repeat-x;
    background-size: 100% 11px;
    background-position: bottom;
    background-position-x: 100%;
    text-align: left;
    margin-bottom: 20px;
    transition: all 300ms;
    &:hover{
        background-position-x: 186px;
    }
}



.register-button {
    border-radius: 5px;
    box-shadow: 0 4px 40px 0 rgba(78, 78, 78, 0.2);
    background-color: #5ec3d7;
    padding: 15px 10px;
    overflow: hidden;
    text-decoration: none !important;
    transition: background-color 300ms;


}
.btn {
    border-radius: 5px;
    box-shadow: 0 4px 40px 0 rgba(78, 78, 78, 0.2);
    background-color: #5ec3d7;
    padding: 15px 10px;
    overflow: hidden;
    text-decoration: none !important;
    transition: background-color 300ms;


}
.signin-button{
    display: inline-block;
}
.logo {
    object-fit: contain;
    width: 18vw;
}
.logo-container{
    display: inline-block;
    max-width: 263px;
    height: 68px;
    width: 19vw;
}
.side-menu-button{
    position: fixed;
    top: 0;
    right: 5vw;
    height: 50px;
    width: 50px;
}
.overlay{
    position: fixed;
    background: #fff;
    box-shadow: 6px 6px 40px 0 rgba(47, 47, 47, 0.1);
    right: 0;
    top: 55px;
    height: 100vh;
    padding: 50px 0 100px 29px;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    -webkit-transition: all .25s linear;
    -moz-transition: all .25s linear;
    -o-transition: all .25s linear;
    transition: all .25s linear;
    

    
}
.overlay-background{
    position: fixed;
    right: 0;
    top: 55px;
    height: 100vh;
    width: 100vw;
    display: none;
    -webkit-transition: all .25s linear;
    -moz-transition: all .25s linear;
    -o-transition: all .25s linear;
    transition: all .25s linear;
    
}
.block{
    background-color: #26274580;
    display: block;
}
.is-closed{
    width: 0vw;
    padding: 0;
}
.is-open{
    width: 75vw;
}
.store-img {
    object-fit: cover;
    margin: 0 0 0 30px;
    border-radius: 5px;
    width: 218px;
    height: 52px;
    box-shadow: 0 3px 15px 0 rgba(255, 255, 255, 0.204);
}
.track-order{
    margin-top: 30px;
    height: 51px;
    display: flex;
    justify-content: center;
}
.banner-field {
    float: left;
    width: 307px;
    height: 100%;
    padding: 16px 15px 16px 15px;
    border-radius: 5px 0 0 5px !important;
    border: solid #202529;
    border-width: 1px 0 1px 1px;
    background-color: #fff;
    line-height: 0 !important;
    display: block;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1 px solid #ced4da;
    font-size: 18px;
    font-weight: bold;
    line-height: 3;


}
.track-order-button{
    height: 100%;
    border-radius:0 5px 5px 0 !important;
    background-color: #202529;
    padding: 15px 10px;
    border-radius: 5px;
    box-shadow: 0 4px 40px 0 rgba(78, 78, 78, 0.2);
    overflow: hidden;
    text-decoration: none !important;
    transition: background-color 300ms;
}

.log-in-btn {
    position: relative !important;
}

.banner {
    width: 100%;
    padding: 14vh 0 5vw;
    background: url("/olivery_website_templates/static/src/css/login_background.png");
    background-position: center !important;
    background-size: cover !important;
    color: #1f272b;
    height: 85vh;
    // padding-bottom: 200px;
}
.banner-custom {
    width: 100%;
    padding: 14vh 0 5vw;
    background: url("/olivery_website_templates/static/src/css/login_background.png");
    background-position: center !important;
    background-size: cover !important;
    color: #1f272b;
}

.contactus-banner{
    width: 100%;
    padding: 14vh 0 5vw;
    background-position: center !important;
    background-size: cover !important;
    color: #1f272b;

}


.about_us_banner {

    width: 100%;
    height: 88vh;
    max-height: 1134px;
    padding: 15vh 0 5vw;
    background:url("/olivery_website_templates/static/src/css/aboutUs.png");
    background-position: top;
    background-repeat: no-repeat;
    background-size: inherit;
    background-position-x:center;
    margin-bottom: 50px;

}

.title-container{
    padding-top: 10vh;
    width: 80vw;
    margin: 0 auto;
    
}
.content {
    width: 80vw;
    margin: 0 auto;

}

.banner-title {
    font-weight:bold !important;
    font-size: 46px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-align: left;
    
}
.subtitle {
    font-weight:500 !important;
    font-size: 22px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-align: left;

}
.title {
    font-weight: bold !important;
    font-size: clamp(22px, 3vw, 38px);
    font-stretch: normal;
    font-style: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-align: left;
    
    
}
// .color-black{
//     color: #5ec3d7;
// }
.color-white{
    color: #fff;
}
#hover{
    height: 1vh;
    width: 100%;
}
.hover-down-arrow{
    margin: 0 auto;
    text-align: center;
    width: fit-content;
    font-weight: bold;
    line-height: .5;
    position: absolute;
    top: clamp(1vw, 90vh, 510px);
    right: 0;
    left: 0;
}
.card{
    width: 25vw;
    height: 18vw;
    border-color: transparent;
    border-radius: 10px;
    box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
    background-position: center  !important;
    background-size: 36vw !important;
    
    display: inline-block;
    margin-bottom: 20px;
    overflow: hidden;
}
.price-cards {
    display: grid;
    grid-template-columns: 22% 22% 22% 22%;

}
.price-card {
    text-align: center;
    height: fit-content;
    border-color: transparent;
    border-radius: 10px;
    box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
    background-position: center !important;
    background-size: 36vw !important;
    display: inline-block;
    overflow: hidden;
}
.price-content{
    margin-block: 30px;
    text-align: center;
}
.price-card-header {
    width: 100%;
    height: 60px;
    border-radius: 10px !important;
    background-color: #5ec3d7;
    text-align: center;
    padding-top: 18px;
    line-height: 1;
}
.card-small {
    width: 25vw;
    height: 18vw;
    border-color: transparent;
    border-radius: 10px;
    box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
    background-position: center !important;
    background-size: 66% !important;
    background-repeat: no-repeat !important;
    display: inline-block;
    margin-bottom: 20px;
    overflow: hidden;
}
.long-card{
    width: 25vw;
    height: 21.58vw;
}
.about-card{
    width:50% !important;
    padding-block:5%;
    background-repeat: no-repeat !important;
}
.full-card {
    margin-block: 60px !important;
    width: 100% !important;
    height: 37vw;
    display: flex;
    flex-direction: row;
}
.inner-card-left{
    padding: 3.5%;
    width: 50%;
}
.inner-card-right {
    display: flex;
    width: 50%;
    background: linear-gradient(#26274580,#26274580) ,url("/olivery_website_templates/static/assets/mobile-app.webp");
    background-position: center;
    background-size: cover;
}
.cards-top{
    display: flex;
    flex-direction: row;
}
.card-margin{
    margin-right:2.5vw;
}
.card h3{
    text-align: center;
    font-size: clamp(14px, 2vw, 24px);
    font-weight: bold; 
    position: absolute;
    bottom: clamp(5px, 0.5vw, 15px);
    right: 0;
    left: 0;
}

.card-header {
    width: 100%;
    height: fit-content;
    margin: 0 0 154px;
    border-radius: 10px;
    background-color: #5ec3d7;
    text-align: center;
    padding-top: clamp(5px, 1.4vw, 18px);
    line-height: 1;
}
.card-title{
    font-weight: bold;
    font-size: clamp(10px, 1vw, 20px)
    
    
}
.banner-buttons{
    display: flex;
}
.paragraph{
    font-size:16px;
    line-height:20px;
    font-weight:500;
    text-align:justify;
}
.side-paragraph{
    width: 55vw;
    padding-right: 32px;
    place-self: center;
}
.sidecard-right{
    place-self: center;
}
.sidecard-left {
    place-self: center;
    margin-right: 32px;
}
.content-left{
    margin-top:60px;
    display:flex;
    direction: rtl;
}
.content-right{
    margin-top:60px;
    display:flex;
}

.hide-web{
    display: none !important;
}
.mobile-img{
    height: 30vw;
    border-radius: 10px;
    box-shadow: 0 8px 40px 0 rgba(47, 47, 47, 0.1);
    margin-right: 30px;
}
.image-bullet {
    margin-top: 12px !important;
    padding-left: 20px;
    margin-top: 18px;
    line-height: 20px;
    list-style: none;
    background-image: url("/olivery_website_templates/static/assets/check.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 16px;
}
.mob-list{
    padding: 0 !important;
}
.card-wide{
    margin-top: 30px;
    width: 100%;
    padding: 25px 10vw 30px 50px;
    height: fit-content;
}
.slider-paragraph{
    opacity: 0;
    display:none;
    padding: 25px;
    place-self: center;
    color: white;
    line-height: clamp(18px, 3.5vw, 60px);
    font-size: clamp(14px, 2vw, 18px);
    transform:translateX(100px);
    -webkit-transition: all .1s linear;
    -moz-transition: all .1s linear;
    -o-transition: all .1s linear;
    transition: all .1s linear;
    
}
.op100{
    opacity: 100;
}
ol {
    counter-reset: item;
    margin-right: 0;
    padding: 0;
    list-style: arabic-indic;

}

li {
    display: block;
    margin-bottom: .5em;
    margin-right: 2em;
}
h2{
   font-size: clamp(20px, 3vw, 38px);
}
.list-item{
    font-size: clamp(14px, 2vw, 24px);
    font-weight: bold;
    margin: clamp(4px, 2vw, 30px) 0;
    --langnumbers: counters(item, ".", english)" ";
}
.list-item::before {
    display: inline-block;
    content: var(--langnumbers);
    counter-increment: item;
    font-weight: bold;
    width: 1em;
    margin-right: 1%;
}
.inner-footer{
    padding-block: 30px;
    display: flex;
    height: fit-content !important;
    flex-direction: row;
    background: #5ec3d7;
    
}
.footer-elem{
    display: flex;
    height: fit-content !important;
    flex-direction: column;
    height: 75px;
    width: 33.3%;
}
ol {
    list-style-type: arabic-indic;
}
.footer-elem-row {
    display: flex;
    flex-direction: row;
    height: fit-content !important;
    height: 75px;
    width: 50%;
}
.footer-button{
    height: 30px !important;
    font-size: 13px;
    padding-right: 15px;
    padding-left: 0;
    width: max-content;

}
#footer{
    padding:15px 10vw;
    background: #5ec3d7;
    margin-top: 0px;
}
.tracking-table td{
    border:none;
    padding-block:25px;
    padding-inline:40px;
}
.tracking-table {
    background: url(/olivery_website_templates/static/src/css/dot-image.png);
    background-position-x: calc(100% - 15px);
    background-repeat: no-repeat;
    background-size: 13px;
    background-position-y: 25px;
}
.order_datetime{
    direction: rtl;
}
.color-black{
    color:#000000;
}
.dotted-right{
        width: 27px;
        height: 42px;
        position: absolute;
        background: url(/olivery_website_templates/static/src/css/dot-image.png);
        background-position-x: calc(100% - 18px);
        background-repeat: repeat-y;
        background-size: 6px;
        background-position-y: 24px;
        margin-top: -24px;
}
button{
    outline: none !important;
}

.list-container{
    margin-top: 60px;
    max-width: 750px;
}
.list-container li{
    font-size: 18px;
    font-weight: normal;
    margin-bottom: 25px;
    text-align: justify;
}
#faqs-container{
    margin-top: 60px;
}
#faqs-container>div{
    width: 100%;
    height: 65px;
    border-color: transparent;
    border-radius: 10px;
    background-color: #e9f1f7;
    box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
    background-position: center !important;
    background-size: 36vw !important;
    margin-bottom: 20px;
    overflow: hidden;
}
.q-card{
    width: 100%;
    height: 65px;
    border-color: transparent;
    border-radius: 10px;
    background-color: #ffffff;
    box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
    background-position: center !important;
    background-size: 36vw !important;
    margin-bottom: 20px;
    overflow: hidden;
}
.a-par{
    padding: 17px;
    text-align: justify;
}
.q-par{
    padding: 25px 15px;
    font-size: 16px;
}
.register_banner{
    width: 100%;
    padding: 7vh 0 5vw;
    background: url("/olivery_website_templates/static/src/css/login_background.png");
    background-position: center !important;
    background-size: cover;
    color:  #000000;
}
.track-container{
    background-color: white;
    font-family: 'Cairo';
    margin-top: 10px;
    padding: 35px 13px;
    border-radius: 50px;
    width: 40%;
    display:flex;
    justify-content:center
}
.container-fluid{
    background: #333333;
    color:white;
    margin-right:0px !important;
    margin-left:0px !important;
    height: 215px;
    
}
@media (min-width:1025px){
    .register-button{
        // background: linear-gradient(65deg, #F3A31A 20%, hsl(278, 85%, 95%) 50%, #5ec3d7 80%);
        background: linear-gradient(to bottom, #6fbd4f 0%, #5faa37 100%)  ;
        background-size: 100px;
        background-repeat: no-repeat;
        // background-color: #5ec3d7;
        background-position-x: -172px;
        &:hover{
            background-position-x: 172px;
            background-color: #F3A31A;
            transition: all 500ms;
        }
    }
    .font_styles_download{
        font-size: 24px;
        font-weight: bold;
    }
    .font_styles{
        font-size: 20px;
    }
    .btn{
        background: linear-gradient(to bottom, #6fbd4f 0%, #5faa37 100%)  ;
        background-size: 100px;
        background-repeat: no-repeat;
        background-color: #333333;
        background-position-x: -172px;
        &:hover{
            background-position-x: 172px;
            background-color: #f3a31b;
            transition: all 500ms;
        }
    }
    .track-order-button{
        background: linear-gradient(65deg, #004976 20%, hsl(278, 85%, 95%) 50%, #32749a 80%);
        background-size: 100px;
        background-repeat: no-repeat;
        background-color: #32749a;
        background-position-x: -172px;
        &:hover{
            background-position-x: 172px;
            background-color: #004976;
            transition: all 500ms;
        }
    }
}


@media (max-width:1100px) {

    .subtitle {
        font-size: 20px;
    }
    .nav-buttons {
        right: 5vw;
    }
    .banner-title {
        font-size: 32px;
    }

    .store-img {
        zoom: .9
    }

    .track-order {
        zoom: .9
    }
    
    .slide-track {
        animation: scroll-large 20s linear infinite !important;
        width: calc(25vw * 12) !important;
    }
    
    .partners-card {
        width: 25vw !important;
        height: 13vw !important;
    }
    
    
    


    
}


@media (max-width:1024px) {
    .paragraph {
        font-size: 14px;
    }
    .side-paragraph {
        width: 63vw;
        padding-right: 32px;
    }
    
    .banner {
        height: 60vh;
        
    }
    .card {
        width: 27vw;
        height: 19.44vw;
        background-size: 38vw !important;
        margin-top: 20px;
    }
    
    
    .long-card{
        height: 23.22vw;
    }
    .full-card {
        height: 40vw;
        min-height: 250px;
        max-height: 564px;
    }
    .mobile-img{
        height: 40vw;
    }
    .card-header{
        line-height: .5;
    }
    .card-margin {
        margin-right: 4.5vw;
    }
    
    .website-navbar{
        width: 90vw;
        
    }
    .logo-container{
        height: fit-content !important;
    }

    .subtitle {
        font-size: 18px;
    }
    .banner-title {
        font-size: 30px;
    }
    .store-img {
        width: 30vw;
        max-width: 192px;
        height: auto;
    }.font_styles_download{
        font-size: 22px;
        font-weight: bold;
    }
    .font_styles{
        font-size: 18px;
    }
    .track-container{
        width: 100%;
    }
    .container-fluid{
        height: auto;
    }
    .track-order {
        zoom: .8;
    }
    .title-container {
        padding-top: 6vh;
        width: 90vw;
    
    }
    .hide-mob{
        display: none !important;
    }

    .contact_us_banner {
        background:url("/olivery_website_templates/static/src/css/aboutUs.png");
        background-repeat: no-repeat;
        background-size: 130px;

    }
    .about_us_banner{
        background:url("/olivery_website_templates/static/src/css/aboutUs.png");
        background-repeat: no-repeat;
        background-size: 340px;
    }
    .hide-web{
        display: block !important;
    }
    
    .logo-container {
        max-width: 55px;
        height: 55px;
        width: 19vw;
        object-fit: contain;
    }
    .content{
        width: 90vw;
    }
    
    .our-clients .slider-container ul li {
        width: 25vw !important;
        height: 14vw !important;
    }
    
    .our-clients .slider-container ul {
        margin-left: calc(12.5vw + 40px) !important;
    }
    #footer {
        zoom: 70%;
    }
    
    .slide-track {
        animation: scroll-medium 20s linear infinite !important;
        width: calc(30vw * 12) !important;
    }
    
    .partners-card {
        width: 30vw !important;
        height: 15.6vw !important;
    }
    .register-button {
        &:hover {
            background-color: #F3A31A;
        }
    }
    .track-order-button {
        &:hover {
            background-color: #1d6c9a;
        }
    }
    
    
}

@media (max-width:520px) {
    .paragraph {
        font-size: 16px;
        position: relative;
    }
    
    .side-paragraph {
        position: relative;
        width: unset;
    }
    .card {
        width: 90vw;
        height: 64.8vw;
        background-size: 130vw !important;
    }
    .cards-top {
        display: block;
    }
    .long-card{
        height: 77.4vw;
    }
    .about-card{
        width:100% !important;
    }
    .full-card {
        height: 90vw;
    }
    .inner-card-right {
        width: 100%;
    }
    
    .price-cards {
        grid-template-columns: 47% 47%;
    
    }


    .content-left {
        display: block;
        direction: ltr;
    }
    
    .content-right {
        display: block;
    }
    .mobile-img {
        display: none !important;
    }
    .hide-mid {
        display: none;
    }
    .our-clients .slider-container ul {
        margin-left: 99px !important;
    }
    .our-clients .slider-container ul li {
        width: 50vw !important;
        height: 28vw !important;
    }
    .our-clients .slider-container ul {
        margin-left: calc(50vw + 40px) !important;
    }
    #footer{
        zoom: unset !important;
    }
    .inner-footer {
        padding-block: 15px;
        flex-direction: column;
        gap: 15px;
    
    }
    .footer-elem{
        width:unset ;
        height: unset;
    }
    .footer-logo{
        display: none !important;
    }
    .website-navbar{
        max-height: 55px;
    }
    .slide-track {
        animation: scroll-small 20s linear infinite !important;
        width: calc(50vw * 12) !important;
    }
    
    .partners-card {
        width: 50vw !important;
        height: 26vw !important;
    }
    .footer-elem-row{
        width: 100%;
    }
    .links{
        zoom: 70%;
    }
}

.menu {
    background-color: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    padding: 0;
}

.line {
    fill: none;
    stroke: white;
    stroke-width: 5;
    transition: stroke-dasharray 600ms cubic-bezier(0.4, 0, 0.2, 1),
        stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.line1 {
    stroke-dasharray: 60 207;
    stroke-width: 5;
}

.line2 {
    stroke-dasharray: 60 60;
    stroke-width: 5;
}

.line3 {
    stroke-dasharray: 60 207;
    stroke-width: 5;
}

.opened .line1 {
    stroke-dasharray: 90 207;
    stroke-dashoffset: -134;
    stroke-width: 5;
}

.opened .line2 {
    stroke-dasharray: 1 60;
    stroke-dashoffset: -30;
    stroke-width: 5;
}

.opened .line3 {
    stroke-dasharray: 90 207;
    stroke-dashoffset: -134;
    stroke-width: 5;
}


.our-clients{
    direction: rtl;
    width: 100vw;
    overflow: hidden;
    margin: 0 auto;
    padding: 0;

}

.our-clients .slider-container {
    width: 3014.694px;
    height: 20vmax;
    display: flex;
    align-items: center;
    overflow: hidden;
    min-height: 170px;
}

.our-clients .slider-container ul {
    padding: 0 !important;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    height: 100%;
}

.our-clients .slider-container ul li {
    list-style: none;
    width: 334.966;
    height: 9.33vw;
    flex-shrink: 0;
    box-sizing: border-box;
    padding: 0 15px;
}

.our-clients .slider-container ul li .card-small {
    margin-block: 30px;
    text-align: center;
    height: 100%;
    width: 100%;
}
a{
    text-decoration: none !important;
}







.slider{
    direction: ltr;
}
.slide-track {
        animation: scroll 20s linear infinite;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        width: calc(16vw * 12);
        padding: 2vmax 0 5vmax 0;
        direction: rtl;
}
.partners-card{
        width: 16.66vw;
        height: 8.66vw;
        border-color: transparent;
        border-radius: 10px;
        box-shadow: 0 6px 40px 0 rgba(47, 47, 47, 0.1);
        background-position: center !important;
        background-size: 66% !important;
        background-repeat: no-repeat !important;
        display: inline-block;
        overflow: hidden;
        background-color: #fff !important;
        margin-inline: 1vmax;
}
.footer-elem>a{
    height: fit-content;
}
.footer-elem>a>button {
    display: inline;
    text-decoration: none;
    background-image: linear-gradient(transparent, transparent),linear-gradient(transparent, transparent),linear-gradient(to left, #ffffff, #b4b4b4);
    background-repeat: no-repeat;
    background-position: 120%, 122%, 200px 130%;
    background-size: 100% 8px;
    transition: all 300ms;
}

.footer-elem>a>button:hover {
    background-position: 120%, 122%, 15px 130%;
}
.nav-buttons>a>.nav-button{
    display: inline;
    text-decoration: none;
    background-image: linear-gradient(transparent, transparent), linear-gradient(transparent, transparent), linear-gradient(to left, #ffffff, #b4b4b4);
    background-repeat: no-repeat;
    background-position: 120%, 122%, 20px 130%;
    background-size: 60% 3px;
    transition: all 300ms;
}

.nav-buttons>a>.nav-button:hover{
        background-position: 120%,122%,33px 90%;
}
.overlay>a>.nav-button {
    display: inline;
    text-decoration: none;
    background-image: linear-gradient(transparent, transparent), linear-gradient(transparent, transparent), linear-gradient(to left, #ffffff, #b4b4b4);
    background-repeat: no-repeat;
    background-position: 120%, 122%, 20px 130%;
    background-size: 100% 3px;
    transition: all 300ms;
}

.overlay>a>.nav-button:hover {
    background-position: 120%, 122%, 58px 87%;
}


/* Slide in */
li:nth-child(2) a {
    overflow: hidden;
}


@import url(https://use.fontawesome.com/releases/v5.15.4/css/all.css);

$timing        : 265ms;
$iconColor     : #262745;
$accent        : #002A8F;
$bluefade      : #0043E0;
$gradient      : #00B5F5;

@mixin transformScale($size: 1) {
    transform: scale($size);
    -ms-transform: scale($size);
    -webkit-transform: scale($size);
}

.social-container {
    text-align: center;
}

.social-icons {
    padding: 0;
    list-style: none;
    direction: rtl;
  
    li {
        display: inline-block;
        margin-inline: 12px;
        margin-block: 0;
        position: relative;
        font-size: 1.2em;
        zoom: .6;
        transition: all $timing;
    }

    i {
        color: $iconColor;
        position: absolute;
        top: 4.5px;
        right: 7px;
        zoom: 2;
        line-height: 130%;
        transition: all $timing ease-out;
    }

    a {
        display: inline-block;
      
        &:before {
            @include transformScale();
            content: " ";
            width: 60px;
            height: 60px;
            border-radius: 100%;
            display: block;
            background: #fff;
            transition: all $timing ease-out;
        }
        
        &:hover:before {
            transform: scale(0);
            transition: all $timing ease-in;
        }
        
        &:hover i {
            transform : scale(1.9) !important;
            color: #fff;
            background: -webkit-linear-gradient(45deg, #fff, #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            transition: all $timing ease-in;
        }
    }
  
}
.form-control:focus{
    background-color: #fafdff;
    border: 1px solid #202529;
    box-shadow: none;

}
.form-control {
    border: 1px solid #5ec3d7;
}

.form-control-track{
    border: 3px solid #cb431f;
    border-radius: 10px;
    padding:0.375rem 0.75rem 0.2rem 0.75rem;
    height: 2.6rem;
    display: flex;
    align-self: center;
    margin: 0 10px;

}

.table th, table td {
    padding: 0.25rem;

}


/* Common Mouse Scroll Ele */
.scroll-down {
    position: absolute;
    bottom: 50px;
    left: 50%;
    width: 75px;
    height: 80px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transform: translateX(-50%);
    bottom: -70px;
    display: inline-block;
    z-index: 0;
}


.scroll-down svg {
    position: absolute;
    right: 0;
    left: 0;
    margin: 0 auto;
    top: 15px;
    animation: animateLargeArrow 1.25s infinite linear;
}

@keyframes animateLargeArrow {
    0% {
        opacity: 0;
        top: 0;
    }

    25%,
    75% {
        opacity: 1;
    }

    50% {
        top: 15px;
    }

    100% {
        opacity: 0;
        top: 30px;
    }
}


.checkbox-container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: rgb(255, 255, 255);
    border-radius: 100%;
    border: 1px solid black;
}
.container2{
 margin: 0px 30px 0px 37vw;
}
.container3{
    margin: 0px 30px 0px 37vw;
   }
/* On mouse-over, add a grey background color */
.checkbox-container:hover input~.checkmark {
    background-color: #ccc;
    border-radius: 100%;
    border: 1px solid black;
}

/* When the checkbox is checked, add a blue background */
.checkbox-container input:checked~.checkmark {
    border-radius: 100%;
    border: 1px solid black;
    background-color: #5ec3d7;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.checkbox-container input:checked~.checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.checkbox-container .checkmark:after {
    left: 9px;
    top: 5px;
    width: 5px;
    height: 10px;
}

@media (max-width: 500px) {
    .background-track{
        background-color: white;
        min-width: auto !important;
        font-family: 'Cairo';
        margin-top: 10px;
        padding: 35px 13px;
        padding-bottom: 200px;
        border-radius: 50px;
        display: flex;
        justify-content: center;
        width: 95% !important;
    }
}



.language-selector-container button{
    height: fit-content;
    font-size: 5pt;
    padding: 8px;
    width: fit-content;
    display: flow;
}


@media (min-width: 768px) and (max-width: 1024px) {
    .logo {
        object-fit: contain;
        width: 30vw; /* Set width to 30vw for this range */
    }
    .track-banner{
        height: auto;
    }
    .register-button {
        padding: 8px 13px;
    }
    .oe_signup_form {
        max-width: 40%;

    }
    
}

@media (min-width: 768px){

    .track_table_sequence_and_reference_col{
        display:none;
    }
}