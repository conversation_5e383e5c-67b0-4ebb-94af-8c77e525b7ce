<?xml version="1.0"?>
<odoo>
    <data>
        <template id="assets_backend" name="dispatcher_map_assets" inherit_id="web.assets_backend">
            <xpath expr="." position="inside">
                <script type="text/javascript" src="/olivery_dispatcher_map/static/src/js/map_widget.js"></script>
            </xpath>
        </template>



        <record id="olivery_dispatcher_map.action_map_widget" model="ir.actions.client">
            <field name="name">Location Map</field>
            <field name="tag">olivery_dispatcher_map.map_widget</field>
        </record>


        <menuitem id="menu_rb_delivery_location" name="Dispatcher Map" parent="rb_delivery.rb_delivery_top_menu" action="olivery_dispatcher_map.action_map_widget" sequence="17"/>

        <record model="ir.actions.act_window" id="action_config">
            <field name="name">Dispatcher Map Config</field>
            <field name="res_model">rb_delivery.dispatcher_map_config</field>
            <field name="view_mode">tree,form</field>
        </record>
        
        <menuitem id="dispatcher_map_config_menu" name="Dispatcher Map Config" parent="rb_delivery.menu_rb_delivery_extrconf" sequence="15"/>

        <menuitem id="menu_config" name="Dispatcher Map Config" parent="olivery_dispatcher_map.dispatcher_map_config_menu" sequence="15" action="action_config" />

        <act_window id="action_dispatcher_map_dispatcher_filter" name="Dispatcher Filter" res_model="rb_delivery.dispatcher_filter" view_mode="tree,form"/>
        <menuitem id="menu_dispatcher_map_dispatcher_filter" name="Dispatcher Filter" parent="olivery_dispatcher_map.dispatcher_map_config_menu" sequence="22" action="action_dispatcher_map_dispatcher_filter" />


        <act_window id="action_dispatcher_map_dispatcher_order_card" name="Dispatcher Order Card" res_model="rb_delivery.dispatcher_order_card" view_mode="tree,form"/>
        <menuitem id="menu_dispatcher_map_dispatcher_order_card" name="Dispatcher Order Card" parent="olivery_dispatcher_map.dispatcher_map_config_menu" sequence="23" action="action_dispatcher_map_dispatcher_order_card" />


        

        <menuitem id="menu_olivery_collection_route_collections" 
            name="Route Collections" 
            parent="rb_delivery.menu_rb_delivery_collection" 
            sequence="6" 
            groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_driver,rb_delivery.role_manager"/>

        <act_window 
            id="action_olivery_collection_route" 
            name="Active Route collection" 
            res_model="rb_delivery.route_collection" 
            view_mode="tree,form" 
            domain="['|','&amp;',('state','!=','done_route'),('state','!=','deleted'),('state','=',False)]"/>

        <menuitem 
            id="menu_olivery_collection_route" 
            name="Active Route collections" 
            parent="menu_olivery_collection_route_collections" 
            sequence="1" 
            action="action_olivery_collection_route" 
            groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_driver,rb_delivery.role_manager"/>
    
        <act_window id="action_olivery_collection_all_route" 
            name="All Route collections" 
            res_model="rb_delivery.route_collection" 
            view_mode="tree,form"/>

        <menuitem id="menu_olivery_collection_all_route" 
            name="All Route collections" 
            parent="menu_olivery_collection_route_collections" 
            sequence="2" 
            action="action_olivery_collection_all_route" 
            groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_driver,rb_delivery.role_manager"/>

    </data>
</odoo>