# -*- coding: utf-8 -*-
import logging
from openerp import models, api, _, fields
_logger = logging.getLogger(__name__)

from odoo.exceptions import ValidationError

class olivery_dispatcher_map_order_card(models.Model):
    _name = 'rb_delivery.dispatcher_order_card'
    _inherit = "mail.thread"


    order_card_fields = fields.Many2many(
        'ir.model.fields',
        'rb_delivery_order_card_fields_rel',
        'order_card_id',
        'field_id',
        domain="[('model_id.model', '=', 'rb_delivery.order')]",
        string="Order Card Fields",
        track_visibility="on_change"
    )

    order_card_buttons = fields.Many2many(
        'rb_delivery.dispatcher_order_card_button',
        'rb_delivery_order_card_buttons_rel',
        'order_card_id',
        'button_id',
        string="Available Buttons",
        track_visibility="on_change"
    )


    @api.constrains('order_card_buttons')
    def _check_order_card_buttons_limit(self):
        for record in self:
            if len(record.order_card_buttons) > 2:
                raise ValidationError(_("You can select a maximum of 3 fields."))
            
    @api.constrains('order_card_fields')
    def _check_order_card_fields_limit(self):
        for record in self:
            if len(record.order_card_fields) > 3:
                raise ValidationError(_("You can select a maximum of 3 fields."))

    @api.model
    def get_card_fields(self):
        """
        Returns the selected order card fields and buttons.
        """
        order_card = self.search([], limit=1)  # Adjust search criteria if needed
        if not order_card:
            return {"fields": [], "buttons": []}

        fields_list = [{
            'id': field.id,
            'name': field.name,
            'field_description': field.field_description
        } for field in order_card.order_card_fields]

        buttons_list = [{
            'id': button.id,
            'name': button.name,
            'action': button.action
        } for button in order_card.order_card_buttons]

        return {
            "fields": fields_list,
            "buttons": buttons_list
        }