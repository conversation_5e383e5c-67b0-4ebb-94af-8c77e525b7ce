<odoo>
    <data>
        <record id="view_form_dispatcher_order_card" model="ir.ui.view">
            <field name="name">view_form_dispatcher_order_card</field>
            <field name="model">rb_delivery.dispatcher_order_card</field>
      
            <field name="arch" type="xml">
              <form>
      
                <header>
                  <!-- Buttons and status widget -->
                </header>
      
                <sheet>
                  <group name="group_top">
                    <group name="group_left">
                        <field name="order_card_fields" widget="many2many_tags"/>
                        <field name="order_card_buttons" widget="many2many_tags"/>
                        
                    </group>
                  </group>
                </sheet>
                <!-- History and communication: -->
                <div class="oe_chatter">
                  <field name="message_follower_ids" widget="mail_followers"/>
                  <field name="message_ids" widget="mail_thread"/>
                </div>
              </form>
      
            </field>
          </record>
          <record id="view_tree_dispatcher_order_card" model="ir.ui.view">
            <field name="name">view_tree_dispatcher_order_card</field>
            <field name="model">rb_delivery.dispatcher_order_card</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="order_card_fields" />
                    <field name="order_card_buttons"/>

                </tree>
            </field>
        </record>
      
    </data>
</odoo>
