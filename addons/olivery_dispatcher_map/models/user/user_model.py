# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, api,_,fields
from openerp import sql_db as sql_db
from datetime import datetime, timedelta

import base64

_logger = logging.getLogger(__name__)


class olivery_dispatcher_map_user(models.Model):

    _inherit = 'rb_delivery.user'

    def _get_nearest_driver_orders_domain(self):
        status_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')
        return [('state_id','in',status_ids)]
    
    def _get_business_dispatcher_orders_domain(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('default_status_olivery_dispatcher_map')
        return [('state_id','in',status_ids)]

    nearest_driver_orders = fields.One2many('rb_delivery.order',inverse_name='assign_to_agent',domain=_get_nearest_driver_orders_domain,string='Driver Orders Assigned')

    dispatcher_business_orders = fields.One2many('rb_delivery.order',inverse_name='assign_to_business',domain=_get_business_dispatcher_orders_domain,string='Business Orders For Dispatcher Map')

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------                 


    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
        
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------  
    # 
    

    @api.model                    
    def event_stream(self):
        
        with api.Environment.manage():
            new_cr = sql_db.db_connect(self.env.cr.dbname).cursor()
            uid, context = self.env.uid, self.env.context
            self.env = api.Environment(new_cr, uid, context)
            minus_four_secounds=datetime.now()- timedelta(hours=0, minutes=0,seconds=4)
            drivers_to_hide=[]
            fields_to_return=[
                    "longitude",
                    "latitude",
                    "username",
                    "mobile_number",
                    "commercial_number",
                    "area_id",
                    "address",
                    "whatsapp_mobile",
                    "role_code",
                    "online",
                    "nearest_driver_orders",
                    "dispatcher_business_orders",
                    "order_ids",
                    "commercial_name",
                    "vehicle_type"
                  ]
            orders_with_agent_changed = self.env['rb_delivery.order'].search([['write_date','>',str(minus_four_secounds)]])
            changed_users=self.env['rb_delivery.user'].search_read([['write_date','>',str(minus_four_secounds)],'|',['role_code','=','rb_delivery.role_driver'],['role_code','=','rb_delivery.role_business']],fields_to_return)
            if self.env.user.rb_user.role_code == 'rb_delivery.role_business':
                shipment_statuses_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')

                business_orders = self.env['rb_delivery.order'].search([
                    ('assign_to_business', '=', self.env.user.rb_user.id),
                    ('state_id', 'in', shipment_statuses_ids)
                ])
                current_assigned_drivers = business_orders.mapped('assign_to_agent')

                recent_business_orders = orders_with_agent_changed.filtered(
                    lambda o: o.assign_to_business.id == self.env.user.rb_user.id
                )

                all_relevant_drivers = current_assigned_drivers
                if recent_business_orders:
                    recent_agents = recent_business_orders.mapped('assign_to_agent')
                    previous_recent_agents = recent_business_orders.mapped('previous_agent')
                    all_relevant_drivers = (current_assigned_drivers + recent_agents + previous_recent_agents).filtered(lambda x: x)

                if len(all_relevant_drivers):
                    changed_users = changed_users + all_relevant_drivers.read(fields_to_return)

                drivers_to_hide = self.sudo().env['rb_delivery.user'].search([
                    ['id', 'not in', current_assigned_drivers.ids],
                    ['role_code', '=', 'rb_delivery.role_driver']
                ]).ids
            self.env.cr.commit()
            agents = orders_with_agent_changed.mapped('assign_to_agent').read(fields_to_return)
            businesses = orders_with_agent_changed.mapped('assign_to_business').read(fields_to_return)
            previous_agents = orders_with_agent_changed.mapped('previous_agent').read(fields_to_return)
            changed_users+=agents+previous_agents+businesses
        if len(changed_users or drivers_to_hide)>0:
            new_cr.close()
            yield "data:"+json.dumps({'changed_users':changed_users,'drivers_to_hide':drivers_to_hide})+"\n\n"

    def get_session_id(self):
        return self.env['ir.http'].session_info().get('session_id')

    @api.model
    def sudo_search_users_for_business(self, domain, fields):
        current_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)], limit=1)
        if not current_user or current_user.role_code != 'rb_delivery.role_business':
            return self.search_read(domain, fields)

        shipment_statuses_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')
        business_orders = self.env['rb_delivery.order'].search([
            ('assign_to_business', '=', current_user.id),
            ('state_id', 'in', shipment_statuses_ids)
        ])
        assigned_driver_ids = business_orders.mapped('assign_to_agent').ids
        filtered_domain = []

        role_filter = None
        for item in domain:
            if isinstance(item, list) and len(item) == 3 and item[0] == 'role_code':
                if item[1] == '=' and item[2] == 'rb_delivery.role_driver':
                    role_filter = 'driver_only'
                elif item[1] == 'in' and 'rb_delivery.role_driver' in item[2]:
                    role_filter = 'mixed'
                break

        if role_filter == 'driver_only':
            if assigned_driver_ids:
                filtered_domain = [('id', 'in', assigned_driver_ids)] + [item for item in domain if not (isinstance(item, list) and len(item) == 3 and item[0] == 'role_code')]
            else:
                return []
        elif role_filter == 'mixed':
            if assigned_driver_ids:
                filtered_domain = [('id', 'in', assigned_driver_ids)] + [item for item in domain if not (isinstance(item, list) and len(item) == 3 and item[0] == 'role_code')]
            else:
                return []
        else:
            if assigned_driver_ids:
                filtered_domain = [('role_code', '=', 'rb_delivery.role_driver'), ('id', 'in', assigned_driver_ids)] + [item for item in domain if not (isinstance(item, list) and len(item) == 3 and item[0] == 'role_code')]
            else:
                return []

        return self.sudo().search_read(filtered_domain, fields)


    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
