# -*- coding: utf-8 -*-
import json
import logging
from openerp import models, api, _, fields
from datetime import datetime, date,timedelta
_logger = logging.getLogger(__name__)

class olivery_dispatcher_map_filter(models.Model):
    _name = 'rb_delivery.dispatcher_filter'
    _inherit = "mail.thread"

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
    
    @api.model
    def get_default_group(self):
        if 'create_role' in self.env.context and self.env.context['create_role']:
            res = self.env.ref(self.env.context['create_role'])
        else:
            res = self.env.ref('rb_delivery.role_business')
        return res.id or False
    name = fields.Char(string="Filter Name",track_visibility="on_change", required=True, help="Name of the filter")

    filter_domain = fields.Char("Filter Domain",track_visibility="on_change")

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups, default=get_default_group)

    default = fields.Boolean('Default', track_visibility="on_change")

    dispatcher_filter_model_selection = fields.Selection([('order', 'Order Model')], string="Model Selection", required=True, track_visibility="on_change",default='order')

    
    @api.model
    def get_user_filters(self, values):
        if self.env.user._is_admin():
            user_group = self.env.ref("rb_delivery.role_super_manager")
        else:
            user_group = self.env["rb_delivery.user"].search([("user_id", "=", self._uid)], limit=1).group_id

        full_domain = [("group_id.id", "=", user_group.id)]

        if "model_name" in values and values["model_name"]:
            full_domain.append(("dispatcher_filter_model_selection", "=", values["model_name"]))

        filters = self.env["rb_delivery.dispatcher_filter"].sudo().search(full_domain)

        default_filters = {}
        all_filters = {}

        for filter_record in filters:
            # Load the existing filter domain
            fixed_filter_domain = (
                (filter_record.filter_domain or "[]")
                .replace("False", "false")
                .replace("True", "true")
                .replace("None", "null")
            )
            filter_domain = json.loads(fixed_filter_domain)
        
            # Convert date-based filters (e.g., "today", "week") into real date conditions
            updated_filter_domain = self.replace_date_conditions(filter_domain)
            # *** FIX: Replace instead of append ***
            full_domain = updated_filter_domain if updated_filter_domain else filter_domain

            filter_data = json.dumps(full_domain)

            # Distinguish between default and user filters
            if filter_record.default:
                default_filters[filter_record.name] = filter_data
            else:
                all_filters[filter_record.name] = filter_data

        return {
            "default_filters": default_filters,
            "all_filters": all_filters,
        }
    def replace_date_conditions(self, filter_domain):
        """ Replaces date keywords ('today', 'yesterday', etc.) with actual date ranges while preserving domain structure. """
        current_date = datetime.today()

        date_ranges = {
            "today": (
                current_date.strftime("%Y-%m-%d 00:00:00"),
                current_date.strftime("%Y-%m-%d 23:59:59"),
            ),
            "yesterday": (
                (current_date - timedelta(days=1)).strftime("%Y-%m-%d 00:00:00"),
                (current_date - timedelta(days=1)).strftime("%Y-%m-%d 23:59:59"),
            ),
            "week": (
                (current_date - timedelta(days=current_date.weekday())).strftime("%Y-%m-%d 00:00:00"),
                (current_date + timedelta(days=6 - current_date.weekday())).strftime("%Y-%m-%d 23:59:59"),
            ),
            "month": (
                current_date.replace(day=1).strftime("%Y-%m-%d 00:00:00"),
                (current_date.replace(day=1) + timedelta(days=32)).replace(day=1).strftime("%Y-%m-%d 23:59:59"),
            ),
        }

        new_domain = []
        date_conditions = []

        for condition in filter_domain:
            if isinstance(condition, list) and len(condition) == 3:
                field, operator, value = condition
                if operator == "=" and value in date_ranges:
                    start_date, end_date = date_ranges[value]
                    date_conditions.append([field, ">=", start_date])
                    date_conditions.append([field, "<=", end_date])
                else:
                    new_domain.append(condition)
            else:
                new_domain.append(condition)  # Preserve existing "&" or "|"

        if date_conditions:
            new_domain = ["&"] + new_domain + date_conditions 

        return new_domain



    def clean_domain(self, domain):
        """ Ensure the domain structure is valid by removing empty conditions and redundant '&' or '|' """
        cleaned_domain = []
        last_operator = None

        for item in domain:
            if isinstance(item, str) and item in ["&", "|"]:
                if last_operator is None:  # Prevent duplicate logical operators
                    last_operator = item
                    cleaned_domain.append(item)
            elif isinstance(item, list) and len(item) >= 3:
                cleaned_domain.append(item)
                last_operator = None  # Reset last operator after a valid condition

        return cleaned_domain
