<div id="sideMenu" class="side-menu container">
    <div class="flex-space-between">
        <button class="button-select button-dropdown active"
            (click)="
            showGroupSelector = false;
            showActionSelector = false;
            showContentSelector = !showContentSelector
            ">
            <span class="icon">
                <!-- Insert your icon here (using an icon font or image) -->
                <img src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/orders.svg" alt="icon" class="button-icon">
            </span>
            <span>{{ activeContentLabel | translate }}</span>
            <span class="icon" style="zoom: .5;">
                <!-- Insert your icon here (using an icon font or image) -->
                <img src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down-select.svg" alt="icon" class="button-icon">
            </span>
        </button>
        <ul *ngIf="showContentSelector" class="active-content-dropdown">
            <li (click)="showContent('orders');" >{{'ORDERS' |translate}}</li>
            <li (click)="showContent('senders');" *ngIf="userRole!='rb_delivery.role_business'" >{{'BUSINESSES' |translate}}</li>
            <li *ngIf="!['rb_delivery.role_business'].includes(userRole)" (click)="showContent('drivers');" >{{'DRIVERS' |translate}}</li>
            <li *ngIf="['admin','rb_delivery.role_super_manager','rb_delivery.role_manager'].includes(userRole)" (click)="showContent('routes');" >{{'ROUTES' | translate}}</li>
            <li (click)="showContent('all');" >{{'ALL' | translate}}</li>
        </ul>
        <div class="separator"></div>
        <button (click)="toggleQuickOrder()"  class="button-base button-orange" >{{'QUICK_ORDER' |translate}}</button>
        <button 
            *ngIf="activeContent === 'orders'"
            class="button-select button-dropdown active"
            style="margin-inline-start: 15px;"
            [disabled]="checkedOrders.length==0"
            [class.disabled]="checkedOrders.length==0"
            (click)="
            showContentSelector=false;
            showGroupSelector = false;
            showActionSelector = !showActionSelector;
            ">
            <span style="position: absolute ; top: -8px;font-weight: bold; font-size: 12px; align-self: center; color: white; background: #D97348; border-radius: 10px; padding: 2px 5px;" *ngIf="checkedOrders.length>0">{{checkedOrders.length}} {{'SELECTED' | translate}}</span>
            <span>{{ 'ACTION' | translate }}</span> 
            <span class="icon" style="zoom: .5;">
                <!-- Insert your icon here (using an icon font or image) -->
                <img src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down-select.svg" alt="icon" class="button-icon">
            </span>
        </button>
        <ul *ngIf="showActionSelector" class="active-action-dropdown">
            <li (click)="showAssignAgent()" >{{'ASSIGN_TO_AGENT' |translate}}</li>
        </ul>
        <app-driver-list 
            (onClickDriver)="assignAgentAction($event)" 
            (onClickShowOnMap)="showAgentOnMap($event.agent)"
            (onClose)="closeDriverList()"
            [driversToSelect]="driversToSelect"
            [currentOrder]="{'assign_to_agent':[]}"
            [hideStatusSelector]="true"
            [loading]="false"
            *ngIf="showAgentList && showActionSelector && checkedOrders.length>0 && !currentOrder" 
            class="app-driver-container" 
            
            [ngStyle]="{
                'top': '80px',
                'position': 'fixed',
                'left': '534px',
                'z-index': '1000',
            }"
            
        ></app-driver-list>
        <button
            *ngIf="activeContent === 'orders'"
            class="button-select button-dropdown active"
            style="margin-inline-start: 15px; min-width: fit-content !important;"
            (click)="
            showActionSelector=false;
            showContentSelector=false;
            showGroupSelector = !showGroupSelector;
            ">
            <span class="icon" style="margin-inline: 14px !important;">
                <!-- Insert your icon here (using an icon font or image) -->
                <img src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/group-by.svg" alt="icon" class="button-icon">
            </span>
        </button>
        <ul *ngIf="showGroupSelector" class="active-group-dropdown">
            <li (click)="toggleGroupOption(groupOption);" 
                *ngFor="let groupOption of groupByFields">
                <span *ngIf="activeGroupByFields.includes(groupOption)">✔</span>
                {{groupOption[2]}}
                <span *ngIf="activeGroupByFields.includes(groupOption)" class="tag-right">{{activeGroupByFields.indexOf(groupOption) + 1}}</span>
            </li>
        </ul>
    </div>


    <div class="dropdown-container"  *ngIf="activeContent != 'all'">
        <div class="input-wrapper">
          <input [(ngModel)]="searchValue" (input)="search($any($event))" class="dropdown-btn input-with-filter"  type="text"  placeholder="{{'SEARCH'|translate}}" style="border: 1px solid #E3E3E3;"/>
          <button *ngIf="activeContent === 'orders'" class="filter-btn-inside-input"(click)="showFilterSelector = !showFilterSelector;">
            <img src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/filter.svg" 
                 alt="filter" 
                 class="button-icon">
          </button>
          <ng-container *ngIf="showFilterSelector">
            <ul class="active-filter-dropdown">
              <li (click)="toggleFilterOption(filter)" 
                  *ngFor="let filter of filterTypes">
                <span *ngIf="activeFilters.includes(filter)">✔</span>
                {{filter}}
                <span *ngIf="activeFilters.includes(filter)" class="tag-right">{{activeFilters.indexOf(filter) + 1}}</span>
              </li>
            </ul>


          </ng-container>
        </div>
        <div *ngIf="activeContent === 'drivers'" class="flex-space-between" style="margin-inline: 10px;">
            <div style="color:red; padding:5px; border-radius: 5px; cursor: pointer;" [ngStyle]="{'border':selectedDriverFilter=='OFFLINE'?'1px solid red':'unset'}" (click)="selectedDriverFilter=='OFFLINE'?filterDrivers('ALL'):filterDrivers('OFFLINE')">{{'OFFLINE' | translate}}({{offlineDriversLength}})</div>
            <div style="color:green; padding:5px; border-radius: 5px; cursor: pointer;" [ngStyle]="{'border':selectedDriverFilter=='ONLINE'?'1px solid green':'unset'}" (click)="selectedDriverFilter=='ONLINE'?filterDrivers('ALL'):filterDrivers('ONLINE')">{{'ONLINE' | translate}}({{onlineDriversLength}})</div>
            <div style="color:orange; padding:5px; border-radius: 5px; cursor: pointer;" [ngStyle]="{'border':selectedDriverFilter=='BUSY'?'1px solid orange':'unset'}" (click)="selectedDriverFilter=='BUSY'?filterDrivers('ALL'):filterDrivers('BUSY')">{{'BUSY' | translate}}({{busyDriversLength}})</div>
        </div>
        <div *ngIf="activeContent === 'routes'" class="flex-space-between" style="margin-inline: 10px;">
            <div style="color:green; padding:5px; border-radius: 5px; cursor: pointer;" [ngStyle]="{'border':selectedRouteFilter=='DONE'?'1px solid green':'unset'}" (click)="selectedRouteFilter='DONE'">{{'DONE_ROUTES' | translate}}({{doneRoutes.length}})</div>
            <div style="color:orange; padding:5px; border-radius: 5px; cursor: pointer;" [ngStyle]="{'border':selectedRouteFilter=='ACTIVE'?'1px solid orange':'unset'}" (click)="selectedRouteFilter='ACTIVE'">{{'ACTIVE_ROUTES' | translate}}({{routes.length}})</div>
        </div>
      </div>


    
    <div *ngIf="activeContent === 'drivers'" style="padding: 15px; background: #F3E8E2;overflow: scroll; height: calc(100vh - 156px)">
        
        <ng-container *ngFor="let user of users;let i = index" >
            <span *ngIf="i == 0 && updateDriversLength() && filterDrivers(selectedDriverFilter)"></span>
            <span *ngIf="user.role_code === 'rb_delivery.role_driver' "></span>
        </ng-container>
            <div *ngFor="let driver of drivers;" class="list-item">
                <div  class="user-container">
                <img [src]="getImageUrl(driver.id!)" alt="{{driver.commercial_name}}"/>
                <span style="width:45vw;">{{driver.commercial_name}}</span>
                <span style="width:45vw;">{{getNumberOfOrders(driver,'agent')}}</span>
                <ng-container *ngIf="userRole!='rb_delivery.role_business' ||(showDriverStatusInSideMenuForBusiness && userRole=='rb_delivery.role_business' )" >
                    <div [class]="driver.online && getNumberOfOrders(driver,'agent')>=maxNumberOfShipments? 'busy-btn' : driver.online ? 'online-btn' : 'offline-btn'">
                        {{driver.online && getNumberOfOrders(driver,'agent')>=maxNumberOfShipments? ('BUSY' | translate) : (driver.online ? 'ONLINE' : 'OFFLINE') |translate}}
                    </div>
                </ng-container>
                </div>
                <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px;">
                    <button (click)="showAgentOnMap(driver,true)" class="select-btn" [ngStyle]="{'background':activeAgent && activeAgent.id == driver.id?'red':'#3ab600'}">{{(activeAgent && activeAgent.id == driver.id?'DISMISS':'SHOW_ON_MAP') | translate}}</button>
                    <button (click)="editUser(driver)" class="select-btn" >{{('EDIT_USER') | translate}}</button>
                </div>
            </div>
    </div>
    <div *ngIf="activeContent === 'routes'" style="padding: 15px; background: #F3E8E2;overflow: scroll; height: calc(100vh - 156px)">
        
        <div *ngFor="let route of (selectedRouteFilter=='ACTIVE'? routes : doneRoutes);" class="list-item">
            <div  class="user-container">
            <span style="width:45vw;">{{getLocalDateTime(route.create_date)}}</span>
            <span style="width:45vw;">{{' /'+ getNumberOfOrders(route,'route')}} {{'ORDERS' | translate}}</span>
            <span style="width:45vw;">{{route.driver_id[1]}}</span>
            </div>
            <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px;">
                <button (click)="showRouteOnMap(route,true)" class="select-btn" [ngStyle]="{'background':activeRoute && activeRoute.id == route.id?'red':'#3ab600'}">{{(activeRoute && activeRoute.id == route.id?'DISMISS':'SHOW_ON_MAP') | translate}}</button>
            </div>
        </div>

    </div>
    <div *ngIf="activeContent === 'senders'" style="padding: 15px; background: #F3E8E2;overflow: scroll; height: calc(100vh - 156px)">
        <ng-container>
            <ng-container *ngFor="let user of users" >
                <div *ngIf="user.role_code === 'rb_delivery.role_business'" class="list-item">
                    <div class="user-container">
                    <img [src]="getImageUrl(user.id!)" alt="{{user.commercial_name}}"/>
                    <span style="width:45vw;">{{user.commercial_name}}</span>
                    <span style="width:45vw;">{{getNumberOfOrders(user,'business')}}</span>

                    </div>
                    <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px;">
                        <button (click)="showSenderOnMap(user,true)" class="select-btn" [ngStyle]="{'background':activeSender && activeSender.id == user.id?'red':'#3ab600'}">{{(activeSender && activeSender.id == user.id?'DISMISS':'SHOW_ON_MAP') | translate}}</button>
                        <button *ngIf="(!activeSender || activeSender.id != user.id) && user.longitude && user.latitude && userRole!='rb_delivery.role_business'" (click)="showDriversInRange(user);showSenderOnMap(user)" class="select-btn" >{{'SHOW_DRIVERS_IN_RANGE' | translate}}</button>
                        <button (click)="editUser(user)" class="select-btn" >{{('EDIT_USER') | translate}}</button>
                    </div>
                </div>
            </ng-container>
        </ng-container>
    </div>
    <div *ngIf="activeContent === 'all'" style="padding: 15px; background: transparent;overflow: scroll; height: calc(100vh - 156px);display: flex; flex-direction: column; gap: 10px;align-items: center;justify-content: center;">
        <div>{{'PLEASE_CHOOSE_A_CATOGARY_TO_SEE_FILTERS_HERE' | translate}}</div>
    </div>
    
    <app-group-container 
        *ngIf="activeContent === 'orders' && orders.length>0"
        [groupSet]="groupingRecordsSet[0]" 
        [level]="0" 
        [parentOrders]="orders"
        [groupingRecordsSet]="groupingRecordsSet"
        [aciveOrder]="aciveOrder"
        [userRole]="userRole"
        [nearestDriverTimeOut]="nearestDriverTimeOut"
        [checkedOrderIds]="checkedOrders"
        (showStatusList)="showStatusList($event)"
        (showDriverList)="showDriverList($event)"
        (showOrderOnMap)="showOrderOnMap($event, true)"
        (showDriversInRange)="showDriversInRange($event)"
        (selectedGroupOrders)="handleSelectedGroupOrders($event)">
    </app-group-container>
    
    
    <div style="text-align: center;" *ngIf="loading">
        {{'LOADING' | translate}}
    </div>
</div>
