import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, NgZone, OnChanges, OnInit, Renderer2, SimpleChanges, ViewChild ,ChangeDetectorRef } from '@angular/core';
import { GoogleMap } from '@angular/google-maps';
import { catchError, map, throwError } from 'rxjs';
import { TranslateService } from "@ngx-translate/core";
import { BehaviorSubject } from 'rxjs';

const USER_FIELDS=[
  "longitude",
  "latitude",
  "username",
  "mobile_number",
  "commercial_number",
  "area_id",
  "address",
  "whatsapp_mobile",
  "role_code",
  "online",
  "nearest_driver_orders",
  "dispatcher_business_orders",
  "order_ids",
  "commercial_name",
  "vehicle_type",
]

var ORDER_FIELDS=[
  "create_date",
  "longitude",
  "latitude",
  "state",
  "state_id",
  "assign_to_business",
  "business_mobile_number",
  "business_whatsapp_mobile",
  "assign_to_agent",
  "picked_up_eta_message",
  "delivered_eta_message",
  "eta_for_delivered",
  "eta_for_picked_up",
  "tracking_url",
  "customer_name",
  "customer_mobile",
  "customer_area",
  "customer_sub_area",
  "cus_whatsapp_mobile",
  "agent_commercial_number",
  "agent_mobile_number",
  "agent_whatsapp_mobile",
  "status_last_updated_on",
]
const ROUTE_FIELDS=[
  "create_date",
  "write_date",
  "create_uid",
  "driver_id",
  "locations",
  "direction",
  "order_ids",
  "done_locations",
  'status'
  
]

const USER_ROLES=[
  "rb_delivery.role_driver",
  "rb_delivery.role_business",
  
]
const AREA_FIELDS=[
  "id",
  "name",
  "is_default"
]
const SUB_AREA_FIELDS=[
  "id",
  "name",
  "parent_id"
]
const STATUS_FIELDS=[
  "id",
  "name",
  "title",
  "next_state_ids",
  "role_action_status_ids"
]

var orderStatuses:number[]=[]
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit{

  @ViewChild(GoogleMap) map!: GoogleMap;
  allOrders:any[]=[]
  routes:any[]=[]
  sessionId:string=""
  urlParams!:URLSearchParams
  googleApiKey:string=""
  iconSize!:google.maps.Size
  mapOptions!:google.maps.MapOptions
  googleApiLoaded!: boolean;
  usersMarkers:{userId:number,isDriver:boolean,marker:google.maps.Marker}[]=[]
  ordersMarkers:{orderId:number,marker:google.maps.Marker}[]=[]
  driverLocationordersMarkers:{driverLocationId:number,marker:google.maps.Marker,orderId:number}[]=[]
  usersEventSource!:EventSource
  ordersEventSource!:EventSource
  routesEventSource!:EventSource
  driverordersLocationEventSource!:EventSource
  motorcycleIcon: any;
  carIcon: any;
  businessIcon: any;
  customerIcon: any;
  infowindow!: google.maps.InfoWindow;
  overdueOrder!: any;
  pickupOverdueOrders!: any[];
  deliveryOverdueOrders!: any[];
  filteredUsers: any[]=[];
  allAreas: any[]=[];
  allSubAreas: any[]=[];
  groupedOrders: any={};
  filteredOnAgent: any;
  filteredOnRoute: any;
  filteredOnSender: any;
  showDriversList: boolean=false;
  currentOrder: any;
  loading!:boolean
  activeContent="orders"
  filteredOnOrder: any;
  currentUsersDomain:any[]=["online","=",true]
  context: any;
  showQuickOrder: boolean = false;
  isToastVisible: boolean = false;
  toastMessage!: string | undefined;
  toastType!: string | undefined;
  toastTitle: string | undefined;
  allStatuses: any[]=[];
  driversRange: number = 5000;
  filteredOnRange!:google.maps.LatLng | undefined;
  geofence!: google.maps.Circle;
  fitGeofenceToBounds:boolean=true
  filterTypes!: string[];  
  allUsers: any[]=[];
  timeDurationConfig:number=0
  maxNumberOfShipments: number=0;
  isChooseStatusActive!: boolean;
  fetchRecordErrorMessage: any ='';
  isFilteredOnOrders: boolean=false;
  userRole!:string
  assignToBusinessMap: any[]=[];
  filterTexts!: string[];
  counter=0
  groupId: any;
  currentUserId!: number;
  nearestDriverStatuses:any[]=[];
  isUsersFetched!: boolean;
  deliveryCost = 0
  orderTypeId: any;
  movingRecordIds: string[]=[];
  currentOrderDomain: any[]=[];
  orderFilterDomain: any[]=[];
  orderSearchDomain: any[] =[];
  visibleStatuses: any[]=[];
  showCustomerDetails = false;
  showPaymentSection=false;
  showNotesSection=false;
  showExtraCost=false;
  directionsRenderer!: google.maps.DirectionsRenderer;
  doneOrderMarkers: google.maps.Marker[]=[];
  routeMarkers: google.maps.Marker[]=[];
  doneRoutes: any[]=[];
  MatchDriverColorToLastOrderStatus=false;
  groupByFields!: any[];
  checkedOrderIds: number[]=[];
  dispatcherOrderFilters: { [key: string]: any } = {};
  orderDefaultFilters: { [key: string]: any } = {};
  orderCardFields:any[]=[];
  orderCardButtons:any[]=[];
  showDriverList=false
  driversToSelect$ = new BehaviorSubject<any[]>([]);
  showDriverStatusInSideMenuForBusiness = false


  

  constructor(
    private renderer: Renderer2,
    private http: HttpClient,
    private translate: TranslateService,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef
  ){
    this.translate.setDefaultLang('en');
    
    this.urlParams = new URLSearchParams(window.location.search)
    this.context = JSON.parse((this.urlParams.get('context') as string))
    if('tz' in this.context){
      delete this.context['tz']
    }
    this.translate.use((this.context['lang'] as string).split('_')[0]);
    if(this.translate.currentLang=='ar' || this.translate.currentLang=='he'){
      document.documentElement.dir = 'rtl';
    }
    this.sessionId = this.urlParams.get('session') as string
    this.googleApiKey = this.urlParams.get('google_api_key') as string
    this.mapOptions={
      gestureHandling:'greedy',
      fullscreenControl:false,
      mapTypeControl:false,
      streetViewControl:false,
      center:{lat:31.971568170785242,lng:35.12121661838731}
    }
    this.addGoogleMapsScript()
    
  }
  async fetchAreas(){
      if (this.allAreas.length<1){
        this.allAreas=await this.fetchRecords("rb_delivery.area", [[],AREA_FIELDS],'search_read');
      }
      if (this.allSubAreas.length<1){
        this.allSubAreas=await this.fetchRecords("rb_delivery.sub_area", [[],SUB_AREA_FIELDS],'search_read');
      }
  }
  async fetchAllUsers() {
    if(this.userRole === 'rb_delivery.role_business') {
      this.allUsers = await this.fetchRecords("rb_delivery.user", [[["role_code", "in", USER_ROLES]],USER_FIELDS],'sudo_search_users_for_business');
    } else {
      this.allUsers = await this.fetchRecords("rb_delivery.user", [[["role_code", "in", USER_ROLES]],USER_FIELDS],'search_read');
    }
    this.fetchOrders(true)
  }

  async fetchUsersFiltered(domain:any[],addOnMap?:boolean) {
    this.filteredUsers = []

    if(this.userRole === 'rb_delivery.role_business') {
      this.filteredUsers = await this.fetchRecords("rb_delivery.user", [domain,USER_FIELDS],'sudo_search_users_for_business');
    } else {
      this.filteredUsers = await this.fetchRecords("rb_delivery.user", [domain,USER_FIELDS],'search_read');
    }

    this.isUsersFetched=true
    if(addOnMap){
      await this.addUsersOnMap(this.filteredUsers)
    }
  }
  async fetchUserOrderFilters() {
    if (this.activeContent === 'orders') {
      let allOrderFilters = await this.fetchRecords('rb_delivery.dispatcher_filter', [{model_name:"order"}], 'get_user_filters');
      
      // Extract the filters as objects
      let defaultFilters = allOrderFilters.default_filters ? allOrderFilters.default_filters : {};
      let ordersFilters = allOrderFilters.all_filters ? allOrderFilters.all_filters : {};
  
      // Merge the objects into dispatcherOrderFilters
      this.dispatcherOrderFilters = { ...defaultFilters, ...ordersFilters };
  
      // Set orderDefaultFilters to only ordersFilters
      this.orderDefaultFilters = { ...defaultFilters };
      if (Object.keys(this.orderDefaultFilters).length > 0) {
       this.updateDomain(Object.keys(this.orderDefaultFilters));
      }
    }
  }
    
  async fetchOrders(addOnMap?:boolean,setLoading=true) {
    let domain = this.getOrderDomain()
    this.allOrders = await this.fetchRecords("rb_delivery.order", [domain,ORDER_FIELDS],'search_read',setLoading);
    if(addOnMap){
      this.removeOrdersFromMap()
      this.addOrdersOnMap(this.allOrders)      
    }
  }

  removeOrdersFromMap() {
    this.usersMarkers.forEach(marker => {
      marker.marker.setMap(null)
    });
    this.ordersMarkers.forEach(marker=>{
      marker.marker.setMap(null)
    })
    if(this.ordersMarkers.length>0){
      const bounds = this.getBounds(this.ordersMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
    }
  }

  async fetchRoutes(setLoading=true) {
    let allRoutes = await this.fetchRecords("rb_delivery.routes", [[],ROUTE_FIELDS],'search_read',setLoading);
    this.routes = allRoutes.filter((route:any)=>route.status=='in_progress')
    this.doneRoutes = allRoutes.filter((route:any)=>route.status=='done')
  }


  async syncNumberOfOrders(user:any,numberOfOrders:number){
    let existMarker = this.usersMarkers.filter(marker=>marker.userId==user.id)
      await this.getUserMarker(user).then(async userMarker=>{
        if(existMarker[0]){
          existMarker[0].marker.setIcon({
            url:'data:image/svg+xml;base64,' + userMarker,
            scaledSize:new google.maps.Size(64,64),
            origin: new google.maps.Point(0, 0), // origin
            anchor: new google.maps.Point(32, 64), // anchor
          })
          
          existMarker[0].marker.setLabel({
            text:"   "+numberOfOrders,
            color:numberOfOrders>=this.maxNumberOfShipments&&user.role_code=='rb_delivery.role_driver'?"orange":"green",
            fontSize:"20px",
            fontWeight:"bolder"
          })
        }
        
      })
  }



async fetchRecords(model_name: string,args:any[],method:string,setLoading=false): Promise<any> {
  
  if(setLoading){
    this.loading = true
  }
  
    try {
        
        if (!this.sessionId) {
            throw new Error('Session ID is missing.');
        }

        let headers = new HttpHeaders({
            'Content-Type': 'application/json; charset=utf-8'
        }).set('X-Openerp-Session-Id', this.sessionId);
        
        const payload = {
            "jsonrpc": "2.0",
            "method": "call",
            "params": {
                "context": this.context,
                "model": model_name,
                "method": method,
                "args": args,
                "kwargs": {context:this.context}
            }
        };
        
        return this.http.post(window.location.origin+'/web/dataset/call_kw', payload, { headers: headers })
        .pipe(map((data: any) => {
          this.loading=false
          if(data && data.result || typeof data.result == 'number' || typeof data.result == 'boolean'){
            return data.result;
          }else{
            this.showToast(data.error.message as string,'fail',data.error.data.message as string)
            this.fetchRecordErrorMessage=data.error.data.message
            return false
          }
        }))
        .pipe(catchError((error) => {
            return throwError(error);
        })).toPromise();

    } catch (error) {
      this.showToast(model_name,'fail',error as string)
        console.error(`Error while fetching records for model ${model_name}:`, error);
        throw error;
    }
  
}
  onOrdersChangeLitener() {
    this.ordersEventSource = new EventSource('https://'+window.location.hostname+"/orders_stream/"+JSON.stringify({...this.context,...{'fields':ORDER_FIELDS}}),{withCredentials:true});
    this.ordersEventSource.onmessage = (event)=>{
      let changedOrders = JSON.parse(event.data)
      this.ngZone.run(async () => {
        for (let order of changedOrders) {
          
          this.updateOrderLocationOnMap(order);
          await this.addOrUpdateOrder(order);
          this.checkOverDueOrder(order);
          
        }
        this.allOrders = [...this.allOrders]
      });
    }
  }

  addOrUpdateOrder(changedOrder: any) {
    let existOrder = this.allOrders.filter(order=>changedOrder.id == order.id)
    let isToAddOrder = existOrder.length==0
    let isToUpdateOrder = existOrder.length>0
    let areOrdersEqual = existOrder && this.areObjectsEqual(this.allOrders[this.allOrders.indexOf(existOrder[0])],changedOrder,'notification_timer')
    if(isToAddOrder){
      this.allOrders.push(changedOrder)
      this.showToast(
        this.translate.instant('SEQUENCE')+
        ":"+changedOrder.sequence+"\n"+
        this.translate.instant("STATUS")+
        ":"+changedOrder.state_id[1],
        "success",
        this.translate.instant("NEW_ORDER_ADDED")
      )
    }
    else if(isToUpdateOrder && !areOrdersEqual){

      
      this.allOrders[this.allOrders.indexOf(existOrder[0])]=changedOrder
      
      
      if((!this.currentOrder || this.currentOrder.id != changedOrder.id) &&  existOrder[0].state_id[0] != changedOrder.state_id[0]){
        this.showToast(
          this.translate.instant('ORDER_OF_SEQUENCE')+
          ": "+changedOrder.sequence+"\n "+
          this.translate.instant("HAS_BEEN_MOVED_TO_STATUS")+
          ": "+changedOrder.state_id[1],
          "success",
          this.translate.instant("ORDER_CHANGED_STATUS")
        ) 
      }
      if(this.currentOrder && this.currentOrder.id == changedOrder.id){
        this.currentOrder=false
      }
    }
    this.loading=false
  }

  areObjectsEqual(obj1: any, obj2: any,ignore?:string): boolean {
    // Check if both parameters are objects
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return false;
    }
  
    // Get the keys of the objects
    let obj1Keys = Object.keys(obj1);
    let obj2Keys = Object.keys(obj2);

    if(ignore){
      obj1Keys = obj1Keys.filter(key=>key!=ignore || (key==ignore && !obj1[key]))
      obj2Keys = obj2Keys.filter(key=>key!=ignore || (key==ignore && !obj2[key]))

    }
  
    // Check if number of keys are the same
    if (obj1Keys.length !== obj2Keys.length) {
      return false;
    }
  
    // Iterate through the keys and values of obj1
    for (const key of obj1Keys) {
      // Check if the key exists in obj2
      if (!obj2.hasOwnProperty(key)) {
        return false;
      }
  
      // Recursively compare nested objects or compare values
      if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
        if (!this.areObjectsEqual(obj1[key], obj2[key])) {
          return false;
        }
      } else {
        if (obj1[key] !== obj2[key]) {
          return false;
        }
      }
    }
  
    // If all checks pass, the objects are equal
    return true;
  }
  

  addOrUpdateUser(changedUser: any) {
    if(this.userRole === 'rb_delivery.role_business' &&
       changedUser.role_code === 'rb_delivery.role_driver' &&
       !this.isDriverVisibleToBusinessUser(changedUser.id)) {
      this.filteredUsers = this.filteredUsers.filter(user => user.id !== changedUser.id)
      return
    }

    let existUser = this.filteredUsers.filter(user=>changedUser.id == user.id)
    let isToAddUser = existUser.length==0
    let isToUpdateUser = existUser.length>0
    if(isToAddUser){
      this.filteredUsers.push(changedUser)
    }
    else if(isToUpdateUser){
      this.filteredUsers[this.filteredUsers.indexOf(existUser[0])]=changedUser
    }
  }

  checkOverDueOrder(orderToCheck: any) {
    if(orderToCheck.state == 'pickup_overdue'){
      let existOrder=[]
      if(this.pickupOverdueOrders){
        existOrder=this.pickupOverdueOrders.filter(order=>order.id==orderToCheck.id)
      }
      if(existOrder?.length>0){
        return
      }else{
        this.pickupOverdueOrders.push(orderToCheck)
      }
      
    }

    else if(orderToCheck.state == 'delivery_overdue'){
      let existOrder=undefined
      if(this.deliveryOverdueOrders){
        existOrder=this.deliveryOverdueOrders.filter(order=>order.id==orderToCheck.id)
      }
      if(existOrder){
        return
      }else{
        this.deliveryOverdueOrders.push(orderToCheck)
      }
    }
  }

  onUsersChangeListener() {
    if(this.usersEventSource){
      this.usersEventSource.close()
    }
    this.usersEventSource = new EventSource('https://'+window.location.hostname+"/users_stream/"+JSON.stringify(this.context),{withCredentials:true});
    this.usersEventSource.onmessage = (event)=>{
      let changed_users = JSON.parse(event.data)['changed_users']
      let drivers_to_hide = JSON.parse(event.data)['drivers_to_hide']
      this.ngZone.run(async () => {
        let usersMarkers = this.usersMarkers.filter(marker=>drivers_to_hide.includes(marker.userId))
        for(let marker of usersMarkers){
          marker.marker.setMap(null)
        }
        this.usersMarkers = this.usersMarkers.filter(marker=>!drivers_to_hide.includes(marker.userId))
        for(let user of changed_users){
          await this.updateUserLocationOnMap(user)
          await this.addOrUpdateUser(user)
        }
        
      });
    }
  }

  async changeActiveContent(activeContent:string,domain:any[]=[],filterTexts=['']){
    this.resetMapMarkers()
    this.activeContent=activeContent
    
    if(this.activeContent=='orders'){
      if(domain.length>0){
        this.isFilteredOnOrders=true
        this.filterTexts=filterTexts
        this.orderFilterDomain=domain
      }else{
        this.orderFilterDomain=[]
        this.isFilteredOnOrders=false
      }
      await this.fetchUserOrderFilters()
      await this.fetchOrders(true)
      if(this.allOrders.length>0)
        this.fitBoundsOnOrders()
    }
    else if(this.activeContent=='drivers'){
      this.currentOrder=undefined
      await this.updateDomain(['ALL'])
      if(this.allUsers.filter(user=>user.role_code=='rb_delivery.role_driver').length>0)
        this.fitBoundsOnDrivers()
    }
    else if(this.activeContent=='senders'){
      this.currentOrder=undefined
      await this.updateDomain(['ALL'])

      if(this.allUsers.filter(user=>user.role_code=='rb_delivery.role_business').length>0)
        this.fitBoundsOnSenders()
    } else if(this.activeContent=='all'){
      this.currentOrder=undefined
      await this.updateDomain(['ALL'])
      await this.fetchUserOrderFilters()
      await this.fetchOrders(true)
      if(this.allOrders.length>0)
        this.fitBoundsOnOrders()
      this.usersMarkers.forEach(marker => {
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
      });
    }
   
  }

  fitBoundsOnSenders() {
    let sendersMarkers:any[] = this.usersMarkers.filter(marker=>!marker.isDriver)
    this.usersMarkers.forEach(marker => {
      marker.marker.setMap(null)
    });
    this.ordersMarkers.forEach(marker=>{
        marker.marker.setMap(null)
    })
    sendersMarkers.forEach(marker => {
      marker.marker.setMap(this.map.googleMap as google.maps.Map)
    });
    if(sendersMarkers.length>0){
      const bounds = this.getBounds(sendersMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
    }
  }

  fitBoundsOnDrivers() {
    let driversMarkers:any[] = this.usersMarkers.filter(marker=>marker.isDriver)
    this.usersMarkers.forEach(marker => {
      marker.marker.setMap(null)
    });
    this.ordersMarkers.forEach(marker=>{
        marker.marker.setMap(null)
    })
    driversMarkers.forEach(marker => {
      marker.marker.setMap(this.map.googleMap as google.maps.Map)
    });
    if(driversMarkers.length>0){
      const bounds = this.getBounds(driversMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
    }
  }

  fitBoundsOnOrders() {
    this.usersMarkers.forEach(marker => {
      marker.marker.setMap(null)
    });
    this.ordersMarkers.forEach(marker=>{
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
    })
    if(this.ordersMarkers.length>0){
      const bounds = this.getBounds(this.ordersMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
    }
  }

  async search(searchValue:string,domain:any[]=[]){
    if(this.activeContent=='orders'){
      if(searchValue && searchValue != ""){
        this.orderSearchDomain=[
          
          '|',
          '|',
          '|',
          '|',
          '|',
          ['sequence','ilike',searchValue],
          ['assign_to_agent','ilike',searchValue],
          ['state','ilike',searchValue],
          ['customer_mobile','ilike',searchValue],
          ['assign_to_business','ilike',searchValue],
          ['customer_name','ilike',searchValue]
        ]
        
      }else{
        this.orderSearchDomain=[]
      }
      await this.fetchOrders(true)
      
    }
    else if(this.activeContent=='drivers'||this.activeContent=='senders'){
      if(searchValue && searchValue != ""){
        domain=domain.concat([
          ["role_code", "in", USER_ROLES],
          '|',
          '|',
          ['username','ilike',searchValue],
          ['mobile_number','ilike',searchValue],
          ['commercial_name','ilike',searchValue]
        ])
        if(this.currentUsersDomain.length>0){
          domain.push(this.currentUsersDomain)
        }
        this.fetchUsersFiltered(domain)
      }
      else{
        domain=domain.concat([["role_code", "in", USER_ROLES]])
        if(this.currentUsersDomain.length>0){
          domain.push(this.currentUsersDomain)
        }
        this.fetchUsersFiltered(domain)
      }
    }
    
  }
  getOrderDomain() {
    return [['state_id','in',orderStatuses]].concat(this.orderFilterDomain).concat(this.orderSearchDomain)
  }

  ngOnInit(){

    this.fetchAreas()
    this.initMapMarkerIcons()

  }

  async initMapMarkerIcons() {
    this.motorcycleIcon = await this.convertImageToBase64('/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/driver.png')
    this.carIcon = await this.convertImageToBase64('/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/car.png')

    this.businessIcon = await this.convertImageToBase64('/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/business.png')
    this.customerIcon = await this.convertImageToBase64('/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/customer.png')
  }

  async updateUserLocationOnMap(user:any) {
    let existMarker = this.usersMarkers.filter(marker=>marker.userId==user.id)
    if(existMarker.length>0){
      this.moveMarker(existMarker[0].marker,user,'U').then(()=>{
        this.addUserMarkerOnMap(user)
      })
      return 
    }else{
      
      return this.addUserMarkerOnMap(user)
    }
  }

  updateOrderLocationOnMap(order:any) {
    let existMarker = this.ordersMarkers.filter(marker=>marker.orderId==order.id)
    this.driverLocationordersMarkers.forEach(marker=>{
      if(order.id == marker.orderId && !this.nearestDriverStatuses.includes(order.state_id[0])){
        marker.marker.setMap(null)
      }
    })
    if(this.nearestDriverStatuses.includes(order.state_id[0])){
      if(existMarker.length>0){
        this.getOrderMarker(order).then(orderMarker=>{
          existMarker[0].marker.setIcon({
            url:'data:image/svg+xml;base64,' + orderMarker,
            scaledSize:new google.maps.Size(64,64),
            origin: new google.maps.Point(0, 0), // origin
            anchor: new google.maps.Point(32, 64), // anchor
          })
        })
        this.moveMarker(existMarker[0].marker,order,'O')
        this.updateOrderMarkerClickListener(existMarker[0].marker,order)
      }else{
        this.addOrderMarkerOnMap(order)
      }
    }
    else{
      if(existMarker.length>0){
        let existMarkerIndex = this.ordersMarkers.indexOf(existMarker[0])
        this.ordersMarkers.forEach(marker=>{
          if(order.id == marker.orderId){
            marker.marker.setMap(null)
          }
        })
        this.ordersMarkers.splice(existMarkerIndex, 1);
      }
    }
  }

  async addUserMarkerOnMap(user:any) {

    return new Promise(async resolve=>{
      if(user.role_code === 'rb_delivery.role_driver' && !this.isDriverVisibleToBusinessUser(user.id)) {
        let existMarker = this.usersMarkers.filter(marker=>marker.userId==user.id)
        if(existMarker.length>0){
          existMarker[0].marker.setMap(null)
          this.usersMarkers = this.usersMarkers.filter(marker=>marker.userId!=user.id)
        }
        resolve(true)
        return
      }

      if(user.longitude && user.latitude){
        await this.getUserMarker(user).then(async userMarker=>{
          let numberOfOrders= await this.getNumberOfOrders(user)
          let marker:google.maps.Marker = new google.maps.Marker(
            { 
              position: {
                lat: Number(user.latitude),
                lng: Number(user.longitude)
              },
              icon:{
                url:'data:image/svg+xml;base64,' + userMarker,
                scaledSize:new google.maps.Size(64,64),
                origin: new google.maps.Point(0, 0), // origin
                anchor: new google.maps.Point(32, 64), // anchor
              },
              label:
                user.role_code == "rb_delivery.role_driver"? 
                {
                  text:"   "+numberOfOrders,
                  color:numberOfOrders<this.maxNumberOfShipments?"green":"orange",
                  fontSize:"20px",
                  fontWeight:"bolder"
                } 
                : "" ,
              
              clickable:true    
            }
          ) 
          marker.setMap(this.map.googleMap as google.maps.Map)
          let markerToAdd = {
            userId:user.id,
            marker:marker,
            isDriver:user.role_code == 'rb_delivery.role_driver'
          }
          let existMarker = this.usersMarkers.filter(marker=>marker.userId==user.id)
          if(existMarker.length==0){
            this.usersMarkers.push(markerToAdd)
            this.updateUserMarkerClickListener(marker,user)
            const bounds = this.getBounds(this.usersMarkers);
            this.map.googleMap?.fitBounds(bounds,200);
          }else{
            existMarker[0].marker.setMap(null)
            existMarker[0].marker = marker
            marker.setMap(this.map.googleMap as google.maps.Map)
            this.updateUserMarkerClickListener(marker,user)
          }
          
          
        })
      }
      resolve(true)
    })
  }

  addOrderMarkerOnMap(order:any) {
    if(order.longitude && order.latitude && this.nearestDriverStatuses.includes(order.state_id[0])){
      this.getOrderMarker(order).then(orderMarker=>{
        let marker:google.maps.Marker = new google.maps.Marker(
          { 
            position: {
              lat: Number(order.latitude),
              lng: Number(order.longitude)
            },
            icon:{
              url:'data:image/svg+xml;base64,' + orderMarker,
              scaledSize:new google.maps.Size(64,64),
              origin: new google.maps.Point(0, 0), // origin
              anchor: new google.maps.Point(32, 64), // anchor
            },
            clickable:true    
          }
        ) 
        
        marker.setMap(this.map.googleMap as google.maps.Map)
        let markerToAdd = {
          orderId:order.id,
          marker:marker
        }
        this.ordersMarkers.push(markerToAdd)
        this.updateOrderMarkerClickListener(marker,order)
        
      })
    }
    else{
      this.ordersMarkers.forEach(marker=>{
        if(order.id == marker.orderId){
          marker.marker.setMap(null)
        }
      })
    }
  }
  updateOrderMarkerClickListener(marker: google.maps.Marker, order: any) {
    google.maps.event.clearInstanceListeners(marker);
    marker.addListener('click',()=>{
      this.showOrderInfoWindow(marker,order)
    })
  }

  updateUserMarkerClickListener(marker: google.maps.Marker, user: any) {
    
    return new Promise((resolve,reject)=>{
      google.maps.event.clearInstanceListeners(marker);
      marker.addListener('click',()=>{
        this.showUserInfoWindow(marker,user)
      })
      resolve(true)
    }).catch(error=>{
    })
  }

  async showUserInfoWindow(marker:google.maps.Marker,user:any) {
    let color='red'
    let buttonContent=''
    let imageContent=''
    let paragraphContent=''
    if(user.role_code == "rb_delivery.role_driver"){
      let numberOfOrders = await this.getNumberOfOrders(user)
      
      if(numberOfOrders==0){
        color='#3AB600'
      }
      imageContent=`<image style="border-radius:12px; box-shadow: 0 0 10px #ccc;" src="${window.location.origin}/web/image/rb_delivery.user/${user.id}/user_image/70x70"/>`
      paragraphContent=``
      buttonContent=`<button id="noOfOrdersButton" style="background-color:${color} ; border-style: none; cursor:pointer; padding-block: 10px; padding-inline: 30px; border-radius: 8px; margin-inline-start: auto; color:white;"> ${numberOfOrders} ${this.translate.instant('ORDERS_ASSIGNED')} </button>`
      let numberLine = '';
      if (user.commercial_number) {
        numberLine = `<p>${this.translate.instant("MOBILE_NUMBER")}: <a href="tel:${user.commercial_number}">${user.commercial_number}</a></p>`;
      } else if (user.mobile_number) {
        numberLine = `<p>${this.translate.instant("MOBILE_NUMBER")}: <a href="tel:${user.mobile_number}">${user.mobile_number}</a></p>`;
      }

      paragraphContent = `
        <div style="display:flex; flex-direction:column; text-align:center;">
          <p>${user.commercial_name}</p>
          ${numberLine}
        </div>
      `;
    } 

    else if(user.role_code == "rb_delivery.role_business"){
      let numberOfOrders = await this.getNumberOfOrders(user)
      color='#3AB600'
      imageContent=`<image style="border-radius:12px; box-shadow: 0 0 10px #ccc;" src="${window.location.origin}/web/image/rb_delivery.user/${user.id}/user_image/70x70"/>`
      paragraphContent=`<p>${user.commercial_name}</p>`
      buttonContent=`<button id="noOfOrdersButton" style="background-color:${color} ; border-style: cursor:pointer; none; padding-block: 10px; padding-inline: 30px; border-radius: 8px; margin-inline-start: auto; color:white;"> ${numberOfOrders} ${this.translate.instant('ORDERS')}</button>`
    } 

    let contentString=
    `
    <div style="display:flex; flex-direction:row; gap:20px; padding:15px; align-items:center; min-width:250px;">
      `+imageContent+`
      `+paragraphContent+`
      `+buttonContent+`
    </div>
    `

    setTimeout(()=>{
      const infoWindowElement = document.getElementsByClassName("gm-style-iw-a")[0].parentNode as HTMLElement;
      infoWindowElement.addEventListener('click', (event) => {
          if (event.target && (event.target as HTMLElement).id === 'noOfOrdersButton') {
            if(user.role_code == 'rb_delivery.role_business'){
              this.changeActiveContent('orders',[['assign_to_business','=',user.id]],user.commercial_name)
              
            }else if(user.role_code == 'rb_delivery.role_driver'){
              this.changeActiveContent('orders',[['assign_to_agent','=',user.id]],user.commercial_name)
            }
          }
      });
    },500)

    this.showInfoWindow(contentString,marker)
  }

  dismissOverdueOrder(){
    this.overdueOrder=undefined
  }

      showOrderInfoWindow(marker: google.maps.Marker, order: any) {
        let color = this.getStatusColor(order.state);
    
        let buttonContent = `
          <button id="statusButton" style="background-color:${color}; border-style: none; padding-block: 10px; cursor:pointer; padding-inline: 30px; border-radius: 8px; color:white;">
            ${this.allStatuses.filter(status => status.name == order.state)[0].title}
          </button>
        `;
    
        let imageContent = `<image style="border-radius:50%; width:70px; box-shadow: 0 0 10px #ccc;" src="${this.customerIcon}"/>`;
        let paragraphContent = `<p>${order.sequence}</p>`;
    
        // Fetch order card fields dynamically
        let fieldsContent = "";
        if (this.orderCardFields && this.orderCardFields.length > 0) {
            fieldsContent = "<div style='display:flex; flex-direction:column; gap:5px;'>";
            this.orderCardFields.forEach(field => {
                fieldsContent += `<p><strong>${field.field_description}:</strong> ${order[field.name] || 'N/A'}</p>`;
            });
            fieldsContent += "</div>";
        }
    
        // Fetch order card buttons dynamically
        let buttonsContent = "";
        if (this.orderCardButtons && this.orderCardButtons.length > 0) {
            if (this.orderCardButtons.length === 1) {
                buttonsContent = `
                    <button id="${this.orderCardButtons[0].action}" 
                        style="background-color: #3AB600; border-style: none; padding: 10px 20px; cursor: pointer; border-radius: 8px; color: white;">
                        ${this.orderCardButtons[0].name}
                    </button>
                `;
            } else if (this.orderCardButtons.length === 2) {
              buttonsContent = `
                  <div style="display:flex; gap:5px;">
                      ${this.orderCardButtons.map(button => {
                          let buttonColor = button.action === 'statusButton' ? color : '#3AB600';
                          let buttonTitle = button.action === 'statusButton' 
                              ? this.allStatuses.filter(status => status.name == order.state)[0].title 
                              : button.name;
          
                          return `
                              <button id="${button.action}" 
                                  style="background-color:${buttonColor}; border-style: none; padding: 10px 20px; cursor: pointer; border-radius: 8px; color: white;">
                                  ${buttonTitle}
                              </button>
                          `;
                      }).join('')}
                  </div>
              `;
          }
          
        }
    
        // Construct content string with fields in between
        let contentString = `
        <div style="display:flex; flex-direction:column; gap:10px; align-items:center;">
          ${imageContent}
          ${paragraphContent}
          ${fieldsContent}
          ${buttonsContent || buttonContent}
        </div>
        `;
    
        this.showInfoWindow(contentString, marker);
    
        setTimeout(() => {
            const infoWindowElement = document.getElementsByClassName("gm-style-iw-a")[0].parentNode as HTMLElement;
            infoWindowElement.addEventListener('click', (event) => {
                if (event.target) {
                    let buttonId = (event.target as HTMLElement).id;
                    if (buttonId === 'statusButton') {
                        this.changeActiveContent('orders', [['id', '=', order.id]], order.sequence);
                    } else if (buttonId === 'assignAgent') {
                        this.fetchAgentsSorted(order)
                    }
                }
            });
        },500);

    }  

  showInfoWindow(contentString: string,marker:google.maps.Marker) {
    if(this.infowindow){
      this.infowindow.close()
    }
    this.infowindow = new google.maps.InfoWindow({
      content:contentString
    });
    this.infowindow.open(this.map.googleMap,marker)
    google.maps.event.addListener(this.infowindow, 'closeclick', () => {
      this.closeDriverList()
    });
  }

  goToOrders(order:any,state:any,filterTexts:string[]){
    this.changeActiveContent('orders',[['state','=',state]],filterTexts)
    let existMarker=this.ordersMarkers.filter(marker=>marker.orderId == order.id)
    if(existMarker){
      this.zoomOnMarker(existMarker[0].marker)
      this.showOrderInfoWindow(existMarker[0].marker,order)
    }
  }

  zoomOnMarker(marker:google.maps.Marker){
    this.smoothZoom( 18, this.map.googleMap?.getZoom() as number)
    this.map.googleMap?.panTo(marker.getPosition() as google.maps.LatLng);
  }
  

  async getUserMarker(user: any) {
    let userColor = '#012d56'
    let imageUrl = window.location.origin+'/web/image/rb_delivery.user/'+user.id+'/user_image/50x50'
    if(user.online){
      userColor='green'
      if(user.role_code=='rb_delivery.role_driver' && await this.getNumberOfOrders(user)>=this.maxNumberOfShipments){
        if (this.MatchDriverColorToLastOrderStatus){
          const nearest_driver_orders = this.allOrders.filter(order => 
            user['nearest_driver_orders'].includes(order.id)
          );
          if (nearest_driver_orders.length > 0) {
            // Sort by 'status_last_updated_on' field to get the most recent order
            nearest_driver_orders.sort((a, b) => {
              return new Date(b.status_last_updated_on).getTime() - new Date(a.status_last_updated_on).getTime();
            });
            const lastOrder = nearest_driver_orders[0];
            if (lastOrder &&lastOrder.state=="in_progress")
            {
              userColor='#0000ff'
            }
            else{
              userColor='orange'
            }

          }
          
        }else{
          userColor='orange'
        }

      }
    }
    else{
      userColor='red'
    
    }

    let userImage = await this.convertImageToBase64(imageUrl)
    const svgString=`
    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
      
      <image style="width:30px!important;height:30px !important;" x="17" y="12" href="${userImage}"/>
      <path id="Vector" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" fill="${userColor}"/>
      <image style="width:30px!important;height:30px !important;" x="-1" y="-1" href="${user.role_code == 'rb_delivery.role_driver' && user.vehicle_type == 'car' ? this.carIcon : 
      user.role_code == 'rb_delivery.role_driver' && user.vehicle_type != 'car' ? this.motorcycleIcon : 
      user.role_code == 'rb_delivery.role_business' ? this.businessIcon : 
      ''}"/>
    </svg>
    `
    return window.btoa(svgString)

  }

  async getOrderMarker(order: any) {
    let statusColor = this.getStatusColor(order.state)

    let orderImage = await this.convertImageToBase64(this.customerIcon)
    const svgString=`
    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
      
      <image style="width:30px!important;height:30px !important;" x="17" y="12" href="${orderImage}"/>
      <path id="Vector" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" fill="${statusColor}"/>
    </svg>
    `
    return window.btoa(svgString)

  }

  getStatusColor(status:string) {
    let statusColor = '#012d56'
    switch(status){
      case "waiting":
        statusColor = 'grey'
        break;
      case "picking_up":
        statusColor = 'orange'
        break;
      case "picked_up":
        statusColor = 'light-green'
        break;
      case "pickup_overdue":
        statusColor = 'red'
        break;
      case "delivery_overdue":
        statusColor = 'red'
        break;
      
    }
    return statusColor
  }

  moveMarker(marker:google.maps.Marker,record:any,postFix:'O'|'U'|'L') {
    return new Promise((resolve, reject) => {
      let recordId=record.id.toString()+postFix
      if(this.movingRecordIds.includes(recordId)){
        return
      }
      this.movingRecordIds.push(recordId)
      let lat = marker.getPosition()?.lat() as number
      let lng = marker.getPosition()?.lng() as number

      let deltalat = (record.latitude - lat) / 100;
      let deltalng = (record.longitude - lng) / 100;
      let delay = 10 * .5;
      let self = this
      for (let i = 0; i < 100; i++) {
        (function(ind) {
          setTimeout(
            function() {
              lat += deltalat;
              lng += deltalng;
              marker.setPosition({lat,lng})
              if(i==99){
                self.movingRecordIds.splice(self.movingRecordIds.indexOf(recordId),1)
                resolve(true)
              }
              
            }, delay * ind
          );
        })(i)
      }
    })
  }

  addGoogleMapsScript() {
    // Create the script element
    const script = this.renderer.createElement('script');
  
    // Set the script source with the Google Maps API URL and your API key
    script.src = 'https://maps.googleapis.com/maps/api/js?v=beta&key=' + this.googleApiKey + "&language="+this.translate.currentLang+"&libraries=geometry,marker,maps,visualization";
  
    // Append the script element to the head of the HTML document
    this.renderer.appendChild(document.head, script);
    setTimeout(()=>{
      this.googleApiLoaded=true
      this.fetchStatusesIds()
      this.fetchQuickOrderConfiguration()
      this.fetchClientConfiguration()
      this.fetchOrderCardDate()

    },1000)
    
  }
  async fetchClientConfiguration() {
    this.MatchDriverColorToLastOrderStatus = await this.fetchRecords('rb_delivery.client_configuration',['match_driver_color_to_last_order_status'],'get_param')
    this.showDriverStatusInSideMenuForBusiness = await this.fetchRecords('rb_delivery.client_configuration',['show_driver_status_in_side_menu_for_business'],'get_param')
  
  }
  async fetchOrderCardDate() {
    let response = await this.fetchRecords('rb_delivery.dispatcher_order_card', [], 'get_card_fields');
    if (response){
      this.orderCardFields = response.fields || [];
      this.orderCardButtons = response.buttons || [];
    }

}
  async fetchQuickOrderConfiguration() {
    this.showCustomerDetails = await this.fetchRecords('rb_delivery.client_configuration',['show_customer_details_on_dispatcher_quick_order'],'get_param')
    this.showPaymentSection = await this.fetchRecords('rb_delivery.client_configuration',['show_payment_section_on_dispatcher_quick_order'],'get_param')
    this.showNotesSection = await this.fetchRecords('rb_delivery.client_configuration',['show_note_section_on_dispatcher_quick_order'],'get_param')
    this.showExtraCost = await this.fetchRecords('rb_delivery.client_configuration',['show_extra_cost_on_dispatcher_quick_order'],'get_param')
  }
  async fetchStatusesIds() {
    this.fetchNearestDriverDurationConfig()
    orderStatuses = await this.fetchRecords('rb_delivery.client_configuration',['default_status_olivery_dispatcher_map'],'get_param')
    this.fetchStatuses()
    this.fetchGroupingConfig()
    await this.fetchUserOrderFilters() 
    await this.fetchAllUsers()
    this.fetchCurrentUser()
    this.fetchUsersFiltered([["role_code", "in", USER_ROLES]])
    this.onUsersChangeListener()
    this.onOrdersChangeLitener()
    
  }
  async fetchGroupingConfig() {
    this.groupByFields = await this.fetchRecords("rb_delivery.dispatcher_map_config", [],'get_order_grouping_config');
    ORDER_FIELDS = [...ORDER_FIELDS,...this.groupByFields.map(element=>element[0]).filter(element=>!ORDER_FIELDS.includes(element))]
  }

  async fetchCurrentUser() {
    let currentUser = await this.fetchRecords('rb_delivery.user',[],'get_user_info')
    if(currentUser.length>0){
      this.groupId = currentUser[0].group_id[0]
      this.userRole=currentUser[0]['role_code']
      this.currentUserId = currentUser[0]['id']
      
    }else{
      // in case admin or system support user
      this.userRole='admin'
    }
    if(['admin','rb_delivery.role_super_manager','rb_delivery.role_manager'].includes(this.userRole)){
      await this.fetchRoutes()
      this.onRoutesChangeLitener()
    }
  }

  async fetchNearestDriverDurationConfig() {
    this.timeDurationConfig = await this.fetchRecords('rb_delivery.nearest_driver',['one_by_one','timer_duration'],'get_param')
    this.maxNumberOfShipments = await this.fetchRecords('rb_delivery.nearest_driver',['one_by_one','number_of_shipments'],'get_param')
    this.nearestDriverStatuses = await this.fetchRecords('rb_delivery.nearest_driver',['one_by_one','shipment_statuses'],'get_param')
  }
  async fetchStatuses() {
    this.allStatuses = await this.fetchRecords("rb_delivery.status", [[['status_type','=','olivery_order']],STATUS_FIELDS],'search_read');
    this.visibleStatuses = this.allStatuses.filter(status=>orderStatuses.includes(status.id))
  }

  convertImageToBase64(imgUrl:any) {
    return new Promise((resolve, reject) => {
      const image = new Image();
      image.crossOrigin='anonymous';
      
      image.onload = () =>{
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.height = image.naturalHeight;
        canvas.width = image.naturalWidth;
        ctx?.drawImage(image, 0, 0);
        const dataUrl = canvas.toDataURL()
        resolve(dataUrl)
      } 
      image.onerror = reject
      image.src = imgUrl
    })
  }

  async addUsersOnMap(users:any){
    if(users && users.length>0)
      for(let user of users){
        if(user.role_code == 'rb_delivery.role_driver' && !user.online){
          continue
        }
        await this.updateUserLocationOnMap(user)
      }
  }

  async fetchBusinessUserDrivers() {
    if(this.userRole === 'rb_delivery.role_business') {
      const drivers = await this.fetchRecords("rb_delivery.user", [[["role_code", "=", "rb_delivery.role_driver"]],USER_FIELDS],'sudo_search_users_for_business');
      return drivers;
    }
    return [];
  }

  isDriverVisibleToBusinessUser(driverId: number): boolean {
    if(this.userRole !== 'rb_delivery.role_business') {
      return true;
    }

    const isAssignedToMyOrders = this.allOrders.some(order =>
      order.assign_to_business &&
      order.assign_to_business[0] === this.currentUserId &&
      order.assign_to_agent &&
      order.assign_to_agent[0] === driverId
    );

    return isAssignedToMyOrders;
  }

  addOrdersOnMap(orders:any[]){
    this.pickupOverdueOrders = orders.filter(order=>order.state=='pickup_overdue')
    this.deliveryOverdueOrders = orders.filter(order=>order.state=='delivery_overdue')
    if(orders && orders.length>0)
      for(let order of orders){
        this.addOrderMarkerOnMap(order)
      }
  }

  getBounds(markers:{userId:number,marker:google.maps.Marker}[] | {orderId:number,marker:google.maps.Marker}[]){
    let north;
    let south;
    let east;
    let west;
  
    for (const marker of markers){
      // set the coordinates to marker's lat and lng on the first run.
      // if the coordinates exist, get max or min depends on the coordinates.
      north = north !== undefined ? Math.max(north, marker.marker.getPosition()?.lat() as number) : marker.marker.getPosition()?.lat() as number;
      south = south !== undefined ? Math.min(south, marker.marker.getPosition()?.lat() as number) :marker.marker.getPosition()?.lat() as number;
      east = east !== undefined ? Math.max(east, marker.marker.getPosition()?.lng() as number) : marker.marker.getPosition()?.lng() as number;
      west = west !== undefined ? Math.min(west, marker.marker.getPosition()?.lng() as number) : marker.marker.getPosition()?.lng() as number;
    };
  
    const bounds = { north:north as number, south:south as number, east:east as number, west:west as number };
  
    return bounds;
  }

  smoothZoom ( zoomTo:number, currentZoom:number) {
    let self = this
    if (currentZoom >= zoomTo) {
        return;
    }
    else {
        let zoomListener = google.maps.event.addListener(self.map.googleMap as google.maps.Map, 'zoom_changed', function(event:any){
            google.maps.event.removeListener(zoomListener);
            self.smoothZoom( zoomTo, currentZoom + 1);
        });
        setTimeout(function(){(self.map.googleMap as google.maps.Map).setZoom(currentZoom)}, 80);
    }
  }  

  showOrderOnMap(order:any,showOnlyRelatedMarkers?:boolean){
    if(this.directionsRenderer){
      this.removeDirectionsFromMap()
    }
    this.filteredOnAgent = undefined
    this.filteredOnRoute = undefined
    this.filteredOnSender = undefined
    let visibleMarkers:any[]=[]
    if(this.filteredOnOrder != order){
      this.filteredOnOrder = order
      let existMarker = this.ordersMarkers.filter(marker=>marker.orderId==order.id)
      if(existMarker.length>0){
        this.showOrderInfoWindow(existMarker[0].marker,order)
      }
      this.ordersMarkers.forEach(marker=>{
        if(order.id != marker.orderId){
          marker.marker.setMap(null)
        }else{
          if(this.nearestDriverStatuses.includes(order.state_id[0])){
            marker.marker.setMap(this.map.googleMap as google.maps.Map)
            visibleMarkers.push(marker)
          }
        }
      })

      this.driverLocationordersMarkers.forEach(marker=>{
        if(order.id == marker.orderId && !this.nearestDriverStatuses.includes(order.state_id[0])){
          marker.marker.setMap(null)
        }
      })
      
      if(showOnlyRelatedMarkers){
        visibleMarkers = this.showOrderRelatedMarkers(order).concat(visibleMarkers)
      }

      
      
    }
    else{
      visibleMarkers = this.showAllMarkers()
    }
    if(visibleMarkers.length>0 && showOnlyRelatedMarkers){
      if((!order.longitude || !order.latitude)){
        this.showToast(this.translate.instant("LOCATION_NOT_SET_FOR_THE_ORDER"),'warning',this.translate.instant("WARNING"))
      }
      const bounds = this.getBounds(visibleMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
      if(this.geofence){
        this.geofence.setMap(null)
      }
    }
    else if(showOnlyRelatedMarkers){
      this.showAllMarkers()
      this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),'fail',this.translate.instant("NO_AVAILABLE_LOCATIONS"))
    }

  }

  showOrderRelatedMarkers(order:any) {
    let visibleMarkers:any[]=[]
    this.usersMarkers.forEach(marker=>{
      if(marker.userId!=order.assign_to_agent[0] && marker.userId!=order.assign_to_business[0]){
        marker.marker.setMap(null)
      }else{
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
        visibleMarkers.push(marker)
      }
    })
    if(this.userRole == 'rb_delivery.role_business'){
      this.driverLocationordersMarkers.forEach(marker=>{
        if(order.id == marker.orderId){
          marker.marker.setMap(this.map.googleMap as google.maps.Map)
          visibleMarkers.push(marker)
        }
      })
    }
    return visibleMarkers
  }

  showAllMarkers() {
    if(this.directionsRenderer){
      this.removeDirectionsFromMap()
    }
    let visibleMarkers:any[]=[]
    this.resetMapMarkers()
    this.ordersMarkers.forEach(marker=>{
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
        visibleMarkers.push(marker)
    })

    this.usersMarkers.forEach(marker=>{
      marker.marker.setMap(this.map.googleMap as google.maps.Map)
      visibleMarkers.push(marker)
    })
    return visibleMarkers
  }
  resetMapMarkers() {
    
    this.filteredOnAgent = undefined
    this.filteredOnSender = undefined
    this.filteredOnOrder = undefined
    this.filteredOnRange=undefined
    if(this.geofence){
      this.geofence.setMap(null)
    }
  }

  showAgentOnMap(agent:any,showOnlyRelatedMarkers?:boolean){
    if(this.directionsRenderer){
      this.removeDirectionsFromMap()
    }
    this.filteredOnOrder=undefined
    this.filteredOnSender=undefined
    let visibleMarkers:any[]=[]
    if(this.filteredOnAgent != agent){
      let relatedBusinessesIds:any[]=[]
      this.filteredOnAgent = agent
      let existMarker = this.usersMarkers.filter(marker=>marker.userId==agent.id)
      if(existMarker.length>0){
        this.showUserInfoWindow(existMarker[0].marker,agent)
      }
      this.ordersMarkers.forEach(marker=>{
        let relatedOrder = this.allOrders.filter(order=>order.id==marker.orderId)[0]
        if(agent.id != relatedOrder.assign_to_agent[0]){
          marker.marker.setMap(null)
        }else{
          marker.marker.setMap(this.map.googleMap as google.maps.Map)
          visibleMarkers.push(marker)
          relatedBusinessesIds.push(relatedOrder.assign_to_business[0])
        }
      })
      
      if(showOnlyRelatedMarkers){
        visibleMarkers = this.showAgentRelatedMarkers(agent,relatedBusinessesIds).concat(visibleMarkers)
      }

      
      
    }
    else{
      visibleMarkers = this.showAllMarkers()
    }
    if(visibleMarkers.length>0 && showOnlyRelatedMarkers){
      const bounds = this.getBounds(visibleMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
      if(this.geofence){
        this.geofence.setMap(null)
      }
    }
    else if(showOnlyRelatedMarkers){
      this.showAllMarkers()
      this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),'fail',this.translate.instant("NO_AVAILABLE_LOCATIONS"))
    }
    

  }

  onRoutesChangeLitener() {
    if(this.routesEventSource){
      this.routesEventSource.close()
    }
    this.routesEventSource = new EventSource('https://'+window.location.hostname+"/routes_stream/"+JSON.stringify(this.context),{withCredentials:true});
    this.routesEventSource.onmessage = (event)=>{
      let changedRoutes = JSON.parse(event.data)
      this.ngZone.run(async () => {

        changedRoutes.forEach((route:any)=>{
          
          let oldRoute = this.routes.filter((oldRoute:any)=>route.id == oldRoute.id)
          if(oldRoute.length>0){
            this.routes[this.routes.indexOf(oldRoute[0])]=route
            if(this.filteredOnRoute && oldRoute[0].id==this.filteredOnRoute.id){
              
              this.showRouteOnMap(route)
              if(route.status=='done'){
                this.routes = this.routes.filter(r=>r.id != route.id)
                let alreadyInDoneRoutes = this.doneRoutes.filter(r=>route.id == r.id).length>0
                if(!alreadyInDoneRoutes){
                  this.doneRoutes.push(route)
                }
              }
            }
          }
          else if(route.status == 'in_progress'){
            this.routes.push(route)
          }
          else{
            let alreadyInDoneRoutes = this.doneRoutes.filter(r=>route.id == r.id).length>0
            if(!alreadyInDoneRoutes){
              this.doneRoutes.push(route)
            }
          }
          
        })
      });
    }
  }

  showRouteOnMap(route:any,showOnlyRelatedMarkers?:boolean){
    
    this.removeDirectionsFromMap()
    this.filteredOnOrder=undefined
    this.filteredOnSender=undefined
    let visibleMarkers:any[]=[]
    if(this.filteredOnRoute != route){
      if(typeof route.direction == 'string'){ 
        route.direction = JSON.parse(route.direction)
        route.locations = JSON.parse(route.locations)
        route.done_locations = JSON.parse(route.done_locations)
      }
      this.filteredOnRoute = route
      
      
      if(showOnlyRelatedMarkers){
        visibleMarkers = this.showRouteRelatedMarkers(route).concat(visibleMarkers)
      }
      if(!this.directionsRenderer){
        this.directionsRenderer = new google.maps.DirectionsRenderer({
          preserveViewport:true
        });
      }
      for (let location of this.filteredOnRoute.done_locations) {
      
        let orderLatLng = new google.maps.LatLng(
          location.location.lat,
          location.location.lng
        );
        
        let doneOrderMarker=new google.maps.Marker({
          position: orderLatLng,
          map: this.map.googleMap,
          icon: '/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/delivered-marker.svg',  
        })
        this.doneOrderMarkers.push(doneOrderMarker)
        doneOrderMarker.addListener('click',(()=>{
          let order = this.allOrders.filter(order=>order.id == location.orderId)
          if(order.length>0){
            this.showOrderInfoWindow(doneOrderMarker,order[0])
          }
        }))
      }
      this.ordersMarkers.forEach((marker)=>{
        marker.marker.setMap(null)
      })
      if(this.filteredOnRoute.direction){
        this.directionsRenderer.setOptions({
          suppressMarkers: true
        });
        this.directionsRenderer.setDirections(this.filteredOnRoute.direction);
        let cumulativeDuration = 0;
      
        const legs = (this.filteredOnRoute.direction?.routes[0] as google.maps.DirectionsRoute).legs;
        const utcwritedate = new Date();
  
        // The Date object automatically uses local time zone when you retrieve date and time components.
        const localDate = new Date(utcwritedate); 
  
        // Convert to local time string if needed
        const orders = this.allOrders.filter(order=>this.filteredOnRoute.order_ids.includes(order.id))
        legs.forEach((leg, index) => {
          
          // Add current leg duration to cumulative duration
          cumulativeDuration += leg.duration?.value || 0; // duration.value is in seconds
  
          // Calculate the arrival time
          const arrivalTime = new Date(localDate.getTime() + cumulativeDuration * 1000);
          const hours = arrivalTime.getHours().toString().padStart(2, '0');
          const minutes = arrivalTime.getMinutes().toString().padStart(2, '0');
          const durationText = `${hours}:${minutes}`;
  
          // Set label with arrival time
          if(index == 0){
            return
          }
          const marker = new google.maps.Marker({
            position: this.filteredOnRoute.locations[index].location,
            map: this.map.googleMap,
            icon: '/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/order-marker.svg',
            label:durationText,
            zIndex:9999
          })
          this.routeMarkers.push(marker)
          visibleMarkers.push({marker})
          marker.addListener('click',(()=>{
            this.showOrderInfoWindow(marker,orders[index])
          }))
        });
  
        // Set the directions renderer
        this.directionsRenderer.setMap(this.map.googleMap as google.maps.Map);
      }
      else{
        visibleMarkers=[]
        this.doneOrderMarkers.forEach(marker => {
          visibleMarkers.push({marker})
        });
      }
      
      
    }
    else{
      visibleMarkers = this.showAllMarkers()
      this.filteredOnRoute = undefined
    }
    if(visibleMarkers.length>0 && showOnlyRelatedMarkers){
      const bounds = this.getBounds(visibleMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
      if(this.geofence){
        this.geofence.setMap(null)
      }
    }
    else if(showOnlyRelatedMarkers){
      this.showAllMarkers()
      this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),'fail',this.translate.instant("NO_AVAILABLE_LOCATIONS"))
    }
    
    

  }
  removeDirectionsFromMap() {
    if(this.directionsRenderer){
      this.directionsRenderer.setMap(null)
    }
    
    this.doneOrderMarkers.forEach((marker, index) => {
      this.doneOrderMarkers[index].setMap(null);
    });
    this.doneOrderMarkers=[]
    this.routeMarkers.forEach((marker)=>{
      marker.setMap(null)
    })
    this.routeMarkers=[]
  }

  

  showAgentRelatedMarkers(agent: any,relatedBusinessesIds:any[]){
    let visibleMarkers:any[]=[]
    this.usersMarkers.forEach(marker=>{
      if(marker.userId!=agent.id && !relatedBusinessesIds.includes(marker.userId)){
        marker.marker.setMap(null)
      }else{
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
        visibleMarkers.push(marker)
      }
    })
    return visibleMarkers
  }

  showRouteRelatedMarkers(route: any){
    let visibleMarkers:any[]=[]
    this.usersMarkers.forEach(marker=>{
      if(marker.userId!=route.driver_id[0]){
        marker.marker.setMap(null)
      }else{
        marker.marker.setMap(this.map.googleMap as google.maps.Map)
        visibleMarkers.push(marker)
      }
    })
    return visibleMarkers
  }
  

  async fetchAgentsSorted(order?:any){

    
    this.closeStatusList()    
    let drivers=this.filteredUsers.filter(user=>user.role_code=="rb_delivery.role_driver")
    if(!order){
        this.currentOrder = undefined
        this.driversToSelect$.next(drivers)
        return
    }
    this.currentOrder=this.allOrders.find(o => o.id==order.id);
    let isEtaInstalled = await this.fetchRecords("ir.module.module", [[["name", "=", "olivery_eta"], ["state", "=", "installed"]]], "search_read");
    if (order['latitude']&& order['latitude']!=false && order['longitude'] &&order['longitude']!=false && isEtaInstalled &&isEtaInstalled.length==1){
      let sortedDrivers = await this.fetchRecords("rb_delivery.order", [order,drivers],'sort_agents_by_google_api');
        if (!sortedDrivers || !sortedDrivers.length){
            sortedDrivers = drivers.sort((a, b) => Number(b.online) - Number(a.online));
          }
        this.driversToSelect$.next(sortedDrivers);
    } else {
        this.driversToSelect$.next(drivers);
    }
    this.showDriverList=true
    this.cdr.detectChanges()
  }

  closeDriverList() {
    this.driversToSelect$.next([]);
    if (this.showDriverList){
      this.showDriverList=false
      this.cdr.detectChanges()
    }    
    if (this.infowindow) {
        this.infowindow.close();
    }
}

  activateChangeStatus(order:any){
    this.currentOrder=this.allOrders.filter(o=>o.id==order.id)[0]
    this.isChooseStatusActive=true
    this.closeDriverList()
  }

  closeStatusList(){
    this.isChooseStatusActive=false
  }

  async updateCurrentOrder(values: any) {
    
    if('state' in values){
      this.allOrders[this.allOrders.indexOf(this.currentOrder)]['add_fade_animation']=true
    }
    let args = [[this.currentOrder['id']], values];
    try {
        let writePass = await this.fetchRecords("rb_delivery.order", args, 'write');
        if(writePass){
          this.closeDriverList()
          this.closeStatusList()
        }else if('state' in values){
          this.allOrders[this.allOrders.indexOf(this.currentOrder)]['add_fade_animation']=false
        }
        
        
        
    } catch (error) {
        console.error("Error during driver assignment:", error);
    }
  }

  filterOnRange() {
    if(this.geofence){
      this.geofence.setRadius(this.driversRange)
      this.geofence.setCenter(this.filteredOnRange as google.maps.LatLng)
    }else{
      this.geofence = new google.maps.Circle({
        strokeColor: "#0275ff",
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: "#0275ff",
        fillOpacity: 0.35,
        center: this.filteredOnRange,
        radius: this.driversRange
      });
    }
    

    this.geofence.setMap(this.map.googleMap as google.maps.Map)

    if(this.fitGeofenceToBounds){
      this.map.googleMap?.fitBounds(this.geofence.getBounds() as google.maps.LatLngBounds)
    }

    this.usersMarkers.forEach((marker) => {
        const markerLatLng = marker.marker.getPosition() as google.maps.LatLng
        const distanceInKM = google.maps.geometry.spherical.computeDistanceBetween(
            this.filteredOnRange as google.maps.LatLng,
            markerLatLng
        );
        if (distanceInKM <= this.driversRange || !marker.isDriver) {
          // Show the marker if within the specified range
          marker.marker.setMap(this.map.googleMap as google.maps.Map);
        } else {
          // Hide the marker if outside the specified range
          marker.marker.setMap(null); 
        }
    });
}


  getTopOffset(order?:any){
    if(!order){
      return '0px'
    }
    let offsetTop = (document.getElementById(order.id.toString())?.offsetTop as number) - (document.getElementById('groupContainer')?.scrollTop as number)
    if((offsetTop + document.documentElement.clientHeight*40/100) > document.documentElement.clientHeight ){
      offsetTop = document.documentElement.clientHeight - document.documentElement.clientHeight*40/100
    }
    return (offsetTop as number).toString()+'px'
  }

   async sendOrder(orderToSend:any){

    let createOrder=await this.fetchRecords("rb_delivery.order", [orderToSend],'create');

    if (createOrder){
      this.showQuickOrder=false
      this.showToast(this.translate.instant("ORDER_ADDED_SUCCESSFULLY"),'success')
    }else{
      if (this.fetchRecordErrorMessage){
        this.showToast(this.translate.instant(this.fetchRecordErrorMessage),'fail')
      }
      else{
        this.showToast(this.translate.instant("ERROR_WHILE_ADDING_ORDER"),'fail')
      }
    }
    

  }
  showToast(message:string,type:string,title?:string) {
    this.isToastVisible = true;
    this.toastMessage=message
    this.toastType=type
    this.toastTitle=title
    setTimeout(() => {
        this.isToastVisible = false;
        this.toastMessage=undefined
        this.toastType=undefined
        this.toastTitle=undefined
    }, 5000); 
    if (type=='success'){
      this.playSuccessSound()
    }
    else if(type=='fail'){
      this.playDangerSound()
    }
    else if(type=='warning'){
      this.playWarningrSound()
    }
    this.cdr.detectChanges()
  }


  playSuccessSound = () => {
    const successAudio = new Audio('/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/success.wav');
    successAudio.play();
  };

  playDangerSound = () => {
    const dangerAudio = new Audio('/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/danger.wav');
    dangerAudio.play();
  };

  playWarningrSound = () => {
    const dangerAudio = new Audio('/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/warning.wav');
    dangerAudio.play();
  };

  showSenderOnMap(sender:any,showOnlyRelatedMarkers?:boolean){
    if(this.directionsRenderer){
      this.removeDirectionsFromMap()
    }
    this.filteredOnOrder=undefined
    this.filteredOnAgent=undefined
    let visibleMarkers:any[]=[]
    if(this.filteredOnSender != sender){
      this.filteredOnSender = sender
      let existMarker = this.usersMarkers.filter(marker=>marker.userId==sender.id)
      if(existMarker.length>0){
        this.showUserInfoWindow(existMarker[0].marker,sender)
      }
      this.ordersMarkers.forEach(marker=>{
        let relatedOrder = this.allOrders.filter(order=>order.id==marker.orderId)[0]
        if(sender.id != relatedOrder.assign_to_business[0]){
          marker.marker.setMap(null)
        }else{
          marker.marker.setMap(this.map.googleMap as google.maps.Map)
          visibleMarkers.push(marker)
        }
      })

      this.usersMarkers.forEach(marker=>{
        if(marker.userId!=sender.id){
          marker.marker.setMap(null)
        }else{
          marker.marker.setMap(this.map.googleMap as google.maps.Map)
          visibleMarkers.push(marker)
        }
      })
      
      
    }
    else{
      visibleMarkers = this.showAllMarkers()
    }
    if(visibleMarkers.length>0 && showOnlyRelatedMarkers){
      const bounds = this.getBounds(visibleMarkers);
      this.map.googleMap?.fitBounds(bounds,200);
      if(this.geofence){
        this.geofence.setMap(null)
      }
    }
    else if(showOnlyRelatedMarkers){
      this.showAllMarkers()
      this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),'fail',this.translate.instant("NO_AVAILABLE_LOCATIONS"))
    }
    

  }

  async updateDomain(filterTypes: string[]) {
    this.filterTypes = filterTypes;
    this.currentUsersDomain = [];
    this.orderFilterDomain = [];
    if (filterTypes.length>0){
      for (let filterType of filterTypes) {
        if (filterType === "ALL") {
          this.currentUsersDomain = [];
          let filteredRoles = []
          if (this.activeContent == 'senders') {
            filteredRoles.push('rb_delivery.role_business')
          } else {
            filteredRoles.push('rb_delivery.role_driver')
          }
          await this.fetchUsersFiltered([["role_code", "in", filteredRoles]], true);
        }
        else if (filterType === "OFFLINE_BUSINESSES") {
          this.currentUsersDomain.push(["online", "=", false]);
          await this.fetchUsersFiltered([["role_code", "=", 'rb_delivery.role_business'], ...this.currentUsersDomain], true);
        }
        else if (filterType === "ONLINE_BUSINESSES") {
          this.currentUsersDomain.push(["online", "=", true]);
          await this.fetchUsersFiltered([["role_code", "=", 'rb_delivery.role_business'], ...this.currentUsersDomain], true);
        }
        else if (filterType === "DEFAULT") {
          if (this.activeContent === 'orders') {
            this.orderFilterDomain = [];
            await this.fetchOrders(true);
          } else if (this.activeContent==='senders') {
            this.currentUsersDomain.push(["online", "=", true]);
            await this.fetchUsersFiltered([["role_code", "=", 'rb_delivery.role_business'], ...this.currentUsersDomain], true);
          }
        }
        else if (filterType === "TODAY_ORDERS") {
          let utcDate = this.getTodayStartEnd();
          this.orderFilterDomain.push(['create_date', '>=', utcDate[0]], ['create_date', '<=', utcDate[1]]);
          await this.fetchOrders(true);
        }
        else if (filterType in this.dispatcherOrderFilters) {
          const filterDomain = JSON.parse(this.dispatcherOrderFilters[filterType]);
          this.orderFilterDomain.push(...filterDomain);
          await this.fetchOrders(true);
        }
      }
    }
    else{
      await this.fetchOrders(true);
    }  
    this.clearUsersOfMap()
  
  }

  getNextStatus(order?:any){
    if(!order){
      return []
    }
    let nextStatusesIds = this.allStatuses.filter(status=>status.id == order.state_id[0])[0].next_state_ids
    let nextStatuses = this.allStatuses.filter(status=>nextStatusesIds.includes(status.id))
    let statusesUserCanChange = nextStatuses.filter((status:any)=>!this.groupId || status.role_action_status_ids.includes(this.groupId))
    return statusesUserCanChange
  }

  getTodayStartEnd(){
    const now = new Date();

    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const utcStartDate = startOfDay.toISOString();

    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    const utcEndDate = endOfDay.toISOString();
    return [utcStartDate,utcEndDate]
  }

  clearUsersOfMap() {
    let userIds = this.filteredUsers.map(user => user.id)
    for (let marker of this.usersMarkers) {
      if (!userIds.includes(marker.userId)) {
        marker.marker.setMap(null)
      }
    }

  }

  async fetchDeliveryCost(event:any){
    if(!this.orderTypeId)
      this.orderTypeId = (await this.fetchRecords('rb_delivery.order_type',[[['default','=',true]],['id'],0,1],'search_read'))[0]?.id | 1
    this.deliveryCost = await this.fetchRecords('rb_delivery.pricelist',[{...event,'order_type_id':this.orderTypeId}],'get_price')
  }

  async getNumberOfOrders(user:any){
    if(user.role_code == 'rb_delivery.role_business' ){
      return user['dispatcher_business_orders'].length
    }
    else if(user.role_code == 'rb_delivery.role_driver'){
        return user['nearest_driver_orders'].length
    }
    return 0
  }

  setCheckedOrderIds(event:number[]){
    this.checkedOrderIds = event

    // Pause the orders event stream when orders are checked to prevent selected orders being moved from their places during the stream
    if(this.checkedOrderIds.length>0){
      this.ordersEventSource.close()
    }
    else{
      // Run the stream again after no orders are checked
      this.onOrdersChangeLitener()
    }
  }

  async handleActionOnSelectedOrders(event:any){
    if(event.action=='assign_agent'){
      this.closeDriverList()
      try {
        let args = [this.checkedOrderIds, event.values];
        let writePass = await this.fetchRecords("rb_delivery.order", args, 'write');
        if(writePass){
          this.closeDriverList()
          this.closeStatusList()
        }
          
          
          
      } catch (error) {
          console.error("Error during driver assignment:", error);
      }
    }

  }


}
