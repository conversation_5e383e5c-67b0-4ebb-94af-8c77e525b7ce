
# -*- coding: utf-8 -*-
{
    'name': "olivery_dispatcher_map",
    'summary': """
        <PERSON><PERSON> App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.0.70',

    # any module necessary for this one to work correctly
    'depends': ['rb_delivery','olivery_nearest_driver'],

    # always loaded
    'data': [
        'views/module_view.xml',
        'demo/client_conf.xml',
        'security/ir.model.access.csv',
        'security/security.xml',
        'models/dispatcher_map_config/dispatcher_map_config_view.xml',
        'models/dispatcher_map_date_rule/dispatcher_map_date_rule_view.xml',
        'models/dispatcher_map_filter/dispatcher_map_filter_view.xml',
        'models/route_collection/route_collection_view.xml',
        'models/dispatcher_map_card_button/dispatcher_map_card_buttons_view.xml',
        'models/dispatcher_map_order_card/dispatcher_map_order_card_view.xml',
        'demo/error_log.xml',
        'demo/sequence.xml',
        'demo/status.xml',
        'demo/card_buttons.xml'
        
    ], 
    'qweb': [
         'static/src/xml/map_view.xml',
    ],
}
