<?xml version="1.0"?>

<odoo>

    <data>
        <record id="paperformat_order_follow_up_a6" model="report.paperformat">
            <field name="name">Delivery_Distribution</field>
            <field name="default" eval="True" />
            <field name="format">A6</field>
            <field name="orientation">Landscape</field>
            <field name="margin_top">0</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False" />
            <field name="header_spacing">1</field>
            <field name="dpi">90</field>
        </record>

        <record id="report_rb_delivery_order_follow_up_a6_action" model="ir.actions.report">
            <field name="name">Follow Up A6</field>
            <field name="model">rb_delivery.order</field>
            <field name="binding_model_id" ref="rb_delivery.model_rb_delivery_order"></field>
            <field name="binding_type">report</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">olivery_templates.order_follow_up_a6</field>
            <field name="paperformat_id" ref="paperformat_order_follow_up_a6" />
        </record>

        <template id="minimal_layout_inherit" inherit_id="web.minimal_layout">
            <xpath expr="//head" position="inside">
                <link rel='stylesheet' href="/rb_delivery/static/src/css/report.css" />
            </xpath>
        </template>

        <template id="order_follow_up_a6">
            <t t-call="web.basic_layout">
                <t t-foreach="docs" t-as="doc">
                    <t t-if="not o" t-set="o" t-value="doc" />
                    <t t-if="not company">
                        <t t-if="company_id">
                            <t t-set="company" t-value="company_id" />
                        </t>
                        <t t-elif="o and 'company_id' in o">
                            <t t-set="company" t-value="o.company_id.sudo()" />
                        </t>
                        <t t-else="else">
                            <t t-set="company" t-value="res_company" />
                        </t>
                    </t>
                    <div class="header" style="display:none;direction:rtl;font-size:12px !important"></div>

                    <div class="footer"></div>
                    <t t-foreach="doc.sudo().follow_up_order or 1" t-as="follow_order">
                        <div class="page" style="page-break-before:always">
                            <div class="row">
                                <div class="col-4 align-self-start">
                                    <t
                                        t-if="doc.sudo().assign_to_business.replace_company_logo_with_business_logo">
                                        <img t-if="doc.sudo().assign_to_business.user_image"
                                            t-att-src="image_data_uri(doc.sudo().assign_to_business.user_image)"
                                            alt="Logo" style="max-height: 70px;max-width: 70px;" />
                                    </t>
                                    <t t-else="else">
                                        <img t-if="company.logo"
                                            t-att-src="image_data_uri(company.logo)" alt="Logo"
                                            style="max-height: 70px;max-width: 70px;" />
                                    </t>
                                </div>
                                <div t-if="doc.sudo().assign_to_business.print_user_logo_in_bolisa and doc.sudo().assign_to_business.user_image">
                                    <div class="col-4 align-self-center">
                                        <img
                                            style="width:350px !important;height:60px;display:block !important;margin:0 auto !important"
                                            t-attf-src="data:image/*;base64,{{doc.sudo().barcode}}" />
                                        <h5 class="text-center"
                                            style="font-size:0.7em !important;font-weight:bold">
                                            <span t-field="doc.sudo().sequence" />
                                        </h5>
                                    </div>
                                    <div class="col-4 text-right"
                                        t-if="doc.sudo().assign_to_business.print_user_logo_in_bolisa">
                                        <img t-if="doc.sudo().assign_to_business.user_image"
                                            t-att-src="image_data_uri(doc.sudo().assign_to_business.user_image)"
                                            alt="Logo"
                                            style="max-height: 70px;max-width:120px;object-fit:contain;" />
                                    </div>
                                </div>
                                <div t-else="else">
                                    <div class="col-8 align-self-center" style="text-align: center;">
                                        
                                    </div>
                                </div>
                            </div>

                            <div class="image_bg"></div>
                            <div class="oe_structure" />
                            <fieldset class="scheduler-border"
                                style="border: 1px groove #ddd !important; padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
                                <legend
                                    style="width:auto;border-bottom:none;font-size:0.7em;color:#ffc33f;text-align: center;">Sender's details</legend>
                                <tbody>
                                    <tr>
                                        <td colspan="2" style="text-align:left ">
                                            <div
                                                style="font-size:0.7em;border-bottom: 1px solid #d0d0db">
                                                Name: <t t-set="business"
                                                    t-value="doc.sudo().assign_to_business" />
											<t
                                                    t-if="doc.sudo().show_alt_address and doc.sudo().alt_business_name">
                                                    <t t-set="business_name"
                                                        t-value="doc.sudo().alt_business_name" />
                                                </t>
											<t
                                                    t-elif="doc.sudo().show_follower_info_in_waybill and doc.sudo().show_follower_info and doc.sudo().follower_store_name">
                                                    <t t-set="business_name"
                                                        t-value="doc.sudo().follower_store_name" />
                                                </t>
											<t
                                                    t-elif="business.commercial_name">
                                                    <t t-set="business_name"
                                                        t-value="business.commercial_name" />
                                                </t>
											<t
                                                    t-else="else">
                                                    <t t-set="business_name" t-value="business" />
                                                </t>
											<span
                                                    t-esc="business_name" />
                                            </div>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td colspan="2" style="text-align:left">
                                            <t t-set="show_sender_mobile_in_waybill"
                                                t-value="request.env['rb_delivery.client_configuration'].sudo().get_param('show_sender_mobile_in_waybill')" />
                                            <t t-if="show_sender_mobile_in_waybill">
                                                <div style="font-size:0.7em;"> Mobile number: <t
                                                        t-if="doc.sudo().show_alt_address and doc.sudo().alt_mobile_number ">
                                                        <t t-set="mobile_number"
                                                            t-value="doc.sudo().alt_mobile_number" />
                                                    </t>
												<t
                                                        t-elif="doc.sudo().show_follower_info_in_waybill and doc.sudo().show_follower_info and doc.sudo().follower_mobile_number">
                                                        <t t-set="mobile_number"
                                                            t-value="doc.sudo().follower_mobile_number" />
                                                    </t>
												<t
                                                        t-elif="doc.sudo().commercial_number">
                                                        <t t-set="mobile_number"
                                                            t-value="doc.sudo().commercial_number" />
                                                    </t>
												<t
                                                        t-else="else">
                                                        <t t-set="mobile_number"
                                                            t-value="doc.sudo().business_mobile_number" />
                                                    </t>
												<span
                                                        t-esc="mobile_number" />
                                                </div>
                                            </t>
                                        </td>
                                    </tr>
                                </tbody>
                            </fieldset>
                            <fieldset class="scheduler-border"
                                style="border: 1px groove #ddd !important; padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
                                <legend
                                    style="width:auto;border-bottom:none;font-size:0.7em;color:#ffc33f;text-align: center;">Recipient's details</legend>
                                <div class="control-group">
                                    <tbody>
                                        <tr>
                                            <td colspan="2" style="text-align:left ">
                                                <div
                                                    style="font-size:0.7em;border-bottom: 1px solid #d0d0db">
                                                    Name: <span
                                                        t-field="doc.sudo().customer_name" />
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" style="text-align:left">
                                                <div
                                                    style="font-size:0.7em;border-bottom: 1px solid #d0d0db">
                                                    Area : <span
                                                        t-field="doc.sudo().customer_area" />
												<span
                                                        t-if="doc.sudo().customer_sub_area"> / </span>
												<span
                                                        t-if="doc.sudo().customer_sub_area"
                                                        t-field="doc.sudo().customer_sub_area"></span>
                                                </div>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" style="text-align:left">
                                                <div class="o_note_container"
                                                    style="font-size:0.7em;border-bottom: 1px solid #d0d0db;line-height:17px;height:20px;overflow:hidden;font-size: 12px;">
                                                    Address in detail: <span
                                                        t-field="doc.sudo().customer_address" />
                                                </div>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" style="text-align:left">
                                                <div
                                                    style="font-size:0.7em;border-bottom: 1px solid #d0d0db">
                                                    Mobile number: <span
                                                        t-field="doc.sudo().customer_mobile" />
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </div>
                            </fieldset>
                            <fieldset class="scheduler-border"
                                style="border: 1px groove #ddd !important; padding: 0 1em 0.7em 1em !important; margin: 0 0 0 0 !important; -webkit-box-shadow:  0px 0px 0px 0px #000; box-shadow:  0px 0px 0px 0px #000;">
                                <legend
                                    style="width:auto;border-bottom:none;font-size:0.7em;color:#ffc33f;text-align: center;">
                                    Notes</legend>
                                <div class="control-group">
                                    <div class="row">
                                        <t t-if="doc.sudo().follow_up_order">
                                            <div class="col-4">
                                                <div
                                                    style="font-size: 12px;margin-top:3px">
                                                    <t t-if="doc.sudo().note">
                                                    <span t-esc="doc.sudo().note[:85] + '...' if len (doc.sudo().note) > 85 else doc.sudo().note" />
                                                    </t>
                                                </div>
                                            </div>
                                            <div class="col-8">
                                                <div style="font-size: 12px;">
                                                    <img
                                                        style="width:350px !important;height:60px;display:block !important;margin:0 auto !important"
                                                        t-attf-src="data:image/*;base64,{{follow_order.sudo().barcode_follow_up}}" />
                                                    <div style="text-align: center;">
                                                        <span style="font-size:12px; font-weight:bold" t-esc="follow_order.sudo().follow_up_sequence"/> -
                                                        <t t-if="follow_order.sudo().name">
                                                            <span style="font-size:12px; font-weight:bold" t-esc="follow_order.sudo().name[:12] + '...' if len(follow_order.sudo().name)>12 else follow_order.sudo().name"/>
                                                        </t>
                                                        <t t-if="follow_order.sudo().note">
                                                            - <span t-esc="follow_order.sudo().note[:12] + '...' if len(follow_order.sudo().note)>12 else follow_order.sudo().note" />
                                                        </t>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                        <t t-else="">
                                            <div class="col-12">
                                                <div
                                                    style="font-size: 12px;margin-top:3px">
                                                    <t t-if="doc.sudo().note">
                                                        <span t-esc="doc.sudo().note[:250] + '...' if len (doc.sudo().note) > 250 else doc.sudo().note" />
                                                    </t>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </fieldset>
                            <div class="row" style="padding: 12px 15px 0 15px; margin-top:0px">
                                <div class="col"
                                    style="padding-top:8px; font-size:0.7em; border:1px solid white; padding-bottom:8px; background-color: #ffc33f">
                                    <div class="row">
                                        <div class="col-10 align-self-center">
                                            <h6 style="text-align:center">Money collection cost when received</h6>
                                        </div>
                                        <div class="col-2 align-self-center"
                                            style="text-align:center">
                                            <h3 style="font-size:1em; font-weight: bold;">
                                                <t
                                                    t-if="doc.sudo().assign_to_business.inclusive_delivery and not doc.sudo().delivery_cost_on_sender"
                                                    t-set="total"
                                                    t-value="doc.sudo().copy_total_cost" />
                                                <t t-else="else">
                                                    <t
                                                        t-if="doc.sudo().assign_to_business.inclusive_delivery and doc.sudo().delivery_cost_on_sender"
                                                        t-set="total"
                                                        t-value="doc.sudo().copy_total_cost - doc.sudo().delivery_cost" />
                                                    <t t-else="else">
                                                        <t
                                                            t-if="not doc.sudo().assign_to_business.inclusive_delivery and doc.sudo().delivery_cost_on_sender"
                                                            t-set="total"
                                                            t-value="doc.sudo().cost" />
                                                        <t t-else="else" t-set="total"
                                                            t-value="doc.sudo().cost + doc.sudo().delivery_cost" />
                                                    </t>
                                                </t>
                                                <t t-esc="total" />
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="display: block; width: 100%;">
                                <div class="col-12" style="text-align: center;">
                                    <img style="width:350px !important;height:50px;display:block !important;margin:0 auto !important"
                                        t-attf-src="data:image/*;base64,{{doc.sudo().barcode}}" />
                                    <h5 style="font-size:0.7em !important;font-weight:bold;text-align:center;margin:0;">
                                        <span t-field="doc.sudo().sequence" />
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>

            </t>
        </template>

    </data>
</odoo>