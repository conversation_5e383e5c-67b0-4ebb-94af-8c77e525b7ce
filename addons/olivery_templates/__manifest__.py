# -*- coding: utf-8 -*-
{
    'name': "olivery_templates",
    'summary': """
        <PERSON><PERSON> Templates App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.66',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'views/print/order_detail_report_A6.xml',
        'views/print/10x10_waybill.xml',
        'views/print/10x10_money_received.xml',
        'views/print/delivery_waybill_A6.xml',
        'demo/client_conf.xml',
        'views/print/60x40_waybill.xml',
        'views/print/60x40_mini_waybill.xml',
        'views/print/75x100_waybill.xml',
        'views/print/40x55_waybill.xml',
        'views/print/sticker_waybill.xml',
        'views/print/9x9_waybill.xml',
        'views/print/80x150_thermal_report.xml',
        'views/print/thermal_45x75.xml',
        'views/print/2x3_waybill.xml',
        'views/print/10x15_waybill.xml',
        'views/print/6x6_waybill.xml',
        'views/print/thremal_48x279.xml',
        'views/print/order_follow_up_A6.xml'

    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
