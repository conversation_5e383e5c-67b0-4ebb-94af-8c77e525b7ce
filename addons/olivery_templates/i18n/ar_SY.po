# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_templates
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-09 23:03+0000\n"
"PO-Revision-Date: 2024-07-09 23:03+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_olivery_tempaltes_60x40_report_action
msgid "60x40 Barcode"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_olivery_templates_60x40_mini
msgid "60x40 Barcode Mini"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "<br/>\n"
"								Address:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "<br/>\n"
"								Phone:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "<br/>\n"
"                                <span style=\"margin: 0px !important;font-size:12px\"> Phone:</span>"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "<br/>\n"
"                                <span style=\"margin: 0px !important;font-size:12px\"> Ref_id:</span>"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "<br/>Phone:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "<span style=\"margin: 0px !important;font-size:12px\"> Date:</span>"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Address :"
msgstr "العنوان :"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Address in detail:"
msgstr "العنوان بالتفصيل: "

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_templates_40x55_mini_report
msgid "Area"
msgstr "المنطقة"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_tempaltes_60x40_report
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_templates_60x40_mini_report
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Area :"
msgstr "المنطقة:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Area:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
msgid "City"
msgstr "المدينة"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "City"
msgstr "الامارة"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "City:"
msgstr "المنطقة: "

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Collection:"
msgstr "التحصيل:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Customer:"
msgstr "العميل:"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_delivery_waybill_A6_action
msgid "Delivery Waybill A6"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Description:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Drop Off Date:<br/>"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Estimated Delivery Date:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "From :"
msgstr "من :"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "From/Sender:"
msgstr "من / المرسل :"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x10_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_tempaltes_60x40_report
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_templates_60x40_mini_report
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.sticker_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Logo"
msgstr "الشعار"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_tempaltes_60x40_report
msgid "Mobile :"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
msgid "Mobile number"
msgstr "رقم الموبايل"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Mobile number:"
msgstr "رقم المحمول: "

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Mobile:"
msgstr "الجوال:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Money collection cost"
msgstr "مبلغ التحصيل"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Required from business"
msgstr " المبلغ المطلوب من التاجر"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Money collection cost when received"
msgstr "قيمة المبلغ المحصل"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x10_waybill
msgid "Money collection cost:"
msgstr "  مبلغ التحصيل :"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "N/A"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_templates_40x55_mini_report
msgid "Name"
msgstr "الاسم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_tempaltes_60x40_report
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_templates_60x40_mini_report
msgid "Name :"
msgstr "الاسم :"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Name:"
msgstr "الاسم:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Note"
msgstr "ملاحظة"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x10_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Notes"
msgstr "ملاحظات"

#. module: olivery_templates
#: model:ir.model,name:olivery_templates.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Package:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Pick up COD:<br/>\n"
"								Drop off COD:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.olivery_tempaltes_60x40_report
msgid "Price :"
msgstr "السعر :"

#. module: olivery_templates
#: model:ir.model.fields,field_description:olivery_templates.field_rb_delivery_order__qr_code
msgid "QR code"
msgstr "رمز QR"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "Recipient"
msgstr "المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "Recipient's address"
msgstr "عنوان المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x10_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Recipient's details"
msgstr "تفاصيل المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Reference #:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "Reference id:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.45x75_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.80x150_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "Sender"
msgstr "المرسل"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x10_waybill
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_detail_a6
#: model_terms:ir.ui.view,arch_db:olivery_templates.order_follow_up_a6
msgid "Sender's details"
msgstr "تفاصيل التاجر"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Service Type:<br/>\n"
"								 Delivery"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.75x100_waybill
msgid "To :"
msgstr "الى : "


#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_olivery_templates_sticker_waybill
msgid "Sticker"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_45x75_action_new
msgid "Thermal Report 45x75"
msgstr "بوليصة حرارية 45x75"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_48x279_action_new
msgid "Thermal Report 48x279"
msgstr "بوليصة حرارية 48x279"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "To/Recipient:"
msgstr ""

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Total COD Amount:"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_75x100_action
msgid "Waybill 7.5x10"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_a6_action
msgid "Waybill A6"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_2x3_action_new
msgid "Waybill 2X3"
msgstr "بوليصة 2X3"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "Weight:"
msgstr ""

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_collection_detail_10x10_action
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_collection_detail_10x10_action_new
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_10x10_action
msgid "Waybill 10x10"
msgstr "بوليصة  10x10"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.9x9_waybill
msgid "<br/>\n"
"                    <span style=\"font-size: 15px;\">Merchant Name: </span>"
msgstr "<br/>\n"
"                    <span style=\"font-size: 15px;\">التاجر: </span>"


#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.9x9_waybill
msgid "<br/>\n"
"                    <span style=\"font-size: 15px;\">Package content: </span>"
msgstr "<br/>\n"
"                    <span style=\"font-size: 15px;\">محتوى الطرد: </span>"


#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.delivery_waybill_A6
msgid "<br/>Address:"
msgstr "<br/>العنوان:"



#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.9x9_waybill
msgid "<span style=\"font-size: 15px;\">Creation date: </span>"
msgstr "<span style=\"font-size: 15px;\">تاريخ الإنشاء: </span>"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_80x150_action_new
msgid "Thermal Report 80x150"
msgstr "بوليصة حرارية 80x150"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "<span>Print Date: </span>"
msgstr "<span>تاريخ الطباعة: </span>"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Recipient Address"
msgstr "عنوان المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Recipient Mobile"
msgstr "رقم المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Recipient Name"
msgstr "اسم المستلم"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Sender Mobile"
msgstr "رقم التاجر"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.10x15_waybill
msgid "Sender Name"
msgstr "اسم التاجر"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_detail_10x15_action
msgid "Waybill 10x15"
msgstr "وثيقة الشحن 10x15"

#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.6x6_waybill
msgid "Receiver Name:"
msgstr "اسم الزبون:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.6x6_waybill
msgid "Receiver Address:"
msgstr "عنوان الزبون:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.6x6_waybill
msgid "Receiver Number:"
msgstr "رقم الزبون:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.6x6_waybill
msgid "Money Collection:"
msgstr "مجموع التحصيل:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.6x6_waybill
msgid "Notes:"
msgstr "الملاحظات:"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "Note: The company is not responsible for the contents of the order."
msgstr "ملاحظة : الشركة غير مسؤولة عن محتويات الطلب"

#. module: olivery_templates
#: model_terms:ir.ui.view,arch_db:olivery_templates.48x279_waybill
msgid "The company is not specialized in transporting parcels, documents and postal items because they are the responsibility of the Postal Authority."
msgstr "الشركة غير مختصة في نقل الطرود والوثائق والبعائث البريدية لانها من اختصاص هيئة البريد"


#. module: olivery_templates
#: model:ir.actions.report,name:olivery_templates.report_rb_delivery_order_follow_up_a6_action
msgid "Follow Up A6"
msgstr "توابع A6"