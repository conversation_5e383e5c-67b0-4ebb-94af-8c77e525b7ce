# -*- coding: utf-8 -*-
import logging

from odoo import api, fields, models, _
from xmlrpc import client
from odoo.tools.misc import DEFAULT_SERVER_DATETIME_FORMAT, DEFAULT_SERVER_DATE_FORMAT
from openerp.exceptions import except_orm,ValidationError,UserError
import datetime
from datetime import timedelta
import requests
import json
from dateutil.relativedelta import relativedelta
import re
import sys
import secrets
import string
import random
from datetime import datetime
from odoo.http import request
import base64

_logger = logging.getLogger(__name__)

rb_delivery_user_model ='rb_delivery.user'
res_users_model = 'res.users'

SELECTION_VALUES = [(str(i), str(i)) for i in range(1, 13)]

class olivery_customers(models.Model):
    
    @api.model
    def _get_default_currency(self):
        return self.env.ref('base.USD')
    
    sub_arr = []

    _inherit = 'res.partner'

    olivery_website_url = fields.Char('Website Olivery URL', track_visibility="onchange")

    website_db = fields.Char('Website Olivery DB', track_visibility="onchange")

    customer_code = fields.Char('Customer code',default='/', readOnly=True, track_visibility="onchange")

    is_white_label = fields.Boolean('Is White Label App ?', default=False, track_visibility="onchange")

    is_olivery_customer = fields.Boolean('Is Olivery Customer ?', default=True, track_visibility="onchange")

    is_onboarding = fields.Boolean('Is Onboarding ?', default=False, track_visibility="onchange")

    is_subscribed = fields.Boolean('Is Subscribed ?', default=False, track_visibility="onchange")

    subscription_id = fields.Many2one('olivery_master.subscription_pricing', string='Subscription', track_visibility="onchange")

    subscription_start_date = fields.Date('Subscription Start Date',track_visibility="on_change")
    
    enable_true_buyer = fields.Boolean('Enable True Buyer ?', default=False, track_visibility="onchange")
    
    last_fetch_orders_date = fields.Char('Last Fetch Orders Date',track_visibility="on_change")

    next_payment_date=fields.Date('Next Payment Date',track_visibility="on_change")

    prev_payment_date=fields.Date('previous Payment Date',track_visibility="on_change",readonly=1)

    payments=fields.One2many("olivery_master.payments" ,inverse_name="client",string="payments", track_visibility="onchange")

    next_payment_value = fields.Float('Next Payment Value', track_visibility="onchange")

    notes = fields.Text('Notes', track_visibility="onchange")

    collector_name=fields.Char('Collector Name', track_visibility="onchange")

    have_special_offer=fields.Boolean('Have special Offer', default=False, track_visibility="onchange")

    payment_method=fields.Char('Payment method', track_visibility="onchange")

    payment_type=fields.Char('Payment type', track_visibility="onchange")

    offer=fields.Char('Offer', track_visibility="onchange") 

    offer_Description=fields.Char('Offer Description', track_visibility="onchange") 

    offer_number_of_orders= fields.Char('Offer number of orders', default=1000, track_visibility="onchange")

    financial_claim_message_days= fields.Integer('Claim Message Days', default=15, track_visibility="onchange")

    financial_cut_message_days= fields.Integer('Cut Message Days', default=2, track_visibility="onchange")

    financial_cut_message_hours = fields.Integer(compute='_compute_number_of_hours', string='Number of Hours', track_visibility="onchange")

    claim_message_default = fields.Text('Claim Message', compute='_compute_claim_message_default', track_visibility="onchange")
    
    claim_message = fields.Text('Whatsapp Claim Message', track_visibility="onchange")

    cut_message = fields.Text('Cut Message' ,compute='_compute_cut_message_default', track_visibility="onchange")

    financial_owner=fields.Char('Owner', track_visibility="onchange")

    financial_owner_mobile=fields.Char('Owner Mobile', track_visibility="onchange")

    second_financial_owner=fields.Char('Second Owner', track_visibility="onchange")

    second_owner_mobile=fields.Char('Second Owner Mobile', track_visibility="onchange")

    financial_payment_id = fields.Many2one('olivery_master.financial_recipient', string='Financial Payment', track_visibility="onchange")

    initial_payment_value =fields.Char('Initial Payment Value', track_visibility="onchange")

    support_username=fields.Char('Support Username ')
    
    support_passwd=fields.Char('Support Password')

    admin_username=fields.Char('Admin Username ')

    admin_passwd=fields.Char('Admin Password ')

    currency_id = fields.Many2one('res.currency', string='Currency', track_visibility="on_change",default=_get_default_currency,compute='_compute_subscription_data', store=True)

    module_ids = fields.Many2many(comodel_name='olivery_master.module', 
                                          relation="modules_client",
                                          column1="module_to_client_col", 
                                          column2="client_to_module_col",
                                          string='Modules', track_visibility="onchange")
    
    module_version_ids = fields.Many2many(comodel_name='olivery_master.module_version', 
                                          relation="module_versions_client",
                                          column1="module_verion_to_client_col", 
                                          column2="client_to_module_verion_col",
                                          string='Module Version', track_visibility="onchange")
    
    customer_monitor_ids = fields.One2many('olivery_master.customer_monitor', 'client_id', string='Customer Monitor', track_visibility="onchange")

    aurora_location = fields.Char('Aurora Location', track_visibility="onchange")

    cluster_location = fields.Char('Cluster Location', track_visibility="onchange")

    enable_archive_user = fields.Boolean('Enable "Archive Olivery Users" Job', default=True, track_visibility="onchange")

    enable_reset_support_pwd = fields.Boolean('Enable "Reset Support Password" Job', default=True, track_visibility="onchange")

    enable_reset_admin_pwd = fields.Boolean('Enable "Reset Admin Password" Job', default=True, track_visibility="onchange")

    enable_monitor_info = fields.Boolean('Enable "Customers Monitoring" Job', default=True, track_visibility="onchange")

    enable_module_version = fields.Boolean('Enable "Customers Modules Versions" Job', default=True, track_visibility="onchange")

    enable_job_queue_monitor = fields.Boolean('Enable "Monitor job queue" Job', default=True, track_visibility="onchange")
    
    enable_request_support = fields.Boolean('Enable Request Support', default=True, track_visibility="onchange")
     
    enable_billing = fields.Boolean('Enable Billing', default=False, track_visibility="onchange")

    support_access_duration = fields.Integer('Support Aceess Duration(Hours)', track_visibility="onchange", default=1)

    billing_message = fields.Char("Billing Message")

    billing_timer = fields.Integer("Billing Timer")

    subscription_module_id = fields.One2many('olivery_master.module_subscription', inverse_name='client_id', string='Subscription Module', track_visibility="onchange")
    
    icon = fields.Binary("Icon (1024px * 1024px)", attachment=True, help="This field holds the icon for mobile app.", track_visibility="onchange")
    
    splash = fields.Binary("Splash (2732px * 2732px)", attachment=True, help="This field holds the splash for mobile app.", track_visibility="onchange")
    
    login_img = fields.Binary("Login (1024px * 1024px)", attachment=True, help="This field holds the login 1024 X 1024 for mobile app.", track_visibility="onchange")
    
    mobile_app_name = fields.Char("Mobile App Name", track_visibility="onchange")

    is_latest = fields.Boolean(compute='_compute_is_latest', store=True)

    health_check_logs = fields.One2many('olivery_master.health_check_logs', inverse_name='client_id', string='Health check system log', track_visibility="onchange")
    
    is_all_jobs_set = fields.Boolean("Is all jobs set?", default=False)

    is_demo = fields.Boolean("Is Demo?", default=False)

    is_assigned_multiple_times = fields.Boolean(string="Assigned Multiple Times", default=False)

    is_ondemand = fields.Boolean('Is on_demand ?')

    license_key = fields.Text('License Key for on_demand')

    product_id = fields.Many2one('olivery_master.products', "Product")

    gif_splash = fields.Binary("GIF Splash", attachment=True, help="This field holds the gif splash screen for mobile app.", track_visibility="onchange")
    
    login_banner = fields.Binary("Login Banner (470.1px * 722.42px)")

    ai_quota = fields.Integer("Ai quota")

    billing_active = fields.Boolean(string="Billing Active", default=False)

    active_billing_count = fields.Integer(string="Active Billing Count", default=0)

    max_billing_limit = fields.Integer(string="Maximum Billing Limit", default=10)

    send_claim_message_on_whatsapp = fields.Binary("Send Claim Message On Whatsapp", default=False)
    
    claim_counter = fields.Integer(string="Claim Counter", default=0)

    has_open_invoices = fields.Boolean(string='Has Open Invoices')

    subscription_period = fields.Selection(
        string='Subscription Period',
        selection=SELECTION_VALUES,
        default='1'
    ,track_visibility="onchange", compute='_compute_subscription_data', store=True)

    subscription_per_month = fields.Float("Monthly Subscription", compute='_compute_subscription_per_month', store=True)
    
    
    def action_update_has_open_invoices(self):
        open_invoice_partners = self.env['res.partner'].search([
            ('invoice_ids.state', '=', 'open')
        ])
        
        if open_invoice_partners:
            open_invoice_partners.write({'has_open_invoices': True})
        
        other_partners = self.env['res.partner'].search([
            ('id', 'not in', open_invoice_partners.ids)
        ])
        if other_partners:
            other_partners.write({'has_open_invoices': False})
    
    def action_update_monthly_subscription(self):
        for record in self:
            if record.subscription_id:
                price = record.subscription_id.price
                period = record.subscription_id.subscription_period
                period_value = int(period) if period and period.isdigit() else 1

                if period_value > 0 and price is not None:
                    record.subscription_per_month = price / period_value
                else:
                    record.subscription_per_month = 0
                
                record.currency_id = record.subscription_id.currency_id
                record.subscription_period = record.subscription_id.subscription_period
            else:
                record.subscription_per_month = 0
                record.currency_id = False
                record.subscription_period = False  
                
    def check_if_exist_in_cron_job(self):
        _logger.info("Starting computation of job status for partners...")

        # Fetch all primary health check functions
        primary_functions = self.env['olivery_master.health_check_functions'].search([('is_primary', '=', True)])
        if not primary_functions:
            _logger.error("No primary functions (cron jobs) found!")
            raise UserError('No primary functions (cron jobs) found.')

        _logger.info(f"Found {len(primary_functions)} primary functions: {primary_functions.ids}")

        # Fetch all jobs associated with primary cron jobs
        jobs_with_primary_crons = self.env['olivery_master.health_check_jobs'].search([('cron_job', 'in', primary_functions.ids)])
        _logger.info(f"Found {len(jobs_with_primary_crons)} jobs with primary cron jobs.")

        # Initialize dictionaries to track assignments
        client_to_job_counts = {}

        for job in jobs_with_primary_crons:
            for client in job.affected_clients:
                if client.id not in client_to_job_counts:
                    client_to_job_counts[client.id] = {}
                if job.cron_job.id not in client_to_job_counts[client.id]:
                    client_to_job_counts[client.id][job.cron_job.id] = 0
                client_to_job_counts[client.id][job.cron_job.id] += 1

        _logger.info(f"Client-to-job mapping: {client_to_job_counts}")

        # Prepare primary function IDs set
        primary_function_ids = set(primary_functions.ids)

        # Separate partners into bulk-write groups
        partners_to_set_true = []
        partners_to_set_false = []
        partners_with_duplicates = []
        partners_without_duplicates = []

        for partner in self:
            partner_jobs = client_to_job_counts.get(partner.id, {})
            # Check if the client is assigned to all primary jobs
            is_all_jobs_set = primary_function_ids.issubset(partner_jobs.keys())
            # Check if any primary job is assigned more than once
            has_duplicates = any(count > 1 for count in partner_jobs.values())

            if is_all_jobs_set:
                partners_to_set_true.append(partner.id)
            else:
                partners_to_set_false.append(partner.id)

            if has_duplicates:
                partners_with_duplicates.append(partner.id)
            else:
                partners_without_duplicates.append(partner.id)

        # Bulk write operations
        if partners_to_set_true:
            self.browse(partners_to_set_true).write({'is_all_jobs_set': True})
            _logger.info(f"Updated {len(partners_to_set_true)} partners with is_all_jobs_set=True")
        if partners_to_set_false:
            self.browse(partners_to_set_false).write({'is_all_jobs_set': False})
            _logger.info(f"Updated {len(partners_to_set_false)} partners with is_all_jobs_set=False")
        if partners_with_duplicates:
            self.browse(partners_with_duplicates).write({'is_assigned_multiple_times': True})
            _logger.info(f"Updated {len(partners_with_duplicates)} partners with duplicate job assignments")
        if partners_without_duplicates:
            self.browse(partners_without_duplicates).write({'is_assigned_multiple_times': False})
            _logger.info(f"Updated {len(partners_without_duplicates)} partners with no duplicate job assignments")

    def paid_billing_action(self):
        # if not self.billing_active:
        #     raise UserError("Billing is not active")
        if self.active_billing_count > 0:
            self.active_billing_count = 0
        context = {'activate': False, 'username': self.env.user.name, 'note': "paid", 'paid': True}
        self.billing_action(context)
            
    def send_claim_message(self, customer):
        if not customer.financial_owner_mobile:
            raise UserError("Please add the owner's mobile number.")

        api_conf = request.env['olivery_master.billing_whatsapp_conf'].sudo().search([], limit=1)
        client_id = api_conf.client_id
        token = api_conf.api_token
        if not client_id or not token:
            raise UserError('API credentials are not set properly.')
        
        url = "https://gate.whapi.cloud/messages/text"
        headers = {
            "accept": 'application/json',
            "Content-Type": 'application/json',
            "authorization": "Bearer " + token
        }

        data =  {
            "to": f'{customer.financial_owner_mobile}',
            "body": f'{customer.claim_message}',
            "view_once": False
        }

        req = requests.post(url, headers=headers, json=data)
        response_data = req.json()

        if req.status_code == 200:
            message_id = response_data.get('messageId', 'No ID')
            return {'status': 'Message sent successfully', 'message_id': message_id}
        else:
            error_message = response_data.get('error', {}).get('message', 'Unknown error')
            return {'error': error_message}
        
    def send_claim_message_with_file(self, customer):
        if not customer.financial_owner_mobile:
            return {'error': "Please add the owner's mobile number."}
        try:
            api_conf = request.env['olivery_master.billing_whatsapp_conf'].sudo().search([], limit=1)
            client_id = api_conf.client_id
            token = api_conf.api_token
            if not client_id or not token:
                return {'error': 'API credentials are not set properly.'}

            url = "https://api.apichat.io/v1/sendFile"
            headers = {
                "client-id": client_id,
                "token": token
            }

            sent_message = f'{customer.claim_message_default}'
            attachment_id = self.env['ir.attachment'].create({
                'name': "invoice.pdf",
                'type': 'binary',
                'datas': base64.encodestring(customer.send_claim_message_on_whatsapp),
                'res_model': 'rb_delivery.multi_print_orders_money_collector',
                'res_id': customer.id,
                'datas_fname':"invoice.pdf",
                'public':True
            })

            attachment_url = 'http://localhost'+'/web/image/'+str(attachment_id.id)

            data = {
                "number": f'{customer.financial_owner_mobile}',
                "url": attachment_url,
                "caption": sent_message,
                "filename": "invoice.pdf"
            }

            req = requests.post(url, headers=headers, json=data)

            if req.status_code != 200:
                return {'error': f'Failed to send message. Status code: {req.status_code}, Response: {req.text}'}

            try:
                response_data = req.json()
            except ValueError:
                return {'error': f'Invalid JSON response. Raw response: {req.text}'}

            message_id = response_data.get('messageId', 'No ID')
            return {'status': 'Message sent successfully', 'message_id': message_id}
        except Exception as e:
            return {'error': str(e)}

    def prepare_claim_message(self, customer):
        billing_configuration = self.env['olivery_master.billing_configuration'].search([], limit=1)

        if not billing_configuration:
            customer.claim_message = "Billing configuration not found."
            return

        template = billing_configuration.claim_message_placeholder
        invoices = self.env['account.invoice'].search([('partner_id', '=', customer.id), ('state', '=', 'open')])
        
        if not invoices:
            customer.claim_message = "No invoice found for this customer."
            return

        invoice_details = ""
        total_amount_due = 0.0
        currency = invoices[0].currency_id.symbol if invoices else ""

        for invoice in invoices:
            # Skip fully paid invoices
            if invoice.residual <= 0:
                continue

            # Get start date
            start_date = invoice.date_invoice if invoice.date_invoice else False
            end_date = invoice.date_due if invoice.date_due else False

            # Calculate due date as start_date + 15 days
            if start_date:
                new_due_date = start_date + timedelta(days=14)
                new_due_date_str = new_due_date.strftime('%Y-%m-%d')
            else:
                new_due_date_str = "غير محددة"

            period = f"من {start_date.strftime('%Y-%m-%d') if start_date else 'غير محددة'} إلى {end_date.strftime('%Y-%m-%d') if end_date else 'غير محددة'}"

            remaining_amount = invoice.residual

            invoice_details += f"\n- الفاتورة عن الفترة **{period}** بقيمة **{remaining_amount} {currency}**، تاريخ الاستحقاق: **{new_due_date_str}**"
            total_amount_due += remaining_amount

        if total_amount_due == 0:
            customer.claim_message = "No outstanding balance for this customer."
            return

        message = template.format(
            customer_name=customer.name,
            total_amount=f"{total_amount_due} {currency}",
            invoice_details=invoice_details
        )

        customer.claim_message = message

    def auto_prepare_claim_messages(self):
        """Cron job: Prepares claim messages and stores them in claim.message.log"""
        partners = self.env['res.partner'].search([('customer', '=', True),('is_demo', '=', False)])
        
        for partner in partners:
            self.prepare_claim_message(partner)
            
            if partner.claim_message:
                self.env['olivery_master.claim_message_log'].create({
                    'partner_id': partner.id,
                    'claim_message': partner.claim_message,
                    'state': 'pending'
                })

    # def prepare_claim_message(self, customer):
    #     billing_configuration = self.env['olivery_master.billing_configuration'].search([], limit=1)
            
    #     if not billing_configuration:
    #         customer.claim_message = "Billing configuration not found."
            
    #     template = billing_configuration.claim_message_placeholder

    #     invoices = self.env['account.invoice'].search([('partner_id', '=', customer.id),('state','=','open')])

    #     if not invoices:
    #         customer.claim_message = "No invoice found for this customer."

    #     placeholders = re.findall(r'\{(\w+)\}', template)

    #     all_invoice_messages = ['']
    #     for invoice in invoices:

    #         invoice_data = {}
                        
                
    #         for field_name in placeholders:
    #             if hasattr(invoice, field_name):
    #                 value = getattr(invoice, field_name)
    #                 if isinstance(invoice._fields[field_name], fields.Date):
    #                     if value:
    #                         try:
    #                             new_date = value + timedelta(days=14)
    #                             value = new_date.strftime('%Y-%m-%d')
    #                         except ValueError:
    #                             value = ''
    #                     else:
    #                         value = ''
    #                 elif isinstance(invoice._fields[field_name], fields.Datetime):
    #                     if value:
    #                         try:
    #                             new_date = value + timedelta(days=14)
    #                             value = new_date.strftime('%Y-%m-%d')
    #                         except ValueError:
    #                             value = ''
    #                 elif isinstance(value, float):
    #                     value = "{:.2f}".format(value)
    #                 else:
    #                     value = str(value) if value else ''
    #                 invoice_data[field_name] = value
    #             else:
    #                 invoice_data[field_name] = ''
    #         try:
    #             claim_message = template.format(**invoice_data)
    #         except KeyError as e:
    #             claim_message = f"Missing data for placeholder: {e.args[0]}"

    #         all_invoice_messages.append(claim_message)

    # Combine all invoice messages into one
        # customer.claim_message = "\n\n".join(all_invoice_messages)


    def claim_action(self, context, customer=None):
        if context.get('username'):
            username = context['username']
        else:
            username = self.env.user.name

        customer = customer or self
        if not customer:
            raise UserError("No customer specified.")

        self.prepare_claim_message(customer)

        return {
            'type': 'ir.actions.act_window',
            'name': 'Send Claim Message',
            'res_model': 'olivery_master.claim_wizard',
            'view_mode': 'form',
            'view_id': self.env.ref('olivery_master.view_claim_wizard_form').id,
            'target': 'new',
            'context': {
                'default_client_id': customer.id
            },
        }

        
    def billing_action(self, context, customer=None):
        activate = context['activate']
        note = context['note'] if 'note' in context else None
        paid = context['paid'] if 'paid' in context else None
        username_log =''
        if context.get('username'):
            username_log = context['username']
        else:
            username_log = self.env.user.name
        customer = customer or self

        # if not customer.enable_billing:
        #     raise UserError(f"Contact malak please ")

        if customer.name and customer.website_db and customer.support_username and customer.support_passwd and customer.olivery_website_url:
            customer_name = customer.name
            db_name = customer.website_db
            username = customer.support_username
            password = customer.support_passwd
            customer_url = customer.olivery_website_url
                    
            uid, url, db, common, pwd, models = self.authenticate_client_server(customer_url, db_name, username, password)
                        
            if uid:
                domain = [] 
                rubik_billing_model = 'rb_delivery.billing_setting'
                res_partner_model = 'res.partner'
                bill_ids = models.execute_kw(db, uid, pwd, rubik_billing_model, 'search', [domain])
                new_values = {'activate': activate, 'activate_for_all': activate, 'billing_timer': 30, 'billing_message': "Dear Customer , you have  Due date bill please contact our financial support ... عزيزي العميل ، لديك فاتورة مستحقة  الرجاء التواصل مع الدعم الفني الرجاء السداد باقل من ٤٨ ساعة لضمان استمرارية الخدمة " }

                if activate:

                    if bill_ids:
                        models.execute_kw(db, uid, pwd, rubik_billing_model, 'write', [bill_ids, new_values])
                        customer.active_billing_count += 1 
                        self.write({'billing_active': True, 'active_billing_count': customer.active_billing_count})
                        message = f'The billing is now active for {customer_name}'
                    else:
                        models.execute_kw(db, uid, pwd, rubik_billing_model, 'create', [{'name': 'is invoice', **new_values}])
                        customer.active_billing_count += 1 
                        self.write({'billing_active': True, 'active_billing_count': customer.active_billing_count})
                        message = f'Billing created and activated successfully for {customer_name}'   

                else:
                    if customer.active_billing_count < customer.max_billing_limit :
                        pass
                    else:
                        raise UserError(f"Deactivation failed! The customer '{customer.name}' has reached the maximum number of active billing records ({customer.max_billing_limit}). Please deactivate an existing billing or increase the limit to activate more.")
                    
                    if bill_ids:
                        models.execute_kw(db, uid, pwd, rubik_billing_model, 'write', [bill_ids, new_values])
                        self.write({'billing_active': False})
                        message = f'The billing has been deactivated for {customer_name}'
                    else:
                        models.execute_kw(db, uid, pwd, rubik_billing_model, 'create', [{'name': 'is invoice', **new_values}])
                        self.write({'billing_active': False})
                        message = f'Billing created but not activated for {customer_name}'
                    
                    client_billing_table = self.env['olivery_master.billing_table'].search([("client_id","=",customer.id)])
                    next_execute_date = datetime.now() + timedelta(days=2)
                    if paid:
                        client_billing_table.write({'active': False})
                    else:
                        if not client_billing_table:
                            self.env['olivery_master.billing_table'].create({"client_id": customer.id, "next_execute_date" : next_execute_date})
                        else: 
                            client_billing_table.execute_date = client_billing_table.next_execute_date
                            client_billing_table.next_execute_date = next_execute_date
                            client_billing_table.active = True

                data ={
                    "description":message,
                    "customer": customer,
                    "activate": activate,
                    "username": username_log,
                    "note": note
                }

                self.create_logs(data)
                self.env.cr.commit()
                return message
                # raise UserError(_(message))
            else:
                raise UserError(f"Wrong authentication")
        else:
            raise UserError(f"Wrong Customer Data")
        
    def create_logs(self, data):
        description = data["description"]
        activate = data["activate"]
        customer = data["customer"].id
        username= data["username"]
        note= data["note"]
        self.env['olivery_master.billing_logs'].create({
                        'activate': activate,
                        'description': description,
                        'client_id': customer,
                        'username':username,
                        'note':note
                        })
                    
    @api.depends('module_ids','module_version_ids', 'is_olivery_customer')
    def _compute_is_latest(self):
        for record in self:
            # modules = self.env['olivery_master.module'].search(['|',('clients_ids_more','in',record.id),('clients_ids_less','in',record.id)])
            record.is_latest = True if len(record.module_ids.ids) == 0 and record.is_olivery_customer else False
            # record.is_latest = True if len(modules.ids) == 0 else False
    
    @api.model
    def create(self, vals):
        if vals.get('customer_code', False) == '/':
            vals['customer_code'] = self.env['ir.sequence'].next_by_code('res.partner')
        existing_client = self.search([('name', '=', vals.get('name'))])

        if existing_client:
            # Raise a warning if the client already exists
            raise UserError("Client with the same name already exists in the master!")


        return super(olivery_customers, self).create(vals)

    
    
    # def authenticate(self, url, db, user, pwd):
    #     try:
    #         common = client.ServerProxy('{}/xmlrpc/2/common'.format(url), allow_none=1)
    #         uid = common.authenticate(db, user, pwd, {})
    #         models = client.ServerProxy('{}/xmlrpc/2/object'.format(url))
    #         print (uid)
    #         if uid == 0:
    #             raise Exception('Credentials are wrong for remote system access: %s',db)
    #         else:
    #             message = 'Connection Stablished Successfully'
    #     except Exception as e:
    #         raise except_orm(_('Remote system access Issue \n '), _(e))

    #     print('******message*****',message)
        
    #     return uid, url, db, common, pwd, models
    
    def authenticate_client_server(self, url, db, user, pwd):
        try:
            common = client.ServerProxy('{}/xmlrpc/2/common'.format(url), allow_none=1)
            uid = common.authenticate(db, user, pwd, {})
            models = client.ServerProxy('{}/xmlrpc/2/object'.format(url))
            print (uid)
            
            if uid == 0 :
                return False,url, db, False, pwd, False
            else:
                message = 'Connection Stablished Successfully'
        except Exception as e:
            return False,url, db, False, pwd, False
        
        return uid, url, db, common, pwd, models

            
    def send_finance_data(self):
        message = ""
        for client in self.sub_arr:
            message += client['url']+" subscription was "+str(client['old_sub'])+", it should be "+str(client['new_sub']) + ".\n"

        title = ("customer subscriptions")
        slack_data = {
            "username": "NotificationBot",
            "icon_emoji": ":satellite:",
            #"channel" : "#somerandomcahnnel",
            "attachments": [
                {
                    "color": '#0099ff',
                    "fields": [
                        {
                            "title": title,
                            "value": message,
                            "short": "false",
                        }
                    ]
                }
            ]
        }
        byte_length = str(sys.getsizeof(slack_data))
        headers = {'Content-Type': "application/json", 'Content-Length': byte_length}
        response = requests.post("*********************************************************************************", data=json.dumps(slack_data), headers=headers)
        if response.status_code != 200:
            raise Exception(response.status_code, response.text)
        
    @api.multi
    def send_whatsapp_message(self):
        if not self.financial_owner_mobile:
            raise ValidationError(_("Please Add Owner Mobiler Number"))
         
        context = self._context
        variable = context.get('variable')
        if variable=='cut_message':
            message = self.cut_message
        else:
            message=self.claim_message_default

    
        if message:
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
            message = self.get_message(message,variables)
            if self.financial_owner_mobile: 
                mobile_number =self.financial_owner_mobile
            return {
                'name' : 'Go to website',
                'res_model': 'ir.actions.act_url',
                'type' : 'ir.actions.act_url',
                'target' : 'new',
                'url' : "https://wa.me/"+mobile_number+"?text="+message}
        else:
            raise ValidationError(_("Please add a whatsapp for "+variable))
           

    def get_message(self,message,variables):
        for variable in variables:
            var = variable.strip('{}')
            variable_value = var.split('.')
            field_name = ''
            field_related= ''
            if variable_value and len(variable_value)==2:
                field_name = variable_value[0]
                field_related = variable_value[1]
            if field_name and isinstance(self[field_name],object):
                if self[field_name]:
                    message = message.replace(variable, str(self[field_name][field_related])) 
                else:
                    message = message.replace(variable,'')     
            else:
                message = message.replace(variable, str(self[var]))
        return message.replace('\n', '%0a')         
    
    @api.depends('financial_cut_message_days')
    def _compute_number_of_hours(self):
        for record in self:
            record.financial_cut_message_hours = record.financial_cut_message_days * 24

    def request_support_access(self, customer=None):
        customer =  self

        if customer.name and customer.website_db and customer.support_username and customer.support_passwd and customer.olivery_website_url:

            customer_name = customer.name
            db_name = customer.website_db
            username = customer.support_username
            password = customer.support_passwd
            customer_url = customer.olivery_website_url
            
            uid, url, db, common, pwd, models = self.authenticate_client_server(customer_url,db_name,username,password)
            
                
            if uid:
                result = self.create_support_user(uid, url, db, common, pwd, models, customer)
                message= result['message']
                support_rec = result['support_rec']

                popup = self.env['olivery_master.popup'].create({
                    'message': message,
                    })
                return {
                    'name': 'New Support User',
                    'type': 'ir.actions.act_window',
                    'res_model': 'olivery_master.popup',
                    'view_mode': 'form',
                    'view_id': self.env.ref('olivery_master.view_form_olivery_master_popup').id,
                    'res_id': popup.id,
                    'target': 'new','context': {
                        'default_description': False,
                        'support_rec': support_rec
                        },
                    }
            else:
                raise ValidationError(_( "Unable to access the database '%s'. This could be due to one of the following reasons:\n"
                                        "- The database name is incorrect.\n"
                                        "- The credentials (username/password) are invalid.\n"
                                        "- The client server at '%s' is currently unreachable.\n\n"
                                        "Please verify the details and try again, or contact your administrator for further assistance."
                                        % (customer.website_db, customer.olivery_website_url)
                                        )
                                        )
                # raise ValidationError(_(f"Can't access {customer.website_db}, please contact your administrator"))
            
    def enable_olivery_users(self, customer=None):
        customer =  self

        if customer.name and customer.website_db and customer.support_username and customer.support_passwd and customer.olivery_website_url:

            customer_name = customer.name
            db_name = customer.website_db
            username = customer.support_username
            password = customer.support_passwd
            customer_url = customer.olivery_website_url
            
            uid, url, db, common, pwd, models = self.authenticate_client_server(customer_url,db_name,username,password)
                
            if uid:
                result = self.activate_olivery_users(uid, url, db, common, pwd, models, customer)
                message= result['message']
                support_rec = result['support_rec']

                popup = self.env['olivery_master.popup'].create({
                    'description': _("activate olivery Users"),
                    'message': message,
                    })
                return {
                    'name': 'New Support User',
                    'type': 'ir.actions.act_window',
                    'res_model': 'olivery_master.popup',
                    'view_mode': 'form',
                    'view_id': self.env.ref('olivery_master.view_form_olivery_master_popup').id,
                    'res_id': popup.id,
                    'target': 'new','context': {
                        'default_description': False,
                        'support_rec': support_rec
                        },
                    }
            else:
                raise ValidationError(_( "Unable to access the database '%s'. This could be due to one of the following reasons:\n"
                                        "- The database name is incorrect.\n"
                                        "- The credentials (username/password) are invalid.\n"
                                        "- The client server at '%s' is currently unreachable.\n\n"
                                        "Please verify the details and try again, or contact your administrator for further assistance."
                                        % (customer.website_db, customer.olivery_website_url)
                                        )
                                        )
                # raise ValidationError(_(f"Can't access {customer.website_db}, please contact your administrator"))

    def generate_password(self, length=12):
        alphabet = string.ascii_letters + string.digits + string.punctuation
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password
    
    def create_support_user(self, uid, url, db, common, pwd, models, customer):

        current_user = self.env.user

        name = f"{current_user.name} (Olivery Support)",
        username = name[0]#'malak'#
        login = current_user.login#'<EMAIL>'#
        time_zone=current_user.tz
        if current_user.has_group('base.group_erp_manager'):
            raise ValidationError(_(f"You are admin, can't generate support user, you can use: \n\nUsername: {self.support_username}\nPassword: {self.support_passwd}"))
            return

        secure_password = self.generate_password(length=12)

        user_id = models.execute_kw(db, uid, pwd, 'res.users', 'search', [[('login', '=', login), ('active', 'in', [True, False])]])
        groups = models.execute_kw(db, uid, pwd, 'res.users', 'read', [[uid]], {'fields': ['groups_id']})
        group_ids = groups[0]['groups_id']
        if user_id:
            # partner_id = models.execute_kw(db, uid, pwd, 'res.users', 'read', [[user_id]], {'fields': ['partner_id']})
            # result = models.execute_kw(db, uid, pwd, 'res.partner', 'write', [partner_id, {'email': login}])
            values = {
                'name': username,
                'active': True,
                'password': secure_password,
                'tz':time_zone
            }
            result = models.execute_kw(db, uid, pwd, 'res.users', 'write', [user_id, values])
        else:
            partner_values = {
                'name': username,
                'email': login
                }
            partner_id = models.execute_kw(db, uid, pwd, 'res.partner', 'create', [partner_values])

            values= {
                'name': username,
                'login': login,
                'password': secure_password,
                'groups_id': [(6, 0, group_ids)],
                'lang': 'en_US',
                'partner_id': partner_id,
                'tz': time_zone,
                }
            
            result = models.execute_kw(db, uid, pwd, 'res.users', 'create', [values])
        if result:
            hours = customer.support_access_duration
            end_date = fields.Datetime.to_string(fields.Datetime.now() + timedelta(hours=hours))
            support_user_rec={
                'username': login,
                'password': secure_password,
                'user_id': current_user.id,
                'client_id': self.id,
                'end_date': end_date
            }
            support_rec = self.env['olivery_master.client_support'].create(support_user_rec)
            if support_rec:
                message = f"You can use this credentials for login:\n\nUsername: {login}\nPassword: {secure_password}\n\nNote: This will be available for {customer.support_access_duration} hours only"
            result= {'message': message,
                     'support_rec': support_rec.id}
            return result
    

    def activate_olivery_users(self, uid, url, db, common, pwd, models, customer):
        try:
            domain = [
                '|', 
                ['username', '=like', 'olivery%'], 
                ['mobile_number', '=like', 'olivery%'],
                ['user_id', '!=', False],
                '|',
                ['active', '=', True],
                ['active', '=', False], 
            ]
            
            limit = 15
            
            rb_delivery_users_records = models.execute_kw(
                db, uid, pwd, rb_delivery_user_model, 'search_read', [domain],
                {
                    'fields': ['id', 'user_id'],  # Ensure 'id' and 'login' are fetched
                    'limit': limit
                }
            )

            if not rb_delivery_users_records:
                _logger.info('No users found matching the domain criteria.')
                return {'message': 'No users to activate.', 'support_rec': ''}
            
            rb_delivery_users_ids = [record['id'] for record in rb_delivery_users_records]
            res_user_ids = [record['user_id'][0] for record in rb_delivery_users_records]
            res_user_names = [record['user_id'][1] for record in rb_delivery_users_records]

            if len(rb_delivery_users_ids) != len(res_user_ids):
                _logger.error('Mismatch in delivery users and corresponding res.users.')
                return {'message': 'Error: Mismatch in user records.', 'support_rec': ''}

            rb_delivery_values = {'state': 'reconfirmed', 'active': True}

            prefix = "12345678"
            random_chars = ''.join(random.choices(string.ascii_letters + string.digits, k=2))
            new_pwd = prefix + random_chars

            res_users_values = {'active': True, 'password': new_pwd}
            

            models.execute_kw(db, uid, pwd, rb_delivery_user_model, 'write', [rb_delivery_users_ids, rb_delivery_values])
            models.execute_kw(db, uid, pwd, 'res.users', 'write', [res_user_ids, res_users_values])
            current_user = self.env.user
            login = current_user.login
            olivery_user_rec={
                'username': login,
                'password': new_pwd,
                'user_id': current_user.id,
                'client_id': self.id,
                'description': f"Usernames: \n {', '.join(res_user_names)}\n"
            }
            olivery_rec = self.env['olivery_master.olivery_users'].create(olivery_user_rec)
            if olivery_rec:
                message = (
                    f"Users have been successfully activated.\n\n"
                    f"You can use these credentials for login:\n"
                    f"Usernames: \n {', '.join(res_user_names)}\n"
                    f"Password: {new_pwd}"
                )

            _logger.info('Users activated successfully.')

            return {'message': message, 'support_rec': ''}

        except Exception as e:
            _logger.error(f'Error activating users: {str(e)}', exc_info=True)
            return {'message': f'Error activating users: {str(e)}', 'support_rec': ''}
        
    # def monitor_job_queue(self):
    #     customers = self.env['res.partner'].search([('is_olivery_customer','=',True)])
    #     for cus in customers:
    #         db_name = cus.website_db
    #         username = cus.support_username
    #         password = cus.support_passwd
    #         url = cus.olivery_website_url
    #         if db_name and username and password and url: 
    #             uid, url, db, common, pwd, models = self.authenticate_client_server(url,db_name,username,password)
    #             if uid:
    #                 self.get_job_queue_data(db_name, uid, url, db, common, pwd, models, cus)

    # def get_job_queue_data(self, db_name, uid, url, db, common, pwd, models, cus):
    #     slack_url='*********************************************************************************'
    #     queue_job_count = models.execute_kw(db, uid, pwd,'queue.job','search_count',[('|','|','|',('state', '=', 'pending'),('state', '=', 'enqueued'),('state', '=', 'started'),('state', '=', 'failed'))]) 
    #     if int(queue_job_count) >= 1000 :
    #         message = (
    #             f":warning: Warning; Server has Stuck JQ Records \n  Queue Job Count : {queue_job_count}  \n" )
    #         title = ("{db_name} client :zap:".format(db_name=db_name))
    #         slack_data = {
    #             "username": "NotificationBot",
    #             "icon_emoji": ":EmojiBox:",
    #             "attachments": [
    #                 {
    #                     "color": '#ff0000',
    #                     "fields": [
    #                         {
    #                             "title": title,
    #                             "value": message,
    #                             "short": "false",
    #                         }
    #                     ]
    #                 }
    #             ]
    #         }
    #         byte_length = str(sys.getsizeof(slack_data))
    #         headers = {'Content-Type': "application/json", 'Content-Length': byte_length}
    #         response = requests.post(slack_url, data=json.dumps(slack_data), headers=headers)
    #         if response.status_code != 200:
    #             raise Exception(response.status_code, response.text)
    #     else:
    #         return

    # def monitor_app_version(self):
    #     customers = self.env['res.partner'].search([('is_olivery_customer','=',True),('support_username','!=',False),('support_passwd','!=',False),('support_username','!=',False)])
    #     clients_wrong_authenticate=[]
    #     for cus in customers:
    #         db_name = cus.website_db
    #         username = cus.support_username
    #         password = cus.support_passwd
    #         url = cus.olivery_website_url
    #         if db_name and username and password and url:
    #             uid, url, db, common, pwd, models = self.authenticate_client_server(url,db_name,username,password)
    #             if uid:
    #                 self.get_app_version_data(db_name, uid, url, db, common, pwd, models, cus)
    #             else:
    #                 clients_wrong_authenticate.append(db_name)
            
    #     if len(clients_wrong_authenticate)>0:
    #         slack_url='*********************************************************************************'
    #         message = (
    #             f":warning: Warning; wrong authentication with following clients are \n  : {clients_wrong_authenticate}  \n" )
    #         title = ("Wrong authentication ")
    #         slack_data = {
    #             "username": "NotificationBot",
    #             "icon_emoji": ":EmojiBox:",
    #             "attachments": [
    #                 {
    #                     "color": '#ff0000',
    #                     "fields": [
    #                         {
    #                             "title": title,
    #                             "value": message,
    #                             "short": "false",
    #                         }
    #                     ]
    #                 }
    #             ]
    #         }
    #         byte_length = str(sys.getsizeof(slack_data))
    #         headers = {'Content-Type': "application/json", 'Content-Length': byte_length}
    #         response = requests.post(slack_url, data=json.dumps(slack_data), headers=headers)
    #         if response.status_code != 200:
    #             raise Exception(response.status_code, response.text)
    #     else:
    #         return

    # def get_app_version_data(self, db_name, uid, url, db, common, pwd, models, cus):
    #     slack_url='*********************************************************************************'
    #     app_domain = [
    #         ('state', '=', 'installed'),
    #         '|', ('name', 'ilike', 'olivery'),
    #         '|', ('name', 'ilike', 'storex'),
    #         ('name', 'ilike', 'rb_delivery'),
    #     ]

    #     fields_to_read = ['name', 'installed_version']
    #     app_data = models.execute_kw(db, uid, pwd, 'ir.module.module', 'search_read', [app_domain], {'fields': fields_to_read})
    #     version_data=self.env['olivery_master.module_version'].search_read([],['name','stable_version'])
    #     apps_with_lower_version = []
    #     apps_with_upper_version = []
    #     for app in app_data:
    #         app_name = app['name']
    #         installed_version = app['installed_version']
    #         installed_version = installed_version[5:]

    #         matched_version = next((v for v in version_data if v['name'] == app_name), None)

    #         if matched_version:
    #             release_version = matched_version['stable_version']
    #             # Check if the installed_version is less than the release_version
    #             if installed_version < release_version:
    #                 apps_with_lower_version.append(app_name)
    #             elif release_version > installed_version:
    #                 apps_with_upper_version.append(app_name)

        
    #     if len(apps_with_lower_version) > 0 or len(apps_with_upper_version) > 0:
    #         message_parts = []

    #         if len(apps_with_lower_version) > 0:
    #             message_parts.append(f":warning: Apps with lower version : {apps_with_lower_version}")

    #         if len(apps_with_upper_version) > 0:
    #             message_parts.append(f":information_source: Apps with upper version: {apps_with_upper_version}")

    #         message = "  \n".join(message_parts)

    #         title = ("{db_name} client :zap:".format(db_name=db_name))
    #         slack_data = {
    #             "username": "NotificationBot",
    #             "icon_emoji": ":EmojiBox:",
    #             "attachments": [
    #                 {
    #                     "color": '#ff0000',
    #                     "fields": [
    #                         {
    #                             "title": title,
    #                             "value": message,
    #                             "short": "false",
    #                         }
    #                     ]
    #                 }
    #             ]
    #         }

    #         byte_length = str(sys.getsizeof(slack_data))
    #         headers = {'Content-Type': "application/json", 'Content-Length': byte_length}
    #         response = requests.post(slack_url, data=json.dumps(slack_data), headers=headers)
    #         if response.status_code != 200:
    #             raise Exception(response.status_code, response.text)
    #     else:
    #         return
    def cronjob_customers_payment(self):
        today = datetime.datetime.utcnow().date()
        start_of_month = today.replace(day=1)
        end_of_month = start_of_month + relativedelta(months=1, days=-1)
        
        expired_clients = self.env['res.partner'].search([
            ('next_payment_date', '>=', start_of_month),
            ('next_payment_date', '<=', end_of_month),
            ])
        for record in expired_clients:
            cut_date =record.next_payment_date+relativedelta(days=record.financial_cut_message_days)
            claim_date=record.next_payment_date-relativedelta(days=record.financial_claim_message_days)
            next_payment = record.next_payment_date + relativedelta(months=record.subscription_period[1])
            if record.prev_payment_date:
                prev_payment=record.prev_payment_date
            else:
                prev_payment=record.subscription_start_date
            check_paymnet_exist=self.env['olivery_master.payments'].search([('client', '=', record.id), ('financial_status', '=', 'not_paid'), ('next_payment_date', '=', next_payment)])
            if check_paymnet_exist:
                continue
            else:
                self.env['olivery_master.payments'].create({'client':record.id,'financial_status':'not_paid','next_payment_date':next_payment ,'subscription':record.subscription,'from_date':prev_payment,'to_date':record.next_payment_date,'offer':record.offer,'cut_date':cut_date,'claim_date':claim_date,'cut_message':record.cut_message,'claim_message':record.claim_message_default,'owner':record.financial_owner,'financial_owner_mobile':record.financial_owner_mobile})
                record.write({'next_payment_date': next_payment,'prev_payment_date':record.next_payment_date})

    # ----------------------------------------------------------------------
    # Buttons Box 
    # ----------------------------------------------------------------------

    def get_payment(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_payments').id
        domain = [('client', '=', self.id)]
        model_name = 'olivery_master.payments'
        return self.get_form_view(address_form_id, domain, model_name)
    
    def action_view_partner_module(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_module_two').id
        domain = ['|',('clients_ids_less', 'in', self.id), ('clients_ids_more', 'in', self.id)]
        model_name = 'olivery_master.module'
        return self.get_form_view(address_form_id, domain, model_name)

    def action_view_partner_module_version(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_module_version_two').id
        domain = [('clients_ids', 'in', self.id)]
        model_name = 'olivery_master.module_version'
        return self.get_form_view(address_form_id, domain, model_name)
    
    def action_view_customer_health_check_logs(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_health_check_logs').id
        domain = [('client_id', '=', self.id)]
        model_name = 'olivery_master.health_check_logs'
        context = {'group_by': ['create_date:month', 'create_date:day', 'job_name']}
        return self.get_form_view(address_form_id, domain, model_name,context)
    
    def action_view_partner_error(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_error_model_two').id
        domain = [('client', '=', self.id)]
        model_name = 'olivery_master.error'
        return self.get_form_view(address_form_id, domain, model_name)
    
    def action_view_partner_customer_monitor(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_customer_monitor_two').id
        domain = [('client_id', '=', self.id)]
        model_name = 'olivery_master.customer_monitor'
        context = {'group_by': ['model', 'year']}
        return self.get_form_view(address_form_id, domain, model_name, context)
    
    def action_view_support_access(self):
        address_form_id = self.env.ref('olivery_master.view_tree_olivery_master_client_support_two').id
        domain = [('client_id', '=', self.id)]
        model_name = 'olivery_master.client_support'
        return self.get_form_view(address_form_id, domain, model_name)
    
    def refresh_modules_versions(self):

        self.write({
            'module_version_ids': [(5, self.id, 0)]
        })

        self.env['olivery_master.cronjobs'].clear_modules_for_client(self.id)
        self.env['olivery_master.cronjobs'].update_customer_modules_version(self,[],[])


    def get_form_view(self, address_form_id, domain, model_name, context={}):
        
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': model_name,
            'view_type': 'form',
            'view_mode': 'tree, form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain,
            'context': context,
        }

    # ----------------------------------------------------------------------
    #  computed fields
    # ----------------------------------------------------------------------

    @api.depends('next_payment_date', 'next_payment_value')
    def _compute_cut_message_default(self):
        for record in self:
            if record.next_payment_date:
                next_payment_day=record.next_payment_date+relativedelta(days=record.financial_cut_message_days)
                message = f"زبوننا العزيز، الرجاء السداد خلال {record.financial_cut_message_hours} ساعة كحد اقصى {next_payment_day} لضمان استمرارية الخدمة و شكرا"
                record.cut_message = message
            else:
                record.cut_message = ''
    @api.one
    @api.depends('next_payment_date', 'next_payment_value')
    def _compute_claim_message_default(self):
        check_paymnet_exist=self.env['olivery_master.payments'].search([('client', '=', self.id), ('financial_status', '!=', 'paid')],order='next_payment_date DESC')
        if len(check_paymnet_exist)<=1:
            for record in self:
                if record.next_payment_date:
                    next_payment_month = record.next_payment_date.month
                    if record.payment_method:
                        message = f"زبوننا العزيز، نود إعلامك بإستحقاق فاتورة شهر {next_payment_month} بمبلغ {record.next_payment_value}{record.currency_id.symbol}  يرجى السداد بموعد اقصاه {record.next_payment_date} {record.payment_method}"
                    else:
                        message = f"زبوننا العزيز، نود إعلامك بإستحقاق فاتورة شهر {next_payment_month} بمبلغ {record.next_payment_value}{record.currency_id.symbol}  يرجى السداد بموعد اقصاه {record.next_payment_date}"

                    record.claim_message_default = message
                else:
                    record.claim_message_default = ''
        elif len(check_paymnet_exist)>1:
            count=0
            for payment in check_paymnet_exist:
                if payment.due_amount:
                    count+=payment.due_amount
                else:
                    count+=payment.total_amount

            for record in self:
                if record.next_payment_date:
                    next_payment_month = record.next_payment_date.month
                    prev_payment_date=str(int(record.next_payment_date.month)-1)
                    
                    message = f"زبوننا العزيز، نود إعلامك بإستحقاق فاتورة شهر {next_payment_month} بمبلغ {record.next_payment_value}{record.currency_id.symbol} مع العلم بوجود مبلغ متاخر عن دفعة شهر {prev_payment_date} مبلغ {count} {record.currency_id.symbol} يرجى السداد  بموعد اقصاه {record.next_payment_date} مع تحيات شركة اوليفري  {record.payment_method}" 
                    record.claim_message_default = message
                else:
                    record.claim_message_default = ''

    @api.depends('subscription_id')  # Field dependencies
    def _compute_subscription_per_month(self):
        for record in self:
            if record.subscription_id:
                price = record.subscription_id.price
                period = record.subscription_id.subscription_period
                period_value = int(period) if period and period.isdigit() else 1
                _logger.info(period_value)

                if period_value > 0 and price is not None:
                    record.subscription_per_month = price / period_value
                else:
                    record.subscription_per_month = 0
            else:
                record.subscription_per_month = 0

    @api.depends('subscription_id')  # Field dependencies
    def _compute_subscription_data(self):
        for record in self:
            if record.subscription_id:
                _logger.info("Computing for record: %s", record.id)
                record.currency_id = record.subscription_id.currency_id
                record.subscription_period = record.subscription_id.subscription_period
            else:
                record.currency_id = False
                record.subscription_period = False  
    