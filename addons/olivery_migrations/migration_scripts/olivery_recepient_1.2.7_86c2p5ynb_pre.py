#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import logging
import argparse
import xmlrpc.client
import traceback

logging.basicConfig(level=logging.INFO)

def pre_deploy(BASE_URL, CREDENTIALS):
    """
    1. Authenticates to Odoo
    2. Searches for the 'rb_delivery' module by name
    3. Calls the 'button_immediate_upgrade' method to upgrade it
    """
    common = xmlrpc.client.ServerProxy(f'{BASE_URL}/xmlrpc/2/common')
    uid = common.authenticate(CREDENTIALS['db'], CREDENTIALS['login'], CREDENTIALS['password'], {})

    if not uid:
        logging.error("Failed to authenticate in 'pre_deploy'. Check credentials.")
        return
    
    models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(BASE_URL))

    records_to_write_on = 'olivery_recepient.rb_delivery_business_access_his_client'
    rule_id = models.execute_kw(CREDENTIALS['db'], uid, CREDENTIALS['password'],'ir.model.data', 'xmlid_to_res_model_res_id',[records_to_write_on])
    if rule_id:
        new_condition = "['|',('commercial_id' ,'in', user.rb_user.id),('commercial_id' ,'in', user.rb_user.child_ids.ids)]"

        models.execute_kw(
            CREDENTIALS['db'], uid, CREDENTIALS['password'],
            'ir.rule', 'write', [[rule_id[1]], {'domain_force': str(new_condition)}]
        )
        print("Domain updated successfully:", new_condition)
    else:
        print("Rule not found")


def create_recomputation_job(BASE_URL, CREDENTIALS, job_name, model_xmlid, field_xmlid, record_limit, active_flag):
    """
    Creates a new 'rb_delivery.recomputation_job' record using XMLIDs to fetch model_id and field_id.
    Returns the new record's ID, or None if creation fails.
    """
    # 1) Authenticate
    common = xmlrpc.client.ServerProxy(f'{BASE_URL}/xmlrpc/2/common')
    uid = common.authenticate(CREDENTIALS['db'], CREDENTIALS['login'], CREDENTIALS['password'], {})
    if not uid:
        logging.error("Failed to authenticate in 'create_recomputation_job'. Check credentials.")
        return None

    models = xmlrpc.client.ServerProxy(f'{BASE_URL}/xmlrpc/2/object')

    # 2) Convert model_xmlid to (res_model, res_id)
    try:
        model_ref = models.execute_kw(
            CREDENTIALS['db'], uid, CREDENTIALS['password'],
            'ir.model.data', 'xmlid_to_res_model_res_id',
            [model_xmlid]
        )
        if not model_ref:
            logging.error(f"No reference found for model XMLID: {model_xmlid}")
            return None
        model_id = model_ref[1]  # (res_model, res_id)
        logging.info(f"Model XMLID '{model_xmlid}' -> model_id = {model_id}")
    except Exception as e:
        logging.error(f"Error retrieving model ID from XMLID '{model_xmlid}': {e}")
        logging.error(traceback.format_exc())
        return None

    # 3) Convert field_xmlid to (res_model, res_id)
    try:
        field_ref = models.execute_kw(
            CREDENTIALS['db'], uid, CREDENTIALS['password'],
            'ir.model.data', 'xmlid_to_res_model_res_id',
            [field_xmlid]
        )
        if not field_ref:
            logging.error(f"No reference found for field XMLID: {field_xmlid}")
            return None
        field_id = field_ref[1]  # (res_model, res_id)
        logging.info(f"Field XMLID '{field_xmlid}' -> field_id = {field_id}")
    except Exception as e:
        logging.error(f"Error retrieving field ID from XMLID '{field_xmlid}': {e}")
        logging.error(traceback.format_exc())
        return None

    # 4) Create the record in 'rb_delivery.recomputation_job'
    data = {
        'name': job_name,
        'model_id': model_id,
        'field_id': field_id,
        'record_limit': record_limit,
        'active': active_flag,
    }
    try:
        new_id = models.execute_kw(
            CREDENTIALS['db'], uid, CREDENTIALS['password'],
            'rb_delivery.recomputation_job', 'create',
            [data]
        )
        logging.info(f"Created new 'rb_delivery.recomputation_job' with ID: {new_id}")
        return new_id
    except Exception as e:
        logging.error(f"Failed to create recomputation_job record: {e}")
        logging.error(traceback.format_exc())
        return None


def call_generate_jobs_on_record(BASE_URL, CREDENTIALS, job_id):
    """
    Calls the 'generate_jobs' method on the given 'rb_delivery.recomputation_job' record.
    """
    # 1) Authenticate
    common = xmlrpc.client.ServerProxy(f'{BASE_URL}/xmlrpc/2/common')
    uid = common.authenticate(CREDENTIALS['db'], CREDENTIALS['login'], CREDENTIALS['password'], {})
    if not uid:
        logging.error("Failed to authenticate in 'call_generate_jobs_on_record'. Check credentials.")
        return

    # 2) Connect to 'object' endpoint
    models = xmlrpc.client.ServerProxy(f'{BASE_URL}/xmlrpc/2/object')

    try:
        # We pass [[job_id]] as the first param, because it's an instance method
        models.execute_kw(
            CREDENTIALS['db'], uid, CREDENTIALS['password'],
            'rb_delivery.recomputation_job', 'generate_jobs',
            [[job_id]]
        )
        logging.info(f"Successfully called 'generate_jobs' on record ID {job_id}.")
    except Exception as e:
        logging.error(f"Error calling generate_jobs on record {job_id}: {e}")
        logging.error(traceback.format_exc())


def main():
    parser = argparse.ArgumentParser(description="Script that upgrades rb_delivery, creates a recomputation job, then calls generate_jobs.")
    # Connection arguments
    parser.add_argument("--db", required=True, help="Enter the database name.")
    parser.add_argument("--login", required=True, help="Enter the login email.")
    parser.add_argument("--password", required=True, help="Enter the password.")
    parser.add_argument("--base_url", required=True, help="Enter the base URL (e.g., http://localhost:8069).")


    args = parser.parse_args()

    BASE_URL = args.base_url
    CREDENTIALS = {
        "db": args.db,
        "password": args.password,
        "login": args.login,
    }

    logging.info("Starting module upgrade process...")
    pre_deploy(BASE_URL, CREDENTIALS)

    logging.info("Creating rb_delivery.recomputation_job record...")
    job_id = create_recomputation_job(
        BASE_URL=BASE_URL,
        CREDENTIALS=CREDENTIALS,
        job_name='Compute client commercial ID',
        model_xmlid='olivery_recepient.model_rb_delivery_client',
        field_xmlid='olivery_recepient.field_rb_delivery_client__is_commercial_empty',
        record_limit=5000,
        active_flag=True
    )

    if job_id:
        logging.info(f"Calling 'generate_jobs' on record ID {job_id}...")
        call_generate_jobs_on_record(BASE_URL, CREDENTIALS, job_id)
    else:
        logging.warning("Could not create the recomputation job, so generate_jobs was not called.")


if __name__ == "__main__":
    main()