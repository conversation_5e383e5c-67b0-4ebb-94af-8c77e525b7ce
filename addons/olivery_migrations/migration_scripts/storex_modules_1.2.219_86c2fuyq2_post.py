import logging
import argparse
import xmlrpc.client
import traceback

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def pre_deploy_rc_next(BASE_URL, CREDENTIALS):
    url = BASE_URL
    db = CREDENTIALS['db']
    username = CREDENTIALS['login']
    password = CREDENTIALS['password']

    logging.info(f"Connecting to Odoo instance at {url} with database {db}...")
    
    try:
        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(url))
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            logging.error("Failed to authenticate with Odoo. Check your credentials.")
            return
        
        logging.info(f"Authenticated as user ID {uid}.")
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(url))

        logging.info("Fetching Menu item ID...")
        menu_item_id = get_menu_item_id(models, db, uid, password)
        
        if menu_item_id:
            logging.info(f"Menu item ID found: {menu_item_id}.")
            update_menu_item_domain(models, db, uid, password, menu_item_id)
            logging.info("Success: Updated domain in menu item.")
        else:
            logging.error("Menu item not found.")

    except Exception as e:
        logging.error("Error during operation:")
        logging.error(e)
        logging.error(traceback.format_exc())


def get_menu_item_id(models, db, uid, password):
    domain = [('module', '=', 'storex_modules'), ('name', '=', 'menu_storex_warehouse')]
    logging.info(f"Searching for menu item with domain: {domain}")
    
    try:
        menu_item_records = models.execute_kw(db, uid, password, 'ir.model.data', 'search_read', [domain, ['res_id']])
        logging.info(f"Menu item records fetched: {menu_item_records}")
        
        if menu_item_records:
            logging.info(f"Menu item records found: {menu_item_records}")
            return int(menu_item_records[0]['res_id'])
        else:
            logging.warning("No menu item records found.")
            return None
            
    except Exception as e:
        logging.error(f"An error occurred: {e}")
        logging.error(traceback.format_exc())
        return None


def update_menu_item_domain(models, db, uid, password, menu_item_id):
    logging.info(f"Reading menu item with ID: {menu_item_id}")
    try:
        menu_item = models.execute_kw(db, uid, password, 'ir.ui.menu', 'read', [[menu_item_id]])
        domain = [('full_name','in',["Administration / Access Rights","Delivery Application / Super Manager Role","Delivery Application / Manager Role","Delivery Application / Sales Role","Delivery Application / Warehouse Manager Role","Delivery Application / Warehouse Auditor Role","Delivery Application / Warehouse Employee Role"])]
        groups_ids = models.execute_kw(db, uid, password, 'res.groups', 'search_read', [domain, ['id']])
        if menu_item and groups_ids:
            group_ids = []
            for group_id in groups_ids:
                group_ids.append(group_id['id'])
            logging.info(f"Menu item data: {menu_item[0]}")
            updated_domain = [(6,0,group_ids)]
            
            updated_data = {'groups_id': updated_domain}
            logging.info(f"Updating menu item's `domain` field: {updated_data}")
            models.execute_kw(db, uid, password, 'ir.ui.menu', 'write', [[menu_item_id], updated_data])
        else:
            logging.error(f"No menu item found with ID: {menu_item_id}")
    except xmlrpc.client.Fault as e:
        logging.error(f"Failed to update menu item with ID {menu_item_id}: {e.faultString}")
    except Exception as e:
        logging.error(f"Unexpected error occurred: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description="Script to modify menu item and configurations.")
    parser.add_argument("--base_url", required=True, help="Enter the Base URL of the Odoo instance.")
    parser.add_argument("--db", required=True, help="Enter the database name.")
    parser.add_argument("--login", required=True, help="Enter the login email.")
    parser.add_argument("--password", required=True, help="Enter the password for the Odoo instance.")

    args = parser.parse_args()

    BASE_URL = args.base_url.strip()
    CREDENTIALS = {
        "db": args.db,
        "password": args.password,
        "login": args.login,
    }

    pre_deploy_rc_next(BASE_URL, CREDENTIALS)

if __name__ == "__main__":
    main()


# python3 migration_scripts/storex_modules_1.2.224_86c1paqkc.py --base_url "http://localhost" --db "unitedexp" --login "admin" --password "admin"
