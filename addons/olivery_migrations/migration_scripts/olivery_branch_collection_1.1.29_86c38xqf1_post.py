import logging
import argparse
import xmlrpc.client
import traceback

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def pre_deploy_rc_next(BASE_URL, CREDENTIALS):
    url = BASE_URL
    db = CREDENTIALS['db']
    username = CREDENTIALS['login']
    password = CREDENTIALS['password']

    logging.info(f"Connecting to Odoo instance at {url} with database {db}...")

    try:
        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(url))
        uid = common.authenticate(db, username, password, {})

        if not uid:
            logging.error("Failed to authenticate with Odoo. Check your credentials.")
            return

        logging.info(f"Authenticated as user ID {uid}.")
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(url))
        security_names = ['rb_delivery_accounting_access_user',
                         'rb_delivery_junior_accounting_access_user',
                         'rb_delivery_manager_access_user',
                         'rb_delivery_role_picking_up_manager_access_user',
                         'rb_delivery_role_distribution_manager_access_user',
                         'rb_delivery_data_entry_access_user',
                         'rb_delivery_call_center_access_user']
        
        for security in security_names:
            logging.info("Fetching action ID...")
            action_id = get_security_rule_id(models, db, uid, password,security)

            if action_id:
                logging.info(f"Action ID found: {action_id}.")
                update_rule_security(models, db, uid, password, action_id)
                logging.info("Success: Updated domain in action.")
            else:
                logging.error("Action not found.")

    except Exception as e:
        logging.error("Error during operation:")
        logging.error(e)
        logging.error(traceback.format_exc())


def get_security_rule_id(models, db, uid, password, security_name):
    domain = [('module', '=', 'rb_delivery'), ('name', '=', security_name)]
    logging.info(f"Searching for action with domain: {domain}")

    try:
        rule_records = models.execute_kw(db, uid, password, 'ir.model.data', 'search_read', [domain, ['res_id']])
        logging.info(f"Action records fetched: {rule_records}")

        if rule_records:
            logging.info(f"Action records found: {rule_records}")
            return int(rule_records[0]['res_id'])
        else:
            logging.warning("No action records found.")
            return None

    except Exception as e:
        logging.error(f"An error occurred: {e}")
        logging.error(traceback.format_exc())
        return None


def update_rule_security(models, db, uid, password, action_id):
    logging.info(f"Reading action with ID: {action_id}")
    try:
        action = models.execute_kw(db, uid, password, 'ir.rule', 'read', [[action_id]])

        if action:
            logging.info(f"Action data: {action[0]}")
            updated_action_data = {'domain_force': "['|','|','|','|',('user_id' ,'=', user.id),(user.partner_id.branch_id.main_branch,'=',True),('branch_id.id','=' ,user.partner_id.branch_id.id),('branch_id.id','in' ,user.partner_id.branch_id.sub_branch.ids),('is_company', '=', True)]"}
            logging.info(f"Updating action's `domain` field: {updated_action_data}")
            models.execute_kw(db, uid, password, 'ir.rule', 'write', [[action_id], updated_action_data])
        else:
            logging.error(f"No action found with ID: {action_id}")
    except xmlrpc.client.Fault as e:
        logging.error(f"Failed to update action with ID {action_id}: {e.faultString}")
    except Exception as e:
        logging.error(f"Unexpected error occurred: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description="Script to modify window actions and configurations.")
    parser.add_argument("--base_url", required=True, help="Enter the Base URL of the Odoo instance.")
    parser.add_argument("--db", required=True, help="Enter the database name.")
    parser.add_argument("--login", required=True, help="Enter the login email.")
    parser.add_argument("--password", required=True, help="Enter the password for the Odoo instance.")

    args = parser.parse_args()

    BASE_URL = args.base_url.strip()
    CREDENTIALS = {
        "db": args.db,
        "password": args.password,
        "login": args.login,
    }

    pre_deploy_rc_next(BASE_URL, CREDENTIALS)

if __name__ == "__main__":
    main()


# python3 migration_scripts/olivery_branch_collection_1.0.119_86bzr3yrm.py --base_url "http://localhost" --db "qashoo" --login "admin" --password "admin"
