import threading
import time
import requests

# Define the URL and headers/cookies (from the curl command)
url = 'https://release.olivery.io/report/pdf/rb_delivery.order_detail_a4/' + ",".join(map(str, range(31385, 31208)))

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'en-US,en;q=0.9',
    'priority': 'u=0, i',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
}

cookies = {
    '_fbp': 'fb.1.1749111869683.14788530426020748',
    'frontend_lang': 'en_US',
    'session_id': '54630dc9ab21b9804e3fd5cd40574d0aad92bb9c'
}

# Define the function each thread will run
def send_request(index):
    try:
        response = requests.get(url, headers=headers, cookies=cookies)
        print(f"Thread {index}: Status Code = {response.status_code}")
    except Exception as e:
        print(f"Thread {index}: Error = {e}")

# Run the test 10 times
for batch in range(10):
    threads = []
    print(f"\n🚀 Starting batch {batch + 1}")
    for i in range(5):
        t = threading.Thread(target=send_request, args=(i + 1,))
        threads.append(t)
        t.start()

    # Wait for all threads in this batch to complete
    for t in threads:
        t.join()

    print(f"✅ Batch {batch + 1} complete.")
    time.sleep(1)  # Optional delay between batches