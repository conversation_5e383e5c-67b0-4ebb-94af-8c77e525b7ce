import logging
import argparse
import xmlrpc.client

logging.basicConfig(level=logging.INFO)

def pre_deploy(BASE_URL, CREDENTIALS):
    common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(BASE_URL))
    uid = common.authenticate(CREDENTIALS['db'], CREDENTIALS['login'], CREDENTIALS['password'], {})
    if uid:
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(BASE_URL))
        base_module_id = models.execute_kw(CREDENTIALS['db'], uid, CREDENTIALS['password'], 'ir.module.module', 'search', [[('name','=','rb_delivery')]])
        if base_module_id:
            models.execute_kw(CREDENTIALS['db'], uid, CREDENTIALS['password'], 'ir.module.module', 'button_immediate_upgrade', [base_module_id])
def main():
    parser = argparse.ArgumentParser(description="Script .")
    parser.add_argument("--db", required=True, help="Enter the database name.")
    parser.add_argument("--login", required=True, help="Enter the login email.")
    parser.add_argument("--password", required=True, help="Enter the password.")
    parser.add_argument("--base_url", required=True, help="Enter BaseUrl.")

    args = parser.parse_args()
    
    BASE_URL = args.base_url
    CREDENTIALS = {
        "db": args.db,
        "password": args.password,
        "login": args.login,
    }
    
    pre_deploy(BASE_URL, CREDENTIALS)

if __name__ == "__main__":
    main()