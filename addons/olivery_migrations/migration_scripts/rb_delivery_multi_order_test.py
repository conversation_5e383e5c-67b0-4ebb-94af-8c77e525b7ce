import threading
import requests
import json
import time

# Define the URL and headers
url = 'http://localhost/create_multi_orders'

headers = {
    'Content-Type': 'application/json',
    'Cookie': 'session_id=00dce6b260db580ba6355ac2c0b23586988b4f6e'
}

# Define the payload
payload = {
    "jsonrpc": "2.0",
    "params": {
        "db": "hi-demo",
        "login": "olivery_bs",
        "password": "12345678",
        "orders_list": [
            {
                "customer_area": "رام الله",
                "customer_address": "الزرقاء",
                "customer_mobile": "0987654321",
                "customer_name": "tes",
                "cost": "190"
            },
            {
                "customer_area": "رام الله",
                "customer_address": "الزرقاء",
                "customer_mobile": "098765432",
                "customer_name": "tes",
                "cost": "190"
            },
            {
                "customer_area": "رام الله",
                "customer_address": "الزرقاء",
                "customer_mobile": "0987654321",
                "customer_name": "tessssss",
                "cost": "190"
            }
        ]
    }
}

# Define the function each thread will run
def send_request(thread_id):
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Thread {thread_id}: Status Code = {response.status_code}")
        print(f"Thread {thread_id}: Response = {response.text[:100]}...")  # Print first 100 chars
    except Exception as e:
        print(f"Thread {thread_id}: Error = {e}")

# Create threads
threads = []
for i in range(4):
    thread = threading.Thread(target=send_request, args=(i+1,))
    threads.append(thread)

# Start all threads at the same time
print("Starting all 4 requests simultaneously...")
for thread in threads:
    thread.start()

# Wait for all threads to complete
for thread in threads:
    thread.join()

print("All requests completed.")
