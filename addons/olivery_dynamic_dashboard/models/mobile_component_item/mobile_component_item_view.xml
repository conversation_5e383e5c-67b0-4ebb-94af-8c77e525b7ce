
<odoo>
  <data>
    <record id="view_form_rb_delivery_mobile_component_item" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_mobile_component_item</field>
      <field name="model">rb_delivery.mobile_component_item</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>
            <div class="oe_title">
                <label for="name" string="Name" class="oe_edit_only"/>
                    <h1>
                        <field name="name"/>
                    </h1>
            </div>



          </sheet>
          <!-- History and communication: -->
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_mobile_component_item" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_mobile_component_item</field>
      <field name="model">rb_delivery.mobile_component_item</field>

      <field name="arch" type="xml">
        <tree editable="top">
          <field name="count_type" invisible="1"/>
          <field name="sequence" widget="handle"/>
          <field name="dashboard_item_id"/>
          <field name="show_count" attrs="{'invisible':[['count_type','!=','sum']]}"/>
          <field name="component_type"/>
          <field name="title"/>
          <field name="icon"/>
          <field name="head_color" string="Head Color                   " widget="color"/>
          <field name="body_color" string="Body Color                   " widget="color"/>
          <field name="background_color" string="Background Color                   " widget="color"/>

          <field name="short_tile" />
          <field name="matrix_item"/>
          <field name="show_count_only"/>
          <field name="is_date_box" />
          <field name="date_box_filter" attrs="{'invisible':[['is_date_box','=',False]]}"/>
          <field name="date_box_value" attrs="{'invisible':[['is_date_box','=',False]]}"/>
          <field name="clickable"/>
          <field name="show_date_filter"/>
          <field name="date_filter_type" attrs="{'invisible':[['show_date_filter','=',False]]}"/>
          <field name="show_sales_filter"/>
          <field name="show_company_filter"/>
          <field name="show_business_filter"/>
          <field name="show_separator"/>
          <field name="separator_title" attrs="{'invisible':[['show_separator','=',False]]}"/>
          <field name="ks_item_start_date" />
          <field name="ks_item_end_date" />
          <field name="ks_date_filter_selection" />
          
        </tree>
      </field>

    </record>

  </data>
</odoo>
