# -*- coding: utf-8 -*-
from openerp import models, fields, api
from openerp.exceptions import ValidationError
import json
from odoo.addons.ks_dashboard_ninja.lib.ks_date_filter_selections import ks_get_date
class rb_delivery_mobile_component_item(models.Model):

    _name = 'rb_delivery.mobile_component_item'
    _inherit = 'mail.thread'
    _order = "sequence ASC"

    

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------
            

    mobile_component_id = fields.Many2one(comodel_name = 'rb_delivery.mobile_component',required=False)

    dashboard_item_id = fields.Many2one(comodel_name = 'ks_dashboard_ninja.item',required=False,domain="[['name','!=',False]]")
    
    ks_item_end_date = fields.Datetime(string="End Date")

    ks_item_start_date = fields.Datetime(string="Start Date")
    
    ks_date_filter_selection = fields.Selection([
        ('l_none', 'None'),
        ('l_day', 'Today'),
        ('t_week', 'This Week'),
        ('t_month', 'This Month'),
        ('t_quarter', 'This Quarter'),
        ('t_year', 'This Year'),
        ('n_day', 'Next Day'),
        ('n_week', 'Next Week'),
        ('n_month', 'Next Month'),
        ('n_quarter', 'Next Quarter'),
        ('n_year', 'Next Year'),
        ('ls_day', 'Last Day'),
        ('ls_week', 'Last Week'),
        ('ls_month', 'Last Month'),
        ('ls_quarter', 'Last Quarter'),
        ('ls_year', 'Last Year'),
        ('l_week', 'Last 7 days'),
        ('l_month', 'Last 30 days'),
        ('l_quarter', 'Last 90 days'),
        ('l_year', 'Last 365 days'),
        ('ls_past_until_now', 'Past Till Now'),
        ('ls_pastwithout_now', ' Past Excluding Today'),
        ('n_future_starting_now', 'Future Starting Now'),
        ('n_futurestarting_tomorrow', 'Future Starting Tomorrow'),
        ('l_custom', 'Custom Filter'),
    ], string="Date Filter Selection", default="l_none", related="dashboard_item_id.ks_date_filter_selection")
    
    ks_date_filter_field = fields.Many2one(comodel_name = 'ir.model.fields',related="dashboard_item_id.ks_date_filter_field",string="Date Filter Field")

    group_id = fields.Many2one('res.groups', related="mobile_component_id.group_id", string="Role",track_visibility="on_change")

    short_tile = fields.Boolean("Short Tile",default=False)

    matrix_item = fields.Boolean("Matrix Item",default=False)

    show_count_only=fields.Boolean("show count only",default=False)

    title = fields.Char(string='Title',related="dashboard_item_id.name")

    head_color = fields.Char('Head Color', track_visibility="on_change")

    body_color = fields.Char('Body Color', track_visibility="on_change")

    icon = fields.Char('Icon', track_visibility="on_change")
 
    component_type = fields.Selection(related="dashboard_item_id.ks_dashboard_item_type",string="Component Type",readonly=True)

    count_type = fields.Selection(related="dashboard_item_id.ks_record_count_type",string="Count Type",readonly=True)

    domain = fields.Char(related="dashboard_item_id.ks_domain")

    is_date_box = fields.Boolean("Is Date Box")

    date_box_filter = fields.Selection(selection=[('create_date','Created On'),('write_date','Last Updated On'),('status_last_updated_on','Status Last Updated On')], default='create_date')

    date_box_value = fields.Selection(selection=[('day','Day'),('yesterday','Yesterday'),('week','Week'),('month','Month')], default='day')

    sequence = fields.Integer(string="Sequence")

    clickable = fields.Boolean(string="Clickable")

    name = fields.Char(string='Name',related="dashboard_item_id.name",track_visibility="on_change")

    show_date_filter = fields.Boolean()

    date_filter_type = fields.Selection(selection=[('create_date','Created On'),('write_date','Last Updated On'),('status_last_updated_on','Status Last Updated On'),('olivery_status_last_updated_on','Delivery Status Last Updated On'),('assign_to_distributor_date', 'Assign to distributor date')], default='create_date')

    show_sales_filter = fields.Boolean()

    show_company_filter = fields.Boolean()

    show_business_filter = fields.Boolean()

    show_count = fields.Boolean()

    show_separator = fields.Boolean()

    separator_title = fields.Char()

    background_color = fields.Char('Background Color', track_visibility="on_change")


    @api.model
    def get_horizontal_bar_chart(self, component_id, custom_domain=False):
        component = self.search([['id', '=', component_id]])
        if (component.ks_date_filter_selection!="l_none"):
            self.updateDateDomains(component)
        else:
            component.ks_item_start_date = False
            component.ks_item_end_date = False
        domain_statuses = []
        if component and component.domain:
            parts = component.domain.split('["state","=",')
            for part in parts[1:]:
                state_value = part.split('"]')[0].replace('"', '').strip()
                domain_statuses.append(state_value.lower())

        all_status_records = []
        if domain_statuses:
            all_status_records = self.env['rb_delivery.status'].search_read(
                [('name', 'in', domain_statuses), '|', ('status_type', '=', False), ('status_type', '=', 'olivery_order')],
                ['id','name', 'title', 'colour_code','icon_name'])
        status_dict = {status['name'].strip().lower(): status for status in all_status_records}
        ordered_status_records = [status_dict[name] for name in domain_statuses if name in status_dict]

        dashboard_item_id = component.dashboard_item_id
        count_dashboard_item_id = dashboard_item_id.copy({'ks_chart_data_count_type': 'count','ks_chart_measure_field':[]})
        dashboard_item = self.env['ks_dashboard_ninja.board'].ks_fetch_item([dashboard_item_id.id], dashboard_item_id.ks_dashboard_ninja_board_id.id)
        json_dashboard_item = dashboard_item[dashboard_item_id.id]

        json_dashboard_item['ks_chart_data'] = json.loads(json_dashboard_item['ks_chart_data'])
        labels = [label.strip().lower() for label in json_dashboard_item['ks_chart_data']['labels']]
        datasets = json_dashboard_item['ks_chart_data']['datasets']

        count_dashboard_item = self.env['ks_dashboard_ninja.board'].ks_fetch_item([count_dashboard_item_id.id], count_dashboard_item_id.ks_dashboard_ninja_board_id.id)
        json_count_dashboard_item = count_dashboard_item[count_dashboard_item_id.id]
        json_count_dashboard_item['ks_chart_data'] = json.loads(json_count_dashboard_item['ks_chart_data'])
        count_datasets = json_count_dashboard_item['ks_chart_data']['datasets']
        label_data = {}
        for index, status_name in enumerate(domain_statuses, start=1):
            status_name_clean = status_name.strip().lower()

            corresponding_status = next((status for status in ordered_status_records if status['name'].strip().lower() == status_name_clean), None)
            
            if corresponding_status:
                title = corresponding_status['title'].strip().lower()                
                matching_index = next((i for i, label in enumerate(labels) if label == title), None)
                sums = datasets[0]['data'][matching_index] if matching_index is not None else 0
                counts = count_datasets[0]['data'][matching_index] if matching_index is not None else 0
                color = corresponding_status.get('colour_code') or '#000000'                
                icon_name = corresponding_status.get('icon_name', 'inProgressIcon') or 'inProgressIcon'
                label_data[corresponding_status['id']] = {'label': corresponding_status['title'], 'sum': sums, 'count': counts, 'color': color,'icon_name': icon_name, 'sort_id': index}
            else:
                label_data[corresponding_status['id']] = {
                    'label': status_name_clean.title(),
                    'sum': 0,
                    'count': 0,
                    'color': '#000000',
                    'icon_name': 'inProgressIcon',
                    'sort_id': 0
                }

        if count_dashboard_item_id:
            count_dashboard_item_id.sudo().unlink()
        return label_data

    @api.model
    def get_chart_data(self,component_id):
        component = self.search([['id','=',component_id]])
        if (component.ks_date_filter_selection!="l_none"):
            self.updateDateDomains(component)
        else:
            component.ks_item_start_date = False
            component.ks_item_end_date = False
        dashboard_item_id = component.dashboard_item_id
        dashboard_item = self.env['ks_dashboard_ninja.board'].ks_fetch_item([dashboard_item_id.id],dashboard_item_id.ks_dashboard_ninja_board_id.id)
        json_dashboard_item = dashboard_item[dashboard_item_id.id]
        json_dashboard_item['ks_chart_data'] = json.loads(json_dashboard_item['ks_chart_data'])
        values=[]
        i = 0
        for data in json_dashboard_item['ks_chart_data']['datasets'][0]['data']:
            if i < 6:
                value={'x':data,'y':json_dashboard_item['ks_chart_data']['labels'][i]}
                i+=1
                values.append(value)

        return {'values':values,'title':component.title,'component_type':'chart'}

    @api.model
    def get_tile_data(self,component_id,custom_domain=False):
        component = self.search([['id','=',component_id]])
        if (component.ks_date_filter_selection!="l_none"):
            self.updateDateDomains(component)
        else:
            component.ks_item_start_date = False
            component.ks_item_end_date = False
        dashboard_item_id = component.dashboard_item_id
        json_dashboard_item={}
        temp_dashboard_item=False
        temp_dashboard_item_id=False
        if component.show_count and component.count_type == 'sum':
            # temp_values = dashboard_item_id.read()[0]
            # temp_values['ks_record_count_type']="count"
            temp_dashboard_item_id = dashboard_item_id.copy({'ks_record_count_type':'count'})
        if custom_domain:
            json_dashboard_item['ks_record_count'] = component.dashboard_item_id.get_record_count_with_domain(custom_domain)
            if temp_dashboard_item_id:
                json_dashboard_item['second_record_count'] = temp_dashboard_item_id.get_record_count_with_domain(custom_domain)
        else:
            
            dashboard_item = self.env['ks_dashboard_ninja.board'].ks_fetch_item([dashboard_item_id.id],dashboard_item_id.ks_dashboard_ninja_board_id.id)
            json_dashboard_item = dashboard_item[dashboard_item_id.id]
            if temp_dashboard_item_id:
                temp_dashboard_item = self.env['ks_dashboard_ninja.board'].ks_fetch_item([temp_dashboard_item_id.id],temp_dashboard_item_id.ks_dashboard_ninja_board_id.id)
                json_dashboard_item['second_record_count'] = temp_dashboard_item[temp_dashboard_item_id.id]['ks_record_count']
        if temp_dashboard_item_id:
            temp_dashboard_item_id.sudo().unlink()
            return {'sum':json_dashboard_item['ks_record_count'],'count':json_dashboard_item['second_record_count']}
        return json_dashboard_item['ks_record_count'] if component.count_type == 'sum' else str(int(json_dashboard_item['ks_record_count'])) if component.count_type == 'count' else str(json_dashboard_item['ks_record_count'])
    
    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None,not_storex=False):
        return super(rb_delivery_mobile_component_item,self).search_read(domain,fields,offset,limit,order) 
    
    @api.model
    def updateDateDomains(self, component):
        res = ks_get_date(component.ks_date_filter_selection, self, fields.Datetime)
        component.ks_item_start_date = res['selected_start_date']
        component.ks_item_end_date = res['selected_end_date']

    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------


    class dynamic_ninja_dashboard_items(models.Model):
        _inherit = 'ks_dashboard_ninja.item'

        def get_record_count_with_domain(self,domain):
            domain = json.dumps(domain+json.loads(self.ks_domain))
            ks_record_count = 0
            if self.ks_record_count_type == 'count' or self.ks_dashboard_item_type == 'ks_list_view':
                ks_record_count = self.ks_fetch_model_data(self.ks_model_name, domain, 'search_count', self)
            elif self.ks_record_count_type in ['sum','average'] and self.ks_record_field and self.ks_dashboard_item_type != 'ks_list_view':
                ks_records_grouped_data = self.ks_fetch_model_data(self.ks_model_name, domain, 'read_group', self)
                if ks_records_grouped_data and len(ks_records_grouped_data) > 0:
                    ks_records_grouped_data = ks_records_grouped_data[0]
                    if self.ks_record_count_type == 'sum' and ks_records_grouped_data.get('__count', False) and (
                            ks_records_grouped_data.get(self.ks_record_field.name)):
                        ks_record_count = ks_records_grouped_data.get(self.ks_record_field.name, 0)
                    elif self.ks_record_count_type == 'average' and ks_records_grouped_data.get(
                            '__count', False) and (ks_records_grouped_data.get(self.ks_record_field.name)):
                        ks_record_count = ks_records_grouped_data.get(self.ks_record_field.name,0) / ks_records_grouped_data.get('__count',1)
            return ks_record_count
                    



