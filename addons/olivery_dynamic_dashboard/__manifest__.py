# -*- coding: utf-8 -*-
{
    'name': "olivery_dynamic_dashboard",
    'summary': """
        Olivery Dynamic Dashboard from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.38',

    # any module necessary for this one to work correctly
    'depends': ['base','rb_delivery','ks_dashboard_ninja', 'olivery_recepient'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/mobile_component/mobile_component_view.xml',
        'models/mobile_component_item/mobile_component_item_view.xml',
        'views/module_view.xml',
        'models/mobile_template/mobile_template_view.xml',
        'demo/templates.xml',
        'models/mobile_attachment/mobile_attachment_view.xml',
        'views/templates.xml'
    ], 
    'qweb': [
        'static/src/xml/*.xml',
    ],
}
