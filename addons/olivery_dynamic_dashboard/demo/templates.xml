<odoo>
    <data noupdate="1">
        <record id="rb_delivery_dynamic_template" model="rb_delivery.dashboard_templates">
            <field name="name">dynamic_dashboard</field>
            <field name="title">Dynamic Template</field>
            <field name="template_selected">True</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/dynamic_dashboard.png"></field>
        </record>
        <record id="rb_delivery_template_one" model="rb_delivery.dashboard_templates">
            <field name="name">bosta_dashboard</field>
            <field name="title">Bosta Template</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/bosta_dashboard.png"></field>
        </record>
        <record id="rb_delivery_template_three" model="rb_delivery.dashboard_templates">
            <field name="name">redline_dashboard</field>
            <field name="title">Redline Template</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/redline_dashboard.png"></field>
        </record>
        <record id="rb_delivery_template_four" model="rb_delivery.dashboard_templates">
            <field name="name">united_dashboard</field>
            <field name="title">United Template</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/united_dashboard.png"></field>
        </record>
        <record id="rb_delivery_template_five" model="rb_delivery.dashboard_templates">
            <field name="name">tornado_dashboard</field>
            <field name="title">Tornado Template</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/tornado_dashboard.png"></field>
        </record>

        <record id="rb_delivery_template_six" model="rb_delivery.dashboard_templates">
            <field name="name">olivery_pro_max_dashboard</field>
            <field name="title">Olivery Pro Max Template</field>
            <field name="image" type="base64"
                file="olivery_dynamic_dashboard/static/src/imgs/olivery_pro_max_dashboard.png"/>
        </record>

        <!-- Selected Template -->
        <record id="rb_delivery_selected_mobile_template" model="rb_delivery.mobile_template">
            <field name="template_ids" eval="[(6, 0, [
                ref('rb_delivery_dynamic_template'),
                ref('rb_delivery_template_one'),
                ref('rb_delivery_template_three'),
                ref('rb_delivery_template_four'),
                ref('rb_delivery_template_five'),
                ref('rb_delivery_template_six')
            ])]"/>
        </record>

    </data>
</odoo>