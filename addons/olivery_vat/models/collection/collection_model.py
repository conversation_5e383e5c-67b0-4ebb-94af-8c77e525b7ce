# -*- coding: utf-8 -*-

import json
import logging
import qrcode
import base64
from io import BytesIO
from openerp import models, fields, api
from openerp.exceptions import ValidationError
import math
_logger = logging.getLogger(__name__)


class order_multi_print_orders_money_collector_wizard(models.Model):
    _inherit = 'rb_delivery.multi_print_orders_money_collector'
    qr_code = fields.Binary('QR code', compute="create_qrcode")
    vat_value = fields.Float(compute="compute_vat_value")
    delivery_fee_without_vat = fields.Float(compute="compute_delivery_fee_without_vat") 
    vat_record_id = fields.Many2one('olivery_vat.vat', string='VAT Record')
    is_in_vat_collection = fields.Boolean('In Vat Collection', default=False,compute="is_vat_collection" ,store=False)

    @api.one
    def compute_vat_value(self):
        total_vat_value = sum(order.vat_value for order in self.order_ids)
        self.vat_value = total_vat_value


    @api.one
    def compute_delivery_fee_without_vat(self):
        total_delivery_fee_without_vat=sum(order.delivery_fee_without_vat for order in self.order_ids)
        self.delivery_fee_without_vat = total_delivery_fee_without_vat

    @api.multi
    @api.depends('vat_record_id')
    def is_vat_collection(self):
        for record in self:
            if len(record.vat_record_id)>0:
                record.is_in_vat_collection = True


    @api.one
    @api.depends('sequence')
    def create_qrcode(self):
        if (self.sequence):
            qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
            qr.add_data(self.sequence)
            qr.make(fit=True)
            img = qr.make_image()
            temp = BytesIO()
            img.save(temp, format="PNG")
            qr_image = base64.b64encode(temp.getvalue())
            self.qr_code = qr_image