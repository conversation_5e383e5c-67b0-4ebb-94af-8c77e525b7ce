# -*- coding: utf-8 -*-

import json
import logging
import qrcode
import base64
from io import BytesIO
from openerp import models, fields, api
from openerp.exceptions import ValidationError
import math
_logger = logging.getLogger(__name__)


class olivery_vat_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    @api.one
    @api.depends('vat_included')
    def _compute_default_vat(self):
        if self.vat_included:
            default_vat = self.env['rb_delivery.client_configuration'].get_param('default_vat_amount')
            self.default_vat = float(default_vat)
        else:
            self.default_vat = 0

    default_vat = fields.Float('Default VAT', compute="_compute_default_vat", store=False)

    vat_value = fields.Float('VAT', compute="get_customer_price",store=False,track_visibility="on_change")

    delivery_fee_without_vat = fields.Float('Delivery Fee Without VAT', compute="get_customer_price", store=False,track_visibility="on_change")

    qr_code = fields.Binary('QR code', compute="create_qrcode")

    vat_included = fields.Boolean(related='assign_to_business.vat_included', readonly=True)

    vat_qr_code_image = fields.Binary('Vat QR Code', compute="create_qr_code_vat")

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    @api.one
    @api.depends('sequence')
    def create_qrcode(self):
        if (self.sequence):
            qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
            qr.add_data(self.sequence)
            qr.make(fit=True)
            img = qr.make_image()
            temp = BytesIO()
            img.save(temp, format="PNG")
            qr_image = base64.b64encode(temp.getvalue())
            self.qr_code = qr_image

    @api.depends('default_vat', 'delivery_fee_without_vat')
    @api.multi
    def _compute_vat(self):
        for rec in self:
            if rec.vat_included:
                rec.vat_value=self.get_vat_value(rec.delivery_fee_without_vat,rec.default_vat)
            else:
                rec.vat_value=0

    @api.model
    def get_vat_value(self,delivery_fee_without_vat,default_vat):

        round_up = self.env['rb_delivery.client_configuration'].get_param('round_up_vat_value')
        if default_vat and delivery_fee_without_vat:
            vat_value = delivery_fee_without_vat * (default_vat/100)
            if round_up:
                return math.ceil(vat_value)
            else:
                return vat_value


    def get_clone_order_values(self,order,cloned_status,cloner_status,values):
        clone_order_values = super(olivery_vat_order,self).get_clone_order_values(order,cloned_status,cloner_status,values)
        if order.vat_value != 0:
            delivery_cost = order.delivery_cost - order.vat_value
            clone_order_values['discount'] = delivery_cost
        return clone_order_values

    @api.depends('customer_area', 'assign_to_business', 'extra_cost', 'order_type_id', 'discount','customer_sub_area', 'service','business_alt_area','show_alt_address','default_vat')
    @api.multi
    def get_customer_price(self):
        if 'active_test' in self._context and 'module' in self._context and 'force_recompute' not in self._context:
            return 0
        confs = self.env['rb_delivery.client_configuration'].get_param(['vat_included_with_delivery_fee','round_up_vat_value'])
        vat_included_with_delivery_fee = confs['vat_included_with_delivery_fee']
        round_up = confs['round_up_vat_value']
        super(olivery_vat_order, self).get_customer_price()
        for rec in self:
            if rec.default_vat:
                returned_value = 0
                if rec.returned_value and rec.returned_discount:
                    if rec.returned_discount == '100%':
                        delivery_cost = 0
                    else:
                        returned_value = float(rec.returned_value)
                        delivery_cost = returned_value

                else:
                    delivery_cost = rec.delivery_cost

                if vat_included_with_delivery_fee:
                    rec.delivery_fee_without_vat = delivery_cost / ((rec.default_vat/100) + 1)
                else:
                    rec.delivery_fee_without_vat = delivery_cost
                    vat_value = delivery_cost * (rec.default_vat/100)
                    if round_up:
                        vat_value = math.ceil(vat_value)

                    result = delivery_cost + vat_value
                    rec.delivery_cost = int(result * 100) / 100.0

            rec._compute_vat()


    @api.one
    @api.depends('agent_cost','money_collection_cost','assign_to_agent')
    def _compute_required_to_company(self):
        res = super(olivery_vat_order, self)._compute_required_to_company()
        self.required_to_company = self.required_to_company + self.agent_cost

    def get_discount_type(self,discount_type,values,value,reject_reason_ids,pricelist_discount_type):
        if self.default_vat:
            updated_values = {}
            if len(reject_reason_ids) == 0 or (self.reject_reason.id in reject_reason_ids) or ('reject_reason' in values and values['reject_reason'] and values['reject_reason'] in reject_reason_ids):
                if discount_type == 'default' and not self.returned_value:
                    updated_values['returned_discount'] = '100%'
                    updated_values['returned_value'] = 0
                    if (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                        updated_values['required_from_business'] = 0
                        updated_values['customer_payment'] = '0'

                elif discount_type == 'fixed' and not self.returned_value:
                    discount_value = float(value)
                    updated_values['returned_discount'] = discount_value
                    updated_values['returned_value'] = self.delivery_fee_without_vat - discount_value
                    if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                        updated_values['required_from_business'] =  discount_value - self.delivery_fee_without_vat
                        updated_values['customer_payment'] = '0'

                elif discount_type == 'pricelist' and not self.returned_value:
                    if self.customer_sub_area:
                        data = {
                            'sender_id': self.sudo().assign_to_business.id,
                            'to_area_id': self.customer_area.id,
                            'order_type_id': self.order_type_id.id,
                            'sub_area_id':self.customer_sub_area.id
                            }
                    else:
                        data = {
                            'sender_id': self.sudo().assign_to_business.id,
                            'to_area_id': self.customer_area.id,
                            'order_type_id': self.order_type_id.id,
                            'sub_area_id':False
                            }

                    if self.show_alt_address:
                        discount_value , fixed_value = self.env['rb_delivery.pricelist'].get_discount_value(data,self.business_alt_area)
                    else:
                        discount_value , fixed_value  = self.env['rb_delivery.pricelist'].get_discount_value(data)

                    if pricelist_discount_type == 'percentage':
                        updated_values['returned_discount'] = round((self.delivery_fee_without_vat * float(discount_value))/100,1)
                        updated_values['returned_value'] = self.delivery_fee_without_vat - round((self.delivery_fee_without_vat * float(discount_value))/100,1)
                        if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                            updated_values['required_from_business'] =  round((self.delivery_fee_without_vat * float(discount_value))/100,1)-	self.delivery_fee_without_vat
                            updated_values['customer_payment'] = '0'
                    elif pricelist_discount_type == 'fixed_value':
                        updated_values['returned_discount'] = self.delivery_fee_without_vat - fixed_value
                        updated_values['returned_value'] = self.delivery_fee_without_vat - updated_values['returned_discount']
                        if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                            updated_values['required_from_business'] =  updated_values['returned_discount'] -	self.delivery_fee_without_vat
                            updated_values['customer_payment'] = '0'
                    else:
                        updated_values['returned_discount'] = float(discount_value)
                        updated_values['returned_value'] = self.delivery_fee_without_vat - float(discount_value)
                        if (not self.customer_payment or self.customer_payment == '0') and (not self.customer_payment or self.customer_payment == '0') and ('customer_payment' not in values or ( 'customer_payment' in values and (not values['customer_payment'] or values['customer_payment'] == '0'))):
                            updated_values['required_from_business'] =  float(discount_value) - self.delivery_fee_without_vat
                            updated_values['customer_payment'] = '0'
                updated_values['returned_discount_type'] = discount_type
            if updated_values:
                self.write(updated_values)
        else:
            return super(olivery_vat_order, self).get_discount_type(discount_type,values,value,reject_reason_ids,pricelist_discount_type)

    def encode_tlv(self, tag, value):
        value_str = str(value or '')
        value_bytes = value_str.encode("utf-8")
        length = len(value_bytes)
        return bytes([tag, length]) + value_bytes
    
    @api.multi
    @api.depends('sequence')
    def create_qr_code_vat(self):
        company = self.env['res.company'].sudo().search([], limit = 1)
        for rec in self:
            if rec.sequence:
                tlv_data = bytearray()
                tlv_data.extend(self.encode_tlv(1, company.name or ''))
                tlv_data.extend(self.encode_tlv(2, company.vat or ''))
                formatted_date = rec.create_date.strftime("%Y-%m-%dT%H:%M:%SZ")
                tlv_data.extend(self.encode_tlv(3, formatted_date or 0.0))
                tlv_data.extend(self.encode_tlv(4, rec.delivery_cost or 0.0))
                tlv_data.extend(self.encode_tlv(5, rec.vat_value or 0.0))

                qr_data = base64.b64encode(bytes(tlv_data)).decode("utf-8")
                
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(qr_data)
                qr.make(fit=True)
                img = qr.make_image()
                
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.vat_qr_code_image=qr_image

    
    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------


    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------