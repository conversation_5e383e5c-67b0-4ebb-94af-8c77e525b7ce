# -*- coding: utf-8 -*-

import json
import logging
import qrcode
import base64
from io import BytesIO
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_vat_vat(models.Model):

    _name = 'olivery_vat.vat'
    _order = "create_date DESC"
    
    _description ="Manage VAT Records for Completed Collections"
    
    passport_id = fields.Char('Passport Id', related='business_id.passport_id',track_visibility="on_change")

    collection_ids = fields.One2many('rb_delivery.multi_print_orders_money_collector', 'vat_record_id', string='Collections')

    total_amount=fields.Float('Total Amount', track_visibility="on_change",compute="compute_fields")

    fixed_total =fields.Float('Fixed Total', track_visibility="on_change")

    business_id = fields.Many2one('rb_delivery.user', 'Business Name',compute="change_orders",store=False)

    sequence = fields.Char('Sequence Number', readonly=True,track_visibility="on_change",copy=False)

    order_count=fields.Float('Order Count', track_visibility="on_change",compute="compute_fields")

    total_delivery_cost=fields.Float('Total Delivery Cost', track_visibility="on_change",compute="compute_fields")

    vat_value=fields.Float('Vat Value', track_visibility="on_change",compute="compute_fields")

    delivery_fee_without_vat=fields.Float('Delivery Fee Without Vat', track_visibility="on_change",compute="compute_fields")

    

    @api.one
    @api.depends('collection_ids')
    def change_orders(self):
        if self.collection_ids and len(self.collection_ids)>0:
            self.business_id=self.collection_ids[0].business_id.id
    
    
    @api.one
    @api.depends('collection_ids')
    def compute_fields(self):
        if self.collection_ids:
            self.business_id = self.collection_ids[0].business_id.id

            total_amount = sum(collection.total_money_collection_cost for collection in self.collection_ids)
            order_count = sum(collection.order_count for collection in self.collection_ids)
            total_delivery_cost = sum(collection.total_delivery_cost for collection in self.collection_ids)
            total_vat_value = sum(collection.vat_value for collection in self.collection_ids)
            total_fee_without_vat = sum(collection.delivery_fee_without_vat for collection in self.collection_ids)
        else:
            total_amount = order_count = total_delivery_cost = total_vat_value = total_fee_without_vat = 0

        values = {
            'total_amount': total_amount,
            'order_count': order_count,
            'total_delivery_cost': total_delivery_cost,
            'vat_value': total_vat_value,
            'delivery_fee_without_vat': total_fee_without_vat,
        }

        self.write(values)
        




    @api.model
    def create(self, vals):
        record = super(olivery_vat_vat, self).create(vals)
        return record

    def get_vat_docs(self, docs):
        grouped_docs = {}
        for doc in docs:
            business_key = doc.business_id.id
            if business_key not in grouped_docs:
                grouped_docs[business_key] = []
            grouped_docs[business_key].append(doc)
        return list(grouped_docs.values())

