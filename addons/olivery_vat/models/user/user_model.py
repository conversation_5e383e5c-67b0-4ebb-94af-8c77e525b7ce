# -*- coding: utf-8 -*-

import json
import logging
import qrcode
import base64
from io import BytesIO
from openerp import models, fields, api
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class olivery_vat_user(models.Model):

    _inherit = 'rb_delivery.user'


    BUSINESS_CATEGORY_SELECTION = [
        ('company', 'Company'),
        ('individual', 'Individual')
    ]


    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
    
    tax_number = fields.Char('Tax Number', track_visibility="on_change")

    commercial_registration = fields.Char('Commercial Registration', track_visibility="on_change")

    business_category = fields.Selection(BUSINESS_CATEGORY_SELECTION, string='Business Category', required=True, default='individual', track_visibility="on_change")

    passport_id = fields.Char('Passport id', track_visibility="on_change")
    
    vat_included = fields.Boolean('VAT Included', default=True)

    vat_subscription_date = fields.Date('Subscription Date', track_visibility="on_change")

    end_vat_subscription_date = fields.Date('End of Subscription', track_visibility="on_change")

    subscription_status = fields.Selection([
        ('active', 'Active'),
        ('ending', 'About to End'),
        ('expired', 'Expired')
    ], string='Subscription Status', store=False,default='active')

    
    
    # ----------------------------------------------------------
    # Function
    # ---------------------------------------------------------- 
    
    @api.multi
    def compute_subscription_status(self):
        for record in self:
            if record.end_vat_subscription_date:
                today = fields.Date.today()
                delta = (record.end_vat_subscription_date - today).days
                status = ''
                if delta > 3:
                    status = 'active'
                elif 0 <= delta <= 3:
                    status = 'ending'
                else:
                    status = 'expired'
                record.write({'subscription_status':status})


    @api.model
    def recompute_subscription_statuses(self):
        users = self.search([('role_code', '=', 'rb_delivery.role_business'),('end_vat_subscription_date','!=',False)])
        users.compute_subscription_status()

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ---------------------------------------------------------- 


    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------