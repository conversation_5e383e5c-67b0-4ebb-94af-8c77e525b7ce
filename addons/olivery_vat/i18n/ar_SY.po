# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_vat
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-08 07:10+0000\n"
"PO-Revision-Date: 2022-06-08 07:10+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "<small>\n"
"									<span>Page</span>\n"
"									<span class=\"page\"/>\n"
"                            of\n"
"									<span class=\"topage\"/>\n"
"								</small> -"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "<span style=\"color:#354058;\">فاتورة ضريبية مبسطة</span>"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "<span style=\"color:#89a3cc;\">Simplified Tax Invoice</span>"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Ammount"
msgstr "المجموع"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Bill to"
msgstr "العميل"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_user__commercial_registration
msgid "Commercial Registration"
msgstr "السجل التجاري"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_order__default_vat
msgid "Default VAT"
msgstr "الضريبة الافتراضية"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_order__delivery_fee_without_vat
msgid "Delivery Fee Without VAT"
msgstr "رسوم التوصيل دون الضريبة"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Description"
msgstr "الوصف"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Invoice Number"
msgstr "رقم الفاتورة"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Logo"
msgstr "الشعار"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Price"
msgstr "سعر"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_order__qr_code
msgid "QR code"
msgstr "رمز QR"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_order__vat_qr_code_image
msgid "Vat QR Code"
msgstr " رمز الضريبة QR"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Qty"
msgstr "الكمية"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Subtotal"
msgstr "المجموع الفرعي"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Supply Date"
msgstr "تاريخ التوريد"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Tax Identification Number"
msgstr "الرقم التعريفي للضريبة"

#. module: olivery_vat
#: model:ir.actions.report,name:olivery_vat.report_rb_delivery_order_tax_invoice
msgid "Tax Invoice"
msgstr "الفاتورة الضريبية"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_user__tax_number
msgid "Tax Number"
msgstr "الرقم الضريبي"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "This QR code is encoded as per ZATCA e-invoicing requirements"
msgstr "رمز الكود مشفر بحسب متطلبات هيئة الزكاة والضريبة والجمارك للفوترة الالكترونية"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Total"
msgstr "الإجمالي"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Total Due"
msgstr "المبلغ المتبقى"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "Total VAT"
msgstr "مجموع الضريبة"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_order__vat_value
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "VAT"
msgstr "ضريبة القيمة المضافة"

#. module: olivery_vat
#: model:ir.model,name:olivery_vat.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""

#. module: olivery_vat
#: model:ir.model,name:olivery_vat.model_rb_delivery_user
msgid "rb_delivery.user"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "اجمالي ضريبة القيمة المضافة"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "الاجمالي"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "الرصيد المستحق"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "السعر"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "العميل"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "القيمة المضافة"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "الكمية"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "المجموع"
msgstr "Total"

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "المجموع الفرعي"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "الوصف"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "الى"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "تاريخ التوريد"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "رقم التسجيل الضريبي"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "رقم الفاتورة"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "رمز الإستجابة السريعة مشفر بحسب متطلبات هيئة الزكاة والضريبة والجمارك للفوترة الإلكترونية"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "شحن"
msgstr ""

#. module: olivery_vat
#: model_terms:ir.ui.view,arch_db:olivery_vat.order_tax_invoice
msgid "من"
msgstr ""


#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_user__end_vat_subscription_date
msgid "End of Subscription"
msgstr "نهاية الاشتراك"

#. module: olivery_vat
#: model:ir.ui.menu,name:olivery_vat.menu_clients_subscription_ending
msgid "Ending Vat Subscriptions"
msgstr "الاشتراكات الضريبية التي أوشكت على الانتهاء"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_user__vat_subscription_date
msgid "Subscription Date"
msgstr "تاريخ الاشتراك"

#. module: olivery_vat
#: model:ir.model.fields,field_description:olivery_vat.field_rb_delivery_user__subscription_status
msgid "Subscription Status"
msgstr "حالة الاشتراك"

#. module: olivery_vat
#: selection:rb_delivery.user,subscription_status:0
msgid "About to End"
msgstr "على وشك الانتهاء"

#. module: olivery_vat
#: selection:rb_delivery.user,subscription_status:0
msgid "Active"
msgstr "نشط"

#. module: olivery_vat
#: selection:rb_delivery.user,subscription_status:0
msgid "Expired"
msgstr "منتهي"

