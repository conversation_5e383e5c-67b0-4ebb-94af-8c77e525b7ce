# -*- coding: utf-8 -*-
{
    'name': "olivery_vat",
    'summary': """
        Olivery VAT App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.0.36',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery','olivery_cargo'],

    # always loaded
    'data': [
        # 'models/order/order_view.xml',
        'data/order_sequence.xml',
        'data/compute_subscription_cron.xml',
        'security/ir.model.access.csv',
        'demo/client_conf.xml',
        'models/order/order_view.xml',
        'models/user/user_view.xml',
        'models/vat_collection/vat_view.xml',
        'views/print/tax_invoice.xml',
        'views/print/collection_tax_invoice.xml',
        'views/print/money_collection_invoice_receipt.xml',
        'views/module_view.xml',
        'models/collection/collection_view.xml',
        'views/print/vat_report.xml',
        'views/print/collection_tax_summary.xml',
        'demo/mobile_compute_functions_view.xml'



    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}
