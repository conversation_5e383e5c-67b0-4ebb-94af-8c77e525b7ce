<odoo >
    <data>
        <record id="default_channels_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Channels</field>
            <field name="model_name">storex_modules.channel</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/channels.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="sequence">1</field>
        </record>

        <record id="wordpress_channels_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Wordpress Channel</field>
            <field name="model_name">storex_modules.channel</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/wordpress_channel.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="sequence">2</field>
        </record>

        <record id="shopify_channels_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Shopify Channel</field>
            <field name="model_name">storex_modules.channel</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/shopify_channel.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="sequence">3</field>
        </record>

        <record id="ecommerce_channels_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">eCommerce Channel</field>
            <field name="model_name">storex_modules.channel</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/ecommerce_channel.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="sequence">3</field>
        </record>
        
        <record id="default_palestine_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/ps_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>
            <field name="sequence">2</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_jordan_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Jordan Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/jordan_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.jordan_tag_demo_data')])]"></field>
            <field name="sequence">3</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_kurdistan_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Kurdistan Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/kurdistan_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.kurdistan_tag_demo_data')])]"></field>
            <field name="sequence">39</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_egypt_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Egypt Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/egypt_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.egypt_tag_demo_data')])]"></field>
            <field name="sequence">4</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_saudi_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Saudi Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/saudi_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.saudi_tag_demo_data')])]"></field>
            <field name="sequence">5</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_iraq_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Iraq Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/iraq_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'}),(0, 0, {'type':'add_new_pricelist'}),(0, 0, {'type':'edit_company_info'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.iraq_tag_demo_data')])]"></field>
            <field name="sequence">6</field>
            <field name="force_override">True</field>
        </record>

        <record id="default_countries_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Countries</field>
            <field name="model_name">rb_delivery.country</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/countries.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data'),ref('olivery_demo_data.jordan_tag_demo_data'),ref('olivery_demo_data.egypt_tag_demo_data'),ref('olivery_demo_data.iraq_tag_demo_data'),ref('olivery_demo_data.saudi_tag_demo_data'),ref('olivery_demo_data.kurdistan_tag_demo_data')])]"></field>
            <field name="sequence">7</field>
        </record>

        <record id="default_palestine_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/ps_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>
            
            <field name="sequence">8</field>
        </record>

           <record id="default_palestine_areas_hebrew_language_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine Areas (Hebrew Language)</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/ps_he_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>
            
            <field name="sequence">88</field>
        </record>

        <record id="default_palestine_pricelist_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine pricelist</field>
            <field name="model_name">rb_delivery.pricelist_item</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/ps_pricelist.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["from_area","to_area", "price", "pricelist_id"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_areas_demo_data')])]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>

            <field name="sequence">8</field>
        </record>

        <record id="default_jordan_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Jordan Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/jordan_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.jordan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="sequence">9</field>
        </record>

         <record id="default_jordan_sub_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Jordan Sub Areas</field>
            <field name="model_name">rb_delivery.sub_area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/jordan_sub_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code", "parent_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.jordan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data'), ref('olivery_demo_data.default_jordan_areas_demo_data')])]"></field>
            <field name="ignore_failure">True</field>
            <field name="sequence">4</field>
        </record>

        <record id="default_kurdistan_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Kurdistan Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/kurdistan_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.kurdistan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="sequence">40</field>
        </record>

        <record id="default_kurdistan_sub_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Kurdistan Sub Areas</field>
            <field name="model_name">rb_delivery.sub_area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/kurdistan_sub_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code", "parent_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.kurdistan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data'), ref('olivery_demo_data.default_kurdistan_areas_demo_data')])]"></field>
            <field name="ignore_failure">True</field>
            <field name="sequence">41</field>
        </record>

        <record id="default_egypt_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Egypt Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/egypt_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.egypt_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="sequence">10</field>
        </record>

        <record id="default_iraq_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Iraq Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/iraq_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.iraq_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="sequence">11</field>
        </record>
        
        <record id="default_iraq_sub_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Iraq Sub Areas</field>
            <field name="model_name">rb_delivery.sub_area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/iraq_sub_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code", "parent_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.iraq_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data'), ref('olivery_demo_data.default_iraq_areas_demo_data')])]"></field>
            <field name="ignore_failure">True</field>
            <field name="sequence">60</field>
        </record>

        <record id="default_saudi_arabia_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Saudi Arabia Areas</field>
            <field name="model_name">rb_delivery.area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/saudi_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.saudi_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data')])]"></field>
            <field name="sequence">12</field>
        </record>

        
        <record id="default_palestine_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/ps_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_areas_demo_data'),ref('olivery_demo_data.default_palestine_config_demo_data')])]"></field>
            <field name="sequence">13</field>
        </record>

        <record id="default_jordan_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Jordan Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/jordan_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.jordan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_jordan_areas_demo_data'),ref('olivery_demo_data.default_jordan_config_demo_data')])]"></field>
            <field name="sequence">14</field>
        </record>

        <record id="default_kurdistan_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Kurdistan Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/kurdistan_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.kurdistan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_kurdistan_areas_demo_data'),ref('olivery_demo_data.default_kurdistan_config_demo_data')])]"></field>
            <field name="sequence">42</field>
        </record>

        <record id="default_egypt_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Egypt Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/egypt_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.egypt_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_egypt_areas_demo_data'),ref('olivery_demo_data.default_egypt_config_demo_data')])]"></field>
            <field name="sequence">15</field>
        </record>
        <record id="default_iraq_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Iraq Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/iraq_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.iraq_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_iraq_areas_demo_data'),ref('olivery_demo_data.default_iraq_config_demo_data')])]"></field>
            <field name="sequence">16</field>
        </record>
        <record id="default_saudi_arabia_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Saudi Arabia Users</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'confirm_users'})]"></field>
            <field file="olivery_demo_data/static/json_data/saudi_users.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.saudi_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_saudi_arabia_areas_demo_data'),ref('olivery_demo_data.default_saudi_config_demo_data')])]"></field>
            <field name="sequence">17</field>
        </record>
        <record id="default_egypt_users_demo_data_storex" model="olivery_demo_data.demo_package">
            <field name="name">Egypt Users For Storex</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/egypt_users_storex.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.egypt_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_egypt_areas_demo_data'),ref('olivery_demo_data.default_egypt_config_demo_data')])]"></field>
            <field name="sequence">18</field>
        </record>
        <record id="default_jordan_users_demo_data_storex" model="olivery_demo_data.demo_package">
            <field name="name">Jordan Users For Storex</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/jordan_users_storex.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.jordan_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_jordan_areas_demo_data'),ref('olivery_demo_data.default_jordan_config_demo_data')])]"></field>
            <field name="sequence">19</field>
        </record>
        <record id="default_saudi_users_demo_data_storex" model="olivery_demo_data.demo_package">
            <field name="name">Saudi Users For Storex</field>
            <field name="model_name">rb_delivery.user</field>
            <field name="module_name">olivery_storex_menu</field>
            <field file="olivery_demo_data/static/json_data/saudi_users_storex.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["email","mobile_number"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.saudi_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_saudi_arabia_areas_demo_data'),ref('olivery_demo_data.default_saudi_config_demo_data')])]"></field>
            <field name="sequence">20</field>
        </record>

        <!-- Add Conflicting Packages -->
        
        <record id="default_palestine_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_jordan_users_demo_data'),ref('olivery_demo_data.default_egypt_users_demo_data'),ref('olivery_demo_data.default_iraq_users_demo_data'),ref('olivery_demo_data.default_saudi_arabia_users_demo_data'),ref('olivery_demo_data.default_kurdistan_users_demo_data')])]"></field>
        </record>
        <record id="default_jordan_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_users_demo_data'),ref('olivery_demo_data.default_saudi_arabia_users_demo_data'),ref('olivery_demo_data.default_egypt_users_demo_data'),ref('olivery_demo_data.default_iraq_users_demo_data'),ref('olivery_demo_data.default_kurdistan_users_demo_data')])]"></field>
        </record>
        <record id="default_egypt_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_users_demo_data'),ref('olivery_demo_data.default_saudi_arabia_users_demo_data'),ref('olivery_demo_data.default_iraq_users_demo_data'),ref('olivery_demo_data.default_jordan_users_demo_data'),ref('olivery_demo_data.default_kurdistan_users_demo_data')])]"></field>
        </record>
        <record id="default_iraq_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_users_demo_data'),ref('olivery_demo_data.default_saudi_arabia_users_demo_data'),ref('olivery_demo_data.default_jordan_users_demo_data'),ref('olivery_demo_data.default_egypt_users_demo_data'),ref('olivery_demo_data.default_kurdistan_users_demo_data')])]"></field>
        </record>
        <record id="default_saudi_arabia_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_users_demo_data'),ref('olivery_demo_data.default_iraq_users_demo_data'),ref('olivery_demo_data.default_jordan_users_demo_data'),ref('olivery_demo_data.default_egypt_users_demo_data'),ref('olivery_demo_data.default_kurdistan_users_demo_data')])]"></field>
        </record>
        <record id="default_kurdistan_users_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_palestine_users_demo_data'),ref('olivery_demo_data.default_saudi_arabia_users_demo_data'),ref('olivery_demo_data.default_egypt_users_demo_data'),ref('olivery_demo_data.default_iraq_users_demo_data'),ref('olivery_demo_data.default_jordan_users_demo_data')])]"></field>
        </record>
        







        

        <!-- <record id="default_stable_status_actions_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Status Actions</field>
            <field name="model_name">rb_delivery.status_action</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_status_actions.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="sequence">21</field>
        </record> -->

        <!-- <record id="default_stable_mobile_actions_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Status Mobile Actions</field>
            <field name="model_name">rb_delivery.status_mobile_action</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_mobile_actions.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="sequence">22</field>
        </record> -->

        <!-- <record id="default_stable_pre_actions_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Status Pre Actions</field>
            <field name="model_name">rb_delivery.status_pre_action</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_pre_actions.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="sequence">23</field>
        </record> -->

        <!-- <record id="default_stable_related_fields_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Status Related_fields</field>
            <field name="model_name">rb_delivery.status_related_field</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_related_fields.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","action_type"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="sequence">24</field>
        </record> -->


        <record id="default_stable_statuses_cargo_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Statuses For Cargo</field>
            <field name="model_name">rb_delivery.status</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'add_security_fields'})]"></field>
            <field file="olivery_demo_data/static/json_data/stable_statuses.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","status_type"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            
            <!-- <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_status_actions_demo_data'),ref('olivery_demo_data.default_stable_mobile_actions_demo_data'),ref('olivery_demo_data.default_stable_pre_actions_demo_data'),ref('olivery_demo_data.default_stable_related_fields_demo_data')])]"></field> -->
            <field name="sequence">25</field>
        </record>

        <record id="default_storex_statuses_cargo_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Storex Statuses</field>
            <field name="model_name">rb_delivery.status</field>
            <field name="module_name">olivery_storex_menu</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'modify_statuses'}),(0, 0, {'type':'add_storex_security_fields'})]"></field>
            <field file="olivery_demo_data/static/json_data/storex_statuses.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","status_type"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="sequence">26</field>
        </record>

        <!-- <record id="default_stable_field_security_cargo_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Field Security For Cargo</field>
            <field name="model_name">rb_delivery.status_field_security</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_field_security.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">27</field>
        </record> -->

        <!-- <record id="default_stable_config_types_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Client Configuration Types</field>
            <field name="model_name">rb_delivery.client_configuration_type</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/client_config_types.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="sequence">28</field>
            <field name="force_override">True</field>
        </record> -->

        <!-- <record id="default_stable_config_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Stable Client Configuration</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/stable_client_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">29</field>
            <field name="force_override">True</field>
        </record> -->

        <record id="default_vhub_config_d_to_d_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">V-Hub Delivery-Delivery Configuration</field>
            <field name="model_name">rb_delivery.vhub_field_security</field>
            <field name="module_name">olivery_vhub</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'delivery_users_status_map'}),(0, 0, {'type':'modify_statuses'}),(0, 0, {'type':'add_security_fields'})]"></field>
            <field file="olivery_demo_data/static/json_data/d_to_d_vhub_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.vhub_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">30</field>
            <field name="prevent_override">True</field>
        </record>

        <record id="default_vhub_config_s_to_d_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">V-Hub Storex-Delivery Configuration</field>
            <field name="model_name">rb_delivery.vhub_field_security</field>
            <field name="module_name">olivery_vhub</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'delivery_users_status_map'}),(0, 0, {'type':'modify_statuses'}),(0, 0, {'type':'add_security_fields'}),(0, 0, {'type':'set_vhub_config'})]"></field>
            <field file="olivery_demo_data/static/json_data/s_to_d_vhub_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">31</field>
        </record>



        <record id="default_dashboard_compnents_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Storex Mobile Dashboard Items</field>
            <field name="model_name">rb_delivery.mobile_component_item</field>
            <field name="module_name">olivery_dynamic_dashboard</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'setup_mobile_dashboard'})]"></field>
            <field file="olivery_demo_data/static/json_data/dashboard_components.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">32</field>
        </record>

        <record id="default_delivery_notifications_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Delivery Notifications</field>
            <field name="model_name">rb_delivery.action</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/delivery_notifications.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","action_type","order_state"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">33</field>
        </record>

        <record id="default_storex_notifications_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Storex Notifications</field>
            <field name="model_name">rb_delivery.action</field>
            <field name="module_name">storex_modules</field>
            <field file="olivery_demo_data/static/json_data/storex_notifications.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","action_type","storex_order_state"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_storex_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">33</field>
        </record>

        <record id="default_delivery_order_filters_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Delivery Order Filters</field>
            <field name="model_name">ir.filters</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/delivery_order_filters.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","model_id","action_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">33</field>
        </record>

        <record id="shopify_configs_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Shopify Configurations</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">olivery_shopify</field>
            <field file="olivery_demo_data/static/json_data/shopify_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'modify_statuses'}),(0, 0, {'type':'add_security_fields'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_storex_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">34</field>
            <field name="force_override">True</field>
        </record>

        <record id="wordpress_configs_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Wordpress Configurations</field>
            <field name="model_name">rb_delivery.client_configuration</field>
            <field name="module_name">olivery_wordpress</field>
            <field file="olivery_demo_data/static/json_data/wordpress_config.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["key"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'modify_statuses'}),(0, 0, {'type':'add_security_fields'})]"></field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_storex_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">35</field>
            <field name="force_override">True</field>
        </record>
        
        <record id="install_web_barcode" model="olivery_demo_data.demo_package">
            <field name="name">Install Web Barcode</field>
            <field name="module_name">olivery_web_barcode</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="sequence">34</field>
        </record>

        <record id="storex_samples_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Storex Samples</field>
            <field name="model_name">storex_modules.product</field>
            <field name="module_name">storex_modules</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'create_user'}),(0, 0, {'type':'add_default_warehouse'})]"></field>
            <field name="actions_first">True</field>
            <field file="olivery_demo_data/static/json_data/storex_samples.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="sequence">36</field>
        </record>

        <record id="whatsapp_messages_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Whatsapp Messages</field>
            <field name="model_name">rb_delivery.whatsapp_message</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/whatsapp_messages.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="sequence">37</field>
        </record>

        <record id="default_olivery_dashboard_compnents_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Olivery Mobile Dashboard Items</field>
            <field name="model_name">rb_delivery.mobile_component_item</field>
            <field name="module_name">olivery_dynamic_dashboard</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'setup_mobile_dashboard'})]"></field>
            <field file="olivery_demo_data/static/json_data/olivery_dashboard_components.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">35</field>
        </record>
        
        <record id="default_reject_reasons_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Reject Reasons</field>
            <field name="model_name">rb_delivery.reject_reason</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/reject_reasons.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.stable_tag_demo_data'),ref('olivery_demo_data.staging_tag_demo_data')])]"></field>
            <field name="sequence">38</field>
        </record>

        <record id="default_united_theme_dashboard_compnents_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">United Theme Dashboard Compnents</field>
            <field name="model_name">rb_delivery.mobile_component_item</field>
            <field name="module_name">olivery_dynamic_dashboard</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'setup_mobile_dashboard'})]"></field>
            <field file="olivery_demo_data/static/json_data/united_theme_components.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">40</field>
        </record>
        <record id="default_tornado_theme_dashboard_compnents_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Tornado Theme Dashboard Compnents</field>
            <field name="model_name">rb_delivery.mobile_component_item</field>
            <field name="module_name">olivery_dynamic_dashboard</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'setup_mobile_dashboard'})]"></field>
            <field file="olivery_demo_data/static/json_data/tornado_theme_components.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["group_id","name"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.cargo_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_stable_statuses_cargo_demo_data')])]"></field>
            <field name="sequence">40</field>
        </record>

        <!-- Mobile Form Creator Demo Data-->
        <record id="mobile_form_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Form Creator</field>
            <field name="model_name">rb_delivery.mobile_form_creator</field>
            <field name="module_name">rb_delivery,olivery_recepient</field>
            <field file="olivery_demo_data/static/json_data/mobile_form_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name", "group_id"]</field>
            <field name="sequence">41</field>
        </record>

        <record id="mobile_form_input_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Form Input</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_form_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'add_mapping_relations'})]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_form_creator_demo_data')])]"></field>
            <field name="sequence">42</field>
        </record>

        <record id="online_payment_inputs_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Online Payment Inputs</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">olivery_advance_calculations,olivery_surepay</field>
            <field name="actions_first">True</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'edit_compute_functions'}),(0, 0, {'type':'add_card_items'})]"></field>
            <field file="olivery_demo_data/static/json_data/online_payment_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_form_creator_demo_data')])]"></field>
            <field name="sequence">42</field>
        </record>

        <!-- Mobile Card Creator Demo Data -->
        <record id="mobile_card_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Card Creator</field>
            <field name="model_name">rb_delivery.mobile_card_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_card_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="sequence">43</field>
        </record>

        <record id="mobile_card_creator_demo_data_movify" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Card Creator Movify</field>
            <field name="model_name">rb_delivery.mobile_card_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_card_creator_movify.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="sequence">44</field>
        </record>

        <record id="mobile_card_item_functions_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Items Functions</field>
            <field name="model_name">rb_delivery.card_model_functions</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/card_item_functions.json" type="base64" name="data"></field>
            <field name="unique_field_names">["technical_name"]</field>
            <field name="sequence">44</field>
        </record>

        <record id="mobile_card_item_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Card Item</field>
            <field name="model_name">rb_delivery.mobile_card_item</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'add_action_items_to_card'})]"></field>
            <field file="olivery_demo_data/static/json_data/mobile_card_item.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_card_creator_demo_data'),ref('olivery_demo_data.mobile_card_item_functions_demo_data')])]"></field>
            <field name="sequence">45</field>
        </record>

        <record id="mobile_card_item_demo_data_movify" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Card Item Movify</field>
            <field name="model_name">rb_delivery.mobile_card_item</field>
            <field name="module_name">rb_delivery</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'add_action_items_to_card'})]"></field>
            <field file="olivery_demo_data/static/json_data/mobile_card_item_movify.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_card_creator_demo_data_movify')])]"></field>
            <field name="sequence">45</field>
        </record>

        <record id="mobile_order_detail_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Order Detail</field>
            <field name="model_name">rb_delivery.mobile_order_detail</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_order_detail.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="sequence">44</field>
        </record>

        <record id="mobile_order_detail_button_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Order Detail Button</field>
            <field name="model_name">rb_delivery.mobile_order_detail_button</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_order_detail_button.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_order_detail_demo_data')])]"></field>
            <field name="sequence">44</field>
        </record>

        <record id="mobile_order_detail_card_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Order Detail Card</field>
            <field name="model_name">rb_delivery.mobile_order_detail_card</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_order_detail_card.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_order_detail_demo_data')])]"></field>
            <field name="sequence">44</field>
        </record>

        <record id="mobile_order_detail_card_item_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Order Detail Card Item</field>
            <field name="model_name">rb_delivery.mobile_order_detail_card_item</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_order_detail_card_item.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="sequence">44</field>
        </record>      

        <record id="mobile_collection_sheet_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Sheet Creator</field>
            <field name="model_name">rb_delivery.mobile_collection_sheet_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_collection_sheet_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["group_id"]</field>
            <field name="sequence">46</field>
        </record>

        <record id="mobile_setting_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Settings Creator</field>
            <field name="model_name">rb_delivery.mobile_setting_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_setting_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["group_id"]</field>
            <field name="sequence">47</field>
        </record>

        <record id="mobile_default_search_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile default search</field>
            <field name="model_name">rb_delivery.mobile_default_search</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_default_search.json" type="base64" name="data"></field>
            <field name="unique_field_names">["model"]</field>
            <field name="sequence">48</field>
        </record>

        <record id="mobile_filter_fields_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Filter Fields</field>
            <field name="model_name">rb_delivery.mobile_filter_fields</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_filter_fields.json" type="base64" name="data"></field>
            <field name="unique_field_names">["model", "filter_type","group_id"]</field>
            <field name="sequence">49</field>
        </record>

        <record id="create_order_button_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Create Order Button</field>
            <field name="model_name">rb_delivery.create_order_button_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/create_order_button_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["group_id"]</field>
            <field name="sequence">50</field>
        </record>

        <record id="mobile_action_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Action Creator</field>
            <field name="model_name">rb_delivery.mobile_action_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_action_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["model","group_id"]</field>
            <field name="sequence">51</field>
        </record>

        <record id="default_palestine_sub_areas_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Palestine Sub Areas</field>
            <field name="model_name">rb_delivery.sub_area</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/palestine_sub_areas.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["name","code", "parent_id"]</field>
            <field name="type_tags" eval="[(6, 0, [ref('olivery_demo_data.delivery_tag_demo_data'),ref('olivery_demo_data.cargo_tag_demo_data'),ref('olivery_demo_data.storex_tag_demo_data')])]"></field>
            <field name="release_tags" eval="[(6, 0, [ref('olivery_demo_data.staging_tag_demo_data'),ref('olivery_demo_data.stable_tag_demo_data')])]"></field>
            <field name="country_tags" eval="[(6, 0, [ref('olivery_demo_data.palestine_tag_demo_data')])]"></field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.default_countries_demo_data'), ref('olivery_demo_data.default_palestine_areas_demo_data')])]"></field>
            <field name="ignore_failure">True</field>
            <field name="sequence">52</field>
        </record>

        <record id="pickup_card_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Pickup Card Creator</field>
            <field name="model_name">rb_delivery.mobile_card_creator</field>
            <field name="module_name">olivery_pickup_request</field>
            <field file="olivery_demo_data/static/json_data/pickup_card_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="sequence">53</field>
        </record>

        <record id="pickup_card_item_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Pickup Card Item</field>
            <field name="model_name">rb_delivery.mobile_card_item</field>
            <field name="module_name">olivery_pickup_request</field>
            <field file="olivery_demo_data/static/json_data/pickup_card_item.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.pickup_card_creator_demo_data'),ref('olivery_demo_data.mobile_card_item_functions_demo_data')])]"></field>
            <field name="sequence">54</field>
        </record>

        <record id="pickup_mobile_form_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Pickup Form Creator</field>
            <field name="model_name">rb_delivery.mobile_form_creator</field>
            <field name="module_name">olivery_pickup_request</field>
            <field file="olivery_demo_data/static/json_data/pickup_form_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name", "group_id"]</field>
            <field name="sequence">55</field>
        </record>

        <record id="pickup_mobile_form_input_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Pickup Form Input</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">olivery_pickup_request</field>
            <field file="olivery_demo_data/static/json_data/pickup_form_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.pickup_mobile_form_creator_demo_data')])]"></field>
            <field name="sequence">56</field>
        </record>

        <!-- Quick order Form Demo Data-->
        <record id="quick_order_form_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Quick Order Form</field>
            <field name="model_name">rb_delivery.mobile_form_creator</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/quick_order_form_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name", "group_id"]</field>
            <field name="sequence">57</field>
        </record>

        <record id="quick_order_form_input_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Quick Order Form Inputs</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/quick_order_form_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.quick_order_form_creator_demo_data')])]"></field>
            <field name="sequence">58</field>
        </record>

        <record id="add_client_mobile_form_input_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Add client in Forms</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">olivery_recepient</field>
            <field name="additional_actions" eval="[(6,0,[]),(0, 0, {'type':'add_mapping_relations'})]"></field>
            <field file="olivery_demo_data/static/json_data/add_client_form_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.mobile_form_creator_demo_data')])]"></field>
            <field name="sequence">59</field>
        </record>

        <record id="tasks_card_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Tasks Card Creator</field>
            <field name="model_name">rb_delivery.mobile_card_creator</field>
            <field name="module_name">olivery_tasks</field>
            <field file="olivery_demo_data/static/json_data/tasks_card_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name"]</field>
            <field name="sequence">61</field>
        </record>

        <record id="tasks_card_item_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Tasks Card Item</field>
            <field name="model_name">rb_delivery.mobile_card_item</field>
            <field name="module_name">olivery_tasks</field>
            <field file="olivery_demo_data/static/json_data/tasks_card_item.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.tasks_card_creator_demo_data'),ref('olivery_demo_data.mobile_card_item_functions_demo_data')])]"></field>
            <field name="sequence">62</field>
        </record>

        <record id="tasks_mobile_form_creator_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Tasks Form Creator</field>
            <field name="model_name">rb_delivery.mobile_form_creator</field>
            <field name="module_name">olivery_tasks</field>
            <field file="olivery_demo_data/static/json_data/tasks_form_creator.json" type="base64" name="data"></field>
            <field name="unique_field_names">["name", "group_id"]</field>
            <field name="sequence">63</field>
        </record>

        <record id="tasks_mobile_form_input_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Tasks Form Input</field>
            <field name="model_name">rb_delivery.mobile_form_input</field>
            <field name="module_name">olivery_tasks</field>
            <field file="olivery_demo_data/static/json_data/tasks_form_inputs.json" type="base64" name="data"></field>
            <field name="unique_field_names">["unique_field_name"]</field>
            <field name="dependency_packages" eval="[(6, 0, [ref('olivery_demo_data.tasks_mobile_form_creator_demo_data')])]"></field>
            <field name="sequence">64</field>
        </record>

        <record id="default_print_demo_data" model="olivery_demo_data.demo_package">
            <field name="name">Mobile Default Print</field>
            <field name="model_name">rb_delivery.mobile_default_print</field>
            <field name="module_name">rb_delivery</field>
            <field file="olivery_demo_data/static/json_data/mobile_default_print.json" type="base64"  name="data"></field>
            <field name="unique_field_names">["report_id", "model", "group_id"]</field>
            <field name="sequence">65</field>
        </record>

        <!-- Add Conflicting Packages -->
        <record id="delivered_location_status_action_driver_form_input" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_olivery_dashboard_compnents_demo_data')])]"></field>
        </record>

        <record id="default_olivery_dashboard_compnents_demo_data" model="olivery_demo_data.demo_package">
            <field name="conflicting_packages" eval="[(6, 0, [ref('olivery_demo_data.default_dashboard_compnents_demo_data')])]"></field>
        </record>

    </data>
</odoo>