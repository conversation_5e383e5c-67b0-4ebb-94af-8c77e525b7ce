# -*- coding: utf-8 -*-
import io
from openerp import models, fields, api,_
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.modules.module import get_module_resource
import json

import os

import logging

import base64
_logger = logging.getLogger(__name__)

class olivery_demo_data_demo_creator(models.TransientModel):

    _name = 'olivery_demo_data.demo_creator'
    # ----------------------------------------------------------------------
    # Compute and Default functions
    # ----------------------------------------------------------------------

    def get_data_from_file(self,file):
        if not file:
            return []
        try:
            with io.BytesIO(base64.b64decode(file)) as json_data:
                data = json.loads(json_data.read(),strict=False)
            return data
        except Exception as e:
            raise ValidationError(_("Error in json file"))
    
    def _get_active_ids(self):
        recs = self.env['olivery_demo_data.demo_package'].browse(self._context.get('active_ids'))
        return [[6,0,recs.ids]]

    @api.depends('demo_package_ids')
    def _is_modules_need_installation(self):
        if len(self.demo_package_ids)>0:
            for package in self.demo_package_ids:
                module_names = package.module_name.split(',')
                for module_name in module_names:
                    module=self.env['ir.module.module'].sudo().search([['name','=',module_name]])
                    if module.state != 'installed':
                        self.need_to_install_modules=True
                        return
            self.need_to_install_modules=False
        else:
            self.need_to_install_modules=False

    def default_additional_actions(self):
        action_ids=[]
        for package in self.env['olivery_demo_data.demo_package'].browse(self._context.get('active_ids')):
            actions_ids=self.env['olivery_demo_data.additional_actions'].sudo().search([['id','in',package.additional_actions.ids]]).ids
            action_ids=action_ids+actions_ids
        return [[6,0,action_ids]]
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    demo_package_ids = fields.Many2many(comodel_name = 'olivery_demo_data.demo_package', string='Demo Package', relation = 'demo_packages_demo_creator', column1 = 'creator_id', column2 = 'package_id', default=_get_active_ids)

    override_existing = fields.Boolean('Override Existing')

    modules = fields.Many2many(comodel_name = 'ir.module.module', string='Modules', relation = 'demo_packages_module', column1 = 'module_id', column2 = 'creator_id', compute="_get_required_modules", store=True)

    need_to_install_modules = fields.Boolean(compute="_is_modules_need_installation",store=True)

    additional_actions = fields.Many2many(comodel_name = 'olivery_demo_data.additional_actions', string='Additional Actions', relation = 'demo_packages_action', column1 = 'action_id', column2 = 'creator_ids', default=default_additional_actions, store=True,readonly=False)

    actions_length = fields.Integer(compute="_get_additional_actions",store=True)

    show_colors = fields.Boolean(compute = "_get_additional_actions",store=True)

    primary_color = fields.Char('Primary Color',default="#FFFFFF")

    secondry_color = fields.Char('Secondry Color',default="#FFFFFF")

    @api.one
    @api.depends('demo_package_ids')
    def _get_additional_actions(self):
        action_ids=[]
        if len(self.demo_package_ids)>0:
            for package in self.demo_package_ids:
                actions=self.env['olivery_demo_data.additional_actions'].sudo().search([['id','in',package.additional_actions.ids]])
                action_ids=action_ids+actions.ids
                if len(actions.filtered(lambda x:x.type=="setup_mobile_dashboard"))>0:
                    self.show_colors=True
            self.additional_actions = [[6,0,action_ids]]
            self.actions_length=len(action_ids)

    @api.depends('demo_package_ids')
    def _get_required_modules(self):
        module_ids=[]
        if len(self.demo_package_ids)>0:
            for package in self.demo_package_ids:
                module_names = package.module_name.split(',')
                for module_name in module_names:
                    module_id=self.env['ir.module.module'].sudo().search([['name','=',module_name]]).id
                    module_ids.append(module_id)
            self.modules = [[6,0,module_ids]]

    def create_demo_data(self):
        self = self.with_context(lang='en_US')
        if self.need_to_install_modules:
            return self.install_modules()
        sorted_demo_packages = self.check_depandency_packages()
        self.check_conflict_packages()
        for package_id in sorted_demo_packages:
            
            if len(package_id.additional_actions)>0:
                if package_id.actions_first:
                    self.do_additional_actions(package_id)
                    self.create_without_action(package_id)
                else:
                    self.create_without_action(package_id)
                    self.do_additional_actions(package_id)
            else:
                self.create_without_action(package_id)
            package_id.write({'status':'installed'})

    def create_without_action(self,package_id):
        data = self.get_data_from_file(package_id.data)
        for record in list(filter(lambda x:'for_action' not in x,data)):
            domain=[]
            for field_name in json.loads(package_id.unique_field_names):
                field = self.env['ir.model.fields'].search([['model','=',package_id.model_name],['name','=', field_name]])
                domain_value = self.get_record_value(field,record,field_name,package_id)
                domain.append([field_name,'=',domain_value])

            active_field = self.env['ir.model.fields'].search([['model','=',package_id.model_name],['name','=', 'active']])
            if active_field:
                domain = domain+['|',['active','=',True],['active','=',False]]
            existing_record=self.env[package_id.model_name].search(domain,limit=1)
            for rec in record:                        
                
                field = self.env['ir.model.fields'].search([['model','=',package_id.model_name],['name','=', rec]])
                record_value=self.get_record_value(field,record,rec,package_id)
                if record_value=="continue;":
                    continue
                record[rec]=record_value
            
            if (self.override_existing or package_id.force_override) and not package_id.prevent_override and existing_record and existing_record.id:
                existing_record.write(record)
            elif not existing_record and not existing_record.id:
                self.env.cr.execute("SAVEPOINT demo_trans")
                try:
                    self.env[package_id.model_name].create(record)
                except Exception as e:
                    if package_id.ignore_failure:
                        self.env.cr.execute("ROLLBACK TO SAVEPOINT demo_trans")
                        pass
                    else:
                        self.env.cr.execute("ROLLBACK TO SAVEPOINT demo_trans")
                        raise ValidationError(e)
                finally:
                    self.env.cr.execute("RELEASE SAVEPOINT demo_trans")

            elif existing_record and not (self.override_existing or package_id.force_override) and not package_id.prevent_override:
                record = self.split_many2many(record,package_id)
                existing_record.write(record)
                
    def split_many2many(self,record,package_id):
        new_record={}
        for rec in record:
            field = self.env['ir.model.fields'].search([['model','=',package_id.model_name],['name','=', rec]])
            if field.ttype == "many2many" or field.ttype == "one2many":
                new_record[rec] = record[rec]
        return new_record
    def do_additional_actions(self,package_id):
        for action in package_id.additional_actions:
            data = self.get_data_from_file(package_id.data)
            status_map_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "delivery_users_status_map"]
            status_modification_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "modify_statuses"]
            add_security_fields_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_security_fields"]
            set_vhub_config_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "set_vhub_config"]
            add_storex_security_fields_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_storex_security_fields"]
            confirm_users_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "confirm_users"]
            mobile_dashboard_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "setup_mobile_dashboard"]
            edit_mobile_functions_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "edit_mobile_functions"]
            add_card_items_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_card_items"]
            add_mapping_relations_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_mapping_relations"]
            add_action_items_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_action_items_to_card"]
            new_pricelist_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_new_pricelist"]
            company_info_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "edit_company_info"]
            create_user_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "create_user"]
            add_default_warehouse_records = [{k: v for k, v in record.items() if k != 'for_action'} for record in data if 'for_action' in record and record['for_action'] == "add_default_warehouse"]

            if len(status_map_records)>0 and action.type == "delivery_users_status_map":
                status_maps=[]
                for record in status_map_records:
                    domain=[]       
                    domain.append(['name','=',record["state_id"]])
                    domain.append(['status_type','=','olivery_order'])
                    record["state_id"]=False
                    record["state_id"]=self.env["rb_delivery.status"].search(domain).id
                    if record["state_id"]:
                        status_maps.append([0,0,record])
                for user in action.users:
                    user.write({'state_map_ids':[[6,0,[]]]+status_maps})
            elif len(status_modification_records)>0 and action.type == "modify_statuses":
                for record in status_modification_records:
                    status = self.env['rb_delivery.status'].search([['status_type','=',record['status_type']],['name','=',record['name']]])
                    if status:
                        for rec in record:                        
                            field = self.env['ir.model.fields'].search([['model','=','rb_delivery.status'],['name','=', rec]])
                            record_value=self.get_record_value(field,record,rec,package_id)
                            if record_value=="continue;":
                                continue
                            record[rec]=record_value
                        status.write(record)

            elif len(add_security_fields_records)>0 and action.type == "add_security_fields":
                for record in add_security_fields_records:
                    
                    for rec in record:                        
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.status_field_security'],['name','=', rec]])
                        record_value=self.get_record_value(field,record,rec,package_id,override=True)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    self.check_exist_security_field(record,'rb_delivery.status_field_security')
            elif len(add_storex_security_fields_records)>0 and action.type == "add_storex_security_fields":
                for record in add_storex_security_fields_records:
                    for rec in record:                        
                        field = self.env['ir.model.fields'].search([['model','=','storex_modules.status_field_security'],['name','=', rec]])
                        record_value=self.get_record_value(field,record,rec,package_id,override=True)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    self.check_exist_security_field(record,'storex_modules.status_field_security')
            elif len(set_vhub_config_records)>0 and action.type == "set_vhub_config":
                for record in set_vhub_config_records:
                    vhub_config = self.env['rb_delivery.vhub_configuration'].search([])[0]
                    for rec in record:                        
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.vhub_configuration'],['name','=', rec]])
                        record_value=self.get_record_value(field,record,rec,package_id)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    vhub_config.write(record)
            elif len(confirm_users_records)>0 and action.type == "confirm_users":
                for record in confirm_users_records:
                    user = self.env['rb_delivery.user'].search(['|',['active','=',True],['active','=',False],['mobile_number','=',record['mobile_number']],['email','=',record['email']]])
                    
                    if user.state == 'confirmed' or user.state == 'reconfirmed':
                        continue
                    for rec in record:                        
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.user'],['name','=', rec]])
                        record_value=self.get_record_value(field,record,rec,package_id)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    if user.state != 'deactivate':
                        user.write({'state': 'confirmed'})
                    else:
                        user.wkf_action_reconfirm()
            elif len(mobile_dashboard_records)>0 and action.type == "setup_mobile_dashboard":
                existing_dashboard = self.env['ks_dashboard_ninja.board'].sudo().search([['name','=','Mobile Dashboard']])
                if not existing_dashboard:
                    if action.package_id and action.package_id.name and (action.package_id.name == 'Olivery Mobile Dashboard Items' or action.package_id.name == 'United Theme Dashboard Compnents' or action.package_id.name == 'Tornado Theme Dashboard Compnents') :
                        ninja_dashboard_file = get_module_resource('olivery_demo_data', 'static/json_data/', 'olivery_dashboard_ninja.json')
                    elif action.package_id and action.package_id.name and action.package_id.name == 'Storex Mobile Dashboard Items':
                        ninja_dashboard_file = get_module_resource('olivery_demo_data', 'static/json_data/', 'storex_dashboard_ninja.json')
                    with open(ninja_dashboard_file, mode="rb") as json_file:
                        contents = json_file.read()
                    self.env['ks_dashboard_ninja.board'].ks_import_dashboard(contents)
                for record in mobile_dashboard_records:
                    dashboard_item_id = self.env['ks_dashboard_ninja.item'].search([['name','=',record['dashboard_item_id.name']]])[0].id
                    component_id = self.env['rb_delivery.mobile_component'].search([['group_id.code','=',record['group_id.code']]]).id
                    if not component_id:
                        role_field = self.env['ir.model.fields'].search([['model','=','rb_delivery.mobile_component'],['name','=', 'group_id']])
                        group_id = self.get_record_value(role_field,{
                            'group_id':'code('+record['group_id.code']+')'
                        },'group_id',package_id)
                        component_id = self.env['rb_delivery.mobile_component'].create({'group_id':group_id}).id
                    exist_item = self.env['rb_delivery.mobile_component_item'].search([['name','=',record['name']],['group_id.code','=',record['group_id.code']]])
                    for rec in record:                        
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.user'],['name','=', rec]])
                        record_value=self.get_record_value(field,record,rec,package_id)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    record['mobile_component_id']=component_id
                    record['dashboard_item_id']=dashboard_item_id
                    if not record.get('head_color') and self.primary_color:
                        record['head_color']=self.primary_color
                    if self.secondry_color:
                        record['body_color']=self.secondry_color
                    if exist_item:
                        exist_item.write(record)
                    else:
                        self.env['rb_delivery.mobile_component_item'].create(record)

            elif len(new_pricelist_records)>0 and action.type == "add_new_pricelist":
                    for record in new_pricelist_records:
                        self.check_exist_record(model='rb_delivery.pricelist',record=record,field_to_check='name',package_id=package_id,add_area=False)
                        
            elif len(company_info_records)>0 and action.type == "edit_company_info":
                    for record in company_info_records:
                        company_info = self.env['res.company'].search([])[0]
                        for rec in record:                        
                            field = self.env['ir.model.fields'].search([['model','=','res.company'],['name','=', rec]])
                            record_value=self.get_record_value(field,record,rec,package_id)
                            if record_value=="continue;":
                                continue
                            record[rec]=record_value
                        record['base_url']=self.env['ir.config_parameter'].get_param('web.base.url')
                        if company_info:
                            
                            company_info.write(record)
                        else:
                            self.env['res.company'].create(record)
            elif len(create_user_records)>0 and action.type == "create_user":
                    for record in create_user_records:
                        self.check_exist_record(model='rb_delivery.user',record=record,field_to_check='mobile_number',package_id=package_id,add_area=True)
                        
            elif len(add_default_warehouse_records)>0 and action.type == "add_default_warehouse":
                    for record in add_default_warehouse_records:
                        self.check_exist_record(model='storex_modules.warehouse',record=record,field_to_check='name',package_id=package_id,make_default=True,add_area=True)
            elif len(edit_mobile_functions_records)>0 and action.type == "edit_mobile_functions":
                for record in edit_mobile_functions_records:
                        for rec in record:                        
                            field = self.env['ir.model.fields'].search([['model','=','rb_delivery.mobile_compute_functions'],['name','=', rec]])
                            record_value=self.get_record_value(field,record,rec,package_id)
                            if record_value=="continue;":
                                continue
                            record[rec]=record_value
                        exist_item = self.env['rb_delivery.mobile_compute_functions'].search([['model','=',record['model'],['name','=',record['name']]]])
                        if exist_item:
                            exist_item.write(record)
            elif len(add_card_items_records)>0 and action.type == "add_card_items":
                for record in add_card_items_records:
                        for rec in record:    
              
                            field = self.env['ir.model.fields'].search([['model','=','rb_delivery.mobile_card_item'],['name','=', rec]])
                            
                            record_value=self.get_record_value(field,record,rec,package_id)
                            if record_value=="continue;":
                                continue
                            record[rec]=record_value
                        exist_item = self.env['rb_delivery.mobile_card_item'].search([['unique_field_name','=',record['unique_field_name']]])
                        if exist_item:
                            continue
                        else:
                            self.env['rb_delivery.mobile_card_item'].create(record)
            elif len(add_mapping_relations_records)>0 and action.type == "add_mapping_relations":
                for record in add_mapping_relations_records:
                    for rec in record:
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.mapping_relation_fields'],['name','=',rec]])
                        record_value=self.get_record_value(field,record,rec,package_id)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    exist_item = self.env['rb_delivery.mapping_relation_fields'].search([['unique_field_name','=',record['unique_field_name']]])
                    if exist_item:
                        continue
                    else:
                        self.env['rb_delivery.mapping_relation_fields'].create(record)
            elif len(add_action_items_records)>0 and action.type == "add_action_items_to_card":
                for record in add_action_items_records:
                    for rec in record:
                        field = self.env['ir.model.fields'].search([['model','=','rb_delivery.mobile_item_actions'],['name','=',rec]])
                        record_value=self.get_record_value(field,record,rec,package_id)
                        if record_value=="continue;":
                            continue
                        record[rec]=record_value
                    exist_item = self.env['rb_delivery.mobile_item_actions'].search([['unique_field_name','=',record['unique_field_name']]])
                    if exist_item:
                        continue
                    else:
                        self.env['rb_delivery.mobile_item_actions'].create(record)


    def check_exist_security_field(self, record, model):        
        exist_security_fields_config = self.env[model].search([('group_id', '=', record['group_id'])])
        
        matching_security_configurations = []
        record_status_ids = record['status_ids'][0][2] 
        for security_field_config in exist_security_fields_config:
            config_status_ids = security_field_config.status_ids.ids
            if set(record_status_ids) & set(config_status_ids):
                matching_security_configurations.append(security_field_config)
        
        if matching_security_configurations:
            del record['group_id']
            del record['status_ids']
            if not self.override_existing:
                fields_value = []
                for id in record['field_ids'][0][2]:
                    fields_value=fields_value+[[4,id]]
                record['field_ids'] = fields_value
            for match in matching_security_configurations:
                match.write(record)
        else:
            # If no match, create a new record
            self.env[model].create(record)



    def check_exist_record(self,model,record,field_to_check,package_id,make_default=False,add_area=False):
        exist_record = self.env[model].search([[field_to_check,'=',record[field_to_check]]])
        for rec in record:                        
            field = self.env['ir.model.fields'].search([['model','=',model],['name','=', rec]])
            record_value=self.get_record_value(field,record,rec,package_id)
            if record_value=="continue;":
                continue
            record[rec]=record_value
        if make_default:
            record["is_default"]=True
        if add_area and not "area_id" in record:
            record["area_id"] = self.get_default_area()
        if exist_record:
            
            exist_record.write(record)
        else:
            self.env[model].create(record)

    def get_default_area(self):
        default_area = self.env['rb_delivery.area'].search([['is_default','=',True]])
        if not default_area:
            default_area = self.env['rb_delivery.area'].search([])[0]
        return default_area.id

    def get_record_value(self,field,record,rec,package_id,override=False):
        record_value=""
        if type(record[rec])==str:
            if "ref(" in record[rec]:
                # Between Brackets of ref()
                ref_value = record[rec].split('ref(')[1].replace(')',"")
                
                if field.ttype == 'many2one':   
                    record_value = self.env.ref(ref_value).id
                if field.ttype == 'many2many' or field.ttype == 'one2many':
                    if "," in record[rec]:
                        refs = (ref_value).split(",")
                        ids = []
                        for ref in refs:
                            ids.append(self.env.ref(ref).id)
                        record_value = [[6,0,ids]]
                    else:
                        record_value = [[6,0,[self.env.ref(ref_value).id]]]
            elif field.ttype == 'many2one':
                
                new_domain=[]
                for value in record[rec].split("&"):
                    temp=value.replace('(','*').replace(')','').split('*')
                    field_name=temp[0]
                    values=temp[1]
                    new_domain.append([field_name,'=',values])
                record_value=self.env[field.relation].search(new_domain).id
            elif field.ttype == 'many2many' or field.ttype == 'one2many':
                new_domain=[]
                ids=[]
                if record[rec]!='false':
                    for value in record[rec].split("&"):
                        temp=value.replace('(','*').replace(')','').split('*')
                        field_name=temp[0]
                        values=temp[1].split(',')
                        new_domain.append([field_name,'in',values])
                    ids=self.env[field.relation].search(new_domain).ids
                if (self.override_existing and not package_id.prevent_override) or override or record[rec]=='false':
                    record_value = [[6,0,ids]]
                else:
                    record_value = []
                    for id in ids:
                        record_value=record_value+[[4,id]]
                    
            else:
                record_value = record[rec]
        else:
            record_value = record[rec]
        if field.required and not record_value and field.ttype != 'boolean':
            self.env['rb_delivery.utility'].send_toast('for_user', [_("Required Field Left Empty, Some Data Will Be Ignored While Creating!")] , str(self._uid))
            return "continue;"
        return record_value


    def check_conflict_packages(self):
        conflict_message=""
        con_packages_names_list = []
        installed_packages=self.env['olivery_demo_data.demo_package'].search([['status','=','installed']])
        for package_id in self.demo_package_ids:
            if len(package_id.conflicting_packages)>0:
                con_packages_ids = [ x for x in package_id.conflicting_packages.ids if x in (self.demo_package_ids.ids + installed_packages.ids)]
                conflict_packages = self.env['olivery_demo_data.demo_package'].search([['id','in',con_packages_ids]])
                con_packages_names = ""
                index=0
                for pack in conflict_packages:
                    con_packages_names = con_packages_names + pack.name
                    if index < len(conflict_packages):
                        con_packages_names = con_packages_names + " - "
                    index += 1
                if len(con_packages_names)>0:
                    con_packages_names_list.append(con_packages_names) 
                    
                conflict_message = conflict_message + package_id.name + _(" cannot be installed with ") + con_packages_names +"\n"
        if len(con_packages_names_list)>0:
            raise Warning(conflict_message)

    def check_depandency_packages(self):
        dependency_message=""
        dep_packages_names_list = []
        installed_packages=self.env['olivery_demo_data.demo_package'].search([['status','=','installed']])
        for package_id in self.demo_package_ids:
            if len(package_id.dependency_packages)>0:
                dep_packages_ids = [ x for x in package_id.dependency_packages.ids if x not in (self.demo_package_ids.ids + installed_packages.ids)]
                dependency_packages = self.env['olivery_demo_data.demo_package'].search([['id','in',dep_packages_ids]])
                dep_packages_names = ""
                index=0
                for pack in dependency_packages:
                    dep_packages_names = dep_packages_names + pack.name
                    if len(dependency_packages) == 1:
                        dep_packages_names = dep_packages_names
                    elif index < len(dependency_packages):
                        dep_packages_names = dep_packages_names + " - "
                    index += 1
                if len(dep_packages_names)>0:
                    dep_packages_names_list.append(dep_packages_names)   
                dependency_message = _("To install ") + package_id.name + _(", You need to install ") + dep_packages_names +_(' first')+"\n"
        if len(dep_packages_names_list)>0:
            raise Warning(dependency_message)
        else:
            return self.sort_packages()
    
    def sort_packages(self):
        packages = self.env['olivery_demo_data.demo_package'].search([['id','in',self.demo_package_ids.ids]],order='sequence asc')
        return packages



    def install_modules(self):
        self.modules.button_immediate_install()
        return {
            'type': 'ir.actions.client',
            'tag': 'reload'
        }

