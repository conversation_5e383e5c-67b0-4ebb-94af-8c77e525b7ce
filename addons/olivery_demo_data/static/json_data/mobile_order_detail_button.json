[{"button_title": "NOTE", "button_label": "NOTE", "button_icon": "movify_note", "button_function": "ref(rb_delivery.add_note_mobile_function)", "order_detail": "name(order_detail_page [Business Role])", "unique_field_name": "order_detail_page [Business Role]_note"}, {"button_title": "EDIT", "button_label": "EDIT", "button_icon": "edit_new", "button_function": "ref(rb_delivery.edit_order_mobile_function)", "order_detail": "name(order_detail_page [Business Role])", "unique_field_name": "order_detail_page [Business Role]_edit"}, {"button_title": "ADD_FOLLOWS", "button_label": "ADD_FOLLOWS", "button_icon": "add_follow_orders", "button_function": "ref(rb_delivery.add_follow_order_mobile_function)", "order_detail": "name(order_detail_page [Business Role])", "unique_field_name": "order_detail_page [Business Role]_add_follows"}, {"button_title": "NOTE", "button_label": "NOTE", "button_icon": "movify_note", "button_function": "ref(rb_delivery.add_note_mobile_function)", "order_detail": "name(order_detail_page [Driver Role])", "unique_field_name": "order_detail_page [Driver Role]_note"}, {"button_title": "EDIT", "button_label": "EDIT", "button_icon": "edit_new", "button_function": "ref(rb_delivery.edit_order_mobile_function)", "order_detail": "name(order_detail_page [Driver Role])", "unique_field_name": "order_detail_page [Driver Role]_edit"}, {"button_title": "NOTE", "button_label": "NOTE", "button_icon": "movify_note", "button_function": "ref(rb_delivery.add_note_mobile_function)", "order_detail": "name(order_detail_page [Call Center Role])", "unique_field_name": "order_detail_page [Call Center Role]_note"}, {"button_title": "EDIT", "button_label": "EDIT", "button_icon": "edit_new", "button_function": "ref(rb_delivery.edit_order_mobile_function)", "order_detail": "name(order_detail_page [Call Center Role])", "unique_field_name": "order_detail_page [Call Center Role]_edit"}, {"button_title": "NOTE", "button_label": "NOTE", "button_icon": "movify_note", "button_function": "ref(rb_delivery.add_note_mobile_function)", "order_detail": "name(order_detail_page [Manager Role])", "unique_field_name": "order_detail_page [Manager Role]_note"}, {"button_title": "EDIT", "button_label": "EDIT", "button_icon": "edit_new", "button_function": "ref(rb_delivery.edit_order_mobile_function)", "order_detail": "name(order_detail_page [Manager Role])", "unique_field_name": "order_detail_page [Manager Role]_edit"}, {"button_title": "ADD_FOLLOWS", "button_label": "ADD_FOLLOWS", "button_icon": "add_follow_orders", "button_function": "ref(rb_delivery.add_follow_order_mobile_function)", "order_detail": "name(order_detail_page [Manager Role])", "unique_field_name": "order_detail_page [Manager Role]_add_follows"}, {"button_title": "NOTE", "button_label": "NOTE", "button_icon": "movify_note", "button_function": "ref(rb_delivery.add_note_mobile_function)", "order_detail": "name(order_detail_page [Super Manager Role])", "unique_field_name": "order_detail_page [Super Manager Role]_note"}, {"button_title": "EDIT", "button_label": "EDIT", "button_icon": "edit_new", "button_function": "ref(rb_delivery.edit_order_mobile_function)", "order_detail": "name(order_detail_page [Super Manager Role])", "unique_field_name": "order_detail_page [Super Manager Role]_edit"}, {"button_title": "ADD_FOLLOWS", "button_label": "ADD_FOLLOWS", "button_icon": "add_follow_orders", "button_function": "ref(rb_delivery.add_follow_order_mobile_function)", "order_detail": "name(order_detail_page [Super Manager Role])", "unique_field_name": "order_detail_page [Super Manager Role]_add_follows"}]