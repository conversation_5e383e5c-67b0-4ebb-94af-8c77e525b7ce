[{"unique_field_name": "order_card [Business Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_order__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "SEQUENCE", "sequence": "1", "button_icon": "barcode_movify", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_reference_id", "field": "ref(rb_delivery.field_rb_delivery_order__reference_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "REFERENCE", "sequence": "2", "button_icon": "qrcode_movify", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_order__state_id)", "position": "header", "button_icon": "reload_new_design", "color_field_name": "ref(rb_delivery.field_rb_delivery_order__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_order__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "local_compute_function": "collection<PERSON>xtraFields", "button_function": "ref(rb_delivery.change_status_mobile_function)", "background": "linear-gradient(72deg, #FFE682 0%, #FA7B30 100%)", "font_color": "white", "sequence": "3", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_order_type_id", "field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "background": "linear-gradient(72deg, #CFAF48 0%, #F5EC79 100%)", "font_color": "black", "sequence": "4", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_customer_name", "field": "ref(rb_delivery.field_rb_delivery_order__customer_name)", "position": "content", "button_icon": "recipient_movify", "label": "RECIPIENT", "sequence": "5", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_address_tag", "field": "ref(rb_delivery.field_rb_delivery_order__address_tag)", "position": "content", "button_icon": "location_movify", "local_compute_function": "customerLocation", "label": "RECIPIENT_ADDRESS", "sequence": "6", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_agent_name", "field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "position": "content", "button_icon": "driver_movify", "label": "DRIVER", "sequence": "7", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_delivery_cost", "field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "position": "content", "button_icon": "delivery_cost_movify", "sequence": "8", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_required_from_business", "field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "position": "content", "button_icon": "required_from_business_cost_movify", "sequence": "9", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_money_collection_cost", "field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "position": "content", "button_icon": "money_cost_movify", "sequence": "10", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_customer_mobile", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MAKE_CALL", "is_button": true, "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_customer_mobile_function)", "sequence": "11", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_customer_chat", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CHAT", "is_button": true, "button_icon": "logo-wechat", "button_function": "ref(rb_delivery.message_order_mobile_function)", "sequence": "13", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_showOrderHistory", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "ORDER_HISTORY", "is_button": true, "button_icon": "historyNew", "button_function": "ref(rb_delivery.order_history_mobile_function)", "sequence": "14", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_orderDetailsNew", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MORE_DETAILS", "is_button": true, "button_icon": "orderDetailsNew", "button_function": "ref(rb_delivery.more_details_order_mobile_function)", "sequence": "15", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_product_note", "field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "position": "content", "button_icon": "business_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "17", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_driver_note", "field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "position": "content", "button_icon": "driver_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "18", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Business Role]_note", "field": "ref(rb_delivery.field_rb_delivery_order__note)", "position": "content", "button_icon": "company_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "19", "card_creator": "name(order_card [Business Role])"}, {"unique_field_name": "order_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_order__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "SEQUENCE", "sequence": "1", "button_icon": "barcode_movify", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_reference_id", "field": "ref(rb_delivery.field_rb_delivery_order__reference_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "REFERENCE", "sequence": "2", "button_icon": "qrcode_movify", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_order__state_id)", "position": "header", "button_icon": "reload_new_design", "color_field_name": "ref(rb_delivery.field_rb_delivery_order__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_order__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "local_compute_function": "collection<PERSON>xtraFields", "button_function": "ref(rb_delivery.change_status_mobile_function)", "background": "linear-gradient(72deg, #FFE682 0%, #FA7B30 100%)", "font_color": "white", "sequence": "3", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_order_type_id", "field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "background": "linear-gradient(72deg, #CFAF48 0%, #F5EC79 100%)", "font_color": "black", "sequence": "4", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_customer_name", "field": "ref(rb_delivery.field_rb_delivery_order__customer_name)", "position": "content", "button_icon": "recipient_movify", "label": "RECIPIENT", "sequence": "5", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_assign_to_business", "field": "ref(rb_delivery.field_rb_delivery_order__assign_to_business)", "position": "content", "button_icon": "business_movify", "label": "SENDER", "sequence": "6", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_address_tag", "field": "ref(rb_delivery.field_rb_delivery_order__address_tag)", "position": "content", "button_icon": "location_movify", "local_compute_function": "customerLocation", "label": "RECIPIENT_ADDRESS", "sequence": "7", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_money_collection_cost", "field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "position": "content", "button_icon": "money_cost_movify", "sequence": "8", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_customer_mobile", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MAKE_CALL", "is_button": true, "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_customer_mobile_function)", "sequence": "9", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_customer_chat", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CHAT", "is_button": true, "button_icon": "logo-wechat", "button_function": "ref(rb_delivery.message_order_mobile_function)", "sequence": "11", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_showOrderHistory", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "ORDER_HISTORY", "is_button": true, "button_icon": "historyNew", "button_function": "ref(rb_delivery.order_history_mobile_function)", "sequence": "12", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_orderDetailsNew", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MORE_DETAILS", "is_button": true, "button_icon": "orderDetailsNew", "button_function": "ref(rb_delivery.more_details_order_mobile_function)", "sequence": "13", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_product_note", "field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "position": "content", "button_icon": "business_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "15", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_driver_note", "field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "position": "content", "button_icon": "driver_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "16", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Driver Role]_note", "field": "ref(rb_delivery.field_rb_delivery_order__note)", "position": "content", "button_icon": "company_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "17", "card_creator": "name(order_card [Driver Role])"}, {"unique_field_name": "order_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_order__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "SEQUENCE", "sequence": "1", "button_icon": "barcode_movify", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_reference_id", "field": "ref(rb_delivery.field_rb_delivery_order__reference_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "REFERENCE", "sequence": "2", "button_icon": "qrcode_movify", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_order__state_id)", "position": "header", "button_icon": "reload_new_design", "color_field_name": "ref(rb_delivery.field_rb_delivery_order__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_order__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "local_compute_function": "collection<PERSON>xtraFields", "button_function": "ref(rb_delivery.change_status_mobile_function)", "background": "linear-gradient(72deg, #FFE682 0%, #FA7B30 100%)", "font_color": "white", "sequence": "3", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_order_type_id", "field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "background": "linear-gradient(72deg, #CFAF48 0%, #F5EC79 100%)", "font_color": "black", "sequence": "4", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_customer_name", "field": "ref(rb_delivery.field_rb_delivery_order__customer_name)", "position": "content", "button_icon": "recipient_movify", "label": "RECIPIENT", "sequence": "5", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_address_tag", "field": "ref(rb_delivery.field_rb_delivery_order__address_tag)", "position": "content", "button_icon": "location_movify", "local_compute_function": "customerLocation", "label": "RECIPIENT_ADDRESS", "sequence": "6", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_required_from_business", "field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "position": "content", "button_icon": "required_from_business_cost_movify", "sequence": "7", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_money_collection_cost", "field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "position": "content", "button_icon": "money_cost_movify", "sequence": "8", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_assign_to_business", "field": "ref(rb_delivery.field_rb_delivery_order__assign_to_business)", "position": "content", "button_icon": "business_movify", "label": "SENDER", "sequence": "9", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_agent_name", "field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "position": "content", "button_icon": "driver_movify", "label": "DRIVER", "sequence": "10", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_delivery_cost", "field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "position": "content", "button_icon": "delivery_cost_movify", "sequence": "11", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_customer_mobile", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MAKE_CALL", "is_button": true, "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_customer_mobile_function)", "sequence": "12", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_customer_chat", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CHAT", "is_button": true, "button_icon": "logo-wechat", "button_function": "ref(rb_delivery.message_order_mobile_function)", "sequence": "14", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_showOrderHistory", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "ORDER_HISTORY", "is_button": true, "button_icon": "historyNew", "button_function": "ref(rb_delivery.order_history_mobile_function)", "sequence": "15", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_orderDetailsNew", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MORE_DETAILS", "is_button": true, "button_icon": "orderDetailsNew", "button_function": "ref(rb_delivery.more_details_order_mobile_function)", "sequence": "16", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_product_note", "field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "position": "content", "button_icon": "business_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "18", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_driver_note", "field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "position": "content", "button_icon": "driver_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "19", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Manager Role]_note", "field": "ref(rb_delivery.field_rb_delivery_order__note)", "position": "content", "button_icon": "company_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "20", "card_creator": "name(order_card [Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_order__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "SEQUENCE", "sequence": "1", "button_icon": "barcode_movify", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_reference_id", "field": "ref(rb_delivery.field_rb_delivery_order__reference_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "REFERENCE", "sequence": "2", "button_icon": "qrcode_movify", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_order__state_id)", "position": "header", "button_icon": "reload_new_design", "color_field_name": "ref(rb_delivery.field_rb_delivery_order__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_order__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "local_compute_function": "collection<PERSON>xtraFields", "button_function": "ref(rb_delivery.change_status_mobile_function)", "background": "linear-gradient(72deg, #FFE682 0%, #FA7B30 100%)", "font_color": "white", "sequence": "3", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_order_type_id", "field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "background": "linear-gradient(72deg, #CFAF48 0%, #F5EC79 100%)", "font_color": "black", "sequence": "4", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_customer_name", "field": "ref(rb_delivery.field_rb_delivery_order__customer_name)", "position": "content", "button_icon": "recipient_movify", "label": "RECIPIENT", "sequence": "5", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_address_tag", "field": "ref(rb_delivery.field_rb_delivery_order__address_tag)", "position": "content", "button_icon": "location_movify", "local_compute_function": "customerLocation", "label": "RECIPIENT_ADDRESS", "sequence": "6", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_required_from_business", "field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "position": "content", "button_icon": "required_from_business_cost_movify", "sequence": "7", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_money_collection_cost", "field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "position": "content", "button_icon": "money_cost_movify", "sequence": "8", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_assign_to_business", "field": "ref(rb_delivery.field_rb_delivery_order__assign_to_business)", "position": "content", "button_icon": "business_movify", "label": "SENDER", "sequence": "9", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_agent_name", "field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "position": "content", "button_icon": "driver_movify", "label": "DRIVER", "sequence": "10", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_delivery_cost", "field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "position": "content", "button_icon": "delivery_cost_movify", "sequence": "11", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_customer_mobile", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MAKE_CALL", "is_button": true, "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_customer_mobile_function)", "sequence": "12", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_customer_chat", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CHAT", "is_button": true, "button_icon": "logo-wechat", "button_function": "ref(rb_delivery.message_order_mobile_function)", "sequence": "14", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_showOrderHistory", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "ORDER_HISTORY", "is_button": true, "button_icon": "historyNew", "button_function": "ref(rb_delivery.order_history_mobile_function)", "sequence": "15", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_orderDetailsNew", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MORE_DETAILS", "is_button": true, "button_icon": "orderDetailsNew", "button_function": "ref(rb_delivery.more_details_order_mobile_function)", "sequence": "16", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_product_note", "field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "position": "content", "button_icon": "business_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "18", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_driver_note", "field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "position": "content", "button_icon": "driver_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "19", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Super Manager Role]_note", "field": "ref(rb_delivery.field_rb_delivery_order__note)", "position": "content", "button_icon": "company_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "20", "card_creator": "name(order_card [Super Manager Role])"}, {"unique_field_name": "order_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_order__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "SEQUENCE", "sequence": "1", "button_icon": "barcode_movify", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_reference_id", "field": "ref(rb_delivery.field_rb_delivery_order__reference_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "label": "REFERENCE", "sequence": "2", "button_icon": "qrcode_movify", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_order__state_id)", "position": "header", "button_icon": "reload_new_design", "color_field_name": "ref(rb_delivery.field_rb_delivery_order__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_order__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "local_compute_function": "collection<PERSON>xtraFields", "button_function": "ref(rb_delivery.change_status_mobile_function)", "background": "linear-gradient(72deg, #FFE682 0%, #FA7B30 100%)", "font_color": "white", "sequence": "3", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_order_type_id", "field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "background": "linear-gradient(72deg, #CFAF48 0%, #F5EC79 100%)", "font_color": "black", "sequence": "4", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_customer_name", "field": "ref(rb_delivery.field_rb_delivery_order__customer_name)", "position": "content", "button_icon": "recipient_movify", "label": "RECIPIENT", "sequence": "5", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [SCall Center Role]_address_tag", "field": "ref(rb_delivery.field_rb_delivery_order__address_tag)", "position": "content", "button_icon": "location_movify", "local_compute_function": "customerLocation", "label": "RECIPIENT_ADDRESS", "sequence": "6", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_required_from_business", "field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "position": "content", "button_icon": "required_from_business_cost_movify", "sequence": "7", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_money_collection_cost", "field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "position": "content", "button_icon": "money_cost_movify", "sequence": "8", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_assign_to_business", "field": "ref(rb_delivery.field_rb_delivery_order__assign_to_business)", "position": "content", "button_icon": "business_movify", "label": "SENDER", "sequence": "9", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_agent_name", "field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "position": "content", "button_icon": "driver_movify", "label": "DRIVER", "sequence": "10", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_delivery_cost", "field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "position": "content", "button_icon": "delivery_cost_movify", "sequence": "11", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_customer_mobile", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MAKE_CALL", "is_button": true, "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_customer_mobile_function)", "sequence": "12", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_customer_chat", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CHAT", "is_button": true, "button_icon": "logo-wechat", "button_function": "ref(rb_delivery.message_order_mobile_function)", "sequence": "14", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_showOrderHistory", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "ORDER_HISTORY", "is_button": true, "button_icon": "historyNew", "button_function": "ref(rb_delivery.order_history_mobile_function)", "sequence": "15", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_orderDetailsNew", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "MORE_DETAILS", "is_button": true, "button_icon": "orderDetailsNew", "button_function": "ref(rb_delivery.more_details_order_mobile_function)", "sequence": "16", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_product_note", "field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "position": "content", "button_icon": "business_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "18", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_driver_note", "field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "position": "content", "button_icon": "driver_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "19", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "order_card [Call Center Role]_note", "field": "ref(rb_delivery.field_rb_delivery_order__note)", "position": "content", "button_icon": "company_note_movify", "background": "#EFEFEF", "sub_position": "ref(rb_delivery.sub_position_notes_section)", "sequence": "20", "card_creator": "name(order_card [Call Center Role])"}, {"unique_field_name": "user_card [Super Manager Role]_username", "field": "ref(rb_delivery.field_rb_delivery_user__username)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_group_id", "field": "ref(rb_delivery.field_rb_delivery_user__group_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_mobile_number", "field": "ref(rb_delivery.field_rb_delivery_user__mobile_number)", "position": "content", "label": "MOBILE", "sequence": "3", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_email", "field": "ref(rb_delivery.field_rb_delivery_user__email)", "position": "content", "label": "EMAIL", "sequence": "4", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_editUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "EDIT_USER", "is_button": true, "button_icon": "create", "button_function": "ref(rb_delivery.edit_user_mobile_function)", "background": "danger", "sequence": "5", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_callUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CALL_USER", "is_button": true, "button_icon": "call", "button_function": "ref(rb_delivery.call_user_mobile_function)", "background": "success", "sequence": "6", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_confirmUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CONFIRM_USER", "is_button": true, "button_icon": "confirmUser", "button_function": "ref(rb_delivery.confirm_user_mobile_function)", "background": "success", "sequence": "7", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Super Manager Role]_deactivateUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DEACTIVATE_USER", "is_button": true, "button_icon": "unconfirmUser", "button_function": "ref(rb_delivery.deactivate_user_mobile_function)", "background": "danger", "sequence": "8", "card_creator": "name(user_card [Super Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_username", "field": "ref(rb_delivery.field_rb_delivery_user__username)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_group_id", "field": "ref(rb_delivery.field_rb_delivery_user__group_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_mobile_number", "field": "ref(rb_delivery.field_rb_delivery_user__mobile_number)", "position": "content", "label": "MOBILE", "sequence": "3", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_email", "field": "ref(rb_delivery.field_rb_delivery_user__email)", "position": "content", "label": "EMAIL", "sequence": "4", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_editUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "EDIT_USER", "is_button": true, "button_icon": "create", "button_function": "ref(rb_delivery.edit_user_mobile_function)", "background": "danger", "sequence": "5", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_callUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CALL_USER", "is_button": true, "button_icon": "call", "button_function": "ref(rb_delivery.call_user_mobile_function)", "background": "success", "sequence": "6", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_confirmUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CONFIRM_USER", "is_button": true, "button_icon": "confirmUser", "button_function": "ref(rb_delivery.confirm_user_mobile_function)", "background": "success", "sequence": "7", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "user_card [Manager Role]_deactivateUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DEACTIVATE_USER", "is_button": true, "button_icon": "unconfirmUser", "button_function": "ref(rb_delivery.deactivate_user_mobile_function)", "background": "danger", "sequence": "8", "card_creator": "name(user_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_multi_print_orders_money_collector_mobile_function)", "sequence": "2", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_id)", "position": "content", "label": "SENDER", "button_icon": "senderNewCollection", "sequence": "5", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Super Manager Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Super Manager Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Super Manager Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "money_collection_card [Super Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__total_cost)", "position": "content", "is_monetary": true, "button_icon": "moneyNewCollection", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Super Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(money_collection_card [Super Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_multi_print_orders_money_collector_mobile_function)", "sequence": "2", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_id)", "position": "content", "label": "SENDER", "button_icon": "senderNewCollection", "sequence": "5", "card_creator": "name(money_collection_card [Manager Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Manager Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Manager Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Manager Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "money_collection_card [Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__total_cost)", "position": "content", "is_monetary": true, "button_icon": "moneyNewCollection", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(money_collection_card [Manager Role])"}, {"unique_field_name": "money_collection_card [Business Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_multi_print_orders_money_collector_mobile_function)", "sequence": "2", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Business Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Business Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Business Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "is_monetary": true, "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Business Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Business Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(money_collection_card [Business Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_multi_print_orders_money_collector_mobile_function)", "sequence": "2", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_id)", "position": "content", "label": "SENDER", "button_icon": "senderNewCollection", "sequence": "5", "card_creator": "name(money_collection_card [Driver Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Driver Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Driver Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "money_collection_card [Driver Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "money_collection_card [Driver Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__total_cost)", "position": "content", "is_monetary": true, "button_icon": "moneyNewCollection", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Driver Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(money_collection_card [Driver Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_multi_print_orders_money_collector_mobile_function)", "sequence": "1", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "4", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__business_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "5", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__driver_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_multi_print_orders_money_collector__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "money_collection_card [Call Center Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_returned_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_id)", "position": "content", "button_icon": "senderNewCollection", "label": "SENDER", "sequence": "5", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Super Manager Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(returned_money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_returned_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Super Manager Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(returned_money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_returned_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Super Manager Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(returned_money_collection_card [Super Manager Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "is_monetary": true, "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Super Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RETURNED_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(returned_money_collection_card [Super Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_returned_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_id)", "position": "content", "button_icon": "senderNewCollection", "label": "SENDER", "sequence": "5", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Manager Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(returned_money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_returned_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Manager Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(returned_money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Manager Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(returned_money_collection_card [Manager Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "is_monetary": true, "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RETURNED_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(returned_money_collection_card [Manager Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_returned_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "is_monetary": true, "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Business Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RETURNED_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(returned_money_collection_card [Business Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_returned_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_returned_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "label": "SEQUENCE", "sequence": "1", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_id)", "position": "content", "button_icon": "senderNewCollection", "label": "SENDER", "sequence": "5", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Driver Role]_business_id [doFunction] [business_mobile_number]", "card_item": "unique_field_name(returned_money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_icon": "mobileNew", "button_function": "ref(rb_delivery.call_sender_returned_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Driver Role]_business_id [doFunction] [business_message]", "card_item": "unique_field_name(returned_money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_icon": "chat<PERSON>ew", "button_function": "ref(rb_delivery.message_sender_order_money_collection_mobile_function)"}, {"for_action": "add_action_items_to_card", "unique_field_name": "returned_money_collection_card [Driver Role]_business_id [geo] [latitude]", "card_item": "unique_field_name(returned_money_collection_card [Driver Role]_business_id)", "action_type": "doFunction", "button_function": "ref(rb_delivery.location_mobile_function)", "action_field_1": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_latitude)", "action_field_2": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_longitude)", "button_icon": "locationNew"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__order_count)", "position": "content", "label": "ORDERS", "button_icon": "ordersNew", "sequence": "7", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "is_monetary": true, "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Driver Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RETURNED_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(returned_money_collection_card [Driver Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_returned_money_collection_mobile_function)", "sequence": "1", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "4", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__business_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "5", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__driver_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_returned_money_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "returned_money_collection_card [Call Center Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RETURNED_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(returned_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__agent_id)", "position": "content", "label": "DRIVER", "button_icon": "driver<PERSON><PERSON>", "sequence": "6", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__order_count)", "position": "content", "button_icon": "ordersNew", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__create_date)", "position": "content", "button_icon": "dateNew", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "label": "DRIVER_PROFIT", "sequence": "9", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Super Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(agent_money_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__agent_id)", "position": "content", "label": "DRIVER", "button_icon": "driver<PERSON><PERSON>", "sequence": "6", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__order_count)", "position": "content", "button_icon": "ordersNew", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__create_date)", "position": "content", "button_icon": "dateNew", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "label": "DRIVER_PROFIT", "sequence": "9", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "printNew", "button_function": "technical_name(print)", "sequence": "10", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(agent_money_collection_card [Manager Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__state_id)", "position": "header", "button_icon": "reload_new_design", "local_compute_function": "collection<PERSON>xtraFields", "color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__status_color)", "secondary_color_field_name": "ref(rb_delivery.field_rb_delivery_agent_money_collection__secondary_status_color)", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_money_collection_mobile_function)", "sequence": "2", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__order_count)", "position": "content", "button_icon": "ordersNew", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__create_date)", "position": "content", "button_icon": "dateNew", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__total_cost)", "position": "content", "button_icon": "moneyNewCollection", "label": "DRIVER_PROFIT", "sequence": "9", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Driver Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "ordersGoTo", "button_function": "technical_name(goToCollectionOrders)", "sequence": "11", "card_creator": "name(agent_money_collection_card [Driver Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_money_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__agent_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_money_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_money_collection_card [Call Center Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_MONEY_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_money_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_returned_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "4", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__business_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "5", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__driver_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Super Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_RETURNED_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_returned_collection_card [Super Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_returned_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "4", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__business_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "5", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__driver_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_RETURNED_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_returned_collection_card [Manager Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_returned_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "4", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_business_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__business_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "5", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_driver_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__driver_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "9", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Call Center Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_RETURNED_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_returned_collection_card [Call Center Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_returned_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "4", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "5", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "6", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "7", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Business Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_RETURNED_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_returned_collection_card [Business Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_agent_returned_collection_mobile_function)", "sequence": "1", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__area_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__order_count)", "position": "content", "label": "ORDERS", "sequence": "4", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "5", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_total_cost", "field": "ref(rb_delivery.field_rb_delivery_agent_returned_collection__total_cost)", "position": "content", "label": "TOTAL_REQUIRED_FROM_BUSINESS", "sequence": "6", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "7", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "agent_returned_collection_card [Driver Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_AGENT_RETURNED_COLLECTION_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(agent_returned_collection_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_runsheet__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_runsheet_mobile_function)", "sequence": "1", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_runsheet__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_assign_to_agent", "field": "ref(rb_delivery.field_rb_delivery_runsheet__assign_to_agent)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_runsheet__order_count)", "position": "content", "label": "ORDERS", "sequence": "6", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_runsheet__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "7", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_total_money_collection_runsheet", "field": "ref(rb_delivery.field_rb_delivery_runsheet__total_money_collection_runsheet)", "position": "content", "label": "TOTAL_MONEY_COLLECTION_RUNSHEET", "sequence": "8", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "9", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Super Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RUNSHEET_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "10", "card_creator": "name(runsheet_card [Super Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_runsheet__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_runsheet_mobile_function)", "sequence": "1", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_runsheet__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_assign_to_agent", "field": "ref(rb_delivery.field_rb_delivery_runsheet__assign_to_agent)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_runsheet__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_runsheet__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_total_money_collection_runsheet", "field": "ref(rb_delivery.field_rb_delivery_runsheet__total_money_collection_runsheet)", "position": "content", "label": "TOTAL_MONEY_COLLECTION_RUNSHEET", "sequence": "9", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Manager Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RUNSHEET_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(runsheet_card [Manager Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_runsheet__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_runsheet_mobile_function)", "sequence": "1", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_runsheet__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "3", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_assign_to_agent", "field": "ref(rb_delivery.field_rb_delivery_runsheet__assign_to_agent)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "6", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_runsheet__order_count)", "position": "content", "label": "ORDERS", "sequence": "7", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_runsheet__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "8", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_total_money_collection_runsheet", "field": "ref(rb_delivery.field_rb_delivery_runsheet__total_money_collection_runsheet)", "position": "content", "label": "TOTAL_MONEY_COLLECTION_RUNSHEET", "sequence": "9", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "10", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Call Center Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RUNSHEET_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(runsheet_card [Call Center Role])"}, {"unique_field_name": "runsheet_card [Business Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_runsheet__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_runsheet_mobile_function)", "sequence": "1", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_runsheet__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_runsheet__order_count)", "position": "content", "label": "ORDERS", "sequence": "4", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_runsheet__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "5", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_total_money_collection_runsheet", "field": "ref(rb_delivery.field_rb_delivery_runsheet__total_money_collection_runsheet)", "position": "content", "label": "TOTAL_MONEY_COLLECTION_RUNSHEET", "sequence": "6", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "7", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Business Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RUNSHEET_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(runsheet_card [Business Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_state_id", "field": "ref(rb_delivery.field_rb_delivery_runsheet__state_id)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_runsheet_mobile_function)", "sequence": "1", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_sequence", "field": "ref(rb_delivery.field_rb_delivery_runsheet__sequence)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_title)", "sequence": "2", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_order_count", "field": "ref(rb_delivery.field_rb_delivery_runsheet__order_count)", "position": "content", "label": "ORDERS", "sequence": "4", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_create_date", "field": "ref(rb_delivery.field_rb_delivery_runsheet__create_date)", "position": "content", "label": "CREATE_DATE", "sequence": "5", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_total_money_collection_runsheet", "field": "ref(rb_delivery.field_rb_delivery_runsheet__total_money_collection_runsheet)", "position": "content", "label": "TOTAL_MONEY_COLLECTION_RUNSHEET", "sequence": "6", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_print", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "PRINT_ORDER", "is_button": true, "button_icon": "print", "button_function": "technical_name(print)", "background": "warning", "sequence": "7", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "runsheet_card [Driver Role]_goToCollectionOrders", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "GO_TO_RUNSHEET_ORDERS", "is_button": true, "button_icon": "cube-outline", "button_function": "technical_name(goToCollectionOrders)", "background": "danger", "sequence": "11", "card_creator": "name(runsheet_card [Driver Role])"}, {"unique_field_name": "business_stores_card [Business Role]_commercial_name", "field": "ref(rb_delivery.field_rb_delivery_order__commercial_name)", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "button_function": "ref(rb_delivery.change_status_mobile_function)", "sequence": "1", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_username", "field": "ref(rb_delivery.field_rb_delivery_user__username)", "label": "USERNAME", "position": "content", "sequence": "2", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_email", "field": "ref(rb_delivery.field_rb_delivery_user__email)", "label": "EMAIL", "position": "content", "sequence": "4", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_mobile_number", "field": "ref(rb_delivery.field_rb_delivery_user__mobile_number)", "label": "MOBILE", "position": "content", "sequence": "5", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_area_id", "field": "ref(rb_delivery.field_rb_delivery_user__area_id)", "label": "AREA", "position": "content", "sequence": "6", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_address", "field": "ref(rb_delivery.field_rb_delivery_user__address)", "label": "ADDRESS", "position": "content", "sequence": "7", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_default_payment_type", "field": "ref(rb_delivery.field_rb_delivery_user__default_payment_type)", "label": "DEFAULT PAYMENT TYPE", "position": "content", "sequence": "8", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_editUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "EDIT_USER", "is_button": true, "button_icon": "create", "button_function": "ref(rb_delivery.edit_user_mobile_function)", "background": "danger", "sequence": "9", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_stores_card [Business Role]_callUser", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "CALL_USER", "is_button": true, "button_icon": "call", "button_function": "ref(rb_delivery.call_user_mobile_function)", "background": "success", "sequence": "10", "card_creator": "name(business_stores_card [Business Role])"}, {"unique_field_name": "business_report_card [Business Role]_name", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__name)", "label": "NAME", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(report_card [Business Role])"}, {"unique_field_name": "business_report_card [Business Role]_print_date_time", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__print_date_time)", "label": "DATE", "position": "content", "sequence": "2", "card_creator": "name(report_card [Business Role])"}, {"unique_field_name": "business_report_card [Business Role]_download_report", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DOWNLOAD_REPORT", "is_button": true, "button_icon": "cloud-download-outline", "button_function": "ref(rb_delivery.download_report_mobile_function)", "background": "success", "sequence": "3", "card_creator": "name(report_card [Business Role])"}, {"unique_field_name": "super_manager_report_card [Super Manager Role]_name", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__name)", "label": "NAME", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(report_card [Super Manager Role])"}, {"unique_field_name": "super_manager_report_card [Super Manager Role]_print_date_time", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__print_date_time)", "label": "DATE", "position": "content", "sequence": "2", "card_creator": "name(report_card [Super Manager Role])"}, {"unique_field_name": "super_manager_report_card [Super Manager Role]_download_report", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DOWNLOAD_REPORT", "is_button": true, "button_icon": "cloud-download-outline", "button_function": "ref(rb_delivery.download_report_mobile_function)", "background": "success", "sequence": "3", "card_creator": "name(report_card [Super Manager Role])"}, {"unique_field_name": "manager_report_card [Manager Role]_name", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__name)", "label": "NAME", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(report_card [Manager Role])"}, {"unique_field_name": "manager_report_card [Manager Role]_print_date_time", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__print_date_time)", "label": "DATE", "position": "content", "sequence": "2", "card_creator": "name(report_card [Manager Role])"}, {"unique_field_name": "manager_report_card [Manager Role]_download_report", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DOWNLOAD_REPORT", "is_button": true, "button_icon": "cloud-download-outline", "button_function": "ref(rb_delivery.download_report_mobile_function)", "background": "success", "sequence": "3", "card_creator": "name(report_card [Manager Role])"}, {"unique_field_name": "call_center_report_card [Call Center Role]_name", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__name)", "label": "NAME", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(report_card [Call Center Role])"}, {"unique_field_name": "call_center_report_card [Call Center Role]_print_date_time", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__print_date_time)", "label": "DATE", "position": "content", "sequence": "2", "card_creator": "name(report_card [Call Center Role])"}, {"unique_field_name": "call_center_report_card [Call Center Role]_download_report", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DOWNLOAD_REPORT", "is_button": true, "button_icon": "cloud-download-outline", "button_function": "ref(rb_delivery.download_report_mobile_function)", "background": "success", "sequence": "3", "card_creator": "name(report_card [Call Center Role])"}, {"unique_field_name": "driver_report_card [Driver Role]_name", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__name)", "label": "NAME", "position": "header", "sub_position": "ref(rb_delivery.sub_position_bubble)", "sequence": "1", "card_creator": "name(report_card [Driver Role])"}, {"unique_field_name": "driver_report_card [Driver Role]_print_date_time", "field": "ref(rb_delivery.field_rb_delivery_report_job_queue__print_date_time)", "label": "DATE", "position": "content", "sequence": "2", "card_creator": "name(report_card [Driver Role])"}, {"unique_field_name": "driver_report_card [Driver Role]_download_report", "position": "footer", "sub_position": "ref(rb_delivery.sub_position_end)", "label": "DOWNLOAD_REPORT", "is_button": true, "button_icon": "cloud-download-outline", "button_function": "ref(rb_delivery.download_report_mobile_function)", "background": "success", "sequence": "3", "card_creator": "name(report_card [Driver Role])"}]