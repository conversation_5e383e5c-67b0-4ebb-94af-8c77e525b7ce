[{"item_title": "CUSTOMER_MOBILE", "item_label": "CUSTOMER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_mobile)", "order_detail_card": "name(order_detail_page [Business Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Business Role]_receiver_information_card_customer_mobile"}, {"item_title": "CUSTOMER_WHATSAPP_MOBILE", "item_label": "CUSTOMER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Business Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Business Role]_receiver_information_card_cus_whatsapp_mobile"}, {"item_title": "CUSTOMER_ADDRESS", "item_label": "CUSTOMER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_address)", "order_detail_card": "name(order_detail_page [Business Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Business Role]_receiver_information_card_customer_address"}, {"item_title": "MONEY_COLLECTION_COST", "item_label": "MONEY_COLLECTION_COST", "item_icon": "money_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "order_detail_card": "name(order_detail_page [Business Role]_financial_information_card)", "unique_field_name": "order_detail_page [Business Role]_financial_information_card_money_collection_cost"}, {"item_title": "DELIVERY_COST", "item_label": "DELIVERY_COST", "item_icon": "delivery_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "order_detail_card": "name(order_detail_page [Business Role]_financial_information_card)", "unique_field_name": "order_detail_page [Business Role]_financial_information_card_delivery_cost"}, {"item_title": "REQUIRED_FROM_BUSINESS", "item_label": "REQUIRED_FROM_BUSINESS", "item_icon": "required_from_business_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "order_detail_card": "name(order_detail_page [Business Role]_financial_information_card)", "unique_field_name": "order_detail_page [Business Role]_financial_information_card_required_from_business"}, {"item_title": "ORDER_TYPE", "item_label": "ORDER_TYPE", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card__order_type_id"}, {"item_title": "BUSINESS_NOTE", "item_label": "BUSINESS_NOTE", "item_icon": "business_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_product_note"}, {"item_title": "DRIVER_NOTE", "item_label": "DRIVER_NOTE", "item_icon": "driver_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_driver_note"}, {"item_title": "NOTE", "item_label": "NOTE", "item_field": "ref(rb_delivery.field_rb_delivery_order__note)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_note"}, {"item_title": "ORDER_CONTENT", "item_label": "ORDER_CONTENT", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__description_tags)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_description_tags"}, {"item_title": "DRIVER", "item_label": "DRIVER", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_agent_name"}, {"item_title": "CURRENT_BRANCH", "item_label": "CURRENT_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__current_branch)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_current_branch"}, {"item_title": "TO_BRANCH", "item_label": "TO_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__to_branch)", "order_detail_card": "name(order_detail_page [Business Role]_order_information_card)", "unique_field_name": "order_detail_page [Business Role]_order_information_card_to_branch"}, {"item_title": "SENDER_MOBILE", "item_label": "SENDER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_mobile_number)", "order_detail_card": "name(order_detail_page [Driver Role]_sender_information_card)", "unique_field_name": "order_detail_page [Driver Role]_sender_information_card_business_mobile_number"}, {"item_title": "SENDER_WHATSAPP_MOBILE", "item_label": "SENDER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Driver Role]_sender_information_card)", "unique_field_name": "order_detail_page [Driver Role]_sender_information_card_business_whatsapp_mobile"}, {"item_title": "SENDER_ADDRESS", "item_label": "SENDER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_address)", "order_detail_card": "name(order_detail_page [Driver Role]_sender_information_card)", "unique_field_name": "order_detail_page [Driver Role]_sender_information_card_business_address"}, {"item_title": "CUSTOMER_MOBILE", "item_label": "CUSTOMER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_mobile)", "order_detail_card": "name(order_detail_page [Driver Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Driver Role]_receiver_information_card_customer_mobile"}, {"item_title": "CUSTOMER_WHATSAPP_MOBILE", "item_label": "CUSTOMER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Driver Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Driver Role]_receiver_information_card_cus_whatsapp_mobile"}, {"item_title": "CUSTOMER_ADDRESS", "item_label": "CUSTOMER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_address)", "order_detail_card": "name(order_detail_page [Driver Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Driver Role]_receiver_information_card_customer_address"}, {"item_title": "MONEY_COLLECTION_COST", "item_label": "MONEY_COLLECTION_COST", "item_icon": "money_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "order_detail_card": "name(order_detail_page [Driver Role]_financial_information_card)", "unique_field_name": "order_detail_page [Driver Role]_financial_information_card_money_collection_cost"}, {"item_title": "DELIVERY_COST", "item_label": "DELIVERY_COST", "item_icon": "delivery_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "order_detail_card": "name(order_detail_page [Driver Role]_financial_information_card)", "unique_field_name": "order_detail_page [Driver Role]_financial_information_card_delivery_cost"}, {"item_title": "REQUIRED_FROM_BUSINESS", "item_label": "REQUIRED_FROM_BUSINESS", "item_icon": "required_from_business_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "order_detail_card": "name(order_detail_page [Driver Role]_financial_information_card)", "unique_field_name": "order_detail_page [Driver Role]_financial_information_card_required_from_business"}, {"item_title": "ORDER_TYPE", "item_label": "ORDER_TYPE", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card__order_type_id"}, {"item_title": "BUSINESS_NOTE", "item_label": "BUSINESS_NOTE", "item_icon": "business_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_product_note"}, {"item_title": "DRIVER_NOTE", "item_label": "DRIVER_NOTE", "item_icon": "driver_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_driver_note"}, {"item_title": "NOTE", "item_label": "NOTE", "item_field": "ref(rb_delivery.field_rb_delivery_order__note)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_note"}, {"item_title": "ORDER_CONTENT", "item_label": "ORDER_CONTENT", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__description_tags)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_description_tags"}, {"item_title": "DRIVER", "item_label": "DRIVER", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_agent_name"}, {"item_title": "CURRENT_BRANCH", "item_label": "CURRENT_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__current_branch)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_current_branch"}, {"item_title": "TO_BRANCH", "item_label": "TO_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__to_branch)", "order_detail_card": "name(order_detail_page [Driver Role]_order_information_card)", "unique_field_name": "order_detail_page [Driver Role]_order_information_card_to_branch"}, {"item_title": "SENDER_MOBILE", "item_label": "SENDER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_mobile_number)", "order_detail_card": "name(order_detail_page [Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Manager Role]_sender_information_card_business_mobile_number"}, {"item_title": "SENDER_WHATSAPP_MOBILE", "item_label": "SENDER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Manager Role]_sender_information_card_business_whatsapp_mobile"}, {"item_title": "SENDER_ADDRESS", "item_label": "SENDER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_address)", "order_detail_card": "name(order_detail_page [Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Manager Role]_sender_information_card_business_address"}, {"item_title": "CUSTOMER_MOBILE", "item_label": "CUSTOMER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_mobile)", "order_detail_card": "name(order_detail_page [Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Manager Role]_receiver_information_card_customer_mobile"}, {"item_title": "CUSTOMER_WHATSAPP_MOBILE", "item_label": "CUSTOMER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Manager Role]_receiver_information_card_cus_whatsapp_mobile"}, {"item_title": "CUSTOMER_ADDRESS", "item_label": "CUSTOMER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_address)", "order_detail_card": "name(order_detail_page [Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Manager Role]_receiver_information_card_customer_address"}, {"item_title": "MONEY_COLLECTION_COST", "item_label": "MONEY_COLLECTION_COST", "item_icon": "money_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "order_detail_card": "name(order_detail_page [Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Manager Role]_financial_information_card_money_collection_cost"}, {"item_title": "DELIVERY_COST", "item_label": "DELIVERY_COST", "item_icon": "delivery_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "order_detail_card": "name(order_detail_page [Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Manager Role]_financial_information_card_delivery_cost"}, {"item_title": "REQUIRED_FROM_BUSINESS", "item_label": "REQUIRED_FROM_BUSINESS", "item_icon": "required_from_business_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "order_detail_card": "name(order_detail_page [Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Manager Role]_financial_information_card_required_from_business"}, {"item_title": "ORDER_TYPE", "item_label": "ORDER_TYPE", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card__order_type_id"}, {"item_title": "BUSINESS_NOTE", "item_label": "BUSINESS_NOTE", "item_icon": "business_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_product_note"}, {"item_title": "DRIVER_NOTE", "item_label": "DRIVER_NOTE", "item_icon": "driver_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_driver_note"}, {"item_title": "NOTE", "item_label": "NOTE", "item_field": "ref(rb_delivery.field_rb_delivery_order__note)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_note"}, {"item_title": "ORDER_CONTENT", "item_label": "ORDER_CONTENT", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__description_tags)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_description_tags"}, {"item_title": "DRIVER", "item_label": "DRIVER", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_agent_name"}, {"item_title": "CURRENT_BRANCH", "item_label": "CURRENT_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__current_branch)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_current_branch"}, {"item_title": "TO_BRANCH", "item_label": "TO_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__to_branch)", "order_detail_card": "name(order_detail_page [Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Manager Role]_order_information_card_to_branch"}, {"item_title": "SENDER_MOBILE", "item_label": "SENDER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_mobile_number)", "order_detail_card": "name(order_detail_page [Super Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_sender_information_card_business_mobile_number"}, {"item_title": "SENDER_WHATSAPP_MOBILE", "item_label": "SENDER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Super Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_sender_information_card_business_whatsapp_mobile"}, {"item_title": "SENDER_ADDRESS", "item_label": "SENDER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_address)", "order_detail_card": "name(order_detail_page [Super Manager Role]_sender_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_sender_information_card_business_address"}, {"item_title": "CUSTOMER_MOBILE", "item_label": "CUSTOMER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_mobile)", "order_detail_card": "name(order_detail_page [Super Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_receiver_information_card_customer_mobile"}, {"item_title": "CUSTOMER_WHATSAPP_MOBILE", "item_label": "CUSTOMER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Super Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_receiver_information_card_cus_whatsapp_mobile"}, {"item_title": "CUSTOMER_ADDRESS", "item_label": "CUSTOMER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_address)", "order_detail_card": "name(order_detail_page [Super Manager Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_receiver_information_card_customer_address"}, {"item_title": "MONEY_COLLECTION_COST", "item_label": "MONEY_COLLECTION_COST", "item_icon": "money_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "order_detail_card": "name(order_detail_page [Super Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_financial_information_card_money_collection_cost"}, {"item_title": "DELIVERY_COST", "item_label": "DELIVERY_COST", "item_icon": "delivery_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "order_detail_card": "name(order_detail_page [Super Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_financial_information_card_delivery_cost"}, {"item_title": "REQUIRED_FROM_BUSINESS", "item_label": "REQUIRED_FROM_BUSINESS", "item_icon": "required_from_business_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "order_detail_card": "name(order_detail_page [Super Manager Role]_financial_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_financial_information_card_required_from_business"}, {"item_title": "ORDER_TYPE", "item_label": "ORDER_TYPE", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card__order_type_id"}, {"item_title": "BUSINESS_NOTE", "item_label": "BUSINESS_NOTE", "item_icon": "business_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_product_note"}, {"item_title": "DRIVER_NOTE", "item_label": "DRIVER_NOTE", "item_icon": "driver_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_driver_note"}, {"item_title": "NOTE", "item_label": "NOTE", "item_field": "ref(rb_delivery.field_rb_delivery_order__note)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_note"}, {"item_title": "ORDER_CONTENT", "item_label": "ORDER_CONTENT", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__description_tags)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_description_tags"}, {"item_title": "DRIVER", "item_label": "DRIVER", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_agent_name"}, {"item_title": "CURRENT_BRANCH", "item_label": "CURRENT_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__current_branch)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_current_branch"}, {"item_title": "TO_BRANCH", "item_label": "TO_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__to_branch)", "order_detail_card": "name(order_detail_page [Super Manager Role]_order_information_card)", "unique_field_name": "order_detail_page [Super Manager Role]_order_information_card_to_branch"}, {"item_title": "SENDER_MOBILE", "item_label": "SENDER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_mobile_number)", "order_detail_card": "name(order_detail_page [Call Center Role]_sender_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_sender_information_card_business_mobile_number"}, {"item_title": "SENDER_WHATSAPP_MOBILE", "item_label": "SENDER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Call Center Role]_sender_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_sender_information_card_business_whatsapp_mobile"}, {"item_title": "SENDER_ADDRESS", "item_label": "SENDER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__business_address)", "order_detail_card": "name(order_detail_page [Call Center Role]_sender_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_sender_information_card_business_address"}, {"item_title": "CUSTOMER_MOBILE", "item_label": "CUSTOMER_MOBILE", "item_icon": "mobile_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_mobile)", "order_detail_card": "name(order_detail_page [Call Center Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_receiver_information_card_customer_mobile"}, {"item_title": "CUSTOMER_WHATSAPP_MOBILE", "item_label": "CUSTOMER_WHATSAPP_MOBILE", "item_icon": "whatsapp_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__cus_whatsapp_mobile)", "order_detail_card": "name(order_detail_page [Call Center Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_receiver_information_card_cus_whatsapp_mobile"}, {"item_title": "CUSTOMER_ADDRESS", "item_label": "CUSTOMER_ADDRESS", "item_icon": "location_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__customer_address)", "order_detail_card": "name(order_detail_page [Call Center Role]_receiver_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_receiver_information_card_customer_address"}, {"item_title": "MONEY_COLLECTION_COST", "item_label": "MONEY_COLLECTION_COST", "item_icon": "money_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__money_collection_cost)", "order_detail_card": "name(order_detail_page [Call Center Role]_financial_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_financial_information_card_money_collection_cost"}, {"item_title": "DELIVERY_COST", "item_label": "DELIVERY_COST", "item_icon": "delivery_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__delivery_cost)", "order_detail_card": "name(order_detail_page [Call Center Role]_financial_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_financial_information_card_delivery_cost"}, {"item_title": "REQUIRED_FROM_BUSINESS", "item_label": "REQUIRED_FROM_BUSINESS", "item_icon": "required_from_business_cost_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__required_from_business)", "order_detail_card": "name(order_detail_page [Call Center Role]_financial_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_financial_information_card_required_from_business"}, {"item_title": "ORDER_TYPE", "item_label": "ORDER_TYPE", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__order_type_id)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card__order_type_id"}, {"item_title": "BUSINESS_NOTE", "item_label": "BUSINESS_NOTE", "item_icon": "business_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__product_note)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_product_note"}, {"item_title": "DRIVER_NOTE", "item_label": "DRIVER_NOTE", "item_icon": "driver_note_movify", "item_field": "ref(rb_delivery.field_rb_delivery_order__driver_note)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_driver_note"}, {"item_title": "NOTE", "item_label": "NOTE", "item_field": "ref(rb_delivery.field_rb_delivery_order__note)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_note"}, {"item_title": "ORDER_CONTENT", "item_label": "ORDER_CONTENT", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__description_tags)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_description_tags"}, {"item_title": "DRIVER", "item_label": "DRIVER", "item_icon": "", "item_field": "ref(rb_delivery.field_rb_delivery_order__agent_name)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_agent_name"}, {"item_title": "CURRENT_BRANCH", "item_label": "CURRENT_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__current_branch)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_current_branch"}, {"item_title": "TO_BRANCH", "item_label": "TO_BRANCH", "item_icon": "", "item_field": "ref(olivery_branch_collection.field_rb_delivery_order__to_branch)", "order_detail_card": "name(order_detail_page [Call Center Role]_order_information_card)", "unique_field_name": "order_detail_page [Call Center Role]_order_information_card_to_branch"}]