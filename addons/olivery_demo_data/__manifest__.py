# -*- coding: utf-8 -*-
{
    'name': "olivery_demo_data",
    'summary': """
        Olivery Demo Data from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.1.91',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/demo_creator/demo_creator_view.xml',
        'models/demo_package/demo_package_view.xml',
        'demo/demo_package_tags.xml',
        'demo/demo_packages.xml',
        'views/module_view.xml',
    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
    'auto_install': True
}
