import { Component, Injectable, ViewChild, Inject, PLATFORM_ID, HostListener, ElementRef } from '@angular/core';
import {Subject} from 'rxjs';
import * as XLSX from 'xlsx';
import { OrderService } from './service/create-orders-service';
import { TransactionService } from './service/create-transaction-service';
import { Order } from './order';
import { HttpClient } from '@angular/common/http';
import { MatPaginatorIntl, MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { getFieldService } from './service/get-options-service'
import { isPlatformBrowser } from '@angular/common';
import { Field } from './field';
import { NgbProgressbarConfig } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from "@ngx-translate/core";
import { TranslateHttpLoader } from "@ngx-translate/http-loader";
import { firstValueFrom } from 'rxjs';
import { ProgressSpinnerMode } from '@angular/material/progress-spinner';
import { ThemePalette } from '@angular/material/core';
import { OdooJsonRPC } from './service/odooJsonRPC';
import { finalize } from 'rxjs/operators';
import { AlertService } from './service/alert-service';
import { MatDialog } from '@angular/material/dialog';



var ALLOWED_FIELDS = [
  "reference_id",
  "assign_to_business",
  "customer_name",
  "customer_mobile",
  "customer_area",
  "customer_sub_area",
  'customer_address',
  "cost"
]

interface ParentChildField {
  parent: string;
  child: string;
  domain: [string, string, string];
}

interface FieldOptions {
  name: string;
  id: string;
}

const parentChildFields: ParentChildField[] = [
  {parent: 'customer_area', child: 'customer_sub_area', domain: ['parent_id', '=', 'customer_area']},
  // {parent: 'customer_sub_area', child: 'customer_address', domain: ['customer_sub_area', '=', 'customer_sub_area']}
];

export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http,window.location.origin+'/olivery_excel_advance/static/src/excel-advance/browser/assets/i18n/','.json');
}



@Injectable()
export class MyCustomPaginatorIntl implements MatPaginatorIntl {
  changes = new Subject<void>();

  // For internationalization, the `$localize` function from
  // the `@angular/localize` package can be used.
  firstPageLabel =`First page`;
  itemsPerPageLabel = `Items per page:`;
  lastPageLabel = `Last page`;

  // You can set labels to an arbitrary string too, or dynamically compute
  // it through other third-party internationalization libraries.
  nextPageLabel = 'Next page';
  previousPageLabel = 'Previous page';

  getRangeLabel(page: number, pageSize: number, length: number): string {
    if (length === 0) {
      return `Page 1 of 1`;
    }
    const amountPages = Math.ceil(length / pageSize);
    return `Page ${page + 1} of ${amountPages}`;
  }
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  rawFileData: any = null;
  title = 'advanced-excel';
  sortedHeader: string | null = null;
  sortDirection: string = 'asc'; 
  data: any;
  headers: string[] = [];
  sessionId:string=""
  pageSize: number = 150;
  currentPage: number = 0;
  Math = Math;
  senderOptions: string[] = []; 
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<any>();
  @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('inputElement') inputElement: ElementRef;
  formGroupArray: FormGroup[] = [];
  context: any;
  urlParams!:URLSearchParams

  isPageinatorEnabled: boolean = false;
  paginatorPageSize: number = 150;
  currentPageStart: number = 1;

  columnTitles: {[key: string]: string} = {};
  columnOptions = ALLOWED_FIELDS;

  optionsArray: { [key: string]: string[] } = {};

  fields: Field[] = [];
  fieldsOptions: { [key: string]: FieldOptions } = {};
  selectedColumnIds: string[] = [];

  indexesToSkip: number[] = [];
  progress: number = 0;

  isOnlyValid: boolean = false;
  isOnlyError: boolean = false;

  isValidated: boolean = false;
  isEditColumn: boolean = false;

  tempDataSource:any ;

  numberOf: number = 0;
  ofAll: number = 0;

  fileName: string = '';

  color: ThemePalette = 'primary';
  mode: ProgressSpinnerMode = 'determinate';
  value = 50;

  loading: boolean = false;
  SizeEditMode: boolean = false;

  skipCreateIndexs: number[] = []
  createdOrdersIndexs: number[] = []

  indexesToUpdate: number[] = []

  loadingIndexes: number[] = []

  editIndex: number = -1

  visible: boolean = false;
  codText: string = ''
  noOfOrdersText: string = ''
  titleMessage:string = ''
  totalCOD: number = 0
  showConfirm: boolean = true
  cancelText: string =''

  errDialogvisible: boolean = false
  errorsStrings: string[] = []
  orderIds: number[] = []

  constructor(
    private orderService: OrderService, 
    private transactionService: TransactionService, 
    private fieldService: getFieldService,
    private config: NgbProgressbarConfig,
    private translate: TranslateService,
    private odooRpc: OdooJsonRPC,
    private alertService: AlertService,
    private dialog: MatDialog,
    @Inject(PLATFORM_ID) private platformId: Object,
    // private translate: TranslateService,
  ) {
    this.translate.setDefaultLang('ar');
    this.urlParams = new URLSearchParams(window.location.search)
    this.context = JSON.parse((this.urlParams.get('context') as string))
    if('tz' in this.context){
      delete this.context['tz']
    }

    if(this.translate.currentLang=='ar' || this.translate.currentLang=='he'){
      document.documentElement.dir = 'rtl';
    }

    this.translate.use((this.context['lang'] as string).split('_')[0]);

    this.sessionId = this.urlParams.get('session') as string

    config.max = 1000;
		config.striped = true;
		config.animated = true;
		config.type = 'success';
		config.height = '20px';

  }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.urlParams = new URLSearchParams(window.location.search);
      this.context = JSON.parse((this.urlParams.get('context') as string));
      if ('tz' in this.context) {
        delete this.context['tz'];
      }


      this.sessionId = this.urlParams.get('session') as string;

      // Example of setting direction based on language, safely within the browser
      if(this.translate.currentLang=='ar' || this.translate.currentLang=='he'){
        document.documentElement.dir = 'rtl';
      }
      // this.translate.use((this.context['lang'] as string).split('_')[0]);

    }


  }

  changeColumnTitle(column: string, newTitle: string, index: number, event: any) {

    if (this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
      this.removeColumnIfNeeded(this.translate.instant('ERRORS'))
       index = index-1
    }
    if (this.displayedColumns.includes(this.translate.instant('CREATED'))) {
      this.removeColumnIfNeeded(this.translate.instant('CREATED'))
      index = index-1
    }
    if (!newTitle) {
      newTitle = this.translate.instant('DONT_IMPORT');
    }
    if (newTitle == this.translate.instant('DONT_IMPORT')) {
      newTitle == this.translate.instant('DONT_IMPORT')+ ' ' + this.columnTitles[column] 
    }
    if (this.displayedColumns.includes(newTitle) && newTitle != this.translate.instant('DONT_IMPORT')) {
      event.component.reset();
      this.alertService.showAlert(this.translate.instant('THIS_COLUMN_NAME_IS_ALREADY_IN_USE'));
    } else {
      this.displayedColumns[index] = newTitle;
      this.columnTitles[column] = newTitle;

  
      let mappedData = this.data.map((row: any[]) => {
        let obj: {[key: string]: any} = {};
        this.displayedColumns.forEach((key: string, index: number) => {
          if (key == this.translate.instant('DONT_IMPORT')) {
            obj[key] = null;
          } else  if (row[index] !== undefined) {
            obj[key] = row[index];
          }
        });
        return obj;
      });
  
      this.selectedColumnIds = this.displayedColumns.map((column) => {
        const field = this.fields.find(field => field.name === column);
        return field?.id ?? this.translate.instant('DONT_IMPORT');
      });
  
      this.headers = this.selectedColumnIds;
      this.dataSource.paginator = this.paginator;
      this.dataSource.data = mappedData;
      this.dataSource._updateChangeSubscription();
      this.updateSelectedFields();
    }
  }

  removeColumnIfNeeded(column: string) {
      // Remove column from displayed columns
      this.displayedColumns = this.displayedColumns.filter(col => col !== column);
  
      // Remove column data from data source
      this.dataSource.data = this.dataSource.data.map(row => {
        const newRow = { ...row };
        delete newRow[column];
        return newRow;
      });
  
      // Update data source
      this.dataSource._updateChangeSubscription();

  }

  async onFileChange(evt: any) {
    const target: DataTransfer = <DataTransfer>(evt.target);
    if (target.files.length !== 1) {
      this.alertService.showAlert('Cannot use multiple files');
      throw new Error('Cannot use multiple files');
    }
    
    const file: File = target.files[0];
    const validExtensions = ['.csv', '.xls', '.xlsx'];
    const fileExtension = file.name.slice(file.name.lastIndexOf('.')).toLowerCase();
  
    if (!validExtensions.includes(fileExtension)) {
      this.alertService.showAlert('Invalid file type. Only Excel files are allowed.');
      throw new Error('Invalid file type. Only Excel files are allowed.');
    }
  
    const reader: FileReader = new FileReader();
    reader.onload = async (e: any) => {
      this.fileName = file.name;
      const rawData: string = e.target.result;
      const base64Data = btoa(reader.result as string);
      this.rawFileData = base64Data;
      const workbook: XLSX.WorkBook = XLSX.read(rawData, { type: file.name.endsWith('.csv') ? 'string' : 'binary' });
      const wsname: string = workbook.SheetNames[0];
      const ws: XLSX.WorkSheet = workbook.Sheets[wsname];
      let data_list: any[] = XLSX.utils.sheet_to_json(ws, { header: 1, blankrows: true });
  
      data_list = data_list.filter(row => row.some((cell: any) => cell !== null && cell.toString().trim() !== ''));
      
      if (data_list.length > 3000) {
        this.alertService.showAlert('File contains more than 3000 records. Please upload a smaller file.');
        throw new Error('File contains more than 3000 records. Please upload a smaller file.');
      }
      let displayedColumns = data_list.shift() as string[];
      const invalidIndices:number[] = []

      for (let i = 0; i < displayedColumns.length; i++) {
        if (displayedColumns[i] === null || displayedColumns[i] === undefined || displayedColumns[i].toString().trim() === "") {
          invalidIndices.push(i);
        }
      }
      this.displayedColumns = displayedColumns.filter(cell => {
        return cell !== null && cell !== undefined && cell.toString().trim() !== "";
      })
      this.displayedColumns = this.displayedColumns.map(column => 
        column ? column.trim() : this.translate.instant('DONT_IMPORT')
      );
      for (let i = 0; i < data_list.length; i++) {
        let row = data_list[i];
        let newRow: any[] = [];        
        for (let j = 0; j < row.length; j++) {
          if (!invalidIndices.includes(j)) {
            newRow.push(row[j]);
          }
        }        
        data_list[i] = newRow;
      }
      let mappedData = data_list.map((row: any[]) => {
        let obj: { [key: string]: any } = {};
        this.displayedColumns.forEach((key: string, index: number) => {
          let value = row[index];
          obj[key] = typeof value === 'string' ? value.trim() : value !== undefined ? value : null;
        });
        return obj;
      });

      this.loading = true
      await this.getFields(this.displayedColumns);
  
      this.dataSource.data = mappedData; 
      this.data = data_list;
      this.headers = this.selectedColumnIds;
      this.dataSource.paginator = this.paginator;
  
      this.ofAll = this.dataSource.data.length;
      evt.target.value = '';
    };
  
    if (file.name.endsWith('.csv')) {
      reader.readAsText(file);
    } else {
      reader.readAsBinaryString(file);
    }
  }
  
  
  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
  }

  async registerOptions(displayedColumns: string[]) {
    for (let i = 0; i < displayedColumns.length; i++) {
      const field = this.fields.find(field => field.name === displayedColumns[i]);
      
      if(displayedColumns[i]) {
        this.fieldService.getFieldOptions(displayedColumns[i], [], this.context, this.sessionId).subscribe({
          next: response => {
            if(response.result.code != 200) {
              console.error('Error fetching options:', response);
            } else {
              if (response.result.isManyToOne) {
                this.fieldsOptions[displayedColumns[i]] = response.result.options;
                this.optionsArray[displayedColumns[i]] = response.result.options;
              }
            }
          },
          error: err => console.error('Error fetching options:', err)
        });
      }
    }
    this.loading = false
  }
  

  async processData() {
    this.visible=false
    const orders: Order[] = [];
    await this.validate();
    let dataIndex = 0
    this.skipCreateIndexs = []
    this.createdOrdersIndexs = []
    this.data.forEach((row: any[]) => {
      const order: Partial<Order> = {};
      this.headers = this.selectedColumnIds;
      let shouldCreateOrder = true;
    
      for (let index = 0; index < this.headers.length; index++) {
        const header = this.headers[index];
        const translatedCreated = this.translate.instant('CREATED');
        const translatedValid = this.translate.instant('VALID');
        if (this.dataSource.data[dataIndex][translatedCreated] && this.dataSource.data[dataIndex][translatedCreated] != '-1') {
          this.skipCreateIndexs.push(dataIndex)
          shouldCreateOrder = false;
          break;
        }
        if (this.dataSource.data[dataIndex][translatedValid] === '-1') {
          this.skipCreateIndexs.push(dataIndex)
          shouldCreateOrder = false;
          break;
        }
    
        order[header as keyof Order] = row[index];
      }
    
      if (shouldCreateOrder) {
        for (let key in order) {
          if (this.chackManyToOneCreate(key)) {
            const options = this.getOptionsCreate(key);
            for (let i = 0; i < options.length; i++) {
              if (options[i].name.includes(order[key as keyof Order])) {
                order[key as keyof Order] = options[i].id;
                break;
              }
            }
          }
        }
        orders.push(order as Order);
      }
      dataIndex = dataIndex+1
    });
    if (orders.length < 1) {
      return
    }
    this.createOrders(orders);
  }

  async createExcelTransaction() {
    let createdOrders = this.dataSource.data.filter((order, index) => {
      return !this.skipCreateIndexs.includes(index) && order.Created !== "-1";
    });
    let skippedOrders = this.dataSource.data.filter((order, index) => {
      return this.skipCreateIndexs.includes(index) && order.Created == "-1";
    });
    const response = await firstValueFrom(this.transactionService.createTransaction(createdOrders, skippedOrders, this.headers, this.rawFileData, this.fileName, this.context, this.sessionId));
  }
  
  async getConfirmation() {
    let number = this.data.length 
    if (this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
      number = this.dataSource.data.filter(row => (!row[this.translate.instant('ERRORS')])).length 
    }
    this.showConfirm = true
    this.noOfOrdersText = this.translate.instant('TOTAL_ORDERS_TO_BE_CREATED') + ': ' + number
    this.codText = this.translate.instant('TOTAL_COD') + ': 190$' 
    this.titleMessage = this.translate.instant('CONFIRMATION_MESSAGE')
    this.cancelText = this.translate.instant('CANCEL')
    this.visible = true
  }

  clear(){
    this.data = null;
    this.senderOptions = [];
    this.displayedColumns = [];
    this.dataSource = new MatTableDataSource<any>();
    this.paginator?.firstPage();
    this.isPageinatorEnabled = false;
    this.paginatorPageSize = 150;
    this.currentPageStart = 1;
    this.columnTitles = {};
    this.optionsArray = {};
    this.fields = [];
    this.fieldsOptions = {};
    this.selectedColumnIds = [];
    this.indexesToSkip = [];
    this.progress = 0;
    this.isOnlyValid = false;
    this.isOnlyError = false;
    this.isValidated = false;
    this.isEditColumn = false;
    this.tempDataSource = null;
    this.numberOf = 0;
    this.ofAll = 0;
    this.fileName = '';
    this.loading = false;
    this.skipCreateIndexs = [];
    this.createdOrdersIndexs = [];
    this.indexesToUpdate = [];
    this.loadingIndexes = [];
    this.visible = false;
    this.codText = '';
    this.noOfOrdersText = '';
    this.titleMessage = '';
    this.totalCOD = 0;
    this.showConfirm = true;
    this.cancelText = '';
    this.errDialogvisible = false;
    this.errorsStrings = [];
    this.orderIds = [];
   }

  async getSampleFile() {
    this.orderService.downloadSampleFile().subscribe({
      next: (response: Blob) => {
        const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
        
        const url = window.URL.createObjectURL(blob);
        
        const anchor = document.createElement('a');
        anchor.href = url;
        anchor.download = 'order_template.xls';  
        anchor.click();
        
        // Clean up the object URL
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading the file:', error);
      },
    });
  }
    

  async createOrders(orders: Order[]) {
    this.loading= true;
    if(this.isCreatable()) {
      this.loading= false;
      return;
    }
    if (!this.displayedColumns.includes(this.translate.instant('CREATED'))) {
      this.displayedColumns.unshift(this.translate.instant('CREATED'));
    }
    
    const orderChunks = this.splitOrdersIntoTenths(orders);
    let index = 0;
    // this.dataSource.data.forEach(row => {if (row[this.translate.instant('CREATED')] !== '-1') {delete row[this.translate.instant('ERRORS')]}});
    this.dataSource._updateChangeSubscription();
    this.progress = 0;
    this.processOrderChunks(orderChunks);
  }

  async processOrderChunks(orderChunks: any) {
    let index = 0;
    let flagIndex = 0;
    this.loading= true;
    this.totalCOD = 0
    for (const chunk of orderChunks) {
      try {
        const response = await firstValueFrom(this.orderService.createOrders(chunk, this.context, this.sessionId));
        if (response.result && response.result.sequences) {
          for (let i = 0; i < response.result.sequences.length; i++) {
            if (this.dataSource.data[index][this.translate.instant('CREATED')] && this.dataSource.data[index][this.translate.instant('CREATED')] === '-1') {
              index++;
              continue;
            }
            this.getProgressValue(true);
            index++;
          }
          let createLimit = response.result.sequences.length
          this.orderIds = [...this.orderIds, ...response.result.ids];
          this.totalCOD+= response.result.totalCOD
          let i = flagIndex
          let flagStopper = flagIndex
          let createdIndexes = 0
          let idsIndex = 0
          while (i < createLimit+this.skipCreateIndexs.length+flagIndex) {
            if (!this.skipCreateIndexs.includes(i)) {
              this.dataSource.data[i][this.translate.instant('CREATED')] = response.result.ids[idsIndex];
              this.dataSource.data[i][this.translate.instant('ERRORS')] = '';
              createdIndexes++
              idsIndex++
            } else if (this.skipCreateIndexs.includes(i) && this.dataSource.data[i][this.translate.instant('CREATED')] != '1') {
              this.dataSource.data[i][this.translate.instant('CREATED')] = '-1';
            }
            flagStopper++
            i++
          }
          flagIndex = flagStopper
        } else if (response.result && response.result.message) {
          if (!this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
            this.displayedColumns.unshift(this.translate.instant('ERRORS'));
          }
          for (let i = 0; i < response.result.message.length; i++) {
            this.dataSource.data[index][this.translate.instant('CREATED')] = '-1';
            this.dataSource.data[index][this.translate.instant('ERRORS')] = response.result.message[i];
            index++;
          }
        } else {
          if (response && response.error && response.error.data && response.error.data.message) {
            this.alertService.showAlert(response.error.data.message);
          }
          for (let i = 0; i < chunk.length; i++) {
            if (this.dataSource.data[index][this.translate.instant('CREATED')] != '1')
              this.dataSource.data[index][this.translate.instant('CREATED')] = '-1';
            index++;
          }
        }
      } catch (err) {
        for (let i = 0; i < chunk.length; i++) {
          if (this.dataSource.data.length > index &&this.dataSource.data[index].hasOwnProperty(this.translate.instant('CREATED')) && this.dataSource.data[index][this.translate.instant('CREATED')] != '1')
            this.dataSource.data[index][this.translate.instant('CREATED')] = '-1';
          index++;
        }
        console.error('Error creating orders:', err);
      }
      this.createExcelTransaction()
    }

    let number = this.data.length 
    let created = 0
    if (this.displayedColumns.includes(this.translate.instant('CREATED'))) {
      created = this.dataSource.data.filter(row => (row[this.translate.instant('CREATED')] > 0)).length 
    }
    this.noOfOrdersText =  created + ' ' + this.translate.instant('ORDERS_OUT_OF') + ': ' + number 
    this.codText = this.translate.instant('TOTAL_COD') + ': ' + this.totalCOD
    this.titleMessage = this.translate.instant('ORDERS_CREATED')
    this.visible = true
    this.loading= false;
    this.cancelText = 'CLOSE'
    this.showConfirm = false
  }

  splitOrdersIntoTenths(orders: Order[]): Order[][] {
    const totalOrders = orders.length;
    const chunkSize = Math.ceil(totalOrders / 5);
    const chunks: Order[][] = [];

    
    if(totalOrders > 150) {
      for (let i = 0; i < totalOrders; i += chunkSize) {
        const chunk = orders.slice(i, i + chunkSize);
        chunks.push(chunk);
      }
    } else {
      chunks.push(orders);
    }
    return chunks;
  }

  async validate(): Promise<any> {
    this.loading= true;
    this.extractData()
    this.removeColumnIfNeeded(this.translate.instant('VALID'))
    this.removeColumnIfNeeded(this.translate.instant('ERRORS'))
    return new Promise((resolve, reject) => {
      // Adding 'Valid' column if it does not exist

      // Ensure the 'errors' column is present for displaying validation errors
      if (!this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
        this.displayedColumns.unshift(this.translate.instant('ERRORS'));
      }


      var filteredHeaders = this.filterHeaders(this.selectedColumnIds);
      var filteredData = this.filterData(this.selectedColumnIds, this.data);      
      this.orderService.ValidateOrders(filteredData, filteredHeaders, this.context, this.sessionId).subscribe({
        next: response => {
          if(response.result && response.result.date_fields){
            let lookupHeaders = this.displayedColumns.reduce<string[]>((ids, column) => {
                const field = this.fields.find(field => field.name === column);
                if (field && field.name !== undefined) {
                  ids.push(field.name);
                }
                return ids;
              }, []);
            let date_fields = response.result.date_fields
            for(let ind = 0; ind<date_fields.length;ind++){
              let date_field = date_fields[ind]
              let dateHeader = lookupHeaders[date_field.col_index]
              this.dataSource.data[date_field.row_index][dateHeader] = date_field.value
            }
          }
          
          this.isValidated = true;
          if (response.error && response.error.data) {
            this.alertService.showAlert(response.error.data.message);
            if (this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
              this.removeColumnIfNeeded(this.translate.instant('ERRORS'))
            }
            if (this.displayedColumns.includes(this.translate.instant('CREATED'))) {
              this.removeColumnIfNeeded(this.translate.instant('CREATED'))
            }
            this.loading= false;
            if (this.isOnlyValid) {
              this.updateValidity('VALID');
            } 
            if (this.isOnlyError) {
              this.updateValidity('ERRORS');
            }
            resolve("Validation failed with errors");
          }
          if (response.result && !response.result.success && response.result.message) {
            var indexes: number[] = [];
            this.alertService.showAlert(this.translate.instant('VALIDATION_FAILED_WITH_ERRORS_IN') + ' ' + (response.result.message.length) + ' ' + this.translate.instant('ROWS'));
            for (let i = 0; i < response.result.message.length; i++) {
              if (response.result.message[i].messages && response.result.message[i].messages.length > 0) {  
                let combinedMessage = ''
                let messagesErros = response.result.message[i]
                this.dataSource.data[response.result.message[i].index][this.translate.instant('VALID')] = '-1';
                for (let j = 0; j < messagesErros.messages.length; j++) {
                  let formatedMessage = this.formatErrorMessage(messagesErros.messages[j])
                  combinedMessage += formatedMessage +'\n\n'
                }
                indexes.push(response.result.message[i].index)
                this.dataSource.data[response.result.message[i].index][this.translate.instant('ERRORS')] = combinedMessage;
              }
              if (this.isOnlyValid) {
                this.updateValidity('VALID');
              } 
              if (this.isOnlyError) {
                this.updateValidity('ERRORS');
              }
            }

            const allIndices = this.dataSource.data.map((_, index) => index);
            const notInIndexes = allIndices.filter(index => !indexes.includes(index));

            this.numberOf = notInIndexes.length;
            // add valid == 1 to all valid row 
            for (let i = 0; i < this.dataSource.data.length; i++) {
              if(notInIndexes.includes(i)){
                this.dataSource.data[i][this.translate.instant('VALID')] = '1'; // Mark as valid
                this.dataSource.data[i][this.translate.instant('ERRORS')] = '';
              }
            }
            this.getProgressValue(true);
            this.dataSource._updateChangeSubscription();
            this.loading= false;
            if (this.isOnlyValid) {
              this.updateValidity('VALID');
            } 
            if (this.isOnlyError) {
              this.updateValidity('ERRORS');
            }
            resolve("Validation failed with errors");
          } else {
            this.numberOf = this.dataSource.data.length;
            this.getProgressValue(true);
            // Cleanup 'errors' column if validation passed
            this.dataSource.data.forEach(row => delete row[this.translate.instant('ERRORS')]);
            this.dataSource._updateChangeSubscription();
            this.getProgressValue(true);
            // Mark all rows as valid
            for (let i = 0; i < this.dataSource.data.length; i++) {
              this.dataSource.data[i][this.translate.instant('VALID')] = '1'; // Mark as valid (changed from '0' to '1')
            }
            this.loading= false;
            if (this.isOnlyValid) {
              this.updateValidity('VALID');
            } 
            if (this.isOnlyError) {
              this.updateValidity('ERRORS');
            }
            resolve("Validation passed without errors");
          }

        },
        error: err => {
          console.error('Error validating orders:', err);
          this.alertService.showAlert(err);
          // Ensure the 'errors' column is present when an error occurs
          if (!this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
            this.displayedColumns.unshift(this.translate.instant('ERRORS'));
          }
          this.loading= false;
          reject(err);
        }
      });
    });
  }


  extractData() {
    const excludeKeys = [this.translate.instant('ERRORS'), this.translate.instant('VALID'), this.translate.instant('CREATED')];

    this.data = this.dataSource.data.map(item => {
      return this.displayedColumns
        .filter(key => !excludeKeys.includes(key))
        .map(key => item[key]);
    });
  }


  formatErrorMessage(errorMessage: string): string {
    let cleanedMsg = errorMessage.replace(/^(\('\s*)/, "");

    // Remove ', '') from the end
    cleanedMsg = cleanedMsg.replace(/(\s*'\s*,\s*''\))$/, "");

    cleanedMsg.replace("', '')", "");
  
    // Split the error message by the dot (.) and trim any whitespace
    const errors = cleanedMsg.split('\\n').map(error => error.trim());
    
    // Remove duplicate errors
    const uniqueErrors = [...new Set(errors)];

    let text = uniqueErrors.join('\n').replace("', '')", '')
  
    // Join the unique errors with a new line character
    return text;
  }
  
  

  

  toggleEdit(row: any) {
    row.editing = !row.editing;
  }

  getOptions(column: string): any {
    const field = this.fields.find(field => field.name === column);
    return this.fieldsOptions[field?.id != null ? field?.id : 'NULL'];
  }

  getOptionsCreate(column: string): any {
    return this.fieldsOptions[column];
  }
  
  updateField(element: any, column: string, index: number) {
    if (element[column] !== undefined) {

      let columnIndex = this.displayedColumns.indexOf(column);


      this.dataSource.data[index][column] = element[column];

      
      this.dataSource._updateChangeSubscription();


      const matchingElementsCount = [this.translate.instant('VALID'), this.translate.instant('ERRORS'), this.translate.instant('CREATED')].filter(item => this.displayedColumns.includes(item)).length;
      if (matchingElementsCount) {
        columnIndex = columnIndex + matchingElementsCount
      }

      
      if (columnIndex !== -1) {
        this.data[index][columnIndex] = element[column];
      }
    }
  }
  

  updateSelectionField(element: any, column: string, index: number, newValue:any) {
    if (newValue !== undefined) {
      element[column] = newValue[0]
      this.dataSource.data[index][column] = newValue[0];
      this.dataSource._updateChangeSubscription();
      this.data = this.dataSource.data.map(Object.values);
    }
  }
  
  chackManyToOne(column: string) {
    const field = this.fields.find(field => field.name === column);
    return this.optionsArray[field?.id != null ? field?.id : 'NULL'] ? true : false;
  }

  chackManyToOneCreate(column: string) {
    const field = this.fields.find(field => field.id === column);
    return this.optionsArray[field?.id != null ? field?.id : 'NULL'] ? true : false;
  }

  exportData() {

  }

  getDialogIcon(created: string, column: string) {
    if(column === this.translate.instant('VALID')) {
      if (created === '1') {
        return '/olivery_excel_advance/static/src/excel-advance/browser/assets/done.svg';
      } else if (created === '-1'){
        return '/olivery_excel_advance/static/src/excel-advance/browser/assets/fail.svg';
      } else {
        return '/olivery_excel_advance/static/src/excel-advance/browser/assets/loading.svg';
      }
    }


    if (created === '1') {
      return '/olivery_excel_advance/static/src/excel-advance/browser/assets/done.svg';
    } else if (created === '-1'){
      return '/olivery_excel_advance/static/src/excel-advance/browser/assets/fail.svg';
    } else {
      return '/olivery_excel_advance/static/src/excel-advance/browser/assets/loading.svg';
    }

  }

  isLoadingIcon(value: any, column: string): boolean {
    const iconPath = this.getDialogIcon(value, column);
    return iconPath.includes('loading.svg');
  }

  async getFields(fields: string[]) {
    this.fieldService.getFields(this.context, this.sessionId, fields).subscribe({
      next: response => {
        if (response.result && response.result.code === 200 && response.result.fields) {
          this.fields = response.result.fields; 
          ALLOWED_FIELDS.push(...this.fields.map(field => field.name ?? this.translate.instant('DONT_IMPORT')));
          this.updateSelectedFields();
        } else {
          console.error('Error fetching fields:', response);
        }
      },
      error: err => console.error('Error fetching fields:', err)
    });
  }

  updateSelectedFields() {
    this.selectedColumnIds = [];
    for (let i = 0; i < this.displayedColumns.length; i++) {
      const selectedField = this.fields.find(field => field.name === this.displayedColumns[i]);
      this.selectedColumnIds.push(selectedField?.id ?? this.translate.instant('DONT_IMPORT'));
      this.getOptions(this.displayedColumns[i]);
    }
    this.registerOptions(this.selectedColumnIds);

  }

  isCreatable() {
    if(this.isEditColumn) {
      return true;
    }
    return this.displayedColumns.includes('ERRORS');
  }

  isValidatable() {
    if(this.isEditColumn) {
      return true;
    }
    return false
  }

  enableEditColumn() {
    this.isEditColumn = !this.isEditColumn;
    if (this.isEditColumn) {
      // this.updateSelectedFields();
    } else {
      this.updateSelectedFields();
    }
  }

  getProgressValue(isValidation: boolean) {
    if (isValidation) {
      this.progress = this.dataSource.data.filter(row => row[this.translate.instant('VALID')] === '1').length / this.dataSource.data.length * 1000;
    } else 
      this.progress =  this.dataSource.data.filter(row => row[this.translate.instant('CREATED')] === '1').length / this.dataSource.data.length * 1000;
  }

  filterData(headers: string[], data: (string | number | null)[][]): any {
    const indicesToKeep = headers.map((value, index) => {
      return value !== this.translate.instant('DONT_IMPORT') ? index : -1;
    }).filter(index => index !== -1);
  
    const filteredData = data.map(row => {
      return indicesToKeep.map(index => {
        return row[index] !== null ? row[index] : undefined;
      });
    });
  
    return filteredData;
  }
  

  filterHeaders(headers: string[]): string[] {
    return headers.filter(header => header !== this.translate.instant('DONT_IMPORT'));
  }

  isUnknownCol(column: string): boolean {
    if (this.fields.find(field => field.name === column)) {
      return false;
    }
    return true
  }

  updateValidity(filterType: string): void {
    if (filterType === 'VALID') {
      if (this.isOnlyError) {
        this.isOnlyError = false;
        this.dataSource.data = this.tempDataSource
      }

      if (this.isOnlyValid) {
        this.tempDataSource = this.dataSource.data
        this.dataSource.data = this.dataSource.data.filter(row => (row[this.translate.instant('VALID')] === '1' || !row[this.translate.instant('VALID')]));
        this.dataSource._updateChangeSubscription();
      } else {
        this.dataSource.data = this.tempDataSource
        this.dataSource._updateChangeSubscription();
      }
      
    } else if (filterType === 'ERRORS') {
      if (this.isOnlyValid) {
        this.isOnlyValid = false;
        this.dataSource.data = this.tempDataSource
      }

      if (this.isOnlyError) {
        this.tempDataSource = this.dataSource.data
        this.dataSource.data = this.dataSource.data.filter(row => row[this.translate.instant('VALID')] === '-1');
        this.dataSource._updateChangeSubscription();
      } else {
        this.dataSource.data = this.tempDataSource
        this.dataSource._updateChangeSubscription();
      }
    }
  }

  setPageSize() {
    this.pageSize = Math.max(1, this.paginatorPageSize);
    this.dataSource.paginator = this.paginator;
    this.dataSource.paginator.pageSize = this.pageSize; // Ensure pageSize is updated
    this.dataSource._updateChangeSubscription();
  }

  toggleEditPaginator() {
    this.isPageinatorEnabled = true;
  }

  getTransulation(key: string): string {
    return this.translate.instant(key);
  }

  toggleEditSize(): void {
    this.SizeEditMode = !this.SizeEditMode;
    if (!this.SizeEditMode) {
      this.setPageSize();
    }
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    if (this.SizeEditMode && this.inputElement && !this.inputElement.nativeElement.contains(event.target)) {
      this.toggleEditSize();
    }
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.toggleEditSize();
    }
  }

  decrementPage(): void {
    if (this.paginator.pageIndex === 0) {
      return;
    }
    this.paginator.previousPage();
  }

  incrementPage(): void {
    if (this.paginator.pageIndex === this.paginator.getNumberOfPages() - 1) {
      return;
    }
    this.paginator.nextPage();
  }

  getCurrentIndex() {
    if (this.paginator.pageIndex * this.paginator.pageSize + 1 > this.dataSource.data.length) {
      return this.dataSource.data.length;
    }
    return this.paginator.pageIndex * this.paginator.pageSize + 1;
  }

  getCurrentIndexMax() {
    if (this.getCurrentIndex() + this.paginatorPageSize > this.dataSource.data.length) {
      return this.dataSource.data.length;
    }
    return this.getCurrentIndex() + this.paginatorPageSize
  }

  FuzzySearchAllFields() {
    let fieldsToFuzzy = []
    let indexes = []
    for (let index = 0; index < this.displayedColumns.length; index++) {
      let col = this.displayedColumns[index];
      if (this.isUnknownCol(col)) {
        if (col == this.translate.instant('DONT_IMPORT') || col === this.translate.instant('ERRORS') || col === this.translate.instant('VALID') || col === this.translate.instant('CREATED')) {
          continue
        }
        fieldsToFuzzy.push(col)
        indexes.push(index)
        
      }
    }
    if (indexes) {
      this.fuzzySearchField(fieldsToFuzzy, indexes);
    }
  }

  fuzzySearchField(cols: string[], indexs: number[]) {

    for (let index of indexs) {
      this.loadingIndexes.push(index);
    }
    this.fieldService.getFuzzyField(this.context, this.sessionId, cols)
      .pipe(
        finalize(() => {
          this.loadingIndexes = []
        })
      )
      .subscribe({
        next: response => {
          if (response.result) {
            for (let i = 0; i < indexs.length; i++) {
              let index = indexs[i]
              let col = cols[i]
              let newTitles = [];
              let search_res = this.fields.find(field => field.id === response.result[i].id)
              if (search_res) {
                newTitles.push(search_res.name)
              }
              newTitles.push(response.result[i].name)
              newTitles.push(response.result[i].another_names)
  
              for (let newTitle of newTitles) {
                if (!newTitle) continue;
  
                if (!this.fields.find(field => field.name === newTitle)) continue;
    
                if (this.displayedColumns.includes(this.translate.instant('ERRORS'))) {
                  this.removeColumnIfNeeded(this.translate.instant('ERRORS'));
                  index = index - 1;
                }
                if (this.displayedColumns.includes(this.translate.instant('CREATED'))) {
                  this.removeColumnIfNeeded(this.translate.instant('CREATED'));
                  index = index - 1;
                }
                if (!newTitle) {
                  newTitle = this.translate.instant('DONT_IMPORT');
                }
                if (newTitle == this.translate.instant('DONT_IMPORT')) {
                  newTitle = this.translate.instant('DONT_IMPORT') + ' ' + this.columnTitles[col];
                }
                if (this.displayedColumns.includes(newTitle) && newTitle != this.translate.instant('DONT_IMPORT')) {
                  // this.alertService.showAlert(this.translate.instant('THIS_COLUMN_NAME_IS_ALREADY_IN_USE'));
                } else {
                  this.displayedColumns[index] = newTitle;
                  this.columnTitles[col] = newTitle;
    
                  let mappedData = this.data.map((row: any[]) => {
                    let obj: { [key: string]: any } = {};
                    this.displayedColumns.forEach((key: string, idx: number) => {
                      if (key == this.translate.instant('DONT_IMPORT')) {
                        obj[key] = null;
                      } else if (row[idx] !== undefined) {
                        obj[key] = row[idx];
                      }
                    });
                    return obj;
                  });
    
                  this.selectedColumnIds = this.displayedColumns.map(column => {
                    const field = this.fields.find(field => field.name === column);
                    return field?.id ?? this.translate.instant('DONT_IMPORT');
                  });
    
                  this.headers = this.selectedColumnIds;
                  this.dataSource.paginator = this.paginator;
                  this.dataSource.data = mappedData;
                }
              }
            }
            this.dataSource._updateChangeSubscription();
            this.updateSelectedFields();
          } else {
            console.error('Error fetching fields:', response);
          }
        },
        error: err => {
          console.error('Error fetching fields:', err);
        }
      });
  }

  enableEditing(index: number) {
    this.toggleEdit(this.dataSource.data[index])
    if (this.editIndex >= 0 && this.editIndex != index) {
      this.toggleEdit(this.dataSource.data[this.editIndex])
    }
    if (this.editIndex != index) {
      this.editIndex = index
    } else {
      this.editIndex = -1
    }
  }

  openErrors(errors: string) {
    this.errorsStrings = Array.from(new Set(errors.split('\n')));
    this.errDialogvisible = true
  }

  getParsedInput(input: string) : string[] {

    let input_arr = input.split('(\'')

    if (input_arr.length < 2) {
      return [input]
    }
    input = input_arr[1].slice(0, -2);


    let first_spliter : string = ''
    let message : string = ''
    let type : string = ''
    let code : string = ''
    let title : string = ''
    let whatToDo : string = ''


    if (input.toLowerCase().includes('[error]')) {
        first_spliter = '[Error]'
    } else if (input.toLowerCase().includes('[warning]')) {
        first_spliter = '[Warning]'
    } else if (input.toLowerCase().includes('[success]')) {
        first_spliter = '[Success]'
    } else {
        message = input;
        if (this.title.length == 0) {
            this.title = this.translate.instant('UNEXPECTED_ERROR_OCCURRED.');
        }
        return [input];
    }

    var data = input.split(first_spliter)


    type = first_spliter.replace('[','').replace(']','').trim()

    if (data.length > 1) {
        message = data[0].replace('("','').replace('")','').trim();
        code = this.translate.instant(this.extractErrorCode(data[1]));
        title = this.translate.instant(this.extractErrorMessage(data[1]));
        whatToDo = this.translate.instant(this.extractWhatToDo(data[1]));

        if(whatToDo.length == 0 && input.split(']]}').length > 0){
            whatToDo = input.split(']]}')[1];
        } else if (whatToDo.length == 0) {
            whatToDo = this.translate.instant('PLEASE_CONTACT_YOUR_ADMINISTRATOR.')
        }
    }
    return [message, whatToDo.replaceAll('\', \'', '')]
  }

  private extractErrorCode(text: string): string  {
    const errorCodePattern = /\[\[\((\d+)\)/;
    const match = text.match(errorCodePattern);
    return match ? match[1] : '';
  }

  private extractErrorMessage(text: string): string {
    const errorMessagePattern = /\(\d+\)\s*([^\]]+)/;
    const match = text.match(errorMessagePattern);
    return match ? match[1].trim() : '';
  }
  

  private extractWhatToDo(text: string): string  {
    let whattoDo = text.split(']]}')
    if (whattoDo.length > 1) {
        return text.split(']]}')[1]
    }
    const whatToDoPattern = /]]}",\s*(.+)/s;
    const match = text.match(whatToDoPattern);
    return match ? match[1]  :  '';
  }

  openRecord(id: any) {
    if (id) {
      const currentDomain = window.location.origin;
      const orderUrl = `${currentDomain}/web#id=${id}&model=rb_delivery.order&view_type=form`;
  
      window.open(orderUrl, '_blank');
    } else {
      this.alertService.showAlert(this.translate.instant('NO_ID_WAS_PROVIDED_FOR_THIS_ORDER'))
    }
  }

  async moveToOrders() {
    try {
      let linkRequest = await this.odooRpc.call('rb_delivery.order', 'get_orders_action', [[],Array.from(new Set(this.orderIds))]);
      if (linkRequest && linkRequest.body && linkRequest.body.result && linkRequest.body.result.result.success && linkRequest.body.result.result.result) {
        let link = linkRequest.body.result.result.result;
          window.open(link, '_blank');
      }
    } catch (error) {
      console.error('Error while fetching the link:', error);
    }
  }


}