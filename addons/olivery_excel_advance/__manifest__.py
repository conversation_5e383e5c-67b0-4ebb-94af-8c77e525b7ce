# -*- coding: utf-8 -*-
{
    'name': "olivery_excel_advance",
    'summary': """
        Olivery Excel advance App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-1.1.19',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'security/excel_advance_security.xml',
        'models/import_transaction/import_transaction_view.xml',
        'models/import_transaction_item/import_transaction_item_view.xml',
        'models/excel_terms/excel_terms_view.xml',
        'models/excel_transaction_item/excel_transaction_item_view.xml',
        'models/excel_transaction/excel_transaction_view.xml',
        'models/translated_terms/translated_terms_view.xml',
        'views/template.xml',
        'views/module_view.xml',
        'demo/translated_terms.xml',
        'demo/client_conf.xml'
    ],
    'qweb': [
         'static/src/xml/*.xml',
         'static/src/xml/excel_view.xml',
    ],
}