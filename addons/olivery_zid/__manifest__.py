# -*- coding: utf-8 -*-
{
    'name': "olivery_zid",
    'summary': """
        <PERSON>y ZID App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.0.16',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/zid_event/zid_events_view.xml',
        'demo/client_conf.xml',
        'views/print/waybill_a4.xml',
        'views/print/waybill_a5.xml',
        'models/order_attachment/order_attachment_view.xml',
        'models/zid_logs/zid_logs_view.xml',
        'models/zid_field_map/zid_field_map_view.xml'
    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
}