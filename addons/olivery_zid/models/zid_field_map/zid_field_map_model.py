import logging
from openerp import models, fields, api,_
_logger = logging.getLogger(__name__)


class olivery_zid_zid_field_map(models.Model):

    _name = 'rb_delivery.zid_field_map'

    field_group_id = fields.Char('Field group name', track_visibility="on_change", required=True)

    zid_key = fields.Char('zid Key', required=True)

    order_field = fields.Many2one('ir.model.fields', track_visibility="on_change", domain=[['model', '=','rb_delivery.order'], ['ttype', 'in',['char', 'text']]], required=True)

    beautify_json = fields.<PERSON><PERSON>an('Beautify Josn')
