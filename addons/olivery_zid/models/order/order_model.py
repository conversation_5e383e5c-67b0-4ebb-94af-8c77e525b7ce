
from openerp import models, api, _
import json
import requests
import logging
import base64
_logger = logging.getLogger(__name__)


class olivery_zid_order(models.Model):

    _inherit = 'rb_delivery.order'

    def json_to_readable_string(self, json_data, values):
        custom_fields_storage = None
        current_index = 1

        def append_new_custom_fields(custom_fields,values):
            nonlocal current_index
            for field in custom_fields:
                if not field.get('group_id'):
                    continue
                map_item = self.env['rb_delivery.zid_field_map'].sudo().search([('field_group_id', '=', field.get('group_name'))], limit=1)
                if not map_item:
                    continue
                if field.get(map_item.zid_key) and values.get(map_item.sudo().order_field.name) and map_item.sudo().order_field.name not in ['customer_mobile', 'second_mobile_number', 'alt_mobile_number', 'second_business_mobile_number']:
                    values[map_item.sudo().order_field.name] += _('\n Product %s : %s') % (current_index, field.get(map_item.zid_key))
                elif map_item.sudo().order_field.name not in ['customer_mobile', 'second_mobile_number', 'alt_mobile_number', 'second_business_mobile_number']:
                    values[map_item.sudo().order_field.name] = _('\n Product %s : %s') % (current_index, field.get(map_item.zid_key))
                else:
                    values[map_item.sudo().order_field.name] = field.get(map_item.zid_key)
            current_index+=1
            
        def process_item(item, values):
            nonlocal custom_fields_storage
            if 'custom_fields' in item:
                custom_fields_storage = item.get('custom_fields')
                item.pop('custom_fields')
            
            if custom_fields_storage:
                append_new_custom_fields(custom_fields_storage, values=values)
                custom_fields_storage = False

            item.pop('id', None)

            name = item.get('name', '')
            quantity = item.get('quantity', '')
            sku = item.get('sku', '')

            components = []
            if name:
                components.append(_('name: ')+str(name))
            if quantity:
                components.append(_(' quantity: ')+str(quantity))
            if sku:
                components.append(_(' sku: ')+str(sku))

            return ' - '.join(components)

        def process_json(data, values):
            """Recursively process a JSON object (dict or list) to convert into a readable string."""
            if isinstance(data, dict):
                return process_item(data, values)
            elif isinstance(data, list):
                return "\n".join(process_item(item, values) for item in data)
            else:
                return str(data)

        try:
            parsed_data = json.loads(json_data) if isinstance(json_data, str) else json_data
            beautified_string = process_json(parsed_data, values)

            return beautified_string
        except (json.JSONDecodeError, TypeError):
            return json_data


    @api.model
    def create(self, values):
        product_notes = []        
        if 'product_note' in values and values['product_note']:
            product_notes = json.loads(values['product_note']) 
            values['product_note'] = self.json_to_readable_string(values['product_note'], values)
        self._update_payment_type(values)

        order = super(olivery_zid_order, self).create(values)
        if order and product_notes:
            attachment_model = self.env['rb_delivery.order_attachment']
            for product in product_notes:
                image_binary = None
                if product.get('image'):
                    try:
                        response = requests.get(product['image'], timeout=20)
                        if response.status_code == 200:
                            image_binary = base64.b64encode(response.content).decode('utf-8')
                    except Exception as e:
                        _logger.warning(f"Failed to download image: {product.get('image')} - {str(e)}")

                attachment_model.create({
                    'order_id': order.id,
                    'product_name': product.get('product_name'),
                    'quantity': product.get('quantity'),
                    'sku': product.get('sku'),
                    'product_id': product.get('id'),
                    'attachment': image_binary,
                })
        return order

    @api.multi
    def write(self, values):
        if 'product_note' in values and values['product_note']:
            values['product_note'] = self.json_to_readable_string(values['product_note'])
        self._update_payment_type(values)

        return super(olivery_zid_order, self).write(values)

    def _update_payment_type(self,values):
        if 'payment_code' in values and values['payment_code']:
            payment_type = self.env['rb_delivery.payment_type'].sudo().search([('code', '=', values['payment_code'])],limit=1)
            if payment_type:
                values['payment_type'] = payment_type.id
