from openerp import models, fields, api
from openerp.exceptions import ValidationError
from datetime import date


class olivery_pickup_request_status(models.Model):

    _inherit = 'rb_delivery.status'

    @api.model
    def get_model_status_type(self, model_name):
        self.MODEL_STATUS_TYPE_MAP['rb_delivery.pickup_request'] = 'olivery_collection'
        return super(olivery_pickup_request_status, self).get_model_status_type(model_name)
    
    @api.model
    def get_model_collection_type(self, model_name):
        self.MODEL_COLLECTION_TYPE_MAP['rb_delivery.pickup_request'] = 'pickup_request'
        return super(olivery_pickup_request_status, self).get_model_collection_type(model_name)

    @api.model
    def get_collection_type(self):
        collection_types = super(olivery_pickup_request_status, self).get_collection_type()
        collection_types.append(('pickup_request','Pickup Request'))
        return collection_types

