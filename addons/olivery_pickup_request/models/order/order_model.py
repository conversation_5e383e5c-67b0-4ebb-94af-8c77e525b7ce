from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import date


class olivery_pickup_request_order(models.Model):

    _inherit = 'rb_delivery.order'

    pickup_request_id = fields.Many2one('rb_delivery.pickup_request', 'Pickup Request ID',copy=False)

    transportation_way = fields.Char("Transportation way", track_visibility="on_change")
    
    reschedule_date = fields.Datetime("Scheduled Delivery",track_visibility="on_change")    
    
    def get_pickup_request(self):
        address_form_id = self.env.ref('olivery_pickup_request.view_tree_rb_delivery_pickup_request').id
        domain = [('order_ids', 'in', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.pickup_request',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def guard_function(self,values):
        for rec in self:
            if values.get('pickup_request_id'):
                pickup_request = self.env['rb_delivery.pickup_request'].search(['|', ('id','=',values['pickup_request_id']), ('sequence', '=', values['pickup_request_id'])])
                if pickup_request:
                    pickup_request.write({'order_ids': [(4, rec.id)] })
        return super(olivery_pickup_request_order, self).guard_function(values)
        

    @api.model
    def create(self,values):
        order = super(olivery_pickup_request_order, self).create(values)
        if 'pickup_request_id' in values and values['pickup_request_id']:
            pickup_request = self.env['rb_delivery.pickup_request'].search([('id','=',values['pickup_request_id'])])
            if pickup_request:
                pickup_request.write({'order_ids': [(4, order.id)] })
        return order

class order_multi_create_pickup_request_wizard(models.TransientModel):
    _name = 'rb_delivery.multi_create_pickup_request' 
    _description = 'Bulk create pickup request'

    
    def create_multi_collection(self, docs):
        new_doc = []
        new_docs = []
        business_list = []
        for doc in docs:
            if doc.assign_to_business.id not in business_list:
                business_list.append(doc.assign_to_business.id)
        for business in business_list:
            for doc in docs:
                if doc.assign_to_business.id == business:
                    new_doc.append(doc)
            new_docs.append(new_doc)
            new_doc= []
        print(new_docs)
        return new_docs  

    def create_pickup_requests(self):
        active_ids = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        recs = self.create_multi_collection(active_ids)  
        ids = []
        for rec in recs:
            for order in rec:
                collection_status = self.env['rb_delivery.client_configuration'].get_param('default_pickup_request_status')
                collection_states = []
                message = ''
                for state_id in collection_status:
                    state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                    collection_states.append(state.name)
                    message = message +' ' +state.title +'/'
                if order.state and order.state not in collection_states:
                    raise ValidationError(_('Order state should be in ')+message)
                pickup_request = self.env['rb_delivery.pickup_request'].sudo().search([('order_ids','=',order.id)])
                if not pickup_request:
                    ids.append(order.id)
            if len(ids)>0:
                assign_to_business = order.assign_to_business.id
                values = {}
                values['assign_to_business'] = assign_to_business
                values['order_ids'] = [(6,False,ids)]

                requests = self.env['rb_delivery.pickup_request'].create(values) 
            ids = []        
        return True 
