from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class olivery_pickup_request_mobile_compute_fields_function(models.Model):

    _inherit = 'rb_delivery.mobile_compute_functions'


    def call_function(self,input,formValues):
        try:
            method_to_call=getattr(olivery_pickup_request_mobile_compute_fields_function,input['compute_function_name'])
            return method_to_call(self,formValues,input)
        except:
            return super(olivery_pickup_request_mobile_compute_fields_function,self).call_function(input,formValues)

    
    @api.model
    def compute_pickup_orders_based_on_selected_business(self,values,input):
        assign_to_business = values.get('assign_to_business', False)
        if input and 'domain' in input and assign_to_business:
            input['domain'] = [["assign_to_business","=",assign_to_business]]
        else:
            input['domain'] = []
        return input
    

    @api.model
    def compute_visibility_for_pick_up_request_alternative_address(self,values,input):
        show_alt_address = values.get('show_alt_address',False)
        if show_alt_address:
            input['invisible'] = False
        else:      
            input['invisible'] = True
        return input