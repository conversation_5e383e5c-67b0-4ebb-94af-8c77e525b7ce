# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class olivery_pickup_request(models.Model):

    _name = 'rb_delivery.pickup_request'
    _inherit = 'mail.thread'
    _order = "create_date DESC"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Note',compute="_compute_order_name")

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','pickup_request'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None


    @api.model
    def default_business(self):
        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        is_business = user.sudo().has_group('rb_delivery.role_business')
        if is_business:
            del_user = self.env['rb_delivery.user'].sudo().search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False

    @api.one
    @api.depends('order_ids')
    def compute_actual_no_orders(self):
        self.actual_no_orders = len(self.order_ids)

    sequence = fields.Char('Sequence',track_visibility="on_change")

    note = fields.Char('Note',track_visibility="on_change")

    barcode = fields.Binary('Barcode', compute="create_barcode",track_visibility="on_change")

    assign_to_business = fields.Many2one('rb_delivery.user', 'Business Name',default=default_business,track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_business')]")

    mobile_number = fields.Char(related='assign_to_business.mobile_number', readonly=True, store=True,track_visibility="on_change")

    whatsapp_mobile_number = fields.Char(related='assign_to_business.whatsapp_mobile', readonly=True, store=True,track_visibility="on_change")

    alt_mobile_number = fields.Char('Business Alternative Mobile Number' ,track_visibility="on_change")

    alt_business_name = fields.Char('Alternative Business Name' ,track_visibility="on_change")

    address = fields.Char(related='assign_to_business.address', readonly=True, store=True,track_visibility="on_change")

    area_id = fields.Many2one('rb_delivery.area',related='assign_to_business.area_id', readonly=True, store=True,track_visibility="on_change")

    driver_id = fields.Many2one('rb_delivery.user', 'Driver',track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_driver')]")

    sub_area_id = fields.Many2one('rb_delivery.sub_area',related='assign_to_business.sub_area', readonly=True, store=True,track_visibility="on_change")

    longitude = fields.Char(related='assign_to_business.longitude',string='Longitude',track_visibility="on_change")

    latitude = fields.Char(related='assign_to_business.latitude',string='Latitude',track_visibility="on_change")

    show_alt_address = fields.Boolean("Show Alternative Address")

    business_alt_area = fields.Many2one('rb_delivery.area',string="Business Alternative Area")

    business_alt_address = fields.Char('Business Alternative Address')

    business_alt_sub_area = fields.Many2one('rb_delivery.sub_area',string='Business Alternative Sub Area', track_visibility="on_change", ondelete='restrict')

    business_alt_longitude = fields.Char(string="Business Alternative Longitude")

    business_alt_latitude = fields.Char('Business Alternative Latitude')

    expected_no_orders = fields.Integer('Expected Number of orders',track_visibility="on_change")

    actual_no_orders = fields.Integer('Actual Number of orders', compute="compute_actual_no_orders", store=True, readonly=True, track_visibility="on_change")

    total_money_collection_cost = fields.Float("Total money collection cost",compute="change_orders", store=False,track_visibility="on_change")

    reschedule_date = fields.Datetime(string="Reschedule Date",track_visibility="on_change")

    pickup_type_id = fields.Many2one(
        'rb_delivery.pickup_request_type', string="Pickup Type", track_visibility="on_change")
    
    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'pickup_request_order_item',
        column1 = 'pickup_request_id',
        column2 = 'order_id')

    active = fields.Boolean('Active', default=True)

    reference_id = fields.Char('Reference Id',track_visibility="on_change",copy=False)

    order_barcode = fields.Text("Order Barcode")

    to_order_state = fields.Many2one('rb_delivery.status', track_visibility="on_change",string="Collection Status")

    barcode_order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'pickup_request_barcode_order_item',
        column1 = 'pickup_request_id',
        column2 = 'order_id')

    collection_ids = []

    transportation_way = fields.Char("Transportation way", track_visibility="on_change")

    @api.onchange('assign_to_business')
    def _change_business(self):
        if self.assign_to_business:
            if self.assign_to_business.area_id:
                self.business_alt_area = self.assign_to_business.area_id.id
            if self.assign_to_business.sub_area:
                self.business_alt_sub_area = self.assign_to_business.sub_area.id
            self.business_alt_address = self.assign_to_business.address
            self.business_alt_longitude = self.assign_to_business.longitude
            self.business_alt_latitude = self.assign_to_business.latitude
            self.alt_mobile_number = self.assign_to_business.mobile_number

    @api.onchange('order_barcode')
    def submit(self):
        if self.order_barcode:
            order_barcodes = self.order_barcode.splitlines()

            ids = []

            if len(order_barcodes):
                for barcode in order_barcodes:
                    if barcode:
                        print (barcode)
                        order = self.env['rb_delivery.order'].search([('sequence', '=', barcode)],limit=1)
                        if order and order.id:
                            ids.append(order.id)
                        else:
                            raise ValidationError(_("No order of sequence ")+barcode)

            self.barcode_order_ids = [(6,0,ids)]

    @api.one
    def convert_data(self):
        missing_records = ''
        records = ''
        if len(self.barcode_order_ids) < len(self.order_ids):
            for order in self.order_ids:
                if order not in self.barcode_order_ids:
                    missing_records = missing_records + order.sequence + ', '
        else:
            for order in self.barcode_order_ids:
                if order not in self.order_ids:
                    records = records + order.sequence + ', '
        if missing_records != '':
            self.barcode_order_ids = [(6,0,[])]
            self.to_order_state  = []
            self.order_barcode = ''
            raise ValidationError(_("Orders {} are missing, please scan them.").format(missing_records))

        elif records != '':
            self.barcode_order_ids = [(6,0,[])]
            self.to_order_state  = []
            self.order_barcode = ''
            raise ValidationError(_("Orders {} do not exist in selected pickup request.").format(records))
        else:
            to_status = self.env['rb_delivery.status'].search([('id','=',self.to_order_state.id)])
            if to_status:
                self.write({'state':to_status.name})

            for rec in self.barcode_order_ids:
                try:
                    if to_status and to_status.related_order_status:
                        rec.write({'state':to_status.related_order_status})
                        rec.get_action(rec,to_status.related_order_status)
                except:
                    raise ValidationError("Something wrong with " + rec.sequence)

            length = len(self.barcode_order_ids)
            message = _(' Orders were processed')
            self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',str(length)+message],str(self._uid))
            self.barcode_order_ids = [(6,0,[])]
            self.to_order_state  = []
            self.order_barcode = ''
    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            import barcode
            from barcode.writer import ImageWriter
            import io
            import base64
            barcode.base.Barcode.default_writer_options['write_text'] = False

            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})

    @api.model
    def get_status(self):
        fields=self.env['rb_delivery.status'].search([])
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','pickup_request')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list


    @api.one
    @api.depends('order_ids')
    def change_orders(self):
        total_money_collection_cost = 0
        if len(self.order_ids)>0:
            for order in self.order_ids:
                total_money_collection_cost = total_money_collection_cost + order.money_collection_cost
        else:
            total_money_collection_cost = 0

        self.total_money_collection_cost = total_money_collection_cost

    @api.one
    def _compute_order_name(self):
        if self.sequence:
            self.name = self.sequence + '_' + str(self.create_date)
    
    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')],limit=1)
                rec.state_id = state_id.id

    @api.onchange('business_alt_sub_area')
    def _compute_business_alt_sub_area(self):
        if self.business_alt_sub_area.parent_id.id :
            self.business_alt_area = self.business_alt_sub_area.parent_id.id

    @api.onchange('business_alt_area')
    def _check_business_alt_sub_area(self):
        if(self.business_alt_sub_area and self.business_alt_area.id != self.business_alt_sub_area.parent_id.id):
            self.business_alt_sub_area = False

    @api.model
    def create(self, values):
        order_vals = {}
        if 'assign_to_business' in values and values['assign_to_business']:
            business = self.env['rb_delivery.user'].sudo().search([('id','=',values['assign_to_business'])])
            if business and values.get('show_alt_address'):
                if business.area_id and ('business_alt_area' not in values or not values['business_alt_area']):
                    values['business_alt_area'] = business.area_id.id
                if business.sub_area and ('business_alt_sub_area' not in values or not values['business_alt_sub_area']):
                    values['business_alt_sub_area'] =business.sub_area.id
                if 'business_alt_address' not in values or not values['business_alt_address']:
                    values['business_alt_address'] = business.address
                if 'business_alt_longitude' not in values or not values['business_alt_longitude']:
                    values['business_alt_longitude'] = business.longitude
                if 'business_alt_latitude' not in values or not values['business_alt_latitude']:
                    values['business_alt_latitude'] = business.latitude
                if 'alt_mobile_number' not in values or not values['alt_mobile_number']:
                    values['alt_mobile_number'] = business.mobile_number
        new_order_ids = []
        if 'createNewRecord' in values and values['createNewRecord'] and 'sequences' in values and values['sequences'] and len(values['sequences']):
            for sequence in values['sequences']:
                record = self.env['rb_delivery.order'].sudo().search(['|',('sequence','=',sequence),('reference_id','=',sequence)])
                if record:
                    new_order_ids.append(record.id)
                elif not record:
                    if 'diver_id' in values and values['driver_id']:
                        result = self.env['rb_delivery.order'].create({'assign_to_business':values['assign_to_business'],'assign_to_agent':values['diver_id'],'reference_id' : sequence,'state': 'picked_up'})
                    else:
                        result = self.env['rb_delivery.order'].create({'assign_to_business':values['assign_to_business'],'reference_id' : sequence})
                    new_order_ids.append(result.id)
            values['order_ids'] = [(6,0,new_order_ids)]
            del values['createNewRecord']
            del values['sequences']
        if 'order_ids' in values and values['order_ids'] and len(values['order_ids']) > 0:
            self.check_order(values['order_ids'][0][2],values)

            if 'transportation_way' in values and values['transportation_way']:
                 order_vals['transportation_way'] = values['transportation_way']

            if 'reschedule_date' in values and values['reschedule_date']:
                 order_vals['reschedule_date'] = values['reschedule_date']

            user_role = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).role_code
            if user_role == 'rb_delivery.role_driver':
                values['state'] = 'picked_up'
                order_vals['assign_to_agent'] = values['driver_id']
                order_vals['state'] = 'picked_up'
            elif 'driver_id' in values and values['driver_id']:
                order_vals['assign_to_agent'] = values['driver_id']

        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
        # create new sequence
        new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.pickup_request')
        values['sequence'] = new_sequence
        request =  super(olivery_pickup_request, self).create(values)

        if 'state' in values and values['state']:
            self.with_delay(channel="root.notification",max_retries=2).get_action(request,values['state'])
        if 'order_ids' in values and values['order_ids'] and len(values['order_ids']) > 0:
            order_vals['pickup_request_id'] = request.id

        if order_vals:
            self.update_orders(order_vals,values['order_ids'][0][2])
        return request

    def update_orders(self,order_vals,order_ids):
        for order_id in order_ids:
            order = self.env['rb_delivery.order'].sudo().browse(order_id)
            order.write(order_vals)
            username = self.env.user.name
            order.sudo().message_post(body=_("Order has been updated by %s through pickup request, usually this is done when user update pickup request collection and in turn it will update all orders inside it.")%(username))

    def get_signature(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_signature').id
        domain = [('pickup_request_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.signature',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}
    
    def guard_function(self,values):
        order_ids = []
        order_vals = {}
        if 'transportation_way' in values and values['transportation_way']:
            order_vals['transportation_way'] = values['transportation_way']
        if 'reschedule_date' in values and values['reschedule_date']:
            order_vals['reschedule_date'] = values['reschedule_date']
        if 'driver_id' in values:
            order_vals['assign_to_agent'] = values['driver_id']
        if 'state' in values and values['state']:
            state = self.env['rb_delivery.status'].sudo().search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')])
            if state.related_order_status:
                web_color = state.web_color
                order_state = self.env['rb_delivery.status'].sudo().search([('name','=',state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                if order_state:
                    order_vals['web_color'] = web_color
                    order_vals['is_from_collection'] = True
                    order_vals['state'] = order_state.name
        for rec in self:
            if 'order_ids' in values and values['order_ids']:
                if len(values['order_ids'])>0:
                    if values['order_ids'][0] and len(values['order_ids'][0])>1 and values['order_ids'][0][0]==4:
                        rec.check_order([values['order_ids'][0][1]],values,True)
                    if values['order_ids'][0] and len(values['order_ids'][0])>2 and values['order_ids'][0][0]==6:
                        rec.check_order(values['order_ids'][0][2],values)
            if 'state' in values and values['state']:
                rec.authorize_change_status(values['state'])
                rec.with_delay(channel="root.notification",max_retries=2).get_action(rec,values['state'])
            if order_vals:
                order_ids = order_ids + rec.order_ids.ids
        if len(order_ids)>0 and order_vals:
            orders = self.env['rb_delivery.order'].browse(order_ids)
            if orders:
                orders.write(order_vals)

    @api.multi
    def write(self, values):
        if 'state' in values and values['state']:
            self.do_action(values['state'])
        if 'from_mobile' in values and values['from_mobile'] and 'sequences' in values and values['sequences']:
            can_skip_expected_number_of_order = self.env['rb_delivery.client_configuration'].get_param('disable_expected_orders_check')
            if can_skip_expected_number_of_order:
                self.check_sequences(values)
            else:
                if self.expected_no_orders != len(values['sequences']):
                    raise ValidationError(_('Number of order must be '+ str(self.expected_no_orders) + '  not  '+str(len(values['sequences']))))
                else:
                        self.check_sequences(values)
        else:
            self.guard_function(values)
        return super(olivery_pickup_request, self).write(values)

    def check_sequences(self,values):
        order_ids = []
        sequences = values['sequences']
        for sequence in sequences:
            order = self.env['rb_delivery.order'].sudo().search(['|',('sequence','=',sequence),('reference_id','=',sequence)])
            if order:
                order_ids.append(order.id)
            else:
                if self.assign_to_business:
                    vals = {'assign_to_business':self.assign_to_business.id,
                              'reference_id' : sequence,
                              'state': 'picking_up'}
                    if self.driver_id:
                        vals['assign_to_agent'] = self.driver_id.id
                    else:
                        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
                        group_id = user.group_id
                        if  group_id.code == 'rb_delivery.role_driver':
                            values['assign_to_agent'] = user.id
                        else :
                            raise ValidationError(_('Just users with role driver can edit pickup state'))
                    try:
                        order = self.env['rb_delivery.order'].sudo().create(vals)
                        order.sudo().message_post(body=_("This order was created through mobile by the user, %s, who attempted to scan orders for modifying the pickup request state.")%(self.env.user.name))
                        order_ids.append(order.id)
                    except Exception as e:
                        raise ValidationError(_('Enable skeleton configuration becuase : ' + str(e)))
        try:
            self.check_order(order_ids,values)
            self.update_orders(values,order_ids)
        except Exception as e:
            raise ValidationError (_(e))

    def check_order(self,order_ids,values,from_order=False):
        order_collection_ids = []
        default_state = ''
        order_collection_ids = self.order_ids.ids
        added_order_ids = []
        removed_order_ids = []
        for order_id in order_ids:
            if order_id not in order_collection_ids:
                order = self.env['rb_delivery.order'].sudo().search([('id','=',order_id)])
                collection = self.env['rb_delivery.pickup_request'].sudo().search([('order_ids','=',order.id)],limit=1)
                if collection and collection.id:
                    raise ValidationError(_('Order already exists in Pickup Request '+collection.sequence))
                default_collection_status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','collection'),('status_type','=','pickup_request')],limit=1)
                collection_status = self.env['rb_delivery.client_configuration'].get_param('default_pickup_request_status')
                collection_states = []
                message = ''
                if not collection_status:
                    return
                for state_id in collection_status:
                    state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                    collection_states.append(state.name)
                    message = message +' ' +state.title +'/'
                if order.state and order.state not in collection_states:
                    raise ValidationError(_('Order state should be in ')+message)
                if self.assign_to_business and order.assign_to_business.id != self.assign_to_business.id:
                    raise ValidationError(_('Order business should be ')+self.sudo().assign_to_business.username)
                if not from_order and not order.pickup_request_id:
                    added_order_ids.append(order.id)

        if not from_order and 'order_ids' in values and values['order_ids'] and len(values['order_ids']) > 0 and values['order_ids'][0]:
            if len(values['order_ids'][0][2])==0 and self.order_ids:
                self.order_ids.write({'pickup_request_id':False})
            else:
                for order_id in order_collection_ids:
                    if order_id not in values['order_ids'][0][2]:
                        removed_order_ids.append(order_id)
                if len(removed_order_ids)>0:
                    orders = self.env['rb_delivery.order'].browse(removed_order_ids)
                    orders.write({'pickup_request_id':False})

        if len(added_order_ids)>0:
            orders = self.env['rb_delivery.order'].browse(added_order_ids)
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            group_id = user.group_id
            if group_id.code == 'rb_delivery.role_driver':
                order_status_ids = self.env['rb_delivery.client_configuration'].get_param('ability_to_assign_agent_to_orders')
                order_status_names = self.env['rb_delivery.status'].browse(order_status_ids).mapped('name')
                for order in orders:
                    if order.sudo().state not in order_status_names and not order.sudo().assign_to_agent:
                        raise ValidationError(_('This order of sequence %s does not have agent and order state not in ability_to_assign_agent_to_orders configuration')%(order.sudo().sequence))
                    else:
                        order.sudo().message_post(body=_("Driver changed to %s While trying scanning pickup request through mobile because this order does not have agent and order state was in ability_to_assign_agent_to_orders")%(user.username))
                orders.sudo().write({'pickup_request_id':self.id,'assign_to_agent':user.id})
            else:
                orders.write({'pickup_request_id':self.id})

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        recs = self.search([('sequence', operator, name)] + args, limit=limit)

        if not recs.ids:
            return super(olivery_pickup_request, self).name_search(name=name, args=args,operator=operator,limit=limit)
        return recs.name_get()

    def get_action(self,pickup_request, state):
        status = self.env['rb_delivery.status'].search([('name','=',state),('collection_type','=','pickup_request'),('status_type','=','olivery_collection')])
        actions = self.env['rb_delivery.action'].search(
            [('collection_state', '=', status.id),('action_type','=','for_collection'),('collection_type','=','pickup_request')])
        for action in actions:
            group = action.group_id
            message = ''
            header= ''
            if action.action_template:
                message = action.action_template.body_html
                header = action.action_template.subject
            elif action.order_user_template:
                message = action.order_user_template.body_html
                header = action.order_user_template.subject
            elif action.collection_template:
                message = action.collection_template.body_html
                header = action.collection_template.subject
            elif action.message:
                message = action.message
                header = action.header
            is_sms = action.is_sms
            is_email = action.is_email
            is_notification = action.is_notification

            self.notify(pickup_request, group, header, message,is_sms,is_email,is_notification)

    def notify(self,pickup_request, group, header, message, is_sms, is_email, is_notification=False):
        players = []
        users = []
        mobile_numbers = []
        emails = []

        # since here we need to view all users even userse we have no access to
        # e.g when create order from business it should send to maanger role ( which the business
        # role ) can not seee
        # ading sudo so the driver/business can send notification to user even if he
        # can not access the user information

        business_user = pickup_request.sudo().assign_to_business
        distributor_user = pickup_request.sudo().driver_id


        if group and group.id:
            group = self.env['res.groups'].search([('id', '=', group.id)])
            if group.get_xml_id()[group.id] == 'rb_delivery.role_business':
                mobile_numbers.append(business_user.mobile_number)
                if is_sms:
                    users.append(business_user)
                if business_user.id and business_user.player_id:
                    users.append(business_user)
                    players.append(str(business_user.player_id))
                if business_user.email and is_email:
                    users.append(business_user)
                    emails.append(business_user.email)


            if group.get_xml_id()[group.id] == 'rb_delivery.role_driver':
                mobile_numbers.append(distributor_user.mobile_number)
                if is_sms:
                    users.append(distributor_user)
                if distributor_user.id and distributor_user.player_id:
                    users.append(distributor_user)
                    players.append(str(distributor_user.player_id))
                if distributor_user.email and is_email:
                    users.append(distributor_user)
                    emails.append(distributor_user.email)


            if group.get_xml_id()[group.id] == 'rb_delivery.role_manager':

                managers = self.env['rb_delivery.user'].sudo().search(
                    [('group_id', '=', group.id)])

                for manager in managers:
                    if is_sms:
                        users.append(manager)
                    mobile_numbers.append(manager.mobile_number)
                    if manager.player_id:
                        users.append(manager)
                        players.append(str(manager.player_id))
                    if manager.email and is_email:
                        users.append(manager)
                        emails.append(manager.email)


            if group.get_xml_id()[group.id] == 'rb_delivery.role_super_manager':

                sp_managers = self.env['rb_delivery.user'].sudo().search(
                    [('group_id', '=', group.id)])

                for sp_manager in sp_managers:
                    if is_sms:
                        users.append(sp_manager)
                    mobile_numbers.append(sp_manager.mobile_number)
                    if sp_manager.player_id:
                        users.append(sp_manager)
                        players.append(str(sp_manager.player_id))
                    if sp_manager.email and is_email:
                        users.append(sp_manager)
                        emails.append(sp_manager.email)

        try:
            self.env['rb_delivery.notification_center'].notification(users, emails, players, mobile_numbers, header, message, is_sms, is_email, is_notification,pickup_request.id,pickup_request.sequence,'rb_delivery.pickup_request')

        except:
            pass

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                raise Warning(_("You are not allowed to change from this status {} to this status {}").format(current_status_record.title,next_status_record.title))

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            raise Warning(_("You are not allowed to change to this state"))

        return


    def do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(olivery_pickup_request,action.name)
                method_to_call(self,next_state)
            except:
                pass

    def get_location(self):
        if self.latitude and self.longitude:
            return {
                'name'     : 'Go to website',
                'res_model': 'ir.actions.act_url',
                'type'     : 'ir.actions.act_url',
                'target'   : '_blank',
                'url'      : "https://www.google.com/maps/search/"+self.latitude+','+self.longitude
            }
        else:
            raise ValidationError('There is no location')
    # Post actions methods
    @api.multi
    def detach_agent_action(self,next_state):
        username = self.env.user.name
        self.sudo().write({"driver_id":""})
        self.sudo().message_post(body=_("Pickup request record updated by %s through automated function called “detach_agent” in configuration, you can check either configuration or contact support.")%(username))

    def get_barcode(self):

        return {
        #'name': self.order_id,
        'res_model': 'rb_delivery.pickup_request_barcode',
        'type': 'ir.actions.act_window',
        'context': {},
        'view_mode': 'form',
        'view_type': 'form',
        'context':{'pickup_request_id':self.id},
        'view_id': self.env.ref("olivery_pickup_request.view_form_rb_delivery_pickup_request_barcode").id,
        'target': 'inline'}

    @api.model
    def order_create_pickup_request_from_mobile(self,values):
        order_ids = []
        not_found_ids = []

        if 'order_ids' in values and values['order_ids']:

            for order in values['order_ids']:
                record = self.env['rb_delivery.order'].sudo().search(['|',('sequence','=',order),('reference_id','=',order)])
                if record:
                    order_ids.append(record.id)
                elif not record:
                    if 'diver_id' in values and values['driver_id']:
                        result = self.env['rb_delivery.order'].create({'assign_to_business':values['assign_to_business'],'assign_to_agent':values['diver_id'],'reference_id' : order,'state': 'picked_up'})
                    else:
                        result = self.env['rb_delivery.order'].create({'assign_to_business':values['assign_to_business'],'reference_id' : order})
                    order_ids.append(result.id)

            if len(not_found_ids) == 0 and len(order_ids) != 0:
                values['order_ids'] = [(6,0,order_ids)]
                pickup_request = self.env['rb_delivery.pickup_request'].create(values)
                return {'result':True,'message':"Success",'pickup_request_id':pickup_request.id}
            else:
                return {'result':False,'message':"order not found",'orders':not_found_ids}
        else:
            pickup_request = self.env['rb_delivery.pickup_request'].create(values)
            return {'result':True,'message':"Success",'pickup_request_id':pickup_request.id}


    @api.model
    def get_public_business(self,user_name):
            fields = ['id', 'user_id', 'state', 'username', 'mobile_number', 'area_id','sub_area_id', 'email', 'address', 'business_type_id', 'group_id', 'role_name','role_code','inclusive_delivery','commercial_name','has_customers','player_id','active']
            if user_name and user_name != '':
                users = self.env['rb_delivery.user'].sudo().search_read([('username','ilike',user_name),('role_code','=','rb_delivery.role_business')],fields)
            else:
                users = self.env['rb_delivery.user'].sudo().search_read([('role_code','=','rb_delivery.role_business')],fields)
            if users:
                return users
            else:
                return []

    @api.model
    def group_by_get_business(self,domain):
        users = []
        pickup_requests = self.env['rb_delivery.pickup_request'].search(domain)
        for collection in pickup_requests:
                if {"id":collection.assign_to_business.id ,"name":collection.assign_to_business.commercial_name} not in users :
                        users.append({"id":collection.assign_to_business.id ,"name":collection.assign_to_business.commercial_name})
        return users

    @api.model
    def group_by_get_driver(self,domain):
        users = []
        pickup_requests = self.env['rb_delivery.pickup_request'].search(domain)
        for collection in pickup_requests:
                if {"id":collection.driver_id.id ,"name":collection.driver_id.username} not in users :
                        users.append({"id":collection.driver_id.id ,"name":collection.driver_id.username})
        return users

    @api.model
    def pickup_request_count(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.pickup_request'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.pickup_request'].search_count([])
            return count

    @api.multi
    def name_get(self):
        result = []
        name = ''
        for pickup in self:
            if pickup.sudo().assign_to_business and pickup.sequence:
                business = pickup.sudo().assign_to_business
                if business.commercial_name:
                    name = business.commercial_name+', '+ pickup.sequence
                else:
                    name = business.username+', '+ pickup.sequence
            else:
                name = pickup.sudo().create_uid.name

            result.append((pickup.id, name))

        return result

class order_select_pickup_request_state_wizard(models.TransientModel):
    _name = 'rb_delivery.select_pickup_request_state'
    _description = 'Select pickup request state'

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    state = fields.Selection(selection='get_status',track_visibility="on_change",string="Status",default="")
    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change", domain=_get_driver_users)


    reschedule_date = fields.Datetime(string="Reschedule Date",track_visibility="on_change")
    show_reschedule_date = fields.Boolean('Show Reschedule Date', default=False)
    required_reschedule_date = fields.Boolean('Show Reschedule Date required', default=False)


    required_agent = fields.Boolean('Show Agent Required', default=False)
    show_agent = fields.Boolean('Show Agent', default=False)

    def get_status(self):
        group_id = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','pickup_request')])
        status_list =[]
        for status in next_statuses:
            if group_id:
                if status.role_action_status_ids and len(status.role_action_status_ids)>0:
                    for role in status.role_action_status_ids:
                        if role.id == group_id.id:
                            status_list.append((status.name,status.title))
            else:
                status_list.append((status.name,status.title))

        return status_list


    @api.onchange('state')
    def change_state(self):
        state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),('status_type','=','olivery_collection'),('collection_type','=','pickup_request')])
        optional_status_actions = state.status_action_optional_related_fields
        required_status_actions = state.status_action_required_aditional_fields
        self.show_agent = False
        self.required_agent = False
        self.show_reschedule_date = False
        self.required_reschedule_date = False
        if state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
                if status_action.name == 'pickup_request_show_reschedule_date':
                    self.show_reschedule_date = True
        if state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
                    self.required_agent = True
                if status_action.name == 'pickup_request_show_reschedule_date':
                    self.show_reschedule_date = True
                    self.required_reschedule_date = True

    @api.multi
    def select_state(self):

        recs = self.env['rb_delivery.pickup_request'].browse(
            self._context.get('active_ids'))
        vals = {}
        if self.state:
            vals['state'] = self.state

        if self.agent:
            vals['driver_id'] = self.agent.id
        if self.reschedule_date:
            vals['reschedule_date']=self.reschedule_date
        recs.write(vals)
        return True

class pickup_request_multi_assign_to_agent_wizard(models.TransientModel):
    _name = 'rb_delivery.pickup_request_multi_assign_to_agent'
    _description = 'Pickup request multi assign to agent'

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def default_driver(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_agent = fields.Many2one(
        'rb_delivery.user', 'Agent', domain=_get_driver_users, default=default_driver)

    @api.multi
    def select_agent(self):
        recs = self.env['rb_delivery.pickup_request'].browse(
            self._context.get('active_ids'))

        for rec in recs:
            rec.write({'driver_id': self.assign_to_agent.id})
            for order in rec.order_ids:
                order.write({'assign_to_agent':self.assign_to_agent.id})
        return True
    
