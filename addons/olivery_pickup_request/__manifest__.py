# -*- coding: utf-8 -*-
{
    'name': "olivery_pickup_request",
    'summary': """
        <PERSON><PERSON> Pickup Request App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-rc-next-2.0.10',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/pickup_request_security.xml',
        'security/ir.model.access.csv',
        'views/module_view.xml',
        'demo/status.xml',
        'demo/sequence.xml',
        'demo/client_config.xml',
        'models/pickup_request/pickup_request_view.xml',
        'models/pickup_request/pickup_request_report.xml',
        'models/pickup_request_barcode/pickup_request_barcode_view.xml',
        'models/order/order_view.xml',
        'models/action/action_view.xml',
        'models/pickup_request_type/pickup_request_type.xml',
        'demo/mobile_compute_fields_pickup.xml',
        'demo/mobile_button_function.xml',
    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],
    'post_init_hook': 'post_init_hook_pickup',
}
