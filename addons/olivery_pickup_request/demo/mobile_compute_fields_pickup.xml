<odoo >
    <data>
      <record id="compute_pickup_orders_based_on_selected_business" model="rb_delivery.mobile_compute_functions">
            <field name="name">compute_pickup_orders_based_on_selected_business</field>
            <field name="model" ref="olivery_pickup_request.model_rb_delivery_pickup_request"/>
            <field name="fields" eval="[(6,0,[ref('olivery_pickup_request.field_rb_delivery_pickup_request__assign_to_business')])]" />
      </record>

      <record id="compute_visibility_for_pick_up_request_alternative_address" model="rb_delivery.mobile_compute_functions">
            <field name="name">compute_visibility_for_pick_up_request_alternative_address</field>
            <field name="model" ref="olivery_pickup_request.model_rb_delivery_pickup_request"/>
            <field name="fields" eval="[(6,0,[ref('olivery_pickup_request.field_rb_delivery_pickup_request__show_alt_address')])]" />
      </record>
   </data>
</odoo>