# -*- coding: utf-8 -*-

import json
import logging
import re

from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

_logger = logging.getLogger(__name__)
from datetime import datetime


class olivery_slots(models.Model):
    _name = 'rb_delivery.slots'

    name = fields.Char('Name',track_visibility="on_change",readonly=True)

    from_time = fields.Char('From time',required=True,track_visibility="on_change")

    to_time = fields.Char(string='To Time', required=True,track_visibility="on_change")

    slot_type = fields.Selection(selection=[('pick_up','Pick Up'),('delivery','Delivery')],string="Type",default='pick_up')

    @api.constrains('from_time','to_time')
    def _check_time_format(self):
        time_pattern = re.compile(r'^\d{2}:\d{2}(:\d{2})?$')
        for record in self:
            if not time_pattern.match(record.from_time) or not time_pattern.match(record.to_time):
                raise ValidationError(_("Time must be in the format HH:MM or HH:MM:SS"))
            from_time_obj = datetime.strptime(record.from_time, '%H:%M')
            to_time_obj = datetime.strptime(record.to_time, '%H:%M')

            from_time_formatted = from_time_obj.strftime('%I:%M %p')
            to_time_formatted = to_time_obj.strftime('%I:%M %p')
            record.name = f"{from_time_formatted} - {to_time_formatted}"

    @api.model
    def get_available_slot_names(self):
        all_names = self.env['rb_delivery.slots'].sudo().search([]).mapped('name')
        unique_names = set(name.strip() for name in all_names if name)
        return sorted(unique_names)
