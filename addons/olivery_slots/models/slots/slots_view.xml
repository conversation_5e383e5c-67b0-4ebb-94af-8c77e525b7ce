
<odoo>
  <data>
    <record id="view_form_rb_delivery_slota" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_slota</field>
      <field name="model">rb_delivery.slots</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>

            <group name="group_top">
              <group name="group_left">
              <field name="name"/>
               <field name="from_time" placeholder="Enter time in HH:MM or HH:MM:SS AM/PM"/>
               <field name="to_time" placeholder="Enter time in HH:MM or HH:MM:SS AM/PM"/>
              </group>
              <group name="group_right">
               <field name="slot_type"/>
              </group>
            </group>
          </sheet>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_slots" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_slots</field>
      <field name="model">rb_delivery.slots</field>

      <field name="arch" type="xml">
        <tree editable = "top">
            <field name="name"/>
            <field name="from_time" placeholder="Enter time in HH:MM or HH:MM:SS AM/PM"/>
            <field name="to_time" placeholder="Enter time in HH:MM or HH:MM:SS AM/PM"/>
            <field name="slot_type"/>
        </tree>
      </field>

    </record>

    <record id="view_search_rb_delivery_slots" model="ir.ui.view">
            <field name="name">view_search_rb_delivery_slots</field>
            <field name="model">rb_delivery.slots</field>

            <field name="arch" type="xml">

                <search>
                    <group name="search_group">
                        <field name="name"/>
                        <field name="from_time"/>
                        <field name="to_time"/>
                        <field name="slot_type"/>
                    </group>
                    <group string="Groups" name="order_group_by_group">
                        <filter name="group_by_slot_type" string="By Type" icon="terp-partner" context="{'group_by':'slot_type'}"/>

                    </group>

                </search>

            </field>

        </record>


  </data>
</odoo>
