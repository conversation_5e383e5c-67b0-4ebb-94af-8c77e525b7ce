# -*- coding: utf-8 -*-

import json
import logging
import re

from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime

_logger = logging.getLogger(__name__)


class olivery_slots_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    pick_up_slot = fields.Many2one("rb_delivery.slots","Pickup slot",track_visibility="on_change")

    delivery_slot = fields.Many2one("rb_delivery.slots","Delivery slot",track_visibility="on_change")

    slot_pickup_date = fields.Date(string="Slot Pickup Date")

    slot_drop_date = fields.Date(string="Slot Drop Date")
    
    # ----------------------------------------------------------
    # Function
    # ---------------------------------------------------------

    def get_closest_time_slot(self):
        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute
        closest_slot = None
        closest_diff = float('inf')
        time_slots_rec = self.env['rb_delivery.slots'].sudo().search([('slot_type','=','pick_up')])
        for slot in time_slots_rec:
            start = slot.from_time
            end = slot.to_time
            start_hour, start_minute = map(int, start.split(":"))
            end_hour, end_minute = map(int, end.split(":"))

            if (current_hour > start_hour or (current_hour == start_hour and current_minute >= start_minute)) and \
            (current_hour < end_hour or (current_hour == end_hour and current_minute < end_minute)):
                continue

            diff = abs(current_hour - start_hour)

            if diff < closest_diff:
                closest_diff = diff
                closest_slot = slot
        return closest_slot


    def check_slot(self, slot_name, slot_type):
        if not slot_name:
            return False

        slot = self.env['rb_delivery.slots'].search([
            ('name', '=', slot_name),
            ('slot_type', '=', slot_type)
        ], limit=1)

        if slot:
            return slot.id

        if ' - ' in slot_name:
            slot_times = slot_name.split(' - ')
            if len(slot_times) == 2:
                from_time_obj = datetime.strptime(slot_times[0].strip(), '%I:%M %p')
                to_time_obj = datetime.strptime(slot_times[1].strip(), '%I:%M %p')

                from_time_24 = from_time_obj.strftime('%H:%M')
                to_time_24 = to_time_obj.strftime('%H:%M')

                slot = self.env['rb_delivery.slots'].sudo().search([
                    ('from_time', '=', from_time_24),
                    ('to_time', '=', to_time_24),
                    ('slot_type', '=', slot_type)
                ], limit=1)

                if not slot:
                    slot = self.env['rb_delivery.slots'].sudo().create({
                        'from_time': from_time_24,
                        'to_time': to_time_24,
                        'slot_type': slot_type,
                    })
                return slot.id
        return False




    @api.model
    def create(self, values):
        slot_pickup_name = values.get('slot_pickup')
        if slot_pickup_name:
            values['pick_up_slot'] = self.check_slot(slot_pickup_name, 'pick_up')

        slot_drop_name = values.get('slot_drop')
        if slot_drop_name:
            values['delivery_slot'] = self.check_slot(slot_drop_name, 'delivery')

        return super(olivery_slots_order, self).create(values)

    def guard_function(self, values):
        slot_pickup_name = values.get('slot_pickup')
        if slot_pickup_name:
            values['pick_up_slot'] = self.check_slot(slot_pickup_name, 'pick_up')

        slot_drop_name = values.get('slot_drop')
        if slot_drop_name:
            values['delivery_slot'] = self.check_slot(slot_drop_name, 'delivery')

        return super(olivery_slots_order, self).guard_function(values)
    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
