<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <data>
    <!--inherit form view-->
    <record id="view_form_olivery_slots_order_select_state" model="ir.ui.view">
        <field name="name">view_form_olivery_slots_order_select_state</field>
        <field name="model">rb_delivery.select_state</field>
        <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order_select_state" />
        <field name="arch" type="xml">
        <xpath expr="//field[@name='state']" position="after">
            <field name="show_pick_up_slot" invisible="1"/>
            <field name="show_delivery_slot" invisible="1"/>
            <field name="required_pick_up_slot" invisible="1"/>
            <field name="required_delivery_slot" invisible="1"/>
            <field name="pick_up_slot" attrs="{'invisible': [('show_pick_up_slot','=',False)],'required':[('required_pick_up_slot','=',True)]}"/>
            <field name="delivery_slot" attrs="{'invisible': [('show_delivery_slot','=',False)],'required':[('required_delivery_slot','=',True)]}"/>
        </xpath>
        </field>
    </record>
   </data>
</odoo>