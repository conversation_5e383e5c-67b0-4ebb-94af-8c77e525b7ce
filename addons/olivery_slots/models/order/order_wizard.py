# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.osv import osv



class olivery_slots_order_select_state_wizard_custom(models.TransientModel):
    _inherit = 'rb_delivery.select_state'

    pick_up_slot = fields.Many2one("rb_delivery.slots","Pickup slot",track_visibility="on_change")

    delivery_slot = fields.Many2one("rb_delivery.slots","Delivery slot",track_visibility="on_change")

    show_pick_up_slot = fields.Bo<PERSON>an('Show Pickup Slot', default=False)

    show_delivery_slot = fields.Boolean('Show Delivery Slot', default=False)

    required_delivery_slot = fields.Boolean('Show Delivery Slot Required', default=False)

    required_pick_up_slot = fields.Bo<PERSON>an('Show Pickup Slot Required', default=False)

    @api.onchange('state')
    def change_state(self):
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
        optional_status_actions = order_state.status_action_optional_related_fields
        required_status_actions = order_state.status_action_required_aditional_fields
        self.show_pick_up_slot = False
        self.show_delivery_slot = False
        self.required_delivery_slot = False
        self.required_pick_up_slot = False
        if order_state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'show_pick_up_slot':
                    self.show_pick_up_slot = True
                if status_action.name == 'show_delivery_slot':
                    self.show_delivery_slot = True
        if order_state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'show_pick_up_slot':
                    self.show_pick_up_slot = True
                    self.required_pick_up_slot = True
                if status_action.name == 'show_delivery_slot':
                    self.show_delivery_slot = True
                    self.required_delivery_slot = True
        return super(olivery_slots_order_select_state_wizard_custom, self).change_state()

    @api.multi
    def btn_show_dialog_box(self):
        self.env.context = dict(self.env.context)
        if self.pick_up_slot:
            self.env.context.update({'pick_up_slot': self.pick_up_slot.id})
        if self.delivery_slot:
            self.env.context.update({'delivery_slot': self.delivery_slot.id})
        self.env.context.update({'state': self.state})
        return super(olivery_slots_order_select_state_wizard_custom, self).btn_show_dialog_box()


class olivery_slots_display_dialog_box_custom(osv.osv):
    _inherit = "display.dialog.box"

    pick_up_slot = fields.Many2one("rb_delivery.slots","Pickup slot",track_visibility="on_change")

    delivery_slot = fields.Many2one("rb_delivery.slots","Delivery slot",track_visibility="on_change")

    @api.model
    def create(self, values):
        if 'pick_up_slot' in self._context:
            values['pick_up_slot'] = self._context.get('pick_up_slot')
        if 'delivery_slot' in self._context:
            values['delivery_slot'] = self._context.get('delivery_slot')

        state = self.env['rb_delivery.status'].sudo().search([('name','=',self._context.get('state')),'|',('status_type','=',False),('status_type','=','olivery_order')])
        if state and state.clone: values['clone_text'] = _("Warning: Changing to this status will create a clone order")

        return super(olivery_slots_display_dialog_box_custom, self).create(values)

    @api.multi
    def select_state(self):
        res = super(olivery_slots_display_dialog_box_custom, self).select_state()
        recs = self._context.get('orders')
        orders = self.env['rb_delivery.order'].browse(recs)
        values = {}
        if self.pick_up_slot:
            values['pick_up_slot'] = self.pick_up_slot.id
        if self.delivery_slot:
            values['delivery_slot'] = self.delivery_slot.id
        if values:
            orders.write(values)
        return res