<?xml version="1.0" encoding="UTF-8"?>

<odoo>
  <data>
      <!--inherit form view-->
      <record id="view_olivery_slots_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_olivery_slots_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//field[@name='service']" position="after">
                <field name="pick_up_slot" domain="[('slot_type','=','pick_up')]"/>
                <field name="delivery_slot" domain="[('slot_type','=','delivery')]"/>
                <field name="slot_drop_date" />
                <field name="slot_pickup_date" />
            </xpath>
         </field>
      </record>

      <!--inherit search view-->
      <record id="view_search_olivery_slots_rb_delivery_order" model="ir.ui.view">
         <field name="name">view_search_olivery_slots_rb_delivery_order</field>
         <field name="model">rb_delivery.order</field>
         <field name="inherit_id" ref="rb_delivery.view_search_rb_delivery_order" />
         <field name="arch" type="xml">
            <xpath expr="//filter[@name='group_by_status_last_updated_on']" position="after">
                <filter name="group_by_pick_up_slot" string="By Pickup Slot" icon="terp-partner" context="{'group_by':'pick_up_slot'}"/>
                <filter name="group_by_delivery_slot" string="By Delivery Slot" icon="terp-partner" context="{'group_by':'delivery_slot'}"/>

            </xpath>
         </field>
      </record>

   </data>
</odoo>
