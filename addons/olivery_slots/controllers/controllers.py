# -*- coding: utf-8 -*-
from odoo.http import request
from openerp.http import request
from openerp import http,_
from odoo.addons.rb_delivery.controllers.controllers import rb_delivery as rb_delivery_controller


class slots_rb_delivery_custom(rb_delivery_controller):
    @http.route('/create_order', auth='none', type="json")
    def create_order(self, **rec):
        if rec.get('service'):
            service_ids = []
            if isinstance(rec.get('service'), list):
                for service_id in rec.get('service'):
                    service = request.env['rb_delivery.service'].sudo().search([('name','=',service_id)])
                    if service:
                        service_ids.append(service.id)
                    else:
                        return {"code":403,"message":_("Service %s does not exist in the system")%(str(service_id))}
            else:
                service = request.env['rb_delivery.service'].sudo().search([('name','=',str(rec.get('service')))])
                if service:
                    service_ids.append(service.id)
                else:
                    return {"code":403,"message":_("Service %s does not exist in the system")%(str(rec.get('service')))}
            if len(service_ids)>0:
                rec['service'] = [(6,0,service_ids)]

        res = super(slots_rb_delivery_custom, self).create_order(**rec)

        return res