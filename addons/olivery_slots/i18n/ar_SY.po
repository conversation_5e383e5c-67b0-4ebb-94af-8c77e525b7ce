# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_slots
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-05 21:01+0000\n"
"PO-Revision-Date: 2024-06-05 21:01+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_search_olivery_slots_rb_delivery_order
msgid "By Delivery Slot"
msgstr "وقت التوصيل"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_search_olivery_slots_rb_delivery_order
msgid "By Pickup Slot"
msgstr "وقت الاستلام"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_search_rb_delivery_slots
msgid "By Service"
msgstr "الخدمة"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_search_rb_delivery_slots
msgid "By Type"
msgstr "النوع"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_slots
#: selection:rb_delivery.slots,slot_type:0
msgid "Delivery"
msgstr "توصيل"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_display_dialog_box__delivery_slot
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_order__delivery_slot
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_select_state__delivery_slot
msgid "Delivery slot"
msgstr "وقت التوصيل"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_form_rb_delivery_slota
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_tree_rb_delivery_slots
msgid "Enter time in HH:MM or HH:MM:SS AM/PM"
msgstr "اضف الوقت مثل HH:MM او HH:MM:SS AM/PM"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__from_time
msgid "From time"
msgstr "من وقت"

#. module: olivery_slots
#: model_terms:ir.ui.view,arch_db:olivery_slots.view_search_rb_delivery_slots
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__name
msgid "Name"
msgstr "الاسم"

#. module: olivery_slots
#: model:ir.model,name:olivery_slots.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_slots
#: selection:rb_delivery.slots,slot_type:0
msgid "Pick Up"
msgstr "استلام"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_display_dialog_box__pick_up_slot
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_order__pick_up_slot
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_select_state__pick_up_slot
msgid "Pickup slot"
msgstr "وقت الاستلام"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__service_id
msgid "Service"
msgstr "الخدمة"

#. module: olivery_slots
#: code:addons/olivery_slots/controllers/controllers.py:19
#: code:addons/olivery_slots/controllers/controllers.py:25
#, python-format
msgid "Service %s does not exist in the system"
msgstr "الخدمة %s غير موجودة في النظام"

#. module: olivery_slots
#: model:ir.actions.act_window,name:olivery_slots.action_rb_delivery_slots
#: model:ir.ui.menu,name:olivery_slots.menu_rb_delivery_slots
msgid "Slots"
msgstr "الاوقات"

#. module: olivery_slots
#: code:addons/olivery_slots/models/order/order_model.py:64
#, python-format
msgid "The slot %s you added does not exist in the system or it exists with a different service."
msgstr "الوقت الذي ادخلته %s غير موجود في النظام او متوفر لخدمة اخرى."

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__to_time
msgid "To Time"
msgstr "الى وقت"

#. module: olivery_slots
#: code:addons/olivery_slots/models/slots/slots_model.py:31
#, python-format
msgid "Time must be in the format HH:MM or HH:MM:SS"
msgstr "الوقت يجب ان يكون في شكل HH:MM او HH:MM:SS"

#. module: olivery_slots
#: code:addons/olivery_slots/models/order/order_model.py:68
#, python-format
msgid "To add slot you need to add service as well."
msgstr "لتتممكن من اضافة وقت للطلبية يجب ان يكون هناك خدمة."

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_slots__slot_type
msgid "Type"
msgstr "النوع"

#. module: olivery_slots
#: model:ir.model,name:olivery_slots.model_rb_delivery_slots
msgid "rb_delivery.slots"
msgstr ""


#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_order__slot_drop_date
msgid "Slot Drop Date"
msgstr "وقت التسليم"

#. module: olivery_slots
#: model:ir.model.fields,field_description:olivery_slots.field_rb_delivery_order__slot_pickup_date
msgid "Slot Pickup Date"
msgstr "وقت الاستلام"