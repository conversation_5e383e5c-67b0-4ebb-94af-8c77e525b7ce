# -*- coding: utf-8 -*-
{
    'name': "olivery_slots",
    'summary': """
        <PERSON><PERSON>pp from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-s-1.0.5',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'models/slots/slots_view.xml',
        'models/order/order_view.xml',
        'views/module_view.xml',
        'demo/status_action.xml',
        'models/order/order_wizard_view.xml',
        'demo/client_conf.xml'
    ],
    'qweb': [
         'static/src/xml/*.xml',
    ],
}