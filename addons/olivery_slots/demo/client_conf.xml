<odoo >
    <data noupdate="1">
        <!--  client configurations -->
        <record id="client_configuration_type_olivery_slots" model="rb_delivery.client_configuration_type">
            <field name="name">Olivery Slots</field>
            <field name="description">Any thing related to Olivery Slots"</field>
        </record> 

        <record id="client_configuration_filter_by_slot_dates" model="rb_delivery.client_configuration">
            <field name="key">filter_orders_by_slot_dates</field>
            <field name="value">True</field>
            <field name="description">Filter orders by slot pickup and drop dates instead of create date (mobile only)</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="olivery_slots.client_configuration_type_olivery_slots"/>
        </record>

    </data>
</odoo>
