# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_recepient
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-15 06:29+0000\n"
"PO-Revision-Date: 2022-06-15 06:29+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__address
msgid "Address"
msgstr "العنوان"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Addresses/Sub Users"
msgstr "العناوين/للمستخدمين الفرعين"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__area_id
msgid "Area"
msgstr "المنطقة"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__commercial_id
msgid "Business"
msgstr ""

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Area"
msgstr "بواسطة المنطقة"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Commercial"
msgstr ""

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Create Date"
msgstr "تاريخ الانشاء"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Creator"
msgstr "المنشئ"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Main Address"
msgstr ""

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Sub Area"
msgstr "المنطقة الفرعية"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_order__client
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_recepient_order
msgid "Client"
msgstr "العميل"

#. module: olivery_recepient
#: model:ir.actions.act_window,name:olivery_recepient.action_rb_delivery_client
#: model:ir.ui.menu,name:olivery_recepient.menu_rb_delivery_client
msgid "Clients"
msgstr "العملاء"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Customer Sub Area"
msgstr "المنطقةالفرعية"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "First Customer Mobile"
msgstr " رقم التلفون الأول للزبون"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "Groups"
msgstr "المجموعات"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Location"
msgstr "الموقع"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Main Address/Main User"
msgstr "العنوان الرئيسي/للمستخدم الرئيسي"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__user_parent_id
msgid "Main client"
msgstr "العميل الرئيسي"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__mobile_number
msgid "Mobile Number"
msgstr "رقم الموبايل"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Second Customer Mobile"
msgstr "رقم التلفون الثاني للزبون"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__second_mobile_number
msgid "Second Mobile Number"
msgstr "رقم الموبايل الثاني"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__second_whatsapp_mobile
msgid "Second Whatsapp Mobile Number"
msgstr "رقم الواتس اب الثاني"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "Settings"
msgstr "الإعدادات"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__show_sub_area
msgid "Show  Sub  Area"
msgstr "اظهار المنطقة الفرعية "

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__sub_area
msgid "Sub  Area"
msgstr "المنطقة الفرعية"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__child_ids
msgid "Sub Users"
msgstr "مستخدم فرعي"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__user_image
msgid "User Image"
msgstr "صورة المستخدم"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "User Info :"
msgstr "معلومات المستخدم"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "User Name"
msgstr "اسم المستخدم"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__username
msgid "Username"
msgstr "اسم المستخدم"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: olivery_recepient
#: model:ir.model.fields,help:olivery_recepient.field_rb_delivery_client__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__whatsapp_mobile
msgid "Whatsapp Mobile Number"
msgstr "رقم الواتس اب"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__latitude
msgid "latitude"
msgstr "خط العرض"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__longitude
msgid "longitude"
msgstr "خط الطول "

#. module: olivery_recepient
#: model:ir.model,name:olivery_recepient.model_rb_delivery_client
msgid "rb_delivery.client"
msgstr ""

#. module: olivery_recepient
#: model:ir.model,name:olivery_recepient.model_rb_delivery_order
msgid "rb_delivery.order"
msgstr ""

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_client
msgid "<span class=\"o_stat_text\">Olivery Orders</span>"
msgstr "<span class=\"o_stat_text\">طلبيات اوليفري</span>"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "Main addresses"
msgstr "العناوين الرئيسية"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_clients_addresses
msgid "Are you sure you want to generate full addresses for those clients?"
msgstr "هل انت متأكد من انك تريد انشاء العناوين الناقصه لهؤلاء الزبائن؟"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_form_rb_delivery_clients_addresses
msgid "Generate"
msgstr "انشاء"

#. module: olivery_recepient
#: model:ir.actions.act_window,name:olivery_recepient.action_generate_missing_full_addresses
msgid "Generate Missing full addresses"
msgstr "انشاء العناوين الناقصة"

#. olivery_recepient
#: code:addons/olivery_recepient/models/client/client_model.py:255
#: code:addons/olivery_recepient/models/order/order_model.py:83
#, python-format
msgid "Client values updated by %s"
msgstr "تم تحديث بيانات العميل بواسطة %s"


#. module: olivery_recepient
#: model:ir.actions.act_window,name:olivery_recepient.action_storex_modules_notify_clients
msgid "Notify clients"
msgstr "إخطار العملاء"

#. module: olivery_recepient
#: model_terms:ir.ui.view,arch_db:olivery_recepient.view_search_rb_delivery_client
msgid "By Whatsapp Msg Date"
msgstr "بواسطة تاريخ رسالة الواتس اب"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__client_number
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_order__client_number
msgid "Client Number"
msgstr "رقم العميل"


#. module: olivery_recepient
#: model:res.groups,name:rb_delivery.rb_delivery.role_client
msgid "Client Role"
msgstr "صلاحية العميل"

#. module: olivery_recepient
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_client__client_user
#: model:ir.model.fields,field_description:olivery_recepient.field_rb_delivery_order__client_user
msgid "Client User"
msgstr "مستخدم العميل"
