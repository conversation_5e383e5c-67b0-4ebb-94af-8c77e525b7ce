
<odoo>
  <data>
    <!-- inherit module [storex_modules] -->
    <record id="view_form_rb_delivery_client" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_client</field>
      <field name="model">rb_delivery.client</field>

      <field name="arch" type="xml">
        <form>

          <header>
          </header>

          <sheet>
           <field name="user_image" widget='image' nolabel="1" class="oe_avatar" />
            <div class="oe_button_box o_full" style="margin-top:15px" name="button_box">
              <button name="retrieve_delivery_orders_assigned" type="object"
                class="btn btn-sm oe_stat_button o_form_invisible">
                <div class="fa fa-fw fa-list-alt o_button_icon" />
                    <div class="o_form_field o_stat_info">
                        <span class="o_stat_text">Olivery Orders</span>
                    </div>
              </button>
           </div>
            <group name="group_top">
              <separator string="User Info :"/>
            </group>

            <group name="group_top">

              <group name="group_left">
                <field name="id"/>
                <field name="username" string="User Name"/>
              </group>
            </group>


            <group name="group_top">

              <group name="group_left">
                <field name="mobile_number" string="First Customer Mobile"/>
                <field name="second_mobile_number" string="Second Customer Mobile" />
                <field name="whatsapp_mobile" />
                <field name="second_whatsapp_mobile"/>
                <field name="email"/>
                <field name="address_tag" />
                <field name="area_id" options="{'no_create': True, 'no_create_edit':True}" />
                <field name="show_sub_area" invisible="1"/>
                <field name="sub_area" string="Customer Sub Area"
                  domain="[('parent_id', '=',area_id)]"
                  attrs="{'invisible':[('show_sub_area','!=',True)], 'readonly':[('area_id','=',False)]}"
                  options="{'no_create': True, 'no_create_edit':True}" />

              </group>

              <group name="group_right">
                <field name="address"/>
                <field name="commercial_id"  widget="many2many_tags" domain="[('role_code','=','rb_delivery.role_business')]"/>
                <field name="client_number"/>
                <field name="rb_user" readonly="1"/>
              </group>

            </group>
            <notebook>

              <page string="Location">
                <group>
                  <field name="location_link" widget="url"/>
                  <field name="latitude"/>
                  <field name="longitude"/>
                </group>
              </page>


              <page string="Settings" >
                <group>
                <field name="user_parent_id" string="Main Address/Main User"/>
                <field name="child_ids" string="Addresses/Sub Users" widget="many2many_tags"/>
                </group>
              </page>
            </notebook>

          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_client" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_client</field>
      <field name="model">rb_delivery.client</field>

      <field name="arch" type="xml">
        <tree>
         <field name="username"/>
         <field name="client_number"/>
          <field name="mobile_number"/>
          <field name="area_id" options="{'no_create': True, 'no_create_edit':True}" />
          <field name="sub_area" options="{'no_create': True, 'no_create_edit':True}" />
          <field name="address"/>
          <field name="commercial_id"/>
         </tree>
      </field>

    </record>

    <record id="view_search_rb_delivery_client" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_client</field>
      <field name="model">rb_delivery.client</field>

      <field name="arch" type="xml">
        <search>
          <group>
            <field name="username" filter_domain="[('username','ilike',self)]"/>
            <field name="client_number"/>
            <field name="mobile_number"/>
            <field name="second_mobile_number"/>
            <field name="commercial_id"/>
            <field name="email"/>
            <field name="address"/>
          </group>
          <group string="Filters">
            <filter name="main_addresses" string="Main addresses" domain="[('user_parent_id','=',False)]"/>
          </group>
          <group string="Groups">
            <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
            <filter name="group_by_create_date" string="By Create Date" icon="terp-partner" context="{'group_by':'create_date'}"/>
            <filter name="group_by_area" string="By Area" domain="[ ]" context="{'group_by': 'area_id'}" />
            <filter name="group_by_sub_area" string="By Sub Area" domain="[ ]" context="{'group_by': 'sub_area'}" />
            <filter name="group_by_main_address" string="By Main Address" domain="[ ]" context="{'group_by': 'user_parent_id'}" />
            <filter name="groub_by_whatsapp_msg_date" string="By Whatsapp Msg Date" icon="terp-partner" context="{'group_by':'last_sent_whatsapp_datetime:day'}"/>
          </group>
        </search>
      </field>

    </record>

    <record id="view_form_rb_delivery_client_archive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_client_archive</field>
      <field name="model">rb_delivery.archive_client</field>
        <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
                <separator string="Are you sure you want to archive ? "/>
            </group>
          </sheet>

          <footer>
            <button name="select_archive" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_client_unarchive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_client_unarchive</field>
      <field name="model">rb_delivery.unarchive_client</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you sure you want to unarchive ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_unarchive" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

  </data>
</odoo>
