# -*- coding: utf-8 -*-

import json
import logging
import qrcode
import base64
from io import BytesIO
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import math
_logger = logging.getLogger(__name__)


class olivery_recepient_order(models.Model):

    _inherit = 'rb_delivery.order'

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    client_fields_map = {
                        'commercial_id':{'fields':['assign_to_business']},
                        'username':{'fields':['customer_name']},
                        'address':{'fields':['customer_address']},
                        'mobile_number':{'fields':['customer_mobile']},
                        'area_id':{'fields':['customer_area','customer_area_id','customer_area_code']},
                        'sub_area':{'fields':['customer_sub_area','customer_sub_area_code','customer_sub_area_id']},
                        'second_mobile_number':{'fields':['second_mobile_number']},
                        'client_number':{'fields':['client_number']},
                        'address_tag':{'fields':['address_tag']},}

    client = fields.Many2one('rb_delivery.client', "Client")

    client_number = fields.Char(string='Client Number',track_visibility="on_change")

    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------

    @api.onchange('receiver_business','receiver_is_business','client')
    def _change_receiver_data(self):
        if self.client and not self.receiver_is_business:
            self.customer_name = self.client.username
            self.customer_mobile = self.client.mobile_number
            self.customer_area = self.client.area_id.id
            self.customer_address = self.client.address
            self.client_number = self.client.client_number
            if self.client.sub_area:
                self.customer_sub_area = self.client.sub_area.id
            if self.client.longitude and self.client.latitude:
                if not self.longitude and not self.latitude:
                    self.longitude=self.client.longitude
                    self.latitude =self.client.latitude
        else:
            self.client = False
            return super(olivery_recepient_order,self)._change_receiver_data()

    def change_in_client(self,values):
        use_parent_child_for_same_mobile = self.env['rb_delivery.client_configuration'].get_param('use_parent_child_for_same_mobile')

        vals = {}
        assign_to_business = values.get('assign_to_business')
        for client_field_name, client_field_info in self.client_fields_map.items():
            for field_name in client_field_info.get('fields', []):
                value = values.get(field_name)
                if field_name == 'assign_to_business' and assign_to_business:
                    vals['commercial_id'] = [(6, 0, [assign_to_business])]
                elif field_name == 'customer_area_code' and 'customer_area_code' in values and values['customer_area_code']:
                    vals['area_id'] = self.env['rb_delivery.area'].search([['code','=',values['customer_area_code']]]).id
                elif field_name == 'customer_sub_area_code' and 'customer_sub_area_code' in values and values['customer_sub_area_code']:
                    vals['sub_area'] = self.env['rb_delivery.sub_area'].search([['code','=',values['customer_sub_area_code']]]).id
                elif value:
                    vals[client_field_name] = value

        if not vals:
            return

        client_id = values.get('client')
        if not client_id:
            required_fields = ['username', 'mobile_number', 'commercial_id']
            if all(vals.get(field) for field in required_fields):
                if use_parent_child_for_same_mobile:
                    new_client = self.env['rb_delivery.client'].create(vals)
                    if new_client:
                        values['client'] = new_client.id
                else:
                    client = self.env['rb_delivery.client'].sudo().search([('mobile_number','=',vals['mobile_number']),('user_parent_id','=',False)])
                    if client and client[0] and client[0].id and not use_parent_child_for_same_mobile:
                            client = client[0]
                            updated_vals = client.sudo().update_existing_client(vals, client.read(['username', 'area_id', 'address', 'mobile_number', 'sub_area', 'second_mobile_number', 'client_number', 'address_tag'])[0])
                            if updated_vals:
                                username = self.env.user.name
                                message=_("Client values updated by %s")%(username)
                                data = {'uid':self._uid,'message':message,'records':client,'values':updated_vals,'update':True}
                                self.env['rb_delivery.utility'].olivery_sudo(data)
                            values['client'] =client.id
                    else:
                        new_client = self.env['rb_delivery.client'].create(vals)
                        if new_client and new_client.id:
                            values['client'] = new_client.id
        else:
            client = self.env['rb_delivery.client'].browse(client_id)
            val = client.change_in_client(vals, client.read(['username', 'area_id', 'address', 'mobile_number', 'sub_area', 'second_mobile_number', 'client_number', 'address_tag'])[0])
            if not val:
                self.env['rb_delivery.client'].update_client_commercial(vals, client)
            else:
                new_client = self.env['rb_delivery.client'].create(vals)
                if new_client:
                    values['client'] = new_client.id

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    @api.model
    def create(self, values):
        # TODO we need to enable this one
        vals = values.copy()
        if 'client' in vals:
            del vals['client']
        if 'assign_to_business' not in values:
            user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
            is_business = user.sudo().has_group('rb_delivery.role_business')
            if is_business:
                del_user = self.env['rb_delivery.user'].sudo().search([('user_id', '=', user.id)])
                vals['assign_to_business'] = del_user.id
        if 'receiver_is_business' in values and values['receiver_is_business']:
            pass
        else:
            self.change_in_client(vals)
        if 'client' in vals and vals['client']:
            values['client'] = vals['client']
        if 'client' in values and values['client']:
            self.map_client_order_info(values)
        return super(olivery_recepient_order, self).create(values)

    @api.model_create_multi
    def create_multi(self, vals):
        for val in vals:
            if 'client' in val and val['client']:
                self.map_client_order_info(val)

        return super(olivery_recepient_order, self).create_multi(vals)


    def guard_import(self, import_fields, data):

        for rec in data:
            if 'client' in import_fields:
                client_index = import_fields.index('client')
                if not rec[client_index]:
                    rec[client_index] = ''
                else:
                    client = self.env['rb_delivery.client'].sudo().search([
                        '|',
                        ('client_number', '=', rec[client_index]),
                        ('id', '=', rec[client_index])
                    ])
                    if not client:
                        continue

                    fields_to_process = {
                        'customer_name': client.username or '',
                        'customer_mobile': client.mobile_number or '',
                        'customer_area': client.area_id.id or False,
                        'customer_sub_area': client.sub_area.id or '',
                        'customer_address': client.address or ''
                    }

                    for field_name, value in fields_to_process.items():
                        if field_name not in import_fields:
                            import_fields.append(field_name)
                            rec.append(value)
                        else:
                            index = import_fields.index(field_name)
                            if len(rec) <= index:
                                rec.extend([''] * (index - len(rec) + 1))
                            rec[index] = value

        return super(olivery_recepient_order, self).guard_import(import_fields, data)


    @api.model
    def map_client_order_info(self, values):
        client = self.env['rb_delivery.client'].sudo().browse([values.get('client')])
        if client:
            if not values.get('customer_area') and client.area_id:
                values['customer_area'] = client.area_id.id
            if not values.get('customer_sub_area') and client.sub_area:
                if client.sub_area.parent_id.id == client.area_id.id:
                    values['customer_sub_area'] = client.sub_area
            if not values.get('customer_mobile') and client.mobile_number:
                values['customer_mobile'] = client.mobile_number
            if not values.get('customer_name') and client.username:
                values['customer_name'] = client.username
            if not values.get('customer_address') and client.address:
                values['customer_address'] = client.address


    def guard_function(self,values):
        for rec in self:
            vals = values.copy()

            if 'assign_to_business' in values or'customer_mobile' in values or 'customer_name' in values or 'customer_address' in values or 'customer_area' in values or 'second_mobile_number' in values:
                if 'assign_to_business' not in values:
                    vals['assign_to_business'] = rec.sudo().assign_to_business.id
                if 'customer_mobile' not in values:
                    vals['customer_mobile'] = rec.customer_mobile
                if 'customer_name' not in values:
                    vals['customer_name'] = rec.customer_name
                if 'customer_address' not in values:
                    vals['customer_address'] = rec.customer_address
                if 'customer_area' not in values:
                    vals['customer_area'] = rec.customer_area.id
                if 'second_mobile_number' not in values:
                    vals['second_mobile_number'] = rec.second_mobile_number
                if rec.receiver_is_business:
                    pass
                else:
                    rec.change_in_client(vals)
                if 'client' in vals and vals['client']:
                    values['client']  =  vals['client']
            if 'longitude' in values and values['longitude'] and 'latitude' in values and values['latitude']:
                client=''
                if 'client' in values and values['client']:
                    client=self.env['rb_delivery.client'].search([('id','=',values['client'])])
                elif rec.client:
                    client=rec.client
                if client and not client.longitude:
                    client.longitude = values['longitude']
                if client and not client.latitude:
                    client.latitude = values['latitude']

        return super(olivery_recepient_order, self).guard_function(values)
    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------