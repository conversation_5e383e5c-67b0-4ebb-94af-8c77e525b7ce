# -*- coding: utf-8 -*-
{
    'name': "olivery_recepient",
    'summary': """
        <PERSON>y Recepient App from olivery.app""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': 'v-1.2.12',

    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'rb_delivery','olivery_whatsapp_multi'],

    # always loaded
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'models/client/client_view.xml',
        'models/client/client_wizard_view.xml',
        'models/order/order_view.xml',
        'views/module_view.xml',
        'demo/client_conf.xml',
        'demo/status.xml',
        'data/delete_duplicate_clients.xml',
        'models/quick_order/quick_order_view.xml'
    ], 
    'qweb': [
         'static/src/xml/*.xml',
    ],

}
