<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="1">

    <record model="ir.module.category" id="model_rb_delivery">
      <field name="name">Delivery Application</field>
      <field name="description">This is related to Delivery application security</field>
      <field name="sequence">20</field>
      <field name="code">model_rb_delivery</field>
    </record>

    <record id="role_super_manager" model="res.groups">
      <field name="name">Super Manager Role</field>
      <field name="code">rb_delivery.role_super_manager</field>
      <field name="comment">
        Super Manager has access to all Orderd, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_pricelist_manager" model="res.groups">
      <field name="name">Pricelist Manager Role</field>
      <field name="code">rb_delivery.role_pricelist_manager</field>
      <field name="comment">
        Pricelist Manager has access to all Pricelists
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_collection_manager" model="res.groups">
      <field name="name">Collection Manager Role</field>
      <field name="code">rb_delivery.role_collection_manager</field>
      <field name="comment">
        Collection Manager has access to create and edit collections
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_returned_collection_manager" model="res.groups">
      <field name="name">Returned Collection Manager Role</field>
      <field name="code">rb_delivery.role_returned_collection_manager</field>
      <field name="comment">
        Returned Collection Manager has access to create and edit returned collections
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_settings_manager" model="res.groups">
      <field name="name">Olivery Settings manager</field>
      <field name="code">rb_delivery.role_settings_manager</field>
      <field name="comment">
      This role will have access to Olivery Settings item, very critical one
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

     <record id="role_configuration_manager" model="res.groups">
      <field name="name">Olivery Configuration manager</field>
      <field name="code">rb_delivery.role_configuration_manager</field>
      <field name="comment">
      This role will have access to Olivery Configuration item, very critical one
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_collection_archiver" model="res.groups">
      <field name="name">Collection Archiver</field>
      <field name="code">rb_delivery.role_collection_archiver</field>
      <field name="comment">
      This role will have access to Archive Collection
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_security_manager" model="res.groups">
      <field name="name">Security Manager Role</field>
      <field name="code">rb_delivery.role_security_manager</field>
      <field name="comment">
        Security Manager has access to all Pricelists and can edit all passwords
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_manager" model="res.groups">
      <field name="name">Manager Role</field>
      <field name="code">rb_delivery.role_manager</field>
      <field name="comment">
        Manager has access to all Orderd, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_picking_up_manager" model="res.groups">
      <field name="name">Picking Up Manager Role</field>
      <field name="code">rb_delivery.role_picking_up_manager</field>
      <field name="comment">
        Picking up Manager has access to all Orderd, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_distribution_manager" model="res.groups">
      <field name="name">Distribution Manager Role</field>
      <field name="code">rb_delivery.role_distribution_manager</field>
      <field name="comment">
        Distribution Manager has access to all Orderd, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_business" model="res.groups">
      <field name="name">Business Role</field>
      <field name="code">rb_delivery.role_business</field>
      <field name="comment">
        Business has access to Orders.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_driver" model="res.groups">
      <field name="name">Driver Role</field>
      <field name="code">rb_delivery.role_driver</field>
      <field name="comment">
        Driver can access his Orders.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_sort_and_distribute_representative" model="res.groups">
      <field name="name">Sort and Distribute Driver Role</field>
      <field name="code">rb_delivery.role_sort_and_distribute_representative</field>
      <field name="comment">
        Sort and Distribute Representative can access his Orders.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_picking_up_representative" model="res.groups">
      <field name="name">Picking up Driver Role</field>
      <field name="code">rb_delivery.role_picking_up_representative</field>
      <field name="comment">
        Picking Up Representative can access his Orders.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_sales" model="res.groups">
      <field name="name">Sales Role</field>
      <field name="code">rb_delivery.role_sales</field>
      <field name="comment">
        Sales can access same as manager but can only edit/create users and pricelist.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_olivery_setting_manager" model="res.groups">
      <field name="name">Olivery Settings Manager Role</field>
      <field name="code">rb_delivery.role_olivery_setting_manager</field>
      <field name="comment">
        Olivery Settings Manager can access his Olivery configurations and settings.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_olivery_block_delivery_fee" model="res.groups">
      <field name="name">Block Delivery Fee Role</field>
      <field name="code">rb_delivery.role_olivery_block_delivery_fee</field>
      <field name="comment">
        Olivery Block Delivery Fee can not see delivery fee anywhere.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_accounting" model="res.groups">
      <field name="name">Accounting Role</field>
      <field name="code">rb_delivery.role_accounting</field>
      <field name="comment">
        Accounting has access to all Orderd, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_junior_accounting" model="res.groups">
      <field name="name">Junior Accounting Role</field>
      <field name="code">rb_delivery.role_junior_accounting</field>
      <field name="comment">
        Accounting has access to all Orderd, and collections.
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_collection_manager" model="res.groups">
      <field name="name">Collection Manager Role</field>
      <field name="code">rb_delivery.role_collection_manager</field>
      <field name="comment">
        Collection Manager has access to create and edit collections
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_access_manager" model="res.groups">
      <field name="name">Access Manager Role</field>
      <field name="code">rb_delivery.role_access_manager</field>
      <field name="comment">
        Only this role has access to security matrix
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_warehouse_manager" model="res.groups">
      <field name="name">Warehouse Manager Role</field>
      <field name="comment">
        Warehouse manager can create and write on Warehouse model
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
      <field name="code">rb_delivery.warehouse_manager</field>
    </record>

    <record id="role_warehouse_employee" model="res.groups">
      <field name="name">Warehouse Employee Role</field>
      <field name="comment">
        Warehouse Employee can only read order lines
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
      <field name="code">rb_delivery.warehouse_employee</field>
    </record>

    <record id="role_warehouse_auditor" model="res.groups">
      <field name="name">Warehouse Auditor Role</field>
      <field name="comment">
        Warehouse Auditor can write and read orderlines
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
      <field name="code">rb_delivery.warehouse_auditor</field>
    </record>

    <!-- <record id="role_branch_accounting" model="res.groups">
      <field name="name">Branch Accounting Role</field>
      <field name="comment">
        Accounting has access to all Ordered in his branch, Users and Customers
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record> -->

    <record id="role_data_entry" model="res.groups">
      <field name="name">Data Entry Role</field>
      <field name="code">rb_delivery.role_data_entry</field>
      <field name="comment">
        Create order details which are not money related
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <record id="role_call_center" model="res.groups">
      <field name="name">Call Center Role</field>
      <field name="code">rb_delivery.role_call_center</field>
      <field name="comment">
         Call Center has access to Read and Edit status for orders
      </field>
      <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
    </record>

    <!-- Order logs -->
    <record id="rb_delivery_business_access_order_logs" model="ir.rule">
      <field name="name">business see only his record for Order logs</field>
      <field name="model_id" ref="model_rb_delivery_order_logs"/>
      <field name="domain_force">['|',('order_id.assign_to_business.user_id' ,'=', user.id),('order_id.assign_to_business.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <!-- notification -->
    <record id="rb_delivery_business_access_notification" model="ir.rule">
      <field name="name">business see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_driver_access_notification" model="ir.rule">
      <field name="name">driver see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_notification" model="ir.rule">
      <field name="name">Sort and distribute see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>
    
    <record id="rb_delivery_role_picking_up_representative_access_notification" model="ir.rule">
      <field name="name">Picking up driver see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_notification" model="ir.rule">
      <field name="name">accounting see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_notification" model="ir.rule">
      <field name="name">Junior accounting see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_notification" model="ir.rule">
      <field name="name">data entry see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_notification" model="ir.rule">
      <field name="name">call center see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_sales_access_notification" model="ir.rule">
      <field name="name">sales see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sales'))]"/>
    </record>

    <record id="rb_delivery_manager_access_notification" model="ir.rule">
      <field name="name">manager see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_notification" model="ir.rule">
      <field name="name">Picking up manager see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_notification" model="ir.rule">
      <field name="name">Distribution manager see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_notification" model="ir.rule">
      <field name="name">manager see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_notification" model="ir.rule">
      <field name="name">Warehouse Employee see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_notification" model="ir.rule">
      <field name="name">Warehouse Auditor see only his record for notification</field>
      <field name="model_id" ref="model_rb_delivery_notification_center"/>
      <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_configuration_manager_ir_filters" model="ir.rule">
      <field name="name">configuration manager see and delete any record  in ir filters</field>
      <field name="model_id" ref="base.model_ir_filters"/>
      <field name="domain_force"></field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_configuration_manager'))]"/>
    </record>


    <record id="rb_delivery_business_access_chat_notification" model="ir.rule">
      <field name="name">business see only his record for chat notification</field>
      <field name="model_id" ref="model_rb_delivery_chat_notification"/>
      <field name="domain_force">[('chat_partner' ,'in', user.id)]</field>
      <field name="groups" eval="[(6,0,[ref('rb_delivery.role_sales'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_picking_up_representative'),ref('rb_delivery.role_sort_and_distribute_representative'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_distribution_manager'),ref('rb_delivery.role_picking_up_manager'),ref('rb_delivery.role_warehouse_manager'),ref('rb_delivery.role_warehouse_employee'),ref('rb_delivery.role_warehouse_auditor'),ref('rb_delivery.role_accounting')])]"/>
    </record>


    <!-- order -->
    <record id="rb_delivery_business_access_order" model="ir.rule">
      <field name="name">business see only his record for order</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
            <!-- <field name="domain_force">['|','|',('assign_to_business.parent_id','=',user.id),('create_uid' ,'=', user.id),('assign_to_business.user_id','=',user.id)]</field> -->
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids),'|',('assign_to_business.user_id','=',user.id),('assign_to_business.user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_driver_access_order" model="ir.rule">
      <field name="name">driver see only his record for order</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids),'|',('assign_to_agent.user_id','=',user.id),('current_drivers.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_order" model="ir.rule">
      <field name="name">Sort and Distribute Representative see only his record for order</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids),'|',('assign_to_agent.user_id','=',user.id),('current_drivers.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_order" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for order</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids),'|',('assign_to_agent.user_id','=',user.id),('current_drivers.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <!-- user -->
    <record id="rb_delivery_business_access_user" model="ir.rule">
      <field name="name">business see only his record for user</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">['|',('user_id' ,'=', user.id),('user_parent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_driver_access_user" model="ir.rule">
      <field name="name">driver see only his record for user</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[('user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_user" model="ir.rule">
      <field name="name">Sort and Distribute Representative see only his record for user</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[('user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_user" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for user</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[('user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_order" model="ir.rule">
      <field name="name">Accounting can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_collections" model="ir.rule">
      <field name="name">Accounting can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_returned_collections" model="ir.rule">
      <field name="name">Accounting can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_agent_money_collections" model="ir.rule">
      <field name="name">Accounting can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Accounting can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_runsheet" model="ir.rule">
      <field name="name">Accounting can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_accounting_access_user" model="ir.rule">
      <field name="name">Accounting can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_order" model="ir.rule">
      <field name="name">Junior Accounting can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_collections" model="ir.rule">
      <field name="name">Junior Accounting can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_returned_collections" model="ir.rule">
      <field name="name">Junior Accounting can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_agent_money_collections" model="ir.rule">
      <field name="name">Junior Accounting can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Junior Accounting can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_runsheet" model="ir.rule">
      <field name="name">Junior Accounting can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_junior_accounting_access_user" model="ir.rule">
      <field name="name">Junior Accounting can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_junior_accounting'))]"/>
    </record>

    <record id="rb_delivery_manager_access_order" model="ir.rule">
      <field name="name">Manager can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_order" model="ir.rule">
      <field name="name">Pciking Up Manager can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_order" model="ir.rule">
      <field name="name">Distribution Manager can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_order" model="ir.rule">
      <field name="name">Manager can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_order" model="ir.rule">
      <field name="name">Warehouse Employee can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_order" model="ir.rule">
      <field name="name">Warehouse Auditor can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_collections" model="ir.rule">
      <field name="name">Manager can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_collections" model="ir.rule">
      <field name="name">Picking Manager can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_collections" model="ir.rule">
      <field name="name">Distribution Manager can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_collections" model="ir.rule">
      <field name="name">Manager can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_collections" model="ir.rule">
      <field name="name">Warehouse Employee can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>


    <record id="rb_delivery_warehouse_auditor_access_collections" model="ir.rule">
      <field name="name">Warehouse Auditor can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_returned_collections" model="ir.rule">
      <field name="name">Manager can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_returned_collections" model="ir.rule">
      <field name="name">Picking Up Manager can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_returned_collections" model="ir.rule">
      <field name="name">Distribution Manager can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_returned_collections" model="ir.rule">
      <field name="name">Manager can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_returned_collections" model="ir.rule">
      <field name="name">Warehouse Employee can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_returned_collections" model="ir.rule">
      <field name="name">Warehouse Auditor can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_agent_money_collections" model="ir.rule">
      <field name="name">Manager can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_agent_money_collections" model="ir.rule">
      <field name="name">Picking up Manager can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_agent_money_collections" model="ir.rule">
      <field name="name">Distribution Manager can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_agent_money_collections" model="ir.rule">
      <field name="name">Manager can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_agent_money_collections" model="ir.rule">
      <field name="name">Warehouse Employee can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_agent_money_collections" model="ir.rule">
      <field name="name">Warehouse Auditor can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Manager can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Picking Up Manager can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Distribution Manager can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Manager can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Warehouse Employee can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Warehouse Auditor can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_runsheet" model="ir.rule">
      <field name="name">Manager can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_runsheet" model="ir.rule">
      <field name="name">Picking Up Manager can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_runsheet" model="ir.rule">
      <field name="name">Distribution Manager can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_runsheet" model="ir.rule">
      <field name="name">Manager can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_runsheet" model="ir.rule">
      <field name="name">Warehouse Employee can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_runsheet" model="ir.rule">
      <field name="name">Warehouse Auditor can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>

    <record id="rb_delivery_manager_access_user" model="ir.rule">
      <field name="name">Manager can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_manager_access_user" model="ir.rule">
      <field name="name">Picking Up Manager can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_manager'))]"/>
    </record>

    <record id="rb_delivery_role_distribution_manager_access_user" model="ir.rule">
      <field name="name">Distribution Manager can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_distribution_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_manager_access_user" model="ir.rule">
      <field name="name">Manager can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_manager'))]"/>
    </record>

    <record id="rb_delivery_warehouse_employee_access_user" model="ir.rule">
      <field name="name">Warehouse Employee can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_employee'))]"/>
    </record>

    <record id="rb_delivery_warehouse_auditor_access_user" model="ir.rule">
      <field name="name">Warehouse Auditor can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_warehouse_auditor'))]"/>
    </record>


    <record id="rb_delivery_data_entry_access_order" model="ir.rule">
      <field name="name">Data entry can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_collections" model="ir.rule">
      <field name="name">Data Entry can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_returned_collections" model="ir.rule">
      <field name="name">Data Entry can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_agent_money_collections" model="ir.rule">
      <field name="name">Data Entry can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Data Entry can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_runsheet" model="ir.rule">
      <field name="name">Data Entry can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>

    <record id="rb_delivery_driver_access_runsheet" model="ir.rule">
      <field name="name">Driver can see the runsheet collections assigned to him</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[('assign_to_agent.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_runsheet" model="ir.rule">
      <field name="name">Sort and Distribute Representative can see the runsheet collections assigned to him</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[('assign_to_agent.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_runsheet" model="ir.rule">
      <field name="name">Picking Up Representative can see the runsheet collections assigned to him</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[('assign_to_agent.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <record id="rb_delivery_data_entry_access_user" model="ir.rule">
      <field name="name">Data entry can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_data_entry'))]"/>
    </record>


    <record id="rb_delivery_call_center_access_order" model="ir.rule">
      <field name="name">Call center can see the orders in statuses he has access to</field>
      <field name="model_id" ref="model_rb_delivery_order"/>
      <field name="domain_force">['|',('state_id','=',False),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_collections" model="ir.rule">
      <field name="name">Call Center can see the collections.</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_returned_collections" model="ir.rule">
      <field name="name">Call Center can see the returned collections.</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_agent_money_collections" model="ir.rule">
      <field name="name">Call Center can see the agent money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_agent_returned_money_collections" model="ir.rule">
      <field name="name">Call Center can see the agent returned money collections.</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_runsheet" model="ir.rule">
      <field name="name">Call Center can see the runsheet collections.</field>
      <field name="model_id" ref="model_rb_delivery_runsheet"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

    <record id="rb_delivery_call_center_access_user" model="ir.rule">
      <field name="name">Call center can see the users.</field>
      <field name="model_id" ref="model_rb_delivery_user"/>
      <field name="domain_force">[]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_call_center'))]"/>
    </record>

<!-- collection -->
    <record id="rb_delivery_business_access_returned_collection" model="ir.rule">
      <field name="name">business see only his record for returned collection</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">['|',('order_ids.assign_to_business.user_id' ,'=', user.id), ('order_ids.assign_to_business.user_parent_id.user_id' ,'=', user.id),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_business_access_collection" model="ir.rule">
      <field name="name">business see only his record for collection</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">['|','|',('order_ids.assign_to_business.user_id' ,'=', user.id), ('order_ids.assign_to_business.user_parent_id.user_id' ,'=', user.id),('create_uid','=',user.id),('state_id','in',user.partner_id.status_ids.ids)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_business_access_agent_collection" model="ir.rule">
      <field name="name">business see only his record for agent collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('has_one_business' ,'=', True), ('business_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
    </record>

    <record id="rb_delivery_driver_access_collection" model="ir.rule">
      <field name="name">driver see only his record for collection</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for collection</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_collection" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for collection</field>
      <field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <record id="rb_delivery.rb_delivery_driver_access_collection" model="ir.rule">
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
    </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_collection')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>

    <record id="rb_delivery_driver_access_returned_money_collection" model="ir.rule">
      <field name="name">driver see only his record for returned money collection</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_returned_money_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for returned money collection</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_returned_money_collection" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for returned money collection</field>
      <field name="model_id" ref="model_rb_delivery_returned_money_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_returned_money_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <record id="rb_delivery.rb_delivery_driver_access_returned_money_collection" model="ir.rule">
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
    </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_returned_money_collection')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>

    <record id="rb_delivery_driver_access_agent_money_collection" model="ir.rule">
      <field name="name">driver see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_agent_money_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_agent_money_collection" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_money_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <record id="rb_delivery.rb_delivery_driver_access_agent_money_collection" model="ir.rule">
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
    </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_money_collection')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>

    <record id="rb_delivery_driver_access_agent_returned_collection" model="ir.rule">
      <field name="name">driver see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_agent_returned_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_agent_returned_collection" model="ir.rule">
      <field name="name">Pciking Up Representative see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_returned_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <record id="rb_delivery.rb_delivery_driver_access_agent_returned_collection" model="ir.rule">
      <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
    </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_returned_collection')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>

    <record id="rb_delivery_driver_access_agent_money_collection" model="ir.rule">
      <field name="name">driver see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="announcement_access_business" model="ir.rule">
      <field name="name">Restrict Announcement Visibility</field>
      <field name="model_id" ref="model_rb_delivery_announcement"/>
      <field name="domain_force">['|', ('users', '=', False), ('users.user_id','=',user.id)]</field>
      <field name="groups" eval="[(4, ref('rb_delivery.role_business'))]"/>
    </record>
    
    <record id="rb_delivery_role_sort_and_distribute_representative_access_agent_money_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_agent_money_collection" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for agent money collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_money_collection"/>
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_money_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
    </function>
    <record id="rb_delivery.rb_delivery_driver_access_agent_money_collection" model="ir.rule">
      <field name="domain_force">[('agent_id.user_id' ,'=', user.id)]</field>
    </record>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_driver_access_agent_money_collection')]"/>
        </function>
        <value eval="{'noupdate': False}" />
    </function>

    <record id="rb_delivery_driver_access_agent_returned_collection" model="ir.rule">
      <field name="name">driver see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">['|',('order_ids.assign_to_agent.user_id' ,'=', user.id), ('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_driver'))]"/>
    </record>

    <record id="rb_delivery_role_sort_and_distribute_representative_access_agent_returned_collection" model="ir.rule">
      <field name="name">Sort And Distribute Representative see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">['|',('order_ids.assign_to_agent.user_id' ,'=', user.id), ('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_sort_and_distribute_representative'))]"/>
    </record>

    <record id="rb_delivery_role_picking_up_representative_access_agent_returned_collection" model="ir.rule">
      <field name="name">Picking Up Representative see only his record for agent returned collection</field>
      <field name="model_id" ref="model_rb_delivery_agent_returned_collection"/>
      <field name="domain_force">['|',('order_ids.assign_to_agent.user_id' ,'=', user.id), ('agent_id.user_id' ,'=', user.id)]</field>
      <field name="groups" eval="[(4,ref('rb_delivery.role_picking_up_representative'))]"/>
    </record>


    <record id="res_users_rule_write_own" model="ir.rule">
        <field name="name">Users: Write Own Record Only</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="domain_force">[('id', '=', user.id)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="res_users_rule_read_all" model="ir.rule">
        <field name="name">Users: Read All Records</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="res_users_rule_no_delete" model="ir.rule">
        <field name="name">Users: No Delete Permission</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="domain_force">[(1, '=', 0)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="res_partner_rule_write_own" model="ir.rule">
        <field name="name">Partners: Write Own Record Only</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">[('id', '=', user.partner_id.id)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="res_partner_rule_read_all" model="ir.rule">
        <field name="name">Partners: Read All Records</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="res_partner_rule_no_delete" model="ir.rule">
        <field name="name">Partners: No Delete Permission</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">[(1, '=', 0)]</field>
        <field name="groups" eval="[(6, 0, [
            ref('rb_delivery.role_business'),
            ref('rb_delivery.role_super_manager'),
            ref('rb_delivery.role_driver'),
            ref('rb_delivery.role_sort_and_distribute_representative'),
            ref('rb_delivery.role_picking_up_representative'),
            ref('rb_delivery.role_manager'),
            ref('rb_delivery.role_picking_up_manager'),
            ref('rb_delivery.role_distribution_manager'),
            ref('rb_delivery.role_sales'),
            ref('rb_delivery.role_accounting'),
            ref('rb_delivery.role_security_manager'),
            ref('rb_delivery.role_pricelist_manager')
        ])]"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="res_users_rule_admin_full" model="ir.rule">
        <field name="name">Users: Admin Full Access</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(6, 0, [ref('base.group_system')])]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="res_partner_rule_admin_full" model="ir.rule">
        <field name="name">Partners: Admin Full Access</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(6, 0, [ref('base.group_system')])]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="role_delivery_company" model="res.groups">
            <field name="name">Delivery Company Role</field>
            <field name="comment">
                Driver can access his Orders.
            </field>
            <field name="code">rb_delivery.role_delivery_company</field>
            <field name="category_id" ref="rb_delivery.model_rb_delivery"/>
        </record>

        <record id="rb_delivery_delivery_company_access_notification" model="ir.rule">
            <field name="name">Delivery company see only his record for notification</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_notification_center"/>
            <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

        <record id="rb_delivery_vhub_delivery_company_access_order" model="ir.rule">
            <field name="name">delivery company see only his record for order</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_order"/>
            <field name="domain_force">['|',('assign_to_agent.user_id','=',user.id),('current_drivers.user_id','=',user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

        <record id="rb_delivery_delivery_company_access_user" model="ir.rule">
            <field name="name">delivery company see only his record for user</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_user"/>
            <field name="domain_force">[('user_id','=',user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

        <record id="rb_delivery_delivery_company_access_collection" model="ir.rule">
            <field name="name">delivery company see only his record for collection</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_multi_print_orders_money_collector"/>
            <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

      <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
      </function>
      <record id="rb_delivery.rb_delivery_delivery_company_access_collection" model="ir.rule">
              <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
          </record>
      <function name="write" model="ir.model.data">
          <function name="search" model="ir.model.data">
              <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_collection')]"/>
          </function>
          <value eval="{'noupdate': False}" />
      </function>

      <record id="rb_delivery_delivery_company_access_returned_money_collection" model="ir.rule">
          <field name="name">delivery company see only his record for returned money collection</field>
          <field name="model_id" ref="rb_delivery.model_rb_delivery_returned_money_collection"/>
          <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
          <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
      </record>

      <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_returned_money_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
      </function>
      <record id="rb_delivery.rb_delivery_delivery_company_access_returned_money_collection" model="ir.rule">
          <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
      </record>
      <function name="write" model="ir.model.data">
          <function name="search" model="ir.model.data">
              <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_returned_money_collection')]"/>
          </function>
          <value eval="{'noupdate': False}" />
      </function>


        <record id="rb_delivery_delivery_company_access_agent_money_collection" model="ir.rule">
            <field name="name">delivery company see only his record for agent money collection</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_agent_money_collection"/>
            <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

        <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_agent_money_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
      </function>
      <record id="rb_delivery.rb_delivery_delivery_company_access_agent_money_collection" model="ir.rule">
            <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
        </record>
      <function name="write" model="ir.model.data">
          <function name="search" model="ir.model.data">
              <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_agent_money_collection')]"/>
          </function>
          <value eval="{'noupdate': False}" />
      </function>

        <record id="rb_delivery_delivery_company_access_agent_returned_collection" model="ir.rule">
            <field name="name">delivery company see only his record for agent returned collection</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_agent_returned_collection"/>
            <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_delivery_company'))]"/>
        </record>

        <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_agent_returned_collection')]"/>
        </function>
        <value eval="{'noupdate': True}" />
      </function>
      <record id="rb_delivery.rb_delivery_delivery_company_access_agent_returned_collection" model="ir.rule">
            <field name="domain_force">[('driver_id.user_id' ,'=', user.id)]</field>
        </record>
      <function name="write" model="ir.model.data">
          <function name="search" model="ir.model.data">
              <value eval="[('model', '=', 'ir.rule'), ('name', '=', 'rb_delivery_delivery_company_access_agent_returned_collection')]"/>
          </function>
          <value eval="{'noupdate': False}" />
      </function>

        <record id="rb_delivery_business_access_driver_order_location" model="ir.rule">
          <field name="name">business see only his record for driver order location</field>
          <field name="model_id" ref="model_rb_delivery_driver_order_location"/>
          <field name="domain_force">['|',('user_id.user_id' ,'=', user.id),('user_id.user_parent_id.user_id' ,'=', user.id)]</field>
          <field name="groups" eval="[(4,ref('rb_delivery.role_business'))]"/>
        </record>

  </data>
</odoo>
