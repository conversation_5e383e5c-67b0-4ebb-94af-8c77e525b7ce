
<odoo >
    <data noupdate="1">
        <!-- http://true-buyer.olivery.io -->
        <!--  client configuration type -->
        <record id="client_configuration_type_login" model="rb_delivery.client_configuration_type">
            <field name="name">login</field>
            <field name="description">Any thing related to login</field>
        </record>

        <record id="client_configuration_type_theme" model="rb_delivery.client_configuration_type">
            <field name="name">Theme</field>
            <field name="description">Any thing related to the Theme</field>
        </record>

        <record id="client_configuration_type_order_detail" model="rb_delivery.client_configuration_type">
            <field name="name">order detail</field>
            <field name="description">order detail Information</field>
        </record>

        <record id="client_configuration_type_financial" model="rb_delivery.client_configuration_type">
            <field name="name">Financial</field>
            <field name="description">Any thing related to financial</field>
        </record>

        <record id="client_configuration_type_collection" model="rb_delivery.client_configuration_type">
            <field name="name">Collection</field>
            <field name="description">Any thing related to Collection</field>
        </record>

        <record id="client_configuration_type_setting" model="rb_delivery.client_configuration_type">
            <field name="name">Setting</field>
            <field name="description">Any thing related to Setting</field>
        </record>

        <record id="client_configuration_type_register" model="rb_delivery.client_configuration_type">
            <field name="name">Register</field>
            <field name="description">Any thing related to Register</field>
        </record>

        <record id="client_configuration_type_defaults" model="rb_delivery.client_configuration_type">
            <field name="name">Defaults</field>
            <field name="description">Any thing related to Default values</field>
        </record>

        <record id="client_configuration_type_vhub" model="rb_delivery.client_configuration_type">
            <field name="name">VHub</field>
            <field name="description">Any thing related to VHub</field>
        </record>

        <record id="client_configuration_type_returned_prices_configuration" model="rb_delivery.client_configuration_type">
            <field name="name">Returned prices configuration</field>
            <field name="description">Any thing related to returned prices</field>
        </record>

        <record id="client_configuration_type_payment_type" model="rb_delivery.client_configuration_type">
            <field name="name">Payment Type</field>
            <field name="description">Any thing related to payment type</field>
        </record>
        <record id="client_configuration_type_dicimal" model="rb_delivery.client_configuration_type">
            <field name="name">Dicimal</field>
            <field name="description">Any thing related to Dicimal values</field>
        </record>
        <record id="client_configuration_type_api" model="rb_delivery.client_configuration_type">
            <field name="name">API</field>
            <field name="description">Any thing related to APIs</field>
        </record>

        <record id="client_configuration_track_driver_link_type" model="rb_delivery.client_configuration_type">
            <field name="name">Tracking Driver Links</field>
            <field name="description">Any thing related to tracking driver links</field>
        </record>


        <record id="client_configuration_type_api" model="rb_delivery.client_configuration_type">
            <field name="name">API</field>
            <field name="description">Any thing related to APIs</field>
        </record>

        <record id="client_configuration_type_quick_order" model="rb_delivery.client_configuration_type">
            <field name="name">Quick Order</field>
            <field name="description">Any thing related to quick order</field>
        </record>

        <record id="client_configuration_type_public_link" model="rb_delivery.client_configuration_type">
            <field name="name">Public Links</field>
            <field name="description">Any thing related to Public Links</field>
        </record>

        <!-- Public Links -->

        <record id="client_configuration_public_link_domain" model="rb_delivery.client_configuration">
            <field name="key">public_link_domain</field>
            <field name="text">http://true-buyer.olivery.io</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Public Link Domain</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_public_link"/>
        </record>

        <record id="client_configuration_public_link_multi_orders" model="rb_delivery.client_configuration">
            <field name="key">is_public_link_multi_orders</field>
            <field name="value">True</field>
            <field name="text"></field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Is Public Link Multi Orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_public_link"/>
        </record>

        <record id="client_configuration_public_link_view_home_page" model="rb_delivery.client_configuration">
            <field name="key">view_home_page</field>
            <field name="value">True</field>
            <field name="text"></field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">View Home Page In Public Link</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_public_link"/>
        </record>

        <record id="client_configuration_is_public_link_button_visible" model="rb_delivery.client_configuration">
            <field name="key">is_public_link_button_visible</field>
            <field name="value">False</field>
            <field name="text"></field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">to make button visable or not</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_public_link"/>
        </record>

        <record id="client_configuration_roles_to_hide_public_link_button" model="rb_delivery.client_configuration">
            <field name="key">roles_to_hide_public_link_button</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_warehouse_employee')])]" />
            <field name="description">Roles that wont see the create public order button on active order page. use this configuration "is_public_link_button_visible" if you want to hide it for all roles </field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_public_link"/>
        </record>

        <!--  API configurations -->
        <record id="client_configuration_order_details_api_fields" model="rb_delivery.client_configuration">
            <field name="key">order_details_api_fields</field>
            <field name="value">True</field>
            <field name="related_to_text">False</field>
            <field name="related_to_field_ids">True</field>
            <field name="field_ids" eval="[(6,0,[
            ref('rb_delivery.field_rb_delivery_order__assign_to_agent'),
            ref('rb_delivery.field_rb_delivery_order__money_collection_cost'),
            ref('rb_delivery.field_rb_delivery_order__customer_payment'),
            ref('rb_delivery.field_rb_delivery_order__delivery_cost'),
            ref('rb_delivery.field_rb_delivery_order__customer_area'),
            ref('rb_delivery.field_rb_delivery_order__customer_name'),
            ref('rb_delivery.field_rb_delivery_order__customer_mobile'),
            ref('rb_delivery.field_rb_delivery_order__customer_address'),
            ref('rb_delivery.field_rb_delivery_order__state'),
            ref('rb_delivery.field_rb_delivery_order__reference_id'),
            ref('rb_delivery.field_rb_delivery_order__sequence'),
            ref('rb_delivery.field_rb_delivery_order__id'),
            ref('rb_delivery.field_rb_delivery_order__clone_reference'),
            ])]" />
            <field name="related_to_group_ids">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">These fields returned in /order API</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_api"/>
        </record>

        <!--  Theme configurations -->
        <record id="client_configuration_default_theme_color" model="rb_delivery.client_configuration">
            <field name="key">default_theme_color</field>
            <field name="value">True</field>
            <field name="text"></field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Default Theme Color</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_theme"/>
        </record>

        <!--  default configurations -->
        <record id="client_configuration_default_time_zone" model="rb_delivery.client_configuration">
            <field name="key">default_time_zone</field>
            <field name="value">True</field>
            <field name="text">Asia/Jerusalem</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Default timezone</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_add_plus_sign_for_sms" model="rb_delivery.client_configuration">
            <field name="key">add_plus_sign_for_sms</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Add plus sign before mobile number if it does not exist when send sms</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_search" model="rb_delivery.client_configuration">
            <field name="key">default_search</field>
            <field name="value">True</field>
            <field name="search_default">barcodes</field>
            <field name="partial_search">False</field>
            <field name="related_to_search">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Default Search</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_sms_provider" model="rb_delivery.client_configuration">
            <field name="key">default_sms_provider</field>
            <field name="value">True</field>
            <field name="related_to_sms">True</field>
            <field name="platform_type">web</field>
            <field name="description">Default SMS provider</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_delay_time" model="rb_delivery.client_configuration">
            <field name="key">default_delay_time</field>
            <field name="value">True</field>
            <field name="text">3</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Default Delay time</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_status_operation_order" model="rb_delivery.client_configuration">
            <field name="key">default_status_operation_order</field>
            <field name="value">False</field>
            <field name="description">the statuses that are related to operation orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_status_financial_order" model="rb_delivery.client_configuration">
            <field name="key">default_status_financial_order</field>
            <field name="value">False</field>
            <field name="description">the statuses that are related to financial orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_paid'),ref('rb_delivery.status_money_out'),ref('rb_delivery.status_money_in')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_status_active_order" model="rb_delivery.client_configuration">
            <field name="key">default_status_active_order</field>
            <field name="value">False</field>
            <field name="description">the statuses that are related to active orders</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_ready_for_return'),ref('rb_delivery.status_waiting'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_stuck'),ref('rb_delivery.status_delivered'),ref('rb_delivery.status_rejected'),ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_branch_returned'),ref('rb_delivery.status_money_received'),ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_out'),ref('rb_delivery.status_paid'),ref('rb_delivery.status_returned_in_progress'),ref('rb_delivery.status_returned_delivered'),ref('rb_delivery.status_ready_for_dispatch'),ref('rb_delivery.status_rejected_partial'),ref('rb_delivery.status_delivered_partial'),ref('rb_delivery.status_replacement'),ref('rb_delivery.status_pending_business_approval')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>



         <record id="client_configuration_default_language_setting" model="rb_delivery.client_configuration">
            <field name="key">default_language_setting</field>
            <field name="value">True</field>
            <field name="description">default language for mobile and new users {'English':'en_US','Hebrew':'he_il','العربية':'ar_SY'}</field>
            <field name="text">ar_SY</field>
            <field name="related_to_text">True</field>
            <field name="related_to_group_ids">False</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_mobile_languages_visibility_setting" model="rb_delivery.client_configuration">
            <field name="key">mobile_languages_visibility_setting</field>
            <field name="value">True</field>
            <field name="description">mobile languages should be like ( en_US,ar_SY ) hint : {'English':'en_US','Hebrew':'he_il','العربية':'ar_SY'}</field>
            <field name="text">ar_SY,en_US</field>
            <field name="related_to_text">True</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_refresh_pricelist_state" model="rb_delivery.client_configuration">
            <field name="key">refresh_pricelist_state</field>
            <field name="value">True</field>
            <field name="description">Default states for orders taken when refresh pricelist.</field>
            <field name="related_to_status">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <!--  client configurations -->

        <!--  collection configurations -->

        <record id="client_configuration_print_pdf_on_create" model="rb_delivery.client_configuration">
            <field name="key">print_pdf_on_create</field>
            <field name="value">False</field>
            <field name="description">Whether to print pdf with collection or not</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_print_returned_pdf_on_create" model="rb_delivery.client_configuration">
            <field name="key">print_returned_pdf_on_create</field>
            <field name="value">False</field>
            <field name="description">Whether to print pdf with collection or not</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_print_runsheet_pdf_on_create" model="rb_delivery.client_configuration">
            <field name="key">print_runsheet_pdf_on_create</field>
            <field name="value">False</field>
            <field name="description">Whether to print pdf with runsheet or not</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_reflect_values_of_collection_to_orders" model="rb_delivery.client_configuration">
            <field name="key">reflect_values_of_money_collection_to_orders</field>
            <field name="value">False</field>
            <field name="description">This configuration is responsible for reflecteing values of driver , current branch , to branch from collection to orders related to the collection . To make the configuration work . u should check the value as true </field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_allow_many_runsheet" model="rb_delivery.client_configuration">
            <field name="key">allow_many_runsheet</field>
            <field name="value">False</field>
            <field name="description">Whether to allow add order in many runsheets</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_show_note_in_collections" model="rb_delivery.client_configuration">
            <field name="key">show_note_in_collections</field>
            <field name="value">False</field>
            <field name="description">Whether to show note in collections</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_returned_collection_status" model="rb_delivery.client_configuration">
            <field name="key">returned_collection_status</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_branch_returned')])]" />
            <field name="description">The status the order should be in to be able to create returned collection</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_agent_returned_collection_status" model="rb_delivery.client_configuration">
            <field name="key">agent_returned_collection_status</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_returned_delivered')])]" />
            <field name="description">The status the order should be in to be able to create agent returned collection</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_agent_collection_status" model="rb_delivery.client_configuration">
            <field name="key">agent_collection_status</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered'),ref('rb_delivery.status_delivered_partial')])]" />
            <field name="description">The status the order should be in to be able to create agent collection</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_collection_status" model="rb_delivery.client_configuration">
            <field name="key">collection_status</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received')])]" />
            <field name="description">The status the order should be in to be able to create collection</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_collection_item_total_required_from_business_visibility" model="rb_delivery.client_configuration">
            <field name="key">collection_item_total_required_from_business_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show total required_from_business or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_collection_item_total_delivery_cost_visibility" model="rb_delivery.client_configuration">
            <field name="key">collection_item_total_delivery_cost_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show total delivery_cost or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>

        <record id="client_configuration_collection_item_total_money_collection_cost_visibility" model="rb_delivery.client_configuration">
            <field name="key">collection_item_total_money_collection_cost_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show total money collection cost or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
        </record>
        <!-- Login configuration  -->

        <record id="client_configuration_mobile_number_prefix_one" model="rb_delivery.client_configuration">
            <field name="key">mobile_number_prefix_one</field>
            <field name="value">True</field>
            <field name="text">972</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">The mobile intro that should be shown by default</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_email_registration" model="rb_delivery.client_configuration">
            <field name="key">email_registration</field>
            <field name="value">False</field>
            <field name="related_to_text">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Use Email for registration instead of mobile number</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_account_number_registration" model="rb_delivery.client_configuration">
            <field name="key">account_number_registration</field>
            <field name="value">False</field>
            <field name="related_to_text">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Use Account Number for registration instead of mobile number</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_mobile_number_prefix_two" model="rb_delivery.client_configuration">
            <field name="key">mobile_number_prefix_two</field>
            <field name="value">True</field>
            <field name="text">970</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">The mobile intro that should be shown by default</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_login_mobile_number_placeholder" model="rb_delivery.client_configuration">
            <field name="key">login_mobile_number_placeholder</field>
            <field name="value">True</field>
            <field name="text">059xxxxxxxx</field>
            <field name="platform_type" >mobile</field>
            <field name="description">The mobile intro that should be shown by default</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_login_enable_without_confirmation" model="rb_delivery.client_configuration">
            <field name="key">login_enable_without_confirmation</field>
            <field name="value">False</field>
            <field name="description">Enable user to login without confirmation</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_mobile_number_digit" model="rb_delivery.client_configuration">
            <field name="key">mobile_number_digit</field>
            <field name="value">True</field>
            <field name="text">10-10</field>
            <field name="related_to_text">True</field>
            <field name="description">Number of digits allowed for mobile number</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_login_show_powered_by_olivery" model="rb_delivery.client_configuration">
            <field name="key">login_show_powered_by_olivery</field>
            <field name="value">True</field>
            <field name="platform_type">mobile</field>
            <field name="description">Show/hide powered by olivery from login</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_show_forgot_passwrod_visibility" model="rb_delivery.client_configuration">
            <field name="key">show_forgot_passwrod_visibility</field>
            <field name="value">False</field>
            <field name="related_to_group_ids">False</field>
            <field name="platform_type" >mobile</field>
            <field name="description">Show/hide forgot password button in login page on mobile app</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>

        <record id="client_configuration_twilio_information" model="rb_delivery.client_configuration">
            <field name="key">twilio_information</field>
            <field name="value">True</field>
            <field name="twilio_sid">**********************************</field>
            <field name="twilio_auth_token">8e6b3ade43e3616b15fdbdbaae3e86da</field>
            <field name="twilio_messaging_service_sid">MG525ce5a935bf92d6fb9019a13f278dff</field>
            <field name="related_to_twilio">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Twilio Information</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_login"/>
        </record>
        <!-- order detail coniguration -->

        <record id="client_configuration_reference_id_digit" model="rb_delivery.client_configuration">
            <field name="key">reference_id_digit</field>
            <field name="value">True</field>
            <field name="text">2-20</field>
            <field name="related_to_text">True</field>
            <field name="description">Number of digits allowed for reference ID</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_hidden_roles" model="rb_delivery.client_configuration">
            <field name="key">hidden_roles</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="description">Roles in this configuration will not be shown when choose role for users.</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_picking_up_manager'),ref('rb_delivery.role_distribution_manager'),ref('rb_delivery.role_sort_and_distribute_representative'),ref('rb_delivery.role_picking_up_representative'),ref('rb_delivery.role_security_manager'),ref('rb_delivery.role_olivery_block_delivery_fee'),ref('rb_delivery.role_collection_archiver'),ref('rb_delivery.role_collection_manager'),ref('rb_delivery.role_configuration_manager'),ref('rb_delivery.role_settings_manager'),ref('rb_delivery.role_olivery_setting_manager'),ref('rb_delivery.role_pricelist_manager'),ref('rb_delivery.role_returned_collection_manager'),ref('rb_delivery.role_security_manager'),ref('rb_delivery.role_access_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_directly_create_order_from_draft_orders" model="rb_delivery.client_configuration">
            <field name="key">directly_create_order_from_draft_orders</field>
            <field name="value">True</field>
            <field name="description">Create orders directly from draft orders or use the action in draft orders page</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_create_order_button" model="rb_delivery.client_configuration">
            <field name="key">show_create_order_button</field>
            <field name="value">True</field>
            <field name="description">Show/hide the create order button on active order page . Just make the value false to hide the create button from the active order page, use this configuration to hide it for some roles "roles_to_hide_create_order_button"</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_roles_to_hide_create_order_button" model="rb_delivery.client_configuration">
            <field name="key">roles_to_hide_create_order_button</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="description">Roles that wont see the create order button on active order page. use this configuration "show_create_order_button" if you want to hide it for all roles </field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_roles_to_hide_quick_order_button" model="rb_delivery.client_configuration">
            <field name="key">roles_to_hide_quick_order_button</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_warehouse_employee')])]" />
            <field name="description">Roles that wont see the Quick order button on active order page.</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_totals_in_select_state_wizard" model="rb_delivery.client_configuration">
            <field name="key">show_totals_in_select_state_wizard</field>
            <field name="value">True</field>
            <field name="description">Show/Hide Total Agent Cost, Total Delivery Fee, Total Net value, and Total COD in select state wizard</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_alternate_address" model="rb_delivery.client_configuration">
            <field name="key">show_alternate_address</field>
            <field name="value">False</field>
            <field name="description">Show/Hide alternate address</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_fuzzy_search_accepted_ratio" model="rb_delivery.client_configuration">
            <field name="key">fuzzy_search_accepted_ratio</field>
            <field name="value">False</field>
            <field name="related_to_text">True</field>
            <field name="text">0.5</field>
            <field name="description">Ratio for fuzzy search similarity, the less it is the more flexible it is (Which means there will be more similar areas for the name sent) {Text value with value less than 1}</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_use_fuzzy_search" model="rb_delivery.client_configuration">
            <field name="key">use_fuzzy_search</field>
            <field name="value">False</field>
            <field name="description">Use fuzzy search in the system for checking values</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_user_to_send_emails" model="rb_delivery.client_configuration">
            <field name="key">user_to_send_emails</field>
            <field name="value">True</field>
            <field name="related_to_user_ids">True</field>
            <field name="description">Default user to send email</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_sender_address_in_waybill" model="rb_delivery.client_configuration">
            <field name="key">show_sender_address_in_waybill</field>
            <field name="value">True</field>
            <field name="description">Show/hide sender address in boolisa</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_sender_city_in_waybill" model="rb_delivery.client_configuration">
            <field name="key">show_sender_city_in_waybill</field>
            <field name="value">True</field>
            <field name="description">Show/hide sender city in boolisa</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_sender_mobile_in_waybill" model="rb_delivery.client_configuration">
            <field name="key">show_sender_mobile_in_waybill</field>
            <field name="value">True</field>
            <field name="description">Show or hide sender mobile number on Waybills</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_sender_name_in_run_sheet" model="rb_delivery.client_configuration">
            <field name="key">show_sender_name_in_run_sheet</field>
            <field name="value">True</field>
            <field name="description">Show/hide sender name in runsheet</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_return_waybill_in_api" model="rb_delivery.client_configuration">
            <field name="key">return_waybill_in_api</field>
            <field name="value">False</field>
            <field name="description">Return waybill A4 in create order API</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_return_return_waybill_in_get_order_details_api" model="rb_delivery.client_configuration">
            <field name="key">return_waybill_in_get_order_details_api</field>
            <field name="value">False</field>
            <field name="description">Return waybill in get order details "/order" API</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_waybill_in_api" model="rb_delivery.client_configuration">
            <field name="key">waybill_in_api</field>
            <field name="value">True</field>
            <field name="related_to_report">True</field>
            <field name="report_id" ref="rb_delivery.report_rb_delivery_order_detail_a4_action"/>
            <field name="description">Waybill that will be printed in the API</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_sender_details_in_waybill" model="rb_delivery.client_configuration">
            <field name="key">show_sender_details_in_waybill</field>
            <field name="value">True</field>
            <field name="description">Show or hide sender details on Waybills</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_normalize_mobile_number" model="rb_delivery.client_configuration">
            <field name="key">normalize_mobile_number</field>
            <field name="value">True</field>
            <field name="text">-, ,intro</field>
            <field name="related_to_text">True</field>
            <field name="description">Characters like(-, space, or .) for example needs be removed from mobile number, and any character needs to be removed can be added. (To remove country intro add intro)</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_add_zero_at_the_beginning_of_the_mobile_number" model="rb_delivery.client_configuration">
            <field name="key">add_zero_at_the_beginning_of_the_mobile_number</field>
            <field name="value">True</field>
            <field name="description">In normalizing mobile number if this configuration is set to true then it will add a zero to the beginning of the mobile number.</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_normalize_intros" model="rb_delivery.client_configuration">
            <field name="key">normalize_intros</field>
            <field name="value">True</field>
            <field name="text">972,970</field>
            <field name="related_to_text">True</field>
            <field name="description">Related to configuration normalize_mobile_number intros in this configuration will be removed from the mobile number, if intro is added in configuration normalize_mobile_number</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_normalize_reference_id" model="rb_delivery.client_configuration">
            <field name="key">normalize_reference_id</field>
            <field name="value">True</field>
            <field name="text">*</field>
            <field name="related_to_text">True</field>
            <field name="description">Characters like (*) for example needs be removed from reference id, and any character needs to be removed can be added, divided by ,.</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_allow_reference_id_accept_the_english_letter" model="rb_delivery.client_configuration">
            <field name="key">allow_reference_id_accept_the_english_letter</field>
            <field name="value">False</field>
            <field name="description">Allow reference ID accept the English letter</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_allowed_states_to_overwrite_orders" model="rb_delivery.client_configuration">
            <field name="key">allowed_states_to_overwrite_orders</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="description">Statuses allowed to override order with same reference ID</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_delivered_by_states" model="rb_delivery.client_configuration">
            <field name="key">delivered_by_states</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="description">When order is changed to a status from this configuration delivered by will be filled by agent of the order if there was an agent on the order.</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_follower_info_in_waybill" model="rb_delivery.client_configuration">
            <field name="key">show_follower_info_in_waybill</field>
            <field name="value">True</field>
            <field name="description">Show/Hide Follower information in waybills</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_service_visibility" model="rb_delivery.client_configuration">
            <field name="key">service_visibility</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">show/hide service field in order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_extra_service_on_sender_visibility" model="rb_delivery.client_configuration">
            <field name="key">extra_service_on_sender_visibility</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">show/hide extra service fee on sender field in order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_extra_service_on_customer_visibility" model="rb_delivery.client_configuration">
            <field name="key">extra_service_on_customer_visibility</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">show/hide extra service fee on customer field in order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_reference_id" model="rb_delivery.client_configuration">
            <field name="key">reference_id_top_priority</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">barcode will print reference_id</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_sequence" model="rb_delivery.client_configuration">
            <field name="key">sequence_top_priority</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">barcode will print sequence_id</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_replacement_order" model="rb_delivery.client_configuration">
            <field name="key">show_replacement_order</field>
            <field name="value">False</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">show/hide replacement order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_replacement_order_status" model="rb_delivery.client_configuration">
            <field name="key">replacement_order_status</field>
            <field name="platform_type">web_mobile</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting')])]" />
            <field name="description">The status the order should create a clone replacement order (Must Only Choose One Status!)</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_returned_order" model="rb_delivery.client_configuration">
            <field name="key">show_returned_order</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">show/hide returned order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_ability_to_auto_refresh" model="rb_delivery.client_configuration">
            <field name="key">ability_to_auto_refresh</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">auto refresh will be called in order if value was set to true</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_description_tags_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_description_tags_visibility</field>
            <field name="value">False</field>
            <field name="description">Show tags or hide it from business</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_reference_id_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_reference_id_visibility</field>
            <field name="value">True</field>
            <field name="platform_type">mobile</field>
            <field name="description">Show tags or hide it from business</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_create_driver_ability" model="rb_delivery.client_configuration">
            <field name="key">order_create_driver_ability</field>
            <field name="value">False</field>
            <field name="description">Give the driver the ability to create order or not, when use the bacode scan , if the order does not exist on the system ,and ther driver scan the order then it will do nothing and it should be in_branch_status</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_driver_detail_business_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_driver_detail_business_visibility</field>
            <field name="value">True</field>
            <field name="description">Show/hide driver detail for business ( just name )</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_business_detail_driver_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_business_detail_driver_visibility</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_returned_in_progress'),ref('rb_delivery.status_waiting'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_delivered'),ref('rb_delivery.status_rejected'),ref('rb_delivery.status_stuck'),ref('rb_delivery.status_reschedule')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="related_to_status">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="description">Show/hide business detail for driver from detail page in mobile (business area,address,name,mobile number)</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_control_confirmation_box_on_status_change" model="rb_delivery.client_configuration">
            <field name="key">control_confirmation_box_on_status_change</field>
            <field name="value">True</field>
            <field name="description">Show/hide confirmation box on status change for mobile</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_append_field_to_note" model="rb_delivery.client_configuration">
            <field name="key">append_field_to_note</field>
            <field name="value">True</field>
            <field name="related_to_text">False</field>
            <field name="related_to_field_ids">True</field>
            <field name="field_ids" eval="[(6,0,[])]" />
            <field name="model_id" ref="model_rb_delivery_order" />
            <field name="platform_type" >web</field>
            <field name="description">Selected field values will be appended to the run sheet report and concatenated in the note column </field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>


        <record id="client_configuration_setting_user_location_ability" model="rb_delivery.client_configuration">
            <field name="key">setting_user_location_ability</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
            <field name="description">The user ( tajer ) can update his location whenever he want on the setting page on mobile </field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_mobile_new_version_message" model="rb_delivery.client_configuration">
            <field name="key">mobile_new_version_message</field>
            <field name="value">True</field>
            <field name="text">THERE_IS_A_NEW_UPDATE</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">This message show on mobile if the mobile version not last update</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_business_can_edit_order_status" model="rb_delivery.client_configuration">
            <field name="key">business_can_edit_order_status</field>
            <field name="value">False</field>
            <field name="description">the statuses that allowed the business edit the order</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_send_chat_notification" model="rb_delivery.client_configuration">
            <field name="key">send_chat_notification</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business')])]" />
            <field name="description">Allow to send chat notification for this role</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_unable_to_export_data" model="rb_delivery.client_configuration">
            <field name="key">roles_who_can_not_export_data</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_sales')])]" />
            <field name="description">Roles who can not export data</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_register_user_location_ability" model="rb_delivery.client_configuration">
            <field name="key">register_user_location_ability</field>
            <field name="value">True</field>
            <field name="platform_type">mobile</field>
            <field name="description">send location when create new user</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_register"/>
        </record>

        <record id="client_configuration_order_business_location_driver_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_business_location_driver_visibility</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_returned_in_progress'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_waiting')])]" />
            <field name="platform_type" >mobile</field>
            <field name="description">Show/hide business location for any role ,will show button GPS once click will go to google map </field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_sender_and_customer_mobile_number_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_sender_and_customer_mobile_number_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">Show sender and Customer mobile number or hide it from order card</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_force_to_add_reason_of_reject_ability" model="rb_delivery.client_configuration">
            <field name="key">order_force_to_add_reason_of_reject_ability</field>
            <field name="value">False</field>
            <field name="description">Force to add reason of reject</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_extra_field_customer_village_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_extra_field_customer_village_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide extra field Customer Village </field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_extra_field_customer_neighbour_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_extra_field_customer_neighbour_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide extra field Customer Neighbour </field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_extra_field_customer_building_number_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_extra_field_customer_building_number_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide extra field Customer Building Number </field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_extra_field_no_of_items_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_extra_field_no_of_items_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide extra field number of items</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_commercial_number_for_driver_in_business_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_commercial_number_for_driver_in_business_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide  mobile number ( commercial number) for driver in the business</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_order_type_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_order_type_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide  Order Type </field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_picking_time_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_picking_time_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide Picking time</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_create_agent_collection_statuses" model="rb_delivery.client_configuration">
            <field name="key">create_agent_collection_statuses</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_received')])]" />
            <field name="related_to_status">True</field>
            <field name="description">When change order to one of these statuses it will create agent collection of these orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_check_agent_when_create_agent_collection" model="rb_delivery.client_configuration">
            <field name="key">check_agent_when_create_agent_collection</field>
            <field name="value">True</field>
            <field name="description">This configuration is responsible for validating the presence of an agent before allowing the creation of an order. It ensures that if no agent is associated with the order, the user will be prevented from proceeding with the order creation.

                True ➝ check the agent and show error prevent creating

                False➝ create agent collection with (undefined ) agent</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_create_agent_collection_with_employee" model="rb_delivery.client_configuration">
            <field name="key">create_agent_collection_with_employee</field>
            <field name="value">False</field>
            <field name="description">This configuration is responsible for creating with an employee if he changed the status to one of the statuses in this configuration create_agent_collection_statuses.
                True ➝ Will create with the employee that changed the status

                False➝ Create normally with the agent assigned to the order</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>


        <record id="client_configuration_order_show_sub_area_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_customer_sub_area_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide sub area</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_driver_assign_himself_on_order_ability" model="rb_delivery.client_configuration">
            <field name="key">order_driver_assign_himself_on_order_ability</field>
            <field name="value">False</field>
            <field name="description">Ability the driver can assign himself on order</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

         <record id="client_configuration_show_only_total_for_roles" model="rb_delivery.client_configuration">
            <field name="key">show_only_total_for_roles</field>
            <field name="value">False</field>
            <field name="related_to_group_ids">True</field>
            <field name="description">Show/Hide required from business and delivery fee from the roles selected</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_driver_assign_himself_on_order_status" model="rb_delivery.client_configuration">
            <field name="key">order_driver_assign_himself_to_order_status</field>
            <field name="value">False</field>
            <field name="description">the statuses that can the driver assign himself on order</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_roles_can_edit_order_status" model="rb_delivery.client_configuration">
            <field name="key">roles_can_edit_order_status</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role and status to show edit button or hide it from order item except for business you need to go to (business_can_edit_order_status) , also super_manager and manager are not affected</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_call_center')])]" />
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_item_money_collection_cost_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_money_collection_cost_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show money collection cost or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_register_social_media_visibility" model="rb_delivery.client_configuration">
            <field name="key">register_social_media_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/Hide Social media fields from register form</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_register"/>
        </record>

        <record id="client_configuration_order_item_required_from_business_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_required_from_business_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show required_from_business or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_use_default_sub_area_if_no_sub_area" model="rb_delivery.client_configuration">
            <field name="key">use_default_sub_area_if_no_sub_area</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="description">Use the default sub area in the business if there is no sub area added</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_item_delivery_cost_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_delivery_cost_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show delivery_cost or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_item_delivery_cost_on_sender_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_delivery_cost_on_sender_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show delivery cost on sender or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_item_package_cost_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_package_cost_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">select role to show package cost or hide it from order item</field>
            <field name="related_to_group_ids">True</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_segment_tasks_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_segment_tasks_visibility</field>
            <field name="value">False</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_picking_up')])]" />
            <field name="platform_type">mobile</field>
            <field name="description">Show/hide tasks from segment and we can select role and state</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_states_to_allow_agent_cost_recalculate" model="rb_delivery.client_configuration">
            <field name="key">states_to_allow_agent_cost_recalculate</field>
            <field name="value">False</field>
            <field name="description">the statuses that agent cost will not be recalculated</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered'),ref('rb_delivery.status_in_progress')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_assign_money_collector" model="rb_delivery.client_configuration">
            <field name="key">assign_money_collector</field>
            <field name="value">False</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_received')])]" />
            <field name="platform_type">web</field>
            <field name="description">Assign to money collector if order was in status {status}</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_prevent_detach_order_if_in_status" model="rb_delivery.client_configuration">
            <field name="key">prevent_detach_order_from_collection</field>
            <field name="value">False</field>
            <field name="description">Prevent detach order from collection if in one of statuses</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_prevent_detach_order_if_in_status_agent" model="rb_delivery.client_configuration">
            <field name="key">prevent_detach_order_from_agent_collection</field>
            <field name="value">False</field>
            <field name="description">Prevent detach order from agent collection if in one of statuses</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_received')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_item_order_type_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_order_type_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide  Order Type in order card </field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <!-- Financial configuration-->
        <record id="client_configuration_order_delivery_fee_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_delivery_fee_status_sum</field>
            <field name="value">True</field>
            <field name="description">sum all the delivery fee in specific statuses for admin</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received')])]" />
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>


        <record id="client_configuration_order_delivery_cost_driver_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_delivery_cost_driver_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/Hide the delivery cost for driver</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_delivery_cost_on_sender_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_delivery_cost_on_sender_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide cost on sender checkbox</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_delivery_cost_on_customer" model="rb_delivery.client_configuration">
            <field name="key">order_delivery_cost_on_customer</field>
            <field name="value">True</field>
            <field name="description">Delivery cost is on customer by default if this is True other wise it is False {True/False Boolean}</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_user_inclusive_ability" model="rb_delivery.client_configuration">
            <field name="key">user_creation_inclusive_ability</field>
            <field name="value">True</field>
            <field name="description">when create new user , should be inclusive/noninclusive</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_dashboard_collections_visibility" model="rb_delivery.client_configuration">
            <field name="key">dashboard_collections_visibility</field>
            <field name="value">True</field>
            <field name="description">in dashboard show/hide two box for business</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_required_from_business_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_required_from_business_status_sum</field>
            <field name="value">True</field>
            <field name="description">the statuses that sum up to result the required for business</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received')])]" />
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

<record id="client_configuration_order_inclusive_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_inclusive_visibility</field>
            <field name="value">False</field>
            <field name="description">show/hide the checbox for inclusive order in the order it self</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_money_collection_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_money_collection_status_sum</field>
            <field name="value">True</field>
            <field name="description">the statuses that sum up to result in the money collection in company</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_all_required_from_business_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_all_required_from_business_status_sum</field>
            <field name="value">True</field>
            <field name="description">the statuses that sum up to result money required for business in all statuses</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_in_progress'), ref('rb_delivery.status_delivered'), ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received') ])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_money_collection_with_driver_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_money_collection_with_driver_status_sum</field>
            <field name="value">True</field>
            <field name="description">the statuses that sum up to show the money with driver</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]"/>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_show_business_contact_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_business_contact_visibility</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">Show/hide business contact from order card</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_dashboard_driver_profit_visibility" model="rb_delivery.client_configuration">
            <field name="key">dashboard_driver_profit_visibility</field>
            <field name="value">False</field>
            <field name="description">in dashboard show/hide driver profit box for driver</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_dashboard_company_profit_visibility" model="rb_delivery.client_configuration">
            <field name="key">dashboard_company_profit_visibility</field>
            <field name="value">False</field>
            <field name="description">in dashboard show/hide company profit box</field>
            <field name="related_to_status">False</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_order_profit_for_driver_status_sum" model="rb_delivery.client_configuration">
            <field name="key">order_profit_for_driver_status_sum</field>
            <field name="value">True</field>
            <field name="description">the statuses that sum up to show the profit for driver</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]"/>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>


        <!-- Register configuration-->

        <record id="client_configuration_register_show_customer_sub_area_visibility" model="rb_delivery.client_configuration">
            <field name="key">register_show_customer_sub_area_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide sub area</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_register"/>
        </record>

        <record id="client_configuration_order_group_by_business_for_driver_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_group_by_business_for_driver_visibility</field>
            <field name="value">True</field>
            <field name="description">Show/hide group by business for driver  in mobile (business area,address,name)</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_select_customer_from_contacts_ability" model="rb_delivery.client_configuration">
            <field name="key">order_select_customer_from_contacts_ability</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">give the users ability to select Customer from contacts on mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_order_show_select_business_when_create_order_ability" model="rb_delivery.client_configuration">
            <field name="key">order_show_select_business_when_create_order_ability</field>
            <field name="value">True</field>
            <field name="platform_type">mobile</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_data_entry')])]" />
            <field name="description">give the user ability to select business in the order creation on mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_select_driver_when_create_order_ability" model="rb_delivery.client_configuration">
            <field name="key">order_show_select_driver_when_create_order_ability</field>
            <field name="value">True</field>
            <field name="platform_type">mobile</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="description">give the user ability to select driver in the order creation on mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_show_delivery_time_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_show_delivery_time_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide Delivery time</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_make_the_cod_required_field" model="rb_delivery.client_configuration">
            <field name="key">order_make_the_cod_required_field</field>
            <field name="value">True</field>
            <field name="description">Make the COD required field or not</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_use_qr_code" model="rb_delivery.client_configuration">
            <field name="key">use_qr_code</field>
            <field name="value">False</field>
            <field name="description">Use QR code instead of barcode in all system</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_show_online_in_dashboard_visibility" model="rb_delivery.client_configuration">
            <field name="key">show_online_in_dashboard_visibility</field>
            <field name="value">False</field>
            <field name="description">Show/hide Online/Offline from the dashboard or not and from web</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_order_choose_payment_type" model="rb_delivery.client_configuration">
            <field name="key">order_choose_payment_type</field>
            <field name="value">False</field>
            <field name="description">Allows the payment type to appear on selected group when create order</field>
            <field name="related_to_group_ids">True</field>
            <field name="group_ids" />
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_payment_type"/>
        </record>

        <record id="client_configuration_order_item_payment_type_visibility" model="rb_delivery.client_configuration">
            <field name="key">order_item_payment_type_visibility</field>
            <field name="value">False</field>
            <field name="description">Allows the payment type to appear on order card for all users</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_payment_type"/>
        </record>

        <record id="client_configuration_setting_deactivate_user_visibility" model="rb_delivery.client_configuration">
            <field name="key">setting_deactivate_user_visibility</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >mobile</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business')])]" />
            <field name="description">The user can deactivate his account whenever he want on the setting page on mobile {role} </field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_show_country" model="rb_delivery.client_configuration">
            <field name="key">show_country</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="description">Show/hide country field</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_order_add_call_logs_in_the_order_chat" model="rb_delivery.client_configuration">
            <field name="key">order_add_call_logs_in_the_order_chat</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="description">add call logs in the order chat</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_global_dicimal_value" model="rb_delivery.client_configuration">
         <field name="key">global_dicimal_value</field>
         <field name="value">True</field>
         <field name="description">Default dicimal value ammount for all fields.</field>
         <field name="related_to_text">True</field>
         <field name="text">2</field>
         <field name="platform_type">web</field>
         <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_dicimal"/>
      </record>

        <record id="client_configuration_google_map_key" model="rb_delivery.client_configuration">
            <field name="key">google_map_key</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">AIzaSyDhUaZ2VNwuDPK-6covuHQMQ_WkDh5dArQ</field>
            <field name="platform_type">web</field>
            <field name="description">key of google map</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_google_map_key_for_server" model="rb_delivery.client_configuration">
            <field name="key">google_map_key_for_server</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">AIzaSyB1xQKhumZoJs0Mijs5z8jPt6ar5TDLaOc</field>
            <field name="platform_type">web</field>
            <field name="description">key of google map 'For Server Requests'</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>
        
        <record id="client_configuration_extract_regex_from_scanner" model="rb_delivery.client_configuration">
            <field name="key">extract_regex_from_scanner</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="platform_type">web_mobile</field>
            <field name="description">extract regex from scanner</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_convert_coordinates_to_locality" model="rb_delivery.client_configuration">
            <field name="key">convert_coordinates_to_locality</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="description">get customer area and sub area by longitude and latitude ('customer address' and 'customer area' and 'customer sub area' should not be in the values)</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_use_default_area_as_fallback" model="rb_delivery.client_configuration">
            <field name="key">use_default_area_as_fallback</field>
            <field name="value">True</field>
            <field name="platform_type">web</field>
            <field name="description">set the default area for the order when using convert_coordinates_to_locality and the area not found in the system.&#13; if dectivated an error will be raised if the area not found, else the default area will be set with a warning shown to the user.&#13; ** note that if no default area set in the system this configuration will not be taken and an error will be raised either way</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_determine_web_platform_login" model="rb_delivery.client_configuration">
            <field name="key">determine_web_platform_login</field>
            <field name="value">False</field>
            <field name="description">Prevent these roles from logging in on the web platform.</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_determine_mobile_platform_login" model="rb_delivery.client_configuration">
            <field name="key">determine_mobile_platform_login</field>
            <field name="value">False</field>
            <field name="description">Prevent these roles from logging in on the mobile platform.</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>


        <record id="client_configuration_mobile_ar_translation_file" model="rb_delivery.client_configuration">
            <field name="key">mobile_ar_translation_file</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">This to upload mobile ar translation file </field>
            <field file="rb_delivery/static/mobile_translation/ar.json" type="base64" name="attachment"></field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_mobile_fr_translation_file" model="rb_delivery.client_configuration">
            <field name="key">mobile_fr_translation_file</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">This to upload mobile french translation file </field>
            <field file="rb_delivery/static/mobile_translation/fr.json" type="base64" name="attachment"></field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_mobile_en_translation_file" model="rb_delivery.client_configuration">
            <field name="key">mobile_en_translation_file</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">This to upload mobile en translation file </field>
            <field file="rb_delivery/static/mobile_translation/en.json" type="base64" name="attachment"></field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_mobile_he_translation_file" model="rb_delivery.client_configuration">
            <field name="key">mobile_he_translation_file</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">This to upload mobile he translation file </field>
            <field file="rb_delivery/static/mobile_translation/he.json" type="base64" name="attachment"></field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_ability_to_edit_collection_status" model="rb_delivery.client_configuration">
            <field name="key">ability_to_edit_collection_status</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="description">Collection status should be in this status to give user ability to edit in collection's orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection" />
        </record>

        <record id="client_configuration_ability_to_reflect_pre_paid_collection_status_to_order" model="rb_delivery.client_configuration">
            <field name="key">ability_to_reflect_pre_paid_collection_status_to_order</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_collection_completed')])]" />
            <field name="description">Ability to reflect prepaid collection status to orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection" />
        </record>

        <record id="client_configuration_user_location_required" model="rb_delivery.client_configuration">
            <field name="key">user_location_required</field>
            <field name="value">False</field>
            <field name="platform_type">mobile</field>
            <field name="description">Whether to make user location required in mobile registration {check value}</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_register"/>
        </record>

   <record id="client_configuration_orders_qweb_limit" model="rb_delivery.client_configuration">
        <field name="key">orders_qweb_limit</field>
        <field name="value">True</field>
        <field name="text">50</field>
        <field name="description">Number of splits for Orders QWeb reports</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>
    <record id="client_configuration_collections_qweb_limit" model="rb_delivery.client_configuration">
        <field name="key">collections_qweb_limit</field>
        <field name="value">True</field>
        <field name="text">30</field>
        <field name="description">Number of splits for collections QWeb reports</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
    </record>

    <record id="client_configuration_orders_print_limit_then_job_queue" model="rb_delivery.client_configuration">
        <field name="key">orders_print_limit_then_job_queue</field>
        <field name="value">True</field>
        <field name="related_to_text">True</field>
        <field name="platform_type">web</field>
        <field name="text">50</field>
        <field name="description">The number of orders that will be printed, larger number of orders will be printed in the job queue, recommended is 50 orders</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_collections_print_limit_then_job_queue" model="rb_delivery.client_configuration">
        <field name="key">collections_print_limit_then_job_queue</field>
        <field name="value">True</field>
        <field name="text">10</field>
        <field name="platform_type">web</field>
        <field name="related_to_text">True</field>
        <field name="description">The number of collections that will be printed, larger number of collections will be printed in the job queue, recommended is 10 collections</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_display_district_visibility" model="rb_delivery.client_configuration">
        <field name="key">display_district_field</field>
        <field name="value">False</field>
        <field name="description">Toggle to display the district field on order forms (web) and order detail pages (mobile).</field>
        <field name="platform_type">web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_consider_clone_when_get_dashboard_item" model="rb_delivery.client_configuration">
        <field name="key">consider_clone_when_get_dashboard_item</field>
        <field name="value">True</field>
        <field name="description">This configuration specifies whether to include or exclude cloned orders when retrieving dashboard items, even if the status of the cloned orders changes to 'in-progress' for example or another status .</field>
        <field name="platform_type">web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_register_business_work_category_visibility" model="rb_delivery.client_configuration">
        <field name="key">register_business_work_category_visibility</field>
        <field name="value">False</field>
        <field name="description">Show/Hide Business work category field from register form</field>
        <field name="platform_type">web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_register"/>
    </record>

    <record id="client_configuration_prevent_preview_totals_on_groupby" model="rb_delivery.client_configuration">
        <field name="key">prevent_preview_totals_on_groupby</field>
        <field name="value">True</field>
        <field name="platform_type">web</field>
        <field name="related_to_group_ids">True</field>
        <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_call_center')])]" />
        <field name="description">Prevent roles that included in this configuration from seeing totals in order tree</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>
    <record id="client_configuration_print_icon_visibility" model="rb_delivery.client_configuration">
        <field name="key">print_icon_in_collection_visibility</field>
        <field name="value">True</field>
        <field name="related_to_group_ids">True</field>
        <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_driver')])]" />
        <field name="platform_type" >mobile</field>
        <field name="description">Any user role included in this configuration will not see print icon in collection page on mobile </field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_collection"/>
    </record>

    <record id="client_configuration_archive_order_statuses" model="rb_delivery.client_configuration">
        <field name="key">archive_order_statuses</field>
        <field name="value">True</field>
        <field name="related_to_status">True</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_deleted'),ref('rb_delivery.status_canceled')])]" />
        <field name="platform_type" >web</field>
        <field name="description">Any status included will not be considered when search in active orders
            for example if you want to consider deleted order while typing reference ID or searching in orders in web barcode page just remove it
        </field>

        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_make_tracking_link_expired_on_status" model="rb_delivery.client_configuration">
        <field name="key">make_tracking_link_expired_on_status</field>
        <field name="value">False</field>
        <field name="description">Makes the tracking driver link expired on these statuses</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]" />
        <field name="related_to_status">True</field>
        <field name="platform_type" >web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_track_driver_link_type"/>
    </record>

    <record id="client_configuration_tracking_link_expiry_time" model="rb_delivery.client_configuration">
        <field name="key">tracking_link_expiry_time</field>
        <field name="value">True</field>
        <field name="description">The time for the tracking link to be expired (Hours).</field>
        <field name="related_to_text">True</field>
        <field name="text">24</field>
        <field name="platform_type">web</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_track_driver_link_type"/>
    </record>
    <record id="client_configuration_enable_scan_reference_id" model="rb_delivery.client_configuration">
        <field name="key">enable_scan_reference_id</field>
        <field name="value">False</field>
        <field name="description">Enable or disable the feature to scan the reference ID using a barcode or QR code after submit the order details in appx</field>
        <field name="platform_type">mobile</field>
        <field name="group_ids" eval="[(6, 0, [ref('rb_delivery.role_business')])]" />
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_default_status_if_business_has_orders" model="rb_delivery.client_configuration">
        <field name="key">default_statuses_to_be_excluded_on_check_for_archive</field>
        <field name="value">True</field>
        <field name="description">Business's order statuses that are excluded when archiving the business</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_completed'),ref('rb_delivery.status_deleted'),ref('rb_delivery.status_canceled'), ref('rb_delivery.status_completed_returned')])]" />
        <field name="related_to_status">True</field>
        <field name="platform_type" >web</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

    <record id="client_configuration_ability_to_hide_paid_field" model="rb_delivery.client_configuration">
        <field name="key">ability_to_hide_paid_field_in_creating_order</field>
        <field name="value">False</field>
        <field name="platform_type" >web</field>
        <field name="description">Ability to hide paid button check box in creating order inside cost </field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_accept_minus_value_in_cod" model="rb_delivery.client_configuration">
        <field name="key">accept_minus_value_in_cod</field>
        <field name="value">True</field>
        <field name="description">This configuration accepts minus values for cod fields (Total cost, Package cost) if is set to True.</field>
        <field name="platform_type">web</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_primary_color_in_mobile" model="rb_delivery.client_configuration">
        <field name="key">primary_color_in_mobile</field>
        <field name="value">True</field>
        <field name="text">#B37300</field>
        <field name="related_to_text">True</field>
        <field name="platform_type" >mobile</field>
        <field name="description">Primary color in appx</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

    <record id="client_configuration_secondary_color_in_mobile" model="rb_delivery.client_configuration">
        <field name="key">secondary_color_in_mobile</field>
        <field name="value">True</field>
        <field name="text">#ECE2D1</field>
        <field name="related_to_text">True</field>
        <field name="platform_type" >mobile</field>
        <field name="description">econdary color in appx</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

    <record id="client_configuration_mobile_card_icon_colors" model="rb_delivery.client_configuration">
        <field name="key">mobile_card_icon_colors</field>
        <field name="value">True</field>
        <field name="text">#b88b11</field>
        <field name="related_to_text">True</field>
        <field name="platform_type" >mobile</field>
        <field name="description">Card icon colors mobile Appx</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

    <record id="client_configuration_mobile_main_icons_color" model="rb_delivery.client_configuration">
        <field name="key">mobile_main_icons_color</field>
        <field name="value">True</field>
        <field name="text"></field>
        <field name="related_to_text">True</field>
        <field name="platform_type" >mobile</field>
        <field name="description">Mobile main icons color appx</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

    <record id="client_configuration_statuses_needed_to_get_money_collection_sum_for_them" model="rb_delivery.client_configuration">
        <field name="key">statuses_needed_to_get_money_collection_sum_for_them</field>
        <field name="value">True</field>
        <field name="description">this configuration should include the statuses that need to get the sum of money collection individually for each status in the mobile dashboard</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_branch'),
        ref('rb_delivery.status_in_progress'), ref('rb_delivery.status_delivered'),
         ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received'),ref('rb_delivery.status_waiting'),ref('rb_delivery.status_rejected'),
         ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck')])]" />
        <field name="related_to_status">True</field>
        <field name="platform_type" >web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
    </record>
    <record id="client_configuration_statuses_to_be_considered_in_today_orders" model="rb_delivery.client_configuration">
        <field name="key">statuses_to_be_considered_in_today_orders</field>
        <field name="value">True</field>
        <field name="description">this configuration should include the statuses that will be considered in today order in the mobile dashboard</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_branch'),
        ref('rb_delivery.status_in_progress'), ref('rb_delivery.status_delivered'),
         ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received'),ref('rb_delivery.status_waiting'),ref('rb_delivery.status_rejected'),
         ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck')])]" />
        <field name="related_to_status">True</field>
        <field name="platform_type" >web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
    </record>



    <record id="client_configuration_ability_to_create_pre_paid_collection" model="rb_delivery.client_configuration">
        <field name="key">ability_to_create_pre_paid_collection</field>
        <field name="value">True</field>
        <field name="related_to_status">True</field>
        <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_received')])]" />
        <field name="platform_type" >web</field>
        <field name="description">This configuration is related to status and orders that have the same status that includes in this configuration
            will be able to create pre-paid collection otherwise the system will prevent the user
        </field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
    </record>

    <record id="client_configuration_mobile_languages_visibility_setting_Appx" model="rb_delivery.client_configuration">
        <field name="key">mobile_languages_visibility_setting_Appx</field>
        <field name="value">True</field>
        <field name="description">mobile languages should be like ( en_US,ar_SY ) hint : {'English':'en_US','Hebrew':'he_il','العربية':'ar_SY'}</field>
        <field name="text">[{"language":"ar_SY","value":"ar","label":"ARABIC"},{"language":"en_US","value":"en","label":"ENGLISH"}]</field>
        <field name="related_to_text">True</field>
        <field name="platform_type">web_mobile</field>
        <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
    </record>

     <record id="client_configuration_allowed_states_to_change_pre_paid_order_status" model="rb_delivery.client_configuration">
            <field name="key">allowed_states_to_change_pre_paid_order_status</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_money_in'),ref('rb_delivery.status_money_received'),ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="description">This configuration restrict users from changing the state of orders in a prepaid collection to only those states specified in the configuration</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_defualt_status_for_pre_paid_collection" model="rb_delivery.client_configuration">
            <field name="key">defualt_status_for_pre_paid_collection</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_collection_money_in')])]" />
            <field name="related_to_status">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="description">This configuration is related to the default status for prepaid collection, only add one status</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_statuses_to_be_considered_in_plankton_orders" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_be_considered_in_plankton_orders</field>
            <field name="value">True</field>
            <field name="description">this configuration should include the statuses that will be considered in plankton order in the mobile dashboard</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_rejected'),ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_financial"/>
        </record>

        <record id="client_configuration_client_use_background_location_on_mobile" model="rb_delivery.client_configuration">
            <field name="key">client_use_background_location_on_mobile</field>
            <field name="value">False</field>
            <field name="platform_type" >mobile</field>
            <field name="description">This setting activates the background location feature on mobile devices, allowing continuous tracking of device location even when the application is running in the background.</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_enable_request_collection_for_all_businesses" model="rb_delivery.client_configuration">
            <field name="key">enable_request_collection_for_all_businesses</field>
            <field name="value">False</field>
            <field name="platform_type" >mobile</field>
            <field name="description">This setting activates the request collection feature on mobile devices, allowing all users with role business to request collection through mobile. this configuration disable the functionallty that is inside feature tabe in users page, so even if the button set true or false the request collection button will appear if this configuration set true</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_calculate_commission_value" model="rb_delivery.client_configuration">
            <field name="key">calculate_commission_value</field>
            <field name="value">False</field>
            <field name="description">When the delivery order status transitions to any designated status within the specified configuration, the commission calculation mechanism will be triggered.</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_enable_or_disable_follow_orders" model="rb_delivery.client_configuration">
            <field name="key">enable_or_disable_follow_orders</field>
            <field name="value">True</field>
            <field name="platform_type" >mobile</field>
            <field name="description">This setting is for enabling or disabling follow orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_field_to_be_empty_after_create_quick_order" model="rb_delivery.client_configuration">
            <field name="key">field_to_be_empty_after_create_quick_order</field>
            <field name="value">False</field>
            <field name="related_to_field_ids">True</field>
            <field name="model_id" ref="rb_delivery.model_rb_delivery_quick_order" />
            <field name="field_ids" eval="[(6,0,[ref('rb_delivery.field_rb_delivery_quick_order__customer_name'),ref('rb_delivery.field_rb_delivery_quick_order__reference_id'),
            ref('rb_delivery.field_rb_delivery_quick_order__customer_address'),ref('rb_delivery.field_rb_delivery_quick_order__customer_mobile'),
            ref('rb_delivery.field_rb_delivery_quick_order__note'),ref('rb_delivery.field_rb_delivery_quick_order__cost'),
            ref('rb_delivery.field_rb_delivery_quick_order__agent'),ref('rb_delivery.field_rb_delivery_quick_order__customer_second_mobile'),
            ref('rb_delivery.field_rb_delivery_quick_order__description_tags'),ref('rb_delivery.field_rb_delivery_quick_order__customer_sub_area'),
            ref('rb_delivery.field_rb_delivery_quick_order__customer_area')])]" />
            <field name="platform_type" >web</field>
            <field name="description">Any field included in this configuration will be empty after creating quick order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_quick_order" />
        </record>

          <record id="client_configuration_Delivery_english_msg" model="rb_delivery.client_configuration">
            <field name="key">A4_Waybill_english</field>
            <field name="value">True</field>
            <field name="text">I the undersigned acknowledge receipt of my baggage that was mishandled duringmy trip as shown in the above mentioned file. The baggage was delivered to me in good condition and without any signs of damage or pilferage.</field>
            <field name="related_to_text">True</field>
            <field name="description">Use this field to edit the English text so it appear in the A4 waybill</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_Delivery_arabic_msg" model="rb_delivery.client_configuration">
            <field name="key">A4_Waybill_arabic</field>
            <field name="value">True</field>
            <field name="text">انا الموقع ادناه اقر باستلام امتعتي التي فقدت اثناء رحلتي المدونة تفاصيلها في الملف المذكور اعلاه وانها بحالة جيدة وبدون اية علامات تلف او نقصان</field>
            <field name="related_to_text">True</field>
            <field name="description">Use this field to edit the Arabic text so it appear in the A4 waybill</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_allowed_statuses_for_pickup_order" model="rb_delivery.client_configuration">
            <field name="key">allowed_statuses_for_pickup_order</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up')])]" />
            <field name="related_to_status">True</field>
            <field name="description">Statuses that allow drivers to pick up order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_statuses_to_change_to_in_branch_when_sort" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_change_to_in_branch_when_sort</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_picking_up')])]" />
            <field name="related_to_status">True</field>
            <field name="description">Statuses that will change the order status to in branch when sort the orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_sort_statuses" model="rb_delivery.client_configuration">
            <field name="key">sort_statuses</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_branch')])]" />
            <field name="related_to_status">True</field>
            <field name="description">The status that will be changed to when sort the orders in appex</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_picked_up_status" model="rb_delivery.client_configuration">
            <field name="key">picked_up_status</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picked_up')])]" />
            <field name="related_to_status">True</field>
            <field name="description">Picked up status for driver when he pickes up a page, please add only one status as if you add multiple we will take the first one</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_allow_change_status_if_area_is_default_and_delivery_cost_zero" model="rb_delivery.client_configuration">
            <field name="key">allow_change_status_if_area_is_default_and_delivery_cost_zero</field>
            <field name="value">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_canceled'),ref('rb_delivery.status_deleted')])]" />
            <field name="related_to_status">True</field>
            <field name="description">Allow user to change status if the delivery cost is equal to zero and the area is default</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_ability_to_create_replacement_order_when_change_status_appx" model="rb_delivery.client_configuration">
            <field name="key">ability_to_create_replacement_order_when_change_status_appx</field>
            <field name="value">True</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Enables the automatic creation of a replacement order when an order's status changes to 'delivered', if only one order is affected and it has an associated replacement order. This setting is configurable and should be activated depending on business requirements.</field>
        </record>
        <record id="client_configuration_replace_barcode_with_qr_code_in_a5_waybill" model="rb_delivery.client_configuration">
            <field name="key">replace_barcode_with_qr_code_in_a5_waybill</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Replaces the barcode in the A5 waybill with a QR code. This setting is configurable and should be activated depending on business requirements.</field>
        </record>
        <record id="client_configuration_show_order_creation_date_in_money_collection" model="rb_delivery.client_configuration">
            <field name="key">show_order_creation_date_in_money_collection</field>
            <field name="value">False</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Show Order Creation Data in a new column in Money Collection Report</field>
        </record>
        <record id="client_configuration_use_defualt_order_type_price_list_when_there_is_no_pricelist" model="rb_delivery.client_configuration">
            <field name="key">use_defualt_order_type_price_list_when_there_is_no_pricelist</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Use defualt order type pricelist when there is no pricelist for the current order type.</field>
        </record>
        <record id="client_configuration_take_default_pricelist_if_no_pricing_item" model="rb_delivery.client_configuration">
            <field name="key">take_default_pricelist_if_no_pricing_item</field>
            <field name="value">False</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Use default pricelist when there is no pricelist for the current order details.</field>
        </record>
        <record id="client_configuration_prevent_adding_order_no_pricelist" model="rb_delivery.client_configuration">
            <field name="key">prevent_adding_order_no_pricelist</field>
            <field name="value">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
            <field name="description">Raise validation error if there is no pricing (delivery fee) for the current order details.</field>
        </record>
        <record id="client_configuration_ability_to_update_collection_on_deferent_status" model="rb_delivery.client_configuration">
            <field name="key">ability_to_update_collection_on_deferent_status</field>
            <field name="value">False</field>
            <field name="description">If this is enabled then you will have access to add orders to collection even if it has deferent status than the orders inside the collection</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_ability_to_update_returned_collection_on_different_status" model="rb_delivery.client_configuration">
            <field name="key">ability_to_update_returned_collection_on_different_status</field>
            <field name="value">False</field>
            <field name="description">If this is enabled then you will have access to add orders to returned collection even if it has different status than the orders inside the returned collection {True/False}</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_ability_to_update_agent_collection_on_different_status" model="rb_delivery.client_configuration">
            <field name="key">ability_to_update_agent_collection_on_different_status</field>
            <field name="value">False</field>
            <field name="description">If this is enabled then you will have access to add orders to agent collection even if it has different status than the orders inside the agent collection {True/False}</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_ability_to_update_returned_agent_collection_on_different_status" model="rb_delivery.client_configuration">
            <field name="key">ability_to_update_returned_agent_collection_on_different_status</field>
            <field name="value">False</field>
            <field name="description">If this is enabled then you will have access to add orders to returned agent collection even if it has different status than the orders inside the returned agent collection {True/False}</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_allow_reschedule_date_to_be_same_as_today_date" model="rb_delivery.client_configuration">
            <field name="key">allow_reschedule_date_to_be_same_as_today_date</field>
            <field name="value">False</field>
            <field name="description">If this is enabled then users will have the ability to add reschedule date same today's date {True/False}</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_fields_to_log_in_order_logs" model="rb_delivery.client_configuration">
            <!-- log_fields = ['state','stuck_comment','assign_to_agent','note','reject_reason'] -->
            <field name="key">fields_to_log_in_order_logs</field>
            <field name="value">True</field>
            <field name="related_to_field_ids">True</field>
            <field name="field_ids" eval="[(6,0,[
            ref('rb_delivery.field_rb_delivery_order__state_id'),
            ref('rb_delivery.field_rb_delivery_order__stuck_comment'),
            ref('rb_delivery.field_rb_delivery_order__assign_to_agent'),
            ref('rb_delivery.field_rb_delivery_order__note'),
            ref('rb_delivery.field_rb_delivery_order__reject_reason'),
            ref('rb_delivery.field_rb_delivery_order__solve_stuck_comment'),
            ref('rb_delivery.field_rb_delivery_order__cost'),
            ref('rb_delivery.field_rb_delivery_order__copy_total_cost'),
            ref('rb_delivery.field_rb_delivery_order__delivery_cost'),
            ref('rb_delivery.field_rb_delivery_order__customer_payment'),
            ref('rb_delivery.field_rb_delivery_order__required_from_business'),
            ref('rb_delivery.field_rb_delivery_order__money_collection_cost'),
            ])]" />
            <field name="model_id" ref="model_rb_delivery_order" />
            <field name="platform_type">web</field>
            <field name="description">Fields that will be logged in order logs{fields}</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_default_status_financial_order_logs" model="rb_delivery.client_configuration">
            <field name="key">default_status_financial_order_logs</field>
            <field name="value">False</field>
            <field name="description">This will show order cost logs that have been changed in these statuses</field>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>
        
        <record id="client_configuration_mobile_splash_screen_color" model="rb_delivery.client_configuration">
            <field name="key">mobile_splash_screen_color</field>
            <field name="value">True</field>
            <field name="text">#FFFFFF</field>
            <field name="related_to_text">True</field>
            <field name="platform_type" >mobile</field>
            <field name="description">Mobile Splash Screen in appx</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>
        <record id="client_configuration_mobile_new_splash_screen" model="rb_delivery.client_configuration">
            <field name="key">mobile_new_splash_screen</field>
            <field name="value">False</field>
            <field name="description">This configuraion is to enable or disable the new splash screen</field>
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>
        <record id="client_configuration_mobile_splash_full_screen" model="rb_delivery.client_configuration">
            <field name="key">mobile_splash_full_screen</field>
            <field name="value">False</field>
            <field name="description">This configuraion is to enable or disable full screen for the new splash screen</field>
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_default_status_to_scan_follow_order_when_change_status" model="rb_delivery.client_configuration">
            <field name="key">default_status_to_scan_follow_order_when_change_status</field>
            <field name="value">True</field>
            <field name="description">Statuses to scan follow orders when change status</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_delivered')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_roles_who_should_scan_follow_orders" model="rb_delivery.client_configuration">
            <field name="key">roles_who_should_scan_follow_orders</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="description">Roles Who Should scan follow orders when change status</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_scan_new_reference_id_when_clone" model="rb_delivery.client_configuration">
            <field name="key">scan_new_reference_id_when_clone</field>
            <field name="value">True</field>
            <field name="description">This configuraion is to enable or disable scanning new reference id when cloned order being created</field>
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_statuses_to_allow_pickup_agent_cost_recalculate" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_allow_pickup_agent_cost_recalculate</field>
            <field name="value">False</field>
            <field name="description">the statuses that pickup agent cost will be recalculated</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_statuses_to_set_value_for_picked_up_by" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_set_value_for_picked_up_by</field>
            <field name="value">False</field>
            <field name="description">the statuses that set value for picked up by</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_statuses_to_skip_scan_validation" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_skip_scan_validation</field>
            <field name="value">False</field>
            <field name="description">This configuration is to allow to change status of an order when scan through barcode for specific statuses even if the user doesn't have access to the order</field>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_from_statuses_to_skip_scan_validation" model="rb_delivery.client_configuration">
            <field name="key">to_statuses_to_skip_scan_validation</field>
            <field name="value">False</field>
            <field name="description">This configuration allows changing the status of an order when scanned via barcode to specific statuses, even if the user does not have the required access rights to change to those statuses.</field>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_driver_statuses_to_skip_scan_validation" model="rb_delivery.client_configuration">
            <field name="key">driver_statuses_to_skip_scan_validation</field>
            <field name="value">False</field>
            <field name="description">Driver Statuses to skip validation when scan orders to change its status</field>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_auto_create_runsheet_collection_statuses" model="rb_delivery.client_configuration">
            <field name="key">auto_create_runsheet_collection_statuses</field>
            <field name="value">True</field>
            <field name="status"/>
            <field name="related_to_status">True</field>
            <field name="description">When change order to one of these statuses it will create runsheet collection of these orders</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_status_to_allow_complete_prepaid_collection" model="rb_delivery.client_configuration">
            <field name="key">status_to_allow_complete_prepaid_collection</field>
            <field name="value">False</field>
            <field name="description">Statuses that orders should be to change the prepaid collection to completed</field>
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_paid'), ref('rb_delivery.status_completed')])]" />
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_number_of_records_to_be_loaded_at_the_same_time" model="rb_delivery.client_configuration">
            <field name="key">number_of_records_to_be_loaded_at_the_same_time</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="platform_type">web</field>
            <field name="text">1000</field>
            <field name="description">The max number of records to be loaded in any table/tree view in the system</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_statuses_for_public_orders_limit" model="rb_delivery.client_configuration">
            <field name="key">statuses_for_public_orders_limit</field>
            <field name="value">False</field>
            <field name="description">the statuses of the orders to check when creating public order to check if the public order is allwed to be created or not, this configuration works alongest with (public_orders_limit_for_same_mobile_per_day and public_orders_limit_in_general_per_day) so when there is orders found in theses statuses it will prevent creating the order if the limit reached.</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch')])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_public_orders_limit_for_same_mobile_per_day" model="rb_delivery.client_configuration">
            <field name="key">public_orders_limit_for_same_mobile_per_day</field>
            <field name="text">3</field>
            <field name="related_to_text">True</field>
            <field name="description">Number of orders allowed to be created for same mobile number within 24 hours with statuses_for_public_orders_limit configuration</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_public_orders_limit_in_general_per_day" model="rb_delivery.client_configuration">
            <field name="key">public_orders_limit_in_general_per_day</field>
            <field name="text">1000</field>
            <field name="related_to_text">True</field>
            <field name="description">Number of orders allowed to be created in the system within 24 hours with statuses_for_public_orders_limit configuration</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_order_types_accept_minus_values" model="rb_delivery.client_configuration">
            <field name="key">order_types_accept_minus_values</field>
            <field name="value">True</field>
            <field name="description">Order type can accept minus value when create order in mobile</field>
            <field name="related_to_status">True</field>
            <field name="related_to_order_type">True</field>
            <field name="platform_type" >mobile</field>
            <field name="order_type"/>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        <record id="client_configuration_auto_assign_business_driver_to_orders_when_create" model="rb_delivery.client_configuration">
            <field name="key">auto_assign_business_driver_to_orders_when_create</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">This if enabled it will auto assign business driver to orders when create order</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_statues_to_prevent_change_to_if_not_from_agent_collection" model="rb_delivery.client_configuration">
            <field name="key">statues_to_prevent_change_to_if_not_from_agent_collection</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="description">This configuration is to prevent changing order status if not being changed from agent collection (related order status field)</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_show_warning_if_not_from_agent_collection" model="rb_delivery.client_configuration">
            <field name="key">show_warning_if_not_from_agent_collection</field>
            <field name="value">False</field>
            <field name="description">This configuration is to show warning if changing order status if not being changed from agent collection. Related to configuration (statues_to_prevent_change_to_if_not_from_agent_collection and prevent_if_not_from_agent_collection)</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_prevent_if_not_from_agent_collection" model="rb_delivery.client_configuration">
            <field name="key">prevent_if_not_from_agent_collection</field>
            <field name="value">True</field>
            <field name="description">This configuration is to prevent if changing order status if not being changed from agent collection. Related to configuration (statues_to_prevent_change_to_if_not_from_agent_collection and show_warning_if_not_from_agent_collection)</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="roles_who_can_not_see_order_logs" model="rb_delivery.client_configuration">
            <field name="key">roles_who_can_not_see_order_logs</field>
            <field name="value">True</field>
            <field name="related_to_group_ids">True</field>
            <field name="platform_type" >web</field>
            <field name="description">This configuration is responsible to select roles who wont be able to see logs order page</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="order_fields_terms_json" model="rb_delivery.client_configuration">
            <field name="key">order_fields_terms_json</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">This configuration is used to add other synonyms for each field</field>
            <field file="rb_delivery/static/src/field_terms.json" type="base64" name="attachment"></field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_google_map_key_for_mobile_ios" model="rb_delivery.client_configuration">
            <field name="key">google_map_key_for_mobile_ios</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">AIzaSyDhUaZ2VNwuDPK-6covuHQMQ_WkDh5dArQ</field>
            <field name="platform_type">mobile</field>
            <field name="description">Ios mobile key of google map</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>

        <record id="client_configuration_create_log_message_for_appx_communication" model="rb_delivery.client_configuration">
            <field name="key">create_log_message_for_appx_communication</field>
            <field name="value">False</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Enable this configuration to create a message in the mail message module for each communication log posted in the order logs.</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_google_map_key_for_mobile_android" model="rb_delivery.client_configuration">
            <field name="key">google_map_key_for_mobile_android</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">AIzaSyDhUaZ2VNwuDPK-6covuHQMQ_WkDh5dArQ</field>
            <field name="platform_type">mobile</field>
            <field name="description">Android mobile key of google map</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>


        <record id="client_configuration_use_month_names_in_group_by" model="rb_delivery.client_configuration">
            <field name="key">use_month_names_in_group_by</field>
            <field name="value">False</field>
            <field name="description">Use month name when group by instead of month number</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_setting"/>
        </record>
        
        <record id="client_configuration_create_agent_collection_for_delivered_orders" model="rb_delivery.client_configuration">
            <field name="key">create_agent_collection_for_delivered_orders</field>
            <field name="value">False</field>
            <field name="related_to_text">False</field>
            <field name="description">Automatically create an agent collection for orders that are delivered, have no driver, and are not already in an agent collection.</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>


        <record id="client_configuration_fuzzy_search_field_highest_ratio" model="rb_delivery.client_configuration">
            <field name="key">fuzzy_search_field_highest_ratio</field>
            <field name="value">False</field>
            <field name="related_to_text">True</field>
            <field name="text">0.8</field>
            <field name="description">Ratio for fuzzy search similarity, when a field found with a higher ration then it will be chooen for excel</field>
            <field name="platform_type" >web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_status_to_go_to_when_solve_stuck_after_timer" model="rb_delivery.client_configuration">
            <field name="key">status_to_go_to_when_solve_stuck_after_timer</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_ready_for_dispatch')])]" />
            <field name="description">Order will go to this status when solve stuck if the timer has ended, Please add only one status (timer configuration solve_stuck_timer_duration)</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_status_to_go_to_when_solve_stuck_before_timer" model="rb_delivery.client_configuration">
            <field name="key">status_to_go_to_when_solve_stuck_before_timer</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_in_progress')])]" />
            <field name="description">Order will go to this status when solve stuck if the timer didn't end yet, Please add only one status (timer configuration solve_stuck_timer_duration)</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_solve_stuck_timer_duration" model="rb_delivery.client_configuration">
            <field name="key">solve_stuck_timer_duration</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">0</field>
            <field name="description">Solve stuck timer (in min), its the time that the business will have to solve stuck and change the order to the status set in status_to_go_to_when_solve_stuck_before_timer, if the stuck was solved after this timer the order will be changed to status_to_go_to_when_solve_stuck_after_timer</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_google_map_locality_language" model="rb_delivery.client_configuration">
            <field name="key">google_map_locality_language</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">ar</field>
            <field name="description">Google Map Locality Language (ar for Arabic, en for English, etc.....)</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_use_qr_code_by_default_in_mobile_scanner" model="rb_delivery.client_configuration">
            <field name="key">use_qr_code_by_default_in_mobile_scanner</field>
            <field name="value">False</field>
            <field name="related_to_text">False</field>
            <field name="description">Use Qr-code by defualt in barcode scanner.</field>
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_fields_to_get_when_change_status" model="rb_delivery.client_configuration">
            <field name="key">fields_to_get_when_change_status</field>
            <field name="value">True</field>
            <field name="related_to_field_ids">True</field>
            <field name="model_id" ref="model_rb_delivery_order" />
            <field name="platform_type" >web</field>
            <field name="description">Fields to get and display on mobile when change status</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_should_spicify_business_when_request_agent_collection" model="rb_delivery.client_configuration">
            <field name="key">should_spicify_business_when_request_agent_collection</field>
            <field name="value">False</field>
            <field name="related_to_text">False</field>
            <field name="description">Should spicify business when request agent collection.</field>
            <field name="platform_type">mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_zone_assignment_status_control" model="rb_delivery.client_configuration">
            <field name="key">zone_assignment_allowed_statuses</field>
            <field name="value">False</field>
            <field name="status" eval="[(6, 0, [ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_ready_for_dispatch')])]" />
            <field name="description">Controls which statuses allow zone assignment</field>
            <field name="related_to_status">True</field>
            <field name="platform_type">web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_notify_business_driver_enabled" model="rb_delivery.client_configuration">
            <field name="key">notify_business_driver_enabled</field>
            <field name="value">False</field>
            <field name="description">When enabled, the driver assigned to the business will be notified each time this action is triggered.</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_defaults"/>
        </record>

        <record id="client_configuration_statuses_to_remove_delivered_by" model="rb_delivery.client_configuration">
            <field name="key">statuses_to_remove_delivered_by</field>
            <field name="value">True</field>
            <field name="related_to_status">True</field>
            <field name="description">when order changed to one of those statuses the delivery date and delivered by will be removed.</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>

        <record id="client_configuration_true_buyer_token" model="rb_delivery.client_configuration">
            <field name="key">true_buyer_token</field>
            <field name="value">True</field>
            <field name="related_to_text">True</field>
            <field name="text">ojdsfbndosuabgfadosbgoiade9r@#@^%T</field>
            <field name="description">True buyer token</field>
            <field name="platform_type">web_mobile</field>
            <field name="configuration_type_id" ref="rb_delivery.client_configuration_type_order_detail"/>
        </record>
        

    </data>
    <function name="write" model="rb_delivery.client_configuration">
        <function name="search" model="rb_delivery.client_configuration">
            <value eval="[('key', '=', 'default_status_active_order')]"/>
        </function>
        <value eval="{'platform_type': 'web_mobile'}" />
    </function>
</odoo>