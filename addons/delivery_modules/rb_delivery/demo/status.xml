<odoo>
    <data noupdate="1">
  <!-- Status Mobile actions -->
        <record id="status_mobile_action_alert_reject_reasons" model="rb_delivery.status_mobile_action">
            <field name="name">alert.reject.reasons</field>
            <field name="description">show alert when reject</field>
        </record>

        <record id="status_mobile_action_alert_stuck_comment" model="rb_delivery.status_mobile_action">
            <field name="name">alert_stuck_comment</field>
            <field name="description">show input box for stuck comment without selecting other option unless there are reject reasons</field>
        </record>

        <record id="status_mobile_action_signature" model="rb_delivery.status_mobile_action">
            <field name="name">signature</field>
            <field name="description">show signature box</field>
        </record>

        <record id="status_mobile_get_current_location" model="rb_delivery.status_mobile_action">
            <field name="name">get.current.location</field>
            <field name="description">Send current GPS location</field>
        </record>

        <record id="status_mobile_action_reschedule_date" model="rb_delivery.status_mobile_action">
            <field name="name">reschedule_date</field>
            <field name="description">Show reschedule time</field>
        </record>

        <record id="status_mobile_action_customer_payment" model="rb_delivery.status_mobile_action">
            <field name="name">customer.payment</field>
            <field name="description">Show customer payment</field>
        </record>

        <record id="status_mobile_action_assign_driver" model="rb_delivery.status_mobile_action">
            <field name="name">assign_driver</field>
            <field name="description">assign driver</field>
        </record>


<!-- Status Pre actions -->

        <record id="status_action_to_in_progress_if_assign_driver" model="rb_delivery.status_pre_action">
            <field name="name">to_in_progress_if_assign_driver</field>
            <field name="description">change status to in progress from specific status if the driver changed</field>
        </record>

        <record id="status_action_to_ready_for_dispatch_if_assign_driver" model="rb_delivery.status_pre_action">
            <field name="name">to_ready_for_dispatch_if_assign_driver</field>
            <field name="description">Change status to ready for dispatch from specific status if the driver changed</field>
        </record>

        <record id="status_pre_action_to_picking_up_if_assign_driver" model="rb_delivery.status_pre_action">
            <field name="name">to_picking_up_if_assign_driver</field>
            <field name="description">move to picking up if driver assigned in specific status</field>
        </record>

        <record id="status_pre_action_to_money_out_if_assign_driver" model="rb_delivery.status_pre_action">
            <field name="name">to_money_out_if_assign_driver</field>
            <field name="description">move to money out if driver assigned in specific status</field>
        </record>


        <!-- Status actions -->
        <record id="status_action_assign_agent_to_order" model="rb_delivery.status_action">
            <field name="name">assign_agent_to_order</field>
            <field name="description">assign agent if the user logged in is driver</field>
        </record>

        <record id="status_action_reset_order_action" model="rb_delivery.status_action">
            <field name="name">reset_order_action</field>
            <field name="description">Remove order action if the action is fulfilled</field>
        </record>

        <record id="status_action_detach_agent_action" model="rb_delivery.status_action">
            <field name="name">detach_agent_action</field>
            <field name="description">remove agent if the ation is fulfilled</field>
        </record>

        <record id="status_action_remove_from_runsheet_action" model="rb_delivery.status_action">
            <field name="name">remove_from_runsheet_action</field>
            <field name="description">remove order from runsheet if the ation is fulfilled</field>
        </record>

        <record id="status_action_clear_reschedule_date_action" model="rb_delivery.status_action">
            <field name="name">clear_reschedule_date</field>
            <field name="description">Clear reschedule date if the ation is fulfilled</field>
        </record>

        <record id="status_action_detach_partner_action" model="rb_delivery.status_action">
            <field name="name">detach_partner_action</field>
            <field name="description">remove partner if the ation is fulfilled</field>
        </record>
        <record id="status_action_agent_required" model="rb_delivery.status_action">
            <field name="name">agent_required</field>
            <field name="description">Make agent required if the status action is fulfilled</field>
        </record>

        <record id="status_action_detach_current_drivers_action" model="rb_delivery.status_action">
            <field name="name">detach_current_drivers_action</field>
            <field name="description">remove current drivers if the ation is fulfilled</field>
        </record>

        <record id="status_action_api_webhook_action" model="rb_delivery.status_action">
            <field name="name">api_webhook_action</field>
            <field name="description">run a certain api on change status</field>
        </record>

        <record id="status_action_change_in_branch_if_not_business_on_create" model="rb_delivery.status_action">
            <field name="name">change_in_branch_if_not_business_on_create</field>
            <field name="description">change to in branch when create if the user who created this order not in business</field>
        </record>

        <record id="status_action_prevent_change_status_if_not_in_agent_collection" model="rb_delivery.status_action">
            <field name="name">prevent_change_status_if_not_in_agent_collection</field>
            <field name="description">Prevent changing status if the action is fulfilled</field>
        </record>

        <record id="status_action_prevent_change_status_if_not_in_collection" model="rb_delivery.status_action">
            <field name="name">prevent_change_status_if_not_in_collection</field>
            <field name="description">Prevent changing status if the action is fulfilled</field>
        </record>
        <record id="status_action_set_ready_for_return" model="rb_delivery.status_action">
            <field name="name">set_ready_for_return</field>
            <field name="description">Set ready for return so when move the order between branches it will keep the status as it is</field>
        </record>

        <record id="status_action_clear_ready_for_return" model="rb_delivery.status_action">
            <field name="name">clear_ready_for_return</field>
            <field name="description">Clear ready for return so when move the order between branches it won't keep the status as it is anymore</field>
        </record>

        <record id="status_action_reset_returned_discount" model="rb_delivery.status_action">
            <field name="name">reset_returned_discount</field>
            <field name="description">Reset discount value when change to current status</field>
        </record>

        <record id="status_action_select_state_show_reschedule_date" model="rb_delivery.status_related_field">
            <field name="name">show_reschedule_date</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field reschedule date in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_agent" model="rb_delivery.status_related_field">
            <field name="name">show_agent</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field agent in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_note" model="rb_delivery.status_related_field">
            <field name="name">show_note</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field note in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_extra_agent_cost" model="rb_delivery.status_related_field">
            <field name="name">show_extra_agent_cost</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field extra agent cost in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_payment_type" model="rb_delivery.status_related_field">
            <field name="name">show_payment_type</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field payment type in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_payment_type_two" model="rb_delivery.status_related_field">
            <field name="name">show_payment_type_two</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field payment type two in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_customer_payment" model="rb_delivery.status_related_field">
            <field name="name">show_customer_payment</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field customer payment in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_customer_payment_two" model="rb_delivery.status_related_field">
            <field name="name">show_customer_payment_two</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field customer payment two in select state model when change to current status</field>
        </record>

        <record id="status_action_select_state_show_reject_reason" model="rb_delivery.status_related_field">
            <field name="name">show_reject_reason</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field reject reason in select state model when change to current status</field>
        </record>

        <record id="status_action_select_order_action" model="rb_delivery.status_related_field">
            <field name="name">show_order_action</field>
            <field name="action_type">olivery_order</field>
            <field name="description">Show field order action in select state model when change to current status</field>
        </record>

        <record id="status_action_collection_select_state_show_agent" model="rb_delivery.status_related_field">
            <field name="name">collection_show_agent</field>
            <field name="action_type">olivery_collection</field>
            <field name="description">Show field agent in select state model in collections when change to current status</field>
        </record>
        <record id="status_action_select_state_show_closing_date" model="rb_delivery.status_related_field">
            <field name="name">show_closing_date</field>
            <field name="action_type">olivery_collection</field>
            <field name="description">Show field closing date in select state model when change to current status</field>
        </record>
        <record id="status_action_set_payment_date" model="rb_delivery.status_action">
            <field name="name">set_payment_date</field>
            <field name="description">Set Payment Date when change to current status </field>
        </record>

         <record id="status_action_order_status_if_paid_collection_check_pass" model="rb_delivery.status_action">
            <field name="name">order_status_if_paid_collection_check_pass</field>
            <field name="description">Change order status to paid if it's financially paid or money received if it's not paid</field>
        </record>

        <record id="status_action_remove_stuck_comment_and_reject_reason" model="rb_delivery.status_action">
            <field name="name">remove_stuck_comment_and_reject_reason</field>
            <field name="description">When change to this status it will remove stuck comment and reject reason</field>
        </record>
        

        <record id="status_completed" model="rb_delivery.status">
            <field name="name">completed</field>
            <field name="title">Completed</field>
            <field name="title_ar">مكتمل</field>
            <field name="description">When the cycle end for cargo,the merchant received the money collection and we have it signed , or the merchant change the status to completed</field>
            <field name="colour_code">#2529A5</field>
            <field name="sequence">17</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_completed_returned" model="rb_delivery.status">
            <field name="name">completed_returned</field>
            <field name="title">Completed Returned</field>
            <field name="title_ar">مكتمل راجع</field>
            <field name="description">When the cycle end for unsucceful delivery,the merchant received the returned collection and we have it signed , or the merchant change the status to completed returned </field>
            <field name="colour_code">#2529A5</field>
            <field name="sequence">22</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>


        <record id="status_canceled" model="rb_delivery.status"> <field name="name">canceled</field> <field name="title">Canceled</field> </record>

        <record id="status_deleted" model="rb_delivery.status">
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوف</field>
            <field name="description">When order is deleted</field>
            <field name="colour_code">#E13216</field>
            <field name="sequence">25</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>


        <record id="status_paid" model="rb_delivery.status">
            <field name="name">paid</field>
            <field name="title">Paid for business</field>
            <field name="title_ar">مدفوع للتاجر</field>
            <field name="description">When driver pay for business , but it does not MEAN that the business received the money</field>
            <field name="colour_code">#1E93B8</field>
            <field name="sequence">16</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <!-- for looping issue -->
        <record id="status_money_in" model="rb_delivery.status"> 
            <field name="name">money_in</field> 
            <field name="title">Money In</field> 
            <field name="colour_code">#447467</field>
            <field name="sequence">15</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
        </record>
        <record id="status_money_received" model="rb_delivery.status">
             <field name="name">money_received</field> 
             <field name="title">Money Received</field> 
             <field name="colour_code">#447467</field>
             <field name="sequence">15</field>
             <field name="default">False</field>
             <field name="status_type">olivery_order</field>
        </record>


        <record id="status_money_out" model="rb_delivery.status">
            <field name="name">money_out</field>
            <field name="title">Money Out</field>
            <field name="title_ar">جاري تسليم المال للتاجر</field>
            <field name="description">When accountant or the super manager send the money to business with driver</field>
            <field name="colour_code">#447467</field>
            <field name="sequence">15</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_money_in'),ref('rb_delivery.status_paid')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_money_in')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_money_in" model="rb_delivery.status">
            <field name="name">money_in</field>
            <field name="title">Money In</field>
            <field name="title_ar">تم استلام ومعالجة المال</field>
            <field name="description">When accountant or the super manager receive the money and process it</field>
            <field name="colour_code">#52AA92</field>
            <field name="sequence">14</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_money_received'),ref('rb_delivery.status_money_out'),ref('rb_delivery.status_paid'),ref('rb_delivery.status_completed')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_money_in')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_money_received')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_money_received" model="rb_delivery.status">
            <field name="name">money_received</field>
            <field name="title">Money Received</field>
            <field name="title_ar">تم استلام المال في الفرع</field>
            <field name="description">When branch manager receive the money in the branch from the driver</field>
            <field name="colour_code">#467121</field>
            <field name="sequence">13</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_money_in'),ref('rb_delivery.status_completed')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_payment_type')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_money_received')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>
        <record id="status_returned_delivered" model="rb_delivery.status">
            <field name="name">returned_delivered</field>
            <field name="title">Returned Delivered</field>
            <field name="title_ar">تم تسليم المرجع للتاجر</field>
            <field name="description">When driver deliver the returned cargo to the business</field>
            <field name="colour_code">#887204</field>
            <field name="sequence">21</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_completed_returned')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <!-- this is for circular defined twice -->
        <record id="status_branch_returned" model="rb_delivery.status">
            <field name="name">branch_returned</field>
            <field name="title">Branch Returned</field>
            <field name="title_ar">مرجع للفرع</field>
        </record>



        <record id="status_returned_in_progress" model="rb_delivery.status">
            <field name="name">returned_in_progress</field>
            <field name="title">Returned in progress</field>
            <field name="title_ar">جاري تسليم المرجع للتاجر</field>
            <field name="description">When driver has the package to be returned to business</field>
            <field name="colour_code">#C7A602</field>
            <field name="sequence">20</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_returned_delivered'),ref('rb_delivery.status_branch_returned')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>

        <!-- defined twice for circular -->
        <record id="status_in_progress" model="rb_delivery.status">
            <field name="name">in_progress</field>
            <field name="title">In Progress</field>
            <field name="title_ar">جاري التوصيل</field>
        </record>

        <record id="status_ready_for_dispatch" model="rb_delivery.status">
            <field name="name">ready_for_dispatch</field>
            <field name="title">Ready For Dispatch</field>
        </record>

        <record id="status_ready_for_return" model="rb_delivery.status">
            <field name="name">ready_for_return</field>
            <field name="title">Ready For Return</field>
        </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_branch_returned')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_branch_returned" model="rb_delivery.status">
            <field name="name">branch_returned</field>
            <field name="title">Branch Returned</field>
            <field name="title_ar">مرجع للفرع</field>
            <field name="description">When driver deliver the rturned package to the branch manager</field>
            <field name="colour_code">#4B3161</field>
            <field name="sequence">18</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_returned_in_progress'),ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_completed_returned'),ref('rb_delivery.status_ready_for_dispatch'),ref('rb_delivery.status_ready_for_return')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business')])]" />
            <field name="show_in_order_track">False</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_branch_returned')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <record id="status_rejected" model="rb_delivery.status">
            <field name="name">rejected</field>
            <field name="title">Rejected</field>
            <field name="title_ar">مرفوض</field>
            <field name="description">When the receiver see the package and refuse to take it</field>
            <field name="colour_code">#AB0707</field>
            <field name="web_color">warning</field>
            <field name="sequence">10</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_branch_returned')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_alert_reject_reasons'),ref('rb_delivery.status_mobile_action_signature'),ref('rb_delivery.status_mobile_get_current_location'),ref('rb_delivery.status_mobile_action_customer_payment')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_payment_type'),ref('rb_delivery.status_action_select_state_show_customer_payment')])]" />
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_reject_reason')])]" />
        </record>

        <record id="status_stuck" model="rb_delivery.status">
            <field name="name">stuck</field>
            <field name="title">Stuck</field>
            <field name="title_ar">عالق</field>
            <field name="description">When the driver try to contact the receiver but there is no response from receiver</field>
            <field name="colour_code">#FF4D4D</field>
            <field name="sequence">11</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_branch_returned')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_alert_reject_reasons'),ref('rb_delivery.status_mobile_get_current_location')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_reject_reason')])]" />
        </record>

        <record id="status_delivered" model="rb_delivery.status">
            <field name="name">delivered</field>
            <field name="title">Delivered</field>
            <field name="title_ar">تم التوصيل</field>
            <field name="description">When the driver try to contact the receiver but there is no response from receiver</field>
            <field name="colour_code">#449102</field>
            <field name="web_color">success</field>
            <field name="sequence">9</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_money_received')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature'),ref('rb_delivery.status_mobile_get_current_location'),ref('rb_delivery.status_mobile_action_customer_payment')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_payment_type'),ref('rb_delivery.status_action_select_state_show_customer_payment')])]" />
        </record>

        <record id="status_delivered_stuck" model="rb_delivery.status">
            <field name="name">delivered_stuck</field>
            <field name="title">Delivered Stuck</field>
            <field name="title_ar">تم التوصيل عالق</field>
            <field name="description">When the driver delivers a package but there is a deference between the payment and cost</field>
            <field name="colour_code">#449102</field>
            <field name="web_color">warning</field>
            <field name="sequence">9</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_money_received')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature'),ref('rb_delivery.status_mobile_get_current_location'),ref('rb_delivery.status_mobile_action_customer_payment')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_payment_type'),ref('rb_delivery.status_action_select_state_show_customer_payment')])]" />
        </record>

        <record id="status_reschedule" model="rb_delivery.status">
            <field name="name">reschedule</field>
            <field name="title">Reschedule</field>
            <field name="title_ar">مؤجل</field>
            <field name="description">When driver reschedule the package based on receiver demand</field>
            <field name="colour_code">#4F0291</field>
            <field name="sequence">11</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_branch_returned')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_reschedule_date'),ref('rb_delivery.status_mobile_get_current_location')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_reschedule_date')])]" />
        </record>

        <record id="status_in_branch" model="rb_delivery.status">
            <field name="name">in_branch</field>
            <field name="title">In Branch</field>
        </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_ready_for_dispatch')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_ready_for_dispatch" model="rb_delivery.status">
            <field name="name">ready_for_dispatch</field>
            <field name="title">Ready For Dispatch</field>
            <field name="title_ar">جاهز للتوزيع</field>
            <field name="description">When packages are ready to be out for delivery with driver</field>
            <field name="colour_code">#02C75E</field>
            <field name="sequence">6</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_canceled'),ref('rb_delivery.status_branch_returned'),ref('rb_delivery.status_in_branch')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_ready_for_dispatch')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_ready_for_return')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_ready_for_return" model="rb_delivery.status">
            <field name="name">ready_for_return</field>
            <field name="title">Ready For Return</field>
            <field name="title_ar">جاهز للارجاع</field>
            <field name="description">When package is ready to be returned to branch or business</field>
            <field name="colour_code">#02C75E</field>
            <field name="sequence">19</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_branch_returned'),ref('rb_delivery.status_returned_in_progress')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_ready_for_return')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <record id="status_replacement" model="rb_delivery.status"> <field name="name">replacement</field> <field name="title">Replacement</field> </record>
        <record id="status_rejected_partial" model="rb_delivery.status"> <field name="name">rejected_partial</field> <field name="title">Rejected Partial</field> </record>
        <record id="status_delivered_partial" model="rb_delivery.status"> <field name="name">delivered_partial</field> <field name="title">Delivered Partial</field> </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_in_progress')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_in_progress" model="rb_delivery.status">
            <field name="name">in_progress</field>
            <field name="title">In Progress</field>
            <field name="title_ar">جاري التوصيل</field>
            <field name="description">When driver it out for delivery for receiver package</field>
            <field name="colour_code">#02C75E</field>
            <field name="sequence">8</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_delivered'),ref('rb_delivery.status_rejected'),ref('rb_delivery.status_reschedule'),ref('rb_delivery.status_stuck'),ref('rb_delivery.status_replacement')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">True</field>
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_in_progress')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <!-- for circular  -->
        <record id="status_shelved" model="rb_delivery.status"> <field name="sequence">5</field> <field name="name">shelved</field> <field name="title">Shelved</field> <field name="title_ar">على الرف</field> </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_in_branch')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_in_branch" model="rb_delivery.status">
            <field name="name">in_branch</field>
            <field name="title">In Branch</field>
            <field name="title_ar">في الفرع</field>
            <field name="description">When receive package from business through driver in branch</field>
            <field name="colour_code">#8676FE</field>
            <field name="sequence">4</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_canceled'),ref('rb_delivery.status_ready_for_dispatch'),ref('rb_delivery.status_shelved')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="status_pre_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_to_in_progress_if_assign_driver')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_data_entry')])]" />
            <field name="show_in_order_track">True</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_in_branch')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_shelved')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_shelved" model="rb_delivery.status">
            <field name="name">shelved</field>
            <field name="title">Shelved</field>
            <field name="title_ar">على الرف</field>
            <field name="description">When package is shelved.</field>
            <field name="colour_code">#8676FE</field>
            <field name="sequence">5</field>
            <field name="default">False</field>
            <field name="active">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_progress'),ref('rb_delivery.status_canceled'),ref('rb_delivery.status_ready_for_dispatch')])]" />
            <!-- <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_select_shelf_action')])]" /> -->
            <!-- <field name="status_pre_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_to_in_progress_if_assign_driver')])]" /> -->
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry')])]" />
            <field name="show_in_order_track">True</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_shelved')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <record id="status_picked_up" model="rb_delivery.status">
            <field name="name">picked_up</field>
            <field name="title">Picked up</field>
            <field name="title_ar">تم استلام الشحنة من التاجر</field>
            <field name="description">When driver confirm acceptance for packge from business</field>
            <field name="colour_code">#AD7EA6</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_canceled')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_picking_up" model="rb_delivery.status">
            <field name="name">picking_up</field>
            <field name="title">Picking up</field>
            <field name="title_ar">جاري استلام الشحنة من التاجر</field>
            <field name="description">When driver is assigned and he is going to pick up the cargo from the business</field>
            <field name="colour_code">#FE7443</field>
            <field name="sequence">2</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_canceled')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">False</field>
            <field name="status_action_required_aditional_fields" eval="[(6, 0, [ref('rb_delivery.status_action_select_state_show_agent')])]" />
        </record>

        <record id="status_waiting" model="rb_delivery.status">
            <field name="name">waiting</field>
            <field name="title">Waiting</field>
            <field name="title_ar">بالانتظار</field>
            <field name="description">When driver is assigned and he is going to pick up the cargo from the business</field>
            <field name="colour_code">#FE7443</field>
            <field name="web_color">danger</field>
            <field name="sequence">1</field>
            <field name="default">True</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_picking_up'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_canceled')])]" />
            <field name="status_pre_action_ids" eval="[(6, 0, [ref('rb_delivery.status_pre_action_to_picking_up_if_assign_driver')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]"/>
            <field name="status_action_on_create_ids" eval="[(6, 0, [ref('rb_delivery.status_action_change_in_branch_if_not_business_on_create')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_pending_business_approval" model="rb_delivery.status">
            <field name="name">pending_business_approval</field>
            <field name="title">Pending business approval</field>
            <field name="title_ar">بأنتظار موافقة التاجر</field>
            <field name="description">When order is created using public link</field>
            <field name="colour_code">#FE7443</field>
            <field name="web_color">danger</field>
            <field name="sequence">1</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_waiting')])]" />
            <field name="status_pre_action_ids" eval="[(6, 0, [ref('rb_delivery.status_pre_action_to_picking_up_if_assign_driver')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_canceled')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_canceled" model="rb_delivery.status">
            <field name="name">canceled</field>
            <field name="title">Cancelled</field>
            <field name="title_ar">ملغي</field>
            <field name="description">When business,manager and super manager cancel the shipment</field>
            <field name="colour_code">#E13216</field>
            <field name="sequence">24</field>
            <field name="default">False</field>
            <field name="status_type">olivery_order</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_waiting'),ref('rb_delivery.status_in_branch'),ref('rb_delivery.status_picked_up'),ref('rb_delivery.status_in_progress')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_call_center')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">True</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_canceled')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <!-- Money Collections -->
        <record id="status_collection_completed" model="rb_delivery.status">
            <field name="name">completed</field>
            <field name="title">Completed</field>
            <field name="title_ar">مكتمل</field>
            <field name="description">When the cycle end for cargo,the merchant received the money collection and we have it signed , or the merchant change the status to completed for the whole collection</field>
            <field name="related_order_status">completed</field>
            <field name="colour_code">#2529A5</field>
            <field name="sequence">5</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">True</field>
        </record>

        <record id="status_collection_paid" model="rb_delivery.status">
            <field name="name">paid</field>
            <field name="title">Paid for business</field>
            <field name="title_ar">مدفوع للتاجر</field>
            <field name="description">When driver pay for business , but it does not MEAN that the business received the money with collection</field>
            <field name="related_order_status">paid</field>
            <field name="default_related_order">False</field>
            <field name="colour_code">#1E93B8</field>
            <field name="related_order_status">paid</field>
            <field name="default_related_order">False</field>
            <field name="sequence">4</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
        </record>
        <!-- for loop issue -->
        <record id="status_collection_money_out" model="rb_delivery.status"> 
            <field name="title">Money Out</field>
            <field name="name">money_out</field> 
            <field name="colour_code">#447467</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
        </record>

        <!-- for loop issue -->
        <record id="status_collection_money_in" model="rb_delivery.status"> 
            <field name="name">money_in</field>
            <field name="title">Money In</field> 
            <field name="colour_code">#447467</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
        </record>

        <!-- for loop issue -->
        <record id="status_collection_money_received" model="rb_delivery.status"> 
            <field name="name">money_received</field> 
            <field name="title">Money Received</field>
            <field name="colour_code">#447467</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
        </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_out')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_collection_money_out" model="rb_delivery.status">
            <field name="name">money_out</field>
            <field name="title">Money Out</field>
            <field name="title_ar">جاري تسليم المال للتاجر</field>
            <field name="related_order_status">money_out</field>
            <field name="default_related_order">False</field>
            <field name="description">When accountant or the super manager send the money to business with collection</field>
            <field name="colour_code">#447467</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_money_in'),ref('rb_delivery.status_collection_paid')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_collection_select_state_show_agent')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_out')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_in')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_collection_money_in" model="rb_delivery.status">
            <field name="name">money_in</field>
            <field name="title">Money In</field>
            <field name="related_order_status">money_in</field>
            <field name="default_related_order">False</field>
            <field name="title_ar">تم استلام ومعالجة المال</field>
            <field name="description">When accountant or the super manager receive the money and process it in the collection</field>
            <field name="colour_code">#52AA92</field>
            <field name="sequence">2</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_money_received'),ref('rb_delivery.status_collection_money_out'),ref('rb_delivery.status_collection_paid'),ref('rb_delivery.status_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_in')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_received')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_collection_money_received" model="rb_delivery.status">
            <field name="name">money_received</field>
             <field name="title">Money Received</field>
            <field name="title_ar">تم استلام المال في الفرع</field>
            <field name="description">When branch manager receive the money in the branch from the driver and make collection</field>
            <field name="related_order_status">money_received</field>
            <field name="default_related_order">True</field>
            <field name="sequence">1</field>
            <field name="default">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_money_in'),ref('rb_delivery.status_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager')])]" />
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_collection_money_received')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>

        <record id="status_deleted_collection" model="rb_delivery.status">
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوف</field>
            <field name="description">When collection is deleted</field>
            <field name="related_order_status">deleted</field>
            <field name="default_related_order">False</field>
            <field name="colour_code">#E13216</field>
            <field name="sequence">6</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_money_received')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <!-- Returned Collection -->
        <record id="status_collection_completed_returned" model="rb_delivery.status">
            <field name="name">completed_returned</field>
            <field name="title">Completed Returned</field>
            <field name="title_ar">مكتمل راجع</field>
            <field name="related_order_status">completed_returned</field>
            <field name="default_related_order">False</field>
            <field name="description">When the cycle end for unsucceful delivery,the merchant received the returned collection and we have it signed , or the merchant change the status to completed returned for a collection</field>
            <field name="colour_code">#2529A5</field>
            <field name="sequence">4</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_collection_returned_delivered" model="rb_delivery.status">
            <field name="name">returned_delivered</field>
            <field name="title">Returned Delivered</field>
            <field name="title_ar">تم تسليم المرجع للتاجر</field>
            <field name="description">When driver deliver the returned cargo to the business with a collection</field>
            <field name="colour_code">#887204</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="lock_status">True</field>
             <field name="related_order_status">returned_delivered</field>
            <field name="default_related_order">False</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_completed_returned')])]" />
            <!-- <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" /> -->
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_returned_collection_branch_returned" model="rb_delivery.status"> 
            <field name="name">branch_returned</field>
            <field name="title">Branch Returned</field> 
            <field name="colour_code">#447467</field>
            <field name="sequence">3</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
        </record>

        <record id="status_returned_collection_replacement" model="rb_delivery.status">
            <field name="name">replacement</field>
            <field name="title">Replacement</field>
            <field name="title_ar">تبديل</field>
            <field name="sequence">5</field>
            <field name="web_color">danger</field>
            <field name="description">When driver deliver something while he should also get another thing</field>
            <field name="colour_code">#FE7443</field>
            <field name="default">False</field>
            <field name="related_order_status">replacement</field>
            <field name="default_related_order">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]"/>
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_returned_collection_branch_returned'),ref('rb_delivery.status_deleted_collection')])]"/>
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_call_center'),ref('rb_delivery.role_data_entry'),ref('rb_delivery.role_driver')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="show_in_order_track">False</field>
        </record>

        <record id="status_returned_collection_returned_in_progress" model="rb_delivery.status">
            <field name="name">returned_in_progress</field>
            <field name="title">Returned in progress</field>
            <field name="title_ar">جاري تسليم المرجع للتاجر</field>
            <field name="description">When driver has the package to be returned to business</field>
            <field name="colour_code">#C7A602</field>
            <field name="sequence">2</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="related_order_status">returned_in_progress</field>
            <field name="default_related_order">False</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_collection_returned_delivered'),ref('rb_delivery.status_returned_collection_branch_returned')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="show_in_order_track">False</field>
            <field name="status_action_optional_related_fields" eval="[(6, 0, [ref('rb_delivery.status_action_collection_select_state_show_agent')])]" />
        </record>

        <record id="status_deleted_returned_collection" model="rb_delivery.status">
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوف</field>
            <field name="description">When Returned collection is deleted</field>
            <field name="related_order_status">deleted</field>
            <field name="default_related_order">False</field>
            <field name="colour_code">#E13216</field>
            <field name="sequence">6</field>
            <field name="default">False</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_returned_collection_branch_returned')])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_business'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_super_manager')])]" />
            <field name="show_in_order_track">False</field>
        </record>

        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_returned_collection_branch_returned')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': False}" />
        </function>
        <record id="status_returned_collection_branch_returned" model="rb_delivery.status">
            <field name="name">branch_returned</field>
            <field name="title">Branch returned</field>
            <field name="title_ar">مرجع للفرع</field>
            <field name="description">When driver deliver the rturned package to the branch manager with a collection</field>
            <field name="colour_code">#4B3161</field>
            <field name="sequence">1</field>
            <field name="default">True</field>
            <field name="related_order_status">branch_returned</field>
            <field name="default_related_order">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">returned_collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_returned_collection_returned_in_progress'),ref('rb_delivery.status_collection_completed_returned'),ref('rb_delivery.status_deleted_returned_collection') ])]" />
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_business')])]" />
            <field name="show_in_order_track">False</field>
        </record>
        <function name="write" model="ir.model.data">
            <!-- First we need to find the record...-->
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'rb_delivery.status'), ('name', '=', 'status_returned_collection_branch_returned')]"/>
            </function>
           <!-- ...and temporarily set the noupdate field to False-->
            <value eval="{'noupdate': True}" />
        </function>



        <!-- Agent Collection -->
        <record id="status_agent_collection_completed" model="rb_delivery.status">
            <field name="sequence">2</field>
            <field name="name">completed</field>
            <!-- <field name="related_order_status">completed</field> -->
            <field name="title">Completed</field>
            <field name="title_ar">تم</field>
            <field name="default">False</field>
            <field name="collection_type">agent_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">When agent collection is signed from the driver</field>
            <field name="colour_code">#2529A5</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="status_mobile_action_ids" eval="[(6, 0, [ref('rb_delivery.status_mobile_action_signature')])]" />
        </record>

        <record id="status_agent_collection_preparing" model="rb_delivery.status">
            <field name="sequence">1</field>
            <field name="name">created</field>
            <field name="title">Created</field>
            <field name="title_ar">تم الانشاء</field>
            <field name="default">True</field>
            <field name="collection_type">agent_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">This is when agent collection is just created</field>
            <field name="colour_code">#2529A5</field>
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_agent_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
        </record>

        <record id="status_deleted_agent_collection" model="rb_delivery.status">
            <field name="sequence">3</field>
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوفة</field>
            <field name="default">False</field>
            <field name="collection_type">agent_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">The order is deleted ( archived ) </field>
            <field name="colour_code">#E13216</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record>

        <!-- Agent returned -->
        <record id="status_agent_collection_completed_returned" model="rb_delivery.status">
            <field name="sequence">2</field>
            <field name="name">completed_returned</field>
            <!-- <field name="related_order_status">completed_returned</field> -->
            <field name="title">Completed Returned</field>
            <field name="title_ar">تم مرجع</field>
            <field name="default">False</field>
            <field name="collection_type">agent_returned_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">This is when returned cargo is back to sender</field>
            <field name="colour_code">#2529A5</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record>

        <record id="status_returned_agent_collection_preparing" model="rb_delivery.status">
            <field name="sequence">1</field>
            <field name="name">created</field>
            <field name="title">Created</field>
            <field name="title">Created</field>
            <field name="title_ar">قيد التحضير</field>
            <field name="default">True</field>
            <field name="collection_type">agent_returned_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">This is when returned agent returned collection is just created</field>
            <field name="colour_code">#2529A5</field>
            <field name="next_state_ids" eval="[(6, 0,[ref('rb_delivery.status_agent_collection_completed_returned')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record>

        <record id="status_deleted_agent_collection_returned" model="rb_delivery.status">
            <field name="sequence">3</field>
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوفة</field>
            <field name="default">False</field>
            <field name="collection_type">agent_returned_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">The colleciont is deleted</field>
            <field name="colour_code">#E13216</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6, 0, [ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6, 0, [ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6, 0, [ref('rb_delivery.role_accounting'),ref('rb_delivery.role_super_manager')])]" />
        </record>


        <!-- Run Sheet -->
        <record id="status_runsheet_collection_completed" model="rb_delivery.status">
            <field name="sequence">2</field>
            <field name="name">completed</field>
            <!-- <field name="related_order_status">completed</field> -->
            <field name="title">Completed</field>
            <field name="title_ar">تم</field>
            <field name="default">False</field>
            <field name="collection_type">runsheet_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">When runsheet completed</field>
            <field name="colour_code">#2529A5</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record>

        <record id="status_runsheet_collection_created" model="rb_delivery.status">
            <field name="sequence">1</field>
            <field name="name">created</field>
            <field name="title">Created</field>
            <field name="title_ar">تم الانشاء</field>
            <field name="default">True</field>
            <field name="collection_type">runsheet_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">When runsheet is just created</field>
            <field name="colour_code">#2529A5</field>
            <field name="next_state_ids" eval="[(6, 0, [ref('rb_delivery.status_runsheet_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
        </record>
        
        <record id="status_runsheet_collection_in_progress" model="rb_delivery.status">
            <field name="sequence">2</field>
            <field name="name">in_progress</field>
            <field name="title">In progress</field>
            <field name="title_ar">جاري التوصيل</field>
            <field name="default">False</field>
            <field name="related_order_status">in_progress</field>
            <field name="collection_type">runsheet_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">When runsheet is in progress</field>
            <field name="colour_code">#2529A5</field>
            <field name="next_state_ids" eval="[(6, 0, [ref('rb_delivery.status_runsheet_collection_completed')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver')])]" />
        </record>

        <record id="status_deleted_runsheet" model="rb_delivery.status">
            <field name="sequence">3</field>
            <field name="name">deleted</field>
            <field name="title">Deleted</field>
            <field name="title_ar">محذوفة</field>
            <field name="default">False</field>
            <field name="collection_type">runsheet_collection</field>
            <field name="status_type">olivery_collection</field>
            <field name="description">The runsheet is deleted</field>
            <field name="colour_code">#E13216</field>
            <field name="status_action_ids" eval="[(6, 0, [ref('rb_delivery.status_action_detach_agent_action')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_accounting'),ref('rb_delivery.role_business'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_super_manager')])]" />
        </record>
    </data>
</odoo>