odoo.define('rb_delivery.hide_delivery_profit_field', function (require) {
    "use strict";

    var ListView = require('web.ListView');
    var session = require('web.session');

    ListView.include({
        init: function (viewInfo, params) {
            this._super.apply(this, arguments);
            if (session && session.user_info && session.user_info.block_delivery_profit && this.arch.tag === 'tree') {
                this.arch.children = this.arch.children.filter(function (field) {
                    return ['total_delivery_profit', 'company_profit_total'].includes(field.attrs.name) === false;
                });
            }
        },
    });
});
