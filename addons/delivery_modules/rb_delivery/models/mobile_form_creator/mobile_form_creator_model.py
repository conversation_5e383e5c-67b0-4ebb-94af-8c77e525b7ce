# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class rb_delivery_mobile_form_creator(models.Model):

    _name = 'rb_delivery.mobile_form_creator'
    _inherit = 'mail.thread'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    _sql_constraints = [('name', 'unique(name)', _('Form name already exists!'))]

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
    
    def _form_selection(self):
        return [('isolated_form','Isolated Form'),('order_form','Order Form'),('login_form','Login Form'),('signup_form','Signup Form'),('user_form','User Form'),('business_stores_form','Business Stores Form'),('tasks_form', 'Tasks Form'),('status_action_form','Status Action Form'),('pickup_request_form','Pickup Request Form'),('quick_order_form', 'Quick Order Form')]

    is_custom = fields.Boolean('Is Custom (For Nav Item)')

    form_name_custom = fields.Char('Form Name')

    form_name = fields.Selection(selection=_form_selection,validate=False)

    show_toolbar = fields.Boolean('Show Toolbar')

    skip_success_message = fields.Boolean('Skip Success Message')

    name = fields.Char(compute="_compute_form_name",store=True, track_visibility="on_change")

    is_public_form = fields.Boolean(track_visibility="on_change")

    model = fields.Many2one('ir.model', required=True, track_visibility="on_change")

    model_relation = fields.Char(related="model.model", store=True, track_visibility="on_change")

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups,default=4, track_visibility="on_change")

    form_inputs = fields.One2many('rb_delivery.mobile_form_input',inverse_name="form_creator", track_visibility="on_change")

    form_json = fields.Text()

    fields_ids = fields.Many2many('ir.model.fields',compute="compute_form_fields",store=True, track_visibility="on_change")

    fields_names = fields.Char(compute="compute_form_fields",store=True, track_visibility="on_change")

    is_collection = fields.Boolean(string='Is Collection', track_visibility="on_change")

    collection_type = fields.Selection(
        selection='_get_dynamic_collection_types', 
        string='Collection Type', track_visibility="on_change"
    )

    isolated_input = fields.Many2one('rb_delivery.mobile_form_input', track_visibility="on_change")

    is_isolated = fields.Boolean('Is Isolated',related="isolated_input.is_isolated_input", track_visibility="on_change")

    isolated_input_field = fields.Many2one('ir.model.fields',related="isolated_input.field", track_visibility="on_change")

    isolated_input_form_creator = fields.Many2one('rb_delivery.mobile_form_creator',related='isolated_input.form_creator', track_visibility="on_change")

    is_action_form = fields.Boolean('Is Action form')

    status_action_domain = fields.Text('Status Action Domain')

    @api.constrains('is_custom')
    def update_form_name_custom(self):
        if self.is_custom:
            self.form_name = False

    @api.depends('isolated_input_field')
    def _compute_is_isolated(self):
        for rec in self:
            if rec.isolated_input_field:
                rec.is_isolated=True
            else:
                rec.is_isolated=False

    @api.onchange('isolated_input_field')
    def _onchange_is_isolated(self):
        if self.is_isolated:
            self.model = self.env['ir.model'].search([['model','=',self.isolated_input_field.relation]]).id
            self.form_name = 'isolated_form'
            self.group_id = self.isolated_input_form_creator.group_id.id
    
    @api.onchange('form_name')
    def change_form_name_custom(self):
        if self.form_name:
            self.form_name_custom = self.form_name


    @api.model
    def _get_dynamic_collection_types(self):
        # Fetch the dynamic collection types from the rb_delivery.status model
        status_model = self.env['rb_delivery.status']
        return status_model.get_collection_type()

    status_ids = fields.Many2many(
        'rb_delivery.status',
        'form_status_rel',
        'form_id',
        'status_id',
        string='Statuses',
    )

    @api.onchange('is_collection', 'collection_type')
    def _onchange_collection_type(self):
        domain = []
        if self.is_collection and self.collection_type:
            domain = [('collection_type', '=', self.collection_type)]
            
            return {'domain': {'status_ids': domain}}
        else:
            return {'domain': {'status_ids': [('status_type','=','olivery_order')]}}

    @api.onchange('is_public_form')
    def publish_form(self):
        if self.is_public_form:
            self.group_id=4
        else:
            self.group_id=False
        


    @api.one
    @api.depends('form_name_custom','form_name','group_id','status_ids','model','is_isolated','isolated_input_form_creator','isolated_input')
    def _compute_form_name(self):
        form_name=""
        if self.group_id:
            form_name=(self.form_name_custom or self.form_name) + ' [' + self.group_id.name + ']'
        else:
            form_name=(self.form_name_custom or self.form_name)
        
            if self.is_isolated:
                form_name=(self.form_name_custom or self.form_name)+' - '+self.isolated_input.display_name+' - '+self.isolated_input_form_creator.name

        if len(self.status_ids)>0 and isinstance(self.id,int):
            if self.model.name:
                form_name += ' - [' + self.model.name + '] - ['
            status_names = []
            for field in self.status_ids:
                existing = self.search([
                ('name', 'like', field.name),
                ('id', '!=', self.id),
                ('model', '=', self.model.id),
                ('group_id', '=', self.group_id.id)
                    ])
                if not existing:
                    status_names.append(field.name)
                elif not self.is_isolated:
                     self.env['rb_delivery.error_log'].raise_olivery_error(570,self.id,{'group_id': self.group_id.name, 'field_name': field.name})
                   #raise ValidationError(
                       # _("A form with the same role and status '{}' already exists: {}").format(self.group_id.name, field.name)
                   # )
            form_name += ' - '.join(status_names) + ']'
        self.name=form_name
    
    @api.one
    @api.depends('form_inputs')
    def compute_form_fields(self):
        fields_ids=[]
        field_names=[]
        for form_input in self.form_inputs:
            if form_input.field:
                fields_ids.append(form_input.field.id)
                field_names.append(form_input.field.name)
        self.fields_names = json.dumps(field_names)
        self.fields_ids=[[6,0,fields_ids]]

    @api.multi
    def copy(self, default=None):
        if default is None:
            default = {}
        default['form_inputs'] = [(0, 0, {
            'field': form_input.field.id,
            'domain': form_input.domain,
            'search_by': [(6, 0, form_input.search_by.ids)],
            'limit_per_search': form_input.limit_per_search,
            'search_domain': form_input.search_domain,
            'input_type': form_input.input_type,
            'placeholder': form_input.placeholder,
            'parent_input': form_input.parent_input.id,
            'parent_field': form_input.parent_field.id,
            'parent_field_map': form_input.parent_field_map.id,
            'required': form_input.required,
            'readonly': form_input.readonly,
            'is_button': form_input.is_button,
            'button_text': form_input.button_text,
            'button_icon': form_input.button_icon,
            'button_function': form_input.button_function,
            'default_value': form_input.default_value,
            'have_image': form_input.have_image,
            'image_field_name': form_input.image_field_name.id,
            'is_separator': form_input.is_separator,
            'as_step': form_input.as_step,
            'separator_title': form_input.separator_title,
            'color': form_input.color,
            'background': form_input.background,
            'invisible': form_input.invisible,
            'invisible_domain': form_input.invisible_domain,
            'show_barcode_scanner': form_input.show_barcode_scanner,
            'show_location_selector': form_input.show_location_selector,
            'show_true_buyer_button': form_input.show_true_buyer_button,
            'min_length': form_input.min_length,
            'max_length': form_input.max_length,
            'sequence': form_input.sequence,
            'create_or_select': form_input.create_or_select,
            'position': form_input.position,
            'is_location':form_input.is_location,
            'is_signature':form_input.is_signature,
            'barcode_verified':form_input.barcode_verified,
            'is_auto_fill_mapping_fields_enabled':form_input.is_auto_fill_mapping_fields_enabled, 
            'mapping_relation_fields': [(0, 0, {
            'origin_model_fields': mapping_field.origin_model_fields.id,
            'inverse_model_fields': mapping_field.inverse_model_fields.id,
            'sequence': mapping_field.sequence
        }) for mapping_field in form_input.mapping_relation_fields] 
            
        }) for form_input in self.form_inputs]
        default['group_id']=False
        return super(rb_delivery_mobile_form_creator, self).copy(default=default)
    
    @api.onchange('model')
    def reset_form_inputs(self):
        self.form_inputs=[(6,0,[])]

    @api.one
    @api.depends('name')
    def _compute_is_public(self):
        if(self.name in ['login_form','signup_form']):
            self.is_public_form=True
        else:
            self.is_public_form=False

    @api.constrains('form_inputs')
    def update_card_fields(self):
        if self.form_name!="status_action_form":
            card_creator = self.find_related_card_creator()
            if card_creator:
                card_creator.compute_card_fields()
    
    def find_related_card_creator(self):
        card_creator = self.env['rb_delivery.mobile_card_creator'].search([['group_id','=',self.group_id.id],['model','=',self.model.id]])
        if card_creator:
            return card_creator
        return False
    
    @api.model
    def get_form_name(self,form_ids,group_id):
        if (form_ids and group_id):
            forms=self.sudo().search_read([['id','in',form_ids],['group_id','=',group_id]],['name'])
            if (forms and forms[0]):
                return forms[0]['name']
            else:
                return False
        else:
            return False
    @api.model
    def create(self, values):
        self._filter_valid_status_ids(values)
        record = super(rb_delivery_mobile_form_creator, self).create(values)
        return record


    @api.multi
    def write(self, values):
        for rec in self:
            rec._filter_valid_status_ids(values)
        return super(rb_delivery_mobile_form_creator, self).write(values)

    def _filter_valid_status_ids(self, values):
        if 'status_ids' in values:
            status_ids = values.get('status_ids', [])
            if status_ids and status_ids[0]:
                if type(status_ids[0]) is list or type(status_ids[0]) is tuple :
                    status_ids = status_ids[0][2] if values.get('status_ids') and len(status_ids[0]) > 2 else []
                else:
                    status_ids = status_ids[0] if values.get('status_ids') else []
                if status_ids:
                    model = False
                    if values.get('model'):
                        model = self.env['ir.model'].browse(values.get('model'))
                    else:
                        model = self.model

                    model_status_type = self.env['rb_delivery.status'].get_model_status_type(model.model)
                    if model_status_type and model_status_type != 'olivery_order':
                        status_ids = self.env['rb_delivery.status'].search([ ('id', 'in', status_ids),('status_type', '=', model_status_type),('collection_type', '=', self.env['rb_delivery.status'].get_model_collection_type(model.model))]).ids
                    else: 
                        status_ids = self.env['rb_delivery.status'].search([
                            ('id', 'in', status_ids),
                            ('status_type', '=', 'olivery_order')
                        ]).ids
                    values['status_ids'] = [[6, 0, status_ids]]   