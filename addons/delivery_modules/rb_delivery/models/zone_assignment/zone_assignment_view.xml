<odoo>
    <data>
  
      <record id="view_zone_assignment_kanban" model="ir.ui.view">
        <field name="name">rb.delivery.zone.load.kanban</field>
        <field name="model">rb_delivery.zone_assignment</field>
        <field name="arch" type="xml">
          <kanban class="o_kanban_small_column" default_group_by="zone_id" create="1" group_create="false">
  
            <field name="reference_name"/>
            <field name="order_count"/>
            <field name="zone_total_count"/> 
            <field name="reference_id"/> 
            <templates>
              <t t-name="kanban-group-header">
                <div>
                  <strong><field name="zone_id"/></strong><br/>
                  <small>Total Orders: <t t-esc="group.records[0].zone_total_count"/></small>
                </div>
              </t>
  
              <t t-name="kanban-box">
                <div class="oe_kanban_global_click">
                  <strong><field name="reference_name"/></strong><br/>
                  <t t-if="record.reference_id.raw_value != 0">
                    <span>Orders: <field name="order_count"/></span>
                  </t>
                </div>
              </t>
            </templates>
          </kanban>
        </field>
      </record>
  
    </data>
  </odoo>
  