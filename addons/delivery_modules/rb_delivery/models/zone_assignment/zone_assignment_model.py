# -*- coding: utf-8 -*-
from odoo import api, fields, models, _

class rb_delivery_zone_load_item(models.Model):
    _name = 'rb_delivery.zone_assignment'
    _description = 'Zone Load Item'
    _inherit = 'mail.thread'

    name = fields.Char(string="Name")
    zone_id = fields.Many2one('rb_delivery.area_zone', string="Zone", track_visibility="on_change")
    reference_model = fields.Selection([
        ('area', 'Area'),
        ('sub_area', 'Sub Area')
    ], required=True)
    reference_id = fields.Integer(string="Reference ID")
    reference_name = fields.Char(string="Reference Name")
    order_count = fields.Integer(string="Orders Count")
    zone_total_count = fields.Integer("Zone Order Total", compute="_compute_zone_total_count", store=False)


    @api.onchange('reference_name', 'reference_model')
    def _onchange_reference(self):
        self.name = f"{self.reference_name} ({self.reference_model})"
        
    @api.model
    def generate_zone_load_items(self):
        self.sudo().search([]).unlink()

        zone_load_vals = []
        order_obj = self.env['rb_delivery.order'].sudo()
        sub_area_obj = self.env['rb_delivery.sub_area'].sudo()
        zone_status_ids = self.env['rb_delivery.client_configuration'].get_param('zone_assignment_allowed_statuses')
        if not zone_status_ids:
            return
        zone_status_arr = self.env['rb_delivery.status'].browse(zone_status_ids).mapped('name')
        sub_area_zone_map = {
            sub.id: sub.zone_id.id for sub in sub_area_obj.sudo().search([])
        }

        zone_ids = self.env['rb_delivery.area_zone'].sudo().search([])
        for zone in zone_ids:
            has_data = False
            area_order_count = {}
            sub_area_order_count = {}
            orders = order_obj.search([
                '|',
                ('customer_area', 'in', zone.areas.ids),
                ('customer_sub_area', 'in', zone.sub_areas.ids),
                ('state', 'in', zone_status_arr)
            ])

            for order in orders:
                area = order.customer_area
                sub = order.customer_sub_area
                sub_zone_id = sub_area_zone_map.get(sub.id) if sub else None

                if sub and sub_zone_id:
                    if sub_zone_id == zone.id:
                        sub_area_order_count[sub.id] = sub_area_order_count.get(sub.id, 0) + 1
                if area and area.zone_id.id == zone.id:
                    if not sub or not sub_zone_id or sub_zone_id == zone.id:
                        area_order_count[area.id] = area_order_count.get(area.id, 0) + 1

            for area in zone.areas:
                count = area_order_count.get(area.id, 0)
                zone_load_vals.append({
                    'reference_model': 'area',
                    'reference_id': area.id,
                    'reference_name': f"Area: {area.name}",
                    'zone_id': zone.id,
                    'order_count': count,
                })
                has_data = has_data or count >= 0

            for sub in zone.sub_areas:
                count = sub_area_order_count.get(sub.id, 0)
                zone_load_vals.append({
                    'reference_model': 'sub_area',
                    'reference_id': sub.id,
                    'reference_name': f"Sub Area: {sub.name}",
                    'zone_id': zone.id,
                    'order_count': count,
                })
                has_data = has_data or count >= 0

            if not has_data:
                zone_load_vals.append({
                    'reference_model': 'area',
                    'reference_id': 0,
                    'reference_name': "No Data",
                    'zone_id': zone.id,
                    'order_count': 0,
                })

        if zone_load_vals:
            self.sudo().create(zone_load_vals)


    @api.depends('zone_id', 'order_count')
    def _compute_zone_total_count(self):
        data = self.sudo().read_group(
            [('zone_id', '!=', False)],
            ['order_count:sum'],
            ['zone_id']
        )
        zone_totals = {
            group['zone_id'][0]: group['order_count']
            for group in data
        }
        for rec in self:
            rec.zone_total_count = zone_totals.get(rec.zone_id.id, 0)

    @api.model
    def push_zone_changes_to_real_models(self):
        
        areas = self.env['rb_delivery.area'].sudo()
        sub_areas = self.env['rb_delivery.sub_area'].sudo()
        order_obj = self.env['rb_delivery.order'].sudo()

        zone_status_ids = self.env['rb_delivery.client_configuration'].get_param('zone_assignment_allowed_statuses')

        if not zone_status_ids:
            return
        zone_status_arr = self.env['rb_delivery.status'].browse(zone_status_ids).mapped('name')

        items = self.sudo().search([])
        if not items:
            return

        for item in items:
            if item.reference_model == 'area':
                area = areas.browse(item.reference_id)
                if area and area.zone_id.id != item.zone_id.id:
                    area.zone_id = item.zone_id.id

                    orders = order_obj.sudo().search([
                        ('customer_area', '=', area.id),
                        '|',
                        ('customer_sub_area', '=', False),
                        ('customer_sub_area.zone_id', '=', False),
                        ('state', 'in', zone_status_arr)
                    ])
                    if orders:
                        orders.sudo().write({'zone_id': item.zone_id.id})

            elif item.reference_model == 'sub_area':
                sub = sub_areas.browse(item.reference_id)
                if sub and sub.zone_id.id != item.zone_id.id:
                    sub.zone_id = item.zone_id.id
                    orders = order_obj.sudo().search([('customer_sub_area', '=', sub.id),('state', 'in', zone_status_arr)])
                    if orders:
                        orders.sudo().write({'zone_id': item.zone_id.id})
        self.generate_zone_load_items()
