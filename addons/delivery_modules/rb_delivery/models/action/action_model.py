# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class rb_delivery_action(models.Model):

    _name = 'rb_delivery.action'

    _description = "Action Model"

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    @api.model
    def get_action_selection(self):
        return [('send_on_registration','Send on registration'),('send_on_confirmation','Send on confirmation')]

    @api.model
    def get_action_type_selection(self):
        return [('for_action', 'For Action'),('for_status', 'For Status'),('for_collection', 'For Collection'),('for_edit', 'For Edit')]

    @api.model
    def get_field_name_selection(self):
        return [('field_cost', 'Cost'),('field_copy_total_cost', 'Copy Total Cost'),('field_customer_area', 'Customer Area'),('field_customer_sub_area', 'Customer Sub Area'),('field_customer_address', 'Customer Address'),('field_customer_mobile', 'Customer mobile')]
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    @api.model
    def get_default_status(self):
        default_status_name=""
        default_status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        if default_status:
           default_status_name=default_status.name
        return default_status_name

    order_state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    collection_state = fields.Many2one('rb_delivery.status', track_visibility="on_change",string="Collection Status")

    collection_type = fields.Selection([('collection','Collection'),('returned_collection','Returned Collection'),('agent_collection','Agent Collection'),('agent_returned_collection','Agent Returned Collection'),('runsheet_collection','Runsheet Collection')], 'Collection Type',track_visibility="on_change")

    @api.model
    def get_status(self):
        fields=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list

    header = fields.Char('Message Title', default="NO_TITLE")

    message = fields.Text(string="message")

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups,copy=False)

    action_type = fields.Selection(selection="get_action_type_selection", string="Action Type",default='for_status')

    actions = fields.Selection(selection="get_action_selection")

    action_template = fields.Many2one('mail.template', 'Action Templates', domain="[('model_id','=','rb_delivery.action')]")

    notify_customer = fields.Boolean("Notify Customer")

    order_user_template = fields.Many2one('mail.template', 'Order Templates', domain="['|',('model_id','=','rb_delivery.order'),('model_id','=','rb_delivery.user')]")

    collection_template = fields.Many2one('mail.template', 'Collection Templates', domain="[('model_id','=','rb_delivery.multi_print_orders_money_collector')]")

    returned_collection_template = fields.Many2one('mail.template', 'Returned Collection Templates', domain="[('model_id','=','rb_delivery.returned_money_collection')]")

    agent_collection_template = fields.Many2one('mail.template', 'Agent Collection Templates', domain="[('model_id','=','rb_delivery.agent_money_collection')]")

    agent_returned_collection_template = fields.Many2one('mail.template', 'Agent Returned Collection Templates', domain="[('model_id','=','rb_delivery.agent_returned_collection')]")

    runsheet_collection_template = fields.Many2one('mail.template', 'Runsheet Collection Templates', domain="[('model_id','=','rb_delivery.runsheet')]")


    is_sms = fields.Boolean('SMS')

    is_notification = fields.Boolean('Mobile Notification',default=True)

    is_web_notification = fields.Boolean('Web Notification',default=True)

    is_email = fields.Boolean('Email')

    field_name = fields.Selection(selection="get_field_name_selection", string="Field Name",default='field_customer_area')

    notification_sound = fields.Many2one('rb_delivery.notification_sound', string="Notification Sound")

    show_notification_timer = fields.Boolean('Show Notification Timer')

    notification_timer_duration = fields.Char('Notification Timer Duration')

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    @api.model
    def create(self, values):
        self.check_duplicate(values)
        return super(rb_delivery_action, self).create(values)

    @api.one
    def write(self, values):
        self.check_duplicate(values)
        return super(rb_delivery_action, self).write(values)

    def check_duplicate(self,values):
        if values.get('order_state') or values.get('group_id') or values.get('action_type') or values.get('collection_state') or values.get('collection_type') or values.get('actions'):
            group_id = values.get('group_id') if (values.get('group_id')) else self.group_id.id
            action_type = values.get('action_type') if (values.get('action_type')) else self.action_type
            actions = values['actions'] if values.get('actions') else self.actions
            domain = []
            if action_type == 'for_edit':
                field_name = values.get('field_name') if (values.get('field_name')) else self.field_name
                domain = [('group_id','=', group_id),('action_type','=',action_type),('field_name','=',field_name)]
            elif action_type == 'for_status':
                order_state = values.get('order_state') if (values.get('order_state')) else self.order_state
                domain = [('order_state','=', order_state),('group_id','=', group_id),('action_type','=',action_type)]
            elif action_type:
                collection_state = values.get('collection_state') if (values.get('collection_state')) else self.collection_state.id
                collection_type = values.get('collection_type') if (values.get('collection_type')) else self.collection_type
                domain = [('collection_type','=', collection_type),('collection_state','=', collection_state),('group_id','=', group_id),('action_type','=',action_type),('actions','=',actions)]

            if len(domain)>0:
                if self.id:
                    domain.append(('id','!=',self.id))
                action = self.env['rb_delivery.action'].sudo().search_count(domain)
                if action:
                    raise ValidationError(_('A notification with the same type, action, state and group already exists!'))

    @api.onchange('actions')
    def change_action(self):
        if self.actions == 'send_on_registration':
            self.is_notification = False
            self.is_email = True
        elif self.actions == 'send_on_confirmation':
            self.is_notification = False
            self.is_sms = True

    @api.onchange('notify_customer')
    def change_notify_customer(self):
        if self.notify_customer:
            self.is_notification = False
            self.is_email = False
            self.is_sms = True
            self.group_id = False


    @api.onchange('action_type')
    def change_action_type(self):
        self.order_state=False
        self.collection_state=False

    def get_confirm_action(self,mobile_number,user_id):
        confirm_action = self.env['rb_delivery.action'].sudo().search([('action_type','=','for_action'),('actions','=','send_on_confirmation')],limit=1)
        if confirm_action and confirm_action.actions == 'send_on_confirmation' and confirm_action.is_sms and mobile_number:
            message = confirm_action.message
            self.env['rb_delivery.sms'].sudo().send_sms(message ,mobile_number,user_id)

    def get_reg_action(self,email):
        email_action = self.env['rb_delivery.action'].sudo().search([('action_type','=','for_action'),('actions','=','send_on_registration')],limit=1)
        if email_action:
            if email_action.actions == 'send_on_registration':
                if email_action.is_email:
                    if email_action.template:
                        email_action.header = email_action.template.subject
                        email_action.message = email_action.template.body_html
                    if email:
                        company = self.env['res.company'].sudo().search([])
                        company_email = company[0].email
                        template_obj = self.env['mail.mail']
                        template_data = {
                            'subject': email_action.header,
                            'body_html': email_action.message,
                            'email_from': company_email,
                            'email_to': email}

                        template_id = template_obj.sudo().create(template_data)
                        template_id.send()

    @api.model
    def notify_for_action_type(self,action_type,action_name=False,state_name=False,collection_type=False,fields_changed=False,object=False,with_delay=True,attachment_ids=False):
        state_field_name = self.get_state_field_name(action_type)
        actions = False
        if state_field_name and state_name and action_type == 'for_status':
            actions = self.env['rb_delivery.action'].search([(state_field_name, '=', state_name),('action_type','=',action_type)])
        elif action_type == 'for_collection' and collection_type and state_name:
            status = self.env['rb_delivery.status'].search([('name','=',state_name),('collection_type','=',collection_type),('status_type','=','olivery_collection')])
            actions = self.env['rb_delivery.action'].search(
            [(state_field_name, '=', status.id),('action_type','=',action_type),('collection_type','=',collection_type)])
        elif action_type == 'for_edit' and len(fields_changed)>0:
            for field_name in fields_changed:
                actions = self.env['rb_delivery.action'].search(
                    [('field_name', '=', 'field_'+field_name),('action_type','=','for_edit')])
                self.do_actions(actions,object,attachment_ids,collection_type,with_delay)
            return
        elif action_type == 'for_action':
            actions = self.env['rb_delivery.action'].search(
            [('action_type','=','for_action'),('actions','=',action_name)])
        if actions:
            self.do_actions(actions,object,attachment_ids,collection_type,with_delay)


    def get_state_field_name(self,action_type):
        action_state_field_map = {
            'for_status':'order_state',
            'for_collection':'collection_state',
            'for_edit':False,
            'for_action':False,
        }

        return action_state_field_map[action_type]

    def do_actions(self,actions,object,attachment_ids,collection_type=False,with_delay=True):
        for action in actions:
            group = action.group_id
            notification_sound_id = action.notification_sound
            notify_customer = action.notify_customer
            message = ''
            header= ''
            if action.action_template:
                message = action.action_template.body_html
                header = action.action_template.subject
            elif action.order_user_template:
                message = action.order_user_template.body_html
                header = action.order_user_template.subject
            elif action.collection_template:
                message = action.collection_template.body_html
                header = action.collection_template.subject
            elif action.message:
                message = action.message
                header = action.header
            is_sms = action.is_sms
            is_email = action.is_email
            is_notification = action.is_notification
            is_web_notification = action.is_web_notification
            if with_delay:
                self.with_delay(channel="root.notification",max_retries=2).notify(action,object, group, header, message,attachment_ids,is_sms,is_email,is_notification,is_web_notification,notification_sound_id,notify_customer,collection_type)
            else:
                self.notify(action,object, group, header, message,is_sms,is_email,attachment_ids,is_notification,is_web_notification,notification_sound_id,notify_customer,collection_type)

    def notify(self,action,object, group, header, message,attachment_ids, is_sms, is_email, is_notification,is_web_notification,notification_sound_id,notify_customer,collection_type=False):
        notify_business_parent_enabled = self.env['rb_delivery.client_configuration'].get_param('notify_parent_on_business_message')
        notify_business_driver_enabled = self.env['rb_delivery.client_configuration'].get_param('notify_business_driver_enabled')

        players = []
        users = []
        collapse_id=action.id
        group = self.env['res.groups'].sudo().search([('id', '=', group.id)])

        # since here we need to view all users even userse we have no access to
        # e.g when create order from business it should send to maanger role ( which the business
        # role ) can not seee
        # ading sudo so the driver/business can send notification to user even if he
        # can not access the user information
        # Ensure that 'object' is an instance of the model and not None 
        if action:
            action_type = getattr(action, 'action_type', False)
            action_name = getattr(action, 'actions', False)
            status_name = getattr(action, 'order_state', False)
        else:
            action_type = False
            action_name = False
            status_name = False

        if action_type and action_name:
            notification_group_type = f"{action_type}_{action_name}"
        elif action_type and status_name:
            notification_group_type = f"{action_type}_{status_name}"
        else:
            notification_group_type = "Actions"

        if group.code == 'rb_delivery.role_business':
            if object:
                business = False
                if collection_type:
                    if object.sudo().business_id:
                        business=  object.sudo().business_id
                elif object.sudo().assign_to_business:
                    business=object.sudo().assign_to_business
                if business and self.user_has_access(business, object):
                    users.append(business)
                    if business and business.player_id:
                        players.append(str(business.player_id))
                    if notify_business_driver_enabled and business.driver_id and business.driver_id.player_id:
                        users.append(business.driver_id)
                        players.append(str(business.driver_id.player_id))


        if group.code in ['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative']:
            if object and object.sudo().assign_to_agent:
                driver=object.sudo().assign_to_agent
                if self.user_has_access(driver, object):
                    users.append(driver)
                    if driver.player_id:
                        players.append(str(driver.player_id))

        if group.code not in ['rb_delivery.role_driver','rb_delivery.role_business']:
            user_recs = self.env['rb_delivery.user'].sudo().search(
                [('group_id', '=', group.id)])
            for user_rec in user_recs:
                if self.user_has_access(user_rec, object):
                    users.append(user_rec)
                    if user_rec.player_id:
                        players.append(str(user_rec.player_id))

        emails = []
        mobile_numbers=[]
        for user in users:
            if user.email:
                emails.append(user.email)
            if user.mobile_number:
                mobile_numbers.append(user.mobile_number)

        if notify_customer:
            if not collection_type:
                if object.sudo().customer_mobile:
                    mobile_numbers.append(object.sudo().customer_mobile)

        sequence = object['sequence'] if object and 'sequence' in object else False
        model = str(object).split('(')[0] if object else False
        self.env['rb_delivery.notification_center'].notification(
            users, emails, players, mobile_numbers, header, message, is_sms, is_email, is_notification, object, sequence, model, attachment_ids, model, is_web_notification=is_web_notification, notification_sound_id=notification_sound_id, notification_group_type=notification_group_type ,collapse_id=collapse_id
        )
    def user_has_access(self,user, obj):
        if not user or not obj:
            return False
        try:
            return bool(obj.sudo(user.user_id.id).search_count([('id', '=', obj.id)]))
        except:
            return False
