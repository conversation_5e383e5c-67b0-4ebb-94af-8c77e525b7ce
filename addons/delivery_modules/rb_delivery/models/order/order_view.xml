<odoo>
    <data>

        <record id="view_form_rb_delivery_multi_assign_area" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_area</field>
            <field name="model">rb_delivery.multi_area</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                        <!-- <button name="select_area" confirm="Do you want to proceed?" type="object" string="Select Area" class="oe_highlight" /> -->
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="customer_area" String="Select Area" options="{'no_create': True, 'no_create_edit':True}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_area" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_multi_assign_sub_area" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_sub_area</field>
            <field name="model">rb_delivery.multi_sub_area</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="area_id" String="Area" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="customer_sub_area" String="Select Sub Area" options="{'no_create': True, 'no_create_edit':True}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_sub_area" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_multi_assign_to_agent" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_assign_to_agent</field>
            <field name="model">rb_delivery.multi_assign_to_agent</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                        <!-- <button name="select_agent" confirm="Do you want to proceed?" type="object" string="Select Agent" class="oe_highlight" /> -->
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="assign_to_agent" options="{'no_create': True, 'no_create_edit':True}" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_agent" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_multi_select_reschedule_date" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_select_reschedule_date</field>
            <field name="model">rb_delivery.multi_select_reschedule_date</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="reschedule_date"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_reschedule_date" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_calendar_rb_delivery_reschedule_order" model="ir.ui.view">
        <field name="name">view_calendar_rb_delivery_reschedule_order</field>
        <field name="model">rb_delivery.order</field>

        <field name="arch" type="xml">
            <calendar string="Calendar View" date_start="reschedule_date" mode="day"  quick_add="True" >
                <field name="sequence"/>
                <field name="reschedule_date"/>
                <field name="note" string="Note" />
                <field name="customer_area" string="Area" />

            </calendar>
        </field>

    </record>

        <!-- <record id="view_form_rb_delivery_multi_assign_to_collector" model="ir.ui.view">

                      <field name="name">view_form_rb_delivery_multi_assign_to_collector</field>
                      <field name="model">rb_delivery.multi_assign_to_collector</field>

                      <field name="arch" type="xml">

                        <form create="false" edit="false">

                          <header>
                            <button name="select_collector" confirm="Do you want to proceed?" type="object" string="Select Collector" class="oe_highlight" />
                          </header>

                          <sheet>

                            <group name="group_top">
                              <field name="assign_to_collector" />
                            </group>

                          </sheet>
                          <footer>
                          </footer>

                        </form>

                      </field>
                    </record>

                    <record id="view_form_rb_delivery_multi_assign_to_money_collector" model="ir.ui.view">

                      <field name="name">view_form_rb_delivery_multi_assign_to_money_collector</field>
                      <field name="model">rb_delivery.multi_assign_to_money_collector</field>

                      <field name="arch" type="xml">

                        <form create="false" edit="false">

                          <header>
                            <button name="select_money_collector" confirm="Do you want to proceed?" type="object" string="Select Money Collector" class="oe_highlight" />
                          </header>

                          <sheet>

                            <group name="group_top">
                              <field name="assign_to_money_collector" />
                            </group>

                          </sheet>
                          <footer>
                          </footer>

                        </form>

                      </field>
                    </record> -->

        <!-- for multi_print_orders_money_collector -->
        <!-- inherit module[olivery_branch_collection] -->
        <record id="view_form_rb_delivery_multi_print_orders_money_collector" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_print_orders_money_collector</field>
            <field name="model">rb_delivery.multi_print_orders_money_collector</field>

            <field name="arch" type="xml">

                <form create="false">

                    <header>
                        <button string="Change status" name="wkf_action_change_status" type="object" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_business,base.group_system"/>
                        <field name="state" widget="statusbar" statusbar_visible=" "/>
                    </header>

                    <sheet>
                    <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh" attrs="{'invisible':[('write_date', '=', False)]}">
                        <button type="object" name="get_signature" class="btn btn-sm oe_stat_button o_form_invisible">
                            <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                            <div class="o_form_field o_stat_info" data-original-title="" title="">
                                <span>Signature</span>
                            </div>
                    </button>
                    <button type="object" name="get_orders" class="btn btn-sm oe_stat_button o_form_invisible">
                        <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                        <div class="o_form_field o_stat_info" data-original-title="" title="">
                            <span>Orders</span>
                        </div>
                    </button>
                    </div>
                        <group name="group_top">
                            <group name="group-right">
                            <field name="write_date" invisible='1'/>
                                <field name="is_block_delivery_profit" invisible="1"/>
                                <field name="name"/>
                                <field name="create_uid" readonly="1" options="{'no_open': True}"/>
                                <field name="create_date" readonly="1"/>
                                <field name="business_id" readonly="1" />
                                <field name="business_name" readonly="1" invisible="1"/>
                                <field name="state" readonly="1"/>
                                <field name="previous_status" readonly="1"/>
                                <field name="driver_id" readonly="1"/>
                                <field name="previous_agent" readonly="1" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
                                <field name="is_block_delivery_fee" invisible="1"/>
                                <separator string="Payment:"/>
                                <field name="payment_type"/>
                                <field name="payment_detail" attrs="{'invisible':[('payment_type','=',False)]}"/>
                                <field name="payment_date"/>
                                <field name="bank_name"/>
                                <field name="bank_number"/>
                                <separator string="Totals:"/>
                                <field name="total_ammount" readonly="1"/>
                                <field name="total_cost" readonly="1"/>
                                <field name="total_delivery_cost" readonly="1" attrs="{'invisible':[('is_block_delivery_fee','=',True)]}"/>
                                <field name="total_money_collection_cost" readonly="1"/>
                                <field name="company_profit_total" readonly="1" attrs="{'invisible':[('is_block_delivery_profit','=',True)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting"/>
                                <field name="report_type" invisible="1" readonly="1"/>
                            </group>
                            <group name="group-left">
                            <div class="oe_right" >
                            <field name="use_qr_code" readonly="1" invisible="1"/>
                            <field name="barcode" style="display:block;text-align:center;width:200px" height="100" width="200" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('use_qr_code','=',True)]}"/>
                            <field name="qr_code_image" style="display:block;text-align:center;max-width:150px; max-height:150px !important" height="150" width="150" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('use_qr_code','=',False)]}"/>
                            <field name="sequence" string="Sequence Number" style="display:block;text-align:center;width:200px"/>
                            </div>
                            </group>
                        </group>
                        <group name="group_top">
                            <field name="is_collection_manager" invisible="1"/>
                            <field name="ability_to_edit_collection" invisible="1" />
                            <field name="is_configuration_manager" invisible = "1" />
                            <field name="order_ids" domain="[('assign_to_business','=',business_id)]" attrs="{'readonly': [('is_configuration_manager', '=', False),'|',('is_collection_manager', '=', False), ('ability_to_edit_collection', '=', False)]}">
                                <tree delete="1">
                                    <field name="sequence_related"/>
                                    <field name="reference_id" />
                                    <field name="assign_to_business"/>
                                    <field name="customer_name"/>
                                    <field name="customer_mobile"/>
                                    <field name="customer_area"/>
                                    <field name="customer_address"/>
                                    <field name="is_block_delivery_fee" invisible="1"/>
                                    <field name="money_collector" groups="rb_delivery.role_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>
                                    <field name="previous_agent"/>
                                    <field name="state"/>
                                    <field name="money_collection_cost" sum="Total Amount"/>
                                    <field name="delivery_cost" sum="Total Delivery Cost"/>
                                    <field name="required_from_business" sum="Required from business" groups="rb_delivery.role_super_manager,base.group_system,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                    <!-- History and communication: -->
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                    <footer></footer>

                </form>


            </field>
        </record>
        <record id="view_money_collection_form_block_delivery" model="ir.ui.view">
            <field name="name">view_money_collection_form_block_delivery</field>
            <field name="model">rb_delivery.multi_print_orders_money_collector</field>
            <field name="inherit_id" ref="view_form_rb_delivery_multi_print_orders_money_collector"/>
            <field name="groups_id" eval="[(6, 0, [ref('rb_delivery.role_olivery_block_delivery_fee')])]"/>
            <field name="arch" type="xml">
                <field name="order_ids" position="replace">
                    <field name="order_ids" domain="[('assign_to_business','=',business_id)]" attrs="{'readonly': [('is_configuration_manager', '=', False),'|',('is_collection_manager', '=', False),('ability_to_edit_collection', '=', False)]}">
                        <tree delete="1">
                      <field name="is_block_delivery_fee" />
                      <field name="sequence_related"/>
                      <field name="reference_id" />
                      <field name="assign_to_business"/>
                      <field name="customer_name"/>
                      <field name="customer_mobile"/>
                      <field name="customer_area"/>
                      <field name="customer_address"/>
                      <field name="previous_agent"/>
                      <field name="state"/>
                      <field name="money_collection_cost" sum="Total Amount" />
                      <field name="required_from_business" sum="Required from business" groups="rb_delivery.role_super_manager,base.group_system"/>
                    </tree>
                  </field>
                </field>
            </field>
        </record>
        <record id="view_form_rb_delivery_money_collection_send_email" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_money_collection_send_email</field>
            <field name="model">rb_delivery.money_collection_send_email</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">
                    <header>
                    </header>

                    <sheet>
                        <group>
                            <field name="sender_id"/>
                            <field name="partner_ids" widget="many2many_tags"/>
                            <field name="subject"/>
                            <field name="body"/>
                            <field name="attachment_ids" widget="many2many_binary"/>
                        </group>
                    </sheet>

                    <footer>
                        <button name="send_email" type="object" string="Confirm"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <!-- inherit module[olivery_branch_collection] -->
        <record id="view_form_rb_delivery_order_select_money_collection_state" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_select_money_collection_state</field>
            <field name="model">rb_delivery.select_money_collection_state</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                        <!-- <button name="select_state" confirm="Do you want to proceed?" type="object" string="Move to state" class="oe_highlight" /> -->
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="show_agent" invisible="1"/>
                            <field name="required_agent" invisible="1"/>
                            <field name="all_status_ids" invisible="1"/>
                            <field name="state" domain="[('id', 'in', all_status_ids)]" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="agent" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]" attrs="{'invisible': [('show_agent','=',False)],'required':[('required_agent','=',True)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_call_center,base.group_system,rb_delivery.role_accounting"  options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="show_closing_date" invisible='1' />
                            <field name="required_closing_date" invisible='1' />
                            <field name="closing_date" attrs="{'invisible': [('show_closing_date','=',False)],'required':[('required_closing_date','=',True)]}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_state" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>
        <record id="view_form_rb_delivery_multi_money_collection_assign_to_agent" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_money_collection_assign_to_agent</field>
            <field name="model">rb_delivery.money_collection_change_agent_wizard</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                        <!-- <button name="select_agent" confirm="Do you want to proceed?" type="object" string="Select Agent" class="oe_highlight" /> -->
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="assign_to_agent" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]" options="{'no_create': True, 'no_create_edit':True}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="select_agent" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

         <record id="view_form_rb_delivery_collection_detach_order" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_collection_detach_order</field>
            <field name="model">rb_delivery.collection_detach_order</field>
            <field name="arch" type="xml">

                <form create="false" edit="false">
                    <header>
                    </header>

                    <sheet>
                        <group name="group_top">
                            <separator string="Are you sure you want to clear orders?"/>
                        </group>
                    </sheet>

                    <footer>
                        <button name="detach_orders" type="object" string="Confirm"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>
            </field>
        </record>

        <!-- deprecated -->
        <record id="view_form_rb_delivery_multi_compute_area" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_multi_compute_area</field>
            <field name="model">rb_delivery.multi_compute_area</field>
            <field name="arch" type="xml">

                <form create="false" edit="false">
                    <header>
                    </header>

                    <sheet>
                        <group name="group_top">
                            <separator string="Are you sure you want to compute areas?"/>
                        </group>
                    </sheet>

                    <footer>
                        <button name="select_area" type="object" string="Confirm"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>
            </field>
        </record>

        <record id="view_form_rb_delivery_multi_print_orders_money_collector_two" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_multi_print_orders_money_collector_two</field>
            <field name="model">rb_delivery.create_money_collection</field>

            <field name="arch" type="xml">


                <form create="false" edit="false">

                    <header>
                        <!-- <button name="%(rb_delivery.report_rb_delivery_order_multi_print_orders_money_collector_action)d" type="action"
                                                                                        string="print" icon="fa-print"/> -->
                    </header>

                    <sheet>
                        <group name="group_top">
                            <field name="show_note" invisible="1"/>
                            <field name="name" string="Note" attrs="{'invisible': [('show_note','=',False)]}"/>
                            <separator attrs="{'invisible': [('show_note','=',True)]}" string="Are you sure?"/>
                        </group>
                    </sheet>

                    <footer>
                        <button name="create_money_collection" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>


        <record id="view_tree_rb_delivery_multi_print_orders_money_collector" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_multi_print_orders_money_collector</field>
            <field name="model">rb_delivery.multi_print_orders_money_collector</field>
            <field name="arch" type="xml">
                <tree create="false" class="collection_tree">
                <field name="is_block_delivery_fee" invisible="1"/>
                <field name="is_block_delivery_profit" invisible="1"/>
                    <field name="sequence" readonly="1"/>
                    <field name="name"/>
                    <field name="state" readonly="1"/>
                    <field name="create_uid" readonly="1" options="{'no_open': True}"/>
                    <field name="create_date" readonly="1"/>
                    <field name="business_name" readonly="1"/>
                    <field name="driver_id" readonly="1"/>
                    <field name="previous_agent" readonly="1" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
                    <field name="email_sent" readonly="1"/>
                    <field name="total_money_collection_cost" readonly="1" sum="Total ammount"/>
                    <field name="total_delivery_cost" readonly="1" sum="Total delivery fee"/>
                    <field name="company_profit_total" readonly="1" sum="Total company profit" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting"/>
                    <field name="total_cost" readonly="1" sum="Total"/>
                    <field name="order_count" sum="Orders"/>
                    <field name="previous_status" readonly="1"/>
                    <field name="bank_name" readonly="1"/>
                    <field name="holder_name" readonly="1"/>
                    <field name="bank_number" readonly="1"/>
                    <field name="status_last_updated_by" readonly="1" options="{'no_open': True}"/>
                </tree>
            </field>
        </record>

        <record id="view_tree_rb_delivery_multi_print_orders_money_collector_block_delivery" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_multi_print_orders_money_collector_block_delivery</field>
            <field name="model">rb_delivery.multi_print_orders_money_collector</field>
            <field name="inherit_id" ref="view_tree_rb_delivery_multi_print_orders_money_collector"/>
            <field name="groups_id" eval="[(6, 0, [ref('rb_delivery.role_olivery_block_delivery_fee') ])]"/>
            <field name="arch" type="xml">
                <field name="total_delivery_cost" position="attributes">
                    <attribute name="attrs">{'column_invisible':1}</attribute>
                </field>
            </field>
        </record>
        <record id="view_search_rb_delivery_multi_print_orders_money_collector" model="ir.ui.view">
            <field name="name">view_search_rb_delivery_multi_print_orders_money_collector</field>
            <field name="model">rb_delivery.multi_print_orders_money_collector</field>
            <field name="arch" type="xml">
                <search>
                    <group>
                        <field name="sequence"/>
                        <field name="create_uid" options="{'no_open': True}"/>
                        <field name="create_date"/>
                        <field name="business_id"/>
                        <field name="business_name"/>
                        <field name="mobile_number"/>
                        <field name="state"/>
                        <field name="email_sent"/>
                        <field name="total_cost"/>
                        <field name="total_ammount"/>
                        <field name="total_delivery_cost"/>
                        <field name="previous_agent"/>
                        <field name="previous_status"/>
                        <field name="driver_id"/>
                        <field name="business_driver_id" string="Business Driver"/>
                        <field name="payment_type"/>
                        <field name="business_id" filter_domain="['|',('business_id','ilike',self),('business_parent_id','ilike',self)]" string="Business Parent"/>
                    </group>
                    <group string="Filters">
                        <filter name="completed_collections" string="Completed Collections" domain="[('state','=','completed')]"/>
                        <filter name="prepaid_collections" string="Prepaid Collections" domain="[('is_pre_paid_collection','=',True)]"/>
                    </group>
                    <group string="Groups">
                        <filter name="group_by_previous_agent " string="By Previous Agent" icon="terp-partner" context="{'group_by':'previous_agent'}"/>
                        <filter name="group_by_previous_status" string="By Previous Status" icon="terp-partner" context="{'group_by':'previous_status'}"/>
                        <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
                        <filter name="group_by_day" string="By Date" icon="terp-partner" context="{'group_by':'create_date:day'}"/>
                        <filter name="group_by_closing_day" string="By Closing Day" icon="terp-partner" context="{'group_by':'close_date:day'}"/>
                        <filter name="group_by_assign_to_business" string="By Business Assigned" icon="terp-partner" context="{'group_by':'business_id'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_mobile_number" string="By Mobile Number" icon="terp-partner" context="{'group_by':'mobile_number'}"/>
                        <filter name="group_by_state" string="By State" icon="terp-partner" context="{'group_by':'state'}"/>
                        <filter name="group_by_email_sent" string="By Email Sent" icon="terp-partner" context="{'group_by':'email_sent'}"/>
                        <filter name="group_by_agent" string="By Agent" icon="terp-partner" context="{'group_by':'driver_id'}"/>
                        <filter name="group_by_business_agent" string="By Business Agent" icon="terp-partner" context="{'group_by':'business_driver_id'}"/>
                        <filter name="group_by_payment_type" string="By Payment Type" icon="terp-partner" context="{'group_by':'payment_type'}"/>
                        <filter name="group_by_bussiness_area" string="By Business Area" icon="terp-partner" context="{'group_by':'area_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_form_rb_delivery_order_multi_select_unarchive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_multi_select_unarchive</field>
      <field name="model">rb_delivery.order_select_unarchive</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you sure you want to unarchive ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_unarchive" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_order_multi_select_archive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_multi_select_archive</field>
      <field name="model">rb_delivery.order_select_archive</field>
        <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
                <separator string="Are you sure you want to archive ? "/>
            </group>
          </sheet>

          <footer>
            <button name="select_archive" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_order_set_to_delete" model="ir.ui.view">
      <field name="name">view_form_rb_delivery_order_set_to_delete</field>
      <field name="model">rb_delivery.set_to_delete</field>
        <field name="arch" type="xml">
        <form create="false" edit="false">
          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
                <separator string="Are you sure you want to set orders to deleted? "/>
            </group>
          </sheet>
          <footer>
            <button name="select_change_sequence" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>
        </form>
      </field>
    </record>

    <record id="view_form_rb_delivery_money_collection_multi_select_archive" model="ir.ui.view">

        <field name="name">view_form_rb_delivery_money_collection_multi_select_archive</field>
        <field name="model">rb_delivery.money_collection_select_archive</field>
          <field name="arch" type="xml">
          <form create="false" edit="false">
            <header>
            </header>
            <sheet>
              <group name="group_top">
                  <separator string="Are you sure you want to archive ? "/>
              </group>
            </sheet>
            <footer>
              <button name="select_archive" type="object" string="Save"/>
              <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
            </footer>

          </form>

        </field>
      </record>

    <record id="view_form_rb_delivery_order_multi_refresh_pricelist" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_multi_refresh_pricelist</field>
      <field name="model">rb_delivery.multi_refresh_pricelist</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">

              <separator string="Are you sure you want to recalculate delivery fee? "/>
            </group>
          </sheet>

          <footer>

          <button name="refresh_pricelist" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

        <record id="view_form_rb_delivery_order_select_state" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_select_state</field>
            <field name="model">rb_delivery.select_state</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                        <!-- <button name="select_state" confirm="Do you want to proceed?" type="object" string="Move to state" class="oe_highlight" /> -->
                    </header>

                    <sheet>

                        <group name="group_top">
                            <field name="show_reschedule_date" invisible="1"/>
                            <field name="show_customer_payment" invisible="1"/>
                            <field name="show_customer_payment_two" invisible="1"/>
                            <field name="show_payment_type" invisible="1"/>
                            <field name="show_payment_type_two" invisible="1"/>
                            <field name="show_agent" invisible="1"/>
                            <field name="show_note" invisible="1"/>
                            <field name="show_extra_agent_cost" invisible="1"/>
                            <field name="required_extra_agent_cost" invisible="1"/>
                            <field name="show_reject_reason" invisible="1"/>
                            <field name="required_reject_reason" invisible="1"/>
                            <field name="show_order_action" invisible="1"/>
                            <field name="required_order_action" invisible="1"/>
                            <field name="required_reschedule_date" invisible="1"/>
                            <field name="required_payment_type" invisible="1"/>
                            <field name="required_payment_type_two" invisible="1"/>
                            <field name="required_agent" invisible="1"/>
                            <field name="required_note" invisible="1"/>
                            <field name="required_customer_payment" invisible="1"/>
                            <field name="required_customer_payment_two" invisible="1"/>
                            <field name="state_id" invisible="1"/>
                            <field name="show_totals" invisible="1"/>
                            
                            <field name="state"/>
                            <field name="reschedule_date" attrs="{'invisible': [('show_reschedule_date','=',False)],'required':[('required_reschedule_date','=',True)]}"/>
                            <field name="order_action" attrs="{'invisible': [('show_order_action','=',False)],'required':[('required_order_action','=',True)]}"/>
                            <field name="reject_reason" attrs="{'invisible': [('show_reject_reason','=',False)],'required':[('required_reject_reason','=',True)]}" domain="['|',('status','in',[state_id]),('status','=',False)]"/>
                            <field name="stuck_comment" attrs="{'invisible': [('show_reject_reason','=',False)]}"/>
                            <field name="customer_payment" attrs="{'invisible': [('show_customer_payment','=',False)],'required':[('required_customer_payment','=',True)]}"/>
                            <field name="payment_type" attrs="{'invisible': [('show_payment_type','=',False)],'required':[('required_payment_type','=',True)]}"/>
                            <field name="customer_payment_two" attrs="{'invisible': [('show_customer_payment_two','=',False)],'required':[('required_customer_payment_two','=',True)]}"/>
                            <field name="payment_type_two" attrs="{'invisible': [('show_payment_type_two','=',False)],'required':[('required_payment_type_two','=',True)]}"/>
                            <field name="extra_agent_cost" attrs="{'invisible': [('show_extra_agent_cost','=',False)],'required':[('required_extra_agent_cost','=',True)]}"/>
                            <field name="agent" attrs="{'invisible': [('show_agent','=',False)],'required':[('required_agent','=',True)]}" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_data_entry,rb_delivery.role_call_center,base.group_system,rb_delivery.role_accounting,rb_delivery.role_warehouse_manager,rb_delivery.role_warehouse_employee,rb_delivery.role_warehouse_auditor"  options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="note" attrs="{'invisible': [('show_note','=',False)],'required':[('required_note','=',True)]}"/>
                            <field name="total_required_from_business" attrs="{'invisible':[('show_totals','=',False)]}"/>
                            <field name="total_money_collection_cost" attrs="{'invisible':[('show_totals','=',False)]}"/>
                            <field name="total_delivery_cost" attrs="{'invisible':[('show_totals','=',False)]}"/>
                            <field name="total_agent_cost" attrs="{'invisible':[('show_totals','=',False)]}"/>
                            <field name="total_required_to_company" attrs="{'invisible':[('show_totals','=',False)]}"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="btn_show_dialog_box" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="wizard_message_form" model="ir.ui.view">
            <field name="name">You Dialog Box Wizard Name</field>
            <field name="model">display.dialog.box</field>
            <field name="type">form</field>
            <field name="arch" type="xml">
            <form version="7.0" style="padding-top: 0px">
                <div class="row" style="padding-bottom: 5px; padding-top:5px; line-height: 2em;">
                    <div class="col-8">
                        <separator string="" colspan="6" style="text-align: left; font: normal normal bold 20px/44px Cairo; letter-spacing: 0px; color: #333333; opacity: 1;" />
                        <field name="warning_text" colspan="4" nolabel="1" readonly="1"  widget="html"/>
                        <field name="text" invisible="1" />
                        <newline/>
                        <separator colspan="6"/>
                        <newline/>
                    </div>
                    <div class="col-4 text-center">
                        <img class="olivery-warn-img" src="/rb_delivery/static/src/img/warning_triangle_icon.png" style="margin-top: 15px;max-height: 150px; width: 150px; object-fit:contain; overflow: hidden;" attrs="{'invisible':[['collection_text','=','']]}"/>
                    </div>
                    </div>
                    <newline/>
                    <div class="row" style="padding-bottom: 5px; padding-top:5px; line-height: 2em;">
                        <div class="col-12" style="padding:0px;">
                            <field name="collection_text" colspan="6" nolabel="1" readonly="1" widget="html"/>
                        </div>
                    </div>
                    <newline/>
                    <div class="row" style="padding-bottom: 5px; padding-top:5px; line-height: 2em;">
                        <div class="col-12" style="padding:0px;">
                            <field name="agent_collection_text" colspan="6" nolabel="1" readonly="1" widget="html" style="padding-right: 5px; padding-left:5px; color: red;"/>
                        </div>
                    </div>
                <footer style="text-align: center; padding:10px;">
                    <button name="select_state" type="object" string="Confirm" class="oe_highlight" style="border-radius: 12px;width: 150px;height: 45px;background: #FE8200 !important;"/>
                    <button special="cancel" string="Cancel" style="width: 140px;height: 45px;border-radius: 12px;border: 1px solid #DDE1E6;background: #FFFFFF;"/>
                </footer>
            </form>
            </field>
        </record>

        <record id="view_form_rb_delivery_order_create_replacement" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_create_replacement</field>
            <field name="model">rb_delivery.create_replacement</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>
                    Do you want to proceed?

                    </sheet>
                    <footer>
                       <button name="create_replacement" type="object" string="Replacement" class="oe_highlight" />
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_order_change_to_previous_status" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_change_to_previous_status</field>
            <field name="model">rb_delivery.change_to_previous_status</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>
                    <field name="text"/>

                    </sheet>
                    <footer>
                       <button name="change_to_previous_status" type="object" string="Change To Previous Status" class="oe_highlight" />
                       <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_order_change_to_previous_agent" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_change_to_previous_agent</field>
            <field name="model">rb_delivery.change_to_previous_agent</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>
                    <field name="text"/>

                    </sheet>
                    <footer>
                       <button name="change_to_previous_agent" type="object" string="Change To Previous Agent" class="oe_highlight" />
                       <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>

        <record id="view_form_rb_delivery_order_create_returned" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_order_create_returned</field>
            <field name="model">rb_delivery.create_returned</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">

                    <header>
                    </header>

                    <sheet>
                    Do you want to proceed?
                    </sheet>

                    <footer>
                       <button name="create_returned" type="object" string="Returned" class="oe_highlight" />
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- inherit module [olivery_vat]
             inherit module [olivery_vhub]
             inherit module [olivery_cargo]
             inherit module [olivery_warehouse]
             inherit module [olivery_sale_commission]
             inherit module [olivery_recepient]
             inherit module [olivery_billing]
             inherit module [olivery_number_of_pieces]
             inherit module [olivery_naqel]
             inherit module [olivery_delivery_repetition]
             inherit module [olivery_international]
             inherit module [olivery_compound_order]
             inherit module [olivery_logetech]
             inherit module [olivery_eta]
             inherit module [storex_modules]
             inherit module[olivery_branch_collection]-->
        <record id="view_form_rb_delivery_order" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order</field>
            <field name="model">rb_delivery.order</field>

            <field name="arch" type="xml">

                <form>

                    <header>
                        <!-- Buttons and status widget -->
                        <button string="change order status" name="wkf_action_change_status" type="object" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_junior_accounting,base.group_system"/>
                        <button string="generate tracking link" name="generate_tracking_link" type="object" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_business,base.group_system"/>
                        <button string="refresh" name="wkf_refresh" type="object" groups="rb_delivery.role_super_manager,base.group_system" invisible="1"/>
                        <button string="migrate" name="wkf_migrate" type="object" groups="rb_delivery.role_super_manager,base.group_system" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible=" "/> </header>

                    <sheet>

                        <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh" attrs="{'invisible':[('write_date', '=', False)]}">
                            <button type="object" name="get_signature" class="btn btn-sm oe_stat_button o_form_invisible">
                                <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Signature</span>
                                </div>
                            </button>

                            <button type="object" name="get_order_attachment" class="btn btn-sm oe_stat_button o_form_invisible">
                                <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Attachment</span>
                                </div>
                            </button>
                            <button type="object" name="get_kashf_tahseel" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('collection_id', '=', False)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_junior_accounting">
                                <div class="fa fa-fw fa-file-text o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Collection</span>
                                </div>
                            </button>
                            <button type="object" name="get_run_sheet" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('runsheet_collection_id', '=', False)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw fa-file o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Run sheet</span>
                                </div>
                            </button>
                            <button type="object" name="get_returned_collection"  attrs="{'invisible':[('returned_collection_id', '=', False)]}" class="btn btn-sm oe_stat_button o_form_invisible" groups="rb_delivery.role_manager,rb_delivery.role_business,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw fa-file-o o_button_icon" style="margin:0px !important; padding:0px !important; width:33px !important;"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="" style="margin:0px !important; padding:0px !important; ">
                                    <span>Returned </span>
                                    <span>collection</span>
                                </div>
                            </button>
                             
                            <button type="object" name="get_agent_collection" attrs="{'invisible':[('agent_collection_id', '=', False)]}" class="btn btn-sm oe_stat_button o_form_invisible" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_accounting,base.group_system,rb_delivery.role_junior_accounting">
                                <div class="fa fa-fw fa-file-text o_button_icon" style="margin:0px !important; padding:0px !important; width:33px !important;"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="" >
                                    <span>Agent </span>
                                    <span>collection</span>
                                </div>
                            </button>
                            <button type="object" name="get_agent_returned_collection" attrs="{'invisible':[('agent_returned_collection_id', '=', False)]}" class="btn btn-sm oe_stat_button o_form_invisible" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw fa-file-o o_button_icon" style="margin:0px !important; padding:0px !important; width:33px !important;"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="" style="white-space:pre-wrap">
                                    <span>Agent </span>
                                    <span>Returned </span>
                                    <span>collection</span>
                                </div>
                            </button>
                            <field name="can_see_logs_button" invisible="1"/>
                            <button attrs="{'invisible':[('can_see_logs_button', '=', False)]}" type="object" name="get_logs" class="btn btn-sm oe_stat_button o_form_invisible">
                                <div class="fa fa-fw fa-history o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Logs</span>
                                </div>
                            </button>
                            <button type="object" name="get_replacement_order" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('replacement_reference', '=', []),('clone_reference', '=', False)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw  fa-clone  o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Replacement</span>
                                </div>
                            </button>

                            <button type="object" name="get_returned_order" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':[('returned_order', '=', False),('returned_clone_reference','=',False)]}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw  fa-clone  o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span>Returned</span>
                                </div>
                            </button>

                            <button type="object" name="get_chat_message" class="btn btn-sm oe_stat_button o_form_invisible" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system">
                                <div class="fa fa-fw  fa-comments  o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span class="o_stat_text">Chat</span>
                                </div>
                            </button>

                            <button type="object" name="get_location" class="btn btn-sm oe_stat_button o_form_invisible" attrs="{'invisible':['|',('longitude', '=', False),('latitude','=',False)]}">
                                     <div class="fa fa-fw fa-location-arrow o_button_icon"/>
                                     <div class="o_form_field o_stat_info" data-original-title="" title="">
                                  <span class="o_stat_text">Map</span>
                                 </div>
                            </button>

                            <button type="object" name="get_notification" class="btn btn-sm oe_stat_button o_form_invisible">
                                <div class="fa fa-fw fa-bell o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span class="o_stat_text">Notifications</span>
                                </div>
                            </button>
                            <button type="object" name="get_onboarding_error_ids" class="btn btn-sm oe_stat_button o_form_invisible"  attrs="{'invisible':[('onboarding_error_ids', '=', [])]}">
                                <div class="fa fa-fw fa-warning o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span class="o_stat_text">Issues</span>
                                </div>
                            </button>
                            <button type="object" name="get_mobile_scan_logs" class="btn btn-sm oe_stat_button o_form_invisible">
                                <div class="fa fa-fw fa-history o_button_icon"/>
                                <div class="o_form_field o_stat_info" data-original-title="" title="">
                                    <span class="o_stat_text">Mobile Scan Logs</span>
                                </div>
                            </button>
                        </div>
                        <div class="oe_title">
                        <field name="returned_clone_reference" invisible="1"/>
                        <field name="clone_reference" invisible="1"/>
                        <field name="replacement_reference" invisible="1"/>
                        <field name="collection_id" invisible="1"/>
                        <field name="returned_collection_id" invisible="1"/>
                        <field name="agent_returned_collection_id" invisible="1"/>
                        <field name="agent_collection_id" invisible="1"/>
                        <field name="runsheet_collection_id" invisible="1"/>
                        <field name="show_sender_address_in_waybill" invisible="1"/>
                            <field name="write_date" invisible='1'/>
                            <field name="ref_priority" invisible='1'/>
                            <field name="seq_priority" invisible='1'/>
                            <field name="onboarding_error_ids" invisible="1"/>
                            <div style="width:100%">

                            <div style="display:inline-block !important; width:50% !important">
                            <field name="use_qr_code" readonly="1" invisible="1"/>
                            <field name="barcode" style="display:block;text-align:center;width:100%;  height:75px !important" height="75" width="100%" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|','|',('write_date', '=', False),('seq_priority', '!=', True),('use_qr_code','=',True)]}"/>
                            <field name="qr_code_image" style="display:block;text-align:center;max-width:150px; max-height:150px !important" height="150" width="150" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|','|',('write_date', '=', False),('seq_priority', '!=', True),('use_qr_code','=',False)]}"/>
                            <label for="seqeuence" string="Seqeuence" style="width:100% !important;text-align:center !important;" attrs="{'invisible': ['|',('id', '=', False),('sequence','=',False)]}"/>
                            <field name="sequence" string="Sequence Number" style="display:block;text-align:center;" attrs="{'invisible':[('ref_priority', '=', True),('seq_priority','!=',True)]}" readonly="1"/>
                            </div>

                            <div style="width:50% !important;display:inline-block !important" attrs="{'invisible':['|',('id','!=',True),'|',('reference_id','!=',True),('ref_priority', '!=', True)]}">
                            <field name="barcode_reference" string="Reference Number" style="display:block;text-align:center;width:100%;height:75px !important" height="75" width="100%" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|',('write_date', '=', False),'|','|',('reference_id','=',False),('ref_priority','!=',True),('use_qr_code','=',True)]}"/>
                            <field name="qr_code_reference" style="display:block;text-align:center;max-width:150px; max-height:150px !important" height="150" width="150" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|',('id','=',False),'|','|',('reference_id','=',False),('ref_priority','=',False),('use_qr_code','=',False)]}"/>
                            <label for="reference_id" string="Reference" style="width:100% !important;text-align:center !important;" attrs="{'invisible':['|',('write_date', '=', False),'|',('reference_id','=',False),('ref_priority','!=',True)]}"/>
                            <field name="reference_id" style="display:block;text-align:center;" attrs="{'invisible':['|',('write_date', '=', False),'|',('reference_id','=',False),('ref_priority', '=', False)]}" readonly="True"/>
                            </div>

                            <div style="width:50% !important;display:inline-block !important" attrs="{'invisible':['|',('id','=',False),('partner_reference_id','=',False)]}">
                            <field name="partner_reference_id_barcode" string="Partner Reference Number" style="display:block;text-align:center;width:200px;height:75px !important;" height="75" width="200" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|','|',('id','=',False),('partner_reference_id','=',False),('use_qr_code','=',True)]}"/>
                            <field name="partner_reference_id_qr_code" style="display:block;text-align:center;max-width:150px; max-height:150px !important" height="150" width="150" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':['|','|',('id','=',False),('partner_reference_id','=',False),('use_qr_code','=',False)]}"/>
                            <label for="partner_reference_id" string="Partner Reference" style="width:100% !important;text-align:center !important;" attrs="{'invisible':['|',('id','=',False),('partner_reference_id','=',False)]}"/>
                            <field name="partner_reference_id" style="display:block;text-align:center;width:200px;" attrs="{'invisible':['|',('id','=',False),('partner_reference_id','=',False)]}" readonly="True"/>
                            </div>

                            </div>
                            <field name="financial_state" style="margin:10px;padding:5px;width:200px" />
                        </div>
                        <sheet>
                            <div class="p-3" style="border: 1px solid #000; border-radius: 10px;">
                                <group class="sender_group" name="group_top" string="Sender" style="margin:0px">
                                    <group name="group-right">
                                        <field name="is_business" invisible='1'/>
                                        <field name="has_children" invisible='1'/>
                                        <field name="business_issues" invisible="1"/>
                                        <field name="seq_exist" nolabel="1" style="background-color:#D9534F; text-align:center; color:white; height: 40px;width:320px" class="oe_edit_only" readonly='1' attrs="{'invisible':['|',('seq_exist', '=', ''),('seq_exist', '=', False)]}"/>

                                        <field name="reference_id" attrs="{'invisible':[('ref_priority', '!=', True)]}"/>
                                        <field name="assign_to_business" attrs="{'readonly':[('is_business','=',True),('has_children','=',False)]}" domain="[('role_code','=','rb_delivery.role_business')]"/>
                                        <span attrs="{'invisible':['|',('business_issues', '=', ''),('business_issues', '=', False)]}" style="color:#D9534F; height: 40px;width:320px">The business have some issues, for more information check issues section in user record.</span>
                                        <field name="show_alternate_address" invisible="1"/>
                                        <field name="error_log" invisible="1"/>
                                        <field name="show_alt_address" attrs="{'invisible':[('show_alternate_address', '=', False)]}"/>
                                        <field name="business_area" string="Business Area" attrs="{'invisible':[('show_alt_address', '=', True)]}"/>
                                        <field name="business_address" string="Business Address" attrs="{'invisible':[('show_alt_address', '=', True)]}"/>
                                        <field name="business_sub_area" string="Business Sub Area" attrs="{'invisible':[('show_alt_address', '=', True)]}"/>
                                        <field name="alt_business_name"  attrs="{'invisible':[('show_alt_address', '=', False)]}"/>
                                        <field name="alt_mobile_number"  attrs="{'invisible':[('show_alt_address', '=', False)],'required':[('show_alt_address', '=', True)]}"/>
                                        <field name="business_alt_area" string="Business Area" attrs="{'invisible':[('show_alt_address', '=', False)],'required':[('show_alt_address', '=', True)]}"/>
                                        <field name="business_alt_address" string="Business Address" attrs="{'invisible':[('show_alt_address', '=', False)],'required':[('show_alt_address', '=', True)]}"/>
                                        <field name="business_alt_sub_area" string="Business Sub Area" domain="[('parent_id', '=',business_alt_area)]" attrs="{'invisible':[('show_alt_address', '=', False)], 'readonly':[('business_alt_area','=',False)]}"/>
                                        <field name="business_alt_location"  attrs="{'invisible':[('show_alt_address', '=', False)]}" widget="url"/>
                                        <field name="business_alt_longitude"  attrs="{'invisible':[('show_alt_address', '=', False)]}"/>
                                        <field name="business_alt_latitude"  attrs="{'invisible':[('show_alt_address', '=', False)]}"/>
                                    </group>
                                    <group name="group-left" col="2">
                                        <field name="business_mobile_number" string="First Business Mobile"/>
                                        <field name="second_business_mobile_number" string="Second Business Mobile"/>
                                        <field name="business_inclusive_delivery"/>
                                        <field name="show_follower_info" invisible="1"/>
                                    </group>
                                </group>
                            </div>
                            <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;" attrs="{'invisible':[('show_follower_info','=',False)]}">
                                <group name="group_top" string="Follower" class="follower_group">
                                    <group name="group-right">
                                        <field name="longitude" invisible="1"/>
                                        <field name="latitude" invisible="1"/>
                                        <field name="follower_store_name"/>
                                        <field name="follower_mobile_number"/>
                                        <field name="follower_second_mobile_number"/>
                                        <field name="follower_area"/>
                                        <field name="follower_address"/>
                                        <field name="follower_ref_id"/>
                                        <field name="follower_latitude"/>
                                        <field name="follower_longitude"/>
                                        <button name="get_location" type="object" string="Map" help="show location on map" attrs="{'invisible':[('longitude','=',False),('latitude','=',False)]}"></button>
                                    </group>
                                </group>
                            </div>
                            <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;">
                                <group name="group_top" string="Customer" class="customer_group">
                                    <group name="group_right">
                                        <field name="receiver_business" attrs="{'invisible':[('receiver_is_business','!=',True)]}"/>
                                        <field name="customer_name" string="Customer Name"/>
                                        <field name="show_country" invisible="1" />
                                        <field name="customer_country" string="Customer Country"
                                            attrs="{'invisible':[('show_country','=',False)]}"
                                            options="{'no_create': True, 'no_create_edit':True}" />
                                        <field name="address_tag" />
                                        <field name="customer_area" options="{'no_create': True, 'no_create_edit':True}" string="Customer Area" attrs="{'readonly':[('business_area','=',False)]}"/>
                                        <field name="show_customer_sub_area" invisible="1"/>
                                        <field name="customer_sub_area" string="Customer Sub Area"  domain="[('show_in_create', '=', True)]" attrs="{'readonly':[('business_area','=',False)],'invisible':[('show_customer_sub_area','!=',True)]}" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="show_customer_district" invisible="1"/>
                                        <field name="customer_district_id" domain="[('show_in_create', '=', True)]" attrs="{'readonly':[('business_area','=',False)],'invisible':[('show_customer_district','!=',True)]}" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="customer_address" string="Customer Address" />
                                        <field name="show_customer_village" invisible="1"/>
                                        <field name="customer_village" attrs="{'invisible':[('show_customer_village','!=',True)]}"/>
                                        <field name="show_customer_neighbour" invisible="1"/>
                                        <field name="customer_neighbour" attrs="{'invisible':[('show_customer_neighbour','!=',True)]}"/>
                                        <field name="show_customer_building_number" invisible="1"/>
                                        <field name="customer_building_number" attrs="{'invisible':[('show_customer_building_number','!=',True)]}"/>
                                    </group>
                                    <group name="group_left" col="2">
                                        <field name="customer_mobile" string="First Customer Mobile"/>
                                        <field name="second_mobile_number" string="Second Customer Mobile"/>
                                        <field name="cus_whatsapp_mobile"/>
                                        <field name="cus_second_whatsapp_mobile" attrs="{'invisible':[('is_business','=',True)]}"/>
                                        <field name="receiver_is_business" />
                                    </group>
                                </group>
                            </div>
                        </sheet>
                        <group col="4">
                            <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;">
                                <!-- inherit module [olivery_advance_calculations] -->
                                <pre attrs="{'invisible':[('pricing_message','=','')]}">
                                    <field style="color:red" name="pricing_message" colspan="2" nolabel="1" readonly="1" widget="html"/>
                                </pre>
                                <group name="group_top" class="cost" string="Cost">
                                    <group name="group_left">
                                        <field name="is_block_delivery_fee" invisible="1"/>
                                        <field name="show_only_total" invisible="1"/>
                                        <field name="is_data_entry" invisible="1"/>
                                        <field name="show_inclusive_delivery" invisible="1"/>
                                        <field name="hide_paid_field" invisible="1"/>
                                        <field name="paid" attrs="{'invisible': [('hide_paid_field', '=', True)]}"/>
                                        <field name="inclusive_delivery" attrs="{'invisible':[('show_inclusive_delivery','=',False)]}"/>
                                        <field name="cod_required_field" invisible="1"/>
                                        <field name="cost" attrs="{'invisible':[('inclusive_delivery','=',True)],'required':[('cod_required_field', '=', True)]}"/>
                                        <field name="copy_cost" attrs="{'readonly':[('inclusive_delivery','=',True)]}" invisible="1"/>
                                        <field name="copy_total_cost" attrs="{'invisible':[('inclusive_delivery','!=',True)],'required':[('cod_required_field', '=', True)]}"/>
                                        <field name="total_cost" attrs="{'readonly':[('inclusive_delivery','!=',True)]}" invisible="1"/>
                                        <field name="money_collection_cost"/>
                                        <field name="customer_payment" attrs="{'readonly':[('is_business','=',True)]}"/>
                                        <field name="customer_discount"/>
                                        <field name="required_from_business" string="Required from business" attrs="{'invisible':['|','&amp;',('is_block_delivery_fee','=',True),('write_date','=',False),('show_only_total','=',True)]}"/>
                                    </group>

                                    <group name="group_right">
                                        <field name="show_delivery_cost_on_sender" invisible="1"/>
                                        <field name="delivery_cost_on_sender" attrs="{'invisible':['|',('show_delivery_cost_on_sender','=',False),('business_inclusive_delivery','=',True)]}"/>
                                        <field name="delivery_cost_on_customer" attrs="{'invisible':['|',('inclusive_delivery','=',False),('paid','=',False)]}"/>
                                        <field name="delivery_cost" attrs="{'invisible':['|',('is_block_delivery_fee','=',True),('show_only_total','=',True)]}"/>
                                        <field name="service_fee" attrs="{'invisible':[('is_block_delivery_fee','=',True)]}"/>
                                        <field name="show_extra_service_fee_on_sender" invisible="1"/>
                                        <field name="show_extra_service_fee_on_customer" invisible="1"/>
                                        <field name="extra_service_fee_on_sender" attrs="{'invisible':['|',('is_block_delivery_fee','=',True), ('show_extra_service_fee_on_sender','=',False)], 'readony': [('is_business','=',True)]}"/>
                                        <field name="extra_service_fee_on_customer" attrs="{'invisible':['|',('is_block_delivery_fee','=',True), ('show_extra_service_fee_on_customer','=',False)]}"/>
                                        <field name="extra_cost" attrs="{'invisible':['|',('is_block_delivery_fee','=',True),('is_business','=',True)]}"/>
                                        <field name="discount" attrs="{'invisible':['|',('is_block_delivery_fee','=',True),('is_business','=',True)]}"/>
                                        <field name="returned_discount" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" attrs="{'invisible':[('returned_discount','=',False)]}"/>
                                         <field name="returned_value" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system" attrs="{'invisible':[('returned_value','=',False)]}"/>
                                        <field name="returned_discount_statuses" invisible="1"/>
                                    </group>
                                    <group name="receiveables"></group>
                                </group>
                            </div>
                        </group>
                        <group>
                            <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;">
                                <group name="group_top" string="Order Details" class="order_details">
                                    <group
                                        name="group-left">
                                        <field name="show_order_type" invisible="1"/>
                                        <field name="order_type_id" attrs="{'invisible':[('show_order_type','=',False)]}"/>
                                        <field name="order_weight"/>
                                        <field name="partner_status"/>
                                        <field name="is_configuration_manager" invisible="1"/>
                                        <field name="is_partner_order" attrs="{'invisible':[('is_configuration_manager','=',False)]}"/>
                                        <field name="is_sender_partner_order" attrs="{'invisible':[('is_configuration_manager','=',False)]}"/>
                                        <field name="reschedule_date" string="Scheduled Delivery" autocomplete="off" attrs="{'readonly':[('is_business','=',True)]}"/>
                                        <field name="show_amount" invisible="1"/>
                                        <field name="show_replacement_order" invisible="1"/>
                                        <field name="readonly_replacement_order" invisible="1"/>
                                        <field name="show_returned_order" invisible="1"/>
                                        <field name="no_of_items" attrs="{'invisible':[('show_amount','!=',True)]}"/>
                                        <field name="order_action"/>

                                    </group>
                                        <group name="group-right">
                                        <field name="show_service" invisible="1"/>
                                        <field name="all_service_ids" invisible="1"/>
                                            <field
                                            name="assign_to_agent" attrs="{ 'required':[('state', '=', 'in_progress')]}" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting,rb_delivery.role_data_entry,rb_delivery.role_call_center"/>
                                            <field name="current_drivers" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting,rb_delivery.role_data_entry,rb_delivery.role_call_center" widget="many2many_tags"/>
                                            <field name="reference_id"/>
                                            <field name="service" domain="[('id', 'in', all_service_ids)]" string="Services" widget="many2many_tags" options="{'no_create': True, 'no_create_edit':True}" attrs="{'invisible':[('show_service','!=',True)]}"/>
                                            <field name="default_service" options="{'no_create': True, 'no_create_edit':True}" attrs="{'invisible':[('show_service','!=',True)]}"/>
                                            <field name="status_last_updated_by" options="{'no_open': True}"/>
                                            <field name="status_last_updated_on"/>
                                            <field name="show_agent_commercial_number" invisible="1"/>
                                            <field name="agent_commercial_number" attrs="{'invisible':[('show_agent_commercial_number','!=',True)]}"/>
                                        <field name="is_replacement" invisible="1"/>
                                        <field name="is_returned" invisible="1"/>
                                        <field name="replacement_order" attrs="{'invisible':['|','|',('show_replacement_order','!=',True),('is_replacement','=',True),('is_returned','=',True)],'readonly':[('readonly_replacement_order','=',True),('is_configuration_manager','=',False)]}"/>
                                        <field name="returned_order" attrs="{'invisible':['|','|','|','&amp;',('is_business','=',True),('assign_to_agent','!=',False),('is_replacement','=',True),('is_returned','=',True),('show_returned_order','!=',True)]}"/>
                                        </group>
                                </group>
                            </div>
                        </group>
                        <group>
                            <div class="p-3 mt-3" style="border: 1px solid #000; border-radius: 10px;">
                                <group name="group_top" string="Notes" class="notes_group">
                                    <group name="group_top">
                                        <field name="is_manager" invisible="1"/>
                                        <field
                                        name="is_super_manager" invisible="1"/>
                                        <field name="description_tags" string="Description Tags" widget="many2many_tags" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="note"/>
                                        <field name="product_note"/>
                                        <field name="reject_reason" attrs="{'invisible': [('is_business','=',True),('write_date','=',False)]}" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="special_note" attrs="{'invisible': ['|',('is_manager','!=',True),('is_super_manager','!=',True)]}"/>
                                        <field name="stuck_comment" string="returned note" attrs="{'invisible': [('is_business','=',True),('write_date','=',False)]}"/>
                                        <field name="solve_stuck_comment"  attrs="{'invisible': [('is_business','!=',True)]}"/>
                                        <field name="driver_note"/>
                                        <field name="show_picking_time" invisible="1"/>
                                        <field name="picking_from_time" attrs="{'invisible':[('show_picking_time','!=',True)]}"/>
                                        <field name="picking_to_time" attrs="{'invisible':[('show_picking_time','!=',True)],'readonly':[('picking_from_time','=',False)]}"/>
                                        <field name="show_delivery_time" invisible="1"/>
                                        <field name="delivery_from_time" attrs="{'invisible':[('show_delivery_time','!=',True)]}"/>
                                        <field name="delivery_to_time" attrs="{'invisible':[('show_delivery_time','!=',True)],'readonly':[('delivery_from_time','=',False)]}"/>
                                    </group>
                                </group>
                            </div>
                        </group>

                        <notebook>
                            <page string="Location">
                                <group name="group_top">
                                    <field name="location_url" widget="url"/>
                                    <field name="customer_location" widget="url"/>
                                    <field name="latitude"/>
                                    <field name="longitude"/>
                                    <field name="tracking_url" widget="url" readonly="1"/>
                                </group>
                            </page>
                            <page string="Accounting" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting" attrs="{'invisible':['|',('is_block_delivery_profit','=',True),('is_block_delivery_fee','=',True)]}">
                                <group name="group_top">
                                    <group name="group-left">
                                        <field name="is_block_delivery_profit" invisible="1"/>
                                        <field name="agent_cost"/>
                                        <field name="pickup_agent_cost"/>
                                        <field name="delivery_profit" attrs="{'invisible':['|','|','|', ('is_block_delivery_profit','=',True),('is_block_delivery_fee','=',True),('is_data_entry','=',True),('is_business','=',True)]}" />
                                        <field name="required_to_company" attrs="{'invisible':[('is_block_delivery_profit','=',True)]}"/>
                                        <field name="extra_agent_cost"/>
                                    </group>
                                    <group name="group-right">
                                        <field name="currency_id"/>
                                    </group>
                                </group>
                                <group name="group_top">
                                    <group name="group-left">
                                        <field name="commission" />
                                        <field name="extra_commission" />
                                    </group>
                                </group>
                            </page>
                                <page string="Others">
                                    <group name="group-top">
                                    <group name="group-left">
                                        <field name="payment_type" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="payment_type_two" options="{'no_create': True, 'no_create_edit':True}"/>
                                        <field name="customer_payment_one" />
                                        <field name="customer_payment_two" />
                                        <field name="delivered_by"/>
                                        <!-- @inherit inside olivery_eta module *do not remove! -->
                                        <field name="delivery_date"/>

                                        </group>
                                    </group>

                            </page>
                            <page string="History">
                                <group>
                                    <field name="previous_status" readonly="1"/>
                                    <field name="previous_agent" readonly="1"/>
                                    <field name="previous_area" readonly="1"/>
                                </group>
                            </page>

                            <page string="Follow up orders">
                                <group name="group_top">
                                    <field name="follow_up_order" >
                                        <tree editable="bottom">
                                            <field name="follow_up_sequence" attrs="{'readonly': [('id', '!=', False)]}"/>
                                            <field name="name"/>
                                            <field name="note"/>
                                        </tree>
                                    </field>
                                </group>
                            </page>
                            <page string="Attachments">
                                <field name="attachment_ids" mode="kanban" context="{'default_order_id': active_id}"/>
                            </page>
                        </notebook>

                    </sheet>
                    <!-- History and communication: -->
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- inherit module [olivery_sale_commission]
             inherit module [olivery_compound_order]
             inherit module [storex_modules]
             inherit module[olivery_branch_collection] -->
        <record id="view_tree_rb_delivery_order" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_order</field>
            <field name="model">rb_delivery.order</field>
            <field name="arch" type="xml">
                <tree decoration-danger="web_color=='danger'" decoration-primary="web_color=='primary'" decoration-info="web_color=='info'" decoration-muted="web_color=='muted'" decoration-success="web_color=='success'" decoration-warning="web_color=='warning'" class="order_tree">
                    <field name="web_color" invisible="1" class="smallTd"/>
                    <field name="show_exclamation" invisible="1" class="smallTd"/>
                    <button icon="fa-solid fa-exclamation" attrs="{'invisible':[('show_exclamation','=',False)]}" class="smallTd"/>
                    <field name="note" class="note"/>
                    <field name="sequence_related"/>
                    <field name="order_action"/>
                    <field name="sequence" invisible="1" readonly="1"/>
                    <field name="reference_id"/>
                    <field name="state"/>
                    <field name="assign_to_business" domain="[('role_code','=','rb_delivery.role_business')]"/>


                        <field name="customer_country"
                        options="{'no_create': True, 'no_edit': True, 'no_delete': False}"
                        invisible="1" />

                    <field name="customer_name"/>
                    <field name="customer_mobile"/>
                    <field name="customer_area" options="{'no_create': True, 'no_create_edit':True}"/>
                    <field name="customer_sub_area" domain="[('show_in_create', '=', True),'|',('parent_id', '=',customer_area),('area_parent_id', '=',customer_area)]" options="{'no_create': True, 'no_create_edit':True}"/>
                    <field name="customer_address"/>
                    <field name="assign_to_agent" domain="[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_accounting,rb_delivery.role_super_manager,base.group_system"/>
                    <field name="money_collection_cost" sum="Collection Cost"/>
                    <field name="delivery_cost" sum="Delivery Cost"/>
                    <field name="required_from_business" sum="Required from business"/>
                    <field name="commission" />
                    <field name="copy_total_cost" sum="Total Amount" invisible="1"/>
                    <field name="create_date"/>
                    <field name="create_uid" options="{'no_open': True}"/>
                    <field name="agent_cost" digits="[16, 2]" sum="Agent Cost" invisible="1"/>
                    <field name="delivery_profit" digits="[16, 2]" sum="Delivery Profit" invisible="1"/>
                </tree>

            </field>

        </record>

        <record id="view_order_tree_block_delivery" model="ir.ui.view">
            <field name="name">view_order_tree_block_delivery</field>
            <field name="model">rb_delivery.order</field>
            <field name="inherit_id" ref="view_tree_rb_delivery_order"/>
            <field name="groups_id" eval="[(6, 0, [ref('rb_delivery.role_olivery_block_delivery_fee') ])]"/>
            <field name="arch" type="xml">
                <field name="delivery_cost" position="attributes">
                    <attribute name="attrs">{'column_invisible':1}</attribute>
                </field>
            </field>
        </record>

        <!-- inherit module [olivery_whatsapp_multi] -->
        <!-- inherit module[olivery_branch_collection] -->
        <record id="view_search_rb_delivery_order" model="ir.ui.view">
            <field name="name">view_search_rb_delivery_order</field>
            <field name="model">rb_delivery.order</field>

            <field name="arch" type="xml">

                <search>
                    <group name="search_group">
                        <field name="default" string="Default" />
                        <field name="barcode" string="Barcode"
                            filter_domain="['|',('sequence','ilike',self),'|',('reference_id','ilike',self),('follower_ref_id','ilike',self)]" />
                        <field name="sequence" filter_domain="[('sequence','ilike',self)]" />
                        <field name="reference_id" filter_domain="[('reference_id','ilike',self)]" />
                        <field name="follower_ref_id" />
                        <field name="customer_name" />
                        <field name="customer_mobile" />
                        <field name="previous_customer_mobile_number" />
                        <field name="all" string="All"
                            filter_domain="['|','|','|','|',
                        ('sequence','ilike',self),('reference_id','ilike',self),'|','|','|',
                        ('state_id','ilike',self),('customer_name','ilike',self),('customer_mobile','ilike',self),('previous_customer_mobile_number','ilike',self),'|','|','|',
                        ('assign_to_business','ilike',self),('commercial_name','ilike',self),('user_sequence','ilike',self),('business_mobile_number','ilike',self),'|','|','|','|','|',
                        ('assign_to_agent','ilike',self),('customer_area','ilike',self),('customer_sub_area','ilike',self),('customer_address','ilike',self),('follower_ref_id','ilike',self),('follow_up_order_sequences','ilike',self)]" />

                        <field name="customer_address" />
                        <field name="business_area" string="Business Area" />
                        <field name="business_sub_area" string="Business Sub Area" />
                        <field name="business_alt_area" />
                        <field name="business_alt_sub_area" />
                        <field name="create_uid" options="{'no_open': True}"/>
                        <field name="state" />
                        <field name="status_last_updated_by" />
                        <field name="status_last_updated_on" />
                        <field name="assign_to_agent" />
                        <field name="assign_to_business"
                            filter_domain="['|','|',('assign_to_business','ilike',self),('commercial_name','ilike',self),('user_sequence','ilike',self)]" />
                        <field name="business_id" string="Business ID" />
                        <field name="business_parent_id"
                            filter_domain="['|',('assign_to_business','ilike',self),('business_parent_id','ilike',self)]" />
                        <field name="business_mobile_number" string="Business mobile number" />
                        <field name="customer_area" />
                        <field name="customer_sub_area" />
                        <field name="zone_id" />
                        <field name="money_collection_cost" />
                        <field name="partner_status" />
                        <field name="delivery_date" />
                        <field name="delivered_by" />
                        <field name="previous_status_title" />
                        <field name="previous_agent" />
                        <field name="previous_area" />
                        <field name="reschedule_date" />
                        <field name="product_note" />
                        <field name="follow_up_order_sequences" />
                        <field name="order_action"/>
                    </group>
                    <group string="categorize by order date">
                        <filter
                        name="order_date" string="Today Orders" domain="[('create_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                                                                       ('create_date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                        <!-- <filter name="order_month_date" string="Month Orders" domain="[('order_date','=',datetime.date.today().strftime('%Y-%m-%d'))]"/> -->
                        <filter name="order_month_date" string="Month Orders" domain="[('order_date','&gt;=',datetime.date.today().replace(day=1).strftime('%Y-%m-%d')), ('order_date','&lt;=',datetime.date.today().strftime('%Y-%m-%d'))]"/>

                           <filter name="not_in_collections" string="Not in collections" domain="[('collection_id','=',False)]"/>
                           <filter name="not_in_agent_collections" string="Not in agent collections" domain="[('agent_collection_id','=',False)]"/>
                           <filter name="not_in_returned_collections" string="Not in returned collections" domain="[('returned_collection_id','=',False)]"/>
                           <filter name="not_in_returned_collections" string="Not in any collection" domain="[('collection_id','=',False),('returned_collection_id','=',False),('agent_collection_id','=',False),('agent_returned_collection_id','=',False),('runsheet_collection_id','=',False)]"/>
                           <filter name="archived_orders" string="Archived Orders" domain="[('active','=',False)]"/>
                           <filter name="completed_orders" string="Completed Orders" domain="['|',('state','=','completed'),('state','=','cancelled_completed')]"/>
                           <filter name="completed_returned_orders" string="Completed Returned Orders" domain="[('state','=','completed_returned')]"/>
                           <filter name="prepaid_orders" string="Prepaid Orders" domain="[('is_prepaid_order','=',True)]"/>
                           <filter name="business_orders" string="Sender And Child Orders" context="{'filter_based_on_business':True}" domain="[]"/>
                           <filter name="receiver_business_orders" string="Receiver And Child Orders" context="{'filter_based_on_receiver_business':True}" domain="[]"/>
                    </group>
                    <group string="Groups" name="order_group_by_group">
                        <filter name="group_by_state" string="By State" icon="terp-partner" context="{'group_by':'state'}"/>
                        <filter name="group_by_assign_to_business" string="By Sender" icon="terp-partner" context="{'group_by':'assign_to_business'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry,rb_delivery.role_collection,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_call_center"/>
                        <filter name="group_by_receiver_business" string="By Receiver Business" icon="terp-partner" context="{'group_by':'receiver_business'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry,rb_delivery.role_collection,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_call_center"/>
                        <filter name="group_by_business_parent_id" string="By Sender Parent" icon="terp-partner" context="{'group_by':'business_parent_id'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_data_entry,rb_delivery.role_collection,rb_delivery.role_business,rb_delivery.role_accounting,rb_delivery.role_call_center"/>
                        <filter name="group_by_sender_id" string="By Sender ID" domain="[ ]" context="{'group_by': 'business_id'}"/>
                        <filter name="group_by_sender_area" string="By Sender Area" domain="[ ]" context="{'group_by': 'business_area'}"/>
                        <filter name="group_by_assign_to_agent" string="By Driver" icon="terp-partner" context="{'group_by':'assign_to_agent'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting,rb_delivery.role_data_entry,rb_delivery.role_call_center"/>
                        <filter name="group_by_customer_area" string="By Receiver Area" domain="[ ]" context="{'group_by': 'customer_area'}"/>
                        <filter name="group_by_zone" string="By Zone" domain="[ ]" context="{'group_by':'zone_id'}"/>
                        <filter name="group_by_previous_status" string="By Previous Status" icon="terp-partner" context="{'group_by':'previous_status'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_previous_agent" string="By Previous Driver" icon="terp-partner" context="{'group_by':'previous_agent'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system,rb_delivery.role_accounting"/>
                        <filter name="group_by_date" string="By Creation Date" icon="terp-partner" context="{'group_by':'create_date:day', 'orderby': 'create_date desc'}"/>
                        <filter name="group_by_previous_area" string="By Previous Area" icon="terp-partner" context="{'group_by':'previous_area'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
                        <filter name="group_by_reschedule_date" string="By Scheduled Delivery" icon="terp-partner" context="{'group_by':'reschedule_date:day', 'orderby': 'create_date desc'}"/>
                        <filter name="group_by_assign_to_delivery_date" string="By Delivery Date" icon="terp-partner" context="{'group_by':'delivery_date:day', 'orderby': 'create_date desc'}"/>
                        <filter name="group_by_delivered_by" string="By Delivered By" icon="terp-partner" context="{'group_by':'delivered_by'}"/>
                        <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
                        <filter name="group_by_status_last_updated_by" string="By Status Last Updated By" icon="terp-partner" context="{'group_by':'status_last_updated_by'}"/>
                        <filter name="group_by_status_last_updated_on" string="By Status Last Updated On" icon="terp-partner" context="{'group_by':'status_last_updated_on'}"/>
                        <filter name="group_by_assign_to_distributor_date" string="By Assign To distributor Date" icon="terp-partner" context="{'group_by':'assign_to_distributor_date'}"/>
                        <filter name="group_by_money_received_date" string="By Money Received Date" icon="terp-partner" context="{'group_by':'money_received_date'}"/>
                        <filter name="group_by_order_action" string="By Action Required" icon="terp-partner" context="{'group_by':'order_action'}"/>
                    </group>

                </search>

            </field>

        </record>
        <record id="view_form_rb_delivery_notify_orders" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_notify_orders</field>
            <field name="model">rb_delivery.notify_orders</field>

            <field name="arch" type="xml">
                <form create="false" edit="false">

                    <header>
                        <!-- Buttons and status widget -->
                    </header>

                    <sheet>
                        <group name="group_top">
                            <field name="header" placeholder="Header"/>
                            <field name="message" placeholder="Message" required="1"/>
                            <field name="is_sms"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="notify" type="object" string="Notify"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>
            </field>
        </record>



        <record id="view_form_rb_delivery_order_assign_to_business" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_order_assign_to_business</field>
            <field name="model">rb_delivery.multi_assign_to_business</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">
                    <header>
                    </header>
                    <sheet>
                        <group name="group_top">
                            <field name="assign_to_business"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="select_business" type="object" string="Save" class="oe_highlight" />
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="view_form_rb_delivery_notify_business" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_notify_business</field>
            <field name="model">rb_delivery.notify_business</field>

            <field name="arch" type="xml">
                <form create="false" edit="false">

                    <header>
                        <!-- Buttons and status widget -->
                    </header>

                    <sheet>
                        <group name="group_top">
                            <field name="header" placeholder="Header"/>
                            <field name="message" placeholder="Message" attrs="{ 'required':[('template', '=', False)]}"/>
                            <field name="template" attrs="{'invisible':[('notification_type','!=','is_email')]}"/>
                            <field name="notification_type" widget="radio"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="notify" type="object" string="Notify"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>
                </form>
            </field>
        </record>



   <record id="channel_notification" model="queue.job.channel">
    <field name="name">notification</field>
    <field name="parent_id" ref="queue_job.channel_root" />
    </record>

    <record id="view_form_rb_delivery_order_create_paid_collection" model="ir.ui.view">

        <field name="name">view_form_rb_delivery_order_create_paid_collection</field>
        <field name="model">rb_delivery.create_paid_collection</field>

        <field name="arch" type="xml">

          <form create="false" edit="false">

            <header>
            </header>
            <sheet>
              <group name="group_top">
                <separator string="Are you sure you want to create pre paid collection ? "/>
              </group>
            </sheet>

            <footer>
            <button name="create_pre_paid_collection" type="object" string="Save"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
            </footer>

          </form>

        </field>
      </record>

      <record id="view_form_create_money_collection_warning" model="ir.ui.view">
            <field name="name">view_form_create_money_collection_warning</field>
            <field name="model">rb_delivery.create_money_collection_warning</field>
            <field name="type">form</field>
            <field name="arch" type="xml">
                <form string="Confirmation Message" version="7.0">
                    <separator string="Confirmation Message" colspan="6"/>
                        <field name="text" />
                    <newline/>
                    <separator colspan="6"/>
                    <footer>
                        <button name="confirm_creation" type="object" string="Confirm" class="oe_highlight"/>
                        <button special="cancel" string="No"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="view_form_rb_delivery_order_refresh_address_wizard" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_order_refresh_address_wizard</field>
            <field name="model">rb_delivery.refresh_address_wizard</field>

            <field name="arch" type="xml">

              <form create="false" edit="false">

                <header>
                </header>
                <sheet>
                  <group name="group_top">
                    <separator string="Are you sure you want to recalculate area,sub area and delivery cost for these orders? "/>
                  </group>
                </sheet>

                <footer>
                <button name="confirm_refresh" type="object" string="Yes"/>
                <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                </footer>

              </form>

            </field>
        </record>
        <record id="view_form_rb_delivery_money_collection_download_all_attachments" model="ir.ui.view">

        <field name="name">view_form_rb_delivery_money_collection_download_all_attachments</field>
        <field name="model">rb_delivery.money_collection_download_all_attachments</field>

        <field name="arch" type="xml">

          <form create="false" edit="false">

            <header>
            </header>
            <sheet>
              <group name="group_top">
                <separator string="Are you sure you want to create download all attachments in ZIP file? "/>
              </group>
            </sheet>

            <footer>
            <button name="download_attachments" type="object" string="Download"/>
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
            </footer>

          </form>

        </field>
      </record>


    </data>

</odoo>
