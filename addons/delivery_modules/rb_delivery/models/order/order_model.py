# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import ValidationError, Warning,AccessError
from datetime import date,datetime, timedelta
import re
import base64
import io
import barcode
from barcode.writer import ImageWriter
from dateutil.relativedelta import relativedelta
import pytz
from odoo.osv import osv
from unidecode import unidecode
import requests
import json
import logging
import difflib
from collections import defaultdict
from dateutil import parser
import re
from collections import Counter
import qrcode
from io import BytesIO
from collections import defaultdict

from odoo.http import request
import re
from urllib.parse import urlparse, parse_qs
_logger = logging.getLogger(__name__)

class order_multi_print_orders_money_collector_wizard(models.Model):
    _name = 'rb_delivery.multi_print_orders_money_collector'
    _inherit = 'mail.thread'
    _order = "create_date DESC"
    _description = "Money Collection Model"
    name = fields.Char('Note')


    @api.model
    def _default_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            default_is_block_delivery_fee = True
        else :
            default_is_block_delivery_fee = False
        return default_is_block_delivery_fee

    @api.model
    def _default_is_block_delivery_profit(self):
        user = self.env['rb_delivery.user'].search_read([('user_id', '=', self._uid)],['block_delivery_profit'],limit=1)
        default_is_block_delivery_profit = False
        if len(user)>0 and user[0].get('block_delivery_profit'):
            default_is_block_delivery_profit = True
        
        return default_is_block_delivery_profit

    @api.multi
    def _compute_is_block_delivery_profit(self):
        user = self.env['rb_delivery.user'].search_read([('user_id', '=', self._uid)],['block_delivery_profit'],limit=1)
        is_block_delivery_profit = False
        if len(user)>0 and user[0].get('block_delivery_profit'):
            is_block_delivery_profit = True
        for rec in self:
            rec.is_block_delivery_profit = is_block_delivery_profit
    
    @api.one
    def _compute_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            is_block_delivery_fee = True
        else :
            is_block_delivery_fee = False
        self.is_block_delivery_fee = is_block_delivery_fee

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = []
        for order in self.order_ids:
            ids.append(order.id)
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def get_signature(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_signature').id
        domain = [('collection_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.signature',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_collection_manager = user.has_group('rb_delivery.role_collection_manager')
        self.is_configuration_manager = user.has_group('rb_delivery.role_configuration_manager')

    def default_is_collection_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_collection_manager = user.has_group('rb_delivery.role_collection_manager')
        return is_collection_manager

    def default_is_configuration_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_configuration_manager = user.has_group('rb_delivery.role_configuration_manager')
        return is_configuration_manager

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note

    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code

    report_type= fields.Selection([
        ('branch','Branch'),
        ('business','Business')
    ])

    is_block_delivery_profit = fields.Boolean('Is Block Delivery Profit', default=_default_is_block_delivery_profit,compute="_compute_is_block_delivery_profit", readonly=True)

    sequence = fields.Char('Sequence', readonly=True,track_visibility=False,copy=False)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    business_id = fields.Many2one('rb_delivery.user', 'Business Name',store=True)

    business_driver_id = fields.Many2one(related='business_id.driver_id', readonly=True, store=False)

    business_name = fields.Char(related='business_id.commercial_name', readonly=True, store=False)

    mobile_number = fields.Char(related='business_id.mobile_number', readonly=True, store=False)

    business_longitude = fields.Char(related='business_id.longitude', readonly=True)

    business_latitude = fields.Char(related='business_id.latitude', readonly=True)

    second_business_mobile_number = fields.Char(related='business_id.second_mobile_number', readonly=True)

    business_whatsapp_mobile = fields.Char(related='business_id.whatsapp_mobile', readonly=True)

    business_second_whatsapp_mobile = fields.Char(related='business_id.second_whatsapp_mobile', readonly=True)

    address = fields.Char(related='business_id.address', readonly=True, store=False)

    area_id = fields.Many2one('rb_delivery.area',string="Business Area",compute="_compute_area_id",store=True,readonly=True)

    driver_id = fields.Many2one('rb_delivery.user', 'Driver',track_visibility="on_change")

    total_cost = fields.Float('Total Net Value',readonly=True,track_visibility="on_change")

    total_delivery_cost = fields.Float('Total Delivery Fee',readonly=True,track_visibility="on_change")

    total_ammount = fields.Float('Expected total amount',readonly=True,track_visibility="on_change")

    total_money_collection_cost = fields.Float("Total COD Value",readonly=True,track_visibility="on_change")

    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    final_cost = fields.Float('Final Cost')

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',track_visibility="on_change")

    payment_detail= fields.Char(related='business_id.default_payment_detail', readonly=True)

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    previous_status = fields.Char('Previous Status', track_visibility=False)

    previous_status_title = fields.Char('Previous Status Name',  track_visibility=False, readonly=True)

    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'print_order_item',
        column1 = 'print_id',
        column2 = 'order_id')


    active = fields.Boolean('Active', default=True,track_visibility="on_change",index=True)

    create_date = fields.Datetime(string='Creation Date', index=True)


    is_block_delivery_fee = fields.Boolean('Is Block Delivery Fee', default=_default_is_block_delivery_fee, compute="_compute_is_block_delivery_fee", readonly=True)
    email_sent = fields.Boolean("Email Sent")

    order_count = fields.Integer(string="Order count",readonly=True)

    previous_agent = fields.Many2one('rb_delivery.user', 'Previous Agent', track_visibility="on_change",copy=False,readonly=True)

    is_collection_manager = fields.Boolean('Is Collection Manager', compute="check_user", default=default_is_collection_manager)

    is_configuration_manager = fields.Boolean('Is Configuration Manager', compute="check_user", default=default_is_configuration_manager)

    payment_date = fields.Datetime('Payment Date',track_visibility="on_change")

    bank_name = fields.Char(related='business_id.bank_name', readonly=True)

    bank_number = fields.Char(related='business_id.bank_number', readonly=True)

    holder_name  = fields.Char(related='business_id.holder_name', readonly=True)

    ability_to_edit_collection = fields.Boolean('Ability to edit collection', compute="_check_ability_to_edit_collection")

    company_profit_total = fields.Float('Company profit total',readonly=True)

    close_date = fields.Datetime('Closing Date',track_visibility="on_change")

    is_pre_paid_collection = fields.Boolean('Is pre paid collection',readonly=True)

    onboarding_error_ids = fields.Many2many(comodel_name = 'rb_delivery.onboarding_error', string = 'Onboarding errors', relation = 'onboarding_errors_money_collection_item', column1 = 'money_collection_id', column2 = 'onboarding_error_id',readonly=True)

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    business_parent_id = fields.Many2one('rb_delivery.user', string="Business Parent", compute='_compute_business_parent_id', store=True)

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)

    status_last_updated_by = fields.Many2one('res.users', string="Status last updated by", track_visibility=False,readonly=True)

    @api.multi
    @api.depends('business_id.user_parent_id')
    def _compute_business_parent_id(self):
        for record in self:
            business = record.sudo().business_id
            if business and business.user_parent_id:
                record.business_parent_id = business.user_parent_id
            else:
                record.business_parent_id = False

    @api.multi
    @api.depends('business_id.area_id')
    def _compute_area_id(self):
        for record in self:
            business = record.sudo().business_id
            if business and business.area_id:
                record.area_id = business.area_id
            else:
                record.area_id = False

    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image

    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
                rec.state_id = state_id.id


    @api.depends('state')
    @api.one
    def _check_ability_to_edit_collection(self):
        collection_status = self.env['rb_delivery.client_configuration'].get_param('ability_to_edit_collection_status')
        if collection_status and len(collection_status) > 0:
            for collection in self:
                if collection.state_id.id in collection_status:
                    collection.ability_to_edit_collection = True

    @api.model
    def get_status(self):
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','collection')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list

    @api.multi
    def name_get(self):
        result = []
        for collection in self:
            name = ''
            if collection.name:
                name = collection.name
            name = name + ' ' + collection.sequence
            result.append((collection.id, name))
        return result

    #inherit module [olivery_osc]
    #NOT DONE
    def check_order(self,order_ids,values):
        default_state = ''
        order_collection_ids = []
        order_collection_ids = self.order_ids.ids
        is_allowed = self.env['rb_delivery.client_configuration'].get_param('ability_to_update_collection_on_deferent_status')
        collection_status = self.env['rb_delivery.client_configuration'].get_param('collection_status')

        if len(self.order_ids) > 0:
            default_state = self.order_ids[0].state
        for order_id in order_ids:
            if order_id not in order_collection_ids:
                order = self.env['rb_delivery.order'].browse([order_id])
                collections = self.env['rb_delivery.multi_print_orders_money_collector'].search([('business_id','=',order.sudo().assign_to_business.id)])
                for collection in collections:
                    if order in collection.order_ids:
                        self.env['rb_delivery.error_log'].raise_olivery_error(102,self.id,{'collection_sequence':collection.sequence,'order_sequence':order.sequence})


                if not is_allowed:
                    if default_state != '' and order.state != default_state:
                        order_status = self.env['rb_delivery.status'].search([('name','=',default_state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                        self.env['rb_delivery.error_log'].raise_olivery_error(103,self.id,{'collection_status':order_status.title})


                    elif default_state == '':
                        default_collection_status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
                        collection_states = []
                        message = ''
                        if not collection_status:
                            return
                        for state_id in collection_status:
                            state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                            collection_states.append(state.name)
                            if state.title:
                                message = message +' ' +state.title +'/'
                            else:
                                message = message+' ' +state.name +'/'
                        if order.state and order.state not in collection_states:
                            order_status = self.env['rb_delivery.status'].search([('name','=',default_state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                            self.env['rb_delivery.error_log'].raise_olivery_error(103,self.id,{'collection_status':message})

                        else:
                            if default_collection_status and default_collection_status.default_related_order and default_collection_status.related_order_status:
                                order_status = self.env['rb_delivery.status'].search([('name','=',default_collection_status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                                if order_status and order_status.name and order_status.name != order.state:
                                    order.write({'state':order_status.name})
                else:
                    statues = self.env['rb_delivery.status'].browse(collection_status).mapped('name')
                    if statues and len(statues) > 0:
                        if order.state not in statues:
                            self.env['rb_delivery.error_log'].raise_olivery_error(103,self.id,{'collection_status':default_state})

                if self.sudo().business_id and order.sudo().assign_to_business.id != self.business_id.id:
                   self.env['rb_delivery.error_log'].raise_olivery_error(104,self.id,{'collection_sender':self.sudo().business_id.username,'order_sequence':order.sequence})

    def check_inactive_collections(self):
        records = self.filtered(lambda collection: not collection.active)
        if records:
            records.write({'active':True})

    def guard_function(self,values):
        state_conf = self.env['rb_delivery.client_configuration'].get_param(['status_to_allow_complete_prepaid_collection','prevent_detach_order_from_collection'])
        status_to_allow_complete_prepaid_collection = state_conf['status_to_allow_complete_prepaid_collection']
        if status_to_allow_complete_prepaid_collection:
            status_to_allow_complete_prepaid_collection = self.env['rb_delivery.status'].browse(status_to_allow_complete_prepaid_collection).mapped('name')
        else:
            status_to_allow_complete_prepaid_collection = ['paid', 'completed']
        for rec in self:
            if 'order_ids' in values and values['order_ids'] and len(values['order_ids'])>0 and values['order_ids'][0] and len(values['order_ids'][0])>1:
                all_current_orders_in_collection = values['order_ids'][0][2]
                check_order = rec.check_order(all_current_orders_in_collection,values)
                if values.get('error_log'):
                    return check_order
                old_sequences = []
                new_sequences = []
                statuses = []
                statuses_titles = []
                status_ids = state_conf['prevent_detach_order_from_collection']
                if status_ids:
                    for status_id in status_ids:
                        status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                        if status:
                            statuses.append(status.name)
                            statuses_titles.append(status.title)
                if len(all_current_orders_in_collection)==0:
                    for order in rec.order_ids:
                        old_sequences.append(order.sequence)
                        if len(statuses_titles)>0 and len(statuses)>0 and order.state in statuses:
                            self.env['rb_delivery.error_log'].raise_olivery_error(105,rec.id,{'collection_sequence':rec.sequence,'order_sequence':order.sequence,'order_status':statuses_titles[statuses.index(order.state)],'disallowed_statuses':', '.join(statuses_titles)})

                        order.write({'collection_id':False,'is_prepaid_order':False})
                        order.message_post(body=_("Order removed from collection %s") % rec.sequence)
                    values['order_count'] = 0
                    values['active'] = False
                else:
                    values_to_be_reflected = self.env['rb_delivery.utility'].reflect_changes_to_collections(all_current_orders_in_collection,'money_collection')
                    if values_to_be_reflected:
                        values.update(values_to_be_reflected)
                    for order in rec.order_ids:
                        new_sequences.append(order.sequence)
                        old_sequences.append(order.sequence)
                        if order.id not in all_current_orders_in_collection:
                            if len(statuses_titles)>0 and len(statuses)>0 and order.state in statuses:
                                self.env['rb_delivery.error_log'].raise_olivery_error(105,rec.id,{'collection_sequence':rec.sequence,'order_sequence':order.sequence,'order_status':statuses_titles[statuses.index(order.state)],'disallowed_statuses':', '.join(statuses_titles)})
                            order.write({'collection_id':False,'is_prepaid_order':False})
                            order.message_post(body=_("Order removed from collection %s") % rec.sequence)
                            index = new_sequences.index(order.sequence)
                            del new_sequences[index]
                    values['order_count'] = len(all_current_orders_in_collection)
                    for order in all_current_orders_in_collection:
                        order_rec = self.env['rb_delivery.order'].search([('id','=',order)])
                        if order not in rec.order_ids.ids:
                            order_rec.write({'collection_id':rec.id})
                            new_sequences.append(order_rec.sequence)

                user = self.env['res.users'].sudo().search([('id','=',self._uid)])
                rec.message_post(body=_("Orders were changed from %s to %s by %s") % (old_sequences,new_sequences,user.name))
            if 'state' in values and values['state']:
                if values['state'] == 'deleted':
                    values['active'] = False
                else:
                    self.check_inactive_collections()
                rec.authorize_change_status(values['state'])
                self.env['rb_delivery.action'].notify_for_action_type('for_collection',state_name=values['state'],collection_type='collection',object=rec)
                rec.do_action(values)
                if values ['state'] == rec.state:
                    rec.message_post(body=_("Collection status changed from "+rec.state+" to "+values['state']+" by " + self.env.user.name + " on " + datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")))
                if values['state'] in ['completed','paid'] and rec.is_pre_paid_collection:
                    for order in rec.order_ids :
                        if order.state not in status_to_allow_complete_prepaid_collection: #TODO: change this to olivery validation Error
                            self.env['rb_delivery.error_log'].raise_olivery_error(239,rec.id,{'order_sequences': ', '.join([order.sequence for order in rec.order_ids]), 'collection_sequence': rec.sequence, 'statuses': ', '.join(status_to_allow_complete_prepaid_collection)})
            if rec.state == 'completed':
                user = self.env['res.users'].sudo().search([('id','=',self._uid)])
                if self._uid == 1 or self._uid == 2 or ('active' in values  or 'message_main_attachment_id' in values and len(values)==1) or user.has_group('rb_delivery.role_configuration_manager'):
                    pass
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(101,rec.id,{'collection_sequence':rec.sequence})


    # inherit module[olivery_branch_collection]
    def update_orders(self,values,records,orders_vals=None):
        if orders_vals == None:
            orders_vals = {}
        _logger.info("###############Update order values function First##############")
        _logger.info("############### collection values ##############")
        _logger.info(values)
        _logger.info("############### order values ##############")
        _logger.info(orders_vals)
        order_ids = [order_id for record in records for order_id in record.sudo().order_ids.ids]
        orders = self.env['rb_delivery.order'].sudo().browse(order_ids)
        reflect_values_of_money_collection_to_orders = self.env['rb_delivery.client_configuration'].get_param('reflect_values_of_money_collection_to_orders')
        if reflect_values_of_money_collection_to_orders:
            if 'driver_id' in values and values['driver_id']:
                orders_vals['assign_to_agent'] = values['driver_id']
        if 'state' in values and values['state']:

            if values['state'] == 'deleted':
                orders_vals['collection_id'] = False
            state = self.env['rb_delivery.status'].sudo().search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','collection')])
            if state.related_order_status:
                order_state = self.env['rb_delivery.status'].sudo().search([('name','=',state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                if not order_state:
                    self.env['rb_delivery.error_log'].raise_olivery_error(107,self.id,{'collection_status':state.title,'order_status':state.related_order_status})
                orders_vals['state'] = order_state.name
                orders_vals['is_from_collection'] = True
        _logger.info("###############Update order values function Second##############")
        _logger.info("############### collection values ##############")
        _logger.info(values)
        _logger.info("############### order values ##############")
        _logger.info(orders_vals)
        if orders_vals and orders:
            data = {'uid':self._uid,'message':_("Orders values updated from money collection."),'records':orders,'values':orders_vals,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)


    def update_collections_one_by_one(self,values):
        values_arr = []
        collections = []
        for rec in self:
            values_copy = {}
            if ('driver_id' in values and values['driver_id']):
                if rec.driver_id:
                    values_copy['previous_agent'] = rec.driver_id.id
            
            if 'state' in values and values['state']:
                values_copy['previous_status'] = rec.state
                money_collection_status = self.env['rb_delivery.status'].sudo().search([('name','=',values_copy['previous_status']),('status_type','=','olivery_collection'),('collection_type','=','collection')])
                if money_collection_status and money_collection_status.title:
                    values_copy['previous_status_title'] = money_collection_status.title
                self._update_status_last_updated_by(values)
            if values_copy:
                values_arr.append(values_copy)
                collections.append(rec)
        if len(values_arr)>0 and len(collections)>0:
            self.with_delay(channel="root.basic",max_retries=2).write_jq(values_arr,collections)


    def write_jq(self,values_arr,collections,context=False):
        if context:
            merged_context = self._context.copy()
            merged_context.update(context)
            self = self.with_context(**merged_context)
        # Group orders by their update values
        grouped_orders = defaultdict(list)
        for value, order in zip(values_arr, collections):
            key = frozenset(value.items())  # Convert the dict to frozenset to use as a key
            grouped_orders[key].append(order.id)  # Store the order ID
         # Bulk update for identical values
        for key, collection_ids in grouped_orders.items():
            # Convert the frozenset back to a dictionary
            update_values = dict(key)

            username = self.env.user.name
            message = _("Collection has been updated by %s through function write_jq.")%(username)
            grouped_order_set = self.browse(collection_ids)
            data = {'uid':self._uid,'message':message,'records':grouped_order_set,'values':update_values,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

    def _update_status_last_updated_by(self, values):
        values['status_last_updated_by'] = self._context.get('uid')
        return values

    @api.multi
    def write(self, values):
        _logger.info("########################### money collection values ##################################")
        _logger.info(values)
        _logger.info("########################### money collection values ##################################")
        prepaid_state_ids = self.env['rb_delivery.client_configuration'].get_param('ability_to_reflect_pre_paid_collection_status_to_order')
        prepaid_states = []
        if prepaid_state_ids:
            prepaid_states = self.env['rb_delivery.status'].browse(prepaid_state_ids).mapped('name')
        if values.get('state') and values.get('state') in prepaid_states:
            records = self
        else:
            records = self.filtered(lambda collection: not collection.is_pre_paid_collection)
        self.update_orders(values,records)
        self.guard_function(values)

        if 'order_ids' in values and values['order_ids'] and len(values['order_ids'])>0 and values['order_ids'][0] and len(values['order_ids'][0])>1:
            new_ids = values['order_ids'][0][2]
            for rec in self:
                removed_ids = [order_id for order_id in rec.order_ids.ids if order_id not in new_ids]
                orders = self.env['rb_delivery.order'].sudo().browse(removed_ids).mapped('sequence')
                if len(orders) > 0:
                    rec.message_post(body=_("Orders %s were removed from collection %s by %s") % (', '.join(orders), rec.sequence, self.env.user.name))
                added_ids = [order_id for order_id in new_ids if order_id not in rec.order_ids.ids]
                if len(added_ids) > 0:
                    added_orders = self.env['rb_delivery.order'].sudo().browse(added_ids).mapped('sequence')
                    rec.message_post(body=_("Orders %s were added to collection %s by %s") % (', '.join(added_orders), rec.sequence, self.env.user.name))

        self.update_collections_one_by_one(values)

        return super(order_multi_print_orders_money_collector_wizard, self).write(values)

    def create_multi_collection(self, docs):
        business_groups = {}
        orders_without_business = self.env['rb_delivery.order']
        for doc in docs:
            business_id = doc.sudo().assign_to_business.id if doc.sudo().assign_to_business else None
            if business_id is None:
                orders_without_business += doc
            key = business_id
            if doc.sudo().assign_to_business.user_parent_id and doc.sudo().assign_to_business.user_parent_id.collection_in_main_user_name:
                key = doc.sudo().assign_to_business.user_parent_id.id
            if key not in business_groups :
                business_groups[key] = []
            business_groups[key].append(doc)
        if orders_without_business:
            self.env['rb_delivery.error_log'].raise_olivery_error(162,self.id,{'order_sequences': ', '.join(orders_without_business.mapped('sequence'))})
        new_docs = list(business_groups.values())
        return new_docs

    def update_collection_order_status(self,orders_list,values):
        status=self.env['rb_delivery.status'].search([('name','=',values['state']),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
        if not (status and status.default_related_order and status.related_order_status):
            return
        order_status = self.env['rb_delivery.status'].search([('name','=',status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        orders_to_be_updated_ids = []
        if not (order_status and order_status.name):
            return
        for order in orders_list:
            if order.state != order_status.name:
                orders_to_be_updated_ids.append(order.id)
        if not len(orders_to_be_updated_ids):
            return
        orders_to_be_updated = self.env['rb_delivery.order'].browse(orders_to_be_updated_ids)
        if not orders_to_be_updated:
            return
        data = {'uid':self._uid,'message':_("Orders status updated from create money collection"),'records':orders_to_be_updated,'values':{'state':order_status.name},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)
    
    # inherit module[olivery_branch_collection]
    @api.model
    def create(self, values):
        if 'order_ids' in values and values['order_ids']:
            active_ids = values['order_ids']
            del values['order_ids']
        else:
            active_ids = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))
        if active_ids:
            existing_records = []
            collections_for_existing_records = []
            for order in active_ids:
                if order.collection_id:
                    existing_records.append(order.sequence)
                    collections_for_existing_records.append(order.collection_id.sequence)

            if len(existing_records) > 0:
                self.env['rb_delivery.error_log'].raise_olivery_error(108,self.id,{'order_sequences':', '.join(existing_records),'collection_sequences':', '.join(collections_for_existing_records)})
            orders_business_lists = self.create_multi_collection(active_ids)
            if 'name' in values and values['name']:
                title = values['name']
            else:
                title = ''
            is_pre_paid_collection = False
            if 'is_pre_paid_collection' in values and values['is_pre_paid_collection']:
                is_pre_paid_collection = True
            for orders_list in orders_business_lists:
                if is_pre_paid_collection:
                    default_prepaid = self.env['rb_delivery.client_configuration'].get_param('defualt_status_for_pre_paid_collection')
                    if default_prepaid:
                        state = self.env['rb_delivery.status'].browse(default_prepaid)
                        values['state'] = state[0].name
                    else:
                        values['state'] = 'money_in'
                else:
                    status = self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
                    if status:
                        values['state'] = status.name
                    else: #TODO: change this to olivery validation Error
                        self.env['rb_delivery.error_log'].raise_olivery_error(240,self.id, {'order_sequence': orders_list[0].sequence})
                        #raise ValidationError(_('There is no default status for collection \n what to do next: go to olivery configuration ==> security => collection statuses and select status and set default True'))
                business = orders_list[0].sudo().assign_to_business
                name_on_collection = business
                if business.user_parent_id and business.user_parent_id.collection_in_main_user_name:
                    name_on_collection = business.user_parent_id

                if not name_on_collection:
                    self.env['rb_delivery.error_log'].raise_olivery_error(109,self.id,{'order_sequence':orders_list[0].sequence})
                if name_on_collection:
                    if name_on_collection.sudo().default_payment_type:
                        values['payment_type'] = name_on_collection.sudo().default_payment_type.id
                        values['payment_detail'] = name_on_collection.sudo().default_payment_detail
                    else:
                        payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
                        if len(payment_type) != 0:
                            values['payment_type'] = payment_type[0].id if payment_type else None
                if title != '':
                    values['name'] = title + '_' + name_on_collection.sudo().username
                else:
                    values['name'] = name_on_collection.sudo().username
                ids = []

                total_cost = 0.0
                total_delivery_cost = 0.0
                total_ammount = 0.0
                total_money_collection_cost = 0.0
                total_company_profit = 0.0
                collection_states_ids = []
                collection_states_ids = self.env['rb_delivery.client_configuration'].get_param('collection_status')

                collection_states = []
                message = ''
                if not collection_states_ids and not is_pre_paid_collection:
                    self.env['rb_delivery.error_log'].raise_olivery_error(110,self.id)
                for state_id in collection_states_ids:
                    state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                    collection_states.append(state.name)
                    if state.title:
                        message = message +' ' +state.title +'/'
                    else:
                        message = message+' ' +state.name +'/'
                for order in orders_list:
                    if (order.state and order.state in collection_states) or is_pre_paid_collection:
                        total_cost = total_cost + order.required_from_business
                        total_delivery_cost = total_delivery_cost + order.delivery_cost
                        if order.inclusive_delivery:
                            total_ammount = total_ammount + order.copy_total_cost
                        else:
                            total_ammount = total_ammount + order.cost + order.delivery_cost
                        total_money_collection_cost = total_money_collection_cost + order.money_collection_cost
                        total_company_profit = total_company_profit + order.delivery_profit
                        ids.append(order.id)
                    else:
                        status_title = self.env['rb_delivery.status'].search([('name','=',order.state),('status_type','=','olivery_order')]).title
                        self.env['rb_delivery.error_log'].raise_olivery_error(111,self.id,{'order_sequence':order.sequence,'order_statuses':message, 'order_status':status_title})

                values['order_ids'] = [(6,0,ids)]
                values['order_count'] = len(ids)
                values['total_cost'] = total_cost
                values['total_money_collection_cost'] = total_money_collection_cost
                values['total_delivery_cost'] = total_delivery_cost
                values['total_ammount'] = total_ammount
                values['business_id'] = name_on_collection.id
                values['company_profit_total'] = total_company_profit
                if 'state' in values and values['state']:
                    self.update_collection_order_status(orders_list,values)
                    self.authorize_change_status(values['state'])
                    self.do_action_create(values)
                    self._update_status_last_updated_by(values)
                new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.multi_print_orders_money_collector')
                values['sequence'] = new_sequence
                if values.get('message_follower_ids') :
                    del values['message_follower_ids']
                order_report = super(order_multi_print_orders_money_collector_wizard, self).create(values)
                if 'state' in values and values['state']:
                    self.env['rb_delivery.action'].notify_for_action_type('for_collection',state_name=values['state'],collection_type='collection',object=order_report)

                vals = {'collection_id':order_report.id}
                order_report.order_ids.write(vals)
                if order_report.report_type == 'branch':
                    order_report.final_cost= total_ammount

            message = _('Collection generated')
            self.env['rb_delivery.utility'].send_toast('for_user', ['collection_generated',message] , str(self._uid))
            return order_report
        else:
            new_sequence = self.env['ir.sequence'].next_by_code(
                'rb_delivery.multi_print_orders_money_collector')
            values['sequence'] = new_sequence
            new_collection = super(order_multi_print_orders_money_collector_wizard, self).create(values)

            return new_collection

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            self.env['rb_delivery.error_log'].raise_olivery_error(112,self.id,{'collection_status':status, 'current_status': self.state})

        return

    def get_attatchments(self,report):
        pdf = self.env.ref('rb_delivery.report_rb_delivery_order_multi_print_orders_money_collector_action').render_qweb_pdf(report.id)
        name = report.sudo().business_id.username
        if report.name:
            name = report.name
        try:
            data_id = self.env['ir.attachment'].create({
                'name': name,
                'type': 'binary',
                'datas': base64.encodestring(pdf[0]),
                'res_model': 'rb_delivery.multi_print_orders_money_collector',
                'res_id': report.id,
                'mimetype': 'application/x-pdf'
                })
            attachment_ids = [(6, 0, [data_id.id])]
        except:
            attachment_ids = []
        return attachment_ids

    def do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state['state']),('status_type','=','olivery_collection'),('collection_type','=','collection')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(order_multi_print_orders_money_collector_wizard,action.name)
                method_to_call(self,next_state)
            except:
                pass

    def do_action_create(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','collection')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(order_multi_print_orders_money_collector_wizard,action.name)
                method_to_call(self,values)
            except:
                pass

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_order_select_money_collection_state').id
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_money_collection_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                self.env['rb_delivery.error_log'].raise_olivery_error(113,self.id,{'current_status':current_status_record.title,'new_status':next_status_record.title, 'collection_status': status})

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            barcode.base.Barcode.default_writer_options['write_text'] = False

            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})

    @api.model
    def print_multi_orders_money_collector_report(self,collection_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delivery_order_multi_print_orders_money_collector_action').sudo().render_qweb_pdf(collection_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.multi
    def change_money_collection_state(self,state_name):
        collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',state_name),('collection_type','=','collection'),('status_type','=','olivery_collection')])
        self.write({'state': collection_state.name})
        return True

    def set_payment_date(self,values):
        values['payment_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    # Post actions methods
    def detach_agent_action(self,next_state):
        if self.sudo().driver_id:
            self.sudo().driver_id = ""


    def get_collection_docs(self,docs):
        new_doc = []
        new_docs = []
        business_list = []
        for doc in docs:
            if doc.business_id.id not in business_list:
                business_list.append(doc.business_id.id)
        for business in business_list:
            for doc in docs:
                if doc.business_id.id == business:
                    new_doc.append(doc)
            new_docs.append(new_doc)
            new_doc= []
        return new_docs

    def driver_get_collection_docs(self, docs):
        driver_dict = {}
        no_driver_docs = []
        for doc in docs:
            if doc.business_id.driver_id:
                driver_id = doc.business_id.driver_id.id
                if driver_id not in driver_dict:
                    driver_dict[driver_id] = []
                driver_dict[driver_id].append(doc)
            else:
                no_driver_docs.append(doc)
        new_docs = []
        for driver_id, docs in driver_dict.items():
            new_docs.append(docs)
        if len(no_driver_docs)>0:
            new_docs.append(no_driver_docs)

        return new_docs

    def get_collection_doc_ids(self, docs, assignment_id):
        doc_ids = [int(doc_id) for doc_id in docs.split(',')]
        orders = self.env['rb_delivery.multi_print_orders_money_collector'].browse(doc_ids)
        assignment_to_docs = {}

        for order in orders:
            if assignment_id == 'business':
                assignment = order.business_id.id
            elif assignment_id == 'driver':
                assignment = order.business_driver_id.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(114,self.id)

            if assignment not in assignment_to_docs:
                assignment_to_docs[assignment] = []

            assignment_to_docs[assignment].append(str(order.id))

        return [','.join(doc_ids) for doc_ids in assignment_to_docs.values()]

    def clear_old_attachment(self):
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        date = datetime.now(pytz.timezone(timezone))
        fmt = "%Y-%m-%d"
        date = date - relativedelta(days=14)
        expired_date = datetime.strftime(date,fmt) + ' 00:00:00'
        old_attachemnt = self.env['ir.attachment'].search([('create_date','<',expired_date),'|',('res_model','=','rb_delivery.multi_print_orders_money_collector'),('res_model','=','rb_delivery.returned_money_collection')])
        if old_attachemnt and len(old_attachemnt) > 0:

            batch_list = self.split_batches(old_attachemnt,1000)
            for batch in batch_list:
                self.with_delay(channel="root.clear_collection_attachment",max_retries=2).clear_old_collection_attachment(batch)

    def split_batches(self,array,size):
        return [array[i:i + size] for i in range(0, len(array), size)]


    def clear_old_collection_attachment(self,records):
        records.unlink()

    @api.model
    def group_by_get_business(self,domain):
        users = []
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].search(domain)
        for collection in collections:
                if {"id":collection.business_id.id ,"name":collection.business_id.commercial_name} not in users :
                        users.append({"id":collection.business_id.id ,"name":collection.business_id.commercial_name})
        return users

    @api.model
    def group_by_get_driver(self,domain):
        users = []
        collections = self.env['rb_delivery.multi_print_orders_money_collector'].search(domain)
        for collection in collections:
                if {"id":collection.driver_id.id ,"name":collection.driver_id.username} not in users :
                        users.append({"id":collection.driver_id.id ,"name":collection.driver_id.username})
        return users

    @api.model
    def money_collection_count(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.multi_print_orders_money_collector'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.multi_print_orders_money_collector'].search_count([])
            return count



class display_dialog_box(osv.osv):
    _name = "display.dialog.box"
    _description = "Display dialog box management"

    text = fields.Text('Text', translate=False)

    warning_text = fields.Text('Warning Text', translate=False)

    collection_text = fields.Text('Agent Collection Text', translate=False)

    agent_collection_text = fields.Text('Agent Collection Text', translate=False)

    def get_values(self,values=None):
        if values == None:
            values = {}
        agent = self._context.get('agent')

        note = self._context.get('note')

        reschedule_date = self._context.get('reschedule_date')

        payment_type = self._context.get('payment_type')

        payment_type_two = self._context.get('payment_type_two')

        customer_payment = self._context.get('customer_payment')

        customer_payment_one = self._context.get('customer_payment_one')

        customer_payment_two = self._context.get('customer_payment_two')

        reject_reason = self._context.get('reject_reason')

        order_action = self._context.get('order_action')

        stuck_comment = self._context.get('stuck_comment')

        state = self.env['rb_delivery.status'].sudo().search([('name','=',self._context.get('state')),'|',('status_type','=',False),('status_type','=','olivery_order')])

        if state:
            values['state']=state.name
        if agent:
            values['assign_to_agent'] = agent
        if note:
            values['note'] = note
        if customer_payment:
            values['customer_payment'] = customer_payment
        if customer_payment_one:
            values['customer_payment_one'] = customer_payment_one
        if customer_payment_two:
            values['customer_payment_two'] = customer_payment_two
        if payment_type:
            values['payment_type'] = payment_type
        if payment_type_two:
            values['payment_type_two'] = payment_type_two
        if reschedule_date:
            values['reschedule_date'] = reschedule_date
        if reject_reason:
            values['reject_reason'] = reject_reason
        if order_action:
            values['order_action'] = order_action
        if stuck_comment:
            values['stuck_comment'] = stuck_comment

        return values

    @api.multi
    def select_state(self):
        values = self.get_select_state_values()
        recs = self.env['rb_delivery.order'].browse( self._context.get('orders'))
        if not values:
            return True
        recs.write(values)
        confs = self.env['rb_delivery.client_configuration'].get_param(['create_agent_collection_for_delivered_orders', 'create_agent_collection_with_employee', 'check_agent_when_create_agent_collection'])
        if values.get('state') == 'delivered' and confs.get('create_agent_collection_for_delivered_orders'):
            orders_without_agent_collection = recs.filtered(lambda order: not order.agent_collection_id and not order.assign_to_agent)            
            if orders_without_agent_collection:
                self.env['rb_delivery.utility'].with_context(append_employee_if_no_agent=True if confs.get('check_agent_when_create_agent_collection') else False, append_employee_if_he_changed_status=True if confs.get('create_agent_collection_with_employee') else False).create_agent_collection(orders=orders_without_agent_collection)
        return True



    @api.model
    def get_select_state_values(self):
        extra_agent_cost = self._context.get('extra_agent_cost')
        extra_agent_cost_per_order = 0
        values = self.get_values()
        if values:
            recs = self.env['rb_delivery.order'].browse( self._context.get('orders'))
            extra_agent_cost_per_order = extra_agent_cost/len(recs)
            if extra_agent_cost_per_order > 0:
                values['extra_agent_cost'] = extra_agent_cost_per_order
            if values.get('extra_agent_cost'):
                orders_with_no_agent = recs.filtered(lambda x:not x.sudo().assign_to_agent)
                if len(orders_with_no_agent)>0:
                    self.env['rb_delivery.error_log'].raise_olivery_error(205,self.id,{'order_sequences':', '.join(orders_with_no_agent.mapped('sequence'))})
                for rec in recs:
                    order_values = {'extra_agent_cost':values['extra_agent_cost']}
                    order_values['extra_agent_cost'] = order_values['extra_agent_cost'] + rec.extra_agent_cost
                    rec.write(order_values)
                del values['extra_agent_cost']
        return values

class rb_delivery_order(models.Model):


    _name = 'rb_delivery.order'
    _inherit = 'mail.thread'
    _order = "sequence DESC"
    _description = "Order Model"


    SUDO_FIELDS=['route_sequence','onboarding_error_ids','business_id','inclusive_delivery','is_partner_order','is_sender_partner_order','is_storex_order','message_attachment_count','no_of_items','readonly_replacement_order','sender_ref_id','show_alt_address','__last_update','status_last_updated_by','status_last_updated_on','sequence','customer_area_group','business_area_group','name','message_follower_ids','subtype_ids','partner_id','partner_reference_id','vhub_logs','web_color','total_cost','previous_customer_mobile_number','total_value_imported','currency_id','zone_id']

    #depricated (DONT USE)
    TRACKED_FIELDS = ['order_type_id', 'assign_to_distributor_date','reference_id','customer_name','assign_to_agent','money_collector','reject_reason','payment_type','clone_reference','is_replacement','replacement_order','returned_order','returned_clone_reference','is_returned','default_service','service_fee','order_weight','partner_status','internal_partner_status','partner_reference_id','is_partner_order','is_sender_partner_order','assign_to_business','active','inclusive_delivery','alt_business_name','alt_mobile_number','business_alt_area','business_alt_address','business_alt_sub_area','customer_mobile','second_mobile_number','customer_address','customer_country','customer_area','customer_sub_area','cost','delivery_cost','extra_cost','total_cost','copy_total_cost','money_collection_cost','delivered_by','delivery_date','note','product_note','driver_note','stuck_comment','special_note','customer_payment','paid','customer_discount','order_date','delivery_cost_on_sender','delivery_cost_on_customer','state_id','solve_stuck_comment','cus_whatsapp_mobile','cus_second_whatsapp_mobile','discount','required_from_business','financial_state','follower_address','follower_ref_id','follower_area','follower_store_name','follower_mobile_number','follower_second_mobile_number','follower_longitude','follower_latitude','longitude','latitude','extra_agent_cost','required_to_company','customer_village','customer_neighbour','customer_building_number','no_of_items','reschedule_date','customer_location','batch_number','actual_created_date','customer_district_id','commission','extra_commission','follow_up_order','follow_up_orders_map','picked_up_by','money_received_date','zone_id']

    RELATED_COLLECTIONS_FIELDS = ['required_from_business','delivery_cost','money_collection_cost','delivery_profit','agent_cost','required_to_company','state_id']

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def solve_stuck(self, order_id, values):
        order = self.env['rb_delivery.order'].browse(order_id)
        confs = self.env['rb_delivery.client_configuration'].get_param(['status_to_go_to_when_solve_stuck_after_timer', 'status_to_go_to_when_solve_stuck_before_timer', 'solve_stuck_timer_duration'])

        status_last_updated_on = order.status_last_updated_on
        now = fields.Datetime.now()
        timer = 0
        try:
            timer = int(confs['solve_stuck_timer_duration'])
        except:
            pass
        if (now - status_last_updated_on >= timedelta(minutes=timer) and timer>0) or not order.assign_to_agent:
            status = self.env['rb_delivery.status'].browse(confs['status_to_go_to_when_solve_stuck_after_timer'])
            values['state']=status.name if status else 'ready_for_dispatch'
        else:
            status = self.env['rb_delivery.status'].browse(confs['status_to_go_to_when_solve_stuck_before_timer'])
            values['state']=status.name if status else 'in_progress'

        values['stuck_comment']=''
        values['reject_reason']=False
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        username = user.username
        message=_("Order has been updated with by %s himself, this is done through automated action from system. You can check configuration or contact support to get more details.")%(username)

        if order:
            data = {'uid':self._uid,'message':message,'records':order,'values':values,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

    @api.model
    def execute_status_actions(self, status_action, orders):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])[0]
        if not status_action:
            return

        if ('longitude' in status_action and status_action['longitude'] and
            'latitude' in status_action and status_action['latitude'] and
            'statusId' in status_action and status_action['statusId']):
            location = self.env['rb_delivery.location'].create_multiple_records(
                status_action['longitude'], status_action['latitude'], status_action['statusId'], orders)
        for order_id in orders:
            order_record = self.search([('id', '=', order_id)])
            if not order_record:
                continue

            if 'signature_image' in status_action and status_action['signature_image'] and 'state' in status_action and status_action['state'] and user.id:
                image_data = status_action['signature_image'].split(',')[1]
                signature_values={
                    'signature_image': image_data,
                    'status': status_action['state'],
                    'user_id': [(6, 0, [user.id])],
                    'order_id': [(6, 0, [order_id])],
                }
                signature = self.env['rb_delivery.signature'].create(signature_values)

            order_values = {'state': status_action.get('state', False)}
            if 'reject_reason' in status_action and status_action['reject_reason'] or 'stuck_comment' in status_action and status_action['stuck_comment']:
                order_values['solve_stuck_comment']=""
            for key, value in status_action.items():
                if key not in ['signature', 'signature_image', 'state']:
                    order_values[key] = value

            if ('scanned_reference_id' in status_action and status_action['scanned_reference_id']):
                order_values['scanned_reference_id'] = status_action['scanned_reference_id']


            order_record.write(order_values)
        return

    @api.model_cr
    def init(self):
        print ("dd")
        # self.env['bus.bus'].sendone('app_auto_refresh', 'refresh')

    def _get_business_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_business')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def default_driver(self):
        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        is_driver = user.sudo().has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].sudo().search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False

    @api.model
    def default_business(self):
        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        is_business = user.sudo().has_group('rb_delivery.role_business')
        if is_business:
            del_user = self.env['rb_delivery.user'].sudo().search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False


    def get_service_fee_sum(self, service):
        if service.pricing_type == 'fixed':
            return service.cost
        elif service.pricing_type == 'percentage':
            return round((self.delivery_cost * float(service.cost)) / 100, 1)
        return 0

    def get_service_fee(self, services, default_service, extra_service_fee_on_sender=0, extra_service_fee_on_customer=0):
        sender_service_fee = 0
        customer_service_fee = 0
        if not services:
            services = []
        for service in services:
            if service.service_fee_on_sender:
                sender_service_fee += self.get_service_fee_sum(service)
            elif service.service_fee_on_customer:
                customer_service_fee += self.get_service_fee_sum(service)
        if default_service:
            sender_service_fee += self.get_service_fee_sum(default_service) if default_service.service_fee_on_sender else 0
            customer_service_fee += self.get_service_fee_sum(default_service) if default_service.service_fee_on_customer else 0
        if self.extra_service_fee_on_customer or extra_service_fee_on_customer:
            customer_service_fee += self.extra_service_fee_on_customer if self and len(self) == 1 else float(extra_service_fee_on_customer)
        if self.extra_service_fee_on_sender or extra_service_fee_on_sender:
            sender_service_fee += self.extra_service_fee_on_sender if self and len(self) == 1 else float(extra_service_fee_on_sender)
        total_service_fee = sender_service_fee + customer_service_fee
        service_fee = {
            'sender_service_fee': sender_service_fee,
            'customer_service_fee': customer_service_fee,
            'total_service_fee': total_service_fee
        }
        return service_fee

    def compute_extra_delivery_cost(self):
        extra_cost = 0
        extra_cost += self.extra_cost
        extra_cost -= self.discount
        service_fee = self.get_service_fee(self.service,self.default_service, self.extra_service_fee_on_sender, self.extra_service_fee_on_customer)
        self.service_fee = service_fee['total_service_fee']
        return extra_cost

    # inherit module[olivery_cargo]
    # inherit module[olivery_international]
    # inherit module[olivery_km_pricing]
    # inherit module[olivery_number_of_pieces]
    # inherit module[olivery_vat]
    # inherit module[olivery_weight_calculations]
    # inherit module[olivery_branch_collection]
    @api.depends('customer_area', 'assign_to_business', 'extra_cost', 'order_type_id', 'discount','customer_sub_area', 'service','business_alt_area','show_alt_address','default_service', 'extra_service_fee_on_sender', 'extra_service_fee_on_customer')
    @api.multi
    def get_customer_price(self):
        for rec in self:
            if rec.sudo().assign_to_business:
                business_user = rec.sudo().assign_to_business
            else:
                business_user = rec.assign_to_business
            rec.inclusive_delivery = business_user.sudo().inclusive_delivery
            data = {
                'sender_id': business_user.sudo().id,
                'to_area_id': rec.customer_area.id,
                'order_type_id': rec.order_type_id.id,
                "sub_area_id":rec.customer_sub_area.id or False
                }
            if rec.business_alt_area and rec.show_alt_address:
                cost = self.env['rb_delivery.pricelist'].sudo().get_price(data,rec.business_alt_area)
            else:
                cost = self.env['rb_delivery.pricelist'].sudo().get_price(data)


            if not rec.is_returned:
                extra_delivery_cost = rec.compute_extra_delivery_cost()
                rec.delivery_cost = cost + extra_delivery_cost

    @api.model
    def default_order_type(self):
        order_type = self.env['rb_delivery.order_type'].search([('default','=',True)])
        if len(order_type) != 0:
            return order_type[0].id if order_type else None
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(215,self.id)


    @api.model
    def default_country(self):
        country = self.env['rb_delivery.country'].search([('is_default','=',True)], limit=1)
        if len(country) != 0:
            return country[0].id if country else None
        else:
            return None

    @api.model
    def default_payment_type(self):
        payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
        if len(payment_type) != 0:
            return payment_type[0].id if payment_type else None
        else:
            return None

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        return status.name if fields else None


    @api.onchange('show_alt_address','assign_to_business')
    def _onchange_business_alt_address(self):
        business_user = self.assign_to_business
        if business_user:
            self.business_alt_area = business_user.area_id
            self.business_alt_address = business_user.address
            self.alt_mobile_number = business_user.mobile_number
            self.business_alt_sub_area = business_user.sub_area
            self.alt_business_name = business_user.username

    @api.model
    def _default_show_cost_on_sender(self):
        default_show_cost_on_sender = self.env['rb_delivery.client_configuration'].get_param('order_delivery_cost_on_sender_visibility')
        return default_show_cost_on_sender

    @api.model
    def _default_delivery_fee_on_customer(self):
        default_delivery_cost_on_customer = self.env['rb_delivery.client_configuration'].get_param('order_delivery_cost_on_customer')
        return default_delivery_cost_on_customer

    @api.model
    def _default_show_alternate_address(self):
        default_show_alternate_address = self.env['rb_delivery.client_configuration'].get_param('show_alternate_address')
        return default_show_alternate_address

    @api.model
    def _default_show_inclusive_delivery(self):
        default_show_inclusive_delivery = self.env['rb_delivery.client_configuration'].get_param('order_inclusive_visibility')
        return default_show_inclusive_delivery

    @api.model
    def _default_hide_paid_field(self):
        default_hide_paid_field = self.env['rb_delivery.client_configuration'].get_param('ability_to_hide_paid_field_in_creating_order')
        return default_hide_paid_field

    @api.multi
    def _compute_hide_paid_field(self):
        hide_paid_field = self.env['rb_delivery.client_configuration'].get_param('ability_to_hide_paid_field_in_creating_order')
        for rec in self:
            rec.hide_paid_field = hide_paid_field

    @api.one
    def _compute_show_cost_on_sender(self):
        show_cost_on_sender = self.env['rb_delivery.client_configuration'].get_param('order_delivery_cost_on_sender_visibility')
        self.show_delivery_cost_on_sender = show_cost_on_sender

    @api.one
    def _compute_show_alternate_address(self):
        show_alternate_address = self.env['rb_delivery.client_configuration'].get_param('show_alternate_address')
        self.show_alternate_address = show_alternate_address

    @api.model
    def _default_show_follower_info_in_waybill(self):
        default_show_follower_info_in_waybill = self.env['rb_delivery.client_configuration'].get_param('show_follower_info_in_waybill')
        return default_show_follower_info_in_waybill

    @api.one
    def _compute_show_follower_info_in_waybill(self):
        show_follower_info_in_waybill = self.env['rb_delivery.client_configuration'].get_param('show_follower_info_in_waybill')
        self.show_follower_info_in_waybill = show_follower_info_in_waybill

    @api.one
    def _compute_show_inclusive_delivery(self):
        show_inclusive_delivery = self.env['rb_delivery.client_configuration'].get_param('order_inclusive_visibility')
        self.show_inclusive_delivery = show_inclusive_delivery

    @api.one
    def _compute_returned_status(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('returned_discount_status')
        statuses = ''
        if status_ids and len(status_ids)>0:
            for status_id in status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                statuses= statuses + ',' + status.name
        self.returned_discount_statuses = statuses

    @api.one
    def _compute_canceled_discount_status(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('canceled_discount_status')
        statuses = ''
        if status_ids and len(status_ids)>0:
            for status_id in status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                statuses= statuses + ',' + status.name
        self.canceled_discount_statuses = statuses


    def _default_returned_status(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('returned_discount_status')
        statuses = ''
        if status_ids and len(status_ids)>0:
            for status_id in status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                statuses= statuses + ',' + status.name
        return statuses

    def _default_canceled_discount_status(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('canceled_discount_status')
        statuses = ''
        if status_ids and len(status_ids)>0:
            for status_id in status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                statuses= statuses + ',' + status.name
        return statuses

    def default_is_business(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_business = user.has_group('rb_delivery.role_business')
        return is_business

    def default_is_configuration_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_configuration_manager = user.has_group('rb_delivery.role_configuration_manager')
        return is_configuration_manager

    @api.model
    def default_is_data_entry(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_data_entry = user.has_group('rb_delivery.role_data_entry')
        return is_data_entry

    @api.model
    def _default_show_only_total(self):
        show_only_total_groups = self.env['rb_delivery.client_configuration'].get_param('show_only_total_for_roles')
        user_groups = self.env.user.groups_id.ids
        if user_groups and show_only_total_groups and set(show_only_total_groups).intersection(set(user_groups)):
            return True
        else:
            return False

    @api.one
    def _compute_show_only_total(self):
        self.show_only_total = self._default_show_only_total()


    @api.model
    def _default_show_description_tags(self):
        show_description_tags = self.env['rb_delivery.client_configuration'].get_param('order_description_tags_visibility')
        return show_description_tags

    @api.one
    def _compute_show_description_tags(self):
        show_description_tags = self.env['rb_delivery.client_configuration'].get_param('order_description_tags_visibility')
        self.show_description_tags_in_waybill = show_description_tags


    def default_has_children(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        children = self.env['rb_delivery.user'].sudo().search([('user_parent_id', '=', user.id)])
        if len(children)>0:
            return True
        else:
            return False

    @api.model
    def get_default_inclusive_delivery(self,sender_id):
        #get the default from business first if exist
        def get_user(user_id, user_model):
            business = False
            if user_id and isinstance(user_id, int):
                business = user_model.browse([user_id])
            elif isinstance(user_id, str):
                business = user_model.search(['|','|','|',('mobile_number','=',user_id),('username','=',user_id),('user_sequence','=',user_id),('commercial_name','=',user_id), ('role_code','=','rb_delivery.role_business')])
            return business
        if self._uid == 1 or self._uid == 2:
            business_user = get_user(sender_id, self.env['rb_delivery.user'])
        else:
            business_user = get_user(sender_id, self.env['rb_delivery.user'].sudo())
        if(business_user):
            default_inclusive_delivery_cost = business_user.inclusive_delivery
        else:
            default_inclusive_delivery_cost = self.env['rb_delivery.client_configuration'].get_param('user_creation_inclusive_ability')

        return default_inclusive_delivery_cost

    @api.one
    def _compute_show_customer_village(self):
        show_customer_village = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_village_visibility')
        self.show_customer_village = show_customer_village

    @api.model
    def _default_show_customer_village(self):
        default_show_customer_village = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_village_visibility')
        return default_show_customer_village

    @api.one
    def _compute_show_customer_neighbour(self):
        show_customer_neighbour = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_neighbour_visibility')
        self.show_customer_neighbour = show_customer_neighbour

    @api.model
    def _default_show_customer_neighbour(self):
        default_show_customer_neighbour = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_neighbour_visibility')
        return default_show_customer_neighbour

    @api.one
    def _compute_show_customer_building_number(self):
        show_customer_building_number = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_building_number_visibility')
        self.show_customer_building_number = show_customer_building_number

    @api.model
    def _default_show_customer_building_number(self):
        default_show_customer_building_number = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_customer_building_number_visibility')
        return default_show_customer_building_number

    @api.one
    def _compute_show_no_of_items(self):
        show_no_of_items = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_no_of_items_visibility')
        self.show_amount = show_no_of_items

    @api.multi
    def _compute_show_service(self):
        show_service = self.env['rb_delivery.client_configuration'].get_param(['service_visibility', 'extra_service_on_customer_visibility', 'extra_service_on_sender_visibility'])
        for rec in self:
            rec.show_service = show_service['service_visibility']
            rec.show_extra_service_fee_on_sender = show_service['extra_service_on_sender_visibility']
            rec.show_extra_service_fee_on_customer = show_service['extra_service_on_customer_visibility']

    @api.model
    def _default_show_no_of_items(self):
        default_show_no_of_items = self.env['rb_delivery.client_configuration'].get_param('order_show_extra_field_no_of_items_visibility')
        return default_show_no_of_items

    @api.model
    def _default_show_service(self):
        default_show_service = self.env['rb_delivery.client_configuration'].get_param('service_visibility')
        return default_show_service

    @api.model
    def _default_show_extra_service_fee_on_customer(self):
        default_show_extra_service_fee_on_customer = self.env['rb_delivery.client_configuration'].get_param('extra_service_on_customer_visibility')
        return default_show_extra_service_fee_on_customer

    @api.model
    def _default_show_extra_service_fee_on_sender(self):
        default_show_extra_service_fee_on_sender = self.env['rb_delivery.client_configuration'].get_param('extra_service_on_sender_visibility')
        return default_show_extra_service_fee_on_sender


    @api.one
    def _compute_show_agent_commercial_number(self):
        show_agent_commercial_number = self.env['rb_delivery.client_configuration'].get_param('order_show_commercial_number_for_driver_in_business_visibility')
        self.show_agent_commercial_number = show_agent_commercial_number

    @api.one
    def _compute_show_replacement_order(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        roles = self.env['rb_delivery.client_configuration'].get_param('show_replacement_order')
        if self.env.user._is_admin():
            self.show_replacement_order = True
        else:
            if roles and type(roles) != bool and roles[0] :
                for role in roles:
                    if user.group_id.id == role:
                        self.show_replacement_order = True
                        break
            else :
                self.show_replacement_order = False

    @api.one
    def _compute_show_returned_order(self):
        show_returned_order = self.env['rb_delivery.client_configuration'].get_param('show_returned_order')
        self.show_returned_order = show_returned_order

    @api.model
    def _default_show_agent_commercial_number(self):
        default_show_agent_commercial_number = self.env['rb_delivery.client_configuration'].get_param('order_show_commercial_number_for_driver_in_business_visibility')
        return default_show_agent_commercial_number

    @api.one
    def _compute_show_picking_time(self):
        show_picking_time = self.env['rb_delivery.client_configuration'].get_param('order_show_picking_time_visibility')
        self.show_picking_time = show_picking_time

    @api.one
    def _compute_show_delivery_time(self):
        show_delivery_time = self.env['rb_delivery.client_configuration'].get_param('order_show_delivery_time_visibility')
        self.show_delivery_time = show_delivery_time

    @api.one
    def _compute_show_order_type(self):
        default_show_order_type = False
        user = self.env['res.users'].search([('id', '=', self._uid)])
        roles = self.env['rb_delivery.client_configuration'].get_param('order_show_order_type_visibility')
        if roles and type(roles) != bool and roles[0] :
            for role in roles:
                group =self.env['res.groups'].sudo().search([('id','=',role)])
                group_name =  group.code
                if user.has_group(group_name):
                    default_show_order_type = True
                    break
        else :
            default_show_order_type = False
        self.show_order_type = default_show_order_type

    @api.model
    def _default_show_order_type(self):
        default_show_order_type = False
        user = self.env['res.users'].search([('id', '=', self._uid)])
        roles = self.env['rb_delivery.client_configuration'].get_param('order_show_order_type_visibility')
        if roles and type(roles) != bool and roles[0] :
            for role in roles:
                group =self.env['res.groups'].sudo().search([('id','=',role)])
                group_name =  group.code
                if user.has_group(group_name):
                    default_show_order_type = True
                    break
        else :
            default_show_order_type = False

        return default_show_order_type

    def _default_show_picking_time(self):
        default_show_picking_time = self.env['rb_delivery.client_configuration'].get_param('order_show_picking_time_visibility')
        return default_show_picking_time

    def _default_show_delivery_time(self):
        default_show_delivery_time = self.env['rb_delivery.client_configuration'].get_param('order_show_delivery_time_visibility')
        return default_show_delivery_time

    def _default_show_replacement_order(self):
        default_show_replacement_order = False
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        roles = self.env['rb_delivery.client_configuration'].get_param('show_replacement_order')
        if self.env.user._is_admin():
            default_show_replacement_order = True
        else:
            if roles and type(roles) != bool and roles[0] :
                for role in roles:
                    if user.group_id.id == role:
                        default_show_replacement_order = True
                        break
            else :
                default_show_replacement_order = False

        return default_show_replacement_order

    def _default_show_returned_order(self):
        default_show_returned_order = self.env['rb_delivery.client_configuration'].get_param('show_returned_order')
        return default_show_returned_order

    def _default_show_customer_sub_area(self):
        default_show_customer_sub_area = self.env['rb_delivery.client_configuration'].get_param('order_show_customer_sub_area_visibility')
        return default_show_customer_sub_area

    def _get_default_area(self):
        default_area = self.env['rb_delivery.area'].search([('is_default', '=', True)])
        return default_area.id

    @api.one
    def _compute_show_customer_sub_area(self):
        show_customer_sub_area = self.env['rb_delivery.client_configuration'].get_param('order_show_customer_sub_area_visibility')
        self.show_customer_sub_area = show_customer_sub_area


    def get_default_ref_priority(self):
        ref_priority = self.env['rb_delivery.client_configuration'].get_param('reference_id_top_priority')
        return ref_priority

    @api.model
    def _default_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            default_is_block_delivery_fee = True
        else :
            default_is_block_delivery_fee = False
        return default_is_block_delivery_fee

    @api.model
    def _default_is_block_delivery_profit(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_profit:
            default_is_block_delivery_profit = True
        else :
            default_is_block_delivery_profit = False
        return default_is_block_delivery_profit

    @api.model
    def _default_is_block_delivery_profit(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_profit:
            default_is_block_delivery_profit = True
        else :
            default_is_block_delivery_profit = False
        return default_is_block_delivery_profit

    @api.one
    def _compute_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            is_block_delivery_fee = True
        else :
            is_block_delivery_fee = False
        self.is_block_delivery_fee = is_block_delivery_fee

    @api.one
    @api.depends("cost","customer_payment","copy_total_cost")
    def _compute_show_exclamation(self):
        try:
            customer_payment = float(self.customer_payment) if self.customer_payment is not False else ''
        except:
            customer_payment = ''
        if (customer_payment or customer_payment ==0) and (customer_payment < self.copy_total_cost or customer_payment < self.cost):
            self.show_exclamation = True
        else:
            self.show_exclamation = False

    @api.one
    def _get_service_domain(self):
        ids = []
        if self.env.user._is_admin():
            business_user = self.assign_to_business
        else:
            business_user = self.sudo().assign_to_business
        if business_user and business_user.pricelist_id:
            pricelist = business_user.pricelist_id
            services = pricelist.service_id
            if len(services)>0:
                for service in services:
                    if not service.is_default:
                        ids.append(service.id)
            else:
                default_pricelist_id = self.env['rb_delivery.pricelist'].search([('code', '=', 'default_pricelist')])
                services = default_pricelist_id.service_id
                for service in services:
                    if not service.is_default:
                        ids.append(service.id)
        else:
            default_pricelist_id = self.env['rb_delivery.pricelist'].search([('code', '=', 'default_pricelist')])
            services = default_pricelist_id.service_id
            for service in services:
                if not service.is_default:
                    ids.append(service.id)
        return ids

    @api.one
    def compute_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        self.show_country = show_country

    def default_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        return show_country

    @api.onchange('customer_area')
    def _domain_sub_area(self):
        if self.customer_area.country_id.id :
            self.customer_country = self.customer_area.country_id.id

        ids=[]
        if self.customer_area:
            sub_areas = self.env['rb_delivery.sub_area'].search([('show_in_create', '=', True),('parent_id', '=', self.customer_area.id)])
            for sub_area in sub_areas:
                ids.append(sub_area.id)
        if len(ids)>0 or self.customer_area:
            return {'domain': {'customer_sub_area': [('id', 'in', ids)]}}
        else:
            return {'domain': {'customer_sub_area': [('show_in_create', '=', True)]}}

    @api.onchange('customer_country')
    def _domain_area(self):
        ids=[]
        if self.customer_country:
            areas = self.env['rb_delivery.area'].search([('country_id', '=', self.customer_country.id)])
            for area in areas:
                ids.append(area.id)
        if len(ids)>0:
            return {'domain': {'customer_area': [('id', 'in', ids),('show_in_create', '=', True)]}}
        else:
            return {'domain': {'customer_area': [('show_in_create', '=', True)]}}

    @api.one
    @api.depends("assign_to_business")
    def _load_all_service_ids(self):
        domain = self._get_service_domain()
        self.all_service_ids = [(6,0, domain[0])]

    @api.one
    @api.depends("assign_to_business")
    def compute_default_service(self):
        if self._uid == 1 or self._uid == 2:
            business_user = self.assign_to_business
        else:
            business_user = self.sudo().assign_to_business
        if business_user and business_user.pricelist_id:
            service = self.env['rb_delivery.service'].search([('pricelist_id','=',business_user.pricelist_id.id),('is_default','=',True)],limit=1)
            if service:
                self.default_service = service.id


    @api.model
    def _default_make_the_cod_required_field(self):
        default_make_the_cod_required_field= self.env['rb_delivery.client_configuration'].get_param('order_make_the_cod_required_field')
        return default_make_the_cod_required_field

    @api.model
    def _default_show_sender_address_in_waybill(self):
        show_sender_address_in_waybill = self.env['rb_delivery.client_configuration'].get_param('show_sender_address_in_waybill')
        return show_sender_address_in_waybill

    @api.one
    def _compute_make_the_cod_required_field(self):
        cod_required_field = self.env['rb_delivery.client_configuration'].get_param('order_make_the_cod_required_field')
        self.cod_required_field = cod_required_field

    @api.one
    def _compute_is_block_delivery_profit(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_profit:
            is_block_delivery_profit = True
        else :
            is_block_delivery_profit = False
        self.is_block_delivery_profit = is_block_delivery_profit

    @api.one
    def _compute_show_customer_district(self):

        show_customer_district = self.env['rb_delivery.client_configuration'].get_param('display_district_field')
        self.show_customer_district = show_customer_district

    @api.model
    def _default_show_customer_district(self):
        show_customer_district = self.env['rb_delivery.client_configuration'].get_param('display_district_field')
        return show_customer_district

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Name', compute="_compute_order_name", readonly=True)

    order_type_id = fields.Many2one(
        'rb_delivery.order_type', string="Order Type", default=default_order_type,track_visibility="on_change")
    
    order_type_color = fields.Char(track_visibility="on_change", compute='_compute_order_type_colors')

    secondary_order_type_color = fields.Char(track_visibility="on_change", compute='_compute_order_type_colors')

    show_order_type = fields.Boolean('Show Order Type', default=_default_show_order_type, compute="_compute_show_order_type",readonly=True)

    status_last_updated_by = fields.Many2one('res.users', string="Status last updated by", track_visibility=False,readonly=True)

    status_last_updated_on = fields.Datetime(string="Status last updated on", track_visibility=False,readonly=True)

    assign_to_distributor_date = fields.Datetime('Assign To distributor Date',track_visibility="on_change",copy=False)

    sequence = fields.Char('Sequence Number', readonly=False,track_visibility=False,copy=False)

    reference_id = fields.Char('Reference Id',track_visibility="on_change",copy=False,index=True)

    sequence_related = fields.Char('Sequence', related='sequence', track_visibility=False, readonly=True,copy=False)

    customer_name = fields.Char('Receiver Name',track_visibility="on_change",required=False)

    previous_status = fields.Char('Previous Status', track_visibility=False, copy=False)

    previous_status_title = fields.Char('Previous Status Name', track_visibility=False, readonly=True)

    previous_agent = fields.Many2one('rb_delivery.user', 'Previous Agent',copy=False)

    previous_area = fields.Many2one('rb_delivery.area', 'Previous Area',copy=False)

    assign_to_agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change", default=default_driver,domain="[('role_code','=','rb_delivery.role_driver')]")

    money_collector = fields.Many2one( 'res.users', 'Money collector', track_visibility="on_change", copy=False,readonly=1)

    current_drivers = fields.Many2many( 'rb_delivery.user', string='Current drivers',copy=False,readonly=True)

    agent_commercial_number = fields.Char(related='assign_to_agent.commercial_number', string="Agent Commercial Number",readonly=True)

    user_sequence = fields.Char(related='assign_to_agent.user_sequence', track_visibility=False, readonly=True)

    show_agent_commercial_number = fields.Boolean('Show Agent Commercial Number', default=_default_show_agent_commercial_number, compute="_compute_show_agent_commercial_number", readonly=True)

    otp_code = fields.Char('OTP Code',track_visibility="on_change",copy=False)

    #deprecated
    assign_to_distributor  = fields.Many2one('rb_delivery.user', 'Assign to distributor', track_visibility="on_change", domain=_get_driver_users, default=default_driver)

    agent_name  = fields.Char('Agent Name',track_visibility="on_change",related="assign_to_agent.username")

    customer_area_name = fields.Char('Area Name',track_visibility="on_change",related="customer_area.name")

    reject_reason = fields.Many2one('rb_delivery.reject_reason', 'Reject Reason',track_visibility="on_change",copy=False)

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',default=default_payment_type,track_visibility="on_change")

    payment_type_two = fields.Many2one('rb_delivery.payment_type', 'Second payment method',default=default_payment_type,track_visibility="on_change")

    clone_reference = fields.Many2one('rb_delivery.order','Clone Reference', track_visibility="on_change", readonly=True,copy=False)

    replacement_reference = fields.One2many('rb_delivery.order',string='Replacement Reference',inverse_name="clone_reference", readonly=True)

    is_replacement = fields.Boolean('Is Replacement',track_visibility="on_change", default=False, readonly=True,copy=False)

    replacement_order = fields.Boolean('Replacement order',track_visibility="on_change", store=True, default=False,copy=False)

    readonly_replacement_order = fields.Boolean("Required Replacement order",default=False,copy=False)

    show_replacement_order = fields.Boolean('show replacement order',default=_default_show_replacement_order, compute="_compute_show_replacement_order", readonly=True)

    returned_order = fields.Boolean('Returned order',track_visibility="on_change", store=True, default=False,copy=False)

    show_returned_order = fields.Boolean('show returned order',default=_default_show_returned_order, compute="_compute_show_returned_order", readonly=True)

    returned_clone_reference = fields.Many2one('rb_delivery.order','Returned Clone Reference', track_visibility="on_change",copy=False, readonly=True)

    returned_reference = fields.One2many('rb_delivery.order',string='Returned Reference',inverse_name="returned_clone_reference",copy=False, readonly=True)

    is_returned = fields.Boolean('Is Returned',track_visibility="on_change", default=False,copy=False, readonly=True)

    description_tags = fields.Many2many(
        comodel_name = 'rb_delivery.description_tags',
        string = 'Description Tags',
        relation = 'description_tags_order_table',
        column1 = 'description_tag_id',
        column2 = 'order_id',copy=False)

    service = fields.Many2many(
        comodel_name = 'rb_delivery.service',
        string = 'Services',
        relation = 'service_order_table',
        column1 = 'service_id',
        column2 = 'order_id',ondelete='restrict', readonly=False,copy=False)

    all_service_ids = fields.Many2many('rb_delivery.service',string="All services",compute="_load_all_service_ids",copy=False)

    default_service = fields.Many2one('rb_delivery.service',string="Default service",compute="compute_default_service",copy=False,track_visibility="on_change")

    service_fee = fields.Float('Service Fee', compute="get_customer_price",copy=False,track_visibility="on_change",readonly=True)

    extra_service_fee_on_sender = fields.Float('Extra Service Fee on sender',copy=False,track_visibility="on_change")

    extra_service_fee_on_customer = fields.Float('Extra Service Fee on customer',copy=False,track_visibility="on_change")

    show_extra_service_fee_on_sender = fields.Boolean('Show Extra Service Fee on sender',compute="_compute_show_service",default=_default_show_extra_service_fee_on_sender,copy=False,track_visibility="on_change")

    show_extra_service_fee_on_customer = fields.Boolean('Show Extra Service Fee on customer',compute="_compute_show_service",default=_default_show_extra_service_fee_on_customer,copy=False,track_visibility="on_change")

    pricing_message = fields.Text('Pricing message',readonly=True,compute="get_customer_price")

    order_weight = fields.Float("Order Weight",track_visibility="on_change",copy=False)

    returned_discount = fields.Char('Returned / Canceled discount', readonly=True,copy=False)

    returned_value = fields.Char('Returned / Canceled value',readonly=True,copy=False)

    returned_discount_statuses = fields.Char(string = 'Return Discount Status', compute="_compute_returned_status",default=_default_returned_status)

    canceled_discount_statuses = fields.Char(string = 'Canceled Discount Status', compute="_compute_canceled_discount_status",default=_default_canceled_discount_status)

    partner_status = fields.Char(string = "Partner Status", readonly=True,copy=False,track_visibility="on_change")

    internal_partner_status = fields.Char(string = "Internal Partner Status", readonly=True,copy=False,track_visibility="on_change")

    partner_reference_id = fields.Char(string = "Partner Reference ID", readonly=True,copy=False,track_visibility="on_change")

    partner_reference_id_barcode = fields.Binary('Barcode', compute="create_partner_ref_id_barcode",copy=False)

    partner_reference_id_qr_code = fields.Binary('Partner reference QR code', compute="create_partner_ref_id_qr_code",copy=False)

    is_partner_order = fields.Boolean(string = "Is Partner Order", default=False,copy=False,track_visibility="on_change")

    is_sender_partner_order = fields.Boolean(string = "Is Sender Partner Order", default=False,copy=False,track_visibility="on_change")

    assign_to_business = fields.Many2one(
        'rb_delivery.user', 'Sender', default=default_business,track_visibility="on_change",required=False,domain="[('role_code','=','rb_delivery.role_business')]")

    error_log = fields.Char('Error log')

    business_issues = fields.Char('Business has issues',compute="get_onboarding_errors",translate=False)

    active = fields.Boolean('Active', default=True,track_visibility="on_change")

    inclusive_delivery = fields.Boolean("Inclusive Delivery Cost",track_visibility="on_change" ,store=True,readonly=False)

    business_name = fields.Char(related='assign_to_business.username', readonly=True)

    commercial_name = fields.Char(related='assign_to_business.commercial_name', readonly=True)

    commercial_number = fields.Char(related='assign_to_business.commercial_number', readonly=True)

    business_mobile_number = fields.Char(related='assign_to_business.mobile_number', readonly=True)

    agent_mobile_number = fields.Char(related='assign_to_agent.mobile_number', readonly=True)

    second_agent_mobile_number = fields.Char(related='assign_to_agent.second_mobile_number', readonly=True)

    second_business_mobile_number = fields.Char('Sender second mobile number',related='assign_to_business.second_mobile_number', readonly=True)

    business_whatsapp_mobile = fields.Char(related='assign_to_business.whatsapp_mobile', readonly=True)

    business_second_whatsapp_mobile = fields.Char(related='assign_to_business.second_whatsapp_mobile', readonly=True)

    agent_whatsapp_mobile = fields.Char(related='assign_to_agent.whatsapp_mobile', readonly=True)

    agent_second_whatsapp_mobile = fields.Char(related='assign_to_agent.second_whatsapp_mobile', readonly=True)

    business_area = fields.Char(string="Business Area",compute='_compute_sender_area', readonly=True, store=True)

    business_parent_id = fields.Many2one('rb_delivery.user',string="Business Parent",related='assign_to_business.user_parent_id', readonly=True, store=True)

    business_sub_area = fields.Char(string="Business Sub Area",compute='_compute_sender_area', readonly=True,store=True)

    business_address = fields.Char(related='assign_to_business.address', readonly=True)

    show_alt_address = fields.Boolean("Show Alternative Address" )

    alt_business_name = fields.Char('Alternative Business Name' ,track_visibility="on_change")

    alt_mobile_number = fields.Char('Mobile Number' ,track_visibility="on_change")

    business_alt_area = fields.Many2one('rb_delivery.area',string="Business Alternative Area",track_visibility="on_change")

    business_alt_address = fields.Char('Business Alternative Address',track_visibility="on_change")

    business_alt_sub_area = fields.Many2one('rb_delivery.sub_area',string='Business Alternative Sub Area', track_visibility="on_change", ondelete='restrict')

    business_location = fields.Char(related='assign_to_business.location', readonly=True)

    business_id = fields.Integer('Business ID',related='assign_to_business.id', readonly=True,store=True)

    business_longitude = fields.Char(related='assign_to_business.longitude', readonly=True)

    business_latitude = fields.Char(related='assign_to_business.latitude', readonly=True)

    business_alt_location = fields.Char('Alternative location',track_visibility="on_change")

    business_alt_longitude = fields.Char('Alternative longitude',track_visibility="on_change")

    business_alt_latitude = fields.Char('Alternative latitude',track_visibility="on_change")

    business_inclusive_delivery = fields.Boolean(related='assign_to_business.inclusive_delivery', string="Business Inclusive Delivery", readonly=True)

    customer_mobile = fields.Char('Receiver Mobile',track_visibility="on_change",required=False)

    previous_customer_mobile_number=fields.Char('Previous Receiver Mobile',track_visibility="on_change")

    second_mobile_number = fields.Char(string='Receiver Second Mobile Number',track_visibility="on_change")

    customer_address = fields.Char('Receiver Address',track_visibility="on_change",required=False)

    address_tag = fields.Many2one('rb_delivery.address_tags','Full Address')

    is_address_tag_values_changed = fields.Boolean('Is address tag values changed')

    business_address_tag = fields.Many2one('rb_delivery.address_tags',string="Business Full Address",related='assign_to_business.address_tag', readonly=True)

    show_country = fields.Boolean('Show country',readonly=True,compute="compute_show_country",  default=default_show_country)

    customer_country = fields.Many2one('rb_delivery.country', string='Receiver Country', track_visibility="on_change", ondelete='restrict', default=default_country)

    customer_area = fields.Many2one('rb_delivery.area', default=_get_default_area , string='Receiver Area', track_visibility="on_change", ondelete='restrict',required=False)

    customer_sub_area = fields.Many2one('rb_delivery.sub_area',string='Receiver Sub Area', track_visibility="on_change", ondelete='restrict')

    business_area_group = fields.Char(compute='_compute_sender_area',string='Business Area Group', readonly=True,store=True)

    customer_area_group = fields.Char(compute='_compute_customer_area_group',string='Customer Area Group', readonly=True,store=True)

    show_customer_sub_area = fields.Boolean('Show Receiver Sub Area', default=_default_show_customer_sub_area, compute="_compute_show_customer_sub_area", readonly=True)

    copy_cost = fields.Float('Cost',compute="_compute_cost", readonly=False, store=True)

    cost = fields.Float('Package Cost',track_visibility="on_change")

    cod_required_field = fields.Boolean('Make COD required field', default=_default_make_the_cod_required_field, compute="_compute_make_the_cod_required_field", readonly=True)

    delivery_cost = fields.Float(
        'Delivery Fee', compute="get_customer_price", store=True,track_visibility="on_change")

    extra_cost = fields.Float('Extra Cost', track_visibility="on_change",copy=False)

    total_cost = fields.Float('Total Amount Computed', compute="_compute_total_cost", readonly=False, store=True)

    copy_total_cost = fields.Float('Total Amount',track_visibility="on_change")

    money_collection_cost = fields.Float('COD Value', compute="_compute_money_collection_cost",track_visibility="on_change",readonly=True,store=True)

    delivered_by = fields.Many2one('rb_delivery.user',string='Delivered By', track_visibility="on_change",readonly=True,copy=False)

    delivery_date = fields.Datetime('Delivery Date',track_visibility="on_change",readonly=True,copy=False)

    note = fields.Text('Note', track_visibility="on_change")

    product_note = fields.Text('Product Note', track_visibility="on_change",copy=False)

    driver_note = fields.Text('Driver Note', track_visibility="on_change",copy=False)

    show_exclamation = fields.Boolean('Show exclamation mark',compute="_compute_show_exclamation",store=True)

    stuck_comment = fields.Text('Stuck Comment', track_visibility="on_change",copy=False)
    # only seen by specific gorups
    special_note = fields.Text('Special Note', track_visibility="on_change",copy=False)

    customer_payment  = fields.Char('Customer Payment',track_visibility="on_change")

    customer_payment_one  = fields.Char('First customer payment',track_visibility="on_change", readonly=True)

    customer_payment_two  = fields.Char('Second customer payment',track_visibility="on_change", readonly=True)

    paid = fields.Boolean("Paid",track_visibility="on_change")

    hide_paid_field = fields.Boolean("hide paid field", default=_default_hide_paid_field, compute="_compute_hide_paid_field")

    customer_discount  = fields.Float('Customer Discount',track_visibility="on_change", compute="_compute_customer_discount")

    order_date = fields.Date(compute="_get_date", store=True,track_visibility="on_change")

    barcode = fields.Binary('Barcode', compute="create_barcode")

    barcode_reference = fields.Binary('Reference Barcode', compute="create_barcode")

    qr_code_reference = fields.Binary('Reference QR code', compute="create_reference_qr_code")

    is_manager = fields.Boolean('Is manager', compute="check_user")

    is_super_manager = fields.Boolean('Is Super manager', compute="check_user")

    is_block_delivery_fee = fields.Boolean('Is Block Delivery Fee', default=_default_is_block_delivery_fee, compute="check_user", readonly=True)

    is_block_delivery_profit = fields.Boolean('Is Block Delivery Profit', default=_default_is_block_delivery_profit,compute="_compute_is_block_delivery_profit", readonly=True)

    is_business = fields.Boolean('Is Business', compute="check_user", default=default_is_business)

    is_configuration_manager = fields.Boolean('Is Business', compute="check_user", default=default_is_configuration_manager)

    show_only_total = fields.Boolean('Show only total', default=_default_show_only_total, compute="_compute_show_only_total", readonly=True)

    default= fields.Char('Default',readonly=True)

    is_data_entry = fields.Boolean('Is Data Entry', compute="check_user", default=default_is_data_entry)

    has_children = fields.Boolean('Have Children', compute="check_children", default=default_has_children)

    last_state = fields.Char('Last State',copy=False)

    web_color = fields.Char('Web Color',copy=False)

    delivery_cost_on_sender = fields.Boolean('Delivery Fee on sender', store=True,track_visibility="on_change",copy=False)

    delivery_cost_on_customer = fields.Boolean('Delivery Fee on customer', store=True,track_visibility="on_change",default=_default_delivery_fee_on_customer,copy=False)

    show_delivery_cost_on_sender = fields.Boolean('Show delivery fee on sender', default=_default_show_cost_on_sender, compute="_compute_show_cost_on_sender", store=False, readonly=True)

    show_alternate_address = fields.Boolean('Show alternative address', default=_default_show_alternate_address, compute="_compute_show_alternate_address", readonly=True)

    show_inclusive_delivery = fields.Boolean('Show Inclusive Delievry', default=_default_show_inclusive_delivery, compute="_compute_show_inclusive_delivery", readonly=True)

    show_follower_info_in_waybill = fields.Boolean('Show Follower info in Boolisa', readonly=True, default=_default_show_follower_info_in_waybill, compute="_compute_show_follower_info_in_waybill")

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status,copy=False,index=True)

    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True , track_visibility="on_change")

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    collection_id = fields.Many2one('rb_delivery.multi_print_orders_money_collector',"In collection",copy=False,readonly=True)

    returned_collection_id = fields.Many2one('rb_delivery.returned_money_collection',"In returned collection",copy=False)

    agent_collection_id = fields.Many2one('rb_delivery.agent_money_collection',"In Agent collection",copy=False)

    agent_returned_collection_id = fields.Many2one('rb_delivery.agent_returned_collection', "In returned Agent collection",copy=False)

    runsheet_collection_id = fields.Many2one('rb_delivery.runsheet',"In Runsheet collection",copy=False)

    attachment_ids = fields.One2many('rb_delivery.order_attachment', 'order_id', string='Attachments', track_visibility="on_change")

    advanced_tags = fields.Char('Tags',copy=False, compute='_getTags_icons')

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)

    solve_stuck_comment = fields.Text('Solve Stuck Comment', track_visibility="on_change",copy=False)

    is_minus_payment = fields.Boolean('Is Minus payment')

    is_plus_payment = fields.Boolean('Is Plus payment')

    total_value_imported = fields.Float('Order Value')

    @api.onchange('address_tag')
    def _onchange_address_tag(self):
        if not self._context.get('skip_address_constraint'):
            if self.address_tag:
                self.customer_area = self.address_tag.area_id.id
                self.customer_sub_area = self.address_tag.sub_area_id.id

    @api.constrains('address_tag')
    def _check_address_tag(self):
        if not self._context.get('skip_address_constraint'):
            for record in self:
                if record.address_tag:
                    new_area = record.address_tag.area_id.id
                    new_sub_area = record.address_tag.sub_area_id.id
                    if record.customer_area and record.customer_sub_area and record.customer_area.id != new_area or record.customer_sub_area.id != new_sub_area:
                        record.with_context(skip_address_constraint=True).write({
                            'customer_area': new_area,
                            'customer_sub_area': new_sub_area
                        })


    @api.model
    def get_status(self):
     fields=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
     status_list=[]
     next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])

     for status in next_statuses:
         status_list.append((status.name,status.title))
     return status_list

    cus_whatsapp_mobile = fields.Char('Customer Whatsapp Mobile Number',track_visibility="on_change")

    cus_second_whatsapp_mobile = fields.Char('Customer Second Whatsapp Mobile Number',track_visibility="on_change")

    discount = fields.Float('Discount',track_visibility="on_change",copy=False)

    required_from_business = fields.Float('Net Value',compute="_compute_required_from_business",track_visibility="on_change",readonly=True,store=True)

    seq_exist = fields.Char('AWB # Exist',track_visibility="on_change")

    ref_priority = fields.Boolean('Ref priority',compute="_compute_ref_priority",default=get_default_ref_priority)

    seq_priority = fields.Boolean('Seq priority', compute="_compute_seq_priority")

    financial_state = fields.Selection([
        ('paid', 'Paid'),
        ('not_paid', 'Not Paid')], track_visibility="on_change", default="not_paid",readonly = True,copy=False)

    location_url = fields.Char('URL location', compute="_compute_location_url", readonly=True)
    follower_address = fields.Char('Follower Address', track_visibility="on_change")

    follower_ref_id = fields.Char('Follower Reference ID',track_visibility="on_change")

    follower_area = fields.Char('Follower Area', track_visibility="on_change")

    follower_store_name = fields.Char('Follower Store Name',track_visibility="on_change")

    follower_mobile_number = fields.Char('Follower Mobile Number',track_visibility="on_change")

    follower_second_mobile_number = fields.Char('Follower Second Mobile Number',track_visibility="on_change")

    follower_longitude = fields.Char('Follower Longitude',track_visibility="on_change",copy=False)

    follower_latitude = fields.Char('Follower Latitude',track_visibility="on_change",copy=False)

    show_follower_info = fields.Boolean(related='assign_to_business.has_customers', readonly=True)

    longitude = fields.Char('Longitude',track_visibility="on_change",copy=False)

    latitude = fields.Char('Latitude',track_visibility="on_change",copy=False)

    extra_agent_cost = fields.Float('Extra Agent Cost',track_visibility="on_change")

    agent_cost=fields.Float(string="Agent Cost",store=True,compute="_compute_agent_cost",track_visibility=False)

    required_to_company=fields.Float(string="Required To company",store=True,compute="_compute_required_to_company",track_visibility="on_change")

    delivery_profit=fields.Float(string="Delivery Profit",store=True,compute="_compute_delivery_profit")

    customer_village = fields.Char('Receiver Village',track_visibility="on_change")

    show_customer_village = fields.Boolean('Show Receiver Village', default=_default_show_customer_village, compute="_compute_show_customer_village", readonly=True)

    customer_neighbour = fields.Char('Receiver Neighbour',track_visibility="on_change")

    show_customer_neighbour = fields.Boolean('Show Receiver Neighbour', default=_default_show_customer_neighbour, compute="_compute_show_customer_neighbour", readonly=True)

    customer_building_number  = fields.Char('Receiver Building Number',track_visibility="on_change")

    show_customer_building_number  = fields.Boolean('Show Receiver Building Number', default=_default_show_customer_building_number, compute="_compute_show_customer_building_number", readonly=True)

    no_of_items = fields.Char('Number of Items',track_visibility="on_change")

    show_amount = fields.Boolean('Show Number of Items', default=_default_show_no_of_items, compute="_compute_show_no_of_items", readonly=True)

    show_service = fields.Boolean('Show Service', default=_default_show_service, compute="_compute_show_service", readonly=True)

    picking_from_time = fields.Datetime(string="Picking From Time")

    picking_to_time = fields.Datetime(string="Picking To Time")

    show_picking_time = fields.Boolean('Show Picking Time', default=_default_show_picking_time, compute="_compute_show_picking_time",readonly=True)

    delivery_from_time = fields.Datetime(string="Delivery From Time")

    delivery_to_time = fields.Datetime(string="Delivery To Time")

    show_delivery_time = fields.Boolean('Show Delivery Time', default=_default_show_delivery_time, compute="_compute_show_delivery_time",readonly=True)

    chat_id = fields.One2many('rb_delivery.chat_notification',string='ChatID',inverse_name='order_id')

    is_from_collection = fields.Boolean("Is from collection", default=True)

    reschedule_date = fields.Datetime(string="Scheduled Delivery",track_visibility="on_change")

    customer_location = fields.Char('Customer Location',track_visibility="on_change")

    batch_number = fields.Char('Batch Number',track_visibility="on_change",copy=False)

    actual_created_date = fields.Datetime('Actual Created Date',track_visibility="on_change",copy=False)

    all = fields.Char('all')

    show_sender_address_in_waybill = fields.Boolean('Show sender address', default=_default_show_sender_address_in_waybill, compute="_show_sender_address_in_waybill", readonly=True)

    show_description_tags_in_waybill = fields.Boolean('Show Description Tags', default=_default_show_description_tags, compute="_compute_show_description_tags", readonly=True)

    customer_district_id = fields.Many2one('rb_delivery.district',string='Receiver District', track_visibility="on_change", ondelete='restrict',required=False)

    show_customer_district=fields.Boolean('show customer district in order form', default=_default_show_customer_district, compute="_compute_show_customer_district", readonly=True)
    route_sequence = fields.Integer(default=1)

    tracking_uid = fields.Char()

    tracking_url = fields.Char()

    tracking_url_expires_at = fields.Datetime()

    tracking_url_generated_by = fields.Many2one('res.users')

    is_prepaid_order = fields.Boolean('Is Pre-Paid Order',readonly=True,copy=False)

    commission = fields.Float('Commission', compute='compute_commission', store = True,track_visibility="on_change")

    extra_commission = fields.Float('Extra Commission',track_visibility="on_change")

    affiliator = fields.Many2one('rb_delivery.user','Affiliator' , track_visibility="on_change",related="assign_to_business.affiliator")

    tracking_uid = fields.Char()

    tracking_url = fields.Char()

    tracking_url_expires_at = fields.Datetime()

    tracking_url_generated_by = fields.Many2one('res.users')

    is_ready_for_return = fields.Boolean(string="Is Ready For Return")

    onboarding_error_ids = fields.Many2many(comodel_name = 'rb_delivery.onboarding_error', string = 'Onboarding errors', relation = 'onboarding_errors_order_item', column1 = 'order_id', column2 = 'onboarding_error_id',compute="get_onboarding_errors")

    follow_up_order = fields.One2many('rb_delivery.follow_up_order',string='Follow up orders',inverse_name='order_id',track_visibility="on_change")

    follow_up_order_sequences = fields.Char('Follow up order sequences',related='follow_up_order.follow_up_sequence',readonly=True)

    follow_up_orders_map = fields.Char('Follow up orders map',track_visibility="on_change")

    is_in_collection = fields.Text('Is in collection',compute="_compute_is_collection_icon")

    is_agent_collection = fields.Text('Is in agent collection',compute="_compute_is_agent_collection_icon")

    receiver_is_business = fields.Boolean('Receiver Is Business',default=False)

    receiver_business = fields.Many2one('rb_delivery.user', 'Receiver',track_visibility="on_change",domain="[('role_code','=','rb_delivery.role_business')]")

    picked_up_by = fields.Many2one('rb_delivery.user','Picked Up By',readonly=True,copy=False,track_visibility="on_change")

    pickup_agent_cost = fields.Float(string="Picked Up By Agent Cost",store=True,compute="_compute_pickup_agent_cost")

    saved_value_for_pickup_agent_cost = fields.Float('Saved Value For Picked Up Agent Cost',readonly=False)

    is_public = fields.Boolean(string='Is Public')


    can_see_logs_button = fields.Boolean(compute='_compute_can_see_logs_button')

    money_received_date = fields.Datetime('Money Received Date',track_visibility="on_change",readonly=True,copy=False)

    zone_id = fields.Many2one(
        comodel_name='rb_delivery.area_zone',
        string='Zone',
        relation='rb_area_zone_order_rel',
        column1='order_id',
        column2='zone_id',
        track_visibility='on_change'
    )

    order_action = fields.Many2one('rb_delivery.order_action','Action Required',track_visibility="on_change",copy=False)

    currency_id = fields.Many2one('res.currency', string='Currency',track_visibility="on_change")

    @api.multi
    @api.depends('order_type_id.order_type_color', 'order_type_id.order_type_secondary_color')
    def _compute_order_type_colors(self):
        for record in self:
            if record.order_type_id:
                record.order_type_color = record.order_type_id.order_type_color
                record.secondary_order_type_color = record.order_type_id.order_type_secondary_color
            else:
                record.order_type_color = False
                record.secondary_order_type_color = False

    @api.multi
    def _compute_can_see_logs_button(self):
        for record in self:
            user = self.env.user
            group_ids = self.env['rb_delivery.client_configuration'].get_param('roles_who_can_not_see_order_logs')
            if group_ids:
                user_group_ids = user.groups_id.ids
                if any(gid in user_group_ids for gid in group_ids):
                    record.can_see_logs_button = False
                else:
                    record.can_see_logs_button = True
            else:
                record.can_see_logs_button = True

    @api.onchange('customer_location')
    def _onchange_customer_location(self):
        if self.customer_location:
            expanded_url = self.expand_google_maps_short_url(self.customer_location)
            coordinates = self.extract_coordinates_from_url(expanded_url)

            # coords = self.get_coordinates_from_google_maps_link(self.customer_location,google_map_key)
            if coordinates:
                self.latitude, self.longitude = coordinates

    @api.onchange('latitude', 'longitude')
    def _onchange_lat_long(self):
        if self.latitude and self.longitude:
            self.customer_location = self._generate_location_link(self.latitude, self.longitude)
    
    @api.onchange('delivery_cost_on_customer')
    def _onchange_delivery_cost_on_customer(self):
        if self.delivery_cost_on_customer:
            self.delivery_cost_on_sender = False

    @api.onchange('delivery_cost_on_sender')
    def _onchange_delivery_cost_on_sender(self):
        if self.delivery_cost_on_sender:
            self.delivery_cost_on_customer = False


    @api.onchange('business_alt_location')
    def _onchange_alternative_location(self):
        if self.business_alt_location:
            expanded_url = self.expand_google_maps_short_url(self.business_alt_location)
            coordinates = self.extract_coordinates_from_url(expanded_url)

            if coordinates:
                self.business_alt_latitude, self.business_alt_longitude = coordinates

    @api.onchange('business_alt_latitude', 'business_alt_longitude')
    def _onchange_alternative_lat_long(self):
        if self.business_alt_latitude and self.business_alt_longitude:
            self.business_alt_location = self._generate_location_link(self.business_alt_latitude, self.business_alt_longitude)

    def expand_google_maps_short_url(self,short_url):
        """
        Expand the shortened Google Maps URL to its full form using a web request.

        Parameters:
        short_url (str): The shortened Google Maps URL.

        Returns:
        str: The expanded URL.
        """
        response = requests.head(short_url, allow_redirects=True)
        response.status_code  # 302
        response.url  # http://github.com, not https.
        response.headers
        return response.url

    def extract_coordinates_from_url(self, url):
        """
        Extract coordinates from the expanded Google Maps URL.
        Parameters:
        url (str): The expanded Google Maps URL.
        Returns:
        tuple: A tuple containing the latitude and longitude as floats, or None if not found.
        """
        # Patterns to match different URL formats
        patterns = [
            r'@([-0-9.]+),([-0-9.]+),([0-9.]+)z',                # Matches '@latitude,longitude,zoom'
            r"search/(-?\d+\.\d+),\s*(-?\d+\.\d+)",              # Matches 'search/latitude, longitude'
            r"search/([\d\.\+\-]+),([\d\.\+\-]+)",               # Matches 'search/latitude,longitude' (with optional spaces)
            r"q=([-+]?\d*\.\d+),([-+]?\d*\.\d+)",                # Matches 'q=latitude,longitude'
            r'@([0-9.]+),([0-9.]+)',                             # Matches '@latitude,longitude'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return float(match.group(1)), float(match.group(2))

        return None

    def _generate_location_link(self, latitude, longitude):
        return f'https://maps.google.com/?q={latitude},{longitude}'

    @api.multi
    @api.depends('assign_to_business','assign_to_agent')
    def get_onboarding_errors(self):
        for rec in self:
            order_onboarding_error_ids = self.env['rb_delivery.onboarding_error'].sudo().search([('record_id','=',rec.id),('model_name','=','rb_delivery.order')])
            onboarding_error_ids = order_onboarding_error_ids.ids
            if len(rec.assign_to_business.onboarding_error_ids)>0:
                rec.business_issues = _("The business have some issues, for more information check issues section in user record. ")
                onboarding_error_ids = onboarding_error_ids + rec.assign_to_business.onboarding_error_ids.ids
            if len(rec.assign_to_agent.onboarding_error_ids)>0:
                onboarding_error_ids = onboarding_error_ids + rec.assign_to_agent.onboarding_error_ids.ids
            rec.onboarding_error_ids = [(6,0,onboarding_error_ids)]


    @api.multi
    @api.depends('collection_id')
    def _compute_is_collection_icon(self):
        for rec in self:
            if rec.collection_id:
                rec.is_in_collection = '<img src="/rb_delivery/static/src/img/successAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'
            else:
                rec.is_in_collection = '<img src="/rb_delivery/static/src/img/errorAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'

    @api.multi
    @api.depends('agent_collection_id')
    def _compute_is_agent_collection_icon(self):
        for rec in self:
            if rec.agent_collection_id:
                rec.is_agent_collection = '<img src="/rb_delivery/static/src/img/successAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'
            else:
                rec.is_agent_collection = '<img src="/rb_delivery/static/src/img/errorAsset.svg" alt="Agent collection icon" class="olivery-tags-icon" />'

    def get_mobile_scan_logs(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_mobile_scan_logs').id
        domain = ['|',('scanned_value', '=', self.sequence),('scanned_value','=',self.reference_id)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Mobile Scan Barcode Logs',
            'res_model': 'rb_delivery.mobile_scan_logs',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def get_onboarding_error_ids(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_onboarding_error').id
        domain = [('id', 'in', self.onboarding_error_ids.ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Onboarding errors',
            'res_model': 'rb_delivery.onboarding_error',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}


    def generate_tracking_link(self):
        import uuid
        error_message = ""
        company = self.env['res.company'].sudo().search([],limit=1)
        if not self.assign_to_agent:
            error_message += _('Please set agent to the order!\n')
        if self.assign_to_agent and (not self.assign_to_agent.longitude or not self.assign_to_agent.latitude):
            error_message += _('Make sure agent is sharing location!\n')

        if not company or not company.base_url:
            error_message += _('Please set your company base url\n')
        elif not error_message:
            expiry_time = self.env['rb_delivery.client_configuration'].get_param('tracking_link_expiry_time')
            tracking_uid = str(uuid.uuid4())
            url = company.base_url
            username = self.env.user.name
            message = _("Update tracking info by user %s, this means that this order has a link anyone who has this link can access driver location.")%(username)
            values = {
                'tracking_uid' : tracking_uid,
                'tracking_url' : url+'/track_driver/' + str(tracking_uid),
                'tracking_url_expires_at' : datetime.now()+relativedelta(hours=int(expiry_time)),
                'tracking_url_generated_by':self._uid            }
            data = {'uid':self._uid,'message':message,'records':self,'values':values,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)


        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(206,self.id,{'error_message':error_message})


    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    @api.depends('assign_to_business')
    @api.multi
    def _compute_sender_area(self):
        for rec in self:
            if not rec.sudo().assign_to_business:
                 current_business=self.default_business()
                 if current_business:
                    rec.sudo().assign_to_business=current_business
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.area_id:
                rec.business_area = rec.sudo().assign_to_business.area_id.name
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.sub_area:
                rec.business_sub_area = rec.sudo().assign_to_business.sub_area.name
            if rec.sudo().assign_to_business and rec.sudo().assign_to_business.area_id and rec.sudo().assign_to_business.area_id.parent_id:
                rec.business_area_group = rec.sudo().assign_to_business.area_id.parent_id.name
    @api.depends('customer_area')
    @api.multi
    def _compute_customer_area_group(self):
        for rec in self:
            if rec.customer_area and rec.customer_area.parent_id:
                rec.customer_area_group = rec.customer_area.parent_id.name



    def refresh_address(self):
        # Grouping the records by address_tag
        address_tag_groups = defaultdict(lambda: self.browse())

        for rec in self:
            if rec.address_tag:
                address_tag_groups[rec.address_tag] |= rec

        # Iterating over each group and calling olivery_sudo once per group
        for address_tag, recordset in address_tag_groups.items():
            data = {
                'uid': self._uid,
                'message': _("Address has been refreshed."),
                'records': recordset,  # Passing the actual recordset instance
                'values': {
                    'is_address_tag_values_changed': False,
                    'customer_area': address_tag.area_id.id,
                    'customer_sub_area': address_tag.sub_area_id.id
                },
                'update': True
            }
            self.env['rb_delivery.utility'].olivery_sudo(data)


    @api.constrains('customer_mobile')
    @api.multi
    def _check_mobile_number(self):
        for rec in self:
            if rec.customer_mobile:
                mobile = self.env['rb_delivery.utility'].check_mobile_number(rec.customer_mobile)
                if mobile != False:
                    rec.customer_mobile = mobile

    def _check_customer_mobile_number(self,mobile_number,field_name=_('Customer mobile')):
        mobile = self.env['rb_delivery.utility'].check_mobile_number(mobile_number,field_name)
        if mobile != False:
            return mobile

    def _check_second_customer_mobile_number(self,second_mobile_number,field_name=_('Second mobile number')):
        mobile = self.env['rb_delivery.utility'].check_mobile_number(second_mobile_number,field_name)
        if mobile != False:
            return mobile

    @api.constrains('second_mobile_number')
    @api.multi
    def _check_second_number(self):
        for rec in self:
            if rec.second_mobile_number:
                mobile = self.env['rb_delivery.utility'].check_mobile_number(rec.second_mobile_number,_('Second mobile number'))
                if mobile != False:
                    rec.second_mobile_number = mobile

    @api.multi
    @api.depends('delivery_cost','extra_commission','state','assign_to_business')
    def compute_commission(self):
        statuses_ids = self.env['rb_delivery.client_configuration'].get_param('calculate_commission_value')
        statuses = self.env['rb_delivery.status'].browse(statuses_ids).mapped('name')

        for record in self:
            commission = record.extra_commission
            if record.sudo().assign_to_business:
                affiliator = record.sudo().assign_to_business.affiliator
                if affiliator and affiliator.user_request_commission:
                    if statuses and len(statuses) > 0 :
                        if record.state in statuses :
                            commission_type=affiliator.commission_type
                            if commission_type == "fixed":
                                commission += affiliator.commission_value
                            elif commission_type == "percentage":
                                commission += (affiliator.commission_value / 100) * record.delivery_cost
            record.commission = commission

    @api.onchange('replacement_order')
    def _replacement_order(self):
        if self.replacement_order:
            return {
                'warning':{
                    'title': _('Warning'),
                    'message': _('This action will create a duplicate order'),
                },
            }

    @api.one
    def _show_sender_address_in_waybill(self):
        self.show_sender_address_in_waybill = self.env['rb_delivery.client_configuration'].get_param('show_sender_address_in_waybill')

    @api.onchange('reference_id','assign_to_business')
    def _change_reference(self):
        domain = [('state','!=','deleted')]
        if self.id:
            domain.append(('id', '!=', self.id))
        if self.reference_id:
            domain.append(('reference_id','=',self.reference_id))
            orders = self.env['rb_delivery.order'].sudo().search(domain)
            if orders and len(orders) > 0:
                if self.assign_to_business:
                    for order in orders:
                        if order.assign_to_business and order.assign_to_business == self.assign_to_business :
                            self.seq_exist = (_('Reference ID '+self.reference_id+' already exist in order '+order.sequence+' and saving will update existing one'))




    @api.constrains('reference_id','sequence')
    def _change_sequ(self):
        print('change sequ')
        print(self)
        accept_english_letter = self.env['rb_delivery.client_configuration'].get_param('allow_reference_id_accept_the_english_letter')
        for rec in self:
            print(rec)
            import re
            number = rec.reference_id
            sequence = rec.sequence
            check_arabic_letter = self.env['rb_delivery.utility'].check_arabic_letter(number)
            sequence_check_arabic_letter = self.env['rb_delivery.utility'].check_arabic_letter(sequence)
            pattern ="^[\u0621-\u064A\u0660-\u06690-9 ]+$"
            arabic_letter_pattern ="[أ  ب  ت  ث  ج  ح  خ  د  ذ  ر  ز  س  ش  ص  ض  ط  ظ  ع  غ  ف  ق  ك  ل  م  ن  هـ  و ي]"
            english_letter_pattern = "[A aB bC cD dE eF fG gH hI iJ jK kL lM mN nO oP pQ qR rS sT tU uV vW wX xY yZ z]"
            if number and re.findall(str(english_letter_pattern), str(number)):
                if not accept_english_letter and not re.findall(str(pattern), str(number)) :
                    self.env['rb_delivery.error_log'].raise_olivery_error(201,rec.id,{'reference_id':rec.reference_id})

            if sequence_check_arabic_letter or re.findall(str(arabic_letter_pattern), str(sequence)) :
                self.env['rb_delivery.error_log'].raise_olivery_error(202,rec.id,{'sequence':rec.sequence})

            if check_arabic_letter or re.findall(str(arabic_letter_pattern), str(number)) :
                self.env['rb_delivery.error_log'].raise_olivery_error(203,rec.id,{'reference_id':rec.reference_id})

            if number and not re.findall(str(arabic_letter_pattern), str(number)) :
                number = unidecode(u''+number)

    @api.constrains('picking_from_time')
    def _check_picking_from_time(self):
        for rec in self:
            if rec.picking_from_time:
                fmt = '%Y-%m-%d %H:%M:%S'
                now_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                picking_from_time = rec.picking_from_time.strftime('%Y-%m-%d %H:%M:%S')
                d1 = datetime.strptime(now_date, fmt)
                d2 = datetime.strptime(picking_from_time, fmt)
                date_difference = d2 - d1
                seconds = date_difference.seconds
                minutes = (seconds / 60)
                if date_difference and date_difference.days == -1 :
                    minutes = (seconds / 60)
                    hour =  (minutes / 60)
                    if d2.hour > d1.hour:
                        pass
                    elif d2.hour == d1.hour:
                        if d2.minute >= d1.minute :
                            pass
                        else:
                            self.env['rb_delivery.error_log'].raise_olivery_error(207,rec.id,{'current_date':now_date, 'orders_sequences': rec.sequence})
                    else:
                        self.env['rb_delivery.error_log'].raise_olivery_error(207,rec.id,{'current_date':now_date, 'orders_sequences': rec.sequence})

                elif  date_difference and date_difference.days < -1  :
                    self.env['rb_delivery.error_log'].raise_olivery_error(207,rec.id,{'current_date':now_date, 'orders_sequences': rec.sequence})


    @api.constrains('picking_to_time')
    def _check_picking_to_time(self):
        for rec in self:
            if rec.picking_to_time and rec.picking_from_time:

                fmt = '%Y-%m-%d %H:%M:%S'
                now_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                picking_from_time = rec.picking_from_time.strftime('%Y-%m-%d %H:%M:%S')
                picking_to_time = rec.picking_to_time.strftime('%Y-%m-%d %H:%M:%S')
                d1 = datetime.strptime(picking_from_time, fmt)
                d2 = datetime.strptime(picking_to_time, fmt)

                date_difference = d2 - d1
                seconds = date_difference.seconds
                minutes = (seconds / 60)
                hour_difference = d2.hour - d1.hour
                if date_difference and date_difference.days == -1 or date_difference and date_difference.days == 0:
                    if d2.hour > d1.hour and hour_difference >= 2:
                        pass
                    else:
                        self.env['rb_delivery.error_log'].raise_olivery_error(207,rec.id,{'current_date':now_date, 'orders_sequences': rec.sequence})

                elif  date_difference and date_difference.days < -1  :
                    self.env['rb_delivery.error_log'].raise_olivery_error(207,rec.id,{'current_date':now_date, 'orders_sequences': rec.sequence})


    def check_order_exist(self,org_values):
        business_user = self.env['rb_delivery.user'].sudo().search([('user_id.id', '=', self._uid),('role_code','=','rb_delivery.role_business')])
        order = ''

        if 'sequence' in org_values and org_values['sequence']:
            org_values['sequence'] = self.normalize_reference_id(org_values['sequence'])
            domain = [('sequence','=',org_values['sequence']),('state','!=','deleted')]
            if self.id:
                domain.append(('id', '!=', self.id))
            order = self.env['rb_delivery.order'].search(domain,limit=1)

            if len(order)==0:
                if 'reference_id' in org_values and org_values['reference_id']:
                    org_values['reference_id'] = self.normalize_reference_id(org_values['reference_id'])
                    domain = [('reference_id','=',org_values['reference_id']),('state','!=','deleted')]
                    if self.id:
                        domain.append(('id', '!=', self.id))
                    orders = self.env['rb_delivery.order'].sudo().search(domain)
                    if len(orders)==0:
                        return False
                    if ('assign_to_business' in org_values and org_values['assign_to_business']) or business_user:
                        business_id = ''
                        if business_user:
                            business_id = business_user.id
                        else:
                            business_id = org_values['assign_to_business']
                        self.check_family_orders_reference(business_id,orders)

                    for exist_order in orders:
                        if exist_order.sudo().assign_to_business:
                            business = exist_order.sudo().assign_to_business
                            if 'assign_to_business' in org_values and org_values['assign_to_business'] and business.id == org_values['assign_to_business']:
                                order = exist_order
                                break
                            elif business_user and business.id == business_user.id :
                                order = exist_order
                                break
                        else:
                            order = exist_order

        elif 'reference_id' in org_values and org_values['reference_id']:
            org_values['reference_id'] = str(org_values['reference_id'])
            org_values['reference_id'] = org_values['reference_id'].replace(" ", "")
            org_values['reference_id'] = self.normalize_reference_id(org_values['reference_id'])
            domain = [('reference_id','=',org_values['reference_id']),('state','!=','deleted')]
            if self.id:
                domain.append(('id', '!=', self.id))
            orders = self.env['rb_delivery.order'].sudo().search(domain)
            if len(orders)==0:
                return False
            if ('assign_to_business' in org_values and org_values['assign_to_business']) or business_user:
                business_id = ''
                if business_user:
                    business_id = business_user.id
                else:
                    business_id = org_values['assign_to_business']
                self.check_family_orders_reference(business_id,orders)

            for exist_order in orders:
                if exist_order.sudo().assign_to_business:
                    business = exist_order.sudo().assign_to_business
                    if 'assign_to_business' in org_values and org_values['assign_to_business'] and business.id == org_values['assign_to_business']:
                        order = exist_order
                        break
                    elif business_user and business.id == business_user.id:
                        order = exist_order
                        break
                else:
                    order = exist_order
        return order

    def _check_reference_sequence(self,org_values):
        override_allowed_states = self.env['rb_delivery.client_configuration'].get_param('allowed_states_to_overwrite_orders')
        states = []
        if override_allowed_states:
            for state in override_allowed_states:
                status = self.env['rb_delivery.status'].sudo().search([('id','=',state)])
                states.append(status.name)
        order = self.check_order_exist(org_values)
        if order:
            if "message_attachment_count" in org_values:
                org_values.pop("message_attachment_count")

            if "__last_update" in org_values:
                org_values.pop("__last_update")

            if "assign_to_agent" in org_values:
                org_values.pop("assign_to_agent")

            if "assign_to_distributor_date" in org_values:
                org_values.pop("assign_to_distributor_date")

            if 'state' in org_values and not org_values['state']:
                org_values.pop("state")
                
            if order.state in states:
                #This is added since when updating sequence it will update all related models records by default, so this to prevent this from happening
                if org_values.get('sequence') and order.sequence == org_values.get('sequence'):
                    del org_values['sequence']
                order.sudo(self._uid).write(org_values)

                return order

            else:
                current_status = self.env['rb_delivery.status'].sudo().search([('name','=',order.state),'|',('status_type','=',False),('status_type','=','olivery_order')])
                self.env['rb_delivery.error_log'].raise_olivery_error(208,self.id,{'order_status':current_status.title})

    # inherit module[olivery_barcode_generator]

    def check_family_orders_reference(self,business_id,orders):

        business = self.env['rb_delivery.user'].search([('id','=',business_id)])
        family_ids = []
        if business.sudo().user_parent_id:
            family_ids.append(business.sudo().user_parent_id.id)

            parent_childs = list(filter(lambda user_id: user_id != business_id , business.sudo().user_parent_id.child_ids.ids))
            family_ids += parent_childs
        if business.sudo().child_ids:
            family_ids += business.child_ids.ids
        family_order = orders.filtered(lambda order: order.sudo().assign_to_business.id in family_ids and order.sudo().assign_to_business.id != business_id)
        if family_order :
            sequences = ''
            for order in family_order:
                sequences += order.sequence
            self.env['rb_delivery.error_log'].raise_olivery_error(209,self.id,{'order_sequences':sequences, 'business_name': order.sudo().assign_to_business.mobile_number, 'reference_id': order.sudo().reference_id})

    def normalize_reference_id(self,sequence):
        sequence = str(sequence)
        char_removed_str = self.env['rb_delivery.client_configuration'].get_param('normalize_reference_id')
        if char_removed_str:
            char_remove = char_removed_str.split(',')
            for char in char_remove:
                sequence = sequence.replace(char,"")

        return sequence

    def _check_sequence(self,values):
        EAN = barcode.get_barcode_class('code39')
        image_output = io.BytesIO()
        try:
            ean = EAN(values['sequence'], writer=ImageWriter(), add_checksum=False)
            ean.write(image_output)
            encoded_ref = base64.b64encode(image_output.getvalue())
        except Exception as e:
            self.env['rb_delivery.error_log'].raise_olivery_error(210,self.id,{'sequence_issue':_(str(e))})

    def _check_reference_id(self,values):
        follow_order = self.env['rb_delivery.follow_up_order'].sudo().search([('follow_up_sequence', '=', values['reference_id'])], limit=1)
        if follow_order:
            self.env['rb_delivery.error_log'].raise_olivery_error(1003,self.id,{'order_reference':values['reference_id']})
        reference_id_digits = self.env['rb_delivery.client_configuration'].get_param('reference_id_digit')
        accept_english_letter = self.env['rb_delivery.client_configuration'].get_param('allow_reference_id_accept_the_english_letter')
        number = values['reference_id']
        pattern ="^[\u0621-\u064A\u0660-\u06690-9 ]+$"
        arabic_letter_pattern ="[أ  ب  ت  ث  ج  ح  خ  د  ذ  ر  ز  س  ش  ص  ض  ط  ظ  ع  غ  ف  ق  ك  ل  م  ن  هـ  و ي]"
        english_letter_pattern = "[A aB bC cD dE eF fG gH hI iJ jK kL lM mN nO oP pQ qR rS sT tU uV vW wX xY yZ z]"

        if number and re.findall(str(english_letter_pattern), str(number)):
            if not accept_english_letter and not re.findall(str(pattern), str(number)) :
                self.env['rb_delivery.error_log'].raise_olivery_error(201,self.id,{'reference_id':values['reference_id']})


        if number and not re.findall(str(arabic_letter_pattern), str(number)):
            number = unidecode(u''+str(number))

        try:
            min_digit = reference_id_digits.split('-')[0]
            max_digit = reference_id_digits.split('-')[1]
        except:
            min_digit = 2
            max_digit = 20

        if number and ( len(str(number)) < int(min_digit) or len(str(number)) > int(max_digit)):
            if min_digit == max_digit:
                self.env['rb_delivery.error_log'].raise_olivery_error(211,self.id,{'number_of_digits':max_digit})
            self.env['rb_delivery.error_log'].raise_olivery_error(212,self.id,{'min_digits':min_digit,'max_digits':max_digit})

        values['reference_id'] = number

        EAN = barcode.get_barcode_class('code39')
        image_output = io.BytesIO()
        try:
            ean = EAN(values['reference_id'], writer=ImageWriter(), add_checksum=False)
            ean.write(image_output)
            encoded_ref = base64.b64encode(image_output.getvalue())
        except Exception as e:
            self.env['rb_delivery.error_log'].raise_olivery_error(213,self.id,{'reference_issue':_(str(_(e))),'field_name':_('Reference ID')})

    _sql_constraints = [
        ('sequence', 'unique(sequence)', 'AWB # already exist !!')]

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------

    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                rec.state_id = state_id.id

    @api.onchange('delivery_cost_on_sender')
    def _compute_delivery_cost_on_sender(self):
        if self.delivery_cost_on_sender:
            return {
                'warning':{
                    'title': "Warning",
                    'message': "رسوم التوصيل على المرسل",
                },
            }

    @api.onchange('customer_sub_area')
    def _compute_customer_sub_area(self):
        if self.customer_sub_area.parent_id.id :
            self.customer_area = self.customer_sub_area.parent_id.id


    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_business = user.has_group('rb_delivery.role_business')
        self.is_manager = user.has_group('rb_delivery.role_manager') or user.has_group('rb_delivery.role_picking_up_manager') or user.has_group('rb_delivery.role_distribution_manager')
        self.is_super_manager = user.has_group('rb_delivery.role_super_manager')
        self.is_data_entry = user.has_group('rb_delivery.role_data_entry')
        self.is_configuration_manager = user.has_group('rb_delivery.role_configuration_manager')
        self.is_block_delivery_fee = user.has_group('rb_delivery.role_olivery_block_delivery_fee')

    @api.one
    def check_children(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        children = self.env['rb_delivery.user'].sudo().search([('user_parent_id', '=', user.id)])
        if len(children)>0:
            self.has_children = True
        else:
            self.has_children = False

    @api.depends('create_date')
    @api.one
    def _get_date(self):
        if self.create_date:
            fmt = "%Y-%m-%d %H:%M:%S"
            date = datetime.strftime(self.create_date, fmt)
            self.order_date = date

    @api.one
    @api.depends('delivery_cost', 'copy_total_cost', 'inclusive_delivery')
    def _compute_cost(self):
        if self.inclusive_delivery:
            self.copy_cost = self.copy_total_cost
            self.cost=self.copy_cost
        else:
            self.copy_cost = self.cost

    @api.one
    @api.depends('delivery_cost', 'cost', 'inclusive_delivery')
    def _compute_total_cost(self):
        if(self.inclusive_delivery ):
            self.total_cost=self.copy_total_cost
        if ( not self.inclusive_delivery):
            self.total_cost=self.cost
            self.copy_total_cost=self.total_cost


    @api.one
    # @api.depends('delivery_cost', 'cost', 'inclusive_delivery')
    @api.depends('delivery_cost_on_sender','delivery_cost_on_customer','cost', 'delivery_cost', 'inclusive_delivery','copy_total_cost','customer_payment','paid')
    def _compute_money_collection_cost(self):
        if isinstance(self.id,int):
            self.read(['inclusive_delivery'])
        try:
            customer_payment = float(self.customer_payment) if self.customer_payment is not False else ''
        except:
            customer_payment = ''
        if not self.returned_discount:
            if customer_payment =='':
                if self.paid:
                    if self.inclusive_delivery:
                        if self.delivery_cost_on_customer:
                            self.money_collection_cost = self.delivery_cost
                        else:
                            self.money_collection_cost = 0
                    else:
                        if self.delivery_cost_on_sender:
                            self.money_collection_cost = 0
                        else:
                            self.money_collection_cost = self.delivery_cost

                else:
                    if self.delivery_cost_on_sender:
                        if self.inclusive_delivery:
                            self.money_collection_cost = self.copy_total_cost - self.delivery_cost
                        else:
                            self.money_collection_cost = self.cost
                    else:
                        if self.inclusive_delivery:
                            self.money_collection_cost = self.copy_total_cost
                        else:
                            self.money_collection_cost = self.cost + self.delivery_cost
                    service_fee = self.get_service_fee(self.service,self.default_service, self.extra_service_fee_on_sender, self.extra_service_fee_on_customer)
                    if service_fee['customer_service_fee']:
                        self.money_collection_cost = self.money_collection_cost + service_fee['customer_service_fee']
            else:
                self.money_collection_cost = customer_payment
        else:
            if customer_payment =='':
                self.money_collection_cost = 0.00
            else:
                self.money_collection_cost = customer_payment

    @api.multi
    @api.depends('delivery_cost', 'cost', 'inclusive_delivery','customer_payment')
    # @api.depends('customer_payment')
    def _compute_customer_discount(self):
        for rec in self:
            if isinstance(rec.id,int):
                rec.read(['inclusive_delivery','customer_payment','delivery_cost','cost'])
            try:
                customer_payment = float(rec.customer_payment) if rec.customer_payment is not False else ''
            except:
                customer_payment = ''
            if (customer_payment or customer_payment ==0) and not rec.inclusive_delivery:
                rec.customer_discount = rec.cost + rec.delivery_cost - customer_payment
            elif (customer_payment or customer_payment ==0) and rec.inclusive_delivery:
                rec.customer_discount = rec.copy_total_cost - customer_payment
            else:
                rec.customer_discount = 0.00

    @api.multi
    @api.depends('returned_collection_id', 'collection_id', 'agent_collection_id', 'note', 'state')
    def _getTags_icons(self):
        for record in self:
            added_tags = False
            advanced_tags = '<div class="olivery-tags-container">'
            if record.returned_collection_id:
                advanced_tags += '<img src="/rb_delivery/static/src/img/returned_icon.svg" alt="Returned collection icon" class="olivery-tags-icon" />'
                added_tags = True
            if record.note:
                advanced_tags += '<img src="/rb_delivery/static/src/img/notes_icon.svg" alt="Notes icon" class="olivery-tags-icon" />'
                added_tags = True
            if record.state == 'completed':
                advanced_tags += '<img src="/rb_delivery/static/src/img/completed_icon.svg" alt="Completed icon" class="olivery-tags-icon" />'
                added_tags = True
            if added_tags:
                advanced_tags += '</div>'
            else:
                advanced_tags = ''
            record.advanced_tags = advanced_tags
            added_tags = False




    @api.one
    @api.depends('paid','money_collection_cost','delivery_cost_on_sender','copy_total_cost','inclusive_delivery','cost','delivery_cost','service','default_service')
    def _compute_required_from_business(self):
        if isinstance(self.id,int):
            self.read(['inclusive_delivery'])
        total_service_fee = 0
        sender_service_fee = 0
        service_fee = self.get_service_fee(self.service,self.default_service, self.extra_service_fee_on_sender, self.extra_service_fee_on_customer)
        if service_fee['total_service_fee']:
            total_service_fee = service_fee['total_service_fee']
        if service_fee['sender_service_fee']:
            sender_service_fee = service_fee['sender_service_fee']
        if self.delivery_cost_on_sender:
            if self.returned_value == False:
                total_amount = self.money_collection_cost
            else:
                if self.inclusive_delivery:
                    total_amount = self.delivery_cost
                else:
                    total_amount = 0.0
            if self.inclusive_delivery and not self.paid: # if inclusive then it was already accounted for delivery cost in the compute money collection function
                self.required_from_business = total_amount - total_service_fee
            else:
                self.required_from_business = total_amount - self.delivery_cost - sender_service_fee
        else:
            self.required_from_business = self.money_collection_cost - self.delivery_cost - sender_service_fee

    def validate_customer_payment(self, value):
        if value:
            try:
                float(value)
            except ValueError: #TODO: Check if this is the correct exception
                self.env['rb_delivery.error_log'].raise_olivery_error(241,self.id,{'customer_payment':value})
                #raise ValidationError("Customer Payment must be a numeric value.")

    @api.onchange('customer_payment')
    def _onchange_customer_payment(self):
            self.validate_customer_payment(self.customer_payment)

    @api.one
    def _compute_order_name(self):
        if self.sequence and self.create_date:
            if self.is_replacement:
                self.name = "replacement_"+self.sequence + '_' + str(self.create_date)
            else:
                self.name = self.sequence + '_' + str(self.create_date)
        else:
            self.name = False

    @api.one
    def _compute_location_url(self):
        company = self.env['res.company'].search([])
        if company and company.base_url:
            url = company.base_url
            self.location_url = url+'/location/' + str(self.id)

    @api.one
    def _compute_ref_priority(self):
        self.ref_priority = self.env['rb_delivery.client_configuration'].get_param('reference_id_top_priority')
        return self.ref_priority

    @api.one
    def _compute_seq_priority(self):
        self.seq_priority = self.env['rb_delivery.client_configuration'].get_param('sequence_top_priority')
        return self.seq_priority

    @api.one
    @api.depends('assign_to_agent','extra_agent_cost','delivered_by','state','customer_area','customer_sub_area','order_type_id','assign_to_business')
    def _compute_agent_cost(self):
        delivered_by = False
        agent = False
        allowed_status_ids = self.env['rb_delivery.client_configuration'].get_param('states_to_allow_agent_cost_recalculate')
        allow_statuses = []
        if allowed_status_ids:
            for status_id in allowed_status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                if status:
                    allow_statuses.append(status.name)
        if self.sudo().assign_to_agent:
            agent = self.sudo().assign_to_agent
        elif self.assign_to_agent:
            agent = self.assign_to_agent
        #We have two one sudo and one without sudo becuase admin can not access self.sudo().delivered_by
        #TODO Search more about the base issue of why admin can not see sudo
        if self.sudo().delivered_by:
            delivered_by = self.sudo().delivered_by
        elif self.delivered_by:
            delivered_by = self.delivered_by
        current_agent_cost = 0
        if self.id and not isinstance(self.id, models.NewId):
            current_agent_cost = self.sudo().read(['agent_cost'])[0]['agent_cost']

        if self.state in allow_statuses:
            if agent:
                self.agent_cost = self.get_agent_cost(agent,'delivered_by')
            elif delivered_by:
                self.agent_cost = self.get_agent_cost(delivered_by,'delivered_by')
            if self.extra_agent_cost:
                self.agent_cost = self.agent_cost + self.extra_agent_cost
        else:
            self.agent_cost = current_agent_cost

    @api.one
    @api.depends('agent_cost','money_collection_cost','assign_to_agent')
    def _compute_required_to_company(self):
        self.required_to_company = self.money_collection_cost - self.agent_cost

    @api.one
    @api.depends('agent_cost','delivery_cost','assign_to_agent','commission','pickup_agent_cost')
    def _compute_delivery_profit(self):
        self.delivery_profit=self.delivery_cost-self.agent_cost - self.commission - self.pickup_agent_cost


    def get_agent_cost(self,agent,cost_type):
        cost=0
        business_user = False
        if self.sudo().assign_to_business:
            business_user = self.sudo().assign_to_business
        elif self.assign_to_business:
            business_user = self.assign_to_business
        if cost_type == 'delivered_by':
            cost = self.calculate_delivered_by_and_picked_up_cost(agent.pricing_type, agent.pricing_fixed_value, agent.pricing_percentage_value, agent, business_user,agent.pricing_pricelist_id.id, True)
        elif cost_type == 'picked_up_by':
            cost = self.calculate_delivered_by_and_picked_up_cost(agent.picked_up_by_pricing_type, agent.picked_up_by_pricing_fixed_value, agent.picked_up_by_pricing_percentage_value, agent, business_user,agent.picked_up_by_pricing_pricelist_id.id, False)
        return cost

    def calculate_delivered_by_and_picked_up_cost(self,pricing_type,pricing_fixed_value,pricing_percentage_value,agent,business_user,pricing_pricelist_id, delivery=True):
        cost = 0
        if agent.is_per_business and business_user and delivery and business_user.driver_commission_value_delivered > -1:
            cost = business_user.driver_commission_value_delivered
        elif agent.is_per_business and business_user and delivery and business_user.driver_commission_value_picked_up > -1:
            cost = business_user.driver_commission_value_picked_up
        elif pricing_type == "fixed":
            cost=pricing_fixed_value
        elif pricing_type == "percentage":
            cost=((pricing_percentage_value/100)*self.delivery_cost)

        elif pricing_type=="pricelist":
            data = {
                'sender_id': business_user.id,
                'agent_id': agent.id,
                'to_area_id': self.customer_area.id,
                'order_type_id': self.order_type_id.id,
                'sub_area_id':False}
            if self.customer_sub_area:
                data['sub_area_id'] = self.customer_sub_area.id
            business_alt_area = False
            if self.show_alt_address:
                business_alt_area = self.business_alt_area
            cost = self.env['rb_delivery.pricelist'].get_agent_price(data,pricing_pricelist_id,business_alt_area)


        return cost

    @api.onchange('customer_area')
    def _check_sub_area(self):
        if(self.customer_sub_area and self.customer_area.id != self.customer_sub_area.parent_id.id):
            self.customer_sub_area = False

    # ----------------------------------------------------------------------
    # Create, Update
    # ----------------------------------------------------------------------


    def create_track_values(self,data,import_fields,orders):
        order_index = 0
        fields = []
        for field in import_fields:
            field = self.env['ir.model.fields'].search_read([('model','=','rb_delivery.order'),('name','=',field)],['ttype','readonly','required','field_description','translate','relation','name'])
            fields.append(field)
        for excel_row in data:
            index = 0
            fields_tracked = []
            for field_id in fields:
                field_id = field_id[0]
                old_value = ''
                model_record = ''
                col_info = {'type': field_id['ttype'], 'change_default': False,
                 'company_dependent': False, 'depends': (),
                 'manual': False, 'readonly': field_id['readonly'], 'required': field_id['required'],
                  'searchable': True, 'sortable': True, 'store': True,
                  'string': field_id['field_description'], 'translate': field_id['translate'],
                 'trim': True}
                if field_id['ttype']=="many2one":
                    model_record = self.env[field_id['relation']].name_search(excel_row[index],None,'=')
                    if isinstance(model_record, list):
                        if len(model_record)>0 and len(model_record[0])>0:
                            model_record = self.env[field_id['relation']].search([('id','=',model_record[0][0])])
                else:
                    model_record = excel_row[index]
                    if field_id['ttype'] == 'selection':
                        selection = self.env['rb_delivery.order']._fields[field_id['name']].selection
                        if isinstance(selection, list):
                            col_info['selection'] = selection
                        else:
                            method_to_call=getattr(rb_delivery_order,selection)
                            selection_vals = method_to_call(self)
                            col_info['selection'] = selection_vals
                    elif field_id['ttype'] == 'boolean':
                        if excel_row[index] == 'True':
                            model_record = '1'
                        else:
                            model_record = '0'
                    elif field_id['ttype'] == 'datetime':
                        if excel_row[index]:
                            model_record = datetime.strptime(excel_row[index],'%Y-%m-%d %H:%M:%S')
                        else:
                            model_record = False
                        old_value = False
                    elif field_id['ttype'] == 'date':
                        if excel_row[index]:
                            model_record = datetime.strptime(excel_row[index],'%Y-%m-%d')
                        else:
                            model_record = False
                        old_value = False
                tracking_object_values = self.env['mail.tracking.value'].create_tracking_values(old_value,model_record,field_id['name'],col_info,100)
                if tracking_object_values:
                    fields_tracked.append([0,0,tracking_object_values])
                index = index + 1
            if 'ids' in orders and orders['ids'] and len(orders['ids']) > 0:
                order_record = self.env['rb_delivery.order'].search([('id','=',orders['ids'][order_index])])
                col_info = {'type': 'float', 'change_default': False,
                    'company_dependent': False, 'depends': (),
                    'manual': False, 'readonly': True, 'required': False,
                    'searchable': True, 'sortable': True, 'store': True,
                    'string': 'Delivery Fee', 'translate': False,
                    'trim': True}
                tracking_object_values = self.env['mail.tracking.value'].create_tracking_values('',order_record.delivery_cost,'delivery_cost',col_info,100)
                if tracking_object_values:
                    fields_tracked.append([0,0,tracking_object_values])
                vat_value_field_id = self.env['ir.model.fields'].search([('model','=','rb_delivery.order'),('name','=','vat_value')])
                if vat_value_field_id:
                    vat_col_info = {'type': 'float', 'change_default': False,
                        'company_dependent': False, 'depends': (),
                        'manual': False, 'readonly': True, 'required': False,
                        'searchable': True, 'sortable': True, 'store': True,
                        'string': 'Vat Value', 'translate': False,
                        'trim': True}
                    vat_tracking_object_values = self.env['mail.tracking.value'].create_tracking_values('',order_record.vat_value,'vat_value',vat_col_info,100)
                    if vat_tracking_object_values:
                        fields_tracked.append([0,0,vat_tracking_object_values])
                    vat_delivery_fee_col_info = {'type': 'float', 'change_default': False,
                        'company_dependent': False, 'depends': (),
                        'manual': False, 'readonly': True, 'required': False,
                        'searchable': True, 'sortable': True, 'store': True,
                        'string': 'Delivery fee without vat', 'translate': False,
                        'trim': True}
                    delivery_fee_tracking_object_values = self.env['mail.tracking.value'].create_tracking_values('',order_record.delivery_fee_without_vat,'delivery_fee_without_vat',vat_delivery_fee_col_info,100)
                    if delivery_fee_tracking_object_values:
                        fields_tracked.append([0,0,delivery_fee_tracking_object_values])
                user_id = self.env['res.users'].search([('id','=',self._uid)]).partner_id.id
                sub_type_id = self.env['mail.message.subtype'].sudo().search([('name','=','Note')])
                track_values = {'subject': False,
                                'body': '',
                                'author_id': user_id,
                                'message_type': 'notification',
                                'model': 'rb_delivery.order',
                                'res_id': orders['ids'][order_index],
                                'subtype_id': sub_type_id.id,
                                'record_name': False,
                                'tracking_value_ids':fields_tracked}
                order_index = order_index + 1
                self.env['mail.message'].create(track_values)
                order_record.message_post(body=_("Imported by excel."))

    def guard_import(self,import_fields,data):
        import copy
        messages = []
        business_not_in_import_field = False
        row_no = 1
        float_fields,date_fields = self.get_float_date_fields()
        override_allowed_states = self.env['rb_delivery.client_configuration'].get_param('allowed_states_to_overwrite_orders')
        states_override = []
        original_import_fields = copy.copy(import_fields)
        if override_allowed_states:
            for state in override_allowed_states:
                status = self.env['rb_delivery.status'].sudo().search([('id','=',state)])
                states_override.append(status.name)
        if 'assign_to_business' not in import_fields:
            import_fields.append('assign_to_business')
            business_not_in_import_field = True
        convert_coordinates_to_locality = self.env['rb_delivery.client_configuration'].sudo().get_param('convert_coordinates_to_locality')
        line_record = True
        for rec in data:
            line_record = True

            for index, field in enumerate(rec):
                if isinstance(field, str):  # Check if the field is a string
                    rec[index] = re.sub(r'\s+', ' ', field).strip()

            business_user = False
            order = False
            if business_not_in_import_field:
                if self._uid == 1 or self._uid == 2:
                    rec.append('')
                else:
                    business_user = self.env['rb_delivery.user'].sudo().search([('user_id.id', '=', self._uid)])
                    group_id = business_user.group_id
                    if group_id.code == 'rb_delivery.role_business':
                        if business_user.inclusive_delivery and 'cost' in import_fields and 'copy_total_cost' not in import_fields:
                            import_fields.append('copy_total_cost')
                        rec.append(business_user.id)
                        if 'cost' in import_fields and business_user.inclusive_delivery:
                            index = import_fields.index('cost')
                            rec.append(rec[index])
                    else:
                        rec.append('')
            else:
                business_index = import_fields.index('assign_to_business')
                if rec[business_index]:
                    business_user = self.env['rb_delivery.user'].search(['|','|','|',('mobile_number','=',rec[business_index]),('username','=',rec[business_index]),('user_sequence','=',rec[business_index]),('commercial_name','=',rec[business_index]), ('role_code','=','rb_delivery.role_business')])
                    if business_user and len(business_user)>1:
                        business_user = False
                        messages.append(_("There are more than one user with the same information %s, please make sure using a unique value like mobile number")%(rec[business_index]))
                    elif business_user:
                        rec[business_index] = business_user.id
                    else:
                        messages.append(_("Business with information %s does not exist in the system. Or you are using a value other than username and mobile number, for a better search please use either mobile number or username for business.")%(rec[business_index]))
            if convert_coordinates_to_locality:
                self.get_locality_by_coordinates(False,rec,import_fields)

            if 'delivery_cost_on_sender' in import_fields and 'delivery_cost_on_customer' in import_fields:
                delivery_cost_on_sender_index = import_fields.index('delivery_cost_on_sender')
                delivery_cost_on_customer_index = import_fields.index('delivery_cost_on_customer')
                if rec[delivery_cost_on_sender_index] == 'True' and rec[delivery_cost_on_customer_index] == 'True':
                    messages.append(_("You can not set both fields 'Delivery cost on sender' and 'Delivery cost on customer' to True at the same time, please make sure to keep at least one of them false."))
            
            if 'follow_up_order/follow_up_sequence' in import_fields:
                follow_up_sequence_index = import_fields.index('follow_up_order/follow_up_sequence')
                if  rec[follow_up_sequence_index]:
                    order = self.env['rb_delivery.order'].search(['|',('sequence', '=', rec[follow_up_sequence_index]),('reference_id', '=', rec[follow_up_sequence_index])], limit=1)
                    if order:
                        messages.append(_("Follow order with the sequence {sequence} already been used for another order").format(sequence=rec[follow_up_sequence_index]))
                    else:
                        follow_up_order = self.env['rb_delivery.follow_up_order'].search([('follow_up_sequence', '=', rec[follow_up_sequence_index])], limit=1)
                        if follow_up_order:
                            messages.append(_("Follow order with the sequence {sequence} already been used for another follow up order").format(sequence=rec[follow_up_sequence_index]))
            follow_up_fields = {'follow_up_order/follow_up_sequence', 'follow_up_order/name', 'follow_up_order/note'}
            if 'follow_up_order/name' in import_fields or 'follow_up_order/note' in import_fields or 'follow_up_order/follow_up_sequence' in import_fields:
                for i, field in enumerate(import_fields):
                    if field not in follow_up_fields:
                        if rec[i] or (isinstance(rec[i], str) and rec[i].strip() != ''):
                            line_record = False
                            break
            else:
                line_record = False

            if 'cost' in import_fields :
                cost_index = import_fields.index('cost')
                cost_vals = {'cost':rec[cost_index]}
                if rec[cost_index]:
                    try:
                        self.check_minus_values(cost_vals)
                    except Exception as e:
                        messages.append(_("Issue with Cost value in row %s %s ")%(str(row_no), str(e)))
                    if 'copy_total_cost' not in import_fields:
                        import_fields.append('copy_total_cost')
                    if business_user:
                        rec.append(rec[cost_index])
                    else:
                        rec.append('')


            if 'reference_id' in import_fields:
                reference_id_index = import_fields.index('reference_id')
                if rec[reference_id_index]:
                    vals = {'reference_id':rec[reference_id_index]}
                    order = self.env['rb_delivery.order'].search_read([('reference_id','=',rec[reference_id_index]),('state','!=','deleted')],['id'])
                    if business_user:
                        old_order = self.env['rb_delivery.order'].search([('reference_id', '=', rec[reference_id_index]), ('assign_to_business', '=', business_user.id),('state','!=','deleted')])
                    else:
                        old_order = self.env['rb_delivery.order'].search([('reference_id', '=', rec[reference_id_index]),('state','!=','deleted')])
                        if len(old_order) > 1:
                            messages.append(_("There are more than one order with the same reference id %s, Please provide business to make it more spisific search")%(rec[reference_id_index]))
                            old_order = False
                    if old_order:
                       if old_order.state not in states_override:
                           messages.append(_('cant update order with reference id in this status %s' % (old_order.state)))
                    try:
                        self._check_reference_id(vals)
                    except Exception as e:
                        messages.append(_("Issue with Reference ID in row %s %s ")%(str(row_no), str(e)))
            if 'sequence' in import_fields:
                sequence_index = import_fields.index('sequence')
                if rec[sequence_index]:
                    order = self.env['rb_delivery.order'].search_read([('sequence','=',rec[sequence_index])],['id'])
            if 'customer_mobile' in import_fields:
                mobile_index = import_fields.index('customer_mobile')
                try:
                    self.env['rb_delivery.utility'].check_mobile_number(rec[mobile_index])
                except Exception as e:
                    messages.append(_("Issue with mobile number in row %s %s ")%(str(row_no), str(e)))
            area_not_in_file = False
            if 'second_mobile_number' in import_fields:
                second_mobile_number_index = import_fields.index('second_mobile_number')
                try:
                    self.env['rb_delivery.utility'].check_mobile_number(rec[second_mobile_number_index], _('Second mobile number'))
                except Exception as e:
                    messages.append(_("Issue with second mobile number in row %s %s ")%(str(row_no), str(e)))
            if 'google_address' in import_fields:

                address_index = import_fields.index('google_address')
                skip_area_validation=False
                address_vals = rec[address_index].split(',')
                area_value = address_vals[0] if len(address_vals) > 0 else ''
                sub_area_value = address_vals[1] if len(address_vals) > 1 else ''
                if 'customer_area' not in import_fields or len(rec)<= import_fields.index('customer_area'):
                    if 'customer_area' not in import_fields:
                        import_fields.append('customer_area')
                    rec.append(area_value)
                    skip_area_validation = True


                if  'customer_sub_area' not in import_fields  or len(rec)<= import_fields.index('customer_sub_area'):
                    if 'customer_sub_area' not in import_fields:
                        import_fields.append('customer_sub_area')
                    rec.append(sub_area_value)
                    skip_area_validation = True


                if skip_area_validation:
                    new_context = dict(self._context)
                    new_context['skip_area_validation']=True
                    self = self.with_context(new_context)
                del import_fields[address_index]
                del rec[address_index]
            if 'address_tag' in import_fields:
                address_tag_index = import_fields.index('address_tag')
                if not rec[address_tag_index]:
                    rec[address_tag_index]=''
                full_address = self.check_full_address(rec[address_tag_index],business_user)
                area_value = ''
                sub_area_value = ''
                if not full_address:
                    messages.append(_("Issue with address tag in row %s %s %s" )%(str(row_no),  _('No address tag found with the name'),rec[address_tag_index]))
                else:
                    area_value = full_address.area_id.name
                    sub_area_value = full_address.sub_area_id.name
                    rec[address_tag_index]=full_address.name
                customer_area_index = import_fields.index('customer_area') if 'customer_area' in import_fields else 0
                if 'customer_area' not in import_fields or not len(rec)>customer_area_index:
                    if 'customer_area' not in import_fields:
                        import_fields.append('customer_area')
                    rec.append(area_value)
                elif area_value:
                    rec[customer_area_index] = area_value
                customer_sub_area_index = import_fields.index('customer_sub_area') if 'customer_sub_area' in import_fields else 0
                if 'customer_sub_area' not in import_fields or not len(rec)>customer_sub_area_index:
                    if 'customer_sub_area' not in import_fields:
                        import_fields.append('customer_sub_area')
                    rec.append(sub_area_value)
                elif sub_area_value:

                    rec[customer_sub_area_index] = sub_area_value

            if 'customer_area/.id' in import_fields or 'customer_area' in import_fields:
                vals = {}

                if 'customer_area/.id' in import_fields:
                    area_index = import_fields.index('customer_area/.id')
                    vals = {'customer_area_id':rec[area_index]}
                else:
                    area_index = import_fields.index('customer_area')
                    if area_index < len(rec):
                        vals['customer_area']=rec[area_index]
                    else:
                        area_not_in_file = True
                try:
                    if vals:
                        rec[area_index] = self.check_area(vals,rec[area_index],business_user)
                        vals['customer_area']=rec[area_index]
                except Exception as e:
                    messages.append(_("Issue with customer area in row %s %s" )%(str(row_no),  str(e)))

            if 'customer_sub_area/.id' in import_fields or 'customer_sub_area' in import_fields:
                if 'customer_area/.id' not in import_fields and 'customer_area' not in import_fields:
                    vals = {}
                if 'customer_sub_area/.id' in import_fields:
                    sub_area_index = import_fields.index('customer_sub_area/.id')
                    vals['customer_sub_area_id']=rec[sub_area_index]
                else:
                    sub_area_index = import_fields.index('customer_sub_area')
                    vals['customer_sub_area']=rec[sub_area_index]

                if 'customer_area' in import_fields and not area_not_in_file:
                    area_index = import_fields.index('customer_area')
                    try:

                        if vals.get('customer_area'):

                            self.check_sub_area(vals,business_user,int(vals['customer_area']))
                            rec[area_index] = str(vals['customer_area'])
                        else:
                            self.check_sub_area(vals,business_user)
                            rec[area_index] = str(vals['customer_area'])
                        rec[sub_area_index] = str(vals['customer_sub_area'])
                    except Exception as e:
                        messages.append(_("Issue with customer sub area in row %s %s" )% (str(row_no), str(e)))
                else:
                    try:


                        if vals.get('customer_area'):
                            if 'customer_area' not in import_fields:
                                import_fields.append('customer_area')

                            self.check_sub_area(vals,business_user,str(vals['customer_area']))
                            rec.append(str(vals['customer_area']))
                        elif 'customer_area' not in import_fields:
                            import_fields.append('customer_area')
                            self.check_sub_area(vals,business_user)
                            rec.append(str(vals['customer_area']))
                        rec[sub_area_index] = str(vals['customer_sub_area'])
                    except Exception as e:
                        rec.append('')
                        messages.append(_("Issue with customer sub area in row %s %s" )% (str(row_no), str(e)))
            if len(import_fields)>len(rec):
                [rec.append('') if len(rec)<len(import_fields) else '' for i in import_fields]
            for field in original_import_fields:
                if field in float_fields and rec[import_fields.index(field)]:
                    try:
                        val = float(rec[import_fields.index(field)])
                    except ValueError:
                        messages.append(_("Issue with '%s': Cannot convert string to number." ) % (float_fields[field]))
                    except Exception as e:
                        messages.append(_("Unexpected issue with '%s': %s") % (float_fields[field], str(e)))

            if not order and not line_record:
                try:
                    values = {}
                    for import_field in import_fields:
                        field_index = import_fields.index(import_field)
                        values[import_field] = rec[field_index]
                    if values:
                        self.get_required_fields(values)
                except Exception as e:
                    messages.append(_("%s in row %s ")%( str(e),str(row_no)))


            row_no = row_no + 1
        return import_fields,data,messages,line_record


    @api.model
    def check_full_address(self,address_tag_name,business_user):
        if not isinstance(address_tag_name, int):
            full_address = self.env['rb_delivery.address_tags'].sudo().search([('name','=',address_tag_name)], limit=1)
        else:
            full_address = self.env['rb_delivery.address_tags'].sudo().browse(address_tag_name)
        if full_address:
            return full_address
        if '-' not in address_tag_name: 
            return False
        address_parts = address_tag_name.split('-')
        area_name = address_parts[0].strip()
        sub_area_name = address_parts[1].strip()
        vals = {
            'customer_area': area_name,
            'customer_sub_area': sub_area_name
        }
        area = self.check_area(vals,area_name,business_user)
        if not area:
            return area
        
        self.check_sub_area(vals, business_user, area)
        if not isinstance(vals.get('customer_sub_area'), int):
            return False

        address_tag = self.env['rb_delivery.address_tags'].search([('area_id', '=', vals.get('customer_area')),('sub_area_id', '=', vals.get('customer_sub_area'))], limit=1)

        return address_tag



    @api.model
    def get_float_date_fields(self):
        float_fields = {}
        date_fields = {}
        for field_name, field in self._fields.items():
            if isinstance(field, fields.Float):
                float_fields[field_name] = field.string
            if isinstance(field, (fields.Date,fields.Datetime)):
                date_fields[field_name] = field.string
        return float_fields,date_fields

    @api.model
    def get_statuses(self, barcodes):
        field_ids = self.env['rb_delivery.client_configuration'].get_param('fields_to_get_when_change_status')

        field_info = {}
        if field_ids:
            field_records = self.env['ir.model.fields'].sudo().search_read(
                [('id', 'in', field_ids)],
                fields=['name', 'field_description', 'ttype', 'relation']
            )
            for field in field_records:
                field_info[field['name']] = {
                    'description': field['field_description'],
                    'ttype': field['ttype'],
                    'relation': field['relation'],
                }

        orders = self.sudo().search(['|', ('sequence', 'in', barcodes), ('reference_id', 'in', barcodes)])
        barcode_state_map = {}
        order_ids = {}
        for barcode in barcodes:
            order = orders.filtered(lambda o: o.sequence == barcode or o.reference_id == barcode)
            order_ids[barcode] = order.id
            if order:
                if len(order) > 1:
                    barcode_state_map[barcode] = "UNABLE_TO_FIND_STATUS_MULTIPLE_ORDERS_FOUND"
                else:
                    if field_info:
                        order_data = order.read(list(field_info.keys()))[0]
                        formatted_data = {}
                        for field, info in field_info.items():
                            value = order_data[field]
                            if field == 'state_id':
                                formatted_data['state_id'] = order.state_id.title
                            elif info['ttype'] == 'many2one' and value:
                                formatted_data[info['description']] = value[1]
                            elif info['ttype'] == 'many2many' and value:
                                related_records = self.env[info['relation']].browse(value)
                                formatted_data[info['description']] = [name for _, name in related_records.name_get()]
                            else:
                                formatted_data[info['description']] = value
                        barcode_state_map[barcode] = formatted_data
                    else:
                        barcode_state_map[barcode] = order.state_id.title
            else:
                barcode_state_map[barcode] = None
        if self._context.get('fetch_ids'):
            return {'ids': order_ids, 'barcode_state_map': barcode_state_map}
        else:
            return barcode_state_map

    @api.model
    def load(self, import_fields, data,draft_import=False):
        if draft_import:
            draft_recs = []
            draft_recs_ids = []
            messages = []
            try:
                for row in data:
                    index = 0
                    draft_item_ids = []
                    for field_value in row:
                        field_name = import_fields[index]
                        field = self.env['ir.model.fields'].sudo().search([('name','=',field_name),('model','=','rb_delivery.order')])
                        if field_value == 'False' or field_value == 'FALSE':
                            field_value = ''
                        order_draft_item = self.env['rb_delivery.order_draft_item'].sudo().create({'name':str(field_value).strip(),'field_id':field.id})
                        draft_item_ids.append(order_draft_item.id)
                        index += 1
                    values = {'order_draft_item_ids':[(6,0,draft_item_ids)],'file_name':'Excel'}
                    draft_rec = self.env['rb_delivery.order_draft'].sudo().create(values)
                    draft_recs.append(draft_rec)
                    draft_recs_ids.append(draft_rec.id)
            except Exception as e:
                messages.append(_('Order draft failed to be created because of ') + str(e))
            create_orders_directly = self.env['rb_delivery.client_configuration'].get_param('directly_create_order_from_draft_orders')
            if create_orders_directly and len(draft_recs) > 0:
                batch_list = [draft_recs[i:i + 20] for i in range(0, len(draft_recs), 20)]
                for batch in batch_list:
                    self.env['rb_delivery.order_draft'].with_delay(channel="root.create_draft_orders",max_retries=2).create_orders_draft(batch)
            return {'ids':draft_recs_ids,'messages':messages}
        else:
            import_fields,data,messages,line_record = self.guard_import(import_fields,data)
            if len(messages)>0:
                displayed_messages = '\n'.join(messages) #TODO: change this to olivery validation error
                #self.env['rb_delivery.error_log'].raise_olivery_error(242,self.id,{'messages':displayed_messages})
                displayed_messages = re.sub(r'\[.*?\]\{\[\[\(\d+\) .*?\]\]\}', '', displayed_messages)
                raise ValidationError(displayed_messages)
            row_number = 1
            try:
                if ('sequence' in import_fields) or ('reference_id' in import_fields):
                    for values in data: # get the sequence and reference id and check if they are valid and get the assign_to_business eg business
                        print(values)
                        date_checker = {}
                        if 'assign_to_business' in import_fields:
                            date_checker['assign_to_business'] = values[import_fields.index('assign_to_business')]
                        if 'sequence' in import_fields:
                            date_checker['sequence'] = values[import_fields.index('sequence')]
                        if 'reference_id' in import_fields:
                            date_checker['reference_id'] = values[import_fields.index('reference_id')]
                        self._check_reference_sequence(date_checker)
                        row_number += 1

            except Exception as e:
                displayed_messages = re.sub(r'\[.*?\]\{\[\[\(\d+\) .*?\]\]\}', '', str(e))
                raise ValidationError('Error in row '+ str(row_number) + ': ' + displayed_messages)
            orders = super(rb_delivery_order, self).load(import_fields, data)
            return orders

    def clear_inactive_orders(self):
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        date_now = datetime.now(pytz.timezone(timezone))
        fmt = "%Y-%m-%d 00:00:00"
        six_months_ago = date_now - relativedelta(months=6)
        six_months_ago_str=datetime.strftime(six_months_ago,fmt)
        one_month_ago = date_now - relativedelta(months=1)
        one_month_ago_date = datetime.strftime(one_month_ago, fmt)

        all_collections = self.env['rb_delivery.multi_print_orders_money_collector'].sudo().search(['|','&',('create_date','<',one_month_ago_date), ('state', '=', 'completed'), ('create_date','<',six_months_ago_str)])
        all_returned_collections = self.env['rb_delivery.returned_money_collection'].sudo().search(['|','&',('create_date','<',one_month_ago_date), ('state', '=', 'completed_returned'), ('create_date','<',six_months_ago_str)])
        orders_in_collections = all_collections.mapped('order_ids')
        returned_orders_in_collections = all_returned_collections.mapped('order_ids')
        all_orders = orders_in_collections + returned_orders_in_collections
        rest_of_orders = self.env['rb_delivery.order'].sudo().search([('create_date','<',six_months_ago_str), ('id', 'not in', all_orders.ids)])
        
        all_orders = all_orders + rest_of_orders

        collection_batch_list = self.env['rb_delivery.multi_print_orders_money_collector']
        for collection in all_collections:
            collection_batch_list += collection
            if len(collection_batch_list)==1000:
                self.with_delay(channel="root.notification",max_retries=2).archive_orders(collection_batch_list, True)
                collection_batch_list = self.env['rb_delivery.multi_print_orders_money_collector']

        returned_collection_batch_list = self.env['rb_delivery.returned_money_collection']
        for collection in all_returned_collections:
            returned_collection_batch_list += collection
            if len(returned_collection_batch_list)==1000:
                self.with_delay(channel="root.notification",max_retries=2).archive_orders(returned_collection_batch_list, True)
                returned_collection_batch_list = self.env['rb_delivery.returned_money_collection']

        batch_list = self.env['rb_delivery.order']
        for order in all_orders:
            batch_list += order
            if len(batch_list)==1000:
                self.with_delay(channel="root.notification",max_retries=2).archive_orders(batch_list, True)
                batch_list = self.env['rb_delivery.order']
        if len(batch_list) > 0:
            self.with_delay(channel="root.notification",max_retries=2).archive_orders(batch_list, True)
        if len(returned_collection_batch_list) > 0:
            self.with_delay(channel="root.notification",max_retries=2).archive_orders(returned_collection_batch_list, True)
        if len(collection_batch_list) > 0:
            self.with_delay(channel="root.notification",max_retries=2).archive_orders(collection_batch_list, True)
        return True
    def get_orders_action(self, order_ids):
        if len(order_ids) > 0:
            action = self.env['ir.actions.act_window'].sudo().create({'name': _('Quick orders'),
                'res_model': 'rb_delivery.order',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [('id', 'in', order_ids)],
            })
            return request.httprequest.scheme + '://' + request.httprequest.host + '/web#action=' + str(action.id) + '&view_type=list&model=rb_delivery.order'

    def reset_expired_tracking_ids(self,values=False):
        current_date = datetime.now().strftime("%Y-%m-%d") + ' 00:00:00'
        tracking_expire_status_ids = self.env['rb_delivery.client_configuration'].get_param('make_tracking_link_expired_on_status')
        domain=[]
        if values and 'state' in values and values['state']:
            status = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            if status and status.id in tracking_expire_status_ids:
                values['tracking_uid'] = False
                values['tracking_url'] = False
                values['tracking_url_expires_at'] = False
            return
        else:
            username = self.env.user.name
            domain=['|',('tracking_url_expires_at','>',current_date),('state_id','in',tracking_expire_status_ids)]
            orders = self.env['rb_delivery.order'].sudo().search(domain)
            message = _("Orders has been updated by %s which is the system, this means that the system closed some tracking_link from public access")%(username)
            updated_vals = {'tracking_uid' : False,'tracking_url' : False,'tracking_url_expires_at' : False}
            data = {'uid':self._uid,'message':message,'records':orders,'values':updated_vals,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

        return True


    @api.multi
    def export_data(self, fields_to_export, raw_data=False):
        return super(rb_delivery_order, self.sudo()).export_data(fields_to_export, raw_data)

    def archive_orders(self,orders, bypass_errors=False):
        orders.with_context(bypass_errors=bypass_errors).write({'active':False})
        return True

    def check_country(self, values):
        if str(values['customer_country']).isnumeric():
            customer_country = self.env['rb_delivery.country'].search([('id','=',values['customer_country'])])
            if customer_country and customer_country.id:
                values['customer_country'] = customer_country.id
            else:
                customer_country = self.env['rb_delivery.country'].search([('code','=',values['customer_country'])])
                if customer_country and customer_country.id:
                    values['customer_country'] = customer_country.id
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(214,self.id,{'customer_country':values['customer_country']})
        else:
            customer_country = self.env['rb_delivery.country'].search(['|',('code','=',values['customer_country']),('name','=',values['customer_country'])])
            if customer_country:
                values['customer_country'] = customer_country.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(214,self.id,{'customer_country':values['customer_country']})

    def fuzzy_search(self, string_to_match, values_list):
        accepted_ratio = self.env['rb_delivery.client_configuration'].get_param('fuzzy_search_accepted_ratio')
        if not accepted_ratio:
            accepted_ratio = 0.5

        string_to_match = string_to_match.lower()
        values = string_to_match.split()
        if len(values)>2:
            values = values + [values[i] + ' ' + values[i + 1] for i in range(len(values) - 1)]
        if len(values)>1:
            values = values + [string_to_match]


        best_match = None
        highest_ratio = 0.0

        for value in values:
            for value_item in values_list:
                value_item_name = value_item.name.lower()
                matcher = difflib.SequenceMatcher(None, value, value_item_name)
                similarity_ratio = matcher.ratio()
                if similarity_ratio > highest_ratio:
                    highest_ratio = similarity_ratio
                    best_match = value_item
        if highest_ratio >= float(accepted_ratio):
            return best_match
        return None

    def check_area(self,values, area_value,rb_delivery_user):
        area_id = False
        area = False
        if area_value:
            area_list = self.env['rb_delivery.area'].sudo().search([])
            if 'customer_area_id' in values and values['customer_area_id'] and str(area_value).isnumeric():
                area = self.env['rb_delivery.area'].search([('id','=',area_value),'|',('active','=',False),('active','=',True)])
                del values['customer_area_id']
            elif 'customer_area_code' in values and values['customer_area_code']:
                area = self.env['rb_delivery.area'].search([('code','=',area_value),'|',('active','=',False),('active','=',True)])
                del values['customer_area_code']
            elif area_value and not str(area_value).isnumeric():
                area = self.env['rb_delivery.area'].search([('name','=',area_value),'|',('active','=',False),('active','=',True)])
            if not area:
                if str(area_value).isnumeric():
                    if str(area_value)[0] == '0':
                        area = self.env['rb_delivery.area'].search([('code','=',area_value),'|',('active','=',False),('active','=',True)])
                    else:
                        area = self.env['rb_delivery.area'].search([('id','=',area_value),'|',('active','=',False),('active','=',True)])
                else:
                    use_fuzzy_search = self.env['rb_delivery.client_configuration'].get_param('use_fuzzy_search')
                    area_map_returned = False
                    if use_fuzzy_search:
                        area = self.fuzzy_search(area_value,area_list)
                        if not area and 'customer_address' in values and values['customer_address']:
                            area = self.fuzzy_search(values['customer_address'],area_list)

                    elif rb_delivery_user:
                        area_based_map = False
                        area_maps = rb_delivery_user.area_map
                        for area_map in area_maps:
                            if area_map.name == area_value:
                                area_based_map = area_map.area_id
                                area_map_returned = area_map
                        if area_based_map:
                            area = area_based_map
                        else:
                            area_map = self.env['rb_delivery.area_map'].search([('name','=',str(area_value)),('user_id','=',False)],limit=1)
                            if area_map:
                                area = area_map.area_id
                                area_map_returned = area_map

                    else:
                        area_map = self.env['rb_delivery.area_map'].search([('name','=',str(area_value)),('user_id','=',False)],limit=1)
                        if area_map:
                            area = area_map.area_id
                            area_map_returned = area_map
                    if area_map_returned:
                        values['customer_area_map'] = area_map_returned

            if area and area.id:
                area_id = area
            if not area_id:
                use_fuzzy_search = self.env['rb_delivery.client_configuration'].get_param('use_fuzzy_search')
                if use_fuzzy_search:
                    if 'customer_address' in values and values['customer_address']:
                        area_matched = self.fuzzy_search(values['customer_address'],area_list)
                        if area_matched:
                            area_id = area_matched
                if rb_delivery_user and not area_id:
                    area_based_map = False
                    area_maps = rb_delivery_user.area_map
                    for area_map in area_maps:
                        if area_map.name == area_value:
                            area_based_map = area_map.area_id
                    if area_based_map:
                        area_id = area_based_map
                    else:
                        area_map = self.env['rb_delivery.area_map'].search([('name','=',str(area_value)),('user_id','=',False)],limit=1)
                        if area_map:
                            area_id = area_map.area_id
                        else:
                            if rb_delivery_user and rb_delivery_user.default_area_id:
                                area_id = rb_delivery_user.default_area_id
                            else:
                                if self._context.get('skip_area_validation'):
                                    values['customer_area'] = False
                                    return
                                self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'area','field_value':str(area_value)})
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'area','field_value':str(area_value)})
        if area_id:
            if not area_id.active:
                if self._context.get('skip_area_validation'):
                    values['customer_area'] = ''
                    return
                self.env['rb_delivery.error_log'].raise_olivery_error(297,self.id,{'area_name':area_id.name})
            is_business = self._context.get('is_business', False)
            if area_id.country_id and not is_business:
                values['customer_country'] = area_id.country_id.id
            area_id = area_id.id      
        return area_id

    def check_sub_area(self, values, rb_delivery_user, area=False):
        def raise_error(code, context):
            if self._context.get('skip_area_validation'):
                values['customer_sub_area'] = ''
                return
            self.env['rb_delivery.error_log'].raise_olivery_error(code, self.id, context)

        def get_sub_area(domain):
            return self.env['rb_delivery.sub_area'].search(domain)

        sub_area_value = ''
        sub_area_id = False
        customer_sub_area = False
        sub_area_maps = False
        if 'customer_sub_area_id' in values and values['customer_sub_area_id']:
            sub_area_value = values.pop('customer_sub_area_id')
            domain = [('id', '=', sub_area_value)]
        elif 'customer_sub_area_code' in values and values['customer_sub_area_code']:
            sub_area_value = values.pop('customer_sub_area_code')
            domain = [('code', '=', sub_area_value)]
        elif 'customer_sub_area' in values and values['customer_sub_area']:
            try:
                sub_area_value = int(values['customer_sub_area'])
            except:
                sub_area_value = values['customer_sub_area']
            domain = [('id' if isinstance(sub_area_value,int) and len(str(sub_area_value<10)) else 'name', '=', sub_area_value)]
        else:
            use_default_sub_area_if_no_sub_area = self.env['rb_delivery.client_configuration'].get_param('use_default_sub_area_if_no_sub_area')
            if rb_delivery_user and rb_delivery_user.default_sub_area_id and use_default_sub_area_if_no_sub_area:
                sub_area = rb_delivery_user.default_sub_area_id
                values.update({'customer_sub_area': sub_area.id, 'customer_area': sub_area.parent_id.id})
            return
        customer_sub_area = get_sub_area(domain)
        if customer_sub_area:
            if isinstance(sub_area_value, int):
                sub_area_value = self.env['rb_delivery.sub_area'].browse([sub_area_value])
                if sub_area_value:
                    sub_area_value = sub_area_value.name
            if len(customer_sub_area) > 1:
                if area:
                    parent_area_name = ', '.join(sub.parent_id.name for sub in customer_sub_area)
                    customer_sub_area = next((sub for sub in customer_sub_area if sub.parent_id.id == area or sub.area_parent_id.id == area), None)
                    if not customer_sub_area:
                        raise_error(218, {'sub_area': str(sub_area_value), 'area_name': str(parent_area_name)})
                else:
                    raise_error(216, {'sub_area': sub_area_value, 'area_count': str(len(customer_sub_area)), 'area': ''})
            if area and (customer_sub_area.parent_id.id != area and customer_sub_area.area_parent_id.id != area):
                raise_error(218, {'sub_area': str(sub_area_value), 'area_name': str(customer_sub_area.parent_id.name)})

            values.update({'customer_sub_area': customer_sub_area.id, 'customer_area': customer_sub_area.parent_id.id})

        else:
            if rb_delivery_user:
                if isinstance(rb_delivery_user, int):
                    rb_delivery_user = self.env['rb_delivery.user'].sudo().browse(rb_delivery_user)
                sub_area_maps = rb_delivery_user.sub_area_map.filtered(lambda m: m.name == sub_area_value)
            if sub_area_maps:
                if len(sub_area_maps) > 1 and area:
                    area_name = self.env['rb_delivery.area'].browse(area).name if isinstance(area, int) and len(str(area)) < 10 else area
                    sub_area_map = next((m for m in sub_area_maps if m.imported_area == area_name), None)
                    if sub_area_map:
                        values.update({'customer_sub_area_map': sub_area_map, 'customer_sub_area': sub_area_map.sub_area_id.id, 'customer_area': sub_area_map.area_id.id})
                    else:
                        raise_error(219, {'sub_area': sub_area_value, 'business_name': rb_delivery_user.username})
                else:
                    sub_area_map = sub_area_maps[0]
                    values.update({'customer_sub_area_map': sub_area_map, 'customer_sub_area': sub_area_map.sub_area_id.id, 'customer_area': sub_area_map.area_id.id})
            else:
                use_fuzzy_search = self.env['rb_delivery.client_configuration'].get_param('use_fuzzy_search')
                if use_fuzzy_search:
                    if values.get('customer_area'):
                        sub_area_list = self.env['rb_delivery.sub_area'].sudo().search([['parent_id','=',values['customer_area']]])
                        sub_area_id = False
                        sub_area_id = self.fuzzy_search(sub_area_value, sub_area_list)
                    elif rb_delivery_user and rb_delivery_user.default_sub_area_id:
                        sub_area_id = rb_delivery_user.default_sub_area_id
                    if sub_area_id:
                        sub_area_id = sub_area_id.id
                if not sub_area_id:
                    raise_error(220, {'field_name': 'sub area', 'field_value': str(sub_area_value)})
                else:
                    sub_area = get_sub_area([('id', '=', sub_area_id)])
                    values.update({'customer_sub_area': sub_area.id, 'customer_area': sub_area.parent_id.id})



    def check_business_alt_sub_area(self, values):
        if str(values['business_alt_sub_area']).isnumeric():
            business_alt_sub_area = self.env['rb_delivery.sub_area'].search(['|',('id','=',values['business_alt_sub_area']),('code','=',values['business_alt_sub_area'])])
        else:
            business_alt_sub_area = self.env['rb_delivery.sub_area'].search(['|',('code','=',values['business_alt_sub_area']),('name','=',values['business_alt_sub_area'])])

        if business_alt_sub_area and business_alt_sub_area.id:
            values['business_alt_sub_area'] = business_alt_sub_area.id
            values['business_alt_area'] = business_alt_sub_area.parent_id.id
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'sub area','field_value':str(values['buiness_alt_sub_area'])})

    def check_payment_type(self, values,rb_delivery_user):
        if str(values['payment_type']).isnumeric():
            values['payment_type'] = values['payment_type']
            payment_type = self.env['rb_delivery.payment_type'].search([('id','=',values['payment_type'])])
            if payment_type and payment_type.id:
                values['payment_type'] = payment_type.id
            else:
                payment_type_id = self.env['rb_delivery.payment_type'].search([('code','=',values['payment_type'])])
                if payment_type_id and payment_type_id.id:
                    values['payment_type'] = payment_type_id.id
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'payment type','field_value':str(values['payment_type'])})
        else:
            payment_type_id = self.env['rb_delivery.payment_type'].search([('code','=',values['payment_type'])])
            if payment_type_id and payment_type_id.id:
                values['payment_type'] = payment_type_id.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'payment type','field_value':str(values['payment_type'])})

    def _check_order_type(self,values):
        order_type=False
        if str(values['order_type_id']).isnumeric():
            order_type = self.env['rb_delivery.order_type'].sudo().search([('id','=',values['order_type_id'])])
        if not order_type:
            order_type = self.env['rb_delivery.order_type'].sudo().search([('code','=',values['order_type_id'])])
        if not order_type:
            order_type = self.env['rb_delivery.order_type'].sudo().search([('name','=',values['order_type_id'])])
        if order_type:
            values['order_type_id'] = order_type.id
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'order type','field_value':str(values['order_type_id'])})

    def update_whatsapp(self,values):
        values['customer_mobile'] = values['customer_mobile'].strip()
        if ('cus_whatsapp_mobile' in values and values['cus_whatsapp_mobile']) or ('cus_second_whatsapp_mobile' in values and values['cus_second_whatsapp_mobile']) :
            pass
        else:
            try:
                prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
                prefix_two = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_two')
                # check if there is leading zero then remove it
                # TODO this may valid only to palestine and jordan
                if values['customer_mobile']:
                    no_space_first_number=values['customer_mobile'].strip()
                    number = no_space_first_number
                    if no_space_first_number[0]=='0':
                        no_space_first_number=no_space_first_number[1:]
                    pattern ="[٠١٢٣٤٥٦٧٨٩]"
                    if number and re.match(str(pattern), str(no_space_first_number)):
                        mobile = unidecode(u''+number)
                        no_space_first_number = mobile
                    country = ''
                    if 'customer_country' in values and values['customer_country']:
                        country = self.env['rb_delivery.country'].search([('id','=',values['customer_country'])])
                    elif self.customer_country:
                        country = self.customer_country
                    if country and country.key_number:
                        values['cus_whatsapp_mobile'] = country.key_number+no_space_first_number
                    else:
                        values['cus_whatsapp_mobile'] = prefix_one+no_space_first_number

                if 'second_mobile_number' in values and values['second_mobile_number']:
                    no_space_second_number=values['second_mobile_number'].strip()
                    number = no_space_second_number
                    if no_space_second_number[0]=='0':
                        no_space_second_number=no_space_second_number[1:]
                    pattern ="[٠١٢٣٤٥٦٧٨٩]"
                    if number and re.match(str(pattern), str(no_space_second_number)):
                        mobile = unidecode(u''+number)
                        no_space_second_number = mobile

                    country = ''
                    if 'customer_country' in values and values['customer_country']:
                        country = self.env['rb_delivery.country'].search([('id','=',values['customer_country'])])
                    elif self.customer_country:
                        country = self.customer_country
                    if country and country.key_number:
                        values['cus_second_whatsapp_mobile'] = country.key_number+no_space_second_number
                    else:
                        values['cus_second_whatsapp_mobile'] = prefix_one+no_space_second_number

                    # Special case for westbank
                if 'second_mobile_number' not in values or ('second_mobile_number' in values and not values['second_mobile_number']) and values['customer_mobile'] and (prefix_two or country.second_key_number):
                    no_space_second_number=values['customer_mobile'].strip()
                    number = no_space_second_number
                    if no_space_second_number[0]=='0':
                        no_space_second_number=no_space_second_number[1:]
                    pattern ="[٠١٢٣٤٥٦٧٨٩]"
                    if number and re.match(str(pattern), str(no_space_second_number)):
                        mobile = unidecode(u''+number)
                        no_space_second_number = mobile

                    if 'customer_country' in values and values['customer_country']:
                        country = self.env['rb_delivery.country'].search([('id','=',values['customer_country'])])
                    elif self.customer_country:
                        country = self.customer_country
                    if country and country.second_key_number:
                        values['cus_second_whatsapp_mobile'] = country.second_key_number+no_space_second_number
                    else:
                        values['cus_second_whatsapp_mobile'] = prefix_two+no_space_second_number
            except:
                pass

    def _get_field_value(self, model, field_value, model_fields):
        if str(field_value).isnumeric():
            return self.env[model].sudo().search([('id', '=', field_value)]) or ('code' in model_fields and self.env[model].sudo().search([('code', '=', field_value)]))
        else:
            return ('code' in model_fields and self.env[model].sudo().search([('code', '=', field_value)])) or self.env[model].sudo().search([('name', '=', field_value)])

    def check_many2one_fields(self,values):
        field_names = []
        field_values = []
        keys_to_remove = []
        checked_models = ['rb_delivery.area','rb_delivery.sub_area','rb_delivery.user','rb_delivery.reject_reason']
        for field_name, field_value in values.items():
            if field_value:
                if isinstance(field_value, str) and field_value.strip() == '':
                    keys_to_remove.append(field_name)
                    continue
                field = self.env['ir.model.fields'].sudo().search_read([('name','=',field_name),('model','=','rb_delivery.order')],['ttype','relation','field_description','name'])
                if field and field[0]:
                    field_data = field[0]
                    field_type = field_data.get('ttype')
                    model = field[0].get('relation')
                    if field_type == 'many2one' and model not in checked_models:
                        model_fields = self.env[model].fields_get()
                        returned_value = self._get_field_value(model,field_value,model_fields)
                        if returned_value:
                            values[field_name] = returned_value.id
                        else:
                            field_names.append(field[0].get('field_description'))
                            field_values.append(values[field_name])

        for key in keys_to_remove:
            del values[key]
            
        if len(field_names)>0 and len(field_values)>0:
            ids = self.ids
            self.env['rb_delivery.error_log'].raise_olivery_error(220,ids,{'field_name':''.join(str(field_names)),'field_value':''.join(str(field_values))})

    def check_minus_values(self,values):
        accept_minus = self.env['rb_delivery.client_configuration'].get_param('accept_minus_value_in_cod')
        if not accept_minus:
            if values.get('cost') and float(values.get('cost')) < 0:
                self.env['rb_delivery.error_log'].raise_olivery_error(294,self.id,{'field_name':'package cost'})
            if values.get('copy_total_cost') and float(values.get('copy_total_cost')) < 0:
                self.env['rb_delivery.error_log'].raise_olivery_error(294,self.id,{'field_name':'total cost'})

    @api.model
    def check_order_type(self, values, skip_status=False):
        order_type = False

        def search_order_type(fields, value):
            if not fields or not value:
                return self.env['rb_delivery.order_type'].sudo().browse()
            domain = []
            for i, field in enumerate(fields):
                if i == 0:
                    domain = [(field, '=', value)]
                else:
                    domain = ['|'] + domain + [(field, '=', value)]
            return self.env['rb_delivery.order_type'].sudo().search(domain, limit=1)

        if 'order_type_id' in values:
            try:
                order_id = int(values['order_type_id'])
                order_type = self.env['rb_delivery.order_type'].browse(order_id)
                order_type = order_type if order_type.exists() else None
            except (ValueError, TypeError):
                order_type = None
            
            if not order_type:
                order_type = search_order_type(['code', 'name'], str(values['order_type_id']))
        
        elif 'order_type_code' in values:
            order_type = search_order_type(['code'], str(values['order_type_code']))
        
        elif 'order_type_name' in values:
            order_type = search_order_type(['name'], str(values['order_type_name']))

        if order_type:
            values['order_type_id'] = order_type.id
        else:
            field_value = (values.get('order_type_id') or values.get('order_type_code') or values.get('order_type_name'))
            self.env['rb_delivery.error_log'].raise_olivery_error(220, self.id, {'field_name': _('order type'),'field_value': str(field_value)})

        if order_type and order_type.state and not skip_status:
            status = self.env['rb_delivery.status'].search([('name', '=', order_type.state),'|',('status_type', '=', False),('status_type', '=', 'olivery_order')], limit=1)
            
            if not status:
                self.env['rb_delivery.error_log'].raise_olivery_error(339, self.id, {'orders_type': order_type.name,'status': self.name})
            values['state'] = order_type.state


    def prepare_values(self,values):
        self.check_minus_values(values)
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        driver_allow_to_create = self.env['rb_delivery.client_configuration'].get_param('order_create_driver_ability')
        if (values.get('order_type_id') or values.get('order_type_code') or values.get('order_type_name')) and not self._context.get('is_clone_by_order_type') and not values.get('state'):
            self.check_order_type(values=values)

        if values.get('reject_reason'):
            self.check_reject_reason(values)

        if not driver_allow_to_create and is_driver:
            self.env['rb_delivery.error_log'].raise_olivery_error(221,self.id)
        else:
            pass
        if 'state' not in values or ('state' in values and not values['state']):
            status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            if status:
                values['state'] = status.name

        if 'assign_to_business' in values and values['assign_to_business'] :
            rb_delivery_user = self.env['rb_delivery.user'].sudo().search([('id', '=',values['assign_to_business'] )])
        else :
            rb_delivery_user = self.env['rb_delivery.user'].sudo().search([('user_id.id', '=', self._uid),('role_code','=','rb_delivery.role_business')])
            if rb_delivery_user:
                values['assign_to_business'] = rb_delivery_user.id
        if rb_delivery_user and rb_delivery_user.role_code != 'rb_delivery.role_business':
            self.env['rb_delivery.error_log'].raise_olivery_error(222,self.id,{'field_name':'Sender','role_name':'business role','username' : rb_delivery_user.username})
        #TODO Add general function to check all many2one fields
        if 'customer_country' in values and values['customer_country']:
            self.check_country(values)
        if not values.get('order_type_id'):
            if rb_delivery_user and rb_delivery_user.order_type:
                values['order_type_id']=rb_delivery_user.order_type.id

        if values.get('address_tag') and (not values.get('customer_area') or not values.get('customer_sub_area')):
            address_tag = self.check_full_address(values.get('address_tag'), rb_delivery_user)
            values['address_tag'] = address_tag.name
            values['customer_sub_area'] = address_tag.sub_area_id.id
            values['customer_area'] = address_tag.area_id.id

        if ('assign_to_agent' in values and values['assign_to_agent']):
            agent_rb_delivery_user = self.env['rb_delivery.user'].sudo().search([('id', '=',values['assign_to_agent'] )])
            driver_role_codes = ['rb_delivery.role_driver','rb_delivery.role_delivery_company','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative']
            if agent_rb_delivery_user and agent_rb_delivery_user.role_code not in driver_role_codes:
                self.env['rb_delivery.error_log'].raise_olivery_error(222,self.id,{'field_name':'Agent','role_name':'driver role','username' :agent_rb_delivery_user.username})

        if 'customer_mobile' in values and values['customer_mobile']:
            mobile_number = self._check_customer_mobile_number(values['customer_mobile'])
            if mobile_number:
                values['customer_mobile'] = mobile_number
            if 'second_mobile_number' in values and values['second_mobile_number']:
                second_mobile_number = self._check_second_customer_mobile_number(values['second_mobile_number'])
                if second_mobile_number:
                    values['second_mobile_number'] = second_mobile_number
            self.update_whatsapp(values)
        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'],values)
            state_last_updated_by = self.env['res.users'].search([('id', '=', self._uid)])
            values['status_last_updated_by'] = state_last_updated_by.id
            fmt = "%Y-%m-%d %H:%M:%S"
            date = datetime.strftime(datetime.today(), fmt)
            values['status_last_updated_on'] = date
        if ('customer_area' in values and values['customer_area']) or ('customer_area_id' in values and values['customer_area_id']) or ('customer_area_code' in values and values['customer_area_code']) :
            if 'customer_area_id' in values and values['customer_area_id']:
                area_value = values['customer_area_id']
            elif 'customer_area_code' in values and values['customer_area_code']:
                area_value = values['customer_area_code']
            else:
                area_value = values['customer_area']
            values['customer_area'] = self.check_area(values,area_value,rb_delivery_user)
        elif values.get('google_address'):
            new_context = dict(self._context)
            new_context['skip_area_validation']=True
            self = self.with_context(new_context)
            adderss_vals = values.get('google_address').split(',')
            area_value = adderss_vals[0] if len(adderss_vals)>0 else ''

            values['customer_area'] = self.check_area(values, area_value, rb_delivery_user)
            if not values.get('customer_sub_area') and len(adderss_vals)>1:

                values['customer_sub_area'] = adderss_vals[1]


        if 'customer_area' in values and values['customer_area']:
            self.check_sub_area(values,rb_delivery_user,values['customer_area'])
        else:
            self.check_sub_area(values,rb_delivery_user)


        if ('business_alt_area' in values and values['business_alt_area']) or ( 'business_alt_area_id' in values and values['business_alt_area_id']) or ( 'business_alt_code' in values and values['business_alt_code']) :
            if 'business_alt_area_id' in values and values['business_alt_area_id']:
                area_value = values['business_alt_area_id']
            elif 'business_alt_code' in values and values['business_alt_code']:
                area_value = values['business_alt_code']
            else:
                area_value = values['business_alt_area']
            values['business_alt_area'] =  self.with_context(is_business=True).check_area(values, area_value, rb_delivery_user)

        if 'business_alt_sub_area' in values and values['business_alt_sub_area'] and 'show_alt_address' in values and values['show_alt_address'] :
            self.check_business_alt_sub_area(values)



        if not rb_delivery_user.inclusive_delivery and 'delivery_cost' in values and 'cost' in values and values['cost']:
            values['copy_total_cost']=float(values['cost'])+float(values['delivery_cost'])

        if rb_delivery_user.inclusive_delivery and 'delivery_cost' in values and 'copy_total_cost' in values and values['copy_total_cost']:
            values['cost']=float(values['copy_total_cost'])-float(values['delivery_cost'])

        if 'reference_id' in values and values['reference_id']:
            self._check_reference_id(values)
            values['reference_id']=values['reference_id'].strip()

        # do actions on create
        if 'state' in values and values['state']:
            self.do_action_create(values)

        if 'second_mobile_number' in values and values['second_mobile_number']:
            mobile_number = self._check_customer_mobile_number(values['second_mobile_number'],'Second mobile number')
            if mobile_number:
                values['second_mobile_number'] = mobile_number

        self.show_toast_for_cost_zero(values)
        if 'sequence' not in values or not values['sequence']:
            new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.order')
            values['sequence'] = new_sequence
        if not values.get('customer_area'):
            default_area = self.env['rb_delivery.area'].search([('is_default', '=', True)])
            if default_area:
                values['customer_area'] = default_area.id
        if values.get('customer_area') and values.get('customer_sub_area'):
            area_id = values.get('customer_area')
            sub_area_id = values.get('customer_sub_area')
            self.check_address_tag(area_id,sub_area_id,values)
        self.check_many2one_fields(values)
        if values.get('customer_area') or values.get('customer_sub_area'):
            self.get_zone(values)
        self.check_for_excel_date_field(values)
        if 'reschedule_date' in values and values['reschedule_date']:
            self.check_reschedule_date(values)
        if values.get('delivery_cost_on_customer') and values.get('delivery_cost_on_sender'):
            self.env['rb_delivery.error_log'].raise_olivery_error(807,self.id,{})
        return values

    def update_order(self,values,order,data):

        updated_values={}

        try:
            if 'replacement_order' in values and values['replacement_order'] and order.id:
                updated_values['readonly_replacement_order'] = True
                self.create_clone_order(order, values)

            if 'returned_order' in values and values['returned_order'] and order.id:
                self.create_returned_clone_order(order)

            self.create_signature(order,values)

            #TO_DO work around for last update by odoobot
            # TODO should be removed the since it will write twice and duplciate the notification
            self.env['rb_delivery.action'].notify_for_action_type('for_status',state_name=order.state,object=order,with_delay=True)

        except Exception as e:
            order.message_post(str(e))
        if data.get('sub_area_map'):
            sub_area_map = data.get('sub_area_map')
            order.message_post(body=_("The value of sub area is set to '%s', since the sub area '%s' does not exist in the system but is mapped to '%s' under the user '%s'.")%(sub_area_map.sub_area_id.name,sub_area_map.name,sub_area_map.sub_area_id.name,order.sudo().assign_to_business.username))
        if data.get('area_map'):
            area_map = data.get('area_map')
            order.message_post(body=_("The value of area is set to '%s', since the area '%s' does not exist in the system but is mapped to '%s' under the user '%s'.")%(area_map.area_id.name,area_map.name,area_map.area_id.name,order.sudo().assign_to_business.username))
        if updated_values:
            order.write(updated_values)

    @api.model
    def create(self, values):
        # TODO we need to enable this one
        if values:
            if values.get('reference_id'):
                values = self.extract_number_from_url(values)
            convert_coordinates_to_locality = request.env['rb_delivery.client_configuration'].sudo().get_param('convert_coordinates_to_locality')

            if convert_coordinates_to_locality:
                self.get_locality_by_coordinates(values)
            result = self.createValidator(values)
            if(result['valid'] and not result['order'] and result['values']):
                values = result['values']
                data = result['data']
                temp_follow_orders_map = ''
                if result['values'].get('follow_up_orders_map'):
                    temp_follow_orders_map = result['values'].get('follow_up_orders_map')
                    result['values'].pop('follow_up_orders_map')
                context = dict(self.env.context, mail_notrack=True)

                # Call the super method to perform the actual create
                with self.env.cr.savepoint():
                    order = super(rb_delivery_order, self.with_context(context)).create(result['values'])

                    order.assign_to_business.with_delay(channel="root.basic",max_retries=2)._compute_order_count()

                    if order.latitude and order.longitude:
                        order.customer_location = self._generate_location_link(order.latitude, order.longitude)
                    if order.customer_location:
                        # Expand short Google Maps URL and extract coordinates
                        expanded_url = self.expand_google_maps_short_url(order.customer_location)
                        coordinates = self.extract_coordinates_from_url(expanded_url)

                        if coordinates:
                            order.latitude, order.longitude = coordinates

                    if order.business_alt_latitude and order.business_alt_longitude:
                        order.business_alt_location = self._generate_location_link(order.business_alt_latitude, order.business_alt_longitude)
                    if order.business_alt_location:
                        expanded_url = self.expand_google_maps_short_url(order.business_alt_location)
                        coordinates = self.extract_coordinates_from_url(expanded_url)

                        if coordinates:
                            order.business_alt_latitude, order.business_alt_longitude = coordinates

                    # Prepare the changes
                    changes = order._prepare_custom_changes([{}],True)
                    # Log the changes
                    self._log_custom_changes(changes)


                    if order and temp_follow_orders_map:
                        import json
                        json_dict = json.loads(temp_follow_orders_map)
                        for follow_up_order in json_dict:
                            follow_up_order = self.env['rb_delivery.follow_up_order'].create({'order_id':order.id,'follow_up_sequence':follow_up_order['sequence'], 'name':follow_up_order['name']})
                            self.write({'follow_up_order': [(4, follow_up_order.id)]})

                    self.update_order(values,order,data)
                    order.check_pricelist_used()
                    return order
            elif result['order']:

                result['order'].check_pricelist_used()
                return result['order']
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(249,self.id,{})
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(246,self.id,{})

    @api.model_create_multi
    def create_multi(self, vals):
        orders = []
        created_orders = self.env['rb_delivery.order']
        convert_coordinates_to_locality = request.env['rb_delivery.client_configuration'].sudo().get_param('convert_coordinates_to_locality')

        for values in vals:
            result = self.createValidator(values)
            if convert_coordinates_to_locality:
                self.get_locality_by_coordinates(values)
            if(result['valid'] and not result['order'] and result['values']):
                orders.append(result['values'])
            elif result['order']:
                created_orders += result['order']


        context = dict(self.env.context, mail_notrack=True)

        # Call the super method to perform the actual create
        orders_new = super(rb_delivery_order, self.with_context(context)).create(orders)
        # Prepare the changes
        changes = orders_new._prepare_custom_changes([{} for i in orders],True)
        # Log the changes
        self._log_custom_changes(changes)
        for order in orders_new:
            created_orders += order

        created_orders.check_pricelist_used()
        return created_orders

    def check_inclusive_business(self,values):
        if 'cost' not in values and 'copy_total_cost' in values and values['copy_total_cost'] is not False:
            values['cost'] = values.get('copy_total_cost')
        if 'copy_total_cost' not in values and 'cost' in values and values['cost'] is not False:
            values['copy_total_cost'] = values.get('cost')
        return values
    
    def clean_values(self,values):
        return {key: re.sub(r'\s+', ' ', value).strip() if isinstance(value, str) else value for key, value in values.items()}
    
    @api.model
    def createValidator(self, values):
        if self.env.context.get('from_excel'):
            values = self.clean_values(values)
        self.check_inclusive_business(values)
        if values.get('assign_to_agent') and not isinstance(values.get('assign_to_agent'), int):
            agent = self.env['rb_delivery.user'].search(['|','|',('mobile_number', '=', values.get('assign_to_agent')), ('commercial_name', '=', values.get('assign_to_agent')), ('username', '=', values.get('assign_to_agent'))], limit=1)
            values['assign_to_agent'] = agent.id

        assign_agent_conf = self.env['rb_delivery.client_configuration'].get_param('auto_assign_business_driver_to_orders_when_create')
        if values.get('customer_payment') is not None:
            self.validate_customer_payment(values['customer_payment'])
        is_exist = self._check_reference_sequence(values)
        if values.get('assign_to_agent'):
            values['assign_to_distributor_date'] = datetime.now()
        elif values.get('assign_to_business') and assign_agent_conf:
            business = False
            if values.get('assign_to_business') and isinstance(values.get('assign_to_business'), int):
                business = self.env['rb_delivery.user'].browse([values.get('assign_to_business')])
            elif isinstance(values.get('assign_to_business'), str):
                business = self.env['rb_delivery.user'].search(['|','|','|',('mobile_number','=',values.get('assign_to_business')),('username','=',values.get('assign_to_business')),('user_sequence','=',values.get('assign_to_business')),('commercial_name','=',values.get('assign_to_business')), ('role_code','=','rb_delivery.role_business')])
                values['assign_to_business'] = business.id
            
            if business and business.driver_id:
                values['assign_to_agent'] = business.driver_id.id
                values['assign_to_distributor_date'] = datetime.now()

        if 'inclusive_delivery' not in values:
                if 'assign_to_business' in values and values['assign_to_business']:
                    assign_to_business = values['assign_to_business']
                else:
                    assign_to_business = self.env.user.rb_user.id
                values['inclusive_delivery']=self.get_default_inclusive_delivery(assign_to_business)
        if is_exist:
            return {'valid': True, 'order': is_exist}
        else:
            if values.get('assign_to_business') and isinstance(values.get('assign_to_business'), str):
                business = self.env['rb_delivery.user'].search(['|','|','|',('mobile_number','=',values.get('assign_to_business')),('username','=',values.get('assign_to_business')),('user_sequence','=',values.get('assign_to_business')),('commercial_name','=',values.get('assign_to_business')), ('role_code','=','rb_delivery.role_business')])
                values['assign_to_business'] = business.id

            self.prepare_values(values)
            self.get_required_fields(values)
            if not 'state' in values or not values['state']:
                values['state'] = self.get_default_status()
                if not values['state']:
                    self.env['rb_delivery.error_log'].raise_olivery_error(245,self.id)
            current_status=values['state']
            values = self.filter_values(values)
            self.authorize_edit(values,current_status)
            data = {}
            if values.get('customer_sub_area_map'):
                data['sub_area_map'] = values.get('customer_sub_area_map')
                del values['customer_sub_area_map']
            if values.get('customer_area_map'):
                data['area_map'] = values.get('customer_area_map')
                del values['customer_area_map']
            if values.get('state'):
                state = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            else:
                state_val = self.get_default_status()
                state = self.env['rb_delivery.status'].search([('name','=',state_val),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            values['web_color']=state.web_color
            values['seq_exist']=""
        
        if values.get('latitude') and values.get('longitude'):
            values['customer_location'] = self._generate_location_link(values.get('latitude'), values.get('longitude'))
        if values.get('customer_location'):
            # Expand short Google Maps URL and extract coordinates
            expanded_url = self.expand_google_maps_short_url(values.get('customer_location'))
            coordinates = self.extract_coordinates_from_url(expanded_url)

            if coordinates:
                values['latitude'], values['longitude'] = coordinates


        return {'valid': True, 'order': False, 'values': values, 'data': data if data else {}}


    def check_pricelist_used(self):

        prevent_adding_order_no_pricelist = self.env['rb_delivery.client_configuration'].get_param('prevent_adding_order_no_pricelist')
        if not prevent_adding_order_no_pricelist:
            return
        for rec in self:
            business = rec.sudo().assign_to_business
            domains = rec.env['rb_delivery.pricelist'].get_domain(rec.order_type_id,business,rec.customer_area,business.area_id,business.pricelist_id,rec.customer_sub_area)
            pricelist_used = False
            for domain in domains:
                res = rec.env['rb_delivery.pricelist'].get_pricelist_item(business.pricelist_id, domain)
                if res.id:
                    pricelist_used = res
                    break

            if not pricelist_used:
                if self.env.context.get('lang') == 'ar_SY' and business.area_id.arabic_name and rec.customer_area.arabic_name:
                    rec.env['rb_delivery.error_log'].raise_olivery_error(337,rec.id,{'from': business.area_id.arabic_name, 'to': rec.customer_area.arabic_name, 'price_list_name': business.pricelist_id.name})
                else:
                    rec.env['rb_delivery.error_log'].raise_olivery_error(337,rec.id,{'from': business.area_id.name, 'to': rec.customer_area.name, 'price_list_name': business.pricelist_id.name})

    @api.model
    def get_field_options(self, field, domain, model):

        if model == 'rb_delivery.user':
            if field == 'assign_to_business':
                records = self.env['rb_delivery.user'].search([('role_code', '=', 'rb_delivery.role_business')])
                result = [{'name': [record.mobile_number, record.display_name, record.username], 'id': record.id} for record in records]
                return result
            else:
                records = self.env['rb_delivery.user'].search([('role_code', '=', 'rb_delivery.role_driver')])
                result = [{'name': [record.mobile_number, record.display_name, record.username], 'id': record.id} for record in records]
                return result
        else:
            records = self.env[model].search(domain)
            result = [{'name': [record.display_name], 'id': record.id} for record in records]
            return result

    @api.model
    def append_fields_to_note(self, values):
        fields_ids = self.env['rb_delivery.client_configuration'].get_param('append_field_to_note')

        note = values['note']


        if fields_ids:
            fields = self.env['ir.model.fields'].browse(fields_ids)

            if 'note' not in values:
                 note = ""


            for field in fields:
                if field.name in values and values[field.name]:
                    note = f"{note + ' -' if note else ''}{values[field.name]}"
        if note:
            note = note.replace('\\', '\ ')
            note = note.replace('/', '/ ')
        return note

    def filter_values(self,values):
        new_values={}
        for key in values.keys():

            if key in self.SUDO_FIELDS or values[key] != self.get_default_value_of(key):
                new_values[key]=values[key]
        return new_values


    def get_default_value_of(self,field_name):
        if field_name in self._fields:
            field = self._fields[field_name]

            default_value = field.default
            if default_value:
                if callable(default_value):
                    if default_value(self):
                        try:
                            return default_value(self).id
                        except:
                            return default_value(self)
                    else:
                        return False
                else:
                    return default_value

            elif field.type=='one2many' or field.type=='many2many':
                return [[6, False, []]]
            else:
                return False
        else:
            return False
    @api.one
    def update_number_of_orders(self,business,orders):
        business.write({'number_of_orders' : orders})


    def get_follow_sequences(self, sequence):
        follow_sequences = self.env['rb_delivery.order'].sudo().search_read(['|',('sequence','=',sequence), ('reference_id', '=', sequence)], fields=['follow_up_order'])
        if not follow_sequences:
            raise ValidationError(_("No order with sequence %s found") % sequence)
        sequenes = []
        for follow_sequence in follow_sequences[0].get('follow_up_order'):
            rec = self.env['rb_delivery.follow_up_order'].sudo().browse(follow_sequence)
            rec_obj = {'follow_up_sequence': rec.sudo().follow_up_sequence, 'name': rec.sudo().name}
            sequenes.append(rec_obj)
        return sequenes

    @api.one
    def update_agent_number_of_orders(self,agent,orders):
        agent.write({'driver_number_of_orders' : orders})

    def copy(self, default=None):
        default = dict(default or {})
        default.update({ 'is_partner_order': False, 'is_sender_partner_order': False, 'partner_reference_id':False})
        if self.env.user.has_group('rb_delivery.role_business'):
            default.update({'assign_to_agent': False})
        res = super(rb_delivery_order, self).copy(default)
        res.message_post(body=_("This order was duplicated from %s") % (self.sequence))
        return res

    def get_previous_status(self,groupby):
        statuses = {}
        if 'previous_status' in groupby:
            status_recs = self.env['rb_delivery.status'].search_read([],fields=['title','name'])
            for status in status_recs:
                if status.get('name') and status.get('title'):
                    statuses[status.get('name')] = status.get('title')
        return statuses

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        """
            Override read_group to orderby create date in DESC if record grouped by date
        """
        if 'name' in groupby:
            self.env['rb_delivery.error_log'].raise_olivery_error(248,self.id,{})
            #raise Warning(_("Please groupby first."))

        users_domain = self.get_domain_for_users_page()
        if users_domain:
            sub_domain = []
            if domain[0] == "&":
                start_index = domain.index('&')
                if domain.index('|'):
                    end_index = domain.index('|')
                else:
                    end_index = -1
                sub_domain = domain[start_index + 1:end_index]
            domain = domain + sub_domain + users_domain

        else:
            for dom in domain:
                if dom[0] == 'assign_to_business' or dom[0] == 'commercial_name':
                    if not isinstance(dom[2],list) and ',' in str(dom[2]):
                        temp_str = dom[2].split(',')
                        dom[2] = temp_str[1].strip()

        res = super(rb_delivery_order, self).read_group(domain, fields, groupby, offset=offset, limit=limit, orderby=orderby, lazy=lazy)

        if any(field in groupby for field in ('create_date:day', 'create_date:week', 'create_date:month', 'create_date:year')):
            res.sort(key=lambda x: x.get('create_date', '1970-01-01'), reverse=True)
        prevented_groups_from_seeing_totals = self.env['rb_delivery.client_configuration'].get_param('prevent_preview_totals_on_groupby')
        delivery_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid),('group_id','in',prevented_groups_from_seeing_totals)])
        statuses = self.get_previous_status(groupby)

        if delivery_user :
            for record in res:
                if 'previous_status' in groupby and record.get('previous_status') and record.get('previous_status') in statuses:
                    record['previous_status'] = statuses[record.get('previous_status')]
                record['money_collection_cost'] = False
                record['delivery_cost'] = False
                record['required_from_business'] = False
                record['agent_cost'] = False
                record['delivery_profit'] = False
        elif 'previous_status' in groupby:
            for record in res:
                if record.get('previous_status') and record.get('previous_status') in statuses:
                    record['previous_status'] = statuses[record.get('previous_status')]

        return res

    def check_ability_change_status_for_same_reference(self,values,rec,orders):
        reference_ids = orders.filtered(lambda order: order.state == 'deleted' or order.state == 'canceled').mapped('reference_id')
        reference_count = Counter(reference_ids)
        if rec.sudo().assign_to_business and rec.reference_id and values['state'] not in ['deleted','canceled'] and rec.state in ['deleted','canceled']:
            if reference_count[rec.sudo().reference_id] > 1:
                self.env['rb_delivery.error_log'].raise_olivery_error(298,rec.id,{'order_reference':rec.reference_id})
            org_vals = {'state':values['state'],'assign_to_business':rec.sudo().assign_to_business.id,'reference_id':rec.sudo().reference_id}
            is_exist = self.check_order_exist(org_vals)
            if is_exist:
                self.env['rb_delivery.error_log'].raise_olivery_error(296,rec.id,{'order_reference':rec.reference_id,'active_status':rec.state_id.title, 'inactive_status': is_exist.state_id.title,'order_sequence':rec.sequence})

    def check_address_tag(self, area_id, sub_area_id, values=None,record=False):
        domain = [('address', '=', False),('sub_area_id', '=', sub_area_id),('area_id', '=', area_id)]
        address_tag = self.env['rb_delivery.address_tags'].sudo().search(domain, limit=1)
        tag_id = address_tag.id if address_tag else False
        if values is not None:
            values['address_tag'] = tag_id
            return values
        else:
            if not record:
                record = self
            record.with_context(skip_address_constraint=True).write({'address_tag': tag_id})

    #inherit module [olivery_osc]
    def guard_function(self,values):
        if 'state' in values and values['state']:
            allowed_status_names = []
            allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('allow_change_status_if_area_is_default_and_delivery_cost_zero')
            if allowed_statuses:
                allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')
                
        if values.get('latitude') and values.get('longitude'):
            values['customer_location'] = self._generate_location_link(values.get('latitude'), values.get('longitude'))
        if values.get('customer_location'):
            expanded_url = self.expand_google_maps_short_url(values.get('customer_location'))
            coordinates = self.extract_coordinates_from_url(expanded_url)

            if coordinates:
                values['latitude'], values['longitude'] = coordinates

        zero_cost_orders = []
        default_areas = []
        statuses_prevent_change_agent =self.env['rb_delivery.client_configuration'].get_param('prevent_update_agent_on_cloned_order')
        convert_coordinates_to_locality = self.env['rb_delivery.client_configuration'].sudo().get_param('convert_coordinates_to_locality')
                
        if convert_coordinates_to_locality:
            self.get_locality_by_coordinates(values)
            _logger.info("*********************values after getting locality************************")
            _logger.info(values)
            _logger.info("**********************values after getting locality***********************")
        use_default_area_as_fallback = self.env['rb_delivery.client_configuration'].sudo().get_param('use_default_area_as_fallback')
        if (values.get('order_type_id') or values.get('order_type_code') or values.get('order_type_name')) and not self._context.get('is_clone_by_order_type'):
            self.check_order_type(values=values, skip_status=True)

        if values.get('delivery_cost_on_customer') and 'delivery_cost_on_sender' not in values:
            values['delivery_cost_on_sender'] = False   
        if values.get('delivery_cost_on_sender') and 'delivery_cost_on_customer' not in values:
            values['delivery_cost_on_customer'] = False
            
        for rec in self:
            customer_val = values.get('delivery_cost_on_customer', rec.delivery_cost_on_customer)
            sender_val = values.get('delivery_cost_on_sender', rec.delivery_cost_on_sender)
            if customer_val and sender_val and ('delivery_cost_on_customer' in values or 'delivery_cost_on_sender' in values):
                self.env['rb_delivery.error_log'].raise_olivery_error(807, rec.id, {})
            rec.check_allow_to_edit_order_in_current_status(rec.state,values)
            if values.get('state'):
                rec.check_ability_change_status_for_same_reference(values,rec,self)
            if rec.sudo().assign_to_business and not rec.sudo().assign_to_business.active:
                error_log = self.env['rb_delivery.error_log'].raise_olivery_error(204,rec.sudo().id,{'sequence':rec.sudo().sequence,'business_name':rec.sudo().assign_to_business.username})
                values['error_log'] = error_log
            from_import = self._context.get('import_file')
            if from_import:
                if 'cost' in values and values['cost'] == False and values['cost'] != 0:
                    del values['cost']
                if 'copy_total_cost' in values and values['copy_total_cost'] == False and values['copy_total_cost'] != 0:
                    del values['copy_total_cost']
            if 'assign_to_agent' in values and values['assign_to_agent']:
                if  rec.agent_collection_id and rec.agent_collection_id.agent_id.id != values['assign_to_agent']:
                    self.env['rb_delivery.error_log'].raise_olivery_error(9001, rec.id, {
                        'order_sequence': rec.sequence,
                        'collection_sequence': rec.agent_collection_id.sequence
                    })
                if rec.replacement_order:
                    if 'state' in values and values['state'] :
                        status=values['state']
                    else:
                        status = rec.state
                    status_id=self.env['rb_delivery.status'].search([('name','=',status),'|',('status_type','=',False),('status_type','=','olivery_order')]).id
                    if statuses_prevent_change_agent:
                        edit_cloned_orders=status_id not in statuses_prevent_change_agent
                        order = self.env['rb_delivery.order'].search([('clone_reference','=',rec.id)])
                        if order and edit_cloned_orders:
                            order.write({'assign_to_agent':values['assign_to_agent']})

            if 'customer_country' in values and values['customer_country']:
                self.check_country(values)
                
            if values.get('replacement_order') or (rec.replacement_order and 'replacement_order' not in values and values.get('state')):
                values['readonly_replacement_order'] = True
                order = self.env['rb_delivery.order'].sudo().search([('clone_reference','=',rec.id)])
                if not order and not rec.clone_reference:
                    self.create_clone_order(rec,values)

            if 'returned_order' in values and values['returned_order']:
                order = self.env['rb_delivery.order'].search([('returned_clone_reference','=',rec.id)])
                if not order:
                    self.create_returned_clone_order(rec)
            if 'reference_id' in values and values['reference_id'] and values['reference_id'] != rec.reference_id:
                values['reference_id'] = self.normalize_reference_id(values['reference_id'])
                business_id = False
                if 'assign_to_business' in values and values['assign_to_business']:
                    business_id = values['assign_to_business']
                elif rec.sudo().assign_to_business:
                    business_id = rec.sudo().assign_to_business.id
                if business_id:
                    rec.check_ability_to_edit_order_with_ref_id(business_id,values['reference_id'])

            if 'state' in values and values ['state']:
                created_clone_by_order_type = self.check_clone_by_order_type(rec,values)
                if created_clone_by_order_type:
                    values['returned_clone_reference'] = created_clone_by_order_type.id
                rec.authorize_change_status(values['state'],values,rec.state)
                if not rec.delivery_cost and rec.customer_area and rec.customer_area.name and rec.customer_area.code:
                    if rec.delivery_cost == 0 and (_("default") in rec.customer_area.code.lower()) and values['state'] not in allowed_status_names:
                        zero_cost_orders.append(rec.sequence)
                        default_areas.append((''.join(rec.customer_area.name) + ' ('+rec.customer_area.code+')'))
                rec.check_if_in_collection(values)
                if values ['state'] == rec.state:
                    rec.message_post(body=_("Order status changed from "+rec.state+" to "+values['state']+" by " + self.env.user.name + " on " + datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")))

                rec.do_action(values['state'])

            rec.show_toast_for_cost_zero(values)
            rec.authorize_edit(values,rec.state)
            rec.pre_action(values)
            if 'assign_to_business' in values and values['assign_to_business'] :
                if rec.reference_id or ('reference_id' in values and values['reference_id'] and values['reference_id']):
                    reference_id = rec.reference_id if rec.reference_id else values['reference_id']
                    rec.check_ability_to_edit_order_with_ref_id(values['assign_to_business'],reference_id)

                rb_delivery_user = self.env['rb_delivery.user'].sudo().search([('id', '=',values['assign_to_business'] )])
                if rb_delivery_user and rb_delivery_user.role_code != 'rb_delivery.role_business':
                    self.env['rb_delivery.error_log'].raise_olivery_error(222,self.id,{'field_name':'Sender','role_name':'business role','username' :rb_delivery_user.username})
                if not values.get('order_type_id'):
                    if rb_delivery_user and rb_delivery_user.order_type:
                        values['order_type_id']=rb_delivery_user.order_type.id
            else :
                rb_delivery_user = rec.sudo().assign_to_business
            if ('customer_area' in values and values['customer_area']):
                values['customer_area'] = rec.check_area(values,values['customer_area'],rb_delivery_user)
                if rec.customer_area:
                    values['previous_area'] = rec.customer_area.id
            elif values.get('google_address') and values.get('by_locality'):
                del values['by_locality']
                new_context = dict(self._context)
                new_context['skip_area_validation']=True
                self = self.with_context(new_context)
                adderss_vals = values.get('google_address').split(',')
                area_value = adderss_vals[0] if len(adderss_vals)>0 else ''
                try:
                    values['customer_area'] = self.check_area(values, area_value, rb_delivery_user)
                    if not values.get('customer_area'):
                        # this error is raised in order to make the except block accessed and raise olivery error as usual
                        raise ValidationError('Error')
                except:
                    if use_default_area_as_fallback:
                        values['customer_area'] = self.env['rb_delivery.area'].search([('is_default','=',True)],limit=1).id
                    if not values.get('customer_area'):
                        self.env['rb_delivery.error_log'].raise_olivery_error(802,rec.id,{})
                    else:
                        message = _('No area found while getting locality by coordinates! So the default area has been set to the order...')
                        self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
                if not values.get('customer_sub_area') and len(adderss_vals)>1 and adderss_vals[1]:

                    values['customer_sub_area'] = adderss_vals[1]

            if 'business_alt_area' in values and values['business_alt_area']:
                values['business_alt_area'] = rec.with_context(is_business=True).check_area(values,values['business_alt_area'],rb_delivery_user)

            if 'customer_sub_area' in values and values['customer_sub_area']:
                if ('customer_area' in values and values['customer_area']):
                    area= values['customer_area']
                else:
                    area = rec.customer_area.id
                rec.check_sub_area(values,rb_delivery_user,area)
            if values.get('customer_sub_area_map'):
                sub_area_map = values.get('customer_sub_area_map')
                rec.message_post(body=_("The value of sub area is set to '%s', since the sub area '%s' does not exist in the system but is mapped to '%s' under the user '%s'.")%(sub_area_map.sub_area_id.name,sub_area_map.name,sub_area_map.sub_area_id.name,rec.sudo().assign_to_business.username))
                del values['customer_sub_area_map']

            if values.get('customer_area_map'):
                area_map = values.get('customer_area_map')
                rec.message_post(body=_("The value of sub area is set to '%s', since the sub area '%s' does not exist in the system but is mapped to '%s' under the user '%s'.")%(area_map.area_id.name,area_map.name,area_map.area_id.name,rec.sudo().assign_to_business.username))
                del values['customer_area_map']

            if 'business_alt_sub_area' in values and values['business_alt_sub_area']:
                rec.check_business_alt_sub_area(values)

            if 'customer_payment' in values:
                if 'customer_payment_one' not in values:
                    values['customer_payment_one'] = ''
                if 'customer_payment_two' not in values:
                    values['customer_payment_two'] = ''
                
            if ('customer_area' in values or 'customer_sub_area' in values) and not values.get('address_tag'):
                area_id = values.get('customer_area') if 'customer_area' in values else rec.customer_area.id if rec.customer_area else False
                sub_area_id = values.get('customer_sub_area') if 'customer_sub_area' in values else rec.customer_sub_area.id if rec.customer_sub_area else False
                rec.check_address_tag(area_id,sub_area_id)
        if zero_cost_orders and default_areas:
            self.env['rb_delivery.error_log'].raise_olivery_error(650,self[0].id,{'number_of_orders':len(zero_cost_orders), 'zero_cost_orders': ','.join( map(str, zero_cost_orders[0:5] )),'customer_area': ','.join(map(str, default_areas))})

    def search_zone(self,domain):
        zone = self.env['rb_delivery.area_zone'].search(domain,limit=1)
        return zone

    @api.model
    def send_otp_code(self, order_id):
        if not order_id:
            return False
        order = self.browse(order_id)
        if not order:
            return False
        otp_settings = self.env['rb_delivery.otp_status_checker'].sudo().search([], limit=1)
        if not otp_settings:
            return False
        otp, result = otp_settings.send_otp(record=order,recipient=order.cus_whatsapp_mobile)
        if not otp:
            return False
        uid = self.env.user.partner_id.id
        order.with_context(original_uid=uid).sudo().write({'otp_code': otp})
        return result

    @api.model
    def check_otp_code(self, order_id, otp_code):
        if not order_id:
            return False
        order = self.browse(order_id)
        if not order:
            raise ValidationError('ERRPR_ON_MOBILE')
        if order.otp_code == otp_code:
            uid = self.env.user.partner_id.id
            order.with_context(original_uid=uid).sudo().write({'otp_code': False})
            return True
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(299, order.id, {'order_sequence': order.sequence})

    @api.model
    def check_if_should_show_otp(self, order_id, status_name):
        if not order_id:
            return False
        order = self.browse(order_id)
        if not order:
            return False
        otp_settings = self.env['rb_delivery.otp_status_checker'].sudo().search([], limit=1)
        if not otp_settings:
            return False
        statuses = otp_settings.otp_statuses.mapped('name')
        rb_user = self.env['rb_delivery.user'].sudo().search([('user_id', '=', self._uid)], limit=1)
        if not rb_user:
            return False
        if status_name in statuses and rb_user and rb_user.group_id.id in otp_settings.groups.ids:
            return True
        return False                
    
    def get_zone(self,values):
        domain = []
        zone = False
        if values.get('customer_sub_area'):
            domain = [('sub_areas', 'in', values.get('customer_sub_area'))]
            zone = self.search_zone(domain)
        if not zone and values.get('customer_area'):
            domain = [('areas', 'in', values.get('customer_area'))]
            zone = self.search_zone(domain)
        values['zone_id'] = False
        if zone:
            values['zone_id'] = zone.id

    def get_locality_by_coordinates(self,values=False,rec=False,import_fields=False):
        api_condition=False
        is_import=False
        if rec and import_fields:
            is_import = True
            longitude=rec[import_fields.index('longitude')] if 'longitude' in import_fields and len(rec)> import_fields.index('longitude') else False
            latitude=rec[import_fields.index('latitude')] if 'latitude' in import_fields and len(rec)> import_fields.index('latitude') else False
            google_address=rec[import_fields.index('google_address')] if 'google_address' in import_fields and len(rec)> import_fields.index('google_address') else False
            customer_area_id=rec[import_fields.index('customer_area_id')] if 'customer_area_id' in import_fields and len(rec)> import_fields.index('customer_area_id') else False
            customer_sub_area=rec[import_fields.index('customer_sub_area')] if 'customer_sub_area' in import_fields and len(rec)> import_fields.index('customer_sub_area') else False

            api_condition = longitude and latitude and not google_address and not customer_area_id  and not customer_sub_area
        else:
            api_condition = (values.get('longitude') and values.get('latitude')
                and ('google_address' not in values or ('google_address' in values and not values['google_address']))
                and ('customer_area_id' not in values or ('customer_area_id' in values and not values['customer_area_id']))
                and ('customer_sub_area' not in values or ('customer_sub_area' in values and not values['customer_sub_area']))
            )
        if api_condition:

            if not is_import:
                latitude = values['latitude']
                longitude = values['longitude']
            confs = request.env['rb_delivery.client_configuration'].sudo().get_param(['google_map_key_for_server', 'google_map_locality_language'])

            geocode_url = f"https://maps.googleapis.com/maps/api/geocode/json?latlng={latitude},{longitude}&key={confs.get('google_map_key_for_server')}&language={confs.get('google_map_locality_language')}"

            response = requests.get(geocode_url)
            # Use an external service to get your public IP address
            if response.status_code == 200:
                geocode_result = response.json()
                results = geocode_result['results']
                if results:
                    street_name = ''
                    area = ''
                    sub_area = ''
                    for component in results[0]['address_components']:
                        if 'sublocality' in component['types']:
                            sub_area = component['long_name']
                        if 'locality' in component['types']:
                            area = component['long_name']
                        if 'route' in component['types']:
                            street_name = component['long_name']

                    if is_import:
                        if len(import_fields)>len(rec):
                            [rec.append('') if len(rec)<len(import_fields) else '' for i in import_fields]
                        if 'google_address' not in import_fields:
                            import_fields.append('google_address')
                            rec.append(f'{area},{sub_area},{street_name}')
                        else:
                            google_address_index = import_fields.index('google_address')
                            rec[google_address_index] = f'{area},{sub_area},{street_name}'

                        if 'customer_address' not in import_fields:
                            import_fields.append('customer_address')
                            rec.append(f'({street_name})')
                        else:
                            address_index = import_fields.index('customer_address')
                            rec[address_index] = f'({street_name})'
                    else:
                        values['by_locality']=True
                        values['google_address'] = f'{area},{sub_area},{street_name}'
                        if street_name:
                            current_address = values.get('customer_address',self.customer_address if self.customer_address else '') or ''
                            if street_name not in current_address:
                                values['customer_address'] = current_address+f'({street_name})'
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(803,self.id,{})
            else:
                _logger.info("*********************************************")
                _logger.info(response.text)
                _logger.info("*********************************************")
                self.env['rb_delivery.error_log'].raise_olivery_error(803,self.id,{})



    def check_allow_to_edit_order_in_current_status(self,current_status,values):
        logged_in_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if logged_in_user and logged_in_user.group_id:
            current_status_record = self.env['rb_delivery.status'].search([('name','=',current_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            if current_status_record and current_status_record.role_can_not_edit_status_ids and len(current_status_record.role_can_not_edit_status_ids) > 0 and logged_in_user.group_id.id in current_status_record.role_can_not_edit_status_ids.ids:
                self.env['rb_delivery.error_log'].raise_olivery_error(338,self.id,{'status':current_status_record.title,'sequence':self.sequence, 'user_role': logged_in_user.group_id.name})

    def check_ability_to_edit_order_with_ref_id(self,business_id,reference_id):
        reference_id = reference_id.replace(" ", "")
        domain = [('id','!=',self.id),('reference_id','=',reference_id),('state','!=','deleted')]
        orders = self.env['rb_delivery.order'].sudo().search(domain)
        if orders and len(orders) > 0:
            self.check_family_orders_reference(business_id,orders)
            for order in orders:
                if order.sudo().assign_to_business and order.sudo().assign_to_business.id == business_id:
                    self.env['rb_delivery.error_log'].raise_olivery_error(224,self.id,{'reference_id':reference_id,'sequence':order.sequence, 'business_name': order.sudo().assign_to_business.username})

    @api.model
    def create_pdf_report(self,order_ids,attachment,report_name):
        report = self.env['rb_delivery.report_job_queue'].sudo().generate_mobile_pdf_report(report_name, 'rb_delivery.order', order_ids, json.dumps({'uid': self.env.uid, 'lang': self.env.lang}))
        attachment.sudo().write({'datas':report})

    def apply_post_write_actions(self,values):
        self.check_notification_actions(values)

    def check_reject_reason(self,values):
        if str(values['reject_reason']).isnumeric():
            reject_reason = self.env['rb_delivery.reject_reason'].sudo().search([('id','=',values['reject_reason'])])
            if reject_reason:
                values['reject_reason'] = reject_reason.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'reject reason','field_value':str(values['reject_reason'])})
        else:
            reject_reason = self.env['rb_delivery.reject_reason'].sudo().search([('name','=',values['reject_reason'])],limit=1)
            if reject_reason:
                values['reject_reason'] = reject_reason.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(220,self.id,{'field_name':'reject reason','field_value':str(values['reject_reason'])})
        if reject_reason and reject_reason.to_status:
            values['state'] = reject_reason.to_status.name

    @api.model
    def get_whatsapp_messages(self, order_id):
        order = self.env['rb_delivery.order'].sudo().browse([order_id])
        messages = self.env['rb_delivery.whatsapp_message'].search([('is_whatsapp_message', '=', True)])
        return self.decorate_message(messages, order)

    @api.model
    def get_sms_messages(self, order_id):
        order = self.env['rb_delivery.order'].sudo().browse([order_id])
        messages = self.env['rb_delivery.whatsapp_message'].search([('is_sms', '=', True)])
        return self.decorate_message(messages, order)


    @api.model
    def get_sender_messages(self, order_id, is_whatsapp):
        domain = [('is_business_message', '=', True)]
        if is_whatsapp:
            domain.append(('is_whatsapp_message', '=', True))
        else:
            domain.append(('is_sms', '=', True))
        order = self.env['rb_delivery.order'].sudo().browse([order_id])
        messages = self.env['rb_delivery.whatsapp_message'].search(domain)
        return self.decorate_message(messages, order)

    def decorate_message(self, messages, order):
        result = []
        for message in messages:
            message_template = message.name
            placeholders = re.findall(r"\{(.*?)\}", message_template)
            for placeholder in placeholders:
                if '.' in placeholder:
                    parts = placeholder.split('.')
                    value = order
                    for part in parts:
                        value = getattr(value, part, '')
                        if hasattr(value, '_fields'):
                            field = value._fields.get(part)
                            if field and field.type == 'many2one':
                                value = getattr(value, 'name', getattr(value, 'display_name', ''))
                                break
                else:
                    value = getattr(order, placeholder, '')
                    if value ==  False:
                        value = ""
                    field = self._fields.get(placeholder)
                    if field and field.type == 'many2one':
                        value = getattr(value, 'name', getattr(value, 'display_name', ''))
                message_template = message_template.replace(f"{{{placeholder}}}", str(value))
            result.append(message_template)
        return result

    def update_one_by_one(self,values):
        values_arr = []
        orders = []
        for rec in self:
            values_copy = {}
            if ('customer_area' in values and values['customer_area']):
                if rec.sudo().customer_area:
                    values_copy['previous_area'] = rec.sudo().customer_area.id
            if 'assign_to_agent' in values and rec.sudo().assign_to_agent.id:
                values_copy['previous_agent'] = rec.sudo().assign_to_agent.id
            if ('assign_to_agent' in values and values['assign_to_agent']):
                values_copy['route_sequence'] = self.get_route_sequence_number(values)
            if values.get('state'):
                values_copy['previous_status'] = rec.sudo().state
                order_status = self.env['rb_delivery.status'].sudo().search([('name','=',values_copy['previous_status']),'|',('status_type','=',False),('status_type','=','olivery_order')])
                if order_status and order_status.title:
                    values_copy['previous_status_title'] = order_status.title

            if 'reject_reason' in values and values['reject_reason']:
                if str(values['reject_reason']).isnumeric():
                    reject_reason = self.env['rb_delivery.reject_reason'].search([('id','=',values['reject_reason'])])
                else:
                    reject_reason = self.env['rb_delivery.reject_reason'].search([('name','=',values['reject_reason'])])
                if reject_reason and reject_reason.to_status:
                    next_states = rec.state_id.next_state_ids
                    if not next_states or (next_states and reject_reason.to_status.id in rec.state_id.next_state_ids.ids):
                        values_copy['state'] = reject_reason.to_status.name

            if 'customer_mobile' in values and values['customer_mobile'] != rec.sudo().customer_mobile:
                values_copy['previous_customer_mobile_number'] = rec.sudo().customer_mobile

            if rec.pickup_agent_cost and rec.pickup_agent_cost != rec.saved_value_for_pickup_agent_cost:
                values_copy['saved_value_for_pickup_agent_cost'] = rec.pickup_agent_cost
            if values_copy:
                values_arr.append(values_copy)
                orders.append(rec)
        if len(values_arr)>0 and len(orders)>0:
            self.with_delay(channel="root.basic",max_retries=2).write_jq(values_arr,orders,self._context)

    @api.model
    def get_route_sequence_number(self,values):
        maxs = self.env['rb_delivery.order'].search([('assign_to_agent', '=', values['assign_to_agent'])], order='route_sequence DESC', limit=1)
        return maxs.route_sequence + 1


    def write_jq(self, values_arr, orders,context=False):
        if context:
            merged_context = self._context.copy()
            merged_context.update(context)
            self = self.with_context(**merged_context)
        # Group orders by their update values
        grouped_orders = defaultdict(list)
        for value, order in zip(values_arr, orders):
            key = frozenset(value.items())  # Convert the dict to frozenset to use as a key
            grouped_orders[key].append(order.id)  # Store the order ID
         # Bulk update for identical values
        for key, order_ids in grouped_orders.items():
            # Convert the frozenset back to a dictionary
            update_values = dict(key)

            username = self.env.user.name
            message = _("Order has been updated by %s through function write_jq.")%(username)
            grouped_order_set = self.browse(order_ids)
            data = {'uid':self._uid,'message':message,'records':grouped_order_set,'values':update_values,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)




    #inherit module [olivery_custody]

    def write_bulk(self,values):
        # TODO This is added for Abwaab Client and should be enhance later on ( for Connect )
        self.check_many2one_fields(values)
        self.check_minus_values(values)
        self.create_signature(self,values)
        if 'payment_type' in values and values['payment_type']:
            if str(values['payment_type']).isnumeric():
                payment_type = self.env['rb_delivery.payment_type'].sudo().search([('id','=',values['payment_type'])],limit=1)
                if payment_type:
                    values['payment_type'] = values['payment_type']
                else:
                    del values['payment_type']
            else:
                payment_type = self.env['rb_delivery.payment_type'].sudo().search([('name','=',values['payment_type'])],limit=1)
                if payment_type:
                    values['payment_type'] = payment_type.id
                else:
                    del values['payment_type']

        if'sequence' in values and values['sequence']:
            self._check_sequence(values)

        if 'reference_id' in values and values['reference_id']:
            return self._check_reference_id(values)

        if 'order_type_id' in values and values['order_type_id']:
            self._check_order_type(values)

        if 'customer_mobile' in values and values['customer_mobile']:
            self.update_whatsapp(values)

        if ('assign_to_agent' in values and values['assign_to_agent']):

            values['current_drivers'] = [(6,0,[])]
            values['assign_to_distributor_date'] = datetime.now()

        if 'state' in values and values['state']==False:
            self.env['rb_delivery.error_log'].raise_olivery_error(225,self.id,{'field_name':'Status', 'sequence':str(values.get('sequence') if values.get('sequence') else '')})
        if values.get('state'):
            state_last_updated_by = self.env['res.users'].search([('id', '=', self._uid)])
            values['status_last_updated_by'] = state_last_updated_by.id
            fmt = "%Y-%m-%d %H:%M:%S"
            state_date = datetime.strftime(datetime.now(), fmt)
            values['status_last_updated_on'] = state_date
            state = self.env['rb_delivery.status'].sudo().search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')])
            web_color = state.web_color
            values['web_color'] = web_color
            self.check_if_assign_to_money_collector(values)
            self.check_auto_filled_fields(values,values['state'])
            if values['state'] == 'money_received':
                values['money_received_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if  'assign_to_business' in values and not values['assign_to_business']:
            self.env['rb_delivery.error_log'].raise_olivery_error(225,self.id,{'field_name':'Business', 'sequence': self.sequence})

        if 'customer_location' in values and values['customer_location']:
            expanded_url = self.expand_google_maps_short_url(values['customer_location'])
            coordinates = self.extract_coordinates_from_url(expanded_url)
            if coordinates:
                values['latitude'], values['longitude'] = coordinates

        if 'latitude' in values and 'longitude' in values:
            values['customer_location'] = self._generate_location_link(values['latitude'], values['longitude'])
        if values.get('customer_payment') is not None:
            self.validate_customer_payment(values['customer_payment'])
        if 'reschedule_date' in values and values['reschedule_date']:
            self.check_reschedule_date(values)
            
        if values.get('customer_area') or values.get('customer_sub_area'):
            self.get_zone(values)
        
        self.env['rb_delivery.action'].notify_for_action_type('for_edit',object=self,fields_changed=values.keys(),with_delay=True)

    def check_for_excel_date_field(self,values):
        float_fields,date_fields = self.get_float_date_fields()
        for field in values:
            if field in date_fields and values[field] and str(values[field]).isnumeric() and int(values[field]) > 30000:
                date_val = datetime(1899, 12, 30) + timedelta(days=int(values[field]))
                values[field] = datetime.strftime(date_val,"%Y-%m-%d %H:%M:%S")
            elif field in date_fields and values[field] == '':
                values[field] = False
    
    def check_reschedule_date(self, values):
        allow_reschedule_date_to_be_same_as_today_date = self.env['rb_delivery.client_configuration'].get_param('allow_reschedule_date_to_be_same_as_today_date')
        if isinstance(values['reschedule_date'], (datetime, date)):
            values['reschedule_date'] = values['reschedule_date'].strftime('%Y-%m-%d %H:%M:%S')
        utc=pytz.UTC
        reschedule_date = self.convert_date_to_desired_format(values['reschedule_date']).replace(tzinfo=utc)
        current_datetime = datetime.now().replace(tzinfo=utc)
        rec_id = self[0].id if self else False
        if reschedule_date <= current_datetime :
            if reschedule_date == current_datetime and allow_reschedule_date_to_be_same_as_today_date:
                return
            self.env['rb_delivery.error_log'].raise_olivery_error(223,rec_id,{'current_date':datetime.now().strftime('%Y-%m-%d %H:%M:%S'),'or_equal':_("a less date or equal")if not allow_reschedule_date_to_be_same_as_today_date else _("a less date")})

    
    def check_reschedule_date_from_api(self, reschedule_date):
        utc = pytz.UTC
        if isinstance(reschedule_date, (datetime, date)):
            reschedule_date = reschedule_date.strftime('%Y-%m-%d %H:%M:%S')
        reschedule_datetime = self.convert_date_to_desired_format(reschedule_date)

        if isinstance(reschedule_datetime, str):
            reschedule_datetime = datetime.strptime(reschedule_datetime, '%Y-%m-%d %H:%M:%S')

        user_tz = self.env.user.tz or 'UTC'
        local_tz = pytz.timezone(user_tz)
        
        if reschedule_datetime.tzinfo is None:
            reschedule_datetime = local_tz.localize(reschedule_datetime)

        reschedule_datetime = reschedule_datetime.astimezone(utc)

        return reschedule_datetime.strftime('%Y-%m-%d %H:%M:%S')

    
    def create_signature(self,orders,values):
        if 'signature' in values and values['signature']:
            image_data = values['signature'].split(',')[1] if len(values['signature'].split(','))>1 else False
            for order in orders:
                signature_values={
                    'signature_image': image_data,
                    'status': order.state,
                    'order_id': [(6, 0, [order.id])],
                }
                if self.env.user.rb_user.id:
                    signature_values['user_id']=[(6, 0, [self.env.user.rb_user.id])]
                self.env['rb_delivery.signature'].sudo().create(signature_values)
            del values['signature']

    def update_delivered_by_and_picked_up_by(self, state,values):
        agents_list = []
        confs = self.env['rb_delivery.client_configuration'].get_param(['delivered_by_states','statuses_to_set_value_for_picked_up_by', 'statuses_to_remove_delivered_by'])

        if confs.get('delivered_by_states') or confs.get('statuses_to_set_value_for_picked_up_by'):
            delivered_by_statuses = self.env['rb_delivery.status'].browse(confs.get('delivered_by_states'))
            delivered_by_state_names = delivered_by_statuses.mapped('name')

            picked_up_by_statuses = self.env['rb_delivery.status'].browse(confs.get('statuses_to_set_value_for_picked_up_by'))
            picked_up_by_state_names = picked_up_by_statuses.mapped('name')

            statuses_to_remove_delivered_by_state_names = self.env['rb_delivery.status'].browse(confs.get('statuses_to_remove_delivered_by')).mapped('name')
            if state in statuses_to_remove_delivered_by_state_names:
                values['delivered_by'] = False
                values['delivery_date'] = False
            if state in delivered_by_state_names or state in picked_up_by_state_names:
                if state in delivered_by_state_names:
                    values['delivery_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                if 'assign_to_agent' in values and values['assign_to_agent']:
                    self.assign_delivered_by_and_picked_up_by(self.ids,values['assign_to_agent'],state , delivered_by_state_names , picked_up_by_state_names)

                else:
                    agents_list = self.mapped("assign_to_agent").ids
                    for agent in agents_list:
                        agent_orders = self.filtered(lambda x: x.assign_to_agent.id == agent)
                        agent_order_ids = agent_orders.ids
                        self.assign_delivered_by_and_picked_up_by(agent_order_ids,agent,state , delivered_by_state_names , picked_up_by_state_names)

    def assign_delivered_by_and_picked_up_by(self,order_ids,agent_id,state , delivered_by_state_names , picked_up_by_state_names):
        values_to_update = {}
        if state in delivered_by_state_names:
            values_to_update['delivered_by'] = agent_id

        if state in picked_up_by_state_names:
            values_to_update['picked_up_by'] = agent_id

        if values_to_update:
            self.env['rb_delivery.order'].browse(order_ids).write(values_to_update)

    def check_auto_filled_fields(self,values,state):
        status = self.env['rb_delivery.status'].sudo().search_read([('name','=',state),'|',('status_type','=',False),('status_type','=','olivery_order')],['auto_filled_field_ids'])
        if status and len(status)>0 and status[0] and status[0].get('auto_filled_field_ids'):
            for auto_filled_field_id in status[0].get('auto_filled_field_ids'):
                auto_filled_field = self.env['rb_delivery.auto_filled_fields'].browse(auto_filled_field_id)
                if auto_filled_field.field_id:
                    if auto_filled_field.field_type == 'boolean':
                        values[auto_filled_field.field_id.name] = auto_filled_field.boolean_value
                    elif auto_filled_field.name:
                        values[auto_filled_field.field_id.name] = auto_filled_field.name



    def check_computed_fields(self,values):
        fields_to_remove = []
        for field in values.keys():
            if field in self._fields and self._fields[field].compute:
                if field not in fields_to_remove:
                    fields_to_remove.append(field)
        if len(fields_to_remove)>0:
            for field in fields_to_remove:
                del values[field]
        return values

    def check_create_agent_collection(self,values):
        if 'from_agent_collection' not in values or not values['from_agent_collection']:
            confs = self.env['rb_delivery.client_configuration'].get_param(['create_agent_collection_statuses', 'create_agent_collection_with_employee', 'check_agent_when_create_agent_collection'])
            if confs.get('create_agent_collection_statuses'):
                statuses = self.env['rb_delivery.status'].browse(confs['create_agent_collection_statuses'])
                orders_with_agents = []
                create_agent_collection = False
                if any(status.name == values['state'] for status in statuses):
                    create_agent_collection = True

                if create_agent_collection:
                    if confs.get('check_agent_when_create_agent_collection'):
                        for rec in self:
                            if rec.sudo().assign_to_agent:
                                orders_with_agents.append(rec)
                        
                    else:
                         for rec in self:
                            orders_with_agents.append(rec)
                    if len(orders_with_agents) > 0:
                        self.env['rb_delivery.create_agent_collection'].with_context(append_employee_if_no_agent=True if confs.get('check_agent_when_create_agent_collection') else False, append_employee_if_he_changed_status=True if confs.get('create_agent_collection_with_employee') else False).create_agent_collection(orders_with_agents)
        else:
            del values['from_agent_collection']

    @api.multi
    def write(self, values):
        try:
            if values.get('reference_id'):
                values = self.extract_number_from_url(values)
            self.check_for_excel_date_field(values)
            self.check_inclusive_business(values)
            _logger.info("#################Inside write Before guard function#################")
            if values.get('reject_reason'):
                self.check_reject_reason(values)
            self.guard_function(values)
            if 'state' in values and values['state']:
                self.check_create_agent_collection(values)

                state_id = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                confs = self.env['rb_delivery.client_configuration'].get_param(['statues_to_prevent_change_to_if_not_from_agent_collection','prevent_if_not_from_agent_collection'])
                statues_to_prevent = confs['statues_to_prevent_change_to_if_not_from_agent_collection']
                prevent_action = confs['prevent_if_not_from_agent_collection']
                if state_id.id in statues_to_prevent and not values.get('is_from_collection') and prevent_action:
                    agent_collections = set()
                    recs_with_collection = []
                    recs_without_collection = []
                    for rec in self:
                        if rec.agent_collection_id:
                            recs_with_collection.append(rec)
                            agent_collections.add(rec.agent_collection_id.sequence)
                        else:
                            recs_without_collection.append(rec.sequence)
                    message = ''
                    if agent_collections:
                        message = _('To solve the issue go to the agent collections with the numbers of %s and change its status') % (', '.join(agent_collections))

                    if message and recs_without_collection:
                        message += _('and')

                    if recs_without_collection:
                        message += _('go to orders with numbers %s and create agent collection for them')  % (', '.join(recs_without_collection))
                    self.env['rb_delivery.error_log'].raise_olivery_error(242,', '.join(map(str, self.ids)),{'to_status':state_id.title, 'message': message})


                self.update_delivered_by_and_picked_up_by(values['state'],values)

                self.reset_expired_tracking_ids(values)
            _logger.info("#################Inside write after guard function#################")
            self.update_one_by_one(values)
            _logger.info("#################Inside write function before bulk#################")
            self.write_bulk(values)
            _logger.info("#################Inside write function after bulk#################")
            _logger.info("#################Inside write function before write#################")
            _logger.info("#################Values#################")
            _logger.info(values)

            if values.get('follow_up_orders_map'):
                import json
                json_dict = json.loads(values.get('follow_up_orders_map'))
                for follow_up_order in json_dict:
                    follow_up_order = self.env['rb_delivery.follow_up_order'].create({'order_id':self.id,'follow_up_sequence':follow_up_order['sequence'], 'name':follow_up_order['name']})
                    self.write({'follow_up_order': [(4, follow_up_order.id)]})
                self.write({'follow_up_orders_map': False})
                values.pop('follow_up_orders_map')

            # Track changes for all fields in values


            # Prevent mail tracking
            context = dict(self.env.context, mail_notrack=True)
            if 'write_compute_fields' in self._context and self._context.get('write_compute_fields'):
                pass
            else:
                values = self.check_computed_fields(values)
            # Prepare the changes
            tracked_fields = self.get_tracked_fields()
            old_values= self.read(tracked_fields)
            # Call the super method to perform the actual write
            old_related_collections_fields_values = self.check_related_collection_fields_values()
            
            order = super(rb_delivery_order, self.with_context(context)).write(values)

            new_related_collections_fields_values = self.check_related_collection_fields_values()

            if old_related_collections_fields_values and new_related_collections_fields_values:
                differences = self.get_differences_in_related_collection_fields(old_related_collections_fields_values, new_related_collections_fields_values)
                if differences:
                    self.check_reflected_values_to_collection(differences)

            changes = self._prepare_custom_changes(old_values)

            # Log the changes
            self._log_custom_changes(changes)

            _logger.info("################# Inside write function after write #################")
            self.apply_post_write_actions(values)

            if 'state' in values and values['state']:
                self.check_auto_creating_runsheet_collection(values['state'])
                _logger.info("#################Inside write Before do action update#################")
                self.do_action_update(values)
                _logger.info("#################Inside write after do action update#################")

            if 'active' in values and not values['active']:
                self._archive_orders()

            return order
        except AccessError as e:
            _logger.error("AccessError: %s", e)
            error_message = str(e)
            model_match = re.search(r"Document type: (.*?),", error_message)
            user_match = re.search(r"User: (\d+)", error_message)
            user_value = user_match.group(1) if user_match else "Unknown"
            
            if not model_match:
                model_match = re.search(r"Document model: (\w+\.\w+)", error_message)
                model_name = model_match.group(1) if model_match else "Unknown"
                self.env['rb_delivery.error_log'].raise_olivery_error(806,self[0].id,{'user_id':user_value,'model_name':model_name})
            
            records_match = re.search(r"Records: \[(.*?)\]", error_message)
            model_name = model_match.group(1) if model_match else "Unknown"
            record_ids = records_match.group(1) if records_match else "Unknown"
            self.env['rb_delivery.error_log'].raise_olivery_error(805,self[0].id,{'user_id':user_value,'record_ids':record_ids,'model_name':model_name}) 

    @api.model
    def get_tracked_fields(self):
        return [field for field in self._fields if getattr(self._fields[field], 'track_visibility', False)]

    def _archive_orders(self):
        archive_vals = []
        for record in self:
            archive_vals.append({
                'archived_date': fields.Datetime.now(),
                'sender_name': record.assign_to_business.username,
                'receiver_name': record.customer_name,
                'sender_mobile': record.business_mobile_number,
                'receiver_area': record.customer_area.name,
                'receiver_sub_area': record.customer_sub_area.name,
                'receiver_full_address': record.customer_address,
                'receiver_mobile_number': record.customer_mobile,
            })
        if len(archive_vals)>0:
            self.env['rb_delivery.archive_order'].sudo().create(archive_vals)

    def check_related_collection_fields_values(self):
        financial_fields_values = {}
        fields_info = self.fields_get(self.RELATED_COLLECTIONS_FIELDS)

        for rec in self:
            rec_values = {}
            for field in self.RELATED_COLLECTIONS_FIELDS:
                if fields_info[field]['type'] == 'many2one':
                    rec_values[field] = rec[field].id
                else:
                    rec_values[field] = rec[field]
            financial_fields_values[rec.id] = rec_values

        return financial_fields_values

    def get_differences_in_related_collection_fields(self, old_values, new_values):
        differences = {}
        for record_id in old_values:
            old_record_values = old_values[record_id]
            new_record_values = new_values[record_id]
            record_differences = {
                field:  new_record_values[field] for field in old_record_values if old_record_values[field] != new_record_values[field]
            }
            if record_differences:
                differences[record_id] = record_differences
        return differences

    def _prepare_custom_changes(self,old_values, is_create=False):
        custom_changes = []
        i=0
        for rec in self:
            change_logs = []
            changes = {}
            tracked_fields = self.get_tracked_fields()
            
            values = rec.read(tracked_fields)[0]
            if is_create and 'state' not in values:
                values['state'] = rec.state
            if 'state_id' not in values and 'state' in values:
                values['state_id'] = self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1).id
            for field in values.keys():
                if field == 'Don\'t import' or field == 'لا تستورد':
                    continue
                if field in tracked_fields and (is_create or old_values[i][field] != values[field] ):
                    old_value = old_values[i][field] if not is_create else False
                    new_value = values[field] if not is_create else rec[field]
                    old_value_id = False
                    new_value_id = False
                    if rec._fields[field].type == 'many2one':
                        old_value_ref = self.env[rec._fields[field].comodel_name].sudo().browse(old_value[0]) if old_value else False
                        new_value_ref = self.env[rec._fields[field].comodel_name].sudo().browse(new_value[0]) if not is_create and new_value else new_value if new_value else False
                        old_value = old_value_ref.sudo().display_name if old_value_ref else False
                        new_value = new_value_ref.sudo().display_name if new_value_ref else False
                        old_value_id = old_value_ref.id if old_value_ref else False
                        new_value_id = new_value_ref.id if new_value_ref else False
                    elif rec._fields[field].type == 'one2many' or rec._fields[field].type=='many2many':
                        continue
                    elif rec._fields[field].type == 'selection':
                        selection_dict = dict(rec._fields[field].selection) if not isinstance(rec._fields[field].selection,str) else dict(getattr(rb_delivery_order,rec._fields[field].selection)(rec))
                        old_value = selection_dict.get(old_value, 'None')
                        new_value = selection_dict.get(new_value, 'None')
                    elif rec._fields[field].type == 'boolean':
                        old_value = '✔' if old_value else '✘'
                        new_value = '✔' if new_value else '✘'
                    elif rec._fields[field].type in ['integer', 'float'] and old_value in [False, None] and new_value == 0.0:
                        continue
                    if str(old_value) != str(new_value):
                        old_value = '✘' if (not old_value or old_value=='None') and rec._fields[field].type not in ['integer','float'] else old_value
                        new_value = '✘' if (not new_value or new_value=='None') and rec._fields[field].type not in ['integer','float'] else new_value
                        changes[field] = (str(old_value),str(new_value),old_value_id,new_value_id)
            i+=1
            change_body=''
            log_fields = self.env['rb_delivery.client_configuration'].get_param('fields_to_log_in_order_logs')
            log_fields = self.env['ir.model.fields'].sudo().browse(log_fields).mapped('name')
            for field, (old_value, new_value, old_value_id, new_value_id) in changes.items():
                field_record = self.env['ir.model.fields'].search([['name', '=', rec._fields[field].name], ['model', '=', 'rb_delivery.order']])
                translated_field_name = field_record.field_description if field_record else _(rec._fields[field].string)
                if field in log_fields:
                    change_logs.append({
                        'order_status':rec.state,
                        'old_value':old_value,
                        'new_value':new_value,
                        'old_value_id':old_value_id,
                        'new_value_id':new_value_id,
                        'order_id':rec.id,
                        'field_id':self.env['ir.model.fields'].search([['name','=',rec._fields[field].name],['model', '=', 'rb_delivery.order']]).id
                    })
                if new_value == '✘':
                    change_body+='<br/>'+_('%s %s has been removed')%(translated_field_name,'<b>'+old_value+'</b>')
                elif old_value in [False,'✘','False', 'None']:
                    change_body+='<br/>'+_('%s has been set to %s')%('<b>'+translated_field_name+'</b>','<b>'+new_value+'</b>')
                else:
                    change_body+='<br/>'+_('Field %s changed from %s to %s')%('<b>'+translated_field_name+'</b>','<b>'+old_value+'</b>','<b>'+new_value+'</b>')
            if is_create:
                if self._context.get('is_public'):
                    change_body+='<b> ('+_('Created By Public User')+')</b>'
                if self._context.get('import_file'):
                    change_body+='<b>'+_("Imported by excel.")+'</b>'

            if len(changes.items()):

                custom_changes.append({
                    'values':{
                        'model': rec._name,
                        'res_id': rec.id,
                        'body': change_body,
                        'message_type': 'notification',
                        'subtype_id': rec.env.ref('mail.mt_note').id,  # Or another suitable subtype
                        'author_id': rec.env.user.partner_id.id,
                    }
                    ,
                    'change_logs':change_logs
                })
        return custom_changes


    def _log_custom_changes(self,changes):
        if self._context.get('original_uid') and self._context.get('original_uid') != 2:
            user_id = self.env['res.users'].sudo().search([('partner_id', '=', self._context.get('original_uid'))], limit=1)
            self=self.sudo(user_id.id)
        uid = self.env.user.partner_id.id
        change_logs=[]
        for change in changes:
            if change['values']:
                if self._context.get('automated'):
                    change['values']['body']+='<b> ('+_('Automated')+')</b>'
                    if self._context.get('automation_message'):
                        change['values']['body']+='<br/>'+self._context.get('automation_message')

                self.env['mail.message'].with_context(original_uid=uid).sudo().create(change['values'])
            if len(change['change_logs']):
                change_logs.append({
                    'change_logs':change['change_logs']
                })
        if len(change_logs):
            self.create_change_logs(change_logs,self._context)

    def create_change_logs(self,change_logs,context):
        context = dict(context)
        if not context.get('original_uid'):
            uid = self.env.user.partner_id.id
            context['original_uid']=uid
        context['lang']=self.env.user.partner_id.lang
        for log in change_logs:
            self.env['rb_delivery.order_logs'].with_context(**context).create(log['change_logs'])

    @api.model
    def create_public_order(self,values):
        if 'customer_mobile' in values:
            public_orders_limit_in_general_per_day = self.env['rb_delivery.client_configuration'].sudo().get_param('public_orders_limit_in_general_per_day')
            statuses_for_public_orders_limit = self.env['rb_delivery.client_configuration'].sudo().get_param('statuses_for_public_orders_limit')
            existing_orders_count = self.env['rb_delivery.order'].sudo().search_count([
                ['state_id','in',statuses_for_public_orders_limit],
                ['create_date','>',datetime.now() - timedelta(days=1)],
                ['is_public','=',True]
            ])
            if existing_orders_count >= int(public_orders_limit_in_general_per_day):
                self.env['rb_delivery.error_log'].raise_olivery_error(1501,0, {})
            else:
                public_orders_limit_for_same_mobile_per_day = self.env['rb_delivery.client_configuration'].sudo().get_param('public_orders_limit_for_same_mobile_per_day')
                existing_orders_with_same_number_count = self.env['rb_delivery.order'].sudo().search_count([
                    ['customer_mobile','=',values['customer_mobile']],
                    ['state_id','in',statuses_for_public_orders_limit],
                    ['create_date','>',datetime.now() - timedelta(days=1)],
                    ['is_public','=',True]
                ])
                if existing_orders_with_same_number_count >= int(public_orders_limit_for_same_mobile_per_day):
                    self.env['rb_delivery.error_log'].raise_olivery_error(1500,0, {'mobile_number':values['customer_mobile']})
        values['is_public'] = True
        return self.env['rb_delivery.order'].sudo().with_context(is_public=True).create(values).read(['sequence'])


    @api.model
    def get_status_history(self,order_id):
        order_log_dic = request.env['rb_delivery.order_logs'].sudo().search_read([('order_id','=',order_id),'|','|','|',('field_id.name','=','state_id'),('field_id.name','=','stuck_comment'),('field_id.name','=','solve_stuck_comment'),('is_message','=',True)],fields=['create_uid','old_value','new_value','create_date','is_message'],order="create_date desc")
        return order_log_dic



    def check_notification_actions(self,values):
        if values.get('state'):
            for rec in self:
                self.env['rb_delivery.action'].notify_for_action_type('for_status',state_name=values['state'],object=rec,with_delay=True)

    def check_if_assign_to_money_collector(self,values):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('assign_money_collector')
        status_names = []
        if status_ids:
            for status_id in status_ids:
                status = self.env['rb_delivery.status'].search([('id','=',status_id)])
                if status and status.name:
                    status_names.append(status.name)
        if values['state'] in status_names:
            values['money_collector'] = self._uid

    @api.one
    def unlink(self):
        role_code = 'Adminstrator'
        if not self._uid == 1 and not self._uid == 2:
            role_code = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).role_code
        self.env['rb_delivery.error_log'].raise_olivery_error(226,self.id, {'sequence':self.sequence,'user_role': role_code})
        return super(rb_delivery_order, self).unlink()

    @api.model
    def load_views(self, views, options=None):
        res =  super(rb_delivery_order, self).load_views(views, options)
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            res['fields']['delivery_cost']['exportable'] = False
            res['fields']['delivery_cost']['selectable'] = False
            res['fields']['delivery_cost']['searchable'] = False
            res['fields']['delivery_cost']['sortable'] = False
        return res

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        '''
            override search_read to customize sorting depends on user input if which contains 'sequence'
            query.
        '''
        if order and 'id' not in order:
            if 'create_date ASC' in order :
                order = order.replace('create_date ASC', 'create_date ASC, id ASC')

            elif 'create_date DESC' in order :
                order = order.replace('create_date DESC', 'create_date DESC, id DESC')

        users_domain = self.get_domain_for_users_page()
        if users_domain:
            if 'group_by' in self._context:
                pass
            else:
                domain = domain + users_domain
        else:
            for dom in domain:
                if dom[0] == 'assign_to_business' or dom[0] == 'commercial_name':
                    if not isinstance(dom[2],list) and ',' in str(dom[2]):
                        temp_str = dom[2].split(',')
                        dom[2] = temp_str[1].strip()

            domain = self.extract_number_from_url_dom(domain)

        records = self.search(domain or [], offset=offset, limit=limit, order=order)
        if not records:
            return []
        #filter - domain contains sequence e.x ['sequance', 'ilike', '035']
        sequanced_domain = [domain_item for domain_item in domain \
                                            if len(domain_item) == 3 and domain_item[1] in ['ilike','='] and domain_item[0] == 'sequence']
        fields.append('sequence')
        orders = records.with_context(self._context).read(fields)
        ordered_orders = []
        for domain in sequanced_domain:
            for ind in range(len(orders)):
                #find it and put it at the beginning
                if domain[2] in orders[ind]["sequence"]:
                    ordered_orders.insert(0,orders[ind])
                    del orders[ind]
                    break
        ordered_orders = ordered_orders + orders #concat
        return ordered_orders

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        created_barcodes = []
        args = self.extract_number_from_url_dom(args)
        for dom in args:
            if (dom[0] == 'sequence' or dom[0] == 'reference_id') and self._context.get('scan_from'):
                uid = self._context.get('scanned_by') if self._context.get('scanned_by') else self._uid
                message = self._context.get('scan_from')
                barcodes = []
                if isinstance(dom[2],list):
                    barcodes = dom[2]
                else:
                    barcodes = dom[2].strip().split(',')
                barcodes_rec = []
                for barcode in barcodes:
                    if barcode in created_barcodes:
                        continue
                    created_barcodes.append(barcode)
                    barcodes_rec.append({
                        'scanned_by': uid,
                        'scanned_value': barcode,
                        'scanned_in': message,
                        'scanned_type': dom[0]
                    })
                self.env['rb_delivery.scan_logs'].create(barcodes_rec)
        return super(rb_delivery_order, self).search(args, offset, limit, order, count)


    # ----------------------------------------------------------------------
    # Reports
    # ----------------------------------------------------------------------
    # order money report (collection report)
    def get_collection_docs(self,docs):
        new_doc = []
        new_docs = []
        business_list = []
        for doc in docs:
            if doc.sudo().assign_to_business.id not in business_list:
                business_list.append(doc.sudo().assign_to_business.id)
        for business in business_list:
            for doc in docs:
                if doc.sudo().assign_to_business.id == business:
                    new_doc.append(doc)
            new_docs.append(new_doc)
            new_doc= []
        return new_docs

    def get_agent_collection_docs(self,docs):
        new_doc = []
        new_docs = []
        default_driver_id = docs[0].assign_to_agent.id
        for doc in docs:
            if doc.assign_to_agent.id == default_driver_id:
                new_doc.append(doc)
            else:
                default_driver_id = doc.assign_to_agent.id
                new_docs.append(new_doc)
                new_doc= []
                new_doc.append(doc)
        new_docs.append(new_doc)
        return new_docs




    def get_doc_ids(self, docs, assignment_type):
        doc_ids = [int(doc_id) for doc_id in docs.split(',')]
        orders = self.env['rb_delivery.order'].browse(doc_ids)
        assignment_to_docs = {}

        for order in orders:
            if assignment_type == 'business':
                assignment_id = order.sudo().assign_to_business.id
            elif assignment_type == 'agent':
                assignment_id = order.sudo().assign_to_agent.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(114,self.id,{'assignment_id':assignment_type})
                return

            if assignment_id not in assignment_to_docs:
                assignment_to_docs[assignment_id] = []

            assignment_to_docs[assignment_id].append(str(order.id))

        return [','.join(doc_ids) for doc_ids in assignment_to_docs.values()]

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    # inherit module[olivery_branch_collection]
    def get_clone_replacement_values(self,order):
        prefix_clone_sequence = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix')
        postfix_clone_sequence = self.env['rb_delivery.client_configuration'].get_param('clone_order_postfix')
        reference_id_prefix = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix_reference_id')
        scanned_reference_id = False
        if self._context.get('scanned_reference_id'):
            scanned_reference_id = self._context.get('scanned_reference_id')

        new_sequence = order.sequence
        if prefix_clone_sequence:
            new_sequence = prefix_clone_sequence + new_sequence
        if postfix_clone_sequence:
            new_sequence = new_sequence + postfix_clone_sequence
        values = {
                'assign_to_business': order.sudo().assign_to_business.id,
                'cost':0.00,
                'copy_total_cost':0.00,
                'customer_name':order.customer_name,
                'customer_address':order.customer_address,
                'customer_mobile':order.customer_mobile,
                'customer_area':order.customer_area.name,
                'second_mobile_number':order.second_mobile_number,
                'clone_reference':order.id,
                'state':'replacement',
                'discount':order.delivery_cost,
                'note': order.note,
                'is_replacement':True,
                'is_cloned':True,
                'follower_address':order.follower_address,
                'follower_ref_id':order.follower_ref_id,
                'follower_area':order.follower_area,
                'follower_store_name':order.follower_store_name,
                'follower_mobile_number':order.follower_mobile_number,
                'follower_second_mobile_number':order.follower_second_mobile_number,
                'follower_longitude':order.follower_longitude,
                'follower_latitude':order.follower_latitude,
                
                }

        statuses_dont_allowed_to_edit =self.env['rb_delivery.client_configuration'].get_param('prevent_update_agent_on_cloned_order')
        edit_cloned_order_agent = True
        if statuses_dont_allowed_to_edit:
            status_id=self.env.ref('rb_delivery.status_replacement').id
            edit_cloned_order_agent=status_id not in statuses_dont_allowed_to_edit
        if edit_cloned_order_agent==True:
            values['assign_to_agent']=order.assign_to_agent.id

        if reference_id_prefix and order.reference_id:
            values['reference_id'] = str(reference_id_prefix) + str(order.reference_id)
        if new_sequence != order.sequence:
            values['sequence'] = new_sequence

        if scanned_reference_id:
            values['reference_id'] = scanned_reference_id
        return values
    
    def check_clone_by_order_type(self,order,values=False):
        if order.order_type_id and order.order_type_id.clone_status and order.order_type_id.clone_status.name != values['state'] or order.replacement_order or order.returned_order or not order.order_type_id.clone_status:
            return
        if values.get('scanned_reference_id'):
            values = self.with_context(scanned_reference_id=values['scanned_reference_id']).get_clone_by_order_type_values(order)
        else:
            values = self.get_clone_by_order_type_values(order)
        uid = self.env.user.partner_id.id
        clone_order = self.env['rb_delivery.order'].with_context(original_uid=uid,is_clone_by_order_type=True).sudo().create(values)
        username = self.env.user.name
        message = _("Clone order has been created by %s automatically through the system based on order type cloning, for further details you can check with support.")%(username)
        clone_order.sudo().with_context(original_uid=uid).message_post(body=message)
        return clone_order


    @api.model
    def create_clone_order(self,order,values=False):
        create_replacement_order_status_ids = self.env['rb_delivery.client_configuration'].get_param('replacement_order_status')
        create_replacement_order_statuses = self.env['rb_delivery.status'].browse(create_replacement_order_status_ids)
        create_replacement_order_status_names = [state.name for state in create_replacement_order_statuses]
        if (
            create_replacement_order_status_ids
            and (
                (not values and order.state_id.id not in create_replacement_order_status_ids)
                or (values and values.get('state') and values.get('state') not in create_replacement_order_status_names)
                or (values and not values.get('state') and order.state not in create_replacement_order_status_names)
                )
        ):
            if self._context.get('create_replacement_action'):
                self.env['rb_delivery.error_log'].raise_olivery_error(
                    804,
                    order.id,
                    {
                        'order_sequences': ', '.join([str(order.sequence)]),
                        'status': order.state
                    }
                )
            else:
                return

        if order.replacement_order:
            replaced_order = self.env['rb_delivery.order'].search([('clone_reference', '=', order.id),'|',('active','=',True),('active','=',False)])
            if replaced_order:
                if not replaced_order.active:
                    self.env['rb_delivery.error_log'].raise_olivery_error(227,order.id,{'sequence':order.sequence,'replacement_sequence':replaced_order.sequence,'returned_type':_('replacement')})
                self.env['rb_delivery.error_log'].raise_olivery_error(228,order.id,{'sequence':order.sequence,'replacement_sequence':replaced_order.sequence,'returned_type':_('replacement')})
        if order.returned_order:
            returned_order = self.env['rb_delivery.order'].search([('returned_clone_reference', '=', order.id),'|',('active','=',True),('active','=',False)])
            if returned_order:
                if not returned_order.active:
                    self.env['rb_delivery.error_log'].raise_olivery_error(227,order.id,{'sequence':order.sequence,'replacement_sequence':returned_order.sequence,'returned_type':_('returned')})
        if values and values.get('scanned_reference_id'):
            values = self.with_context(scanned_reference_id=values['scanned_reference_id']).get_clone_replacement_values(order)
        else:
            values = self.get_clone_replacement_values(order)
        uid = self.env.user.partner_id.id
        clone_order = self.env['rb_delivery.order'].with_context(original_uid=uid).sudo().create(values)
        username = self.env.user.name
        message = _("Clone order has been created by %s automatically through the system based on the configuration of the cloning, for further details you can check with support.")%(username)
        data = {'uid':self._uid,'message':message,'records':clone_order,'values':{},'update':False}
        self.env['rb_delivery.utility'].olivery_sudo(data)

    # inherit module[olivery_branch_collection]
    def get_clone_returned_values(self,order):
        reference_id_prefix = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix_reference_id')
        business = order.sudo().assign_to_business
        values = {
                'assign_to_business': business.id,
                'cost':0.00,
                'copy_total_cost':0.00,
                'customer_name':business.username,
                'customer_address':business.address,
                'customer_mobile':business.mobile_number,
                'customer_area':business.area_id.name,
                'second_mobile_number':business.second_mobile_number,
                'returned_clone_reference':order.id,
                'state':'branch_returned',
                'discount':order.delivery_cost,
                'is_returned':True,
                'is_cloned':True,
                'follower_address':order.follower_address,
                'follower_ref_id':order.follower_ref_id,
                'follower_area':order.follower_area,
                'follower_store_name':order.follower_store_name,
                'follower_mobile_number':order.follower_mobile_number,
                'follower_second_mobile_number':order.follower_second_mobile_number,
                'follower_longitude':order.follower_longitude,
                'follower_latitude':order.follower_latitude,
                
                
                
                }

        if reference_id_prefix and order.reference_id:
            values['reference_id'] = str(reference_id_prefix) + str(order.reference_id)
        return values
    
    @api.model
    def extract_number_from_url(self,values):
        extract_regex_from_scanner = self.env['rb_delivery.client_configuration'].get_param('extract_regex_from_scanner')
        if extract_regex_from_scanner and values.get('reference_id'):
            url = values['reference_id']
            match = re.search(extract_regex_from_scanner, url)
            if match:
                values['reference_id'] = match.group(1)

        return values

    @api.model
    def extract_number_from_url_dom(self, args):
        extract_regex_from_scanner = self.env['rb_delivery.client_configuration'].get_param('extract_regex_from_scanner')
        for idx, dom in enumerate(args):
            if extract_regex_from_scanner and dom[0] == 'reference_id':
                url = dom[2]
                match = re.search(extract_regex_from_scanner, url)
                if match:
                    new_value = match.group(1)
                    args[idx] = (dom[0], dom[1], new_value)
        return args
                    
    def get_clone_by_order_type_values(self, order):
        fields = [
            'assign_to_business', 'customer_name', 'customer_address', 
            'customer_mobile', 'customer_area', 'second_mobile_number', 
            'id', 'order_type_id', 'delivery_cost', 'reference_id' , 'sequence'
        ]
        order_data = order.read(fields)[0]
        context = self._context
        scanned_reference_id = context.get('scanned_reference_id', order_data['sequence'])
        clone_configurations = self.env['rb_delivery.client_configuration'].get_param(['clone_order_prefix','clone_order_postfix'])
        prefix_clone_sequence = clone_configurations['clone_order_prefix']
        postfix_clone_sequence = clone_configurations['clone_order_postfix']
        new_sequence = order.sequence
        if prefix_clone_sequence:
            new_sequence = prefix_clone_sequence + new_sequence
        if postfix_clone_sequence:
            new_sequence = new_sequence + postfix_clone_sequence
        values = {
            'assign_to_business': order_data['assign_to_business'][0] if order_data['assign_to_business'] else False,
            'cost': 0.00,
            'copy_total_cost': 0.00,
            'customer_name': order_data['customer_name'],
            'customer_address': order_data['customer_address'],
            'customer_mobile': order_data['customer_mobile'],
            'customer_area': order_data['customer_area'][0] if order_data['customer_area'] else False,
            'second_mobile_number': order_data['second_mobile_number'],
            'cloner_order_id':order_data['id'],
            'order_type_id': self.env.ref('rb_delivery.normal_order').id,
            'state': order.order_type_id.cloned_order_status.name,
            'discount': order_data['delivery_cost'],
            'is_returned': True,
            'is_cloned': True,
            'follower_address':order_data['follower_address'],
            'follower_ref_id':order_data['follower_ref_id'],
            'follower_area':order_data['follower_area'],
            'follower_store_name':order_data['follower_store_name'],
            'follower_mobile_number':order_data['follower_mobile_number'],
            'follower_second_mobile_number':order_data['follower_second_mobile_number'],
            'follower_longitude':order_data['follower_longitude'],
            'follower_latitude':order_data['follower_latitude'],

        }
        if new_sequence != order.sequence:
            values['sequence'] = new_sequence

        if scanned_reference_id:
            values['reference_id'] = scanned_reference_id

        return values

    @api.model
    def create_returned_clone_order(self,order):
        if order.replacement_order:
            replaced_order = self.env['rb_delivery.order'].search([('clone_reference', '=', order.id),'|',('active','=',True),('active','=',False)])
            self.env['rb_delivery.error_log'].raise_olivery_error(244,self.id,{'order': order.sequence, 'returned_waybill': replaced_order.sequence})
            #raise Warning(_("Order of sequence %s has replacement waybill with sequence %s"% (order.sequence,replaced_order.sequence)))
        if order.returned_order:
            returned_order = self.env['rb_delivery.order'].search([('returned_clone_reference', '=', order.id),'|',('active','=',True),('active','=',False)])
            self.env['rb_delivery.error_log'].raise_olivery_error(244,self.id,{'order': order.sequence, 'returned_waybill': returned_order.sequence})
            #raise Warning(_("Order of sequence %s has returned waybill with sequence %s"% (order.sequence,returned_order.sequence)))
        values = self.get_clone_returned_values(order)
        uid = self.env.user.partner_id.id
        clone_order = self.env['rb_delivery.order'].with_context(original_uid=uid).sudo().create(values)
        if order.assign_to_agent:
            clone_order.write({'assign_to_agent':order.assign_to_agent.id})

    @api.model
    def create_partial_clone_order(self,order):
        create_partial_mirrored = self.env['rb_delivery.client_configuration'].get_param('create_clone_partial')
        if create_partial_mirrored:
            reference_id_prefix = self.env['rb_delivery.client_configuration'].get_param('clone_order_prefix_reference_id')
            values = {
                    'assign_to_business': order.sudo().assign_to_business.id,
                    'cost':0.00,
                    'copy_total_cost':0.00,
                    'customer_name':order.customer_name,
                    'customer_address':order.customer_address,
                    'customer_mobile':order.customer_mobile,
                    'customer_area':order.customer_area.name,
                    'second_mobile_number':order.second_mobile_number,
                    'state':'rejected_partial',
                    'partial_clone_reference':order.id,
                    'discount':order.delivery_cost}

            if reference_id_prefix and order.reference_id:
                values['reference_id'] = str(reference_id_prefix) + str(order.reference_id)
            clone_order = self.env['rb_delivery.order'].create(values)
            if order.assign_to_agent:
                clone_order.write({'assign_to_agent':order.assign_to_agent.id})



    def get_notification(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_notification_center').id
        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Notifications',
            'res_model': 'rb_delivery.notification_center',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def get_location(self):
        lon = self.longitude
        lat = self.latitude

        if lon and lat:
            return {
                'name'     : 'Go to website',
                'res_model': 'ir.actions.act_url',
                'type'     : 'ir.actions.act_url',
                'target'   : '_blank',
                'url'      : "https://www.google.com/maps/search/"+lat+","+lon
            }
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(229,self.id,{'order_sequence':self.sequence, 'order_customer_name': self.customer_name})

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            image_output_sequence = io.BytesIO()
            image_output_reference = io.BytesIO()
            # by default take sequence , if reference_id exist then choose reference id
            if self.ref_priority and self.seq_priority and self.reference_id and self.sequence:
                barcode_number_seq = self.sequence
                barcode_number_reference = self.reference_id
            elif self.ref_priority and self.reference_id:
                barcode_number_reference=self.reference_id
            elif self.seq_priority and self.sequence:
                barcode_number_seq=self.sequence

            if self.seq_priority and self.sequence and barcode_number_seq:
                try:
                    ean = EAN(barcode_number_seq, writer=ImageWriter(), add_checksum=False)
                    ean.write(image_output_sequence)
                    encoded_seq = base64.b64encode(image_output_sequence.getvalue())
                    self.barcode = encoded_seq
                except Exception as e:
                    self.env['rb_delivery.error_log'].raise_olivery_error(213,self.id,{'reference_issue':_(str(_(e))),'field_name':_('Sequence')})
            if self.ref_priority and self.reference_id and barcode_number_reference:
                try:
                    ean = EAN(barcode_number_reference, writer=ImageWriter(), add_checksum=False)
                    ean.write(image_output_reference)
                    encoded_ref = base64.b64encode(image_output_reference.getvalue())
                    self.barcode_reference = encoded_ref
                except Exception as e:
                    self.env['rb_delivery.error_log'].raise_olivery_error(213,self.id,{'reference_issue':_(str(_(e))),'field_name':_('Reference ID')})

    @api.one
    @api.depends('partner_reference_id')
    def create_partner_ref_id_barcode(self):
        if (self.partner_reference_id):
            barcode.base.Barcode.default_writer_options['write_text'] = False

            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.partner_reference_id, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.partner_reference_id_barcode = encoded

    @api.multi
    @api.depends('partner_reference_id')
    def create_partner_ref_id_qr_code(self):
        for rec in self:
            if (rec.partner_reference_id):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.partner_reference_id)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.partner_reference_id_qr_code = qr_image

    @api.multi
    @api.depends('reference_id')
    def create_reference_qr_code(self):
        for rec in self:
            if (rec.reference_id):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.reference_id)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_reference = qr_image

    def get_signature(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_signature').id
        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.signature',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def get_order_attachment(self):
        tree_view_id = self.env.ref('rb_delivery.view_tree_delivery_order_attachment').id
        kanban_view_id = self.env.ref('rb_delivery.view_kanban_delivery_order_attachment').id
        form_view_id = self.env.ref('rb_delivery.view_form_delivery_order_attachment').id

        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': '%s - Attachments' % self.name,
            'res_model': 'rb_delivery.order_attachment',
            'view_type': 'form',
            'view_mode': 'tree,kanban,form',
            'views': [(tree_view_id, 'tree'), (kanban_view_id, 'kanban'), (form_view_id, 'form')],
            'target': 'current',
            'domain': domain,
            'context': {'default_order_id': self.id}
        }

    def get_logs(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order_logs').id
        domain = [('order_id','=',self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order_logs',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree')],
            'target': 'current',
            'domain': domain}

    def get_kashf_tahseel(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_multi_print_orders_money_collector').id
        domain = [('order_ids', 'in', self.id),'|',('active','=',True),('active','=',False)]
        context = {
            'model':'rb_delivery.multi_print_orders_money_collector',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.multi_print_orders_money_collector',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'context': context,
            'domain': domain}

    def get_replacement_order(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        if self.replacement_order:
            domain = [('clone_reference', '=', self.id)]
        else:
            domain = [('id', '=', self.clone_reference.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def get_returned_order(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        if self.returned_order:
            domain = [('returned_clone_reference', '=', self.id)]
        else:
            domain = [('id','=',self.returned_clone_reference.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def get_partial_order(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        domain = [('partial_clone_reference', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    def get_run_sheet(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_runsheet').id
        domain = [('order_ids', 'in', self.id)]
        context = {
            'model':'rb_delivery.runsheet',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.runsheet',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'context':context,
            'target': 'current',
            'domain': domain}

    def get_agent_returned_collection(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_agent_returned_collection').id
        domain = [('order_ids', 'in', self.id)]
        context = {
            'model':'rb_delivery.agent_returned_collection',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.agent_returned_collection',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'context':context,
            'target': 'current',
            'domain': domain}

    def get_agent_collection(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_agent_money_collection').id
        domain = [('order_ids', 'in', self.id)]
        context = {
            'model':'rb_delivery.agent_money_collection',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.agent_money_collection',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'context':context,
            'target': 'current',
            'domain': domain}

    def get_returned_collection(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_returned_money_collection').id
        domain = [('order_ids', 'in', self.id),'|',('active','=',True),('active','=',False)]
        context = {
            'model':'rb_delivery.returned_money_collection',
            'active_ids':[]
        }
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.returned_money_collection',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'context':context,
            'domain': domain}

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_order_select_state').id
        status_list=self.get_status()
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State and Agent',
            'res_model': 'rb_delivery.select_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    def change_in_branch_if_not_business_on_create(self,values):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if not user.has_group('rb_delivery.role_business'):
            values['state']='in_branch'
            self.do_action_create(values)


    def do_action_create(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_on_create_ids
        for action in status_actions:
            try:
                method_to_call=getattr(rb_delivery_order,action.name)
                method_to_call(self,values)
            except:
                pass

    def do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            if 'prevent' in action.name:
                try:
                    method_to_call=getattr(rb_delivery_order,action.name)
                    method_to_call(self,next_state)
                except Exception as e:
                    if action.name=='prevent_change_status_if_not_in_agent_collection' or action.name == 'prevent_change_status_if_not_in_collection':
                        displayed_messages = re.sub(r'\[.*?\]\{\[\[\(\d+\) .*?\]\]\}', '', str(e))
                        self.env['rb_delivery.error_log'].raise_olivery_error(230,self.id,{'action_name':action.name,'action_issue':displayed_messages})
                    else:
                        pass

    def do_action_update(self,values):
        status_actions=self.env['rb_delivery.status'].search([('name','=',values['state']),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_action_ids
        for action in status_actions:
            if 'prevent' not in action.name:
                try:
                    method_to_call=getattr(rb_delivery_order,action.name)
                    method_to_call(self,values)
                except:
                    pass

    #inherit module [olivery_osc]

    def check_if_in_collection(self,values):
        if self.sudo().collection_id and 'is_from_collection' not in values:
            if not self.sudo().collection_id.is_pre_paid_collection:
                 self.env['rb_delivery.error_log'].raise_olivery_error(231,self.id,{'collection_type':_('collection'),'collection_sequence':self.sudo().collection_id.sequence,'order_sequence':self.sequence})
            else:
                allowed_prepaid_changing_state_id = self.env['rb_delivery.client_configuration'].get_param('allowed_states_to_change_pre_paid_order_status')
                allowed_prepaid_changing_state_name = self.env['rb_delivery.status'].search([('id','in',allowed_prepaid_changing_state_id),'|',('status_type','=',False),('status_type','=','olivery_order')]).mapped('name')
                if values['state'] not in allowed_prepaid_changing_state_name and self.sudo().collection_id.state != values['state']: #TODO: change this to olivery
                    allowed_prepaid_changing_state_name = ', '.join(allowed_prepaid_changing_state_name)
                    self.env['rb_delivery.error_log'].raise_olivery_error(243,self.id,{'allowed_status':allowed_prepaid_changing_state_name})
                    #raise ValidationError(_('You are not allowed to change the state of orders within a prepaid collection unless the desired state for the change in %s')%(allowed_prepaid_changing_state_name))
        if (self.returned_collection_id) and 'is_from_collection' not in values:
            self.env['rb_delivery.error_log'].raise_olivery_error(231,self.id,{'collection_type':_('returned collection'),'collection_sequence':self.returned_collection_id.sequence,'order_sequence':self.sequence})

    def check_reflected_values_to_collection(self, values):
        for rec in self:
            order_values = values[rec.id] if values.get(rec.id) else False
            if order_values:
                if rec.sudo().agent_collection_id:
                    agent_collection_mapping_fields = self.env['rb_delivery.utility'].COLLECTION_FIELDS_MAPPING['agent_money_collection_mapping_fields']
                    agent_collection_values = {
                        agent_collection_mapping_fields[key]: key for key in order_values.keys() if key in agent_collection_mapping_fields
                    }

                    if agent_collection_values:
                        rec.check_changing_collections_values(agent_collection_values,'agent_money_collection')

                if rec.sudo().collection_id:
                    money_collection_mapping_fields = self.env['rb_delivery.utility'].COLLECTION_FIELDS_MAPPING['money_collection_mapping_fields']
                    money_collection_values = {
                        money_collection_mapping_fields[key]: key for key in order_values.keys() if key in money_collection_mapping_fields
                    }

                    if money_collection_values:
                        rec.check_changing_collections_values(money_collection_values,'money_collection')

                if rec.sudo().returned_collection_id:
                    returned_money_collection_mapping_fields = self.env['rb_delivery.utility'].COLLECTION_FIELDS_MAPPING['returned_monay_collection_mapping_fields']
                    returned_money_collection_values = {
                        returned_money_collection_mapping_fields[key]: key for key in order_values.keys() if key in returned_money_collection_mapping_fields
                    }

                    if returned_money_collection_values:
                        rec.check_changing_collections_values(returned_money_collection_values,'returned_money_collection')

                if rec.sudo().runsheet_collection_id:
                    runsheet_collection_mapping_fields = self.env['rb_delivery.utility'].COLLECTION_FIELDS_MAPPING['runsheet_collection_mapping_fields']
                    runsheet_collection_values = {
                        runsheet_collection_mapping_fields[key]: key for key in order_values.keys() if key in runsheet_collection_mapping_fields
                    }

                    if runsheet_collection_values:
                        rec.check_changing_collections_values(runsheet_collection_values,'runsheet_collection')

                if rec.sudo().agent_returned_collection_id:
                    returned_agent_money_collection_mapping_fields = self.env['rb_delivery.utility'].COLLECTION_FIELDS_MAPPING['returned_agent_money_collection_mapping_fields']
                    returned_agent_money_collection_values = {
                        returned_agent_money_collection_mapping_fields[key]: key for key in order_values.keys() if key in returned_agent_money_collection_mapping_fields
                    }
                    if returned_agent_money_collection_values:
                        rec.check_changing_collections_values(returned_agent_money_collection_values,'returned_agent_money_collection')


    def check_changing_collections_values(self, values,collection_type):

        values_to_change = {key: 0 for key in values.keys()}
        order_fields = self.fields_get()
        total_amount = 0
        order_ids = False
        if collection_type == 'agent_money_collection':
            order_ids = self.agent_collection_id.sudo().order_ids
        if collection_type == 'money_collection':
            order_ids = self.collection_id.sudo().order_ids
        if collection_type =='returned_money_collection':
            order_ids = self.returned_collection_id.sudo().order_ids
        if collection_type == 'runsheet_collection':
            order_ids = self.runsheet_collection_id.sudo().order_ids
        if collection_type == 'returned_agent_money_collection':
            order_ids = self.agent_returned_collection_id.sudo().order_ids
        if order_ids:
            for order in order_ids:
                for key, value in values.items():
                    if order_fields[value]['type'] in ['integer','float']:
                        values_to_change[key] += getattr(order, value, 0)
                    elif order_fields[value]['type'] in ['many2one']:
                        values_to_change[key] = getattr(order, value, 0).id
                    else:
                        values_to_change[key] = getattr(order, value, 0)
                if order.inclusive_delivery:
                    total_amount += order.copy_total_cost
                else:
                    total_amount += order.cost + order.delivery_cost

            if total_amount:
                values_to_change['total_ammount'] = total_amount

        if values_to_change and collection_type == 'agent_money_collection':
            data = {'uid':self._uid,'message':_("Agent money collection values updated through update orders."),'records':self.agent_collection_id,'values':values_to_change,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

        if values_to_change and collection_type == 'money_collection':
            data = {'uid':self._uid,'message':_("Money collection values updated through update orders."),'records':self.collection_id,'values':values_to_change,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

        if values_to_change and collection_type == 'returned_money_collection':
            data = {'uid':self._uid,'message':_("Returned Money collection values updated through update orders."),'records':self.returned_collection_id,'values':values_to_change,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

        if values_to_change and collection_type == 'runsheet_collection':
            data = {'uid':self._uid,'message':_("Runsheet collection values updated through update orders."),'records':self.runsheet_collection_id,'values':values_to_change,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

        if values_to_change and collection_type == 'returned_agent_money_collection':
            data = {'uid':self._uid,'message':_("Returned agent money collection values updated through update orders."),'records':self.agent_returned_collection_id,'values':values_to_change,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

    @api.one
    def wkf_action_update_status(self,status_name):
        # TODO need to add role and when this should be used
        state = self.env['rb_delivery.status'].sudo().search([('name','=',status_name),'|',('status_type','=',False),('status_type','=','olivery_order')])
        web_color = state.web_color
        self.write({'web_color':web_color})
        self.write({'state': status_name})

    #TODO this is added for abwaab client and need to be enhance later on ( for Connect)
    @api.one
    def mobile_update_status(self,values):
        status_name = values['state']
        state = self.env['rb_delivery.status'].sudo().search([('name','=',status_name),'|',('status_type','=',False),('status_type','=','olivery_order')])
        web_color = state.web_color
        values['web_color']=web_color
        self.write(values)

    #assign_agent_to_order
    @api.multi
    def assign_agent_to_order(self,next_state):
        if self._context.get('ignore_assign_agent'):
            return
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._context.get('uid'))])
        group_id = user.group_id
        username = user.username
        if group_id.code == 'rb_delivery.role_driver':
            message=_("Order has been updated with agent by the agent %s himself, this is done through automated action from system. You can check configuration or contact support to get more details.")%(username)
            data = {'uid':self._uid,'message':message,'records':self,'values':{'assign_to_agent':user.id},'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

    # Post actions methods
    @api.multi
    def detach_agent_action(self,next_state):
        recs = self
        recs = recs.filtered(lambda x:x.is_sender_partner_order==False)
        username = self.env.user.name
        message = _("Order has been updated by %s detaching the agent, this is done through automation form the system called “detach agent”, you can check configuration to know more or contact support")%(username)
        data = {'uid':self._uid,'message':message,'records':recs,'values':{'assign_to_agent':''},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)

    # Post actions methods
    @api.multi
    def reset_order_action(self,next_state):
        recs = self.filtered(lambda x:x.order_action!=False)
        username = self.env.user.name
        message = _("Order has been updated by %s removing the order action, this is done through automation from the system called “reset_order_action”, you can check configuration to know more or contact support")%(username)
        data = {'uid':self._uid,'message':message,'records':recs,'values':{'order_action':''},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)
    
    # Post actions methods
    def remove_from_runsheet_action(self,next_state):
        runsheet = self.env['rb_delivery.runsheet'].sudo().search([('order_ids','=',self.id)],limit=1)
        order_ids = []
        for order in runsheet.order_ids:
            if order.id != self.id:
                order_ids.append(order.id)
        runsheet.write({'order_ids':[(6,0,order_ids)]})


    @api.multi
    def clear_reschedule_date(self,next_state):
        self.write({'reschedule_date':False})

    # Post actions methods
    @api.multi
    def detach_current_drivers_action(self,next_state):
        username = self.env.user.name
        message=_("Order has been updated by %s through automation system called “detach current drivers action” you can check configuration or contact support for more information.")%(username)
        data = {'uid':self._uid,'message':message,'records':self,'values':{'current_drivers':[(6,0,[])]},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)

    def set_ready_for_return(self,values):
        username = self.env.user.name
        message = _("Order has been updated by %s setting is ready for return to True, this is done through automation form the system called “set_ready_for_return”, you can check configuration to know more or contact support")%(username)
        data = {'uid':self._uid,'message':message,'records':self,'values':{'is_ready_for_return':True},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)

    def clear_ready_for_return(self,values):
        username = self.env.user.name
        message = _("Order has been updated by %s setting is ready for return to False, this is done through automation form the system called “clear_ready_for_return”, you can check configuration to know more or contact support")%(username)
        data = {'uid':self._uid,'message':message,'records':self,'values':{'is_ready_for_return':False},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)


    @api.multi
    def reset_returned_discount(self,next_state):
        returned_statuses = []
        before_delivery_statuses = []
        returned_status_ids =  self.env['rb_delivery.client_configuration'].get_param('returned_discount_status')
        before_delivery_status_ids =  self.env['rb_delivery.client_configuration'].get_param('canceled_discount_status')
        if returned_status_ids:
            for status_id in returned_status_ids:
                    status = self.env['rb_delivery.status'].search([('id','=',status_id),'|',('status_type','=',False),('status_type','=','olivery_order')])
                    returned_statuses.append(status.name)
        if before_delivery_status_ids:
            for status_id in before_delivery_status_ids:
                    status = self.env['rb_delivery.status'].search([('id','=',status_id),'|',('status_type','=',False),('status_type','=','olivery_order')])
                    before_delivery_statuses.append(status.name)
        recs = self
        recs.filtered(lambda x:x.state=='canceled' or x.state == 'rejected' or x.state in returned_statuses or x.state in before_delivery_statuses)
        values['discount_value'] = 0
        values['returned_discount'] = 0
        values['returned_value'] = 0
        recs.write(values)
        for rec in recs:
            rec._compute_required_from_business()

    def pre_action(self,values):
        status_pre_actions=self.env['rb_delivery.status'].search([('name','=',self.state),'|',('status_type','=',False),('status_type','=','olivery_order')]).status_pre_action_ids
        for action in status_pre_actions:
            try:
                method_to_call=getattr(rb_delivery_order,action.name)
                method_to_call(self,values)
            except:
                pass

    def to_in_progress_if_assign_driver(self,values):
        if 'assign_to_agent' in values and values['assign_to_agent'] and 'state' not in values :
            self.with_context(ignore_assign_agent=True).write({'state':'in_progress'})
    
    def to_ready_for_dispatch_if_assign_driver(self,values):
        if values.get('assign_to_agent') and not values.get('state'):
            self.with_context(ignore_assign_agent=True).write({'state':'ready_for_dispatch'})

    def to_picking_up_if_assign_driver(self,values):
        if 'assign_to_agent' in values and values['assign_to_agent'] and 'state' not in values :
            self.with_context(ignore_assign_agent=True).write({'state':'picking_up'})

    def to_money_out_if_assign_driver(self,values):
        if 'assign_to_agent' in values and values['assign_to_agent'] and 'state' not in values :
            self.with_context(ignore_assign_agent=True).write({'state':'money_out'})


    def prevent_change_status_if_not_in_collection(self,values):
        if not self.collection_id:
            self.env['rb_delivery.error_log'].raise_olivery_error(232,self.id,{'action_name':'prevent_change_status_if_not_in_collection','order_sequence':self.sequence,'status_name':values,'collection_type':'collection'})

    def prevent_change_status_if_not_in_agent_collection(self,values):
        if not self.agent_collection_id:
            self.env['rb_delivery.error_log'].raise_olivery_error(232,self.id,{'action_name':'prevent_change_status_if_not_in_agent_collection','order_sequence':self.sequence,'status_name':values,'collection_type':'agent collection'})

    @api.model
    def get_total_required_business(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_required_from_business_status_sum')
            states = []
            if statuses:
                for status in statuses:
                    state = self.env['rb_delivery.status'].search([('id','=',status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                    states.append(state.name)
                order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['required_from_business'])
                total = 0
                for rec in order_rec:
                    total += rec['required_from_business']
                return total
        except:
            print ("no statuses found inthe required from businesss sum")

    @api.model
    def get_total_delivery_fee(self):
        statuses = self.env['rb_delivery.client_configuration'].get_param('order_delivery_fee_status_sum')
        states = []
        try:
            if statuses:
                for status in statuses:
                    state = self.env['rb_delivery.status'].search([('id','=',status)])
                    states.append(state.name)
        except:
            print("object is not iterable , there is no statuses in the configuration for order Deliver fee status")

        order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['delivery_cost'])
        total = 0
        for rec in order_rec:
            total += rec['delivery_cost']
        return total

    @api.model
    def get_total_required_business_all_order(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_all_required_from_business_status_sum')
            states = []
            for status in statuses:
                state = self.env['rb_delivery.status'].search([('id','=',status)])
                states.append(state.name)
            order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['required_from_business'])
            total = 0
            for rec in order_rec:
                total += rec['required_from_business']
            return total
        except:
              pass

    @api.model
    def get_total_collection_with_driver(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_money_collection_with_driver_status_sum')
            states = []
            for status in statuses:
                state = self.env['rb_delivery.status'].search([('id','=',status)])
                states.append(state.name)
            order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['money_collection_cost'])
            total = 0
            for rec in order_rec:
                total += rec['money_collection_cost']
            return total
        except:
              pass

    @api.model
    def get_total_collection_in_company(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_money_collection_status_sum')
            states = []
            for status in statuses:
                state = self.env['rb_delivery.status'].search([('id','=',status)])
                states.append(state.name)
            order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['money_collection_cost'])
            total = 0
            for rec in order_rec:
                total += rec['money_collection_cost']
            return total
        except:
              pass

    @api.model
    def get_total_profit_for_driver(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_profit_for_driver_status_sum')
            states = []
            for status in statuses:
                state = self.env['rb_delivery.status'].search([('id','=',status)])
                states.append(state.name)
            order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['agent_cost'])
            total = 0
            for rec in order_rec:
                total += rec['agent_cost']
            return total
        except:
              pass

    @api.model
    def get_total_delivery_profit_for_company(self):
        try:
            statuses = self.env['rb_delivery.client_configuration'].get_param('order_profit_for_driver_status_sum')
            states = []
            for status in statuses:
                state = self.env['rb_delivery.status'].search([('id','=',status)])
                states.append(state.name)
            order_rec = self.env['rb_delivery.order'].search_read([('state','in',states)],['delivery_profit'])
            total = 0
            for rec in order_rec:
                total += rec['delivery_profit']
            return total
        except:
              pass



    @api.model
    def get_total_collection_for_status(self,status):
        if status:
            orders = self.env['rb_delivery.order'].search_read([('state','in',status)],['money_collection_cost'])
            total = 0
            for rec in orders:
                total += rec['money_collection_cost']
            return total

    @api.model
    def count_order_status(self,status):
        count = 0
        if status:
            count = self.env['rb_delivery.order'].search_count([('state','in',status)])
            return count
        else:
            count = self.env['rb_delivery.order'].search_count([])
            return count

    @api.model
    def count_order(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.order'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.order'].search_count([])
            return count

    @api.model
    def order_assign_to_agent(self,order_seq,driver_id):
        if order_seq:
            order = self.env['rb_delivery.order'].sudo().search(['|',('reference_id','=',order_seq),('sequence','=',order_seq)],limit=1)
            if order :
                driver_allow_to_assign = self.env['rb_delivery.client_configuration'].get_param('order_driver_assign_himself_on_order_ability')
                if driver_allow_to_assign :
                    statuses = self.env['rb_delivery.client_configuration'].get_param('order_driver_assign_himself_to_order_status')
                    states = []
                    for status in statuses:
                        state = self.env['rb_delivery.status'].search([('id','=',status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                        states.append(state.name)
                    if order.state in states:
                        username = self.env.user.name
                        message=_("Order has been updated by agent %s, the driver assigned him self to the order this was allowed through configuration called “order_assign_to_agent”. You can check the configuration or contact support for more details.")%(username)
                        data = {'uid':self._uid,'message':message,'records':order,'values':{'assign_to_agent':driver_id},'update':True}
                        self.env['rb_delivery.utility'].olivery_sudo(data)
                        return {'success': True, 'message': 'Add Order Success', 'sequence': order.sequence, 'order_id': order.id}
                    else :
                        return {'fail': True, 'message': 'You are not allowed to change to this order'+' '+order.sequence, 'sequence': order.sequence}
                else :
                    return {'fail': True, 'message': 'You are not allowed to add an order please contact your adminstration'}
            else:
                 self.env['rb_delivery.order'].create({
                            'reference_id' : order_seq,
                            'assign_to_agent': driver_id,
                            'state': 'in_progress'
                            })
                 return {'success': True, 'message': 'Create Order Success', 'sequence': order_seq }

    @api.model
    def get_total_collection(self,domain):
        total = 0
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for rec in orders:
                total += rec['money_collection_cost']
            return total

    @api.model
    def get_business_order(self,domain):
        users = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                    if {"id":order.assign_to_business.id ,"name":order.commercial_name} not in users :
                         users.append({"id":order.assign_to_business.id ,"name":order.commercial_name})
            return users

    @api.model
    def get_driver_order(self,domain):
        users = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                    if {"id":order.assign_to_agent.id ,"name":order.assign_to_agent.username} not in users :
                         users.append({"id":order.assign_to_agent.id ,"name":order.assign_to_agent.username})
            return users

    @api.model
    def get_area_order(self,domain):
        areas = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                if {"id":order.customer_area.id ,"name":order.customer_area.name} not in areas :
                         areas.append({"id":order.customer_area.id ,"name":order.customer_area.name})
            return areas

    @api.model
    def get_business_area_order(self,domain):
        areas = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                    if order.business_area not in areas :
                        areas.append(order.business_area)
            return areas

    @api.model
    def get_sub_area_order(self,domain):
        sub_areas = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                if {"id":order.customer_sub_area.id ,"name":order.customer_sub_area.name} not in sub_areas :
                         sub_areas.append({"id":order.customer_sub_area.id ,"name":order.customer_sub_area.name})
            return sub_areas

    @api.model
    def get_order_has_a_route(self,domain):
        sub_areas = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                if {"id":order.customer_sub_area.id ,"name":order.customer_sub_area.name} not in sub_areas :
                         sub_areas.append({"id":order.customer_sub_area.id ,"name":order.customer_sub_area.name})
            return sub_areas

    @api.model
    def get_business_sub_area_order(self,domain):
        areas = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                    if order.business_sub_area not in areas :
                        areas.append(order.business_sub_area)
            return areas

    @api.model
    def get_business_commercial_name_order(self,domain):
        users = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                    if {"id":order.assign_to_business.id ,"name":order.commercial_name} not in users :
                         users.append({"id":order.assign_to_business.id ,"name":order.commercial_name})
            return users

    @api.model
    def get_country_order(self,domain):
        countries = []
        if domain:
            orders = self.env['rb_delivery.order'].search(domain)
            for order in orders:
                if {"id":order.customer_country.id ,"name":order.customer_country.name} not in countries :
                         countries.append({"id":order.customer_country.id ,"name":order.customer_country.name})
            return countries

    @api.model
    def action_multi_assign_to_agent(self,order_ids,driver_id):
        if len(order_ids) > 0 and driver_id:
            orders = self.env['rb_delivery.order'].search([['id','in',order_ids]])
            for order in orders:
                order.write({'assign_to_agent': driver_id})
            return {'code':200,'success': True}

    @api.model
    def print_report(self,report_name,order_id):
        pdf, _ = self.env['ir.actions.report'].search([('report_name','=',report_name),('model','=','rb_delivery.order')]).sudo().render_qweb_pdf(order_id)
        data = base64.encodestring(pdf)
        return data


    @api.model
    def print_waybill_a5(self,order_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delivery_order_detail_action').sudo().render_qweb_pdf(order_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.model
    def print_waybill_a4(self,order_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delivery_order_detail_a4_action').sudo().render_qweb_pdf(order_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.model
    def print_thermal_printer(self,order_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delivery_order_thermal_report_action').sudo().render_qweb_pdf(order_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.model
    def create_call_logs(self,data):
        add_call_logs_in_the_chat = self.env['rb_delivery.client_configuration'].get_param('order_add_call_logs_in_the_order_chat')
        if add_call_logs_in_the_chat:
            subtype = "mail.mt_comment"
        else:
            subtype = "mail.mt_note"

        user = self.env['res.users'].sudo().search([('id', '=', self._uid)])
        if user and user.name and user.partner_id and 'customer_name' in data and data['customer_name'] and 'mobile' in data and data['mobile'] and 'order_id' in data and data['order_id']:
            self.env['mail.message'].create({
                    'message_type': 'comment',
                    'model':'rb_delivery.order',
                    'res_id':data['order_id'],
                    'subtype_id': self.env.ref(subtype).id,
                    'author_id': user.partner_id.id,
                    'subject': "Call Logs",
                    'body': user.name +" "+_("Tried to contact")+" "+data['customer_name']+" ("+data['mobile']+")",
                    'needaction_partner_ids': [(4, user.partner_id.id)],
                })

    @api.one
    def wkf_refresh(self):
        orders=self.env['rb_delivery.order'].search([])
        for order in orders:
            order._compute_required_from_business()

    @api.one
    def wkf_migrate(self):
        orders=self.env['rb_delivery.order'].search([('assign_to_distributor','!=',False)])
        for order in orders:
            order.write({'assign_to_agent': order.assign_to_distributor.id})

    @api.model
    def shortcut_buttons_action(self,ids,action_name):
        records=self.env['rb_delivery.order'].search([('id','in',ids)])
        for rec in records:
            rec.wkf_action_update_status(action_name)
        #elif action_name == 'received_in_company':
        # fn = type("wkf_action_"+action_name, (), {})
        # for rec in records:
        #     rec.fn
        return

    @api.model
    def return_fields_to_skip_authorize():
        # these fields will be skipped from autherization
        # Since they are mostly generated from teh system
        fields_to_skip=['__last_update','seq_exist','longitude','latitude',]
        return  fields_to_skip


    def authenticte_user(self,company):
        params = {
            "jsonrpc": "2.0",
            "params": {
                "login":company.company_username,
                "password": company.company_password,
                "db":company.company_db}}
        headers = {'content-type': 'application/json'}
        session_id = ''
        response = requests.post(company.sudo().company_url+'/web/session/authenticate', data=json.dumps(params), headers=headers)
        return response.json()

    @api.model
    def authorize_edit(self,values,status=False):
        #for admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1:
            return

        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        if not status:
            current_status=self.state
        else:
            current_status = status

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status_field_security'].search([('group_id','=',user_group.id),('status_ids.name','=',current_status)],limit=1)

        field_names = [field.name for field in record.field_ids]
        allowed_fields = self.SUDO_FIELDS

        if len(field_names)==0 and len(allowed_fields)==0:
            # TODO translate message
            self.env['rb_delivery.error_log'].raise_olivery_error(234,self.id,{'user_role':user_group.name,'status_name':current_status})


        general_configuration = self.env['rb_delivery.general_configuration'].sudo().search([])
        if general_configuration:
            field_ids = general_configuration[0].field_ids
            if field_ids:
                allowed_fields = allowed_fields + [field.name for field in field_ids]

        not_allowed_fields = []
        for value in values:
            if value not in field_names and value not in allowed_fields:
                field = record.field_ids.search([('name','=',value),('model_id','=','rb_delivery.order')],limit=1)
                if field and field.display_name:
                    not_allowed_fields.append(field.display_name)
        if len(not_allowed_fields)>0:
            str_not_allowed_fields = ",".join(not_allowed_fields[:10])
            sequence = values.get('sequence') if values.get('sequence') else self.sequence
            self.env['rb_delivery.error_log'].raise_olivery_error(235,self.id,{'user_role':user_group.name,'status_name':current_status,'field_names':str_not_allowed_fields, 'order_sequence': sequence})

        return

    def show_toast_for_cost_zero(self,values):
        message = _('Warning: Cost is set to zero')
        inclusive_delivery = False
        if 'assign_to_business' in values and values['assign_to_business']:
            user = self.env['rb_delivery.user'].sudo().search_read([('id','=',values['assign_to_business'])],['inclusive_delivery'])
            if user:
                inclusive_delivery = user[0]['inclusive_delivery']
        else:
            inclusive_delivery = self.sudo().assign_to_business.inclusive_delivery
        if inclusive_delivery:
            if 'copy_total_cost' in values and values['copy_total_cost'] == 0:
                self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))
        else:
            if 'cost' in values and values['cost'] == 0:
                self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message] , str(self._uid))

    @api.model
    def authorize_change_status(self,status,values,previous_status=None):
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if previous_status and (self._uid!=1 and self._uid!=2): self.check_lock_status(previous_status,status)
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            rb_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            self.env['rb_delivery.error_log'].raise_olivery_error(236,self.id,{'status_name':status, 'from_status': previous_status, 'order_sequence': self.sequence, 'user_role': user_group.name, 'user_mobile': rb_user.mobile_number})

        return

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                rb_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
                self.env['rb_delivery.error_log'].raise_olivery_error(237,self.id,{'from_status':current_status_record.title,'status_name':next_status_record.title, 'order_sequence': self.sequence, 'user_mobile': rb_user.mobile_number, 'user_role': rb_user.group_id.name})

    def get_chat_message(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_chat_notification').id
        domain = [('order_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.chat_notification',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    @api.model
    def update_order_status_by_barcode(self, status, order_id, reference_id):
        try:
            order_record = self.env['rb_delivery.order'].browse(order_id)
            update_result = order_record.write({'state': status})
            if update_result:
                replacement_order_values = self.get_clone_replacement_values(order_record)
                replacement_order_values['reference_id'] = reference_id
                replacement_order_values['assign_to_agent']=order_record.assign_to_agent.id
                new_replacement_order = self.sudo().create(replacement_order_values)
                if new_replacement_order:
                    new_replacement_order.message_post(_('This order Created by %s through mobile when status has been updated to delivered by barcode.')%(self.env.user.name))
                    return {'success': True }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @api.model
    def bulk_change_state_by_barcode(self, state, barcode_lists):
        domain = []
        all_barcodes=[]
        exist_barcodes=[]
        error_messages = []
        write_values ={'state':state['name']} if not self._context.get('valListSent') else state
        for key in barcode_lists.keys(): # generate the domain
            if isinstance(barcode_lists[key], list) and len(barcode_lists[key]) > 0:
                all_barcodes = all_barcodes + barcode_lists[key]
                domain.append([key, 'in', barcode_lists[key]])
        domain = ['|'] * (len(domain) - 1) + domain
        # check user role get the role_code and get the configuration key
        user = self.env['rb_delivery.user'].search_read([('user_id', '=', self._uid)], ['role_code'])
        config_key = 'statuses_to_skip_scan_validation'
        if user and user[0]['role_code'] == 'rb_delivery.role_driver':
            config_key = 'driver_statuses_to_skip_scan_validation'

        # get the statuses that should skip validation and do them as olivery_sudo
        statuses_to_skip = self.env['rb_delivery.client_configuration'].get_param(config_key)

        orders_skip = self.env['rb_delivery.order']
        orders_write = self.env['rb_delivery.order']

        # sudo search for all orders
        order_list = self.env['rb_delivery.order'].sudo().search(domain) # status,next_status
        allowed_orders = order_list
        if len(order_list) > 0:
            if user and user[0]['role_code'] == 'rb_delivery.role_driver':
                allowed_orders = order_list.filtered(lambda x: x.assign_to_agent and x.assign_to_agent.id == user[0]['id'])
                if self._context.get('valListSent'):
                    for val in write_values.keys():
                        write_values[val]['assign_to_agent'] = user[0]['id']
                else:
                    write_values['assign_to_agent'] = user[0]['id']


            elif user and user[0]['role_code'] == 'rb_delivery.role_business':
                allowed_orders = order_list.filtered(lambda x: x.assign_to_business and x.assign_to_business.id == user[0]['id'])

        for order in order_list:
            state_name = state['name'] if not self._context.get('valListSent') else write_values[str(order.id)]['state']
            if order.state_id.id in statuses_to_skip:
                # check if the status is next status for the order
                next_statuses = self.env['rb_delivery.status'].search([('name','=',order.state),'|',('status_type','=',False),('status_type','=','olivery_order')]).next_state_ids.mapped('name')
                if state_name in next_statuses:
                    orders_skip += order
                else: # if not next status for the order then append the error message
                    error_messages.append({'message':_('The status you are trying to change to is not next status for the order'),'sequence':order.sequence, 'reference_id':order.reference_id})
            else:
                try: # try to authorize the change
                    order.check_lock_status(order.state,state_name)
                    if order in allowed_orders:
                        orders_write += order
                    else: # if not allowed then append the message to the error_messages
                        error_messages.append({'message':_('You dont have access to this order'),'sequence':order.sequence, 'reference_id':order.reference_id})
                except Exception as e:
                    message = str(e)  # if not allowed then append the message to the error_messages
                    error_messages.append({'message':message, 'sequence':order.sequence, 'reference_id':order.reference_id})
        if len(orders_write) > 0: # normal change for the orders that need validation
            if self._context.get('valListSent'):
                for order in orders_write:
                    try:
                        order.write(write_values[str(order.id)])   
                    except Exception as e:
                        message = str(e)  # if not allowed then append the message to the error_messages
                        error_messages.append({'message':message, 'sequence':order.sequence, 'reference_id':order.reference_id})
            else:
                orders_write.write(write_values)
        if len(orders_skip) > 0: # sudo change for the skip validation orders
            if self._context.get('valListSent'):
                username = self.env.user.name
                for order in orders_skip:
                    state_name = write_values[str(order.id)]['state']
                    message = _("Order has been updated by %s changing the status to: %s")%(username, state_name)
                    data = {'uid':self._uid,'message':message,'records':orders_skip,'values':write_values[str(order.id)],'update':True}
                    try:
                        self.env['rb_delivery.utility'].olivery_sudo(data)
                    except Exception as e:
                        message = str(e)
                        error_messages.append({'message':message, 'sequence':order.sudo().sequence, 'reference_id':order.sudo().reference_id})

        return error_messages

    def convert_date_to_desired_format(self,date_string):
        try:
            date_object = parser.parse(date_string)
            return date_object
        except ValueError:
            self.env['rb_delivery.error_log'].raise_olivery_error(238,self.id,{'date_string':date_string})

    @api.onchange('assign_to_business')
    def _onchange_assign_to_business(self):
        for record in self:
            if record.assign_to_business and record.assign_to_business.order_type:
                record.order_type_id = record.assign_to_business.order_type.id
            else:
                record.order_type_id = record.default_order_type()


    def calculate_total(self,orders, field):
        return sum(rec[field] for rec in orders)

    def get_orders_by_statuses(self, config_key, fields):
        try:
            domain = []
            get_clone_orders  = self.env['rb_delivery.client_configuration'].get_param('consider_clone_when_get_dashboard_item')
            status_ids = self.env['rb_delivery.client_configuration'].get_param(config_key)
            if len(status_ids) > 0:
                if get_clone_orders:
                    domain = [('state_id', 'in', status_ids)]
                else:
                    domain = [('state_id', 'in', status_ids),('is_cloned','=',False)]
                orders = self.env['rb_delivery.order'].search_read(domain, fields)
                return orders
        except:
            return []

    @api.model
    def get_dashboard_info(self):

        dashboard_values = {}

        # Get Count User Waiting Confirmation
        count_user_waiting_confirmation = self.env['rb_delivery.user'].search_count([('state', '=', 'pending')])
        dashboard_values['count_user_waiting_confirmation'] = count_user_waiting_confirmation

        # Get Total Required Business
        order_rec = self.get_orders_by_statuses('order_required_from_business_status_sum', ['required_from_business'])
        total_required_business = self.calculate_total(order_rec, 'required_from_business')
        dashboard_values['total_required_business'] = total_required_business

        # Get Total Delivery Fee
        order_rec = self.get_orders_by_statuses('order_delivery_fee_status_sum', ['delivery_cost'])
        total_delivery_cost = self.calculate_total(order_rec, 'delivery_cost')
        dashboard_values['total_delivery_fee'] = total_delivery_cost

        # Get Total Collection In Company
        order_rec = self.get_orders_by_statuses('order_money_collection_status_sum', ['money_collection_cost'])
        total_collection_in_company = self.calculate_total(order_rec, 'money_collection_cost')
        dashboard_values['total_collection_in_company'] = total_collection_in_company

        # Get Total Collection With Driver
        order_rec = self.get_orders_by_statuses('order_money_collection_with_driver_status_sum', ['money_collection_cost'])
        total_collection_with_driver = self.calculate_total(order_rec, 'money_collection_cost')
        dashboard_values['total_collection_with_driver'] = total_collection_with_driver

        # Get Total Profit For Driver
        order_rec = self.get_orders_by_statuses('order_profit_for_driver_status_sum', ['agent_cost'])
        total_profit_for_driver = self.calculate_total(order_rec, 'agent_cost')
        dashboard_values['total_profit_for_driver'] = total_profit_for_driver

        # Get Total Required Business All Order
        order_rec = self.get_orders_by_statuses('order_all_required_from_business_status_sum', ['required_from_business'])
        total_required_business_all_order = self.calculate_total(order_rec, 'required_from_business')
        dashboard_values['total_required_business_all_order'] = total_required_business_all_order

        # Get Total Collection With Statuses
        dashboard_values['total_money_collection_cost'] = {}
        dashboard_values['count_order_status'] = {}
        statuses_need_to_get_total_collection_for_them = self.env['rb_delivery.client_configuration'].get_param('statuses_needed_to_get_money_collection_sum_for_them')
        get_clone_orders  = self.env['rb_delivery.client_configuration'].get_param('consider_clone_when_get_dashboard_item')

        if len(statuses_need_to_get_total_collection_for_them) > 0:
            for status in statuses_need_to_get_total_collection_for_them:
                if get_clone_orders:
                    domain = [('state_id', '=', status)]
                else:
                    domain = [('state_id', '=', status),('is_cloned','=',False)]
                orders = self.env['rb_delivery.order'].search_read(domain, ['money_collection_cost','state'])
                if len(orders) > 0:
                    state_name = orders[0]['state']
                    total = self.calculate_total(orders, 'money_collection_cost')
                    dashboard_values['total_money_collection_cost'][state_name] = total
                    dashboard_values['count_order_status'][state_name] = len(orders)

        # Count today order statuses
        count_today_order_statuses, total_today_order_statuses = 0, 0
        statuses_considered_in_today_orders = self.env['rb_delivery.client_configuration'].get_param('statuses_to_be_considered_in_today_orders')
        if len(statuses_considered_in_today_orders) > 0:
            domain = self.get_date_domain(statuses_considered_in_today_orders)
            orders = self.env['rb_delivery.order'].search_read(domain, ['money_collection_cost', 'create_date'])
            if orders:
                total_today_order_statuses = self.calculate_total(orders, 'money_collection_cost')
                count_today_order_statuses = len(orders)
            dashboard_values['total_today_order_statuses'] = total_today_order_statuses
            dashboard_values['count_today_order_statuses'] = count_today_order_statuses

        # Count plankton order statuses
        count_plankton_order_statuses, total_plankton_order_statuses = 0, 0
        orders = self.get_orders_by_statuses('statuses_to_be_considered_in_plankton_orders', ['money_collection_cost'])
        total_plankton_order_statuses = self.calculate_total(orders, 'money_collection_cost')
        count_plankton_order_statuses = len(orders)
        dashboard_values['total_plankton_order_statuses'] = total_plankton_order_statuses
        dashboard_values['count_plankton_order_statuses'] = count_plankton_order_statuses

        # Count all active orders
        count_all_active_orders = 0
        default_status_active_order = self.env['rb_delivery.client_configuration'].get_param('default_status_active_order')
        domain = [('state_id', 'in', default_status_active_order)]
        if not get_clone_orders:
            domain += [('is_cloned','=',False),('is_replacement','=',False)]
        count_all_active_orders = self.env['rb_delivery.order'].search_count(domain)
        dashboard_values['count_all_active_orders'] = count_all_active_orders

        return dashboard_values

    def get_date_domain(self, statuses):
        domain = []
        user = self.env['res.users'].search([('id', '=', self._uid)])
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        local_tz = pytz.timezone(timezone)
        utc_tz = pytz.timezone('UTC')

        local_date = datetime.now(local_tz)
        fmt = "%Y-%m-%d %H:%M:%S"

        local_start_date = local_date.replace(hour=0, minute=0, second=0, microsecond=0)
        local_end_date = local_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        utc_start_date = local_start_date.astimezone(utc_tz).strftime(fmt)
        utc_end_date = local_end_date.astimezone(utc_tz).strftime(fmt)

        if user.has_group('rb_delivery.role_driver'):
            domain = [('state_id', 'in', statuses), ('assign_to_distributor_date', '>=', utc_start_date), ('assign_to_distributor_date', '<=', utc_end_date)]
        else:
            domain = [('state_id', 'in', statuses), ('create_date', '>=', utc_start_date), ('create_date', '<=', utc_end_date)]

        return domain

    @api.model
    def request_money_collection(self):
        required_from_business = 0
        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('ability_to_create_pre_paid_collection')
        allowed_status_names = []
        allowed_statuses_name_ids = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')
        if self._context.get('lang') == 'ar_SY':
            allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('title_ar')
        else:
            allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('title')
        orders = self.env['rb_delivery.order'].search([('state','in',allowed_statuses_name_ids),('collection_id','=',False),('is_prepaid_order','=',False)])
        if len(orders) > 0:
            for order in orders:
                required_from_business += order.required_from_business
            return {
                'required_from_business' : required_from_business,
                'order_count' : len(orders)
            }
        else:
            allowed_status_names = ','.join(allowed_status_names)
            self.env['rb_delivery.error_log'].raise_olivery_error(247,self.id,{'status_name':allowed_status_names})
            #raise ValidationError(_('You do not have orders that statuses in %s OR your orders paid')%(allowed_status_names))

    @api.model
    def request_agent_money_collection(self, selected_business_id=False):
        stuck_orders_count = self.search_count([('state','=','delivered_stuck')])
        customer_payments = 0
        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('agent_collection_status')
        allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')
        domain = [('state','in',allowed_status_names),
                  ('agent_collection_id','=',False)]
        if selected_business_id:
            domain.append(('assign_to_business','=',selected_business_id))
        orders = self.env['rb_delivery.order'].search(domain)
        if len(orders) > 0:
            for order in orders:
                customer_payments += float(order.customer_payment)
            return {
                'customer_payments' : customer_payments,
                'order_count' : len(orders),
                'stuck_orders_count':stuck_orders_count
            }
        else:
            allowed_status_names = ','.join(allowed_status_names)
            self.env['rb_delivery.error_log'].raise_olivery_error(247,self.id,{'status_name':allowed_status_names})

    @api.model
    def get_agent_collection_orders(self):
        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('agent_collection_status')
        allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')
        orders = self.env['rb_delivery.order'].search_read([('state','in',allowed_status_names),('agent_collection_id','=',False)], ['assign_to_business'])
        return orders

    @api.model
    def create_agent_collection(self, selected_business_id=False):
        uid = self.env.user.partner_id.id
        rb_agent = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        confs = self.env['rb_delivery.client_configuration'].get_param(['agent_collection_status','ability_to_update_agent_collection_on_different_status', 'should_spicify_business_when_request_agent_collection'])
        allowed_status_names = self.env['rb_delivery.status'].browse(confs['agent_collection_status']).mapped('name')
        domain = [('state','in',allowed_status_names),('agent_collection_id','=',False)]
        if selected_business_id:
            domain.append(('assign_to_business','=',selected_business_id))
        orders = self.env['rb_delivery.order'].search(domain)
        if len(orders) > 0:
            values = {'order_ids': orders}
            try:
                with self.env.cr.savepoint():

                    if confs['ability_to_update_agent_collection_on_different_status']:
                        old_domain = [('state','=','created'), ('agent_id','=',rb_agent.id)]
                        if selected_business_id:
                            old_domain.append(('business_id','=',selected_business_id))
                        old_agent_collection = self.env['rb_delivery.agent_money_collection'].search([('state','=','created'), ('agent_id','=',rb_agent.id)], limit=1)
                        if old_agent_collection:

                            old_agent_collection.with_context(original_uid=uid).sudo().order_ids = [(4, order.id) for order in orders]
                            old_agent_collection.message_post(_('This collection edited by %s through mobile when click on request agent collection button')%(self.env.user.name))
                            return {
                                'collectionId':old_agent_collection.id
                            }

                    pre_paid_collection = self.env['rb_delivery.agent_money_collection'].with_context(original_uid=uid).sudo().create(values)
                    pre_paid_collection.message_post(_('This collection created by %s through mobile when click on request agent collection button')%(self.env.user.name))
            except Exception as e:
                self.env.cr.rollback()
                raise ValidationError(_(e))
            return {
                'collectionId':pre_paid_collection.id
            }
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(247,self.id,{'status_name':allowed_status_names})

    @api.model
    def create_prepaid_collection(self):
        uid = self.env.user.partner_id.id
        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('ability_to_create_pre_paid_collection')
        allowed_status_names = self.env['rb_delivery.status'].browse(allowed_statuses).mapped('name')
        orders = self.env['rb_delivery.order'].search([('state','in',allowed_status_names),('collection_id','=',False),('is_prepaid_order','=',False)])
        if len(orders) > 0:
            order_ids = []
            for order in orders:
                order_ids.append(order.id)
            values = {'order_ids': self.env['rb_delivery.order'].browse(order_ids)}
            values['is_pre_paid_collection'] = True
            try:
                with self.env.cr.savepoint():
                    rb_business = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
                    business_ids = [rb_business.id]
                    if rb_business.sudo().user_parent_id and rb_business.sudo().user_parent_id.collection_in_main_user_name:
                        business_ids.append(rb_business.sudo().user_parent_id.id)
                    status_money_collection=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','collection'),('status_type','=','olivery_collection')],limit=1)
                    old_money_collection = self.env['rb_delivery.multi_print_orders_money_collector'].sudo().search([('state','=',status_money_collection.name), ('business_id','in',business_ids)], limit=1)
                    if old_money_collection:
                        try:
                            old_money_collection.with_context(original_uid=uid).sudo().order_ids = [(4, order.id) for order in orders]
                            old_money_collection.sudo().message_post(_('This collection edited by %s through mobile when click on request collection button')%(self.env.user.name))
                            for order in orders:
                                order.message_post(_('This order edited by %s through mobile when click on request collection button')%(self.env.user.name))
                            return {
                                'collectionId':old_money_collection.sudo().id,
                                'appended':True
                            }
                        except Exception as e:
                            pre_paid_collection = self.env['rb_delivery.multi_print_orders_money_collector'].with_context(original_uid=uid).sudo().create(values)
                            pre_paid_collection.message_post(_('This collection created by %s through mobile when click on request collection button')%(self.env.user.name))
                            pre_paid_collection.order_ids.with_context(original_uid=uid).sudo().write({'is_prepaid_order': True})
                            for order in pre_paid_collection.order_ids:
                                order.message_post(_('This order edited by %s through mobile when click on request collection button')%(self.env.user.name))
                            return {
                                'collectionId':pre_paid_collection.id,
                                'appended':False
                            }
                    else:
                        pre_paid_collection = self.env['rb_delivery.multi_print_orders_money_collector'].with_context(original_uid=uid).sudo().create(values)
                        pre_paid_collection.message_post(_('This collection created by %s through mobile when click on request collection button')%(self.env.user.name))
                        pre_paid_collection.order_ids.with_context(original_uid=uid).sudo().write({'is_prepaid_order': True})
                        for order in pre_paid_collection.order_ids:
                            order.message_post(_('This order edited by %s through mobile when click on request collection button')%(self.env.user.name))
                        return {
                            'collectionId':pre_paid_collection.id,
                            'appended':False
                        }
            except Exception as e:
                self.env.cr.rollback()
                raise ValidationError(_(e))
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(247,self.id,{'status_name':allowed_status_names})
            #raise ValidationError(_('You do not have orders that statuses in %s OR your orders paid')%(allowed_status_names))

    def get_required_fields(self,values):
        messages = []
        if ('is_cloned' in values and values['is_cloned']):
            return
        allow_to_create_skeleton = self.env['rb_delivery.client_configuration'].get_param('order_create_driver_ability')
        if not allow_to_create_skeleton:
            model_id = self.env.ref('rb_delivery.model_rb_delivery_order').id
            control_fields = self.env['rb_delivery.control_fields'].sudo().search([('model_id','=',model_id)])
            for control_field in control_fields:
                if control_field.required:
                    for field in control_field.field_ids:
                        if field.name not in values or (field.name in values and not values[field.name]):
                            messages.append(_("Field %s is required.")%(field.field_description))
        if len(messages)>0:
            messages_str = '\n '.join(messages)
            raise Warning(messages_str)

    @api.model
    def fields_get(self, allfields=None, attributes=None):
        res = super(rb_delivery_order, self).fields_get(allfields,attributes)
        model_id = self.env.ref('rb_delivery.model_rb_delivery_order').id
        control_fields = self.env['rb_delivery.control_fields'].sudo().search([('model_id','=',model_id)])
        for control_field in control_fields:
            if control_field.required:
                for field in control_field.field_ids:
                    if field.name in res:
                        res[field.name]['required'] = True
            else:
                for field in control_field.field_ids:
                    if field.name in res:
                        res[field.name]['required'] = False

        return res

    @api.model
    def pick_up_order(self, sequence):
        uid = self.env.user.partner_id.id
        order = self.env['rb_delivery.order'].with_context(original_uid=uid).sudo().search(['|', ('sequence','=',sequence), ('reference_id', '=', sequence)],limit=1)
        allowed_statuses = self.env['rb_delivery.client_configuration'].get_param('allowed_statuses_for_pickup_order')
        next_status = self.env['rb_delivery.client_configuration'].get_param('picked_up_status')
        if not next_status:
            self.env['rb_delivery.error_log'].raise_olivery_error(257,self.id,{'sequence':sequence})
        else:
            next_status = self.env['rb_delivery.status'].search([('id','=',next_status[0])],limit=1)
            if not next_status:
                self.env['rb_delivery.error_log'].raise_olivery_error(257,self.id,{'sequence':sequence})
        if order.state_id.id in allowed_statuses:
            rb_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
            if rb_user and rb_user.id and rb_user.group_id and rb_user.group_id.code == 'rb_delivery.role_driver':
                order.write({'assign_to_agent':rb_user.id, 'state':next_status.name})
                return order
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(255,self.id,{'sequence':sequence})
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(256,self.id,{'sequence':sequence})

    @api.model
    def sort_orders(self, sequence, changeState=True, to_in_progress=False):
        uid = self.env.user.partner_id.id

        if not isinstance(sequence, list):
            order = self.env['rb_delivery.order'].with_context(original_uid=uid).sudo().search([('state', '!=', 'deleted'),'|','|','|', ('sequence','=',sequence), ('reference_id', '=', sequence), ('partner_reference_id', '=', sequence), ('follower_ref_id', '=', sequence)],limit=1)
            allowed_statuses = self.env['rb_delivery.client_configuration'].get_param(['statuses_to_change_to_in_branch_when_sort','sort_statuses'])
            next_status = allowed_statuses.get('sort_statuses')
            allowed_statuses = allowed_statuses.get('statuses_to_change_to_in_branch_when_sort')


            if not next_status:
                self.env['rb_delivery.error_log'].raise_olivery_error(257,self.id,{'sequence':sequence})
            else:
                next_status = self.env['rb_delivery.status'].browse([next_status[0]])
                if not next_status:
                    self.env['rb_delivery.error_log'].raise_olivery_error(257,self.id,{'sequence':sequence})
            if order:
                if order.sudo().state_id.id in allowed_statuses and changeState:
                    to_state = next_status.name
                    if to_in_progress:
                        rb_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
                        if rb_user and rb_user.role_code == 'rb_delivery.role_driver':
                            username = self.env.user.name
                            message = _("Order has been updated by %s adding the agent, this is done through mobile")%(username)
                            data = {'uid':self._uid,'message':message,'records':order,'values':{'assign_to_agent': rb_user.id, 'state':'in_progress'},'update':True}
                            self.env['rb_delivery.utility'].olivery_sudo(data)
                    else:
                        order.write({'state':to_state})
                    return {'changed': True, 'has_follow_orders': {True if order.follow_up_order else False} , 'order': order.sudo().read(['sequence', 'customer_address', 'reference_id', 'customer_area', 'customer_sub_area', 'address_tag', 'business_area','partner_reference_id'])}
                else:
                    return {'changed': False, 'order': order.sudo().read(['sequence', 'customer_address', 'reference_id', 'customer_area', 'customer_sub_area', 'address_tag','note','stuck_comment', 'business_area','partner_reference_id'])}
            else:
                order = self.env['rb_delivery.follow_up_order'].search([('follow_up_sequence', '=', sequence)])

                if order:
                    order = order.order_id
                    return {'changed': False, 'is_follow_order': True,'order': order.sudo().read(['sequence', 'customer_address', 'reference_id', 'customer_area', 'customer_sub_area', 'address_tag','note','stuck_comment', 'business_area','partner_reference_id'])}
        else:
            for barcode in sequence:
                self.sort_orders(barcode, changeState)
            return {'success': True}

    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image

    @api.model
    def verify_scan(self, sequence_list, reference_list):
        orders = self.env['rb_delivery.order'].sudo().search(['|',['sequence', 'in', sequence_list],['reference_id', 'in', reference_list]])
        follow_orders = self.env['rb_delivery.follow_up_order'].sudo().search(['|', ['follow_up_sequence', 'in', sequence_list],  ['follow_up_sequence', 'in', reference_list]])
        success = True
        messages = []
        order_sequences = []
        if orders:
            for order in orders:
                missing_follow_orders = [fo.follow_up_sequence for fo in order.follow_up_order if fo not in follow_orders]
                if missing_follow_orders:
                    messages.append(_('Order number %s has unscanned follow up orders: %s') % (order.reference_id, ', '.join(missing_follow_orders)))
                    success = False
                else:
                    order_sequences.append(order.sequence)

        return {'messages': messages, 'success': success, 'orders': order_sequences}

    @api.onchange('receiver_business','receiver_is_business')
    def _change_receiver_data(self):
        if self.receiver_is_business and self.receiver_business:
            self.customer_name = self.receiver_business.username
            self.customer_mobile = self.receiver_business.mobile_number
            self.customer_area = self.receiver_business.area_id.id
            self.second_mobile_number = self.receiver_business.second_mobile_number
            self.customer_address = self.receiver_business.address
            if self.receiver_business.sub_area:
                self.customer_sub_area = self.receiver_business.sub_area.id
            if self.receiver_business.longitude and self.receiver_business.latitude:
                if not self.longitude and not self.latitude:
                    self.longitude=self.receiver_business.longitude
                    self.latitude =self.receiver_business.latitude

        elif not self.receiver_is_business:
            self.receiver_business = False

    @api.model
    def get_domain_for_users_page(self):
        ctx = self._context or {}

        if not (ctx.get('inside_users_page')
                and ctx.get('default_business_id')
                and ctx.get('default_child_business_id')):
            return []

        default_bus = ctx['default_business_id']
        child_bus = ctx['default_child_business_id']

        if not isinstance(child_bus, (list, tuple)):
            child_bus = [child_bus]

        domain = []

        if ctx.get('filter_based_on_receiver_business'):
            domain += [
                '|',
                ('receiver_business', '=', default_bus),
                ('receiver_business', 'in', child_bus),
            ]
        elif ctx.get('filter_based_on_business'):
            domain += [
                '|',
                ('assign_to_business', '=', default_bus),
                ('assign_to_business', 'in', child_bus),
            ]
        else:
            domain += [
                '|', '|', '|',
                ('assign_to_business', '=', default_bus),
                ('receiver_business', '=', default_bus),
                ('assign_to_business', 'in', child_bus),
                ('receiver_business', 'in', child_bus),
            ]

        return domain

    @api.multi
    @api.depends('assign_to_agent','state','customer_area','customer_sub_area','order_type_id','assign_to_business','picked_up_by')
    def _compute_pickup_agent_cost(self):
        allowed_status_ids = self.env['rb_delivery.client_configuration'].get_param('statuses_to_allow_pickup_agent_cost_recalculate')
        allow_statuses = []
        if allowed_status_ids:
            allow_statuses = self.env['rb_delivery.status'].search([('id', 'in', allowed_status_ids)]).mapped('name')

        for order in self:
            if order.saved_value_for_pickup_agent_cost:
                order.pickup_agent_cost = order.saved_value_for_pickup_agent_cost
            else:
                picked_up_by = False
                agent = False
                if order.sudo().assign_to_agent:
                    agent = order.sudo().assign_to_agent
                elif order.assign_to_agent:
                    agent = order.assign_to_agent
                if order.sudo().picked_up_by:
                    picked_up_by = order.sudo().picked_up_by
                elif order.picked_up_by:
                    picked_up_by = order.picked_up_by
                if order.state in allow_statuses and agent:
                    order.pickup_agent_cost = order.get_agent_cost(agent,'picked_up_by')
                elif picked_up_by:
                    order.pickup_agent_cost = order.get_agent_cost(picked_up_by,'picked_up_by')

    def check_auto_creating_runsheet_collection(self,state):
        runsheet_collection_state_ids = self.env['rb_delivery.client_configuration'].get_param('auto_create_runsheet_collection_statuses')
        if runsheet_collection_state_ids:
            runsheet_statuses = self.env['rb_delivery.status'].browse(runsheet_collection_state_ids)
            runsheet_orders_ids_with_agents = []
            create_runsheet_collection = False
            if any(status.name == state for status in runsheet_statuses):
                create_runsheet_collection = True

            if create_runsheet_collection:

                for rec in self:
                    if rec.sudo().assign_to_agent:
                        runsheet_orders_ids_with_agents.append(rec.id)

                if len(runsheet_orders_ids_with_agents) > 0:
                    self.env['rb_delivery.create_runsheet'].create_runsheet(runsheet_orders_ids_with_agents,False)

    @api.multi
    def fetch_message_data(self,context):
        messages =  self.env['mail.message'].search(args=[('model','=','rb_delivery.order'),('res_id','in',[self.id])],load=True)
        messages = messages.with_context(context).message_format()
        return messages

    @api.model
    def create_communication_log(self, values):
        # Extract values from the dictionary
        order_id = values.get('order_id')
        message = values.get('new_value', '')
        create_mail_message = values.get('create_mail_message', False)

        if not order_id or not message:
            self.env['rb_delivery.error_log'].raise_olivery_error(1004,self.id)
        # Prepare log entry data
        log_values = {
            'order_id': order_id,
            'new_value': message,
            'is_message': True,
        }
        log_entry = self.env['rb_delivery.order_logs'].create(log_values) if log_values else None
        if not log_entry:
            self.env['rb_delivery.error_log'].raise_olivery_error(1005,self.id)
        # If log entry is created and email message is required
        if create_mail_message:
            order = self.browse(order_id)
            if order:
                order.message_post(body=message)
        return log_entry.id

    def remove_stuck_comment_and_reject_reason(self, values):
        recs = self
        recs = recs.filtered(lambda x:x.is_sender_partner_order==False)
        username = self.env.user.name
        message = _("Order has been updated by %s removing stuck comment and reject reason, this is done through automation form the system called 'remove_stuck_comment_and_reject_reason', you can check configuration to know more or contact support.")%(username)
        data = {'uid':self._uid,'message':message,'records':recs,'values':{'stuck_comment':False,'reject_reason':False},'update':True}
        self.env['rb_delivery.utility'].olivery_sudo(data)


    @api.model
    def sudo_search_count(self, domain):
        try:
            no_orders = self.env['rb_delivery.order'].sudo().search_count(domain)
            return no_orders
        except:
            return []