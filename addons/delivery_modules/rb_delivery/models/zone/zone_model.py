# -*- coding: utf-8 -*-
import barcode
from barcode.writer import ImageWriter
import base64
import io
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class RbDeliveryZone(models.Model):
    _name = "rb_delivery.area_zone"
    _inherit = "mail.thread"

    _sql_constraints = [('code', 'unique(code)', _('Zone with same code already exists!')),('name', 'unique(name)', _('Zone with same name already exists!'))]

    name = fields.Char('Name', translate=True, track_visibility="on_change",copy=False)
    
    code = fields.Char('Code', track_visibility="on_change",copy=False)

    barcode = fields.Binary('Barcode', compute="_generate_barcode")

    order_total_count = fields.Integer("Total Orders", compute="_compute_order_total", store=False)

    sub_areas = fields.One2many(
        comodel_name='rb_delivery.sub_area',
        string='Sub Areas',
        relation='rb_area_zone_sub_area_rel',
        column1='zone_id',
        column2='sub_area_id',
        inverse_name="zone_id",
        track_visibility='on_change',
        copy=False
    )

    areas = fields.One2many(
        comodel_name='rb_delivery.area',
        string='Areas',
        relation='rb_area_zone_area_rel',
        column1='zone_id',
        column2='area_id',
        inverse_name="zone_id",
        track_visibility='on_change',
        copy=False
    )

    @api.depends('code')
    def _generate_barcode(self):
        if self.code:
            try:
                barcode.base.Barcode.default_writer_options['write_text'] = False
                EAN = barcode.get_barcode_class('code39')
                ean = EAN(self.code, writer=ImageWriter(), add_checksum=False)
                image_output = io.BytesIO()
                ean.write(image_output)
                encoded = base64.b64encode(image_output.getvalue())
                self.barcode = encoded
            except Exception as e:
                self.env['rb_delivery.error_log'].raise_olivery_error(213,self.id,{'reference_issue':_(str(_(e))),'field_name':_('Code')})

    @api.depends('areas', 'sub_areas')
    def _compute_order_total(self):
        order = self.env['rb_delivery.order'].sudo()
        sub_area_obj = self.env['rb_delivery.sub_area'].sudo()
        zone_status_ids = self.env['rb_delivery.client_configuration'].get_param('zone_assignment_allowed_statuses')
        zone_status_arr = []

        if zone_status_ids:
            status_recs = self.env['rb_delivery.status'].browse(zone_status_ids)
            zone_status_arr = [status.name for status in status_recs if status.name]
        sub_area_zone_map = {
            sub.id: sub.zone_id.id
            for sub in sub_area_obj.sudo().search([])
        }

        for zone in self:
            total = 0

            for sub in zone.sub_areas:
                count = order.search_count([
                    ('customer_sub_area', '=', sub.id),
                    ('state', 'in', zone_status_arr)
                ])
                total += count

            for area in zone.areas:
                orders = order.search([
                    ('customer_area', '=', area.id),
                    ('state', 'in', zone_status_arr)
                ])
                for o in orders:
                    sub = o.customer_sub_area
                    if not sub or not sub_area_zone_map.get(sub.id):
                        total += 1

            zone.order_total_count = total

    def name_get(self):
        result = []
        for zone in self:
            label = zone.name
            if hasattr(zone, 'order_total_count') and zone.order_total_count:
                label += " (%s %s)" % (zone.order_total_count, _("Orders"))
            result.append((zone.id, label))
        return result
