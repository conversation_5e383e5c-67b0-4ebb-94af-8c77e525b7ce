# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from openerp import SUPERUSER_ID
import re
from datetime import datetime
from onesignal_sdk.client import Client
import json
import random
import string
import logging
import requests
_logger = logging.getLogger(__name__)
from passlib.context import CryptContext
from odoo.http import request
from urllib.parse import urlparse


class rb_delivery_user(models.Model):

    _name = 'rb_delivery.user'
    _inherit = 'mail.thread'
    _rec_name = 'username'
    _order = "create_date DESC"
    _description = "User Model"

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------
    def get_groups(self):
        group_ids = self.env['rb_delivery.client_configuration'].get_param('hidden_roles')
        domain = [('category_id.code','=','model_rb_delivery')]
        if group_ids:
            domain.append(('id','not in', group_ids))
        return domain

    @api.model
    def get_default_group(self):
        if 'create_role' in self.env.context and self.env.context['create_role']:
            res = self.env.ref(self.env.context['create_role'])
        else:
            res = self.env.ref('rb_delivery.role_business')
        return res.id or False

    @api.model
    def get_default_pricelist(self):
        res = self.env['rb_delivery.pricelist'].search(
            [('is_default', '=', 'True')])
        return res.id or False

    @api.model
    def get_default_inclusive_delivery(self):
        default_inclusive_delivery_cost = self.env['rb_delivery.client_configuration'].get_param('user_creation_inclusive_ability')
        return default_inclusive_delivery_cost

    @api.model
    def default_show_location(self):
        def_show_location = False
        for rec in self:
            roles = rec.env['rb_delivery.client_configuration'].get_param('setting_user_location_ability')
            if roles and type(roles) != bool and roles[0] :
                for role in roles:
                    if rec.group_id.id == role :
                        def_show_location = True
                        break
            else :
                def_show_location = False
            rec.show_location = def_show_location

    @api.model
    def get_count_user_waiting_confirmation(self):
        count = 0
        count = self.env['rb_delivery.user'].search_count([('state','=','pending')])
        return count

    def _get_business_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_business')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    def default_is_business(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_business = user.has_group('rb_delivery.role_business')
        return is_business

    def default_user_is_driver(self):
        if self._uid == 1 or self._uid == 2:
            return True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            group_id = user.group_id
            is_driver = False
            if  group_id.code == 'rb_delivery.role_driver':
                is_driver = True
            return is_driver

    def default_user_is_business(self):
        if self._uid == 1 or self._uid == 2:
            return True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            group_id = user.group_id
            is_business = False
            if group_id.code == 'rb_delivery.role_business':
                is_business = True
            return is_business

    def default_is_base(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_base = user.has_group('base.group_system')
        return is_base

    def default_is_super_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_super_manager = user.has_group('rb_delivery.role_super_manager')
        return is_super_manager

    def default_is_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_manager = user.has_group('rb_delivery.role_manager') or user.has_group('rb_delivery.role_picking_up_manager') or user.has_group('rb_delivery.role_distribution_manager')
        return is_manager

    def default_is_sales(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_sales = user.has_group('rb_delivery.role_sales')
        return is_sales

    def default_is_sales(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_sales = user.has_group('rb_delivery.role_sales')
        return is_sales

    def default_is_pricelist_manger(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        is_pricelist_manger = user.pricelist_manager
        return is_pricelist_manger

    def _default_order_count(self):
        orders = self.env['rb_delivery.order'].search([('assign_to_business','=',self.id)])
        return len(orders)

    def default_is_password_manager(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        is_password_manager = user.password_manager
        return is_password_manager

    def default_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        is_block_delivery_fee = user.block_delivery_fee
        return is_block_delivery_fee

    def default_is_role_manager(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        is_role_manager = user.role_manager
        return is_role_manager

    def default_is_confirm_user_manager(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        is_confirm_user_manager = user.confirm_user_manager
        return is_confirm_user_manager

    def _default_discount_type(self):
        default_discount_type = self.env['rb_delivery.client_configuration'].get_param('default_returned_discount_type')
        if default_discount_type:
            dis_type = default_discount_type['type']
            dis_value = default_discount_type['value']
            return dis_type

    def _default_discount_value(self):
        default_discount_type = self.env['rb_delivery.client_configuration'].get_param('default_returned_discount_type')
        dis_value=0
        if default_discount_type:
            dis_value = 0
            dis_type = default_discount_type['type']
            if dis_type == '1':
                dis_value = default_discount_type['value']
            return dis_value

    def _default_canceled_discount_type(self):
        default_discount_type = self.env['rb_delivery.client_configuration'].get_param('default_cenceled_discount_type')
        if default_discount_type:
            dis_type = default_discount_type['type']
            dis_value = default_discount_type['value']
            return dis_type

    def _default_canceled_discount_value(self):
        default_discount_type = self.env['rb_delivery.client_configuration'].get_param('default_cenceled_discount_type')
        dis_value=0
        if default_discount_type:
            dis_value = 0
            dis_type = default_discount_type['type']
            if dis_type == '1':
                dis_value = default_discount_type['value']
            return dis_value

    @api.one
    def compute_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        self.show_country = show_country

    def default_show_country(self):
        show_country = self.env['rb_delivery.client_configuration'].get_param('show_country')
        return show_country

    def _default_show_sub_area(self):
        default_show_sub_area = self.env['rb_delivery.client_configuration'].get_param('register_show_customer_sub_area_visibility')
        return default_show_sub_area

    @api.one
    def _compute_show_sub_area(self):
        show_sub_area = self.env['rb_delivery.client_configuration'].get_param('register_show_customer_sub_area_visibility')
        self.show_sub_area = show_sub_area

    def _default_email_registration(self):
        email_registration = self.env['rb_delivery.client_configuration'].get_param('email_registration')
        return email_registration

    @api.one
    def _compute_email_registration(self):
        email_registration = self.env['rb_delivery.client_configuration'].get_param('email_registration')
        self.email_registration = email_registration

    def get_auto_generated_password(self):
        letters = string.ascii_letters
        result_str = ''.join(random.choice(letters) for i in range(8))
        return result_str

    @api.multi
    def _field_ids_domain(self):
        model_id = self.env['ir.model'].sudo().search([('model','=','rb_delivery.order')]).id
        domain = [('model_id', '=', model_id)]
        return domain

    def _default_show_online(self):
        show_online = self.env['rb_delivery.client_configuration'].get_param('show_online_in_dashboard_visibility')
        return show_online

    @api.one
    def _compute_show_online(self):
        show_online = self.env['rb_delivery.client_configuration'].get_param('show_online_in_dashboard_visibility')
        self.show_online = show_online

    @api.multi
    @api.depends('account_manager_id')
    def _compute_account_manager_mobile(self):
        for rec in self:
            account_manager_id = rec.sudo().account_manager_id
            if account_manager_id:
                rec.account_manager_mobile = account_manager_id.mobile_number
            else:
                rec.account_manager_mobile = False

    @api.model
    def _get_default_business_work_category(self):
        default_business_work_category = self.env['rb_delivery.business_work_category'].search([('is_default', '=', True)])
        return default_business_work_category.id

    def _default_affiliator_user(self):
        if not self.env.user.has_group('rb_delviery.role_business') and not self.env.user.has_group('base.group_system'):
            delivery_user = self.env['rb_delivery.user'].search([('user_id','=',self.env.user.id)],limit=1)
            if delivery_user:
                return delivery_user.id

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    @api.model
    def default_order_type(self):
        order_type = self.env['rb_delivery.order_type'].search([('default','=',True)])
        if len(order_type) != 0:
            return order_type[0].id if order_type else None
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(499,self.id,{'user_name': self.env['rb_delivery.user'].browse(self._uid).username})
            #raise ValidationError(('order type is empty'))

    user_id = fields.Many2one('res.users', 'User', readonly=True,track_visibility="on_change")

    user_parent_id = fields.Many2one('rb_delivery.user', 'Main User',track_visibility="on_change")

    driver_id = fields.Many2one('rb_delivery.user', 'Driver',track_visibility="on_change", domain=[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])])

    driver_order_assigned = fields.One2many('rb_delivery.order',inverse_name='assign_to_agent',domain=[('state','in',['waiting','no_answer','pending_driver','rejected_by_driver','picked_up','picking_up','in_progress','reschedule','rejected','stuck'])],string='Driver Orders Assigned')

    driver_number_of_orders = fields.Integer(string='Driver Number of orders',track_visibility="on_change", store=True)

    area_map = fields.Many2many(comodel_name = 'rb_delivery.area_map', string = 'Area Map', relation = 'area_map_user_item', column1 = 'user_id', column2 = 'area_map_id')

    sub_area_map = fields.Many2many(comodel_name = 'rb_delivery.sub_area_map', string = 'Sub Area Map', relation = 'sub_area_map_user_item', column1 = 'user_id', column2 = 'sub_area_map_id')

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups, default=get_default_group)

    password = fields.Char('Password', default=get_auto_generated_password)

    commercial_name= fields.Char('Commercial Name', track_visibility="on_change",copy=False)

    commercial_activity= fields.Char('Commercial Activity', track_visibility="on_change")

    commercial_number= fields.Char('Commercial Number', track_visibility="on_change")

    user_auth_key = fields.Char('User Auth Key',track_visibility="on_change")

    app_auth_key = fields.Char('App Auth Key',track_visibility="on_change")

    app_id = fields.Char('App ID',track_visibility="on_change")

    uuid = fields.Char('UUID',track_visibility="on_change")

    order_type = fields.Many2one(
        'rb_delivery.order_type', string="Default Order Type", default=default_order_type,track_visibility="on_change")

    mobile_number = fields.Char('Mobile Number', required=True,track_visibility="on_change")

    second_mobile_number = fields.Char('Second Mobile Number',track_visibility="on_change")

    whatsapp_mobile = fields.Char('Whatsapp Mobile Number',track_visibility="on_change")

    second_whatsapp_mobile = fields.Char('Second Whatsapp Mobile Number',track_visibility="on_change")

    user_sequence = fields.Char('User ID Number',track_visibility=False,copy=False,readonly=True)

    username = fields.Char(String='UserName', required=True,track_visibility="on_change")

    role_name = fields.Char(related='group_id.name', readonly=True, string="Role Name",track_visibility=False)

    role_code = fields.Char(related='group_id.code', readonly=True, string="Role Code",track_visibility="on_change")

    email = fields.Char('Email',track_visibility="on_change",copy=False)  # not required

    country_id = fields.Many2one('rb_delivery.country',string='Country',track_visibility="on_change", ondelete='restrict')

    show_country = fields.Boolean('Show country',readonly=True,compute="compute_show_country", default=default_show_country)

    show_follower_vhub = fields.Boolean('Show follower Vhub')

    area_id = fields.Many2one('rb_delivery.area', string='Area', required=True,track_visibility="on_change", ondelete='restrict')

    sub_area = fields.Many2one('rb_delivery.sub_area',string='Sub  Area', track_visibility="on_change", ondelete='restrict')

    show_sub_area = fields.Boolean('Show  Sub  Area', default=_default_show_sub_area, compute="_compute_show_sub_area", readonly=True)

    email_registration = fields.Boolean('Email Registration', default=_default_email_registration, compute="_compute_email_registration", readonly=True)

    address = fields.Char('Address', required=True,track_visibility="on_change")

    address_tag = fields.Many2one('rb_delivery.address_tags','Full Address')

    show_address_in_waybill = fields.Boolean('Show Address In Waybill', default=True,track_visibility="on_change")

    show_area_in_waybill = fields.Boolean('Show Area In Waybill', default=True,track_visibility="on_change")

    parent_id = fields.Many2one('res.users', 'Master Branch',track_visibility="on_change")

    default_area_id = fields.Many2one('rb_delivery.area', 'Default area',track_visibility="on_change")

    default_sub_area_id = fields.Many2one('rb_delivery.sub_area', 'Default sub area',track_visibility="on_change")

    player_id = fields.Char('Player ID',track_visibility="on_change")

    pricelist_id = fields.Many2one(
        'rb_delivery.pricelist', 'Pricelist', default=get_default_pricelist,track_visibility="on_change")

    previous_pricelist_id = fields.Many2one('rb_delivery.pricelist', 'Previous Pricelist', track_visibility="on_change")

    car_number = fields.Char('Car Number',track_visibility="on_change")

    show_location = fields.Boolean('Show location',readonly=True,compute="default_show_location", default="default_show_location")

    location = fields.Char('Location',track_visibility="on_change",readonly=True)

    state = fields.Selection([
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('reconfirmed', 'Reconfirmed'),
        ('deactivate', 'Deactivate'),
        ('canceled', 'Canceled')], default="pending", track_visibility="on_change")

    inclusive_delivery = fields.Boolean("Inclusive Delivery Cost", default=get_default_inclusive_delivery,track_visibility="on_change")

    active = fields.Boolean("Active", default=True,track_visibility="on_change")

    is_company = fields.Boolean('Is Company', default=False,track_visibility="on_change")

    company_username = fields.Char('V-Hub Username',track_visibility="on_change")

    company_password = fields.Char('V-Hub Password',track_visibility="on_change")

    company_url = fields.Char('Company URL',track_visibility="on_change")

    company_db = fields.Char('Company Identifier',track_visibility="on_change")

    bank_name = fields.Char('Bank Name', track_visibility="on_change")

    bank_number = fields.Char('Bank Number', track_visibility="on_change")

    wallet_name = fields.Char('Wallet Name', track_visibility="on_change")

    wallet_number = fields.Char('Wallet Number', track_visibility="on_change")

    is_business = fields.Boolean('Is Business', compute="check_user", default=default_is_business)

    user_is_business = fields.Boolean('User Is Business', compute="check_user_business", default=default_user_is_business)

    user_is_driver = fields.Boolean('User Is Driver', compute="check_user_driver", default=default_user_is_driver)

    is_base = fields.Boolean('Is Base', compute="check_user", default=default_is_base)

    is_super_manager = fields.Boolean('Is Super Manager', compute="check_user", default=default_is_super_manager)

    is_manager = fields.Boolean('Is Manager', compute="check_user", default=default_is_manager)

    is_sales = fields.Boolean('Is Sales', compute="check_user", default=default_is_sales)

    is_security_manager = fields.Boolean('Is Security Manager', compute="check_security_manager")

    password_manager = fields.Boolean('Password Manager', default=False)

    settings_manager = fields.Boolean('Settings Manager', default=False)

    block_delivery_fee = fields.Boolean('Block Delivery Fee', default=False)

    pricelist_edit_manager = fields.Boolean('Pricelist Edit Manager', default=False)

    allow_edit_collection_orders = fields.Boolean('Allow edit collection orders', default=False)

    collection_manager = fields.Boolean('Collection Manager', default=False)

    returned_collection_manager = fields.Boolean('Returned Collection Manager', default=False)

    pricelist_manager = fields.Boolean('Pricelist Manager', default=False)

    is_password_manager = fields.Boolean('Is Password Manager', compute="check_manager", default=default_is_password_manager)

    is_block_delivery_fee = fields.Boolean('Is Block Delivery Fee', compute="check_block_delivery_fee", default=default_is_block_delivery_fee)

    role_manager = fields.Boolean('Role Manager', default=False)

    is_role_manager = fields.Boolean('Is Role Manager', compute="check_manager", default=default_is_role_manager)

    confirm_user_manager = fields.Boolean('Confirm Users Manager', default=False)

    show_fields_button = fields.Boolean('Show fields button', default=False)

    is_confirm_user_manager = fields.Boolean('Is Confirm Users Manager', compute="check_manager", default=default_is_confirm_user_manager)

    is_pricelist_manager = fields.Boolean('Is Pricelist Manager',compute="check_manager" ,default=default_is_pricelist_manger)

    latitude = fields.Char('latitude',track_visibility="on_change")

    longitude = fields.Char('longitude',track_visibility="on_change")

    location_link = fields.Char('Location Link',track_visibility="on_change")

    has_follower_user = fields.Boolean("Has Follower User")
    has_customers = fields.Boolean("Has Stores")

    user_image = fields.Binary('User Image')

    action_type = fields.Selection([('for_action', 'For Action'),('for_status', 'For Status')], string="Action Type",track_visibility="on_change")

    pricing_type=fields.Selection([('fixed', 'Fixed'),('percentage', 'Percentage'),('pricelist','Pricelist')], string="Pricing Type",track_visibility="on_change")

    is_per_business = fields.Boolean('Is Per Business', track_visibility="on_change")

    pricing_fixed_value=fields.Float(string="Fixed value",track_visibility="on_change")

    pricing_percentage_value=fields.Integer(string="Percentage value",track_visibility="on_change")

    pricing_pricelist_id = fields.Many2one('rb_delivery.pricelist', 'Select Pricelist',track_visibility="on_change")

    fixed_discount = fields.Integer('Fixed discount', default=_default_discount_value,track_visibility="on_change")

    returned_discount_type = fields.Selection([('0', 'Default'),('1', 'Fixed Discount'),('2', 'Based on pricelist')], default=_default_discount_type,track_visibility="on_change")

    website = fields.Char('Website',track_visibility="on_change")

    facebook_hyperlink = fields.Char('Facebook',track_visibility="on_change")

    instagram_hyperlink = fields.Char('Instagram',track_visibility="on_change")

    twitter_hyperlink = fields.Char('Twitter',track_visibility="on_change")

    linkedin_hyperlink = fields.Char('Linkedin',track_visibility="on_change")

    tiktok_hyperlink = fields.Char('Tiktok',track_visibility="on_change")

    canceled_discount_type = fields.Selection([('0', 'Default'),('1', 'Fixed Discount'),('2', 'Based on pricelist')], default=_default_canceled_discount_type, string="Canceled discount type",track_visibility="on_change")

    fixed_canceled_discount = fields.Integer('Fixed Canceled discount', default=_default_canceled_discount_value,track_visibility="on_change")

    collection_in_main_user_name = fields.Boolean("Collection in main user name", track_visibility="on_change")

    hide_totals_from_dashboard_parent = fields.Boolean("Hide totals from dashboard parent", track_visibility="on_change", compute="_compute_hide_totals_from_dashboard_parent", store=True)

    hide_totals_from_dashboard = fields.Boolean("Hide totals from dashboard", track_visibility="on_change", default=False)

    child_ids = fields.One2many('rb_delivery.user',inverse_name='user_parent_id',string='Sub Users',track_visibility="on_change")

    print_user_logo_in_bolisa = fields.Boolean("Print User Logo In Waybill" )

    insurance_type = fields.Selection([('promissory_note', 'Promissory note'), ('cash', 'Cash')],string='Insurance type', default="cash",track_visibility="on_change")

    insurance_value = fields.Float('Insurance Value',track_visibility="on_change")

    insurance_attachment = fields.Binary('Insurance Attachment',track_visibility="on_change")

    credit = fields.Float('Credit',track_visibility="on_change")

    default_payment_type = fields.Many2one('rb_delivery.payment_type', 'Default Payment Type',track_visibility="on_change")

    default_payment_detail = fields.Char(string='Default Payment Detail',track_visibility="on_change")

    holder_name  = fields.Char(string='Holder Name',track_visibility="on_change")

    forgot_password = fields.Boolean('Forgot Password',readonly=True)

    field_ids = fields.Many2many(comodel_name = 'ir.model.fields', string = 'Fields shown', relation = 'fields_user_item', column1 = 'user_id', column2 = 'field_id', domain=_field_ids_domain,track_visibility="on_change")

    from_import = fields.Boolean('From import', default=False)

    online = fields.Boolean('Online', default=False,track_visibility="on_change")

    show_online = fields.Boolean('Show Online', default=_default_show_online, compute="_compute_show_online", readonly=True)

    replace_company_logo_with_business_logo = fields.Boolean('Replace company logo with business logo' )

    export_date = fields.Datetime('Export date',readonly=True)

    block_delivery_profit = fields.Boolean('Block Delivery Profit', default=False)

    business_work_category = fields.Many2one('rb_delivery.business_work_category', string='business Work Category', default=_get_default_business_work_category, track_visibility="on_change", ondelete='restrict')

    show_editable_button = fields.Boolean('Show editable button', default=False)

    for_app_stores_use = fields.Boolean()

    onboarding_error_ids = fields.Many2many(comodel_name = 'rb_delivery.onboarding_error', string = 'Onboarding errors', relation = 'onboarding_errors_user_item', column1 = 'user_id', column2 = 'onboarding_error_id',compute="get_onboarding_errors")

    show_request_collection_button = fields.Boolean('Show Request Collection Button')

    user_request_commission = fields.Boolean('User Request Commission')

    commission_type=fields.Selection([('fixed', 'Fixed'),('percentage', 'Percentage')], string="Commission Type",track_visibility="on_change")

    commission_value=fields.Integer(string="Commission Value",track_visibility="on_change")

    driver_commission_value_picked_up = fields.Float(string="Driver commission value picking up",track_visibility="on_change",default=-1)

    driver_commission_value_delivered = fields.Float(string="Driver commission value delivery",track_visibility="on_change",default=-1)

    affiliator = fields.Many2one('rb_delivery.user','Affiliator' , track_visibility="on_change",default=_default_affiliator_user)

    account_manager_id= fields.Many2one('rb_delivery.user', 'Account Manager',track_visibility="on_change",domain=[('role_code','not in',['rb_delivery.role_business','rb_delivery.role_driver','rb_delivery.role_delivery_company','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])])

    account_manager_mobile = fields.Char('Account Manager Mobile',compute='_compute_account_manager_mobile')

    picked_up_by_pricing_type=fields.Selection([('fixed', 'Fixed'),('percentage', 'Percentage'),('pricelist','Pricelist')], string="Picked Up By Pricing Type",track_visibility="on_change")

    picked_up_by_pricing_fixed_value=fields.Float(string="Picked Up By Fixed value",track_visibility="on_change")

    picked_up_by_pricing_percentage_value=fields.Integer(string="Picked Up By Percentage value",track_visibility="on_change")

    picked_up_by_pricing_pricelist_id = fields.Many2one('rb_delivery.pricelist', 'Picked Up By Select Pricelist',track_visibility="on_change")

    number_of_orders = fields.Integer(string='Number of orders',track_visibility="on_change",compute="_compute_order_count",readonly=True, store=True)

    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    _sql_constraints = [('mobile_number', 'check(1=1)', 'Mobile Number already exists!'),('email', 'check(1=1)', 'No constraint')]


    @api.depends('user_parent_id','user_parent_id.hide_totals_from_dashboard')
    @api.multi
    def _compute_hide_totals_from_dashboard_parent(self):
        for rec in self:
            if rec.sudo().user_parent_id and rec.sudo().user_parent_id.hide_totals_from_dashboard:
                rec.hide_totals_from_dashboard_parent = True
            else:
                rec.hide_totals_from_dashboard_parent = False

    @api.onchange('location_link')
    def _onchange_location_link(self):
        if self.location_link:
            expanded_url = self.env['rb_delivery.order'].expand_google_maps_short_url(self.location_link)
            coords = self.env['rb_delivery.order'].extract_coordinates_from_url(expanded_url)
            if coords:
                self.latitude, self.longitude = coords

    @api.multi
    def _compute_order_count(self):
        for rec in self:
            if rec.role_code == 'rb_delivery.role_business':
                orders_count = self.env['rb_delivery.order'].search_count(['|','|','|',('assign_to_business', '=', rec.id),('receiver_business','=',rec.id),('assign_to_business', 'in', rec.child_ids.ids),('receiver_business','in',rec.child_ids.ids)])
            else:
                orders_count = 0
            rec.order_count=orders_count

    def get_redirected_url(self,url):
        response = requests.head(url, allow_redirects=True)
        return response.url

    # Function to extract coordinates from a Google Maps URL
    def extract_coordinates(self,url):
        pattern = r'@([-0-9.]+),([-0-9.]+),([0-9.]+)z'
        full_url = self.get_redirected_url(url)
        match = re.search(pattern, full_url)
        if match:
            latitude = match.group(1)
            longitude = match.group(2)
            return latitude, longitude
        else:
            return None

    @api.onchange('latitude', 'longitude')
    def _onchange_lat_long(self):
        if self.latitude and self.longitude:
            self.location_link = self._generate_location_link(self.latitude, self.longitude)

    def _generate_location_link(self, latitude, longitude):
        return f'https://maps.google.com/?q={latitude},{longitude}'

    @api.multi
    def get_onboarding_errors(self):
        for rec in self:
            onboarding_error_ids = self.env['rb_delivery.onboarding_error'].sudo().search([('record_id','=',rec.id),('model_name','=','rb_delivery.user')])
            rec.onboarding_error_ids = [(6,0,onboarding_error_ids.ids)]

    @api.onchange('address_tag')
    @api.constrains('address_tag')
    def change_address_with_tag(self):
        if self.address_tag:
            if self.address_tag.area_id:
                self.area_id = self.address_tag.area_id.id
            if self.address_tag.sub_area_id:
                self.sub_area = self.address_tag.sub_area_id.id

    def get_onboarding_error_ids(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_onboarding_error').id
        domain = [('id', 'in', self.onboarding_error_ids.ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Onboarding errors',
            'res_model': 'rb_delivery.onboarding_error',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}

    #api one so we have one record
    #it seems having parent return two records here ( child and parent)
    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_business = user.has_group('rb_delivery.role_business')
        self.is_base = user.has_group('base.group_system')
        self.is_super_manager = user.has_group('rb_delivery.role_super_manager')
        self.is_manager = user.has_group('rb_delivery.role_manager') or user.has_group('rb_delivery.role_picking_up_manager') or user.has_group('rb_delivery.role_distribution_manager')
        self.is_sales = user.has_group('rb_delivery.role_sales')


    def get_field_ids(self):
        user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        fields_ids = []
        for field in user.field_ids:
            fields_ids.append(field.name)
        return {'field_ids':fields_ids,'is_block_delivery_fee':user.is_block_delivery_fee,'is_block_delivery_profit':user.block_delivery_profit,'role_code':user.role_code,"show_fields_button":user.show_fields_button}

    def write_fields(self, field_names):
        user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        model_id = self.env['ir.model'].sudo().search([('model','=','rb_delivery.order')]).id
        field_ids = []
        for field in field_names:
            field_id = self.env['ir.model.fields'].sudo().search([('model_id','=',model_id),('name','=',field)]).id
            if field_id and field_id not in user.field_ids.ids or len(field_names) != len(user.field_ids):
                field_ids.append(field_id)
        if field_ids != []:
            user.write({'field_ids':[(6,0,field_ids)]})




    @api.one
    def check_user_business(self):
        group_id = self.group_id
        if group_id.code == 'rb_delivery.role_business':
            self.user_is_business = True

    @api.one
    def check_user_driver(self):
        group_id = self.group_id
        if group_id.code == 'rb_delivery.role_driver':
            self.user_is_driver = True

    @api.one
    def check_manager(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        self.is_password_manager = user.password_manager
        self.is_pricelist_manager = user.pricelist_manager
        self.is_role_manager = user.role_manager
        self.is_confirm_user_manager = user.confirm_user_manager

    @api.onchange('replace_company_logo_with_business_logo')
    def business_logo_visibility(self):
        if self.replace_company_logo_with_business_logo :
            self.print_user_logo_in_bolisa = False

    @api.onchange('print_user_logo_in_bolisa')
    def business_logo_in_bolisa_visibility(self):
        if self.print_user_logo_in_bolisa:
            self.replace_company_logo_with_business_logo = False

    @api.one
    def check_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        self.is_block_delivery_fee = user.block_delivery_fee

    @api.one
    def check_security_manager(self):
        self.is_security_manager  = self.user_id.has_group('rb_delivery.role_security_manager')

    def _check_number(self,number):
        mobile_digits = self.env['rb_delivery.client_configuration'].get_param('mobile_number_digit')
        try:
            digits = mobile_digits.split('-')
            if len(digits)==1:
                min_digit = digits[0]
                max_digit = digits[0]
            else:
                min_digit = digits[0]
                max_digit = digits[1]
        except:
            min_digit = 10
            max_digit = 10

        if number and ( len(str(number)) < int(min_digit) or len(str(number)) > int(max_digit)):
            if min_digit == max_digit:
                self.env['rb_delivery.error_log'].raise_olivery_error(500,self.id,{'min_digits': str(min_digit)})
                #raise ValidationError(('Mobile number must be %s digits only')%(str(min_digit)))
            self.env['rb_delivery.error_log'].raise_olivery_error(501,self.id,{'min_digits': str(min_digit), 'max_digit': str(max_digit)})
            # raise ValidationError(('Mobile number must be within %s and %s digits')%(str(min_digit),str(max_digit)))

    @api.constrains('password')
    @api.one
    def _check_password(self):
        if self.password and len(str(self.password)) < 8:
            self.env['rb_delivery.error_log'].raise_olivery_error(502,self.id,{})
            #raise ValidationError(('Password must be at least 8 characters or longer'))


    @api.onchange('country_id')
    def _domain_area(self):
        ids=[]
        if self.country_id:
            areas = self.env['rb_delivery.area'].search([('country_id', '=', self.country_id.id)])
            for area in areas:
                ids.append(area.id)
        else:
            self.area_id = False
            self.sub_area = False
            self.address_tag = False
        if len(ids)>0:
            return {'domain': {'area_id': [('id', 'in', ids),('show_in_register', '=', True)]}}
        else:
            self.area_id = False
            self.sub_area = False
            self.address_tag = False
            return {'domain': {'area_id': [('show_in_register', '=', True)]}}

    @api.onchange('area_id')
    def _check_area(self):
        if self.area_id.country_id.id :
            self.country_id = self.area_id.country_id.id

        if(self.sub_area and self.area_id.id != self.sub_area.parent_id.id):
            self.sub_area = False

        if(self.address_tag and self.area_id.id != self.address_tag.area_id.id):
            self.address_tag = False

    @api.onchange('sub_area')
    def _check_sub_area(self):
        if(self.sub_area and self.sub_area.id != self.address_tag.sub_area_id.id):
            self.address_tag = False


    # ----------------------------------------------------------------------
    # Create, Update, Delete
    # ----------------------------------------------------------------------

    @api.one
    @api.depends('group_id')
    def _compute_role_name(self):
        domain = [('model', '=', 'res.groups'), ('res_id', '=', self.group_id.id)]
        model_data = self.env['ir.model.data'].search(domain, limit=1)
        xml_id = "%s.%s" % (model_data.module, model_data.name)
        self.role_code = xml_id

    def copy(self, default=None):

        # Block duplication for all users except super managers
        if not self.env.user.has_group('rb_delivery.role_super_manager'):
            self.env['rb_delivery.error_log'].raise_olivery_error(581,self.id,{})
        
        mobile_digits = self.env['rb_delivery.client_configuration'].get_param('mobile_number_digit')
        digits = mobile_digits.split('-')
        copy_mobile = ''
        if len(digits) == 2:
            max_digits = digits[1]
        else:
            max_digits = 10
        copy_mobile = copy_mobile.ljust(int(max_digits), 'x')
        default = dict(default or {})
        default.update(
            { 'mobile_number':copy_mobile,
             'second_mobile_number':copy_mobile,
             'username': self.username + '_copy',
             'state':'pending',
             'user_id':False,
             'commercial_name':'',
             'password':''})

        return super(rb_delivery_user, self).copy(default)


    def check_pricelist_with_areas(self,pricelist,sender_area):
        area = self.env['rb_delivery.area'].sudo().search([('id','=',sender_area)])
        areas = self.env['rb_delivery.area'].sudo().search([]).mapped('name')
        pricelist_areas = pricelist.pricelist_item_ids.filtered(lambda x: x.from_area.id == sender_area).mapped('to_area').mapped('name')
        model_name = 'rb_delivery.user'
        if len(pricelist.pricelist_item_ids) == 0:
            message = (_('There is no pricing at all for "%s" pricelist.')%(pricelist.name))
            what_to_do_next = (_('Go to pricelist "%s" and add pricing from "%s" to all areas.')%(pricelist.name,area.name))
            data = {'message':message,'what_to_do_next':what_to_do_next,'error_code':'user_01','record_id':self.id,'model_name':model_name}
            self.env['rb_delivery.onboarding_error'].create_onboarding_error(data)
            return
        if len(pricelist_areas) == 0:
            message = (_('There is no pricing from area "%s" in pricelist "%s".')%(area.name,pricelist.name))
            what_to_do_next = (_('Go to pricelist "%s" and add pricing from "%s" to all areas.')%(pricelist.name,area.name))
            data = {'message':message,'what_to_do_next':what_to_do_next,'error_code':'user_01','record_id':self.id,'model_name':model_name}
            self.env['rb_delivery.onboarding_error'].create_onboarding_error(data)
            return
        if not set(areas).issubset(set(pricelist_areas)):
            message = (_('Not all areas are mapped with area "%s" in pricelist "%s".')%(area.name,pricelist.name))
            what_to_do_next = (_('Go to pricelist "%s" and add pricing from "%s" to all areas.')%(pricelist.name,area.name))
            data = {'message':message,'what_to_do_next':what_to_do_next,'error_code':'user_01','record_id':self.id,'model_name':model_name}
            self.env['rb_delivery.onboarding_error'].create_onboarding_error(data)
            return

        return

    def check_pricelist(self, values):
        if values.get('pricelist_id'):
            if str(values['pricelist_id']).isnumeric():
                pricelist_id = self.env['rb_delivery.pricelist'].search([('id','=',values['pricelist_id'])])
                if pricelist_id and pricelist_id.id:
                    values['pricelist_id'] = pricelist_id.id
                else:
                    pricelist_id = self.env['rb_delivery.pricelist'].search([('code','=',values['pricelist_id'])])
                    if pricelist_id and pricelist_id.id:
                        values['pricelist_id'] = pricelist_id.id
                    else:
                        self.env['rb_delivery.error_log'].raise_olivery_error(531,self.id,{'pricelist': str(values['pricelist_id'])})
                        #raise Warning(_("Pricelist "+ values['pricelist_id']+" does not exist in the system."))
            else:
                pricelist_id = self.env['rb_delivery.pricelist'].search([('code','=',values['pricelist_id'])])
                if pricelist_id:
                    values['pricelist_id'] = pricelist_id.id
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(531,self.id,{'pricelist': str(values['pricelist_id'])})
                    #raise Warning(_('Pricelist '+ values['pricelist_id']+' does not exist in the system.'))
        else:
            pricelist_id = self.pricelist_id
        if pricelist_id:
            group_id = values.get('group_id') or self.group_id.id
            role_code = self.env['res.groups'].sudo().browse(group_id).code
            if role_code == 'rb_delivery.role_business':
                area_id = values.get('area_id') or self.area_id.id
                self.check_pricelist_with_areas(pricelist_id,area_id)

    def check_sub_area(self, values,area):
        if str(values['sub_area']).isnumeric():
            sub_area = self.env['rb_delivery.sub_area'].search(['|',('id','=',values['sub_area']),('code','=',values['sub_area']),'|',('parent_id','=',area),('area_parent_id','=',area)])
            if sub_area and sub_area.id:
                values['sub_area'] = sub_area.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(532,self.id,{'area_sub': str(values['sub_area'])})
                #raise Warning(_("Sub area "+ str(values['sub_area'])+ " does not exist in the system."))
        else:
            sub_area = self.env['rb_delivery.sub_area'].search(['|',('code','=',values['sub_area']),('name','=',values['sub_area']),'|',('parent_id','=',area),('area_parent_id','=',area)])
            if sub_area:
                values['sub_area'] = sub_area.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(532,self.id,{'area_sub': str(values['sub_area'])})
                #raise Warning(_('Sub Area '+ values['sub_area']+' does not exist in the system.'))

    def _check_mobile(self,org_values):
        if 'mobile_number' in org_values and org_values['mobile_number'] and 'from_import' in org_values and org_values['from_import']:
            user = self.env['rb_delivery.user'].search([('mobile_number','=',org_values['mobile_number'])],limit=1)
            if len(user)==0:
                return False
            else:
                if "message_attachment_count" in org_values:
                    org_values.pop("message_attachment_count")
                if "__last_update" in org_values:
                    org_values.pop("__last_update")

                if "state" in org_values:
                    org_values.pop("state")

                if "assign_to_agent" in org_values:
                    org_values.pop("assign_to_agent")

                if "assign_to_distributor_date" in org_values:
                    org_values.pop("assign_to_distributor_date")

                if "sequence" in org_values:
                    org_values.pop("sequence")


                user.write(org_values)

                return user

    def check_sub_area_map(self,sub_area_maps):
        checked_area_names = []
        for sub_area_map in sub_area_maps:
            if sub_area_map.name in checked_area_names:
                self.env['rb_delivery.error_log'].raise_olivery_error(503,self.id,{'name': str(sub_area_map.name)})
                #raise ValidationError(_("Name %s is duplicated in area map list, the name should be unique.") % sub_area_map.name)
            #checked_area_names.append(sub_area_map.name)

    # inherit module[olivery_branch_collection]
    @api.model
    def create(self, values):
        if 'username' not in values:
            self.env['rb_delivery.error_log'].raise_olivery_error(544,self.id,{})

        if 'address' not in values:
            self.env['rb_delivery.error_log'].raise_olivery_error(545,self.id,{})

        if 'commercial_name' in values and not 'username' in values:
            values['username']=values['commercial_name']
        if 'sub_area' in values and values['sub_area']:
            if 'area_id' in values and values['area_id']:
                self.check_sub_area(values,values['area_id'])
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(533,self.id,{})
                #raise Warning(_("Please add area first."))

        if 'area_id' in values and values['area_id']:
            if str(values['area_id']).isnumeric():
                area = self.env['rb_delivery.area'].search([('id','=',values['area_id'])])
                if not area:
                    area = self.env['rb_delivery.area'].search([('code','=',values['area_id'])])
            else:
                area = self.env['rb_delivery.area'].search(['|',('name','=',values['area_id']),('code','=',values['area_id'])])
            if area and area.id:
                values['area_id'] = area.id
            elif 'for_app_stores_use' in values and values['for_app_stores_use']:
                pass
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(504,self.id,{'name': str(values['area_id'])})
                #raise ValidationError(_("Area does not exist"))
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(533,self.id,{})
        if 'email' in values and values['email'] :
            self.check_email_arabic_ascii(values['email'])
            user = self.env['rb_delivery.user'].sudo().search([('email','=',values['email'])])
            if len(user)>0:
                self.env['rb_delivery.error_log'].raise_olivery_error(505,self.id,{'email': values['email']})
                #raise ValidationError(_('Email already exists!'))
        if 'mobile_number' in values and values['mobile_number']:
            self.check_mobile_number(values['mobile_number'])
            self.update_whatsapp(values)
            self._check_number(values['mobile_number'])
        else:
             self.env['rb_delivery.error_log'].raise_olivery_error(543,self.id,{})

        if 'second_mobile_number' in values and values['second_mobile_number']:
            self._check_number(values['second_mobile_number'])

        if 'commercial_name' not in values or ('commercial_name' in values and  not values['commercial_name']):
            if 'username' in values and values['username']:
                values['commercial_name'] = values['username']
        if 'group_id' in values:
            values = self.set_default_access_groups(values)
            group_name=self.env['res.groups'].sudo().search([('id','=',values['group_id'])]).name
            user = self.env['res.users'].search([('id', '=', self._uid)])
            is_super_manager = user.has_group('rb_delivery.role_super_manager')
            if 'Super Manager' in group_name and not ( is_super_manager or  SUPERUSER_ID==self._uid ):
                self.env['rb_delivery.error_log'].raise_olivery_error(506,self.id,{'new_user_role': values['group_id']})
                #raise ValidationError('You do not have access')

        if 'password' in values and values['password']:
            if len(str(values['password'])) < 8:
                self.env['rb_delivery.error_log'].raise_olivery_error(502,self.id,{})
                #raise ValidationError(('Password must be at least 8 characters or longer'))
        _logger.critical(values)
        if values.get('username'):
            values['username'] = values.get('username').strip()
        if values.get('mobile_number'):
            values['mobile_number'] = values.get('mobile_number').strip()
        if values.get('commercial_name'):
            values['commercial_name'] = values.get('commercial_name').strip()
        is_exist = self._check_mobile(values)
        if is_exist:
            return is_exist
        else:
            # create new sequence
            user_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.user')
            values['user_sequence'] = user_sequence
            user = super(rb_delivery_user, self).create(values)
            self.env['rb_delivery.action'].notify_for_action_type('for_action','send_on_registration',object=user,with_delay=True)
            #self.env['rb_delivery.utility'].send_toast('for_all', ['rb_delivery.user'],self._uid)
            if values.get('pricelist_id') or values.get('area_id'):
                user.check_pricelist(values)
            if 'sub_area_map' in values and values['sub_area_map']:
                self.check_sub_area_map(user.sub_area_map)
            if not user.user_id and user.for_app_stores_use:
                user.wkf_action_confirm()
            return user

    def check_mobile_number(self,mobile_number, archive=0):
        active_users = self.env['rb_delivery.user'].sudo().search([('mobile_number','=',mobile_number),('active','=',True)])
        deactive_users = self.env['rb_delivery.user'].sudo().search([('mobile_number','=',mobile_number),('active','=',False)])

        if active_users and len(active_users)>0:
            self.env['rb_delivery.error_log'].raise_olivery_error(507,self.id,{'mobile_number': mobile_number})
            #raise ValidationError(_('Mobile Number already exists!'))

        elif deactive_users and len(deactive_users) > archive :
            self.env['rb_delivery.error_log'].raise_olivery_error(508,self.id,{'user_name': deactive_users[0].username,'archive_date': str(deactive_users[0].write_date),'archiving_user': str(deactive_users[0].write_uid.display_name), 'mobile_number': mobile_number})
            #raise ValidationError(_('This user ' + deactive_users[0].username + ' archived on ' + str(deactive_users[0].write_date) + ' by ' + str(deactive_users[0].write_uid.display_name)))

        else:
            return True

    def set_res_user_role(self,field,role):
        if self.user_id.id:
            if not self[field]:
                group_id = self.env.ref(role).id
                b_group_id = "in_group_" + str(group_id)
                self.user_id.sudo().write({b_group_id: True})

            if self[field]:
                group_id = self.env.ref(role).id
                b_group_id = "in_group_" + str(group_id)
                self.user_id.sudo().write({b_group_id: False})

    #inherit module [olivery_custody]
    # inherit module[olivery_branch_collection]
    @api.one
    def write(self, values):
        if self.for_app_stores_use and 'state' in values and values['state']!='confirmed':
            self.env['rb_delivery.error_log'].raise_olivery_error(511,self.id,{'user': self.env.user.rb_user.username})
            #raise ValidationError(_('This User Can\'t Be Deactivated'))
        if 'area_id' in values or 'address' in values or 'mobile_number' in values:
            all_orders = self.env['rb_delivery.order'].sudo().search_count(['|',('assign_to_agent','=',self.id),('assign_to_business','=',self.id)])
            accessed_orders = self.env['rb_delivery.order'].search_count(['|',('assign_to_agent','=',self.id),('assign_to_business','=',self.id)])
            if all_orders > accessed_orders:
                self.env['rb_delivery.error_log'].raise_olivery_error(512,self.id,{'user': self.env.user.rb_user.username})
                #raise ValidationError(_("You can not edit this user since info is used in previous orders."))
        if 'area_id' in values and values['area_id']:
            if str(values['area_id']).isnumeric():
                area = self.env['rb_delivery.area'].search([('id','=',values['area_id'])])
                if not area:
                    area = self.env['rb_delivery.area'].search([('code','=',values['area_id'])])
            else:
                area = self.env['rb_delivery.area'].search(['|',('name','=',values['area_id']),('code','=',values['area_id'])])
            if area and area.id:
                values['area_id'] = area.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(504,self.id,{'name': str(values['area_id'])})
                #raise ValidationError(_("Area does not exist"))
        if values.get('pricelist_id') or values.get('area_id'):
            self.check_pricelist(values)
        email_registration = self.env['rb_delivery.client_configuration'].get_param('email_registration')

        if values.get('email') and values.get('email') != self.email:
            self.check_email_arabic_ascii(values['email'])
            user = self.env['rb_delivery.user'].sudo().search([('email','=',values['email'])])
            if user and user.id:
                self.env['rb_delivery.error_log'].raise_olivery_error(505,self.id,{'email': values['email']})
                #raise ValidationError(_('Email already exists!'))
            if self.user_id.id:
                self.user_id.sudo().email = values['email']
                if email_registration:
                    self.user_id.sudo().login = values['email']

        if 'mobile_number' in values and values['mobile_number']:
            self.check_mobile_number(values['mobile_number'])
            self.update_whatsapp(values)
            self._check_number(values['mobile_number'])
            values['mobile_number'] = values['mobile_number'].strip()
            if self.user_id.id and not email_registration:
                self.user_id.sudo().login = values['mobile_number']

        if 'group_id' in values and values['group_id']:
            self.default_show_location()
        if('password' in values and 'user_id' in values):
            # add new password to user
            self.env['res.users'].search([('id', '=', values['user_id'])]).sudo().write({
                'password': values['password']})
            # remove the old password from the old user
            self.env['res.users'].search(
                [('id', '=', self.user.id)]).sudo().write({'password': ''})

        # only password has changed
        elif('password' in values):
            # add the new password to the user
            self.user_id.sudo().write({'password': values['password']})
            self.message_post(body=("Password was updated."))

        # only user has changed
        elif('user' in values):
            # add the new password to the user
            self.env['res.users'].search([('id', '=', values['user_id'])]).sudo().write({
                'password': self.password})
            # remove the old password from the old user
            self.env['res.users'].search(
                [('id', '=', self.user.id)]).sudo().write({'password': ''})

        if 'group_id' in values and values['group_id']:
            values = self.set_default_access_groups(values)
            b_group_id = "in_group_" + str(self.group_id.id)
            if self.user_id.id and self.group_id and self.group_id.id:
                self.user_id.sudo().write({b_group_id: False})
                new_group_id = "in_group_" + str(values['group_id'])
                self.user_id.sudo().write({new_group_id: True})

        if 'pricelist_edit_manager' in values:
            self.set_res_user_role('pricelist_edit_manager','rb_delivery.role_pricelist_manager')

        if 'collection_manager' in values:
            self.set_res_user_role('collection_manager','rb_delivery.role_collection_manager')

        if 'returned_collection_manager' in values:
            self.set_res_user_role('returned_collection_manager','rb_delivery.role_returned_collection_manager')

        if 'settings_manager' in values:
            self.set_res_user_role('settings_manager','rb_delivery.role_settings_manager')

        if 'block_delivery_fee' in values:
            self.set_res_user_role('block_delivery_fee','rb_delivery.role_olivery_block_delivery_fee')

        if 'state' in values and values['state']:
            if values['state'] == 'confirmed':
                self.env['rb_delivery.action'].get_confirm_action(self.mobile_number,self.id)
                self.sudo().create_new_user()
            elif values['state'] == 'reconfirmed':
                self.user_id.sudo().active = True
                self.active = True
            else:
                self.user_id.sudo().active = False
                self.active = False
                self.user_id.access_token_ids.sudo().unlink()

            if 'rb_delivery.role_super_manager' == self.group_id.code:
                values['pricelist_edit_manager'] = True
                self.set_res_user_role('pricelist_edit_manager','rb_delivery.role_pricelist_manager')

        #self.env['bus.bus'].sendone('auto_refresh', 'rb_delivery.user')
        if 'branch_id' in values and values['branch_id']:
            self.env['ir.rule'].clear_cache()

        if 'pricelist_id' in values and values['pricelist_id']:
            if self.pricelist_id:
                values['previous_pricelist_id'] = self.pricelist_id.id
        if 'second_mobile_number' in values and values['second_mobile_number']:
            self._check_number(values['second_mobile_number'])
        if 'field_ids' in values and values['field_ids']:
            changed_fields = []
            origin_fields = self.env['ir.model.fields'].sudo().browse(self.field_ids.ids).mapped('name')
            if len(values['field_ids'][0]) > 0 and values['field_ids'][0][2] :
                changed_fields = self.env['ir.model.fields'].sudo().browse(values['field_ids'][0][2]).mapped('name')
            self.message_post(body=_("Fields to shown changed by %s on %s from %s to %s") % (self.env.user.name,datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S"),origin_fields if len(origin_fields) > 0 else 'empty',changed_fields if len(changed_fields) > 0 else 'empty'))
        if self.user_is_driver and 'online' in values and values['online']==True and self.player_id==False:
            raise ValidationError(_("You cannot set the driver to online while the player ID is not assigned."))
        if 'player_id' in values and values['player_id'] and self.player_id != values['player_id']:
            # Search for users with the same player_id
            users_with_same_player_id = self.env['rb_delivery.user'].sudo().search([('player_id', '=', values['player_id'])])
            # If multiple users are found, set their 'online' status to False (offline)
            if users_with_same_player_id:
                users_with_same_player_id.write({'online': False,'player_id' : False})
        if values.get('username'):
            values['username'] = values.get('username').strip()
        if values.get('mobile_number'):
            values['mobile_number'] = values.get('mobile_number').strip()
        if values.get('commercial_name'):
            values['commercial_name'] = values.get('commercial_name').strip()
        if values.get('location_link'):
            expanded_url = self.env['rb_delivery.order'].expand_google_maps_short_url(values.get('location_link'))
            coords = self.env['rb_delivery.order'].extract_coordinates_from_url(expanded_url)
            if coords:
                values['latitude'], values['longitude'] = coords

        if values.get('latitude') or values.get('longitude'):
            values['latitude'] = values.get('latitude') or self.latitude
            values['longitude'] = values.get('longitude') or self.longitude
            values['location_link'] = self._generate_location_link(values['latitude'], values['longitude'])

        return super(rb_delivery_user, self).write(values)

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        clean_name = re.sub(r'[,]', '', str(name))

        domain = ['|', '|', ('username', operator, clean_name), ('commercial_name', operator, clean_name), ('mobile_number', operator, clean_name)]
        if str(clean_name).isnumeric():
            domain.insert(0, '|')
            domain.extend([('id', operator, clean_name)])
        recs = self.search(domain + args, limit=limit)

        if not recs.ids:
            return super(rb_delivery_user, self).name_search(name=name, args=args, operator=operator, limit=limit)
        return recs.name_get()

    def check_email_arabic_ascii(self,email):
        ascii_chars = set(string.printable)
        arabic_chars = set(chr(i) for i in range(0x0621, 0x064B))
        email_chars = set(email)
        if arabic_chars.intersection(email_chars):
            self.env['rb_delivery.error_log'].raise_olivery_error(513,self.id,{'email': email})
            #raise ValidationError(_("The email field must not contain Arabic characters."))
        elif not email_chars.issubset(ascii_chars):
            self.env['rb_delivery.error_log'].raise_olivery_error(514,self.id,{'email': email})
            #raise ValidationError(_("The email field must only contain ASCII characters."))

    @api.multi
    def name_get(self):
        result = []
        for user in self:
            if user.commercial_name:
                name = user.commercial_name
            else:
                name = user.username
            result.append((user.id, name))
        return result

    def get_session_id(self):
        return self.env['ir.http'].session_info().get('session_id')

    @api.onchange('username')
    def _compute_username(self):
        if self.user_id:
            self.user_id.sudo().write({"name" : self.username})

    @api.model
    def can_see_logs(self):
        restricted_group_ids = self.env['rb_delivery.client_configuration'].sudo().get_param('roles_who_can_not_see_order_logs')

        if not restricted_group_ids:
            return True


        user_groups = self.env.user.groups_id.ids
        has_restricted_group = any(group_id in user_groups for group_id in restricted_group_ids)

        return not has_restricted_group


    @api.one
    def unlink(self):
        user = self.env['res.users'].search([('id', '=', self.user_id.id)])
        user.sudo().unlink()
        #self.env['rb_delivery.utility'].send_toast('for_all', ['rb_delivery.user'],self._uid)
        return super(rb_delivery_user, self).unlink()

    def set_default_access_groups(self,values):
        group = self.env['res.groups'].sudo().browse(values['group_id'])

        if group.exists() and 'rb_delivery.role_super_manager' == group.code:
            values['password_manager'] = True
            values['pricelist_manager'] = True
            values['role_manager'] = True
            values['confirm_user_manager'] = True
            # price_list_manager can only be added if the user is also in res.users
            if self.state == 'confirmed' or self.state == 'reconfirmed':
                values['pricelist_edit_manager'] = True

        return values

    @api.model
    def load_views(self, views, options=None):
        res =  super(rb_delivery_user, self).load_views(views, options)
        res['fields']['password']['exportable'] = False
        res['fields']['password']['selectable'] = False
        res['fields']['password']['searchable'] = False
        res['fields']['password']['sortable'] = False
        res['fields']['company_password']['exportable'] = False
        res['fields']['company_password']['selectable'] = False
        res['fields']['company_password']['searchable'] = False
        res['fields']['company_password']['sortable'] = False
        return res

    @api.model
    def load(self, import_fields, data):
        if 'mobile_number' in import_fields:
            import_fields.append('from_import')
            index = import_fields.index('mobile_number')
            for rec in data:
                if rec[index]:
                    rec[index] = rec[index].strip()
                    user = self.env['rb_delivery.user'].search([('mobile_number','=',rec[index])],limit=1)
                    if user and user.id:
                        rec.append("TRUE")
                    else:
                        rec.append("FALSE")

        index = 0
        for rec in data:
            index = 0
            for field in rec:
                rec[index] = rec[index].strip()
                index = index + 1

        users = super(rb_delivery_user, self).load(import_fields, data)
        return users


    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    @api.one
    def wkf_action_reopen(self):
        # TODO need to add role and when this should be used
        self.write({'state': 'pending'})

    def check_order(self):
        business_state_ids = []
        statuses_allow_to_archive_business_ids =self.env['rb_delivery.client_configuration'].get_param('default_statuses_to_be_excluded_on_check_for_archive')
        if self.user_is_business:
            if statuses_allow_to_archive_business_ids:
                business_state_ids = statuses_allow_to_archive_business_ids
            business_orders = self.env['rb_delivery.order'].sudo().search([('assign_to_business', '=', self.id), ('state_id', 'not in', business_state_ids)])
            if business_orders:
                return True
        return False
    @api.one
    def wkf_action_archive(self):
        # TODO need to add role and when this should be used
        if self.check_order():
            self.env['rb_delivery.error_log'].raise_olivery_error(515,self.id,{'user': self.username})
            #raise ValidationError(_("You Cannot deactivate %s since the user has uncompleted orders.\n What you can do: Go to orders and filter on this business name, finish process and make sure all orders are processed before deactivating.")%(self.username))
        if self.user_id:
            self.write({'active': False,'state': 'deactivate'})
        else:
            self.write({'active': False})

    @api.one
    def wkf_action_unarchive(self):
        # TODO need to add role and when this should be used
        if self.user_id:
            self.write({'active': True,'state': 'reconfirmed'})
        else:
            self.write({'active': True})

    @api.one
    def wkf_action_cancel(self):
        # TODO need to add role and when this should be used
        self.write({'state': 'canceled'})

    @api.one
    def wkf_action_confirm(self):
        # TODO need to add role and when this should be used
        self.write({'state': 'confirmed'})

    def action_confirm(self):
        if self.password:
            self.wkf_action_confirm()
        else:
            address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_add_password').id
            context = {'default_user_id': self.user_id.id}
            return {
                'type': 'ir.actions.act_window',
                'name': 'Add password',
                'res_model': 'rb_delivery.add_password',
                'view_type': 'form',
                'view_mode': 'form',
                'views': [(address_form_id, 'form')],
                'target': 'new',
                'context': context,
                }

    @api.one
    def wkf_action_reconfirm(self):
        # TODO need to add role and when this should be used

        self.write({'state': 'reconfirmed'})

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        domain = ['|','|','|',('assign_to_business', '=', self.id),('receiver_business','=',self.id),('assign_to_business', 'in', self.child_ids.ids),('receiver_business','in',self.child_ids.ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Orders',
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'context':{'inside_users_page':True,'default_business_id':self.id,'default_child_business_id':self.child_ids.ids},
            'target': 'current',
            'domain': domain}

    def get_notification(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_notification_center').id
        domain = [('user_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Notifications',
            'res_model': 'rb_delivery.notification_center',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def create_new_user(self):
        default_timezone = self.env['rb_delivery.client_configuration'].get_param('default_time_zone')
        default_language = self.env['rb_delivery.client_configuration'].get_param('default_language_setting')
        if self.group_id:
            b_group_id = "in_group_" + str(self.group_id.id)
        if self.username and self.password:
            email_registration = self.env['rb_delivery.client_configuration'].sudo().get_param('email_registration')
            account_number_registration = self.env['rb_delivery.client_configuration'].sudo().get_param('account_number_registration')
            values = {'password': self.password, 'name': self.username, 'login': self.mobile_number, 'email': self.email or '<EMAIL>', b_group_id: True, 'sel_groups_1': 1,'tz': default_timezone, 'active': True}
            if account_number_registration and self.user_sequence:
                values['login'] = self.user_sequence
            elif email_registration and self.email:
                values['login'] = self.email
            res = self.env['res.users'].sudo().create(values)
            self.user_id = res.id
            res.lang = default_language
        else:
            return False

    @api.one
    def wkf_migrate(self):
        users=self.env['rb_delivery.user'].search([])
        for user in users:
            user.write({'commercial_name': user.username, 'commercial_number': user.mobile_number})

    @api.one
    def wkf_action_deactivate(self):
        if self.check_order():
            self.env['rb_delivery.error_log'].raise_olivery_error(515,self.id,{'user': self.username})
            #raise ValidationError(_("You Cannot deactivate %s since the user has uncompleted orders.\n What you can do: Go to orders and filter on this business name, finish process and make sure all orders are processed before deactivating.")%(self.username))
        self.write({'state': 'deactivate'})

    def toggle_active(self):
        if self.active:
            self.wkf_action_archive()
        else:
            self.wkf_action_unarchive()


    @api.one
    def wkf_mobile_action_deactivate(self):
        if self.user_id:
            self.sudo().user_id.active = False
        self.write({'state': 'deactivate'})
        return True


    @api.one
    def wkf_mobile_action_reconfirm(self):
        # TODO need to add role and when this should be used
        if self.user_id:
            self.sudo().user_id.active = True
        self.write({'state': 'reconfirmed'})
        return True

    def sync_areas(self):

        areas_arr = []
        areas = self.env['rb_delivery.area'].search([])
        area_map = self.area_map
        for area in areas:
            areas_arr.append(area.name)
        for area in area_map:
            areas_arr.append(area.name)
        partner_areas = []
        params = {
            "jsonrpc": "2.0",
            "params": {
                "login":self.company_username,
                "password": self.company_password,
                "db":self.company_db}}
        headers = {'content-type': 'application/json'}
        response = requests.post(self.company_url+'/get_areas', data=json.dumps(params), headers=headers)
        if response.status_code == 200:
            if response.json().get('result') and response.json().get('result').get('response'):
                result = response.json().get('result').get('response')
                if len(result)>0:
                    for area in result:
                        if 'name' in area and area['name']:
                            partner_areas.append(area['name'])
        if len(partner_areas)>0:
            intersect_areas = list(set(partner_areas) & set(areas_arr))
            default_area = self.env.ref('rb_delivery.area_vhub_default').id
            if default_area:
                for partner_area in partner_areas:
                    if partner_area not in intersect_areas:
                        area_map = self.env['rb_delivery.area_map'].sudo().create({'area_id':default_area,'name':partner_area})
                        if area_map:
                            self.area_map = [(4,area_map.id)]
        return

    def change_password(self):
        super_manager = self.env.user.has_group('rb_delivery.role_super_manager')
        if not self.user_id or (not super_manager and self.role_code not in ['rb_delivery.role_business', 'rb_delivery.role_driver',  'rb_delivery.role_picking_up_representative', 'rb_delivery.role_sort_and_distribute_representative']):
            self.env['rb_delivery.error_log'].raise_olivery_error(516, self.id, {})

        view = self.env.ref(
            'rb_delivery.view_form_rb_delivery_change_password')
        context = {'default_user_id': self.user_id.id}
        return {
            'name': 'Change Password',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'rb_delivery.change_password',
            'views': [(view.id, 'form')],    # view id and type
            'view_id': view.id,  # view id
            'target': 'new',
            'context': context
            # 'domain': domain

        }

    def get_location(self):
        if self.latitude and self.longitude:
            return {
                'name'     : 'Go to website',
                'res_model': 'ir.actions.act_url',
                'type'     : 'ir.actions.act_url',
                'target'   : '_blank',
                'url'      : "https://www.google.com/maps/search/"+self.latitude+','+self.longitude
            }
        else:
            self.env['rb_delivery.error_log'].raise_olivery_error(517,self.id,{'user_name': self.username})
            #raise ValidationError('There is no location')

    @api.model
    def update_whatsapp(self,values):
        prefix_one = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_one')
        prefix_two = self.env['rb_delivery.client_configuration'].get_param('mobile_number_prefix_two')
        # check if there is leading zero then remove it
        # TODO this may valid only to palestine and jordan

        mobile_number=values['mobile_number']
        if 'commercial_number' in values and values['commercial_number']:
            mobile_number=values['commercial_number']
        no_space_first_number=mobile_number.strip()
        if no_space_first_number[0]=='0':
            no_space_first_number=no_space_first_number[1:]

        country = ''
        if 'country_id' in values and values['country_id']:
            country = self.env['rb_delivery.country'].search([('id','=',values['country_id'])])
        elif self.country_id:
            country = self.country_id
        if country and country.key_number:
            values['whatsapp_mobile'] = country.key_number+no_space_first_number
        else:
            values['whatsapp_mobile'] = prefix_one+no_space_first_number

        if 'second_mobile_number' in values and values['second_mobile_number']:
            no_space_second_number=values['second_mobile_number'].strip()
            if no_space_second_number[0]=='0':
                no_space_second_number=no_space_second_number[1:]

            country = ''
            if 'country_id' in values and values['country_id']:
                country = self.env['rb_delivery.country'].search([('id','=',values['country_id'])])
            elif self.country_id:
                country = self.country_id
            if country and country.key_number:
                values['second_whatsapp_mobile'] = country.key_number+no_space_second_number
            else:
                values['second_whatsapp_mobile'] = prefix_one+no_space_second_number

                # Special case for westbank
        if not ('second_mobile_number' in values and values['second_mobile_number']) and mobile_number and prefix_two:
                no_space_second_number=mobile_number.strip()
                if no_space_second_number[0]=='0':
                    no_space_second_number=no_space_second_number[1:]
                values['second_whatsapp_mobile'] = prefix_two+no_space_second_number


    @api.model
    def get_user_info_with_fields(self,fields):
        info = self.env['rb_delivery.user'].search_read([('user_id','=',self._uid)],fields)
        return info

    # inherit module [olivery_osc]
    @api.model
    def get_user_info(self):
        userInfo = self.env['rb_delivery.user'].search_read([('user_id','=',self._uid)],['id', 'user_id', 'state', 'username', 'mobile_number', 'area_id', 'email', 'address', 'group_id', 'role_name','role_code','inclusive_delivery','commercial_name','has_customers','player_id','forgot_password','online','is_block_delivery_fee','default_payment_type','default_payment_detail','bank_name','bank_number','wallet_name', 'wallet_number','holder_name','warehouse'])
        return userInfo

    @api.model
    def add_new_user_address(self,values):
        user = super(rb_delivery_user, self).sudo().create(values)
        return user.id

    @api.model
    def get_rb_delivery_groups(self):
        groups_arr = ['rb_delivery.role_business', 'rb_delivery.role_manager', 'rb_delivery.role_super_manager', 'rb_delivery.role_data_entry', 'rb_delivery.role_call_center', 'rb_delivery.role_accounting', 'rb_delivery.role_driver', 'rb_delivery.role_sales','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative','rb_delivery.role_picking_up_manager','rb_delivery.role_distribution_manager']
        groups = self.env['res.groups'].sudo().search([('code','in',groups_arr)])
        rb_groups=[]
        for group in groups:
            item = {
                'id':group.id,
                'name':group.name,
                'full_name':group.full_name,
                'category_id':group.category_id

            }
            rb_groups.append(item)
        return rb_groups


    def toggle_online(self):
        if self.is_base or self.is_super_manager or self.is_manager:
            if self.user_id:
                self.online = not self.online
            self.write({'online': self.online})

    @api.model
    def wkf_mobile_logout_action(self):
        rb_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
        rb_user.write({'online':False,'player_id':''})
        return True

    def get_logs(self):
        address_form_id = self.env.ref('rb_delivery.enhanced_view_mail_tracking_value_tree').id
        domain = ['|',('field','=','state'),('field','=','assign_to_agent'),('business_id','=',self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.username +" "+ _("Order Logs"),
            'res_model': 'mail.tracking.value',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def mobile_register(self,values):
        if 'email' in values and values['email'] == '':
            values['email'] = False
        try:
            user=self.env['rb_delivery.user'].sudo().create(values)
            if (self.env['rb_delivery.client_configuration'].sudo().get_param('login_enable_without_confirmation')):
                user.wkf_action_confirm()
        except Exception as e:
            self.env['rb_delivery.error_log'].raise_olivery_error(518,None,{'error': str(e)})
        return True

    def check_password_to_deactivate(self,password):

        pwd_context = CryptContext(
            schemes=["pbkdf2_sha512"],
            default="pbkdf2_sha512",
            pbkdf2_sha512__default_rounds=60000,
        )

        # Fetch the stored hash from the database
        self.env.cr.execute("SELECT password FROM res_users WHERE id = %s", (self._uid,))
        password_hashed = self.env.cr.fetchone()[0]

        # Check if the old password hash matches the stored hash

        if pwd_context.verify(password, password_hashed):
            rb_delivery_user = self.env['rb_delivery.user'].search([('user_id','=',self._uid)])
            business_state_ids = []
            statuses_allow_to_archive_business_ids =self.env['rb_delivery.client_configuration'].get_param('default_statuses_to_be_excluded_on_check_for_archive')
            if rb_delivery_user.user_is_business:
                if statuses_allow_to_archive_business_ids:
                    business_state_ids = statuses_allow_to_archive_business_ids
                business_orders = self.env['rb_delivery.order'].sudo().search([('assign_to_business', '=', rb_delivery_user.id), ('state_id', 'not in', business_state_ids)])
                if business_orders:
                    self.env['rb_delivery.error_log'].raise_olivery_error(515,self.id,{'user': rb_delivery_user.username})

            self.env.user.active = False

            data = {'uid':self._uid,'message':_('The user deactivated him self using the mobile app.'),'records':rb_delivery_user,'values':{'state': 'deactivate'},'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)
            return True
        else:
            return False


class rb_delivery_res_user(models.Model):

    _inherit = 'res.users'
    _description = "Res User Model"

    @api.multi
    def _get_rb_user(self):
        for user in self:
            user.rb_user = self.env['rb_delivery.user'].search([('user_id','=',user.id)])

    rb_user = fields.Many2one('rb_delivery.user', compute=_get_rb_user, compute_sudo=True, readonly=True)



    @api.model
    def mobile_change_password(self, user_id, old_password, new_password):
        # Create a context for pbkdf2_sha512. This is what Odoo generally uses.
        pwd_context = CryptContext(
            schemes=["pbkdf2_sha512"],
            default="pbkdf2_sha512",
            pbkdf2_sha512__default_rounds=60000,
        )

        # Fetch the stored hash from the database
        self.env.cr.execute("SELECT password FROM res_users WHERE id = %s", (user_id,))
        old_password_hashed = self.env.cr.fetchone()[0]

        # Check if the old password hash matches the stored hash

        if pwd_context.verify(old_password, old_password_hashed):
            # Hash the new password
            self.env['res.users'].browse(user_id).sudo().write({'password': new_password})
            request.session.logout()
            return {"status":"success","message":"Password changed successfully"}
        else:
            return {"status":"error","message":"Old password is incorrect"}

    def unlink(self):
        for rec in self:
            if rec.rb_user and rec.rb_user.for_app_stores_use:
                self.env['rb_delivery.error_log'].raise_olivery_error(519,rec.id,{'user': rec.rb_user.username})
        return super(rb_delivery_res_user, self).unlink()

    @api.multi
    def write(self, values):
        for record in self:
            if values.get('password') and (record.id == 1 or record.id == 2) and self._uid  != 2:
                self.env['rb_delivery.error_log'].raise_olivery_error(520, record.id, {})
        return super(rb_delivery_res_user, self).write(values)
