# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import ValidationError, Warning,AccessError
import base64
import io
import barcode
from barcode.writer import ImageWriter


class rb_delivery_follow_up_order(models.Model):

    _name = 'rb_delivery.follow_up_order'
    _order = "create_date ASC"
    _inherit = 'mail.thread'
    _description = "Follow up orders"


    name = fields.Char(string='Name', track_visibility="on_change")

    follow_up_sequence = fields.Char('Sequence', track_visibility="on_change",copy=False)

    order_id = fields.Many2one('rb_delivery.order',required=True,track_visibility="on_change",readonly=True)

    status = fields.Many2one('rb_delivery.status',required=True,track_visibility="on_change",compute="_compute_order_fields")

    active = fields.Boolean("Active", default=True, track_visibility="on_change")

    business = fields.Many2one('rb_delivery.user',track_visibility="on_change",compute="_compute_order_fields",store=True)
    
    barcode_follow_up = fields.Binary('Follow up Barcode', compute="create_barcode")

    note = fields.Char(string='Note', track_visibility="on_change")


    @api.depends('order_id')
    def _compute_order_fields(self):
        for record in self:
            record.business = record.sudo().order_id.assign_to_business
            record.status = record.sudo().order_id.state_id




    @api.model
    def create(self, values):
        if not values.get('follow_up_sequence'):
            new_sequence = self.env['ir.sequence'].next_by_code(
                'rb_delivery.order')
            values['follow_up_sequence'] = new_sequence
        else:
            order = self.env['rb_delivery.order'].search(['|',('sequence', '=', values['follow_up_sequence']),('reference_id', '=', values['follow_up_sequence'])])
            if order:
                self.env['rb_delivery.error_log'].raise_olivery_error(700,self.id,{'sequence':values['follow_up_sequence'],'name':values['name']})
            follow_order = self.env['rb_delivery.follow_up_order'].search([('follow_up_sequence', '=', values['follow_up_sequence'])])
            if follow_order:
                self.env['rb_delivery.error_log'].raise_olivery_error(701,self.id,{'sequence':values['follow_up_sequence'],'name':values['name']})

        order = self.env['rb_delivery.order'].browse([values['order_id']])
        if values['name']:
            order.message_post(body=(_("Follow Up Order Created With this Sequence --> {sec} and this name --> {name}")).format(sec=values['follow_up_sequence'], name=values['name']),)
        else:
           order.message_post(body=(_("Follow Up Order Created With this Sequence --> {sec}")).format(sec=values['follow_up_sequence']),)


        return super(rb_delivery_follow_up_order, self).create(values)
    
    @api.multi
    def get_order(self):
        self.ensure_one()
        if self.order_id:
            return {
                'view_type': 'form',
                'view_mode': 'form',
                'view_id': self.env.ref('rb_delivery.view_form_rb_delivery_order').id,
                'res_model': 'rb_delivery.order',
                'context': self._context,
                'type': 'ir.actions.act_window',
                'nodestroy': True,
                'target': 'current',
                'res_id': self.order_id.id,
            }
        
    @api.multi
    def unlink(self):
        for rec in self:
            if not  rec.follow_up_sequence:
                continue
            if rec.name:
                self.order_id.message_post(body=(_("Deleted Follow Up Order With this Sequence --> {sec} and this name --> {name}")).format(sec=rec.follow_up_sequence, name=rec.name),)
            else:
                self.order_id.message_post(body=(_("Deleted Follow Up Order With this Sequence --> {sec}")).format(sec=rec.follow_up_sequence),)
        return super(rb_delivery_follow_up_order, self).unlink()
        
    @api.multi
    def write(self,values):
        for rec in self:
            if not 'name' in values:
                continue
            old_name = rec.name
            self.order_id.message_post(body=(_("Change name in Follow Up Order From --> {old} to --> {new}")).format(old=old_name, new=values['name']),)

        return super(rb_delivery_follow_up_order, self).write(values)
    
    @api.one
    @api.depends('follow_up_sequence')
    def create_barcode(self):
        if self.follow_up_sequence:
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            image_output_follow_up = io.BytesIO()
            
            try:
                ean = EAN(self.follow_up_sequence, writer=ImageWriter(), add_checksum=False)
                ean.write(image_output_follow_up)
                encoded_follow_up = base64.b64encode(image_output_follow_up.getvalue())
                self.barcode_follow_up = encoded_follow_up
            except Exception as e:
                self.env['rb_delivery.error_log'].raise_olivery_error(213, self.id, {
                    'reference_issue': _(str(_(e))), 
                    'field_name': _('Follow Up Sequence')
                })
