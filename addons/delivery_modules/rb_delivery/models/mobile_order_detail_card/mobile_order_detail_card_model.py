# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json
class rb_delivery_mobile_order_detail_card(models.Model):
    
    _name = 'rb_delivery.mobile_order_detail_card'
    
    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    order_detail = fields.Many2one('rb_delivery.mobile_order_detail')
    
    card_label = fields.Char(required=True)

    card_title = fields.Char()

    card_icon = fields.Char(default="edit")

    card_icon_selction = fields.Many2one('rb_delivery.icons')

    card_items = fields.One2many('rb_delivery.mobile_order_detail_card_item', inverse_name="order_detail_card")

    unique_field_name = fields.Char(compute = "_compute_unique_field_name", store=True)

    name = fields.Char('Unique Name',compute = "compute_name", store=True)

    show_labels = fields.Boolean()

    label_color = fields.Char(string='Label Color')

    @api.multi
    @api.depends('order_detail','card_label')
    def _compute_unique_field_name(self):
        for rec in self:
            if rec.order_detail and rec.order_detail.name and rec.card_label:
                rec.unique_field_name = rec.order_detail.name + '_' + rec.card_label.replace(" ", "_").lower()

    @api.onchange('card_icon_selction')
    def card_icon_selection(self):
        if self.card_icon_selction:
            self.card_icon = self.card_icon_selction.name
    
    @api.multi
    @api.depends('order_detail','card_label')
    def compute_name(self):
        for rec in self:
            if rec.order_detail and rec.order_detail.name and rec.card_label:
                rec.name = rec.order_detail.name + '_' + rec.card_label.replace(" ", "_").lower()+ '_card' 