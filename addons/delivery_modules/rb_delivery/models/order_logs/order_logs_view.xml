
<odoo>
  <data>


    <record id="view_tree_rb_delivery_order_logs" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_order_logs</field>
      <field name="model">rb_delivery.order_logs</field>

      <field name="arch" type="xml">
        <tree create="false">
          <field name="create_uid" options="{'no_open': True}"/>
          <field name="create_date"/>
          <field name="order_status"/>
          <field name="field_id"/>
          <field name="old_value"/>
          <field name="new_value"/>
          <button name="go_to_order" type="object" string="Go to Order" class="oe_highlight fa fa-eye"/>
        </tree>
      </field>

    </record>


    <record id="view_search_rb_delivery_order_logs" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_order_logs</field>
      <field name="model">rb_delivery.order_logs</field>

      <field name="arch" type="xml">
        <search>

          <group>
            <field name="order_status"/>
            <field name="field_id"/>
            <field name="business_id"/>
            <field name="driver_id"/>
          </group>

        </search>
      </field>

    </record>

    <record id="view_pivot_rb_delivery_order_logs" model="ir.ui.view">
      <field name="name">view_pivot_rb_delivery_order_logs</field>
      <field name="model">rb_delivery.order_logs</field>
      <field name="arch" type="xml">
          <pivot string="Business Executive Report">
              <field name="order_status" type="col"/>
              <field name="business_id" type="row"/>
          </pivot>
      </field>
    </record>

    <record id="view_pivot_rb_delivery_driver_order_logs" model="ir.ui.view">
      <field name="name">view_pivot_rb_delivery_driver_order_logs</field>
      <field name="model">rb_delivery.order_logs</field>
      <field name="arch" type="xml">
          <pivot string="Driver Executive Report">
              <field name="order_status" type="col"/>
              <field name="driver_id" type="row"/>
          </pivot>
      </field>
    </record>

  </data>
</odoo>
