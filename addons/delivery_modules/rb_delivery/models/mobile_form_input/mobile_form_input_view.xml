
<odoo>
  <data>
    <record id="view_form_rb_delivery_mobile_form_input" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_mobile_form_input</field>
      <field name="model">rb_delivery.mobile_form_input</field>

      <field name="arch" type="xml">
        <form>

          <header>
            <!-- Buttons and status widget -->
          </header>
          <sheet>

            <group name="group_top">
              <field name="model" invisible="1"/>
              <field name="form_creator_fields" invisible="1"/>
              <field name="form_creator_inputs" invisible="1"/>
              <field name="form_creator" invisible="1"/>
              <field name="field_model" invisible="1"/>
              <field name="field_ttype" invisible="1"/>
              <field name="field_relation" invisible="1"/>
              <field name="connected_field_relation" invisible="1"/>
              <field name="parent_field_relation" invisible="1"/>
              <group name="group_right" >
                <field name="position"/>
                <field name="is_separator" attrs="{'invisible':['|','|','|','|',('is_signature','=',True),('is_location','=',True),('is_button','=',True),('position','!=','in_form'),('barcode_verified','=',True),('is_multi_scan','=',True)]}"/>
                <field name="separator_title" attrs="{'invisible':[['is_separator','=',False]],'required':[['is_separator','=',True]]}"/>
                <field name="is_button" attrs="{'invisible':['|','|','|','|',['is_signature','=',True],['is_separator','=',True],['is_location','=',True],['barcode_verified','=',True],['is_multi_scan','=',True]]}"/>
                <field name="is_location" attrs="{'invisible': ['|','|','|', ('is_signature', '=', True), ('barcode_verified', '=', True), ('is_separator', '=', True), ('is_multi_scan', '=', True)]}"/>
                <field name="is_signature" attrs="{'invisible': ['|','|','|', ('is_location', '=', True), ('barcode_verified', '=', True), ('is_separator', '=', True), ('is_multi_scan', '=', True)]}"/>
                <field name="is_multi_scan" attrs="{'invisible': ['|','|','|','|', ('is_location', '=', True), ('barcode_verified', '=', True), ('is_signature', '=', True), ('is_location', '=', True), ('is_separator', '=', True)]}"/>
                <field name="barcode_verified" attrs="{'invisible': ['|','|','|', ('is_location', '=', True), ('is_signature', '=', True), ('is_separator', '=', True), ('is_multi_scan', '=', True)]}"/>
                <field name="color" attrs="{'invisible':[['is_separator','=',False],['is_button','=',False]]}"/>
                <field name="background" attrs="{'invisible':[['is_separator','=',False],['is_button','=',False]]}"/>
                <field name="button_function" attrs="{'invisible':[['is_button','=',False]]}"/>
                <field name="form_to_open" attrs="{'invisible':[['button_function','!=','openForm']]}"/>
                <field name="show_terms_and_conditions" attrs="{'invisible':[['is_button','=',False]]}"/>
                <field name="as_step" attrs="{'invisible':[['is_separator','=',False]]}"/>
                <field name="form_name" invisible="1" />
                <field name="quick_order_position" attrs="{'invisible': [('form_name', '!=', 'quick_order_form')]}" domain="[['model_id', '=', model]]"/>
                <field name="quick_order_touch_selection" attrs="{'invisible': [('form_name', '!=', 'quick_order_form')]}" domain="[['model_id', '=', model]]"/>
                <field name="clear_after_submit" attrs="{'invisible': [('form_name', '!=', 'quick_order_form')]}" domain="[['model_id', '=', model]]"/>
              </group>
              <group name="group_left" attrs="{'invisible':[['is_separator','=',False],['is_button','=',False]]}">
                <field name="color" attrs="{'invisible':[['is_separator','=',False],['is_button','=',False]]}"/>
                <field name="background" attrs="{'invisible':[['is_separator','=',False],['is_button','=',False]]}"/>
                <field name="button_text" attrs="{'invisible':[['is_button','=',False]],'required':[['is_button','=',True]]}"/>
                <field name="button_icon_selction" attrs="{'invisible':[['is_button','=',False]]}"/>
                <field name="button_icon" attrs="{'invisible':[['is_button','=',False]]}"/>
              </group>
              <group name="group_right" string="Values" attrs="{'invisible':['|','|','|','|','|','|',['is_separator','=',True],['is_button','=',True],['is_location','=',True],['is_multi_scan','=',True],['is_signature','=',True],['barcode_verified','=',True],['model','=',False]]}">
                <field name="combine_two_booleans" attrs="{'invisible':[['field_ttype','!=','boolean']]}"/>
                <field name="field"  domain="[['model_id','=',model]]" attrs="{'required':[['is_separator','=',False],['is_button','=',False],['is_location','=',False],['is_multi_scan','=',False],['is_signature','=',False],['barcode_verified','=',False],['model','!=',False]]}"/>
                <field name="is_isolated_input" attrs="{'invisible':[['field_ttype','not in',['one2many','many2one','many2many']]]}"/>
                <field name="isolated_form" attrs="{'invisible':[['is_isolated_input','=',False]]}">
                  <tree>
                    <field name="name"/>
                  </tree>
                </field> 
                <field name="second_boolean_field"  domain="[['model_id','=',model]]" attrs="{'invisible':[['combine_two_booleans','=',False]],'required':[['is_separator','=',False],['is_button','=',False],['is_location','=',False],['is_signature','=',False],['barcode_verified','=',False],['model','!=',False],['combine_two_booleans','=',True]]}"/>
                <field name="search_by" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}" domain="[['model','=',field_relation]]" widget="many2many_tags"/>
                <field name="limit_per_search" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="image_field_name" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}" domain="[['model','=',field_relation],['ttype','=','binary']]"/>
                <field name="have_image" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="image_source" attrs="{'invisible':[['field_ttype','!=','binary']]}"/>
                <field name="domain" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}" widget="domain" options="{'model': 'field_relation','in_dialog': true}"/>
                <field name="show_barcode_scanner" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char']]}"/>
                <field name="show_location_selector" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char']]}"/>
                <field name="show_true_buyer_button" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char']]}"/>
                <field name="show_voice_to_text_ability" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer']]}"/>
                <field name="is_auto_fill_mapping_fields_enabled" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}"/>
                <field name="show_client_history" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer'],['field_ttype','!=','float']]}"/>
                <field name="open_selection_modal_when_click"  attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer'],['field_ttype','!=','float']]}"/>
                <field name="connected_field_to_selection_modal" attrs="{'invisible':[['open_selection_modal_when_click','=',False]]}" domain="[['model_id','=',model]]"/>
                <field name="search_by_inside_selection_modal" attrs="{'invisible':[['open_selection_modal_when_click','=',False]]}" domain="[['model','=',connected_field_relation]]" widget="many2many_tags"/>
                <field name="is_monetary" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer'],['field_ttype','!=','float']]}"/>
                <field name="placeholder"/>
                <field name="first_placeholder" attrs="{'invisible':[['combine_two_booleans','=',False]]}"/>
                <field name="second_placeholder" attrs="{'invisible':[['combine_two_booleans','=',False]]}"/>
                <field name="default_value" attrs="{'required':[['required','=',True],['invisible','=',True]]}"/>
                <field name="field_icon_selction" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer'],['field_ttype','!=','float']]}"/>
                <field name="field_icon" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','char'],['field_ttype','!=','integer'],['field_ttype','!=','float']]}"/>
                <field name="sort_by" attrs="{'invisible': ['|', ('field', '=', False), ('field_ttype', 'not in', ['many2one', 'one2many', 'many2many'])]}"/>
                <field name="is_label_field" attrs="{'invisible': [('field', '=', False)]}"/>
                <field name="combine_three_fields" attrs="{'invisible': [('field', '=', False)]}"/>
                <field name="show_create_button" attrs="{'invisible': [('field', '=', False)]}"/>
                <field name="quick_order_show_as" attrs="{'invisible': ['|','|',('field', '=', False),('form_name', '!=', 'quick_order_form'),('field_ttype', 'not in', ['one2many'])]}"/>
              </group>
              <group name="group_right" string="Values" attrs="{'invisible':[('is_multi_scan','=',False)]}">
                <field name="placeholder" attrs="{'required':[('is_multi_scan','=',True)]}"/>
              </group>

              
              <group name="group_left" string="Relations" attrs="{'invisible':[['field_ttype','!=','many2one'],['field_ttype','!=','many2many'],['field_ttype','!=','one2many']]}">
                <field name="parent_field" domain="[['id','in',form_creator_fields]]"/>
                <field name="parent_input" />
                <field name="parent_field_map" attrs="{'required':[['parent_input','!=',False]]}" domain="[['model','=',field_relation],['relation','=',parent_field_relation],['relation','!=',False],['related','=',False]]"/>
                <field name="cascade_child_to_parent" attrs="{'invisible':[['parent_field','=',False]]}" />
              </group>
              
            </group>
            <group name="group_top" string="Mapping Relation Fields" attrs="{'invisible':[['open_selection_modal_when_click','=',False],['is_auto_fill_mapping_fields_enabled','=',False]]}">
                <field name="cascade_mapping_fields" />
                <field name="mapping_relation_fields">
                  <tree editable="bottom">
                    <field name="origin_model_fields" domain="[('model_id', '=', parent.model)]"/>
                    <field name="inverse_model_fields" domain="['|',('model_id', '=', parent.field_relation), ('model_id', '=', parent.connected_field_relation)]"/>
                    <field name="sequence" widget="handle"/>
                  </tree>
                </field>
            </group>
            <group name="group_top" string="Table Fields" attrs="{'invisible': ['|','|',('field', '=', False),('form_name', '!=', 'quick_order_form'),('quick_order_show_as', '!=', 'table')]}">
              <field name="table_comodel_inverse_name" domain="[['model','=',field_relation]]"/>
              <field name="quick_order_table_fields" >
                <tree editable="bottom">
                  <field name="field" domain="[('model_id', '=', parent.field_relation)]"/>
                  <field name="domain"/>
                  <field name="local_compute_function"/>
                  <field name="required"/>
                  <field name="locked"/>
                </tree>
              </field>
            </group>
            <group name="group_top" string="Input Type And Validation" attrs="{'invisible':['|',['is_separator','=',True],['is_button','=',True]]}">
                <group name="group_left" >
                  <field name="input_type" attrs="{'invisible':[['field_ttype','!=','text'],['field_ttype','!=','float'],['field_ttype','!=','char'],['field_ttype','!=','integer']]}"/>
                  <field name="min_length" attrs="{'invisible':[['input_type','!=','text'],['input_type','!=','number'],['input_type','!=','tel']]}"/>
                  <field name="invisible"/>
                </group>
                <group name="group_right">
                  <field name="lock_required" invisible="1"/>
                  <field name="required" attrs="{'readonly': [('lock_required', '=', True)], 'invisible': [('invisible', '=', True)]}"/>                  
                  <field name="max_length" attrs="{'invisible':[['input_type','!=','text'],['input_type','!=','number'],['input_type','!=','tel']]}" />
                  <field name="invisible_domain" widget="domain" options="{'model': 'rb_delivery.user','in_dialog': true}" attrs="{'invisible':[['invisible','=',False]]}"/>
                  <field name="mobile_invisible_domain"/>
                  <field name="readonly" attrs="{'invisible':[['invisible','=',True]]}"/>
                </group>
                
              </group>

              <group name="group_top" string="Compute Functions">
                <group name="group_right">
                  <field name="compute_function" />
                </group>
              </group>
            
          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>

    <record id="view_tree_rb_delivery_mobile_form_input" model="ir.ui.view">

      <field name="name">view_tree_rb_delivery_mobile_form_input</field>
      <field name="model">rb_delivery.mobile_form_input</field>

      <field name="arch" type="xml">
        <tree create='false'>
          <field name="display_name"/>
        </tree>

      </field>
    </record>

  </data>
</odoo>
