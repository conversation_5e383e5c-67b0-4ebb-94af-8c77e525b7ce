# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json
import logging
_logger = logging.getLogger(__name__)
class rb_delivery_mobile_order_detail(models.Model):

    _name = 'rb_delivery.mobile_order_detail'

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    _sql_constraints = [('page_name_group_unique', 'unique(page_name,group_id)', _('page name with this role already exists!'))]

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
    
    page_name = fields.Selection([('order_detail_page','Order Detail Page')], required=True)

    model = fields.Many2one('ir.model', required=True)

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups,copy=False)

    name = fields.Char('Unique Name',compute = "compute_name", store=True)

    buttons = fields.One2many('rb_delivery.mobile_order_detail_button',inverse_name="order_detail")

    cards = fields.One2many('rb_delivery.mobile_order_detail_card', inverse_name="order_detail")
    
    card_structure_hash = fields.Char(compute='_compute_card_structure_hash', store=True)

    @api.multi
    @api.depends('page_name','group_id')
    def compute_name(self):
        for rec in self:
            if rec.page_name and rec.group_id:
                rec.name = rec.page_name + " [" + rec.group_id.name + "]"

    @api.model
    def get_card_fields(self):
        cards = self.cards
        result = self.env['ir.model.fields']
        for card in cards:
            card_items = card.card_items
            for card_item in card_items:
                if card_item.item_field:
                    result += card_item.item_field
        return result
    
    @api.multi
    @api.depends('cards', 'cards.card_items', 'cards.card_items.item_field')
    def _compute_card_structure_hash(self):
        import hashlib
        for record in self:
            structure = []
            cards = record.cards.sorted(lambda c: c.id)
            for card in cards:
                card_info = f"Card:{card.id}:{card.card_label}"
                structure.append(card_info)
                card_items = card.card_items.sorted(lambda i: i.id)
                for item in card_items:
                    item_field_name = item.item_field.name if item.item_field else 'None'
                    item_info = f"Item:{item.id}:{item.item_label}:{item_field_name}"
                    structure.append(item_info)
            if structure:
                structure_str = '|'.join(structure)
                hash_value = hashlib.md5(structure_str.encode()).hexdigest()
            else:
                hash_value = ''
            record.card_structure_hash = hash_value

    @api.model
    def _on_card_structure_changed(self, record):
        card_creator = self.env['rb_delivery.mobile_card_creator'].search([('order_detail', '=', record.id)], limit=1)
        if card_creator:
            card_creator.compute_card_fields()
        else:
            domain = [
                ('group_id', '=', record.group_id.id),
                ('model', '=', record.model.id)
            ]
            card_creator = self.env['rb_delivery.mobile_card_creator'].search(
                domain, 
                limit=1
            )
            card_creator.write({'order_detail': record.id})
            card_creator.compute_card_fields()

    @api.model
    def get_mobile_order_detail_data(self):
        user_data = self.env['rb_delivery.user'].sudo().search_read(
            [['user_id', '=', self._uid]],
            ['group_id'],
            limit=1
        )
        if not user_data or not user_data[0].get('group_id'):
            return []
        group_id = user_data[0]['group_id'][0]
        order_details = self.env['rb_delivery.mobile_order_detail'].sudo().search_read(
            [['group_id', '=', group_id]],
            ['id', 'page_name', 'name', 'model', 'group_id']
        )
        order_detail_ids = [od['id'] for od in order_details]
        buttons = self.env['rb_delivery.mobile_order_detail_button'].sudo().search(
            [['order_detail', 'in', order_detail_ids]]
        )
        cards = self.env['rb_delivery.mobile_order_detail_card'].sudo().search_read(
            [['order_detail', 'in', order_detail_ids]],
            ['id', 'order_detail', 'card_label', 'card_title', 'card_icon', 'show_labels','label_color']
        )
        card_ids = [card['id'] for card in cards]
        card_items = self.env['rb_delivery.mobile_order_detail_card_item'].sudo().search(
            [['order_detail_card', 'in', card_ids]]
        )
        data_dict = {}
        for od in order_details:
            data_dict[od['id']] = {
                'id': od['id'],
                'page_name': od.get('page_name'),
                'model': od.get('model')[1] if od.get('model') else '',
                'group_id': od.get('group_id')[0] if od.get('group_id') else False,
                'name': od.get('name'),
                'buttons': [],
                'cards': []
            }
        for button in buttons:
            if not button.order_detail:
                continue
            od_id = button.order_detail.id
            if od_id in data_dict:
                data_dict[od_id]['buttons'].append({
                    'button_label': button.button_label,
                    'button_title': button.button_title,
                    'button_icon': button.button_icon,
                    'button_function': button.button_function.technical_name
                                    if button.button_function and button.button_function.technical_name
                                    else (button.button_function.name if button.button_function else False)
                })
        card_mapping = {}
        for card in cards:
            od_field = card.get('order_detail')
            if not od_field:
                continue
            od_id = od_field[0]
            card_data = {
                'card_label': card.get('card_label'),
                'card_title': card.get('card_title'),
                'card_icon': card.get('card_icon'),
                'label_color': card.get('label_color'),
                'show_labels': card.get('show_labels') if card.get('show_labels') else False,
                'card_items': []
            }
            card_mapping[card['id']] = card_data
            if od_id in data_dict:
                data_dict[od_id]['cards'].append(card_data)
        for item in card_items:
            if not item.order_detail_card:
                continue
            order_detail_card_id = item.order_detail_card.id
            if order_detail_card_id in card_mapping:
                card_mapping[order_detail_card_id]['card_items'].append({
                    'item_label': item.item_label,
                    'item_title': item.item_title,
                    'item_icon': item.item_icon,
                    'item_field': item.item_field.name if item.item_field else False,
                    'item_ttype': item.item_field.ttype if item.item_field else False,
                    'inverse_model': item.item_field.relation if item.item_field else False
                })
        return list(data_dict.values())
    
    @api.model
    def create(self,values):
        order_detail = super(rb_delivery_mobile_order_detail,self).create(values)
        self._on_card_structure_changed(order_detail)
        return order_detail
    
    @api.multi
    def write(self,values):
        is_written = super(rb_delivery_mobile_order_detail,self).write(values)
        for rec in self:
            rec._on_card_structure_changed(rec)
        return is_written