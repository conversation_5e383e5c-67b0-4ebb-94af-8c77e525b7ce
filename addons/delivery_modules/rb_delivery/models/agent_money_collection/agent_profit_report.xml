<?xml version="1.0" encoding="utf-8"?>

<odoo>
<data>
    <record model="report.paperformat" id="paperformat_company_profit">
        <field name="name">paperformat.company_profit</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="page_width">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">30</field>
        <field name="margin_right">5</field>
        <field name="margin_bottom">10</field>
        <field name="margin_left">5</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">30</field>
        <field name="dpi">90</field>
		<field name="sequence">10</field>
    </record>

        <report id="report_rb_delivery_company_profit"
            model="rb_delivery.agent_money_collection"
            groups="rb_delivery.role_accounting,rb_delivery.role_junior_accounting,rb_delivery.role_super_manager,base.group_system"
            string="Company Profit"
            report_type="qweb-pdf"
            name="rb_delivery.company_profit"
            paperformat="paperformat_company_profit"
			/>

		<template id="minimal_layout_inherit" inherit_id="web.minimal_layout">
			<xpath expr="//head" position="inside">
				<link rel='stylesheet' href="/rb_delivery/static/src/css/report.css"/>
			</xpath>
		</template>


		<template id="company_profit">
			<style type="text/css">

				body {

				color: #000 !important;

				}

				.background {

				background-color: blue;

				}

				.table_header_data{

				font-size:12px !important;

				}

				.table_data{

				font-size:13px !important;

				}

				.p {

				font-size: 22px;

				color: green;

				}
				table{
					th{
						border :1px solid black;
					}
				}


			</style>
			<t t-call="web.basic_layout">
			<t t-foreach="docs" t-as="doc">


			        <t t-if="not o" t-set="o" t-value="doc"/>

                    <t t-if="not company">
                    <!-- Multicompany -->
                        <t t-if="company_id">
                            <t t-set="company" t-value="company_id"/>
                        </t>
                        <t t-elif="o and 'company_id' in o">
                            <t t-set="company" t-value="o.company_id.sudo()"/>
                        </t>
                        <t t-else="else">
                            <t t-set="company" t-value="res_company"/>
                        </t>
                    </t>


				    <div class="header" style="font-size:13px !important">
					<span style="position:absolute;right:350px;top:40px">
							<img t-attf-src="data:image/*;base64,{{doc.barcode}}" style="width:200px;height:50px" />
						</span>

						<br/>

						<h5 class="text-center" style="position:absolute;right:400px;top:100px">

							<t>
							<span t-field="doc.sequence" />

							</t>
						</h5>
						<div class="row">

						<div class="col-4">
								<div class="col-12">Agent's name: <span t-field="doc.sudo().agent_id"/>
						        </div>
								<div class="col-12">Date : <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/></div>

								</div>

							<div class="col-4">
								<p class="text-center">Company Profit Report</p>
							</div>

							<div class="col-4 text-center">
							<p>
								<t t-if="company.agent_money_logo">
									<img t-if="company.agent_money_logo" t-att-src="image_data_uri(company.agent_money_logo)" alt="Logo" style="max-height: 100px;"/>
								</t>
								<t t-else="else">
									<img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="max-height: 100px;"/>
								</t>
								</p>
							</div>

						</div>

				    </div>

				    <div class="footer">

					    <div class="row">
						    <div >The recipient's signature: ..............................</div>
					    </div>

						<div class="row">
							<div>
								<small>
									<span>Page</span>
									<span class="page" />
                            of
									<span class="topage" />
								</small>
							</div>
						</div>

				    </div>

   				   <div class="page">

					<div class="image_bg"></div>
					<div class="oe_structure"/>
					<table class="table table-condensed" style="font-size:0.7em;width:100%;border:1px solid black">
						<thead>
							<tr>
							    <th style="border:1px solid black">#</th>
								<th style="border:1px solid black">Sequence number</th>
								<th style="border:1px solid black">Sender's name</th>
								<th style="border:1px solid black">Recipient's name</th>
							    <th style="border:1px solid black">Recipient's address</th>
							    <th style="border:1px solid black">Money collection cost</th>
								<th style="border:1px solid black" t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">Company profit</th>
								<th style="border:1px solid black" t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">Agent Cost</th>
							    <th style="border:1px solid black" t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">Delivery cost</th>
							    <th style="border:1px solid black">Total cost</th>
							</tr>
						</thead>
						<tbody class="sale_tbody">
							<t t-set="counter" t-value="0"/>
							<t t-set="order_ids_sorted_area" t-value="docs.order_ids.sorted(key=lambda a:(a.customer_area.name if a.customer_area else '', a.customer_sub_area.name if a.customer_sub_area else '', a.customer_address if a.customer_address else ''))"/>
							<t t-foreach="order_ids_sorted_area" t-as="order">
								<t  t-set="counter" t-value="counter + 1"/>
								<tr style="page-break-inside: avoid;">
									<!-- for note -->
									<td>
									    <span t-esc="counter"/>
									</td>
									<td>

                                    <t t-if="order.reference_id" >
										<span t-field="order.sequence"/>
										<span t-field="order.reference_id"/>
										</t>
										<t t-else="else">
										<span t-field="order.sequence"/>
										</t>
									</td>
									<td >
									<t t-if="order.sudo().assign_to_business.commercial_name" >
										<span t-field="order.sudo().assign_to_business.commercial_name"/>
										</t>
										<t t-else="else">
										<span t-field="order.sudo().assign_to_business"/>
										</t>
									</td>
									<td >
										<span t-field="order.customer_name"/>
										<br/>
										<span t-field="order.customer_mobile"/>

									</td>
									<td >
										<span t-field="order.customer_area"/> - <span t-field="order.customer_address"/>
									</td>
									<td>
										<span t-field="order.money_collection_cost"/>
									</td>
									<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
										<span t-field="order.delivery_profit"/>
									</td>
									<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
										<span t-field="order.agent_cost"/>
									</td>
									<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
										<span t-field="order.delivery_cost"/>
									</td>
									<td >
										<span t-field="order.required_from_business"/>
									</td>

								</tr>

								<tr t-if="len(doc.order_ids) == counter" style="font-weight:bold;page-break-inside: avoid;">
								<td>Total</td>
								<td>	</td>
								<td>	</td>
								<td>	</td>
							    <td>	</td>
							    <td>
									<t t-esc="'%.2f' % (money_collection_total if money_collection_total is not None else 0)"/>
								</td>
								<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
									<t t-esc="'%.2f' % (delivery_profit_total if delivery_profit_total is not None else 0)"></t>
								</td>								
								<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
									<t t-esc="'%.2f' % (agent_cost_total if agent_cost_total is not None else 0)"></t>
								</td>								
								<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
									<t t-esc="'%.2f' % (delivery_cost_total if delivery_cost_total is not None else 0)"></t>
								</td>								
								<td>
									<t t-esc="'%.2f' % (required_from_business_total if required_from_business_total is not None else 0)"></t>
								</td>								


								</tr>
							</t>
						</tbody>
					</table>


					<div class="oe_structure"/>
				</div>
			</t>

    	</t>
	</template>

	</data>
</odoo>