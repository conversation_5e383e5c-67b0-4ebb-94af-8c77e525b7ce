# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.exceptions import UserError
from openerp import SUPERUSER_ID
import base64
import qrcode
from io import BytesIO

class rb_delivery_runsheet(models.Model):
    _name = 'rb_delivery.runsheet'
    _inherit = 'mail.thread'
    _order = "create_date DESC"
    _description = "Runsheet Model"

    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_collection_manager = user.has_group('rb_delivery.role_collection_manager')

    def default_is_collection_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_collection_manager = user.has_group('rb_delivery.role_collection_manager')
        return is_collection_manager

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note

    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','runsheet_collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None

    def default_allow_edit_collection_orders(self):
        if self._uid == 1 or self._uid == 2:
            allow_edit_collection_orders = True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            allow_edit_collection_orders = user.allow_edit_collection_orders
        return allow_edit_collection_orders

    @api.one
    def compute_allow_edit_collection_orders(self):
        if self._uid == 1 or self._uid == 2:
            self.allow_edit_collection_orders = True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            self.allow_edit_collection_orders = user.allow_edit_collection_orders

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code

    name = fields.Char('Note')

    sequence = fields.Char('Sequence', readonly=True,track_visibility=False,copy=False)

    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',track_visibility="on_change")

    payment_detail = fields.Char(string='Payment Detail',track_visibility="on_change")

    assign_to_agent = fields.Many2one('rb_delivery.user', 'Agent', domain=_get_driver_users)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'print_order_runsheet_item',
        column1 = 'runsheet_id',
        column2 = 'order_id')

    active = fields.Boolean('Active', default=True , track_visibility="on_change")

    previous_agent = fields.Many2one('rb_delivery.user', 'Previous Agent', track_visibility="on_change",copy=False)

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    previous_status = fields.Char('Previous Status', track_visibility=False)

    previous_status_title = fields.Char('Previous Status Name',  track_visibility="on_change", readonly=True)

    order_count = fields.Integer(string="Order count")

    is_collection_manager = fields.Boolean('Is Collection Manager', compute="check_user", default=default_is_collection_manager)


    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    total_money_collection_runsheet = fields.Float("Total Money Collection Runsheet")


    allow_edit_collection_orders = fields.Boolean('Allow edit collections orders', compute="compute_allow_edit_collection_orders", default=default_allow_edit_collection_orders)

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)


    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if self.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')],limit=1)
                rec.state_id = state_id.id

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = []
        for order in self.order_ids:
            ids.append(order.id)
        domain = [('id', 'in', ids)]
        if self.name:
            name = self.name
        elif self.assign_to_agent:
            name = self.assign_to_agent.username
        else:
            name = "Runsheet"
        return {
            'type': 'ir.actions.act_window',
            'name': name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    @api.model
    def get_status(self):
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list

    # inherit module[olivery_branch_collection]
    @api.model
    def create(self, values):
        if 'order_ids' in values and values['order_ids']:
            orders_list = self.env['rb_delivery.order'].browse(values['order_ids'])
            del values['order_ids']
        else:
            orders_list = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',action_name='runsheet_collection',state_name=values['state'])

        orders_no_agent = ''
        total_money_collection_cost = 0
        for order in orders_list:
            if not order.sudo().assign_to_agent:
                orders_no_agent = orders_no_agent  + ', ' + order.sequence
            total_money_collection_cost = total_money_collection_cost + order.money_collection_cost

        if orders_no_agent != '':
            message =_("There is no agent in these orders ")+orders_no_agent
            self.env['rb_delivery.error_log'].raise_olivery_error(290,self.id,{'messages': orders_no_agent, 'orders': orders_no_agent})
            #raise ValidationError(message)

        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','runsheet_collection'),('status_type','=','olivery_collection')],limit=1)
        if status and status.default_related_order and status.related_order_status:
            order_status = self.env['rb_delivery.status'].search([('name','=',status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            if order_status:
                data = {'uid':self._uid,'message':_("Orders status updated from create runsheet collection."),'records':orders_list,'values':{'state':order_status.name},'update':True}
                self.env['rb_delivery.utility'].olivery_sudo(data)

        if orders_list[0].sudo().assign_to_agent:
            if orders_list[0].sudo().assign_to_agent.default_payment_type:
                values['payment_type'] = orders_list[0].sudo().assign_to_agent.default_payment_type.id
                values['payment_detail'] = orders_list[0].sudo().assign_to_agent.default_payment_detail
            else:
                payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
                if len(payment_type) != 0:
                    values['payment_type'] = payment_type[0].id if payment_type else None


        values['order_ids'] = [(6,0,orders_list.ids)]
        values['order_count'] = len(orders_list.ids)
        values['total_money_collection_runsheet'] = total_money_collection_cost
        values['assign_to_agent'] = orders_list[0].sudo().assign_to_agent.id
        new_sequence = self.env['ir.sequence'].next_by_code(
            'rb_delivery.runsheet')
        values['sequence'] = new_sequence

        runsheet = super(rb_delivery_runsheet, self).create(values)



        for order in runsheet.order_ids:
            order.write({'runsheet_collection_id':runsheet.id})
        message = _('Runsheet collection generated')
        self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message], str(self._uid))
        return runsheet

    # inherit module[olivery_branch_collection]
    @api.one
    def write(self,values):
        if ('assign_to_agent' in values and values['assign_to_agent']):
            if self.assign_to_agent:
                values['previous_agent'] = self.assign_to_agent.id
        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',action_name='runsheet_collection',state_name=values['state'],collection_type='runsheet_collection',object=self)
            if values['state'] == 'deleted':
                self.active = False
                for order in self.order_ids:
                    order.write({'runsheet_collection_id':False})
            values['previous_status'] = self.state
            runsheet_status = self.env['rb_delivery.status'].sudo().search([('name','=',values['previous_status']),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')])
            if runsheet_status and runsheet_status.title:
                values['previous_status_title'] = runsheet_status.title
        if 'order_ids' in values and values['order_ids']:
            ids = []
            to_seqs = []
            for order in self.order_ids:
                ids.append(order.sequence)
            if len(values['order_ids'][0][2])==0:
                self.active =False
                for order in self.order_ids:
                    order.write({'runsheet_collection_id':False})
            else:
                new_ids = values['order_ids'][0][2]
                removed_ids = [order_id for order_id in self.order_ids.ids if order_id not in new_ids]
                orders = self.env['rb_delivery.order'].sudo().browse(removed_ids).mapped('sequence')
                if len(orders) > 0:
                    self.message_post(body=_("Orders %s were removed from runsheet collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))
                added_ids = [order_id for order_id in new_ids if order_id not in self.order_ids.ids]
                orders = self.env['rb_delivery.order'].sudo().browse(added_ids).mapped('sequence')
                if len(orders) > 0:
                    self.message_post(body=_("Orders %s were added to runsheet collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))

                for order_id in values['order_ids'][0][2]:
                    if order_id not in ids:
                        order = self.env['rb_delivery.order'].search([('id','=',order_id)])
                        to_seqs.append(order.sequence)
                        order.write({'runsheet_collection_id':self.id})
                values['order_count'] = len(values['order_ids'][0][2])
                values_to_be_reflected = self.env['rb_delivery.utility'].reflect_changes_to_collections(values['order_ids'][0][2],'runsheet_collection')
                if values_to_be_reflected:
                    values.update(values_to_be_reflected)
            user = self.env['res.users'].sudo().search([('id','=',self._uid)])
            self.message_post(body=_("Orders were changed from %s to %s by %s") % (ids,to_seqs,user.name))
        return super(rb_delivery_runsheet, self).write(values)

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_order_select_runsheet_state').id
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_runsheet_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    @api.multi
    def name_get(self):
        result = []
        for runsheet in self:
            if runsheet.name:
                name = runsheet.name
            elif runsheet.assign_to_agent:
                name = runsheet.assign_to_agent.username
            else:
                name = "Runsheet"

            result.append((runsheet.id, name))
        return result

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            self.env['rb_delivery.error_log'].raise_olivery_error(293,self.id,{'group': user_group, 'status': status,'collection_type':_('Runsheet collection')})
            #raise Warning(_("You are not allowed to change to this state"))

        return

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                self.env['rb_delivery.error_log'].raise_olivery_error(292,self.id,{'first': current_status_record.title, 'second': next_status_record.title,'collection_type':_('Runsheet collection')})
                #raise Warning(_("You are not allowed to change from this status {} to this status {}").format(current_status_record.title,next_status_record.title))

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            import barcode
            from barcode.writer import ImageWriter
            import io
            import base64
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})

    @api.model
    def print_multi_orders_runsheet_report(self,collection_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delevery_runsheet_action').sudo().render_qweb_pdf(collection_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.multi
    def change_runsheet_state(self,state_name):
        collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',state_name),('collection_type','=','runsheet_collection'),('status_type','=','olivery_collection')])
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',collection_state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])

        self.write({'state': collection_state.name})
        if order_state and len(order_state) >0:
             for record in self.order_ids:
                order = self.env['rb_delivery.order'].search([('id','=',record.id)])
                data = {'uid':self._uid,'message':_("Orders values updated from update runsheet collection."),'records':order,'values':{'state': order_state.name,'is_from_collection':True},'update':True}
                self.env['rb_delivery.utility'].olivery_sudo(data)
        return True


    @api.model
    def group_by_get_driver(self,domain):
        users = []
        collections = self.env['rb_delivery.runsheet'].search(domain)
        for collection in collections:
                if {"id":collection.assign_to_agent.id ,"name":collection.assign_to_agent.username} not in users :
                        users.append({"id":collection.assign_to_agent.id ,"name":collection.assign_to_agent.username})
        return users

    @api.model
    def runsheet_count(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.runsheet'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.runsheet'].search_count([])
            return count

    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image



    def get_collection_doc_ids(self, docs, assignment_id):
        doc_ids = [int(doc_id) for doc_id in docs.split(',')]
        orders = self.env['rb_delivery.runsheet'].browse(doc_ids)
        assignment_to_docs = {}
        for order in orders:
            if assignment_id == 'driver':
                assignment = order.assign_to_agent.id
            else:
                self.env['rb_delivery.error_log'].raise_olivery_error(114,self.id)
            if assignment not in assignment_to_docs:
                assignment_to_docs[assignment] = []
            assignment_to_docs[assignment].append(str(order.id))
        return [','.join(doc_ids) for doc_ids in assignment_to_docs.values()]