import logging
from openerp import models, api
from openerp.exceptions import UserError
from odoo import api, fields, models, tools, SUPERUSER_ID, _
from odoo.tools import config
from collections import OrderedDict
from odoo.sql_db import TestCursor
import os
import tempfile
import requests
import re
import base64
import mimetypes
from contextlib import closing

_logger = logging.getLogger(__name__)


class ir_actions_report(models.Model):
    _inherit = 'ir.actions.report'

    def _convert_html_links(self, html_content):
        """Convert HTML links to absolute URLs without inlining CSS content"""
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')

        try:
            # Step 1: Convert all relative URLs to absolute URLs (including CSS)
            html_content = re.sub(r'href="(/[^"]*)"', r'href="http://web:8069\1"', html_content)
            html_content = re.sub(r'src="(/[^"]*)"', r'src="http://web:8069\1"', html_content)
            html_content = re.sub(r"url\('(/[^']*)'\)", r"url('http://web:8069\1')", html_content)
            html_content = re.sub(r'url\("(/[^"]*)"\)', r'url("http://web:8069\1")', html_content)
            html_content = re.sub(r'@import "(/[^"]*)"', r'@import "http://web:8069\1"', html_content)

            # Step 2: Inline only images (keep CSS as URLs)
            html_content = self._inline_images_from_files(html_content)

            # Step 3: Apply direction support as final step
            html_content = self._add_direction_support(html_content)

        except Exception as e:
            _logger.error(f"Error in _convert_html_links: {e}")
            # Fallback: at least apply direction support
            html_content = self._add_direction_support(html_content)

        return html_content.encode('utf-8')

    def _process_html_content(self, html_content):
        """Process HTML content - clean up whitespace and remove JS (deprecated method kept for compatibility)"""
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')

        try:
            # Clean up extra whitespace
            html_content = re.sub(r'\n\s*\n', '\n', html_content)

            # Remove any JavaScript for security
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<script[^>]*/?>', '', html_content, flags=re.IGNORECASE)

        except Exception as e:
            _logger.error(f"Error in _process_html_content: {e}")

        return html_content

    def _add_direction_support(self, html_content):
        """Add RTL/LTR direction support based on language"""
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')

        try:
            # Get current language from context
            lang = self.env.context.get('lang', 'en_US')

            # Check if language is Arabic (RTL) - be more comprehensive
            is_rtl = (lang.startswith('ar_') or lang == 'ar' or
                     lang.startswith('he_') or lang == 'he' or  # Hebrew
                     lang.startswith('fa_') or lang == 'fa' or  # Persian/Farsi
                     lang.startswith('ur_') or lang == 'ur')    # Urdu
            direction = 'rtl' if is_rtl else 'ltr'

            _logger.info(f"Language detected: {lang}, Direction: {direction}")

            # More robust regex patterns that handle existing dir attributes
            # Remove existing dir attributes first, then add new ones
            html_content = re.sub(r'\s+dir=["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)

            # Add direction to html tag
            if '<html' in html_content.lower():
                html_content = re.sub(r'<html([^>]*?)>', rf'<html\1 dir="{direction}">', html_content, flags=re.IGNORECASE)

            # Add direction to body tag
            if '<body' in html_content.lower():
                html_content = re.sub(r'<body([^>]*?)>', rf'<body\1 dir="{direction}">', html_content, flags=re.IGNORECASE)

            # Also add to any main container divs
            html_content = re.sub(r'<div([^>]*class=["\'][^"\']*(?:page|report|main|container)[^"\']*["\'][^>]*?)>',
                                rf'<div\1 dir="{direction}">', html_content, flags=re.IGNORECASE)

            # If no html/body tags, wrap entire content with direction
            if '<html' not in html_content.lower() and '<body' not in html_content.lower():
                html_content = f'<div dir="{direction}" style="direction: {direction};">{html_content}</div>'

            # Also add CSS direction style to ensure it takes effect (Bootstrap-friendly)
            if direction == 'rtl':
                style_injection = '''
                <style type="text/css">
                /* Bootstrap-friendly RTL support */
                html[dir="rtl"], body[dir="rtl"] {
                    direction: rtl !important;
                    text-align: right !important;
                }

                /* Bootstrap RTL adjustments */
                [dir="rtl"] .container,
                [dir="rtl"] .container-fluid,
                [dir="rtl"] .row,
                [dir="rtl"] .col,
                [dir="rtl"] .col-1, [dir="rtl"] .col-2, [dir="rtl"] .col-3, [dir="rtl"] .col-4,
                [dir="rtl"] .col-5, [dir="rtl"] .col-6, [dir="rtl"] .col-7, [dir="rtl"] .col-8,
                [dir="rtl"] .col-9, [dir="rtl"] .col-10, [dir="rtl"] .col-11, [dir="rtl"] .col-12 {
                    direction: rtl !important;
                }

                /* Bootstrap text alignment for RTL */
                [dir="rtl"] .text-left { text-align: right !important; }
                [dir="rtl"] .text-right { text-align: left !important; }
                [dir="rtl"] .float-left { float: right !important; }
                [dir="rtl"] .float-right { float: left !important; }

                /* Bootstrap margin/padding RTL adjustments */
                [dir="rtl"] .ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
                [dir="rtl"] .mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
                [dir="rtl"] .ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
                [dir="rtl"] .mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
                [dir="rtl"] .ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
                [dir="rtl"] .mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
                [dir="rtl"] .ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
                [dir="rtl"] .mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
                [dir="rtl"] .ml-5 { margin-right: 3rem !important; margin-left: 0 !important; }
                [dir="rtl"] .mr-5 { margin-left: 3rem !important; margin-right: 0 !important; }

                [dir="rtl"] .pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
                [dir="rtl"] .pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
                [dir="rtl"] .pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
                [dir="rtl"] .pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
                [dir="rtl"] .pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
                [dir="rtl"] .pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
                [dir="rtl"] .pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
                [dir="rtl"] .pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
                [dir="rtl"] .pl-5 { padding-right: 3rem !important; padding-left: 0 !important; }
                [dir="rtl"] .pr-5 { padding-left: 3rem !important; padding-right: 0 !important; }

                /* Table RTL support */
                [dir="rtl"] table { direction: rtl !important; }
                [dir="rtl"] th, [dir="rtl"] td { text-align: right !important; }

                /* Report-specific RTL */
                [dir="rtl"] .page { direction: rtl !important; }
                [dir="rtl"] .report { direction: rtl !important; }

                /* Form controls RTL */
                [dir="rtl"] .form-control { text-align: right !important; }
                [dir="rtl"] .input-group { direction: rtl !important; }

                /* Flexbox RTL adjustments */
                [dir="rtl"] .d-flex { direction: rtl !important; }
                [dir="rtl"] .justify-content-start { justify-content: flex-end !important; }
                [dir="rtl"] .justify-content-end { justify-content: flex-start !important; }
                </style>
                '''
                # Insert style at the beginning of head or body
                if '<head>' in html_content.lower():
                    html_content = html_content.replace('<head>', f'<head>{style_injection}', 1)
                elif '<body>' in html_content.lower():
                    html_content = html_content.replace('<body>', f'<body>{style_injection}', 1)
                else:
                    html_content = style_injection + html_content

        except Exception as e:
            _logger.error(f"Error in _add_direction_support: {e}")

        return html_content

    def _ensure_css_accessibility(self, html_content):
        """Ensure CSS URLs are accessible but keep them as URLs (not inlined)"""
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')

        # Find CSS links and log them for debugging
        css_link_patterns = [
            r'<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']*)["\'][^>]*>',
            r'<link[^>]*href=["\']([^"\']*)["\'][^>]*rel=["\']stylesheet["\'][^>]*>',
            r'<link[^>]*type=["\']text/css["\'][^>]*href=["\']([^"\']*)["\'][^>]*>',
        ]

        css_links = set()
        for pattern in css_link_patterns:
            css_links.update(re.findall(pattern, html_content, re.IGNORECASE))

        _logger.info(f"Found {len(css_links)} CSS links (keeping as URLs): {list(css_links)}")

        # Just log which CSS files we found - don't inline them
        for css_url in css_links:
            if css_url.startswith('/'):
                _logger.info(f"CSS URL will be converted to absolute: {css_url}")
            else:
                _logger.info(f"CSS URL is already absolute: {css_url}")

        return html_content



    def _inline_images_from_files(self, html_content):
        """Inline images from file paths into base64 data URLs with custom path mapping"""
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')

        # Pattern to match img tags with src attributes
        img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
        img_matches = re.findall(img_pattern, html_content, re.IGNORECASE)

        for img_src in img_matches:
            try:
                # Skip if already a data URL or external URL
                if img_src.startswith('data:') or img_src.startswith('http'):
                    continue

                image_data = self._get_image_data(img_src)
                if image_data:
                    # Replace the src attribute with base64 data URL
                    old_src_pattern = rf'src=["\']' + re.escape(img_src) + r'["\']'
                    new_src = f'src="{image_data}"'
                    html_content = re.sub(old_src_pattern, new_src, html_content, flags=re.IGNORECASE)

            except Exception:
                continue

        # Also handle background images in CSS
        css_bg_pattern = r'background-image:\s*url\(["\']?([^"\']*)["\']?\)'
        css_bg_matches = re.findall(css_bg_pattern, html_content, re.IGNORECASE)

        for bg_img_src in css_bg_matches:
            try:
                # Skip if already a data URL or external URL
                if bg_img_src.startswith('data:') or bg_img_src.startswith('http'):
                    continue

                image_data = self._get_image_data(bg_img_src)
                if image_data:
                    # Replace the background-image URL with base64 data URL
                    old_bg_pattern = rf'url\(["\']?' + re.escape(bg_img_src) + r'["\']?\)'
                    new_bg = f'url({image_data})'
                    html_content = re.sub(old_bg_pattern, new_bg, html_content, flags=re.IGNORECASE)

            except Exception:
                continue

        return html_content

    def _get_image_data(self, img_src):
        """Get image data as base64 data URL from various sources"""
        try:
            # First try to get from database attachments
            image_data = self._get_image_from_database(img_src)
            if image_data:
                return image_data

            # Then try to get from static files with custom path mapping
            image_data = self._get_image_from_static_files(img_src)
            if image_data:
                return image_data

        except Exception:
            pass

        return None

    def _get_image_from_database(self, img_src):
        """Get image from database attachments"""
        try:
            img_filename = img_src.split('/')[-1]

            # Search for attachment by filename
            attachment = self.env['ir.attachment'].search([
                ('name', 'ilike', f'%{img_filename}%'),
                ('mimetype', 'ilike', 'image/%')
            ], limit=1)

            if not attachment:
                attachment = self.env['ir.attachment'].search([
                    ('name', '=', img_src),
                    ('mimetype', 'ilike', 'image/%')
                ], limit=1)

            if attachment and attachment.datas:
                mimetype = attachment.mimetype or 'image/png'
                return f'data:{mimetype};base64,{attachment.datas.decode()}'

        except Exception:
            pass

        return None

    def _get_image_from_static_files(self, img_src):
        """Get image from static files and convert to base64 data URL with custom path mapping"""
        try:
            # Handle paths like olivery_client/static/src/images/A5.jpg
            img_src_clean = img_src.lstrip('/')
            parts = img_src_clean.split('/')

            if len(parts) > 0:
                module_name = parts[0]
                rel_path = os.path.join(*parts[1:]) if len(parts) > 1 else ''

                # Custom path mapping based on module name
                if module_name == 'olivery_client':
                    # If path starts with olivery_client, use /var/lib/odoo/custom/olivery_client
                    base_paths = [
                        os.path.join('/var/lib/odoo/custom', module_name),
                    ]
                elif module_name == 'rb_delivery':
                    # If rb_delivery, keep as is (use standard addon paths)
                    base_paths = [
                        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                        '/opt/odoo/addons',
                        '/var/lib/odoo/addons',
                        '/usr/lib/python3/dist-packages/odoo/addons',
                        './addons'
                    ]
                    base_paths = [os.path.join(base, module_name) for base in base_paths]
                else:
                    # For anything else, use /var/lib/odoo/extra/path
                    base_paths = [
                        os.path.join('/var/lib/odoo/extra', module_name),
                    ]

                for base_path in base_paths:
                    full_path = os.path.join(base_path, rel_path)
                    if os.path.exists(full_path) and os.path.isfile(full_path):
                        try:
                            with open(full_path, 'rb') as f:
                                image_content = f.read()

                            # Get MIME type
                            mimetype, _ = mimetypes.guess_type(full_path)
                            if not mimetype or not mimetype.startswith('image/'):
                                # Default to common image types based on extension
                                ext = os.path.splitext(full_path)[1].lower()
                                if ext == '.jpg' or ext == '.jpeg':
                                    mimetype = 'image/jpeg'
                                elif ext == '.png':
                                    mimetype = 'image/png'
                                elif ext == '.gif':
                                    mimetype = 'image/gif'
                                elif ext == '.svg':
                                    mimetype = 'image/svg+xml'
                                else:
                                    mimetype = 'image/png'  # Default fallback

                            # Convert to base64 data URL
                            base64_data = base64.b64encode(image_content).decode('utf-8')
                            return f'data:{mimetype};base64,{base64_data}'

                        except Exception:
                            continue

        except Exception:
            pass

        return None

    @api.model
    def _run_wkhtmltopdf(
            self,
            bodies,
            header=None,
            footer=None,
            landscape=False,
            specific_paperformat_args=None,
            set_viewport_size=False):
        paperformat_id = self.get_paperformat()
        command_args = self._build_wkhtmltopdf_args(
            paperformat_id,
            landscape,
            specific_paperformat_args=specific_paperformat_args,
            set_viewport_size=set_viewport_size)
        shared_dir = '/var/lib/odoo/pdfs'
        if not os.path.exists(shared_dir):
            os.makedirs(shared_dir, mode=0o755, exist_ok=True)
        files_command_args = []
        temporary_files = []
        if header:
            header = self._convert_html_links(header)
            head_file_fd, head_file_path = tempfile.mkstemp(suffix='.html', prefix='report.header.tmp.', dir=shared_dir)
            with closing(os.fdopen(head_file_fd, 'wb')) as head_file:
                head_file.write(header)
            temporary_files.append(head_file_path)
            files_command_args.extend(['--header-html', head_file_path])
        if footer:
            footer = self._convert_html_links(footer)
            foot_file_fd, foot_file_path = tempfile.mkstemp(suffix='.html', prefix='report.footer.tmp.', dir=shared_dir)
            with closing(os.fdopen(foot_file_fd, 'wb')) as foot_file:
                foot_file.write(footer)
            temporary_files.append(foot_file_path)
            files_command_args.extend(['--footer-html', foot_file_path])
        paths = []
        for i, body in enumerate(bodies):
            body = self._convert_html_links(body)
            prefix = '%s%d.' % ('report.body.tmp.', i)
            body_file_fd, body_file_path = tempfile.mkstemp(suffix='.html', prefix=prefix, dir=shared_dir)
            with closing(os.fdopen(body_file_fd, 'wb')) as body_file:
                body_file.write(body)
            paths.append(body_file_path)
            temporary_files.append(body_file_path)
        pdf_report_fd, pdf_report_path = tempfile.mkstemp(suffix='.pdf', prefix='report.tmp.', dir=shared_dir)
        os.close(pdf_report_fd)
        temporary_files.append(pdf_report_path)
        try:
            command_string = "--enable-local-file-access "
            if shared_dir.startswith('/var/lib/odoo'):
                command_string += "--allow /var/lib/odoo/pdfs "
            else:
                command_string += f"--allow {shared_dir} "

            # Add RTL support for wkhtmltopdf
            try:
                lang = self.env.context.get('lang', 'en_US')
                is_rtl = (lang.startswith('ar_') or lang == 'ar' or
                         lang.startswith('he_') or lang == 'he' or
                         lang.startswith('fa_') or lang == 'fa' or
                         lang.startswith('ur_') or lang == 'ur')

                if is_rtl:
                    # Add RTL-specific wkhtmltopdf options
                    command_string += "--load-error-handling ignore "
                    command_string += "--load-media-error-handling ignore "
                    # Enable JavaScript to handle RTL rendering properly
                    command_string += "--enable-javascript "
                    command_string += "--javascript-delay 1000 "
                    # Set encoding to handle RTL text properly
                    command_string += "--encoding utf-8 "
                    _logger.info(f"Added RTL support to wkhtmltopdf command for language: {lang}")
            except Exception as e:
                _logger.warning(f"Could not add RTL support to wkhtmltopdf: {e}")

            for arg in command_args:
                command_string += f"{arg} "
            for i in range(0, len(files_command_args), 2):
                if i+1 < len(files_command_args):
                    option = files_command_args[i]
                    path = files_command_args[i+1]
                    if shared_dir.startswith('/var/lib/odoo'):
                        rel_path = os.path.relpath(path, '/var/lib/odoo')
                        command_string += f'{option} file:///var/lib/odoo/{rel_path} '
                    else:
                        command_string += f'{option} file://{path} '
            for path in paths:
                if shared_dir.startswith('/var/lib/odoo'):
                    rel_path = os.path.relpath(path, '/var/lib/odoo')
                    command_string += f'file:///var/lib/odoo/{rel_path} '
                else:
                    command_string += f'file://{path} '
            command_string = 'wkhtmltopdf ' + command_string
            if self._context.get('get_command'):
                payload = {
                    'command_string': command_string
                }
                return payload
            else:
                baseUrl = 'http://wep-api-clusterip'
                payload = {
                    'command_string': command_string
                }
                response = requests.post(
                    f'{baseUrl}/convert-advanced',
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                )
                if response.status_code != 200:
                    error_data = response.json() if response.headers.get('content-type') == 'application/json' else {'error': response.text}
                    error_msg = error_data.get('error', 'Unknown error')
                    details = error_data.get('details', '')
                    raise UserError('Wkhtmltopdf API failed: %s. Details: %s' % (error_msg, details))
                pdf_content = response.content
                return pdf_content, payload 

        except requests.exceptions.RequestException as e:
            raise UserError('Failed to connect to wkhtmltopdf service: %s' % str(e))
        except Exception as e:
            raise UserError('Unexpected error: %s' % str(e))


    @api.model
    def get_wkhtmltopdf_state(self):
        return "ok"

    @api.multi
    def render_qweb_pdf(self, res_ids=None, data=None):
        if not data:
            data = {}
        data.setdefault('report_type', 'pdf')

        data.update(enable_editor=False)

        if (tools.config['test_enable'] or tools.config['test_file']) and not self.env.context.get('force_report_rendering'):
            return self.render_qweb_html(res_ids, data=data)

        context = dict(self.env.context)
        if not config['test_enable']:
            context['commit_assetsbundle'] = True

        context['debug'] = False
        if isinstance(self.env.cr, TestCursor):
            return self.with_context(context).render_qweb_html(res_ids, data=data)[0]

        save_in_attachment = OrderedDict()
        if res_ids:
            Model = self.env[self.model]
            record_ids = Model.browse(res_ids)
            wk_record_ids = Model
            if self.attachment:
                for record_id in record_ids:
                    attachment_id = self.retrieve_attachment(record_id)
                    if attachment_id:
                        save_in_attachment[record_id.id] = attachment_id
                    if not self.attachment_use or not attachment_id:
                        wk_record_ids += record_id
            else:
                wk_record_ids = record_ids
            res_ids = wk_record_ids.ids

        if save_in_attachment and not res_ids:
            _logger.info('The PDF report has been generated from attachments.')
            return self._post_pdf(save_in_attachment), 'pdf'

        if self.get_wkhtmltopdf_state() == 'install':
            raise UserError(_("Unable to find Wkhtmltopdf on this system. The PDF can not be created."))

        html = self.with_context(context).render_qweb_html(res_ids, data=data)[0]

        html = html.decode('utf-8')

        bodies, html_ids, header, footer, specific_paperformat_args = self.with_context(context)._prepare_html(html)

        if self.attachment and set(res_ids) != set(html_ids):
            raise UserError(_("The report's template '%s' is wrong, please contact your administrator. \n\n"
                "Can not separate file to save as attachment because the report's template does not contains the attributes 'data-oe-model' and 'data-oe-id' on the div with 'article' classname.") %  self.name)
        
        pdf_content = self.with_context(get_command=context.get('get_command'))._run_wkhtmltopdf(
            bodies,
            header=header,
            footer=footer,
            landscape=context.get('landscape'),
            specific_paperformat_args=specific_paperformat_args,
            set_viewport_size=context.get('set_viewport_size'),
        )
        return pdf_content
