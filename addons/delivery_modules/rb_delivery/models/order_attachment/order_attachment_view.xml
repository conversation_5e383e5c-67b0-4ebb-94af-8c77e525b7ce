<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_form_delivery_order_attachment" model="ir.ui.view">
        <field name="name">view_form_delivery_order_attachment</field>
        <field name="model">rb_delivery.order_attachment</field>
        <field name="arch" type="xml">
          <form>
              <header>
                  <button name="get_download_url" type="object" string="DOWNLOAD_FILE" icon="fa-download" class="oe_highlight"/>
              </header>
              <sheet>
                <div class="oe_title">
                  <h1><field name="name" placeholder="File Name"/></h1>
                </div>
                <group>
                  <group>
                    <field name="file_type"/>
                    <field name="mimetype"/>
                    <field name="order_id"/>
                    <field name="create_date" readonly="1"/>
                  </group>
                  <group>
                    <field name="attachment" filename="name"
                      style="width: 300px; height: auto;"/>
                  </group>
                </group>
              </sheet>
              <div class="oe_chatter">
                <field name="message_follower_ids" widget="mail_followers"/>
                <field name="message_ids" widget="mail_thread"/>
              </div>
          </form>
        </field>
      </record>

    <record id="view_tree_delivery_order_attachment" model="ir.ui.view">
        <field name="name">view_tree_delivery_order_attachment</field>
        <field name="model">rb_delivery.order_attachment</field>
        <field name="arch" type="xml">
          <tree>
            <field name="name" string="FILE_NAME"/>
            <field name="file_type"/>
            <field name="create_date"/>
            <field name="order_id"/>
            <field name="id" invisible="1"/>
            <field name="attachment" filename="name" widget="binary" readonly="1"/>
          </tree>
        </field>
      </record>

    <record id="view_search_delivery_order_attachment" model="ir.ui.view">
        <field name="name">view_search_delivery_order_attachment</field>
        <field name="model">rb_delivery.order_attachment</field>
        <field name="arch" type="xml">
          <search>
            <field name="name"/>
            <field name="file_type"/>
            <field name="order_id"/>
            <filter string="Images" name="images" domain="[('mimetype', 'ilike', 'image/')]"/>
            <filter string="Documents" name="documents" domain="[('mimetype', 'ilike', 'application/')]"/>
            <group expand="0" string="Group By">
              <filter string="File Type" name="group_by_type" context="{'group_by': 'file_type'}"/>
              <filter string="Order" name="group_by_order" context="{'group_by': 'order_id'}"/>
              <filter string="Month" name="group_by_month" context="{'group_by': 'create_date:month'}"/>
            </group>
          </search>
        </field>
      </record>

    <!-- Action for attachments -->
    <record id="action_view_order_attachments" model="ir.actions.act_window">
        <field name="name">Order Attachments</field>
        <field name="res_model">rb_delivery.order_attachment</field>
        <field name="view_type">form</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="view_search_delivery_order_attachment"/>
        <field name="help" type="html">
          <p class="oe_view_nocontent_create">
            No attachments found.
          </p>
        </field>
      </record>

    <!-- Kanban view for attachments -->
    <record id="view_kanban_delivery_order_attachment" model="ir.ui.view">
        <field name="name">view_kanban_delivery_order_attachment</field>
        <field name="model">rb_delivery.order_attachment</field>
        <field name="arch" type="xml">
          <kanban class="o_kanban_small_column">
            <field name="id"/>
            <field name="name"/>
            <field name="attachment"/>
            <field name="file_type"/>
            <field name="mimetype"/>
            <templates>
              <t t-name="kanban-box">
                <div class="o_kanban_record">
                  <div class="oe_kanban_details">
                    <div class="row">
                      <div class="col-md-12">
                        <div t-if="record.mimetype.raw_value and record.mimetype.raw_value.indexOf('image/') === 0" style="text-align: center;">
                          <img t-if="record.attachment.raw_value" t-att-src="'data:' + record.mimetype.raw_value + ';base64,' + record.attachment.raw_value" style="width: 179px; height: 102px; object-fit: cover;"/>
                        </div>
                        <!-- Display file icon for non-image files -->
                        <div t-if="!record.mimetype.raw_value || record.mimetype.raw_value.indexOf('image/') !== 0" style="text-align: center;">
                          <div style="font-size: 48px; color: #7C7BAD; margin-bottom: 10px;">
                            <i t-if="record.file_type.raw_value === 'pdf'" class="fa fa-file-pdf-o"/>
                            <i t-elif="['xls', 'xlsx', 'csv'].indexOf(record.file_type.raw_value) !== -1" class="fa fa-file-excel-o"/>
                            <i t-elif="['doc', 'docx', 'rtf', 'txt'].indexOf(record.file_type.raw_value) !== -1" class="fa fa-file-word-o"/>
                            <i t-elif="['ppt', 'pptx'].indexOf(record.file_type.raw_value) !== -1" class="fa fa-file-powerpoint-o"/>
                            <i t-elif="['zip', 'rar', 'tar', 'gz', '7z'].indexOf(record.file_type.raw_value) !== -1" class="fa fa-file-archive-o"/>
                            <i t-else="" class="fa fa-file-o"/>
                          </div>
                        </div>
                        <div style="text-align: center; margin-top: 5px;">
                          <div style="font-weight: bold; margin-bottom: 5px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            <t t-esc="record.name.value || 'Attachment'"/>
                          </div>
                          <a t-att-href="'/rb_delivery/attachment/download/' + record.id.raw_value" class="btn btn-sm btn-primary" target="_blank">
                            <i class="fa fa-download"/> Download
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </t>
            </templates>
          </kanban>
        </field>
      </record>

</odoo>
