# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import random
import string
class rb_delivery_change_password(models.Model):

    _name = 'rb_delivery.change_password'
    _description = "Change Password Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    def get_auto_generated_password(self):
        letters = string.ascii_letters
        result_str = ''.join(random.choice(letters) for i in range(8))
        return result_str
     
    
    user_id = fields.Many2one('res.users', required=True, readonly = True, ondelete = 'cascade')

    password = fields.Char('Password', required=True,default=get_auto_generated_password)

    # ----------------------------------------------------------------------
    # Create, Update
    # ----------------------------------------------------------------------

    @api.model
    def create(self, values):
        change = super(rb_delivery_change_password, self).create(values)
        change.user_id.sudo().write({'password': change.password, 'forgot_password': False})
        change.user_id.access_token_ids.sudo().unlink()
        return change

