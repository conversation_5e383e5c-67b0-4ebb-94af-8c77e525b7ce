# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from datetime import datetime
from onesignal_sdk.client import Client
import re
from dateutil.relativedelta import relativedelta
import pytz
import logging
_logger = logging.getLogger(__name__)
class rb_delivery_notification_center(models.Model):

    _name = 'rb_delivery.notification_center'
    _order = "create_date DESC"
    _description = "Notification Center Model"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    name = fields.Char('Name')

    model_name = fields.Char('Model Name')

    notification_type = fields.Many2one('rb_delivery.notification_type','Notification Type')

    #deprecated to be deleted later
    record_id = fields.Char('Record ID')

    user_id = fields.Many2one('rb_delivery.user', 'User', ondelete='restrict')

    order_id = fields.Many2one('rb_delivery.order', 'Order')

    sequence = fields.Char(related="order_id.sequence", string='Sequence', store=True)

    notification_title = fields.Char('Title')

    notification_message = fields.Text('Notification message')

    seen = fields.Boolean( default=False,string="Seen")

    is_chat_notification = fields.Boolean( string="Is chat notification",compute="compute_notification_type",store=True ,readonly=True)
    
    is_announcement = fields.Boolean( string="Is Announcement",readonly = False)
    
    announcement_link = fields.Text('Announcement Link')
    
    # ----------------------------------------------------------------------
    # Constraints
    # ----------------------------------------------------------------------

    # ----------------------------------------------------------------------
    # Functions
    # ----------------------------------------------------------------------

    @api.depends('notification_type')
    @api.one
    def compute_notification_type(self):
        if self.notification_type and self.notification_type['name'] and self.notification_type.name == 'chat_notification':
            self.is_chat_notification = True
        return self.is_chat_notification

    @api.model
    def get_seen_notification_count(self):
        count = 0
        count = self.env['rb_delivery.notification_center'].search_count([('seen','=',True)])
        return count

    @api.model
    def get_un_seen_notification_count(self):
        count = 0
        count = self.env['rb_delivery.notification_center'].search_count([('seen','=',False)])
        return count

    def create_notification_record(self,user_id,message,title,order_id,model,sequence,notification_type="text",record_id=False,announcement_link=False):
        order=order_id
        if order_id and not str(order_id).isnumeric():
            order=order_id.id
        notification_type_value = notification_type
        notification_type = self.env['rb_delivery.notification_type'].sudo().search([('name','=',notification_type)],limit=1)
        if notification_type:
            notification_type = notification_type.id
        else:
            notification_type = self.env['rb_delivery.notification_type'].sudo().create({'name':notification_type_value})
            if notification_type:
                notification_type = notification_type.id
        date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        values={'sequence':sequence,'model_name':model,'user_id':user_id,'name':model+date,'order_id':order,'notification_type':notification_type,'notification_message':message,'notification_title':title,'seen':False,'record_id':record_id,'is_announcement':self._context.get('is_announcement'),'announcement_link':announcement_link}
        notification = self.sudo().create(values)
        return notification

    def send_email_forget_password(self,email,message,user,header):
        variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
        message = self.get_message(message,variables,user,None,'rb_delivery.user')
        template_obj = self.env['mail.mail']
        email_from = f'"{user.username}" <{email}>'
        template_data = {
            'subject': header,
            'body_html': message,
            'email_from': email_from,
            'email_to': email}
        template_id = template_obj.create(template_data)
        template_id.send(raise_exception=True)


    def notification(self, users, emails, players, mobile_numbers, header, message, is_sms, is_email, is_notification,record_id,sequence,model,attachment_ids=False,model_name=False,notification_sound_id = False,show_notification_timer=False,notification_timer_duration=False,is_web_notification=False,notification_group_type=False,collapse_id=False,is_announcement = False, announcement_link = False):
        if isinstance(record_id,int):
            record_id = record_id
        elif record_id:
            record_id = record_id.id
        conf = self.env['rb_delivery.one_signal'].sudo().search([], limit=1)
        notification_sound=[]
        if notification_sound_id :
            notification_sound =  notification_sound_id
        else:
            notification_sound = self.env['rb_delivery.notification_sound'].sudo().search([['is_default','=',True]],limit=1)
        variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
        if is_email:
            default_user = self.env['rb_delivery.client_configuration'].get_param('user_to_send_emails')
            user_name=''
            if default_user:
                user = self.env['rb_delivery.user'].sudo().search([('id','=',default_user[0])])
                if user and user.email:
                    company_email = user.email
                    user_name = user.username
            else:
                company = self.env['res.company'].search([])
                company_email = company[0].email
                user_name = company[0].name
            if not company_email:
                self.env['rb_delivery.error_log'].raise_olivery_error(420,self.id,{})
                #raise ValidationError('Please add an email to your company')
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", message)
            index = 0
            for email in emails:
                message = self.get_message(message,variables,users[index],record_id,model)
                template_obj = self.env['mail.mail']
                email_from = '"'+user_name+'" <'+company_email+'>'
                template_data = {
                    'subject': header,
                    'body_html': message,
                    'email_from': email_from,
                    'email_to': email}
                if attachment_ids:
                    template_data['attachment_ids'] = attachment_ids
                else:
                    try:
                        record = self.env[model].browse(record_id)
                        attachments = self.env[model].get_attatchments(record)
                        template_data['attachment_ids'] = attachments
                    except:
                        pass
                template_id = template_obj.create(template_data)
                template_id.send(raise_exception=True)
                index +=1

        if is_notification:
            client = Client(app_id=str(conf.app_id), rest_api_key=str(conf.app_auth_key), user_auth_key=str(conf.user_auth_key))
            index = 0
            if not header:
                    header = ''
            for player in players:
                message = self.get_message(message,variables,users[index],record_id,model)
                if not collapse_id:
                    collapse_id="no_group_{record_id}"
                notification_body = {
                        'headings': {'en': header},
                        'contents': {'en': message},
                        'include_player_ids': [player],
                        'filters': [],
                        'data': {'order_id': record_id, 'sequence': sequence, 'model': model},
                        'android_group': notification_group_type,
                        'android_group_message': {'en': "$[notif_count] new notifications"},
                        'collapse_id': str(collapse_id),
                        'ios_thread_id': notification_group_type
                    }
                try:
                    # threading.Thread(target=client.send_notification(notification_body)).start()
                    # pass
                    if show_notification_timer and notification_timer_duration:
                        notification_body['data']['type'] = 'notification_timer'
                        notification_body['data']['notification_timer'] = int(notification_timer_duration)
                        notification_body['data']['accept_status'] = 'picking_up'
                        notification_body['data']['reject_status'] = 'rejected_by_driver'
                        notification_body['data']['create_date'] = str(datetime.now())

                    if len(notification_sound) > 0 :
                        notification_body['android_channel_id'] = notification_sound[0]['android_channel_id']
                        notification_body['existing_android_channel_id'] = notification_sound[0]['existing_android_channel_id']
                        notification_body['priority'] = int(notification_sound[0]['priority'])
                        notification_body['ios_sound'] = notification_sound[0]['key']+'.wav'

                    client.send_notification(notification_body)
                    if model == 'rb_delivery.order':
                        self.create_notification_record(users[index].id,message,header,record_id,model,sequence,'text')
                    else:
                        self.create_notification_record(users[index].id,message,header,False,model,sequence,'text',record_id)
                except Exception as e:
                    pass
                index += 1
        if is_sms:
            index = 0
            #if else added for customer sending message
            for mobile_number in mobile_numbers:
                if len(users) == 0:
                    message = self.get_message(message,variables,False,record_id,model)
                else:
                    message = self.get_message(message,variables,users[index],record_id,model)


                user_id = False
                if len(users) > 0:
                    user_id = users[index].id
                self.env['rb_delivery.sms'].sudo().send_sms(message,mobile_number,user_id,record_id,model_name='rb_delivery.user')

                self.create_notification_record(user_id,message,header,record_id,model,sequence,'sms')

                index += 1
        if is_web_notification:
            users_ids = []
            index = 0
            messages = ''
            for user in users:
                messages = self.get_message(message,variables,users[index],record_id,model)
                if user.user_id:
                    users_ids.append(user.user_id.id)
                    index += 1
                    self.env['rb_delivery.utility'].send_toast('for_user', ['chat_notification',users_ids,model,messages,'record_id//'+str(record_id)] , str(user.user_id.id))
        
        if is_announcement:
            client = Client(app_id=str(conf.app_id), rest_api_key=str(conf.app_auth_key), user_auth_key=str(conf.user_auth_key))
            index = 0
            if not header:
                    header = 'Announcement'
            for player in players:
                if collapse_id:
                    collapse_id=f"{collapse_id}_{record_id}"
                else:
                    collapse_id=f"{record_id}_announcement"
                notification_body = {
                        'headings': {'en': header},
                        'contents': {'en': message},
                        'include_player_ids': [player],
                        'filters': [],
                        'data': {'announcement_id': record_id,'title': header,'description': message,'model': model_name, 'announcement_link' : announcement_link },
                        'android_group': notification_group_type,
                        'android_group_message': {'en': "$[notif_count] new notifications"},
                        'collapse_id': collapse_id,
                        'ios_thread_id': notification_group_type
                    }
                try:
                    if len(notification_sound) > 0 :
                        notification_body['android_channel_id'] = notification_sound[0]['android_channel_id']
                        notification_body['existing_android_channel_id'] = notification_sound[0]['existing_android_channel_id']
                        notification_body['priority'] = int(notification_sound[0]['priority'])
                        notification_body['ios_sound'] = notification_sound[0]['key']+'.wav'
                    client.send_notification(notification_body)
                    
                    self.with_context(is_announcement=True).create_notification_record(users[index].id,message,header,False,model,sequence,'text',record_id,announcement_link=announcement_link)
                except Exception as e:
                    pass
                index += 1
    def get_message(self,message,variables,user,record_id,model_name=False):
        for variable in variables:
            cut = variable.strip('{}')
            model = ''
            variable_value =''
            related_value = ''
            cut = cut.split('.')
            model = cut[0]
            variable_value = cut[1]
            if len(cut)>2:
                related_value= cut[2]

            value_to_replace = None
            if model  == "user":
                if related_value:
                    value_to_replace = user.sudo()[variable_value][related_value]
                else:
                    value_to_replace = user.sudo()[variable_value]
            elif model == "order":
                if not model_name:
                    model_name = 'rb_delivery.order'
                if record_id:
                    order = self.env[model_name].browse([record_id])
                    if order.sudo().assign_to_business and order.sudo().assign_to_business.user_id and order.sudo().assign_to_business.user_id.lang:
                        order = order.with_context(lang=order.sudo().assign_to_business.user_id.lang)
                    if related_value:
                        value_to_replace = order.sudo()[variable_value][related_value]
                    else:
                        value_to_replace = order.sudo()[variable_value]
            elif model == 'collection' and model_name and record_id:
                collection = self.env[model_name].browse([record_id])
                if collection:
                    if related_value:
                        value_to_replace = collection.sudo()[variable_value][related_value]
                    else:
                        value_to_replace = collection.sudo()[variable_value]


            # Only replace the variable if value_to_replace is not False
            if value_to_replace is not False:
                message = message.replace(variable, str(value_to_replace))
            else:
                message = message.replace(variable, '')

        return message

    def archive_old_notification_center(self):
        timezone = self._context.get('tz') or self.env.user.tz or 'Asia/Hebron'
        date = datetime.now(pytz.timezone(timezone))
        fmt = "%Y-%m-%d"
        date = date - relativedelta(months=6)
        date = datetime.strftime(date,fmt) + ' 00:00:00'
        records = self.env['rb_delivery.notification_center'].sudo().search([('create_date','<',date)])
        batch_list = [records[i:i + 1000] for i in range(0, len(records), 1000)]
        for batch in batch_list:
            self.with_delay(channel="clear.notification_center",max_retries=2).archive_records(batch)

        return True

    def archive_records(self,records):
        records.write({'active':False})

