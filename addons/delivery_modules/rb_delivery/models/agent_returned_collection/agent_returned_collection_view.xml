<odoo>
  <data>
   <!-- inherit module[olivery_branch_collection] -->
    <record id="view_form_rb_delivery_agent_returned_collection" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_agent_returned_collection</field>
      <field name="model">rb_delivery.agent_returned_collection</field>

      <field name="arch" type="xml">
        <form create="false">

          <header>
            <button string="Change status" name="wkf_action_change_status" type="object" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,rb_delivery.role_business,base.group_system"/>
            <field name="state" widget="statusbar" statusbar_visible=" "/>
          </header>

          <sheet>
            <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh" attrs="{'invisible':[('write_date', '=', False)]}">
                <button type="object" name="get_signature" class="btn btn-sm oe_stat_button o_form_invisible">
                    <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                    <div class="o_form_field o_stat_info" data-original-title="" title="">
                        <span>Signature</span>
                    </div>
                  </button>
                <button type="object" name="get_orders" class="btn btn-sm oe_stat_button o_form_invisible">
                  <div class="fa fa-fw fa-pencil-square-o o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                      <span>Orders</span>
                  </div>
                </button>
            </div>

            <group name="group_top">
              <group name="group-right">
                <field name="write_date" invisible='1'/>
                <field name="name"/>
                <field name="create_uid" options="{'no_open': True}"/>
                <field name="create_date"/>
                <field name="business_id" attrs="{'invisible': [('report_type','!=','business')]}"/>
                <field name="agent_id"/>
                <field name="previous_status"/>
                <field name="payment_type"/>
                <field name="payment_detail" attrs="{'invisible':[('payment_type','=',False)]}"/>
                <field name="total_ammount"/>
                <field name="total_money_collection_cost"/>
                <field name="report_type" invisible="1"/>
                <field name="stuck_orders"/>
                <field name="rejected_orders"/>
                <field name="cancelled_orders"/>
                <field name="partial_rejected_orders"/>
                <field name="reschedule_orders"/>
                <field name="replacement_orders"/>
              </group>
              <group name="group-left">
                <div class="oe_right" >
                <field name="use_qr_code" readonly="1" invisible="1"/>
                <field name="barcode" style="display:block;text-align:center;width:200px" height="100" width="200" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('use_qr_code','=',True)]}"/>
                <field name="qr_code_image" style="display:block;text-align:center;max-width:150px;max-height:150px!important" height="150" width="150" widget="image" class="oe_center" nolabel="1" attrs="{'invisible':[('use_qr_code','=',False)]}"/>
                <field name="sequence" string="Sequence Number" style="display:block;text-align:center;width:200px" />
                </div>
              </group>
            </group>
            <group name="group_top">

              <field name="is_collection_manager" invisible="1"/>
              <field name="allow_edit_collection_orders" invisible="1"/>
              <field name="order_ids" attrs="{'readonly':[('allow_edit_collection_orders','=',False),('is_collection_manager','=',False)]}">
                <tree delete="1">
                <field name="is_block_delivery_fee" invisible="1"/>
                  <field name="sequence_related"/>
                  <field name="reference_id"/>
                  <field name="assign_to_business"/>
                  <field name="customer_name" />
                  <field name="customer_mobile"/>
                  <field name="customer_area"/>
                  <field name="customer_address"/>
                  <field name="stuck_comment"/>
                   <field name="previous_agent"/>
                  <field name="state"/>
                  <field name="money_collection_cost" sum="Money collection" groups="rb_delivery.role_super_manager,base.group_system"/>
                  <field name="delivery_cost" sum="Delivery Cost" attrs="{'invisible':[('is_block_delivery_fee','=',True)]}"/>
                  <field name="required_from_business" sum="Required from business" groups="rb_delivery.role_super_manager,base.group_system"/>
                </tree>
              </field>
            </group>

          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_agent_returned_collection_two" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_agent_returned_collection_two</field>
      <field name="model">rb_delivery.agent_returned_collection</field>

      <field name="arch" type="xml">
        <form create="false" edit="false">

          <header>
          </header>

          <sheet>
            <group name="group_top">
              <field name="show_note" invisible="1"/>
              <field name="name" string="Note" attrs="{'invisible': [('show_note','=',False)]}"/>
              <separator attrs="{'invisible': [('show_note','=',True)]}" string="Are you sure?"/>
            </group>
          </sheet>

          <footer>
          <button name="write" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>
        </form>

      </field>
    </record>

    <record id="view_tree_rb_delivery_agent_returned_collection" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_agent_returned_collection</field>
      <field name="model">rb_delivery.agent_returned_collection</field>
      <field name="arch" type="xml">
        <tree create="false" class="collection_tree">
        <field name="is_block_delivery_fee" invisible="1"/>
          <field name="sequence"/>
          <field name="name"/>
          <field name="state"/>
          <field name="create_uid" options="{'no_open': True}"/>
          <field name="create_date"/>
          <field name="business_id"/>
          <field name="agent_id"/>
          <field name="total_money_collection_cost" sum="Total money collection cost"/>
          <field name="total_delivery_cost" sum="Total Delivery cost"/>
          <field name="total_cost" sum="Total cost"/>
          <field name="order_count" sum="Orders"/>
          <field name="previous_status"/>
        </tree>
      </field>
    </record>

    <record id="view_tree_rb_delivery_agent_returned_collection_block_delivery" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_agent_returned_collection_block_delivery</field>
            <field name="model">rb_delivery.agent_returned_collection</field>
            <field name="inherit_id" ref="view_tree_rb_delivery_agent_returned_collection"/>
            <field name="groups_id" eval="[(6, 0, [ref('rb_delivery.role_olivery_block_delivery_fee') ])]"/>
            <field name="arch" type="xml">
                <field name="total_delivery_cost" position="attributes">
                    <attribute name="attrs">{'column_invisible':1}</attribute>
                </field>
            </field>
        </record>
    <record id="view_search_rb_delivery_agent_returned_collection" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_agent_returned_collection</field>
      <field name="model">rb_delivery.agent_returned_collection</field>
      <field name="arch" type="xml">
        <search >
          <group>
            <field name="sequence"/>
            <field name="create_uid" options="{'no_open': True}"/>
            <field name="create_date"/>
            <field name="business_id"/>
            <field name="agent_id" filter_domain="['|',('agent_id','ilike',self),('user_sequence','ilike',self)]"/>
            <field name="mobile_number"/>
            <field name="agent_mobile_number"/>
            <field name="mobile_number"/>
            <field name="state"/>
            <field name="previous_status"/>
          </group>
          <group string="Groups">
            <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
            <filter name="group_by_day" string="By Date" icon="terp-partner" context="{'group_by':'create_date:day'}"/>
            <filter name="group_by_assign_to_business" string="By Business Assigned" icon="terp-partner" context="{'group_by':'business_id'}" groups="rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_super_manager,base.group_system"/>
            <filter name="group_by_mobile_number" string="By Mobile Number" icon="terp-partner" context="{'group_by':'mobile_number'}"/>
            <filter name="group_by_state" string="By Status" icon="terp-partner" context="{'group_by':'state'}"/>
            <filter name="group_by_agent" string="By Agent" icon="terp-partner" context="{'group_by':'agent_id'}"/>
            <filter name="group_by_previous_status" string="By Previous Status" icon="terp-partner" context="{'group_by':'previous_status'}"/>
          </group>
        </search>
      </field>
    </record>
    <!-- inherit module[olivery_branch_collection] -->
    <record id="view_form_rb_delivery_order_select_agent_returned_money_collection_state" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_select_agent_returned_money_collection_state</field>
      <field name="model">rb_delivery.select_agent_returned_money_collection_state</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_state" confirm="Do you want to proceed?" type="object" string="Move to state" class="oe_highlight" /> -->
          </header>

          <sheet>

            <group name="group_top">
              <field name="state"/>
            </group>

          </sheet>
          <footer>
          <button name="select_state" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>
    <record id="view_form_rb_delivery_agent_returned_collection_detach_order" model="ir.ui.view">
            <field name="name">view_form_rb_delivery_agent_returned_collection_detach_order</field>
            <field name="model">rb_delivery.agent_returned_collection_detach_order</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">
                    <header>
                    </header>

                    <sheet>
                        <group name="group_top">
                            <separator string="Are you sure you want to clear orders?"/>
                        </group>
                    </sheet>

                    <footer>
                        <button name="detach_orders" type="object" string="Confirm"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

            </field>
        </record>
  </data>
</odoo>