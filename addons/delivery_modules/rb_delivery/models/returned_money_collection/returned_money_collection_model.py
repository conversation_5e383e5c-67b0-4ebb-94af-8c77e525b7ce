# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.exceptions import UserError
from openerp import SUP<PERSON>USER_ID
import base64
import qrcode
from io import BytesIO

class rb_delivery_returned_money_collection(models.Model):
    _name = 'rb_delivery.returned_money_collection'
    _inherit = 'mail.thread'
    _order = "sequence DESC"
    _description = "Returned Money Collection Model"

    name = fields.Char('Note')

    @api.model
    def _default_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            default_is_block_delivery_fee = True
        else :
            default_is_block_delivery_fee = False
        return default_is_block_delivery_fee

    @api.one
    def _compute_is_block_delivery_fee(self):
        user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        if user.block_delivery_fee:
            is_block_delivery_fee = True
        else :
            is_block_delivery_fee = False
        self.is_block_delivery_fee = is_block_delivery_fee

    def get_signature(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_signature').id
        domain = [('returned_collection_id', '=', self.id)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.signature',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = []
        for order in self.order_ids:
            ids.append(order.id)
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            # 'view_id ref="obe_planning.semester_tree_view"': '',
            'target': 'current',
            'domain': domain}

    @api.one
    def check_user(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        self.is_returned_collection_manager = user.has_group('rb_delivery.role_returned_collection_manager')

    def default_is_returned_collection_manager(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_returned_collection_manager = user.has_group('rb_delivery.role_returned_collection_manager')
        return is_returned_collection_manager

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note

    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','returned_collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if fields else None

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code

    report_type= fields.Selection([
        ('branch','Branch'),
        ('business','Business')
    ])

    sequence = fields.Char('Sequence', readonly=True,track_visibility=False,copy=False)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    business_id = fields.Many2one('rb_delivery.user', 'Business Name')

    mobile_number = fields.Char(related='business_id.mobile_number', readonly=True, store=False)

    business_longitude = fields.Char(related='business_id.longitude', readonly=True)

    business_latitude = fields.Char(related='business_id.latitude', readonly=True)

    second_business_mobile_number = fields.Char(related='business_id.second_mobile_number', readonly=True)

    business_whatsapp_mobile = fields.Char(related='business_id.whatsapp_mobile', readonly=True)

    business_second_whatsapp_mobile = fields.Char(related='business_id.second_whatsapp_mobile', readonly=True)

    address = fields.Char(related='business_id.address', readonly=True, store=False)

    area_id = fields.Many2one('rb_delivery.area',related='business_id.area_id', readonly=True, store=False)

    driver_id = fields.Many2one('rb_delivery.user', 'Driver')

    total_cost = fields.Float('Total Net Value')

    total_delivery_cost = fields.Float('Total Delivery Fee')

    total_ammount = fields.Float('Expected total amount')

    total_money_collection_cost = fields.Float("Total COD Value")

    final_cost = fields.Float('Final Cost')

    payment_type = fields.Many2one('rb_delivery.payment_type', 'Payment Method',track_visibility="on_change")

    previous_agent = fields.Many2one('rb_delivery.user', 'Previous Agent', track_visibility="on_change",copy=False,readonly=True)

    payment_detail = fields.Char(string='Payment Detail',track_visibility="on_change")

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    previous_status = fields.Char('Previous Status', track_visibility=False)

    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    previous_status_title = fields.Char('Previous Status Name',  track_visibility="on_change", readonly=True)

    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders',
        relation = 'print_order_returned_item',
        column1 = 'print_returned_id',
        column2 = 'order_id')

    active = fields.Boolean('Active', default=True ,track_visibility="on_change")

    is_block_delivery_fee = fields.Boolean('Is Block Delivery Fee', default=_default_is_block_delivery_fee, compute="_compute_is_block_delivery_fee", readonly=True)
    email_sent = fields.Boolean("Email Sent")

    order_count = fields.Integer(string="Order count")

    is_returned_collection_manager = fields.Boolean('Is Returned Collection Manager', compute="check_user", default=default_is_returned_collection_manager)

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)

    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')],limit=1)
                rec.state_id = state_id.id

    @api.model
    def get_status(self):
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','returned_collection')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list

    def check_order(self,order_ids):
        default_state = ''
        order_collection_ids = []
        if len(self.order_ids) > 0:
            default_state = self.order_ids[0].state
        returned_status = self.env['rb_delivery.client_configuration'].get_param('returned_collection_status')
        is_allowed_to_edit = self.env['rb_delivery.client_configuration'].get_param('ability_to_update_returned_collection_on_different_status')
        for order in self.order_ids:
            order_collection_ids.append(order.id)
        for order_id in order_ids:
            if order_id not in order_collection_ids:
                order = self.env['rb_delivery.order'].search([('id','=',order_id)])
                collections = self.env['rb_delivery.returned_money_collection'].search([('business_id','=',order.assign_to_business.id)])
                for collection in collections:
                    if order in collection.order_ids:
                        self.env['rb_delivery.error_log'].raise_olivery_error(280,self.id,{'order_sequence': order.sequence,'collection_sequence': collection.sequence})
                        #raise ValidationError(_("Order already exists in Returned Collection %s ") %(collection.sequence))
                if not is_allowed_to_edit and default_state != '' and order.state != default_state:
                    order_status = self.env['rb_delivery.status'].search([('name','=',default_state),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                    self.env['rb_delivery.error_log'].raise_olivery_error(281,self.id,{'order_sequence': order.sequence,'order_status': order_status.title})
                    #raise ValidationError(_('Order state should be '+order_status.title))
                elif default_state == '' or is_allowed_to_edit:
                    default_collection_status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','returned_collection'),('status_type','=','olivery_collection')],limit=1)
                    returned_states = []
                    message = ''
                    if not returned_status:
                        return
                    for state_id in returned_status:
                        state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                        returned_states.append(state.name)
                        message = message +' ' +state.title +'/'
                    if order.state and order.state not in returned_states:
                        self.env['rb_delivery.error_log'].raise_olivery_error(281,self.id,{'order_sequence': order.sequence,'order_status': message})
                        #raise ValidationError(_('Order state should be in ')+message)
                    else:
                        if default_collection_status and default_collection_status.default_related_order and default_collection_status.related_order_status:
                            order_status = self.env['rb_delivery.status'].search([('name','=',default_collection_status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                            if order_status and order_status.name and order_status.name != order.state:
                                data = {'uid':self._uid,'message':_("Orders status updated from update returned money collection status."),'records':order,'values':{'state':order_status.name},'update':True}
                                self.env['rb_delivery.utility'].olivery_sudo(data)
                    self.active = True
                    self.state =  default_collection_status.name

                if self.business_id and order.assign_to_business.id != self.business_id.id:
                    self.env['rb_delivery.error_log'].raise_olivery_error(282,self.id,{'order_business': self.business_id.username, 'order_sequance': order.sequence, 'order_state': order.state})
                    #raise ValidationError(_('Order business should be ')+self.business_id.username)


    # inherit module[olivery_branch_collection]
    @api.one
    def write(self, values):
        orders_vals = {}
        if ('driver_id' in values and values['driver_id']):
            if self.driver_id:
                values['previous_agent'] = self.driver_id.id
        if 'state' in values and values['state']:
            collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',values['state']),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')])
            if collection_state.related_order_status:
                order_state = self.env['rb_delivery.status'].sudo().search([('name','=',collection_state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                if order_state:
                    orders_vals['state'] = order_state.name
                    orders_vals['is_from_collection'] = True
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',action_name='returned_collection',state_name=values['state'],collection_type='returned_collection',object=self)
            self.do_action(values['state'])
            if values['state'] == 'deleted':
                self.active = False
                for order in self.order_ids:
                    order.write({'returned_collection_id':False})
            values['previous_status'] = self.state
            returned_money_collection_status = self.env['rb_delivery.status'].sudo().search([('name','=',values['previous_status']),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')])
            if returned_money_collection_status and returned_money_collection_status.title:
                values['previous_status_title'] = returned_money_collection_status.title
        else:
            if self.state == 'completed_returned':
                if self._uid == 1 or self._uid == 2 or ('active' in values  or 'message_main_attachment_id' in values and len(values)==1) or self.env.user.has_group('rb_delivery.role_configuration_manager'):
                    pass
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(283,self.id,{'state': self.state, 'collection_sequence': self.sequence})
                    #raise ValidationError(_("You can't edit the collection when the status is Completed Returned"))
        if 'order_ids' in values and values['order_ids'] and len(values['order_ids'])>0 and values['order_ids'][0] and len(values['order_ids'][0])>1:
            all_current_orders_in_collection = values['order_ids'][0][2]
            self.check_order(all_current_orders_in_collection)
            old_sequences = []
            new_sequences = []
            old_order_ids = self.order_ids.ids
            new_ids = values['order_ids'][0][2]
            removed_ids = [order_id for order_id in self.order_ids.ids if order_id not in new_ids]
            orders = self.env['rb_delivery.order'].sudo().browse(removed_ids).mapped('sequence')
            if len(orders) > 0:
                self.message_post(body=_("Orders %s were removed from returned collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))
            added_ids = [order_id for order_id in new_ids if order_id not in self.order_ids.ids]
            orders = self.env['rb_delivery.order'].sudo().browse(added_ids).mapped('sequence')
            if len(orders) > 0:
                self.message_post(body=_("Orders %s were added to returned collection %s by %s") % (', '.join(orders), self.sequence, self.env.user.name))
            if len(all_current_orders_in_collection)==0:
                self.active = False
                for order in self.order_ids:
                    old_sequences.append(order.sequence)
                    order.write({'returned_collection_id':False})
            else:
                values_to_be_reflected = self.env['rb_delivery.utility'].reflect_changes_to_collections(all_current_orders_in_collection,'returned_money_collection')
                if values_to_be_reflected:
                    values.update(values_to_be_reflected)
                for order in self.order_ids:
                    new_sequences.append(order.sequence)
                    old_sequences.append(order.sequence)
                    if order.id not in all_current_orders_in_collection:
                        order.write({'returned_collection_id':False})
                        index = new_sequences.index(order.sequence)
                        del new_sequences[index]
                values['order_count'] = len(all_current_orders_in_collection)
                for order in all_current_orders_in_collection:
                    order_rec = self.env['rb_delivery.order'].search([('id','=',order)])
                    if order not in old_order_ids:
                        order_rec.write({'returned_collection_id':self.id})
                        new_sequences.append(order_rec.sequence)

            user = self.env['res.users'].sudo().search([('id','=',self._uid)])
            self.message_post(body=_("Orders were changed from %s to %s by %s") % (old_sequences,new_sequences,user.name))
        if orders_vals and self.order_ids:
            data = {'uid':self._uid,'message':_("Orders values updated from update returned money collection."),'records':self.order_ids,'values':orders_vals,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)
        return super(rb_delivery_returned_money_collection, self).write(values)

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('rb_delivery.view_form_rb_delivery_order_select_returned_collection_state').id
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_returned_collection_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    def create_multi_collection(self, docs):
        business_groups = {}
        for doc in docs:
            business_id = doc.assign_to_business.id if doc.assign_to_business else None
            if business_id is None:
                continue
            key = business_id
            if doc.sudo().assign_to_business.user_parent_id and doc.sudo().assign_to_business.user_parent_id.collection_in_main_user_name:
                key = doc.sudo().assign_to_business.user_parent_id.id
            if key not in business_groups :
                business_groups[key] = []
            business_groups[key].append(doc)

        new_docs = list(business_groups.values())
        return new_docs

    # inherit module[olivery_branch_collection]
    @api.model
    def create(self, values):
        if 'order_ids' in values and values['order_ids']:
            active_ids = values['order_ids']
            del values['order_ids']
        else:
            active_ids = self.env['rb_delivery.order'].browse(
            self._context.get('active_ids'))

        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',action_name='returned_collection',state_name=values['state'])

        existing_records = []
        for order in active_ids:
            if order.returned_collection_id:
                existing_records.append(_("This order %s exists in returned money collection of sequence %s ") %(order.sequence,order.returned_collection_id.sequence))

        exist_message = ''
        if len(existing_records) > 0:
            for rec in existing_records:
                exist_message = exist_message + "\n" + rec
            self.env['rb_delivery.error_log'].raise_olivery_error(284,self.id,{'message': exist_message})
            #raise ValidationError(_(exist_message))

        orders_business_lists = self.create_multi_collection(active_ids)
        if 'name' in values and values['name']:
            title = values['name']
        else:
            title = ''
        branch_id = ''
        if values.get('branch_id'):
            branch_id = values.get('branch_id')
        returned_states_ids = self.env['rb_delivery.client_configuration'].get_param('returned_collection_status')
        for orders_list in orders_business_lists:
            values = {}
            if branch_id:
                values['branch_id'] = branch_id
            name_on_collection = orders_list[0].assign_to_business
            if orders_list[0].sudo().assign_to_business.user_parent_id and orders_list[0].sudo().assign_to_business.user_parent_id.collection_in_main_user_name:
                name_on_collection = orders_list[0].sudo().assign_to_business.user_parent_id

            if not name_on_collection:
                self.env['rb_delivery.error_log'].raise_olivery_error(285,self.id,{'sequence': orders_list[0].sequence})
                #raise ValidationError(_('Please add a business to the order'))
            if name_on_collection:
                if name_on_collection.default_payment_type:
                    values['payment_type'] = name_on_collection.default_payment_type.id
                    values['payment_detail'] = name_on_collection.default_payment_detail
                else:
                    payment_type = self.env['rb_delivery.payment_type'].search([('default','=',True)])
                    if len(payment_type) != 0:
                        values['payment_type'] = payment_type[0].id if payment_type else None
            if title != '':
                values['name'] = title + '_' + name_on_collection.username
            else:
                values['name'] = name_on_collection.username
            print(values)
            ids = []

            total_cost = 0.0
            total_delivery_cost = 0.0
            total_ammount = 0.0
            total_money_collection_cost = 0.0
            returned_states = []
            message = ''
            if not returned_states_ids:
                self.env['rb_delivery.error_log'].raise_olivery_error(286,self.id,{})
                #raise ValidationError('Please contact your adminstrator to add states for returned collection')
            for state_id in returned_states_ids:
                state = self.env['rb_delivery.status'].search([('id','=',state_id)])
                returned_states.append(state.name)
                message = message +' ' +state.title +'/'
            for order in orders_list:
                if order.state and order.state in returned_states:
                    total_cost = total_cost + order.required_from_business
                    total_delivery_cost = total_delivery_cost + order.delivery_cost
                    if order.inclusive_delivery:
                        total_ammount = total_ammount + order.copy_total_cost
                    else:
                        total_ammount = total_ammount + order.cost + order.delivery_cost
                    total_money_collection_cost = total_money_collection_cost + order.money_collection_cost

                    ids.append(order.id)
                else:
                    self.env['rb_delivery.error_log'].raise_olivery_error(287,self.id,{'order_sequence': order.sequence, 'message': message})
                    #raise ValidationError(_('To create returned collection, order status must be in ') + message)

            values['order_ids'] = [(6,0,ids)]
            values['order_count'] = len(ids)
            values['total_cost'] = total_cost
            values['total_money_collection_cost'] = total_money_collection_cost
            values['total_delivery_cost'] = total_delivery_cost
            values['total_ammount'] = total_ammount
            values['business_id'] = name_on_collection.id

            for order in orders_list:
                status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','returned_collection'),('status_type','=','olivery_collection')],limit=1)
                if status and status.default_related_order and status.related_order_status:
                    order_status = self.env['rb_delivery.status'].search([('name','=',status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
                    if order_status and order_status.name and order_status.name != order.state:
                        data = {'uid':self._uid,'message':_("Orders status updated from create returned money collection."),'records':order,'values':{'state':order_status.name},'update':True}
                        self.env['rb_delivery.utility'].olivery_sudo(data)

            new_sequence = self.env['ir.sequence'].next_by_code('rb_delivery.returned_money_collection')
            values['sequence'] = new_sequence
            order_report = super(rb_delivery_returned_money_collection, self).create(values)
            for order in order_report.order_ids:
                order.write({'returned_collection_id':order_report.id})
            if order_report.report_type == 'branch':
                order_report.to_branch= orders_list[0].to_branch.id
                order_report.final_cost= total_ammount

        message = _('Returned collection generated')
        self.env['rb_delivery.utility'].send_toast('for_user', ['returned_collection_generated',message] , str(self._uid))
        return order_report

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].search([('id', '=', self._uid)])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            self.env['rb_delivery.error_log'].raise_olivery_error(293,self.id,{'group': user_group, 'status': status,'collection_type':_('Returned money collection')})
            #raise Warning(_("You are not allowed to change to this state"))

        return

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].search([('id', '=', self._uid)]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                self.env['rb_delivery.error_log'].raise_olivery_error(292,self.id,{'first': current_status_record.title, 'second': next_status_record.title,'collection_type':_('Returned money collection')})
                #raise Warning(_("You are not allowed to change from this status {} to this status {}").format(current_status_record.title,next_status_record.title))

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            import barcode
            from barcode.writer import ImageWriter
            import io
            import base64
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})

    @api.multi
    def change_returned_money_collection_state(self,state_name):
        collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',state_name),('collection_type','=','returned_collection'),('status_type','=','olivery_collection')])
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',collection_state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])

        self.write({'state': collection_state.name})
        for record in self.order_ids:
                order = self.env['rb_delivery.order'].search([('id','=',record.id)])
                order.write({'state': order_state.name,'is_from_collection':True})
        return True


    @api.model
    def print_multi_orders_returned_money_collection_report(self,collection_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delevery_returned_money_collection_action').sudo().render_qweb_pdf(collection_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    def do_action(self,next_state):
        status_actions=self.env['rb_delivery.status'].search([('name','=',next_state),('status_type','=','olivery_collection'),('collection_type','=','returned_collection')]).status_action_ids
        for action in status_actions:
            try:
                method_to_call=getattr(rb_delivery_returned_money_collection,action.name)
                method_to_call(self,next_state)
            except Exception as e:
                self.message_post("Function name is do action " + str(e))
    # Post actions methods
    def detach_agent_action(self,next_state):
        if self.sudo().driver_id:
            self.sudo().driver_id = ""

    @api.model
    def group_by_get_business(self,domain):
        users = []
        collections = self.env['rb_delivery.returned_money_collection'].search(domain)
        for collection in collections:
                if {"id":collection.business_id.id ,"name":collection.business_id.commercial_name} not in users :
                        users.append({"id":collection.business_id.id ,"name":collection.business_id.commercial_name})
        return users

    @api.model
    def group_by_get_driver(self,domain):
        users = []
        collections = self.env['rb_delivery.returned_money_collection'].search(domain)
        for collection in collections:
                if {"id":collection.driver_id.id ,"name":collection.driver_id.username} not in users :
                        users.append({"id":collection.driver_id.id ,"name":collection.driver_id.username})
        return users

    @api.model
    def returned_money_collection_count(self,domain):
        count = 0
        if domain:
            count = self.env['rb_delivery.returned_money_collection'].search_count(domain)
            return count
        else:
            count = self.env['rb_delivery.returned_money_collection'].search_count([])
            return count


    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image

    @api.multi
    def name_get(self):
        result = []
        for returned_money_collection in self:
            name = ''
            if returned_money_collection.name:
                name = returned_money_collection.name
            name = name + ' ' + returned_money_collection.sequence
            result.append((returned_money_collection.id, name))
        return result