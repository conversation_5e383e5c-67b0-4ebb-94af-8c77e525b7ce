<?xml version="1.0"?>

<odoo>

	<data>
		<record id="paperformat_returned_money_collection" model="report.paperformat">
			<field name="name">returned_money_collection</field>
			<field name="default" eval="True"/>
			<field name="format">A4</field>
			<field name="orientation">Portrait</field>
			<field name="margin_top">40</field>
			<field name="margin_bottom">12</field>
			<field name="margin_left">7</field>
			<field name="margin_right">7</field>
			<field name="header_line" eval="False"/>
			<field name="header_spacing">40</field>
			<field name="dpi">90</field>
		</record>

        <report id="report_rb_delevery_returned_money_collection_action"
            model="rb_delivery.returned_money_collection"
            groups="base.group_system"
            string="Returned Collection"
            report_type="qweb-pdf"
            name="rb_delivery.returned_money_collection"
            paperformat="paperformat_returned_money_collection"
			/>

		<template id="minimal_layout_inherit" inherit_id="web.minimal_layout">
			<xpath expr="//head" position="inside">
				<link rel='stylesheet' href="/rb_delivery/static/src/css/report.css"/>
			</xpath>
		</template>


		<template id="returned_money_collection">
			<style type="text/css">

				body {

				color: #000 !important;

				}

				.background {

				background-color: blue;

				}

				.table_header_data{

				font-size:12px !important;

				}

				.table_data{

				font-size:13px !important;

				}

				.p {

				font-size: 22px;

				color: green;

				}
				table{
					th{
						border :1px solid black;
					}
				}


			</style>
			 <t t-call="web.basic_layout">
			<t t-foreach="docs" t-as="doc">



			        <t t-if="not o" t-set="o" t-value="doc"/>

                    <t t-if="not company">
                    <!-- Multicompany -->
                        <t t-if="company_id">
                            <t t-set="company" t-value="company_id"/>
                        </t>
                        <t t-elif="o and 'company_id' in o">
                            <t t-set="company" t-value="o.company_id.sudo()"/>
                        </t>
                        <t t-else="else">
                            <t t-set="company" t-value="res_company"/>
                        </t>
                    </t>


				    <div class="header" style="font-size:13px">
						<br/>
						<div class="row">

							<div class="col-4">
								<div class="col-12"> Business name : <t t-if="doc.sudo().business_id.commercial_name" >
										<span t-field="doc.sudo().business_id.commercial_name"/>
										</t>
										<t t-else="else">
										<span t-field="doc.sudo().business_id"/>
										</t></div>
						        <div class="col-12">Business mobile number:  <span t-field="doc.mobile_number"/></div>
								<div class="col-12">Date: <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/></div>

								<div class="col-12">Address  :  <span t-field="doc.area_id"/> - <span t-field="doc.address"/></div>
								<div class="col-12" t-if="company.company_registry">Company Registry:  <span t-field="company.company_registry"/></div>
							</div>
							<div class="col-4 text-center">
								<p class="text-center">Returned Collection Report</p>
								<t t-if="request.env['rb_delivery.client_configuration'].sudo().get_param('use_qr_code')">
										<img style="width:70px;height:70px;display:flex; justify-content: center;align-items: center;margin:0 auto" t-attf-src="data:image/*;base64,{{doc.qr_code_image}}" />
									</t>
									<t t-else="">
										<img t-attf-src="data:image/*;base64,{{doc.barcode}}" style="width:200px;height:50px" />
									</t>
									<h5 class="text-center" style="font-size:18px ;font-weight:bold">
										<t>
											<span t-field="doc.sequence" />
										</t>
									</h5>	
							</div>

							<div class="col-4 text-center">
							<t t-if="company.returned_money_logo">
								<img t-if="company.returned_money_logo" t-att-src="image_data_uri(company.returned_money_logo)" alt="Logo" style="max-height: 100px;"/>
							</t>
							<t t-else="else">
								<img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo" style="max-height: 100px;"/>
							</t>

							</div>

						</div>

				    </div>

				    <div class="footer">
						<div class="row text-center" style="border-top: 1px solid black;">
							<div class="col-3">
								<ul class="list-inline mt-2">
									<li class="list-inline-item">signature: </li>
								</ul>
							</div>
							<div class="col-9">
								<ul class="list-inline mt-2">
									<li class="list-inline-item">Page:</li>
									<li class="list-inline-item">
										<span class="page"></span>
									</li>
									<li class="list-inline-item">/</li>
									<li class="list-inline-item">
										<span class="topage"></span>
									</li>
								</ul>
							</div>
						</div>
				    </div>

   				    <div class="page">

					    <div class="image_bg"></div>
					    <div class="oe_structure"/>

                        <t t-set="total" t-value="0"/>

					    <table class="table table-condensed" style="font-size:0.85em;width:100%;border:1px solid black">
						    <thead>
							    <tr>
							        <th style="border:1px solid black">#</th>
									<th style="border:1px solid black">Sequence number</th>
								    <th style="border:1px solid black">Recipient's name</th>
								    <th style="border:1px solid black">Recipient's address</th>
									<th style="border:1px solid black">Returned Notes</th>
								    <th style="border:1px solid black">Money collection cost</th>
							     	<th style="border:1px solid black" t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">Delivery cost</th>
								    <th style="border:1px solid black">Total cost</th>
							    </tr>
						    </thead>
						    <tbody class="sale_tbody">
							    <t t-set="counter" t-value="0"/>
							    <t t-set="order_ids_sorted_area" t-value="docs.sudo().order_ids.sorted(key=lambda a:(a.customer_area.name if a.customer_area else '', a.customer_sub_area.name if a.customer_sub_area else '', a.customer_address if a.customer_address else ''))"/>
								<t t-foreach="order_ids_sorted_area" t-as="order">
								    <t  t-set="counter" t-value="counter + 1"/>
								    <tr style="page-break-inside: avoid;">
									    <td style="padding: 0 ; margin: 0 ;">
									        <t t-esc="counter"/>
								      	</td>
								    	<td style="padding: 0 ; margin: 0 ;">
									    	<span t-field="order.sudo().sequence"/>
											<span t-field="order.sudo().reference_id"/>
									    </td>	
								    	<td style="padding: 0 ; margin: 0 ;">
									    	<span t-field="order.sudo().customer_name"/>
											<br/>
											<span t-field="order.sudo().customer_mobile" />        
								     	</td>
								    	<td style="padding: 0 ; margin: 0 ;">
									    	<span t-field="order.sudo().customer_area"/> <span t-if="order.sudo().customer_area and order.sudo().customer_sub_area ">-</span> <span t-field="order.sudo().customer_sub_area"/> <span t-if="order.sudo().customer_address"> -</span> <span t-field="order.sudo().customer_address"/>
									    </td>
										<td style="padding: 0; margin: 0;">
											<span t-field="order.sudo().note" t-if="order.sudo().note"/>
											<span t-if="order.sudo().note and (order.sudo().reject_reason or order.sudo().stuck_comment)">, </span>
										
											<span t-field="order.sudo().reject_reason" t-if="order.sudo().reject_reason"/>
											<span t-if="order.sudo().reject_reason and order.sudo().stuck_comment">, </span>
										
											<span t-field="order.sudo().stuck_comment" t-if="order.sudo().stuck_comment"/>
										</td>
										
								    	<td style="padding: 0 ; margin: 0 ;">
									    	<span t-field="order.sudo().money_collection_cost"/>
									    </td>
								    	<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')" style="padding: 0 ; margin: 0 ;">
									    	<span t-field="order.sudo().delivery_cost"/>
								     	</td>
							    		<td style="padding: 0 ; margin: 0 ;">
								     		<span t-field="order.sudo().required_from_business"/>
							    		</td>.
							    	</tr>

								    <tr t-if="len(doc.sudo().order_ids) == counter" style="font-weight:bold;page-break-inside: avoid;">
								        <td>Total</td>
								        <td>	</td>
										<td>	</td>
							            <td>	</td>
										<td>	</td>
        							    <td>
											<t t-esc="'%.2f' % (money_collection_total if money_collection_total is not None else 0)"></t>
										</td>
										<td t-if="not user.has_group('rb_delivery.role_olivery_block_delivery_fee')">
											<t t-esc="'%.2f' % (delivery_cost_total if delivery_cost_total is not None else 0)"></t>
										</td>
										<td>
											<t t-esc="'%.2f' % (required_from_business_total if required_from_business_total is not None else 0)"></t>
										</td>																				
								    </tr>

							    </t>
						    </tbody>
					    </table>

					<div class="oe_structure"/>
				</div>
			</t>

    	</t>
	</template>

	</data>
</odoo>