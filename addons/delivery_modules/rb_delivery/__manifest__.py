# -*- coding: utf-8 -*-
{
    'name': "rb_delivery",

    'summary': """
        Olivery - One Platform for all logistics needs""",

    'description': """
    Olivery Core
    """,

    'author': "Olivery",
    'website': "http://www.olivery.app",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/10.0/odoo/addons/base/module/module_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': '7.7.248-pdf-fix',
    # any module necessary for this one to work correctly
    'depends': ['base', 'bus', 'board', 'mail', 'website', 'web_groupby_expand', 'queue_job', 'rowno_in_tree', 'muk_web_views_list_dynamic', 'rb_remove_odoo', 'editable_groupby_listview', 'sw_multi_search','jwt_provider','multi_step_wizard'],

    'css': [
        'static/src/css/report.css',
        'static/src/css/navbar.css',
    ],
    # always loaded
    'data': [
        # 'views/views.xml',
        'views/templates.xml',
        'security/rb_delivery_security.xml',
        'security/ir.model.access.csv',
        'models/order/agent_report.xml',
        # [Group] Money Collection
        'models/order/multi_order_money_report.xml',
        'models/order/order_money_report.xml',
        'models/recomputation_job/recomputation_job_view.xml',
        'models/order/order_money_report_default.xml',
        'models/order/partner_money_collection.xml',
        'models/order/collection_partner_money_collection.xml',
        'models/order/returned_order_money_report.xml',
        'models/order/returned_order_money_per_business_report.xml',
        'models/status_related_field/status_related_field_view.xml',
        'views/web_login_view.xml',
        'views/bad_request_view.xml',
        'views/olivery_terms_and_conditions.xml',
        'views/web_client.xml',
        'data/order_sequence.xml',
        'data/params.xml',
        'data/archive_completed_orders.xml',
        'models/runsheet/runsheet_view.xml',
        'models/returned_money_collection/returned_money_collection_report.xml',
        'models/returned_money_collection/returned_money_collection_view.xml',
        'models/returned_money_collection/return_declaration.xml',
        'models/status/status_view.xml',
        'models/status_field_security/status_field_security_view.xml',
        'models/status_action/status_action_view.xml',
        'models/status_pre_action/status_pre_action_view.xml',
        'models/order/order_view.xml',
        'models/billing_setting/billing_setting_view.xml',
        'models/billing/billing_view.xml',
        'models/billing/billing_report.xml',
        'models/order_live/order_live_view.xml',
        'models/order/order_report_view.xml',
        'models/order/money_collection_receipt_A5.xml',
        'models/order/money_collection_receipt_A4.xml',
        'models/order/money_collection_receipt_A6.xml',
        'models/order/money_collection_with_services.xml',
        'models/order/money_collection_receipt_declaration.xml',
        'models/order/multi_order_money_report_bank_detail.xml',
        'models/otp_status_checker/otp_status_checker_view.xml',
        # [Group] Operation Print
        'models/order/order_detail_report.xml',
        'models/order/order_detail_report_A4.xml',
        'models/order/thermal_report.xml',
        'models/order/order_detail_report_returned.xml',
        'models/runsheet/runsheet_report.xml',
        'models/runsheet/runsheet_report_without_barcode.xml',
        'models/order/agent_money_collection_report.xml',
        'models/order/agent_returned_collection_report.xml',
        'models/agent_money_collection/agent_company_collection_report.xml',
        'models/agent_money_collection/agent_money_collection_report.xml',
        'models/agent_money_collection/agent_profit_report.xml',
        'models/agent_money_collection/agent_report_with_services.xml',
        'models/agent_returned_collection/agent_returned_collection_report.xml',
        'models/agent_returned_collection/agent_returned_collection_view.xml',
        'models/agent_money_collection/agent_money_collection_view.xml',
        'models/order_type/order_type_view.xml',
        'models/user/user_view.xml',
        'models/notification_center/notification_center_view.xml',
        'models/notification_type/notification_type_view.xml',
        'models/pricelist/pricelist_view.xml',
        'models/pricelist_item/pricelist_item_view.xml',
        'models/area/area_view.xml',
        'models/area/area_wizard_view.xml',
        'models/area_map/area_map_view.xml',
        'models/sub_area_map/sub_area_map_view.xml',
        'models/company/company_view.xml',
        'models/currency/currency_view.xml',
        'models/mail_tracking_value/mail_tracking_value_view.xml',
        'models/one_signal/one_signal_view.xml',
        'models/action/action_view.xml',
        'models/sms/sms_view.xml',
        'models/sms_item/sms_item_view.xml',
        'models/general_action/general_action_view.xml',
        'models/location/location_view.xml',
        'models/change_password/change_password_view.xml',
        'models/reject_reason/reject_reason_view.xml',
        'models/payment_type/payment_type_view.xml',
        'models/client_configuration_type/client_configuration_type_view.xml',
        'models/client_configuration/client_configuration_view.xml',
        'models/description_tags/description_tags_view.xml',
        'models/order_barcode/order_barcode_view.xml',
        'models/signature/signature_view.xml',
        'models/task/task_view.xml',
        'models/task_type/task_type_view.xml',
        'models/development_status/development_status_view.xml',
        'models/customer_status/customer_status_view.xml',
        'models/whatsapp_message/whatsapp_message_view.xml',
        'models/address_tags/address_tags_view.xml',
        'models/order_logs/order_logs_view.xml',
        'models/service/service_view.xml',
        'models/menu_access/menu_access_view.xml',
        'models/action_access/action_access_view.xml',
        'models/report_access/report_access_view.xml',
        'models/security_matrix/security_matrix_view.xml',
        'models/scan_logs/scan_logs_view.xml',
        #'views/operations/agent_operations_view.xml',
        # 'views/operations/agent_operations_view.xml',
        'views/order_tracking_view.xml',
        'views/olivery_signup_view.xml',
        'views/forgot_password_view.xml',
        'views/olivery_order_location_view.xml',
        'views/driver_live_tracking.xml',
        'models/follow_up_order/follow_up_order_view.xml',
        'views/module_view.xml',
        'models/order/sender_partner_collection.xml',
        'models/sub_area/sub_area_view.xml',
        'models/chat_notification/chat_notification_view.xml',
        'models/icons/icons_model_view.xml',
        'models/order/money_collection_with_receipt_A4.xml',
        'models/order/money_collection_total_receipt.xml',
        'models/filter/ir_filters_view.xml',
        'models/country/country_view.xml',
        'models/order/branch_collection_report.xml',
        'models/quick_order/quick_order_view.xml',
        'models/general_configuration/general_configuration_view.xml',
        'models/order/collection_summary_report_A4.xml',
        'models/returned_money_collection/returned_collection_non_financial_report.xml',
        'views/collection_partner_money_collection.xml',

        'models/order/order_non_financial_report.xml',
        'models/order_draft/order_draft_view.xml',
        'models/order_draft/order_draft_wizard_view.xml',
        'models/order/money_collection_invoice_receipt.xml',
        'models/order/business_driver_collection.xml',
        'models/placeholder/placeholder_view.xml',
        'models/redirect_group/redirect_group_view.xml',
        'models/notification_sound/notification_sound_view.xml',
        'models/district/district_view.xml',
        'models/auto_filled_fields/auto_filled_fields_view.xml',
        'models/mobile_form_creator/mobile_form_creator_view.xml',
        'models/mobile_form_input/mobile_form_input_view.xml',
        'models/mobile_card_creator/mobile_card_creator_view.xml',
        'models/mobile_card_item/mobile_card_item_view.xml',
        'models/mobile_item_actions/mobile_item_actions_view.xml',
        'models/order/order_financial_report.xml',
        'models/order/collection_financial_report.xml',
        'data/clear_session_storage.xml',
        'data/reset_expired_tracking_ids.xml',
        'models/mobile_action_creator/mobile_action_creator_view.xml',
        'models/mobile_action_item/mobile_action_item_view.xml',
        'models/mobile_default_search/mobile_default_search_view.xml',
        'models/mobile_default_print/mobile_default_print_view.xml',
        'models/mobile_filter_fields/mobile_filter_fields_view.xml',
        'models/mobile_card_item/mobile_card_functions_view.xml',
        'models/business_work_category/business_work_category_view.xml',
        'views/error_template.xml',
        'models/mobile_setting_item/mobile_setting_item_view.xml',
        'models/mobile_setting_creator/mobile_setting_creator_view.xml',
        'models/mobile_compute_fields_function/mobile_compute_fields_function_view.xml',
        'models/agent_money_collection/agent_collection_profit_report.xml',
        'models/returned_money_collection/retured_collection_with_reason.xml',
        'models/order/order_runsheet_without_barcode.xml',
        'models/mapping_relation_fields/mapping_relation_fields_view.xml',
        'models/onboarding_error/onboarding_error_view.xml',
        'models/mobile_models_filter/mobile_models_filter_view.xml',
        'models/mobile_nav_creator/mobile_nav_creator_view.xml',
        'models/create_order_button_actions/create_order_button_action_model.xml',
        'models/create_order_button_creator/create_order_button_creator_view.xml',
        'models/mobile_collection_item/mobile_collection_item_view.xml',
        'models/mobile_collection_sheet_creator/mobile_collection_sheet_creator_view.xml',
        'models/error_log/error_log_view.xml',
        'models/migration/migration_view.xml',
        'views/new_html.xml',
        'models/control_fields/control_fields_view.xml',
        'models/order_attachment/order_attachment_view.xml',
        'models/quick_access_buttons/quick_access_buttons_view.xml',
        'models/delayed_orders_timer_monitor/delayed_orders_timer_monitor_view.xml',
        'data/delayed_orders_timer_monitor.xml',
        'models/delayed_orders_logs/delayed_orders_view.xml',
        'models/order/public_link_view.xml',
        'models/driver_order_location/driver_order_location_view.xml',
        'models/order/order_detail_report_a5_duplicated.xml',
        'views/print/envelope_report.xml',
        'models/mobile_scan_logs/mobile_scan_logs_view.xml',
        'views/print/returned_collection_envelope_report.xml',
        'models/order/money_collection_report.xml',
        'models/zone/zone_view.xml',
        'views/money_collection_with_note.xml',
        'models/returned_money_collection/returned_money_collection_report_with_barcode.xml',
        'models/quick_order_table_items/table_items_view.xml',
        'models/mobile_sort_and_distribute/mobile_sort_and_distribute_view.xml',
        'views/print/runheet_by_driver.xml',
        'models/order_archive/order_archive_view.xml',
        'models/group_by_card_configurations/group_by_card_configurations_view.xml',
        'models/announcement/announcement_views.xml',
        'models/mobile_order_detail/mobile_order_detail_view.xml',
        'models/mobile_order_detail_button/mobile_order_detail_button_view.xml',
        'models/mobile_order_detail_card/mobile_order_detail_card_view.xml',
        'models/mobile_order_detail_card_item/mobile_order_detail_card_item_view.xml',
        # ==================================================== THIS SECTION FOR DEMO FILES ONLY ====================================================
        'demo/mobile_models_filter.xml',
        'demo/mobile_collection_items.xml',
        'demo/status.xml',
        'demo/area.xml',
        'demo/order_type.xml',
        'demo/security_matrix.xml',
        'demo/client_conf.xml',
        'demo/error_log_demo.xml',
        'demo/status.xml',
        'demo/payment_type.xml',
        'demo/notification_type.xml',
        'demo/general_configuration.xml',
        'demo/mobile_action_item.xml',
        'demo/business_work_category.xml',
        'demo/mobile_setting_item.xml',
        'demo/mobile_compute_fields.xml',
        'demo/create_order_button_actions.xml',
        'demo/control_fields.xml',
        'demo/quick_access_buttons.xml',
        'demo/icons_demo.xml',
        'models/order_archive/order_archive_view.xml',
        'models/recomputation_job/recomputation_job_view.xml',
        'models/order_action/order_action_view.xml',
        'models/zone_assignment/zone_assignment_view.xml',
        'views/remove_follower.xml'

    ],
    'qweb': [
        'static/src/xml/dynamic_quick_order_template.xml',
        'static/src/xml/*.xml',
    ],

    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'post_init_hook': 'post_init_hook',

}
