# -*- coding: utf-8 -*-
import json
import werkzeug
from odoo import http,_
from odoo.http import request, Response
from odoo.addons.auth_signup.models.res_users import SignupError
from odoo.exceptions import UserError
from odoo.api import call_kw
from ..constants import message_keys
from ..validator import validator
from ..jwt_http import jwt_http
import requests
import datetime

import logging
_logger = logging.getLogger(__name__)


class JwtController(http.Controller):


    @http.route('/api/get_records',type='json', auth='public', csrf=False,save_session=False)
    def get_records(self, **kw):
        result = self.jwt_middel_ware()
        if result['valid']:
            request_body = result['request_body']
            model = request_body['model']
            domain = request_body['domain'] if 'domain' in request_body else []
            fields = request_body['fields'] if 'fields' in request_body else []
            offset = request_body['offset'] if 'offset' in request_body else 0
            limit = request_body['limit'] if 'limit' in request_body else 0
            sort = request_body['sort'] if 'sort' in request_body else False
            record = request.env[model].search_read(domain,fields,offset,limit,sort)
            return self.get_jwt_resposne(True,200,record,message_keys.SUCCESS_GETTING_RECORD)
                
                
        else:
            return result['error']
        
    @http.route('/api/resequence', type='json', auth='public', csrf=False,save_session=False)
    def resequence(self, **kw):
        result = self.jwt_middel_ware()
        if result['valid']:
            request_body = result['request_body']
            model = request_body['model']
            ids = request_body['ids']
            field = request_body['field'] if 'field' in request_body else []
            offset = request_body['offset'] if 'offset' in request_body else 0
            m = request.env[model]
            if not m.fields_get([field]):
                return False
            for i, record in enumerate(m.browse(ids)):
                message = _("Order has been resequenced by %s using mobile app")%(request.env.user.name)
                updated_vals ={field: i + offset}
                data = {'uid':request._uid,'message':message,'records':record,'values':updated_vals,'update':True}
                request.env['rb_delivery.utility'].olivery_sudo(data)
            return True
        else:
            return result['error']
        
    @http.route('/api/create_record',type='json', auth='public', csrf=False,save_session=False)
    def create_record(self, **kw):
        
        result = self.jwt_middel_ware()
        if result['valid']:
            request_body = result['request_body']
            model = request_body['model']
            values = request_body['values'] if 'values' in request_body else {}
            record = request.env[model].create(values)
            return self.get_jwt_resposne(True,200,record.read(),message_keys.SUCCESS_CREATING_RECORD)

        else:
            return result['error']
    
    @http.route('/api/update_records',type='json', auth='public', csrf=False,save_session=False)
    def update_records(self, **kw):
        result = self.jwt_middel_ware()
        if result['valid']:
            request_body = result['request_body']
            model = request_body['model']
            if 'ids' in request_body:
                ids = request_body['ids']
            else:
                return jwt_http.errcode(code=400, message=message_keys.MUST_SPECIFY_IDS)
            values = request_body['values'] if 'values' in request_body else {}
            records = request.env[model].search([['id','in',ids]])
            if records and len(records) > 0:
                records.write(values)
                return self.get_jwt_resposne(True,200,False,message_keys.SUCCESS_EDITING_RECORD)
                
            else:
                return self.get_jwt_resposne(False,400,False,message_keys.RECORD_DOES_NOT_EXIST)
                    
                
        else:
            return result['error']
    
    @http.route('/api/call_method',type='json', auth='public', csrf=False,save_session=False)
    def call_method(self, **kw):
        result = self.jwt_middel_ware()
        if result['valid']:
            request_body = result['request_body']
            model = request_body['model']
            if 'method' in request_body:
                method = request_body['method']
            else:
                return jwt_http.errcode(code=400, message=message_keys.MUST_SPECIFY_METHOD)
            
            args = request_body['args'] if 'args' in request_body else {}
            kwargs = request_body['kwargs'] if 'kwargs' in request_body else {}
            record = call_kw(request.env[model], method, args, kwargs)
            return self.get_jwt_resposne(True,200,record,message_keys.SUCCESS_CALLING_METHOD)
                
                
        else:
            return result['error']
        
    @http.route('/api/call_public_method',type='json', auth='public', csrf=False,save_session=False)
    def call_public_method(self, **kw):
        request_body = kw
       
        if 'model' in request_body:
            model = request_body['model']
        else:
            return jwt_http.errcode(code=400, message=message_keys.MUST_SEPICFY_MODEL)
        if 'method' in request_body:
            method = request_body['method']
        else:
            return jwt_http.errcode(code=400, message=message_keys.MUST_SPECIFY_METHOD)
        
        params = request_body['args'] if 'args' in request_body else []
        
        method=getattr(type(request.env[model]), method)

        record = method(request,*params)
        return self.get_jwt_resposne(True,200,record,message_keys.SUCCESS_CALLING_METHOD)
            
        

    @http.route('/api/check_public_link_token', auth='none', type="json")
    def check_public_link_token(self, **rec):
        try:
            if 'token' in rec:
                res = request.env['rb_delivery.public_link_tokens'].sudo().check_token_valid(rec['token'])
                args = {'code':200,'success': res['valid'], 'result': res}
            else:
                fail_message = _("Please Provide Token")
                jwt_http.response_403(fail_message)
        except Exception as e:
            fail_message = _("Invalid Token")
            jwt_http.response_403(fail_message)
        return args
    
    @http.route('/api/create_public_order', auth='none', type="json")
    def create_public_order(self, **rec):
        state = request.env['rb_delivery.status'].sudo().search([('name', '=', 'pending_business_approval')], limit=1)
        if state and state.active:
            rec['state'] = 'pending_business_approval'       
        new_order_id = request.env['rb_delivery.order'].sudo().create(rec)
        valid = request.env['rb_delivery.public_link_tokens'].sudo().use_token(rec['token'],new_order_id.id) if 'token' in rec else False
        if valid:
            args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'sequence':new_order_id.sequence,'delivery_cost':new_order_id.delivery_cost }
        else:
            fail_message = _("Invalid Token")
            jwt_http.response_403(fail_message)
        return args

    
    @http.route('/api/update_location_method',type='json', auth='public', csrf=False,save_session=False)
    def update_background_location_method(self, **req):
        _logger.info('*********************************')
        _logger.info(req)
        params = req
        if 'extras' in params and params['extras'] and 'token' in params['extras'] and params['extras']['token']:
            token = params['extras']['token']
            result = validator.verify_token(token)
            if not result['status']:
                return jwt_http.errcode(code=result['code'], message=result['message'])
            else:
                coords = req.get('coords')
                user_id = req.get('extras')['user_id']
                values={
                    'user_id':user_id,
                    'longitude':coords['longitude'],
                    'latitude':coords['latitude']
                    }
                
                if req.get('timestamp'):
                    values['time_stamp']=req.get('timestamp')
                
                try:
                    location = request.env['rb_delivery.location'].create(values)
                    return self.get_jwt_resposne(True,200,location.read(),message_keys.SUCCESS_CREATING_RECORD)

                except Exception as e:
                    jwt_http.response_403(e)
        else:
            return jwt_http.errcode(code=400, message=message_keys.TOKEN_MUST_BE_INCLUDED_IN_PARAMS)
        
        

    @http.route('/api/login', type='json', auth='public', csrf=False, methods=['POST'],save_session=False)
    def login(self, email, password, **kw):
        return jwt_http.do_login(email, password)

            

    @http.route('/api/me', type='json', auth='public', csrf=False,save_session=False)
    def me(self, **kw):
        params = kw
        if 'token' in params and params['token']:
            token = params['token']
            _logger.info('********************************* TOKEN *********************************')
            _logger.info(token)
            _logger.info('********************************* TOKEN *********************************')
            _logger.info('********************************* PARAMS *********************************')
            _logger.info(params)
            _logger.info('********************************* PARAMS *********************************')
            result = validator.verify_token(token)
            if not result['status']:
                return jwt_http.errcode(code=result['code'], message=result['message'])
            else:
                response = jwt_http.response(request.env.user.to_dict(True))
                if response and response.status_code == 200:
                    user_info={}
                    nav_creator=request.env['rb_delivery.mobile_nav_creator'].sudo().search([['group_id','=',request.env.user.rb_user.group_id.id]])
            
                    user_info['redirect_to']='tabs/dashboard'
                    user_info['show_nav']=True

                    user_info['account_manager_mobile'] = request.env.user.rb_user.account_manager_mobile

                    if nav_creator:
                        user_info['redirect_to'] = nav_creator[0].redirect_to
                        user_info['show_nav']=nav_creator[0].show_nav
                        user_info['button_style']=nav_creator[0].button_style
                        if len(nav_creator[0].nav_items):
                            user_info['nav_items']= nav_creator[0].nav_items.read(['redirect_to','icon', 'redirect_type'])
                    return jwt_http.successcode(code=200,message=message_keys.AVAILABLE_TOKEN,user_info=user_info)
                else:
                    return jwt_http.errcode(code=301, message=message_keys.TOKEN_EXPIRED)
        else:
            return jwt_http.errcode(code=400, message=message_keys.TOKEN_MUST_BE_INCLUDED_IN_PARAMS)

    @http.route('/api/logout', type='json', auth='public', csrf=False,save_session=False)
    def logout(self, **kw):
        http_method, body, headers, token = jwt_http.parse_request()
        player_id = kw.get('player_id')
        if player_id:
            users = request.env['rb_delivery.user'].sudo().search([('id', '=', request.env.user._uid)])
            if users and users.player_id == player_id:
                users.write({'player_id': '','online':False})
        jwt_http.do_logout(token)
        return jwt_http.response()

    @http.route('/api/register', type='json', auth='public', csrf=False, methods=['POST'],save_session=False)
    def register(self, email=None, name=None, password=None, **kw):
        if not validator.is_valid_email(email):
            return jwt_http.errcode(code=400, message=message_keys.INVALID_EMAIL_ADDRESS)
        if not name:
            return jwt_http.errcode(code=400, message=message_keys.NAME_MUST_NOT_BE_EMPTY)
        if not password:
            return jwt_http.errcode(code=400, message=message_keys.PASSWORD_MUST_NOT_BE_EMPTY)

        # sign up
        try:
            self._signup_with_values(login=email, name=name, password=password)
        except AttributeError:
            return jwt_http.errcode(code=501, message=message_keys.SIGNUP_IS_DISABLE)
        except (SignupError, AssertionError) as e:
            if request.env["res.users"].sudo().search([("login", "=", email)]):
                return jwt_http.errcode(code=400, message=message_keys.EMAIL_ADDRESS_ALREADY_EXIST)
            else:
                _logger.error("%s", e)
                return jwt_http.response_500()
        except Exception as e:
            _logger.error(str(e))
            return jwt_http.response_500()
        # log the user in
        return jwt_http.do_login(email, password)

    def _signup_with_values(self, **values):
        request.env['res.users'].sudo().signup(values, None)
        request.env.cr.commit()     # as authenticate will use its own cursor we need to commit the current transaction
        self.signup_email(values)


    def signup_email(self, values):
        user_sudo = request.env['res.users'].sudo().search([('login', '=', values.get('login'))])
        template = request.env.ref('auth_signup.mail_template_user_signup_account_created', raise_if_not_found=False)
        if user_sudo and template:
            template.sudo().with_context(
                lang=user_sudo.lang,
                auth_login=werkzeug.url_encode({'auth_login': user_sudo.email}),
            ).send_mail(user_sudo.id, force_send=True)


    def jwt_middel_ware(self):

        http_method, body, headers, token = jwt_http.parse_request()
        token_status=validator.verify_token(token)
        if not token_status['status']:
            return {
                'valid' : False,
                'error' : jwt_http.errcode(code=token_status['code'], message=token_status['message'])
            }
        if 'model' in body:
            return {
                'valid' : True,
                'request_body' : body
            }
        else:
            return {
                'valid' : False,
                'error' : jwt_http.errcode(code=401, message=message_keys.MUST_SEPICFY_MODEL)
            }

    def get_jwt_resposne(self,success,code,result,message):
        jwt_response = {"success":success, "code":code,"result":result,"message":message}
        return jwt_response