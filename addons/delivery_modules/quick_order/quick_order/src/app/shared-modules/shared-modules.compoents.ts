import { NgModule } from '@angular/core';
import { InputItemComponent } from '../components/input-component/form-input.component';
import { AttachmentFormInputComponent } from '../components/attachment-form-input/attachment-form-input.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatStepperModule } from '@angular/material/stepper';
import { FormFooterComponent } from '../components/form-footer/form-footer.component';
import { OrdersTableComponent } from '../components/orders-table/orders-table.component';
import { MatTableModule } from '@angular/material/table';
import { ErrorDialogComponent } from '../components/error-dialog/error-dialog.component';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { EqualWidthDirective } from '../services/equal-width';
import { MatMenuModule } from '@angular/material/menu';
import { DynamicSelectionTemplateComponent } from '../components/dynamic-selection-template/dynamic-selection-template.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatMomentDatetimeModule } from '@mat-datetimepicker/moment';
import { MatDatetimepickerModule } from '@mat-datetimepicker/core';
import { MultiSelectionTemplateComponent } from '../components/multi-selection-template/multi-selection-template.component';
import { TableFormInputComponent } from '../components/table-form-input/table-form-input.component';
import { OliveryAutoCompleteComponent } from '../components/olivery-auto-complete/olivery-auto-complete.component';


@NgModule({
    exports: [
        InputItemComponent,
        FormFooterComponent,
        OrdersTableComponent,
        AttachmentFormInputComponent,
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatCheckboxModule,
        MatRadioModule,
        MatInputModule,
        MatToolbarModule,
        MatListModule,
        MatFormFieldModule,
        MatProgressSpinnerModule,
        MatDialogModule,
        MatSnackBarModule,
        MatChipsModule,
        MatDatepickerModule,
        MatSelectModule,
        CommonModule,
        TranslateModule,
        FormsModule,
        MatSlideToggleModule,
        MatStepperModule,
        MatTableModule,
        MatNativeDateModule,
        BrowserAnimationsModule,
        MatMenuModule,
        MatAutocompleteModule,
        MatMomentDatetimeModule,
        MatDatetimepickerModule,
        TableFormInputComponent,
        OliveryAutoCompleteComponent
    ],
    imports: [
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatCheckboxModule,
        MatRadioModule,
        MatInputModule,
        MatToolbarModule,
        MatListModule,
        MatFormFieldModule,
        MatProgressSpinnerModule,
        MatDialogModule,
        MatSnackBarModule,
        MatChipsModule,
        MatDatepickerModule,
        MatSelectModule,
        CommonModule,
        TranslateModule,
        FormsModule,
        MatSlideToggleModule,
        MatStepperModule,
        MatTableModule,
        MatNativeDateModule,
        BrowserAnimationsModule,
        MatMenuModule,
        MatAutocompleteModule,
        MatMomentDatetimeModule,
        MatDatetimepickerModule,
        OliveryAutoCompleteComponent
    ],
    declarations: [
        InputItemComponent,
        FormFooterComponent,
        OrdersTableComponent,
        ErrorDialogComponent,
        EqualWidthDirective,
        DynamicSelectionTemplateComponent,
        MultiSelectionTemplateComponent,
        TableFormInputComponent,
        AttachmentFormInputComponent
    ]

})
export class SharedComponentModule { }
