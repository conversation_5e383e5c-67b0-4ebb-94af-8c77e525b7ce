import { Component, EventEmitter, Input, Output, ViewChild, ElementRef, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-attachment-form-input',
  templateUrl: './attachment-form-input.component.html',
  styleUrls: ['./attachment-form-input.component.scss']
})
export class AttachmentFormInputComponent implements OnInit {
  @Input() field: any;
  @Input() input: any;
  @Input() formValues: any = {};
  @Output() selectedValue = new EventEmitter<any>();

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  selectedFiles: File[] = [];
  maxFileSize = 15;
  isDragOver = false;

  constructor(
    private translate: TranslateService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    if (this.input && this.input.value) {
      this.selectedFiles = this.input.value;
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const files = input.files;

    if (!files || files.length === 0) {
      return;
    }

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileSizeMB = file.size / 1024 / 1024;

      if (fileSizeMB > this.maxFileSize) {
        this.snackBar.open(
          this.translate.instant('FILE_TOO_LARGE', { size: this.maxFileSize }),
          this.translate.instant('CLOSE'),
          { duration: 5000 }
        );
        return;
      }
    }

    const fileArray: File[] = Array.from(files);

    if (this.field && (this.field.ttype === 'one2many' || this.field.ttype === 'many2many')) {
      this.selectedFiles = [...this.selectedFiles, ...fileArray];
    } else {
      this.selectedFiles = fileArray.slice(-1);
    }

    this.emitFiles();

  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.emitFiles();
  }

  clearFiles(): void {
    this.selectedFiles = [];

    if (this.fileInput && this.fileInput.nativeElement) {
      this.fileInput.nativeElement.value = '';

      const event = new Event('change', { bubbles: true });
      this.fileInput.nativeElement.dispatchEvent(event);
    }

    this.selectedValue.emit(undefined);

  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    if (!event.dataTransfer) {
      return;
    }

    const files = event.dataTransfer.files;
    if (!files || files.length === 0) {
      return;
    }

    // Check file sizes
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileSizeMB = file.size / 1024 / 1024;

      if (fileSizeMB > this.maxFileSize) {
        this.snackBar.open(
          this.translate.instant('FILE_TOO_LARGE', { size: this.maxFileSize }),
          this.translate.instant('CLOSE'),
          { duration: 5000 }
        );
        return;
      }
    }

    const fileArray: File[] = Array.from(files);

    if (this.field && (this.field.ttype === 'one2many' || this.field.ttype === 'many2many')) {
      this.selectedFiles = [...this.selectedFiles, ...fileArray];
    } else {
      this.selectedFiles = fileArray.slice(-1);
    }

    this.emitFiles();
  }

  private emitFiles(): void {
    if (this.selectedFiles.length === 0) {
      this.selectedValue.emit(undefined);
      return;
    }

    const value = this.selectedFiles

    this.selectedValue.emit(value);
  }
}