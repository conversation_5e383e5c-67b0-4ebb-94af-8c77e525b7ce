.attachment-form-input {
  width: 100%;

  .file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    border: 2px dashed #ccc;
    cursor: pointer;
    min-height: 150px;
    position: relative;

    &:hover {
      background-color: #f0f0f0;
      border-color: #fcb045;
    }

    &.drag-over {
      background-color: rgba(252, 176, 69, 0.1);
      border-color: #fcb045;
      box-shadow: 0 0 10px rgba(252, 176, 69, 0.3);
    }

    .drop-zone-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      height: 100%;

      .icon-container {
        margin-bottom: 16px;

        .drop-icon {
          font-size: 48px;
          height: 48px;
          width: 48px;
          color: #fcb045;
          transition: all 0.3s ease;
          animation: float 3s ease-in-out infinite;

          .drag-over & {
            animation: bounce 1s ease infinite;
            color: #EE8413;
          }
        }
      }

      .drop-text {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .drop-subtext {
        font-size: 14px;
        color: #666;
      }
    }

    .debug-info {
      margin-top: 16px;
      font-size: 14px;
      color: #666;
      text-align: center;
      background-color: rgba(252, 176, 69, 0.1);
      padding: 8px;
      border-radius: 4px;
    }

    .selected-files {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 16px;

      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        border-left: 4px solid #fcb045;
        animation: slideIn 0.3s ease-out forwards;

        &:hover {
          transform: translateX(5px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 12px;

          mat-icon {
            color: #fcb045;
          }

          .file-name {
            font-weight: 500;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .file-size {
            color: #666;
            font-size: 0.9em;
          }
        }

        button {
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(244, 67, 54, 0.1);
          }
        }
      }
    }
  }
}


@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-7px);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}