import { Component, OnInit, Input, ViewChild, ViewChildren, ElementRef, QueryList, ChangeDetectorRef,Renderer2, RendererStyleFlags2 } from '@angular/core';
import { OdooJsonRPC } from '../../services/odooJsonRPC';
import { MatStepper } from '@angular/material/stepper';
import { Subject, debounceTime, filter, take, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { TimeService } from '../../services/time-services';
import { Store } from '@ngrx/store';
import { Order } from '../orders-table/orders-table.component';
import { DialogService } from '../../services/error-handlar-service';
import { ObjectService } from '../../services/object-service';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as $ from 'jquery';
import { InputItemComponent } from '../input-component/form-input.component';
import { AttachmentFormInputComponent } from '../attachment-form-input/attachment-form-input.component';

@Component({
  selector: 'app-quick-order',
  templateUrl: './quick-order.component.html',
  styleUrls: ['./quick-order.component.scss']
})
export class QuickOrderComponent implements OnInit {

  title = 'quick_order';
  loading = false;
  formInputs: any;
  rightFormInputs: any[] = [];
  leftFormInputs: any[] = [];
  tableInputs: any[] = [];
  attachmentInputs: any[] = [];
  @Input() record!: any
  @ViewChild('firstInput') firstInput!: ElementRef;
  formValues: any = {}
  @Input() steps!: any[]
  @ViewChild('stepper') stepper!: MatStepper;
  @Input() destroyed!: Subject<any>;
  model!: string
  formFooterInputs: any[] = []
  private emitValuesSubject = new Subject<any>();
  insideFooterCount: number = 0
  orders: Order[] = []

  @ViewChildren(InputItemComponent) formInputComponents!: QueryList<InputItemComponent>;
  @ViewChildren(AttachmentFormInputComponent) attachmentComponents!: QueryList<AttachmentFormInputComponent>;

  sessionId: string = ''

  computedFields :any[]= []
  inputsToBeUpdated : any[]=[]

  updatedFormValues: any = {}
  defaultVals: any = {}

  private updateInputsSubject = new Subject<{ inputsToUpdate: any }>();

  constructor(
    private odooRpc: OdooJsonRPC,
    private translate: TranslateService,
    private timeService : TimeService,
    private dialogService: DialogService,
    private objectService: ObjectService,
    private el: ElementRef, 
    private renderer: Renderer2,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  )
   { }


  
   @ViewChild('formContainer', { static: false }) formContainer!: ElementRef;

  ngOnInit() {
    this.loading = true;
    this.model = 'rb_delivery.order'
    this.odooRpc.callRequest({
      args: ['quick_order_form', false, 'ar'],
      model: "rb_delivery.mobile_form_input",
      method: "get_form"
    }).then(formFields => {
      this.loading = false;
      if (formFields && formFields.body && formFields.body.result.result && formFields.body.result.result.success) {
        this.formInputs = formFields.body.result.result.result.inputs;
        this.filterFormInputs();
        this.getTableInputs();
        this.splitFormInputs();
        this.getFooterFields();
        this.getComputeFields()
        this.setUpDefaultValues()
        } else {
        this.dialogService.error(
          {
            title: this.translate.instant('Error while fetching form'),
            message: this.translate.instant('Error while fetching form'),
            whatToDo: this.translate.instant('Please referesh the page and try again'),
            code: 1444
          }
        )
      }
    });
    this.setupInputUpdateStream();
  }
  setUpDefaultValues() {
    for (let input of this.formInputs){
      if (input.value && input.field && input.field.name) {
        this.defaultVals[input.field.name] = input.value
      }
    }
  }
  getTableInputs() {
    if (this.formInputs && Array.isArray(this.formInputs)) {
      this.tableInputs = this.formInputs.filter(input => input.quick_order_show_as === 'table');
      this.attachmentInputs = this.formInputs.filter(input => input.quick_order_show_as === 'attachments');
      this.formInputs = this.formInputs.filter(input =>
        input.quick_order_show_as !== 'table' && input.quick_order_show_as !== 'attachments'
      );
    }
  }
  filterFormInputs() {
    let formInputs = this.formInputs
    let filteredInputs: any[] = [];
    let fieldNames: string[] = [];
    for (let input of formInputs) {
      if (input.field && input.field.name && !fieldNames.includes(input.field.name)) {
        fieldNames.push(input.field.name);
        filteredInputs.push(input);
      } else if (!input.field) {
        filteredInputs.push(input);
      }
    }
    this.formInputs = filteredInputs;
  }

  ngAfterViewInit() {
    this.adjustLabelWidths();

    if (this.firstInput && this.firstInput.nativeElement) {
      this.firstInput.nativeElement.focus();
    }
  }

  ngAfterViewChecked() {
    this.adjustLabelWidths();
  }

  getComputeFields(){
    for(let index = 0; index < this.formInputs.length; index++){
      let input = this.formInputs[index]
      if(input.compute_function_name && input.compute_function_fields){
        this.inputsToBeUpdated.push(input)
        this.computedFields.push({
          'computeFunctionName' : input.compute_function_name,
          'computeFunctionFields' : input.compute_function_fields,
          'inputToBeUpdated' : input,
          'inputIndex' : index
        })
      }
    }
    
    }

  adjustLabelWidths() {
    const labels = this.el.nativeElement.querySelectorAll('.input-label');
    let maxWidth = 0;

    // Calculate the maximum width of all labels
    labels.forEach((label: HTMLElement) => {
      const labelWidth = label.offsetWidth;
      if (labelWidth > maxWidth) {
        maxWidth = labelWidth;
      }
    });

    // Set all labels to the maximum width
    labels.forEach((label: HTMLElement) => {
      this.renderer.setStyle(label, 'width', `${maxWidth}px`, RendererStyleFlags2.Important);
      this.renderer.setStyle(label, 'min-width', `160px`, RendererStyleFlags2.Important);
      this.renderer.setStyle(label, 'display', 'block', RendererStyleFlags2.Important);
      this.renderer.setStyle(label, 'color', '#333333', RendererStyleFlags2.Important);
      this.renderer.setStyle(label, 'font-size', '10pt', RendererStyleFlags2.Important);
    });
  }


  async getFooterFields() {
    this.formFooterInputs = [];
    let formInputs;
    if (this.formInputs) {
      formInputs = this.formInputs;
    } else {
      if (this.stepper)
        formInputs = this.steps[this.stepper.selectedIndex].form_inputs;
    }
    if (formInputs && formInputs.length > 0) {
      this.insideFooterCount = formInputs.filter((input: { position: string; }) => input.position === 'inside_footer').length;
      // First, add inside_footer inputs
      for (let input of formInputs) {
        if (input.position === 'inside_footer') {
          this.formFooterInputs.push(input);
        }
        if ( input.value == '{{user_id}}') {
          let fields_to_fetch = ['id', 'display_name']
          if(input.is_auto_fill_mapping_fields_enabled && input.mapping_relation_fields && input.mapping_relation_fields.length > 0){
            for(let mappingField of input.mapping_relation_fields){
              fields_to_fetch.push(mappingField[1])
            }
          }
          let user = await this.odooRpc.call('rb_delivery.user', 'get_user_info_with_fields', [ fields_to_fetch])
          user = user.body.result.result.result[0]
          this.updateValue(input,{
            id: user.id,
            display_name: user.display_name
          } )
          this.updateMappingFieldsInfo(user, input)
        } else if (input && input.field && input.value && typeof input.value =='string' && input.value.includes('{{id:')) {
          let ids = this.extractNumbers(input.value)
          let inputValue = await this.odooRpc.searchRead(input.field.model_name, [['id', 'in', ids]], ['id', 'display_name'], 0, 0, "id DESC")
          if (inputValue && inputValue.body && inputValue.body.result && inputValue.body.result.result && inputValue.body.result.result.result) {
            inputValue = inputValue.body.result.result.result
            if(input.field.ttype == 'many2one') {
              this.updateValue(input,{
              id: inputValue[0].id,
              display_name: inputValue[0].display_name
            })
            }
            else {
              this.updateValue(input,inputValue)
            }
          }
       } else if (input && input.field && input.value && typeof input.value =='string' && input.value) {
        this.updateValue(input,input.value)
       }
      }
     for (let input of formInputs) {
       if (input.position === 'in_footer') {
         this.formFooterInputs.push(input);
       }
     }
   }
 }

 extractNumbers(input: string): number[] {
  const trimmedInput = input.replace(/{{id:|}}/g, '').trim();
  
  const numberArray = trimmedInput
    .split(',')
    .map(str => parseInt(str.trim(), 10))
    .filter(num => !isNaN(num));
  
  return numberArray;
}
 

  doFunction(input?: any,event?:any) {
    if (input && input.button_function) {
      this.updateInput(input, 'loading', true)
      this[input.button_function as OperationType](input)
      
    }else if(event){
      this[event as OperationType](input)
    }

  }

  async submitForm(input: any) {

    if ('is_quick_order' in this.formValues) {
      delete this.formValues['is_quick_order']
    }

    let valid = this.checkValidations()
    if(this.record){
      this.removeUnchangedValues()
    }

    
    if(valid){
      await this.createTableRecords()
      let order = await this.odooRpc.createRecord(this.model, this.formValues)
      if (order && order.body && order.body.result && order.body.result.result && order.body.result.result.success && order.body.result.result.result) {
        let orderData = order.body.result.result.result[0]
        this.orders.push(
          {
            'id': orderData.id,
            'referenceNumber': orderData.reference_id,
            'sender': orderData.assign_to_business[1],
            'recipientName': orderData.customer_name,
            'recipientMobile': orderData.customer_mobile,
            'recipientRegion': orderData.customer_area[1],
            'recipientAddress': orderData.customer_address

          }
        )


        this.clearInputs();
        this.orders = [...this.orders]

        

        this.snackBar.open(this.translate.instant('ORDER_CREATED_SUCCESSFULY'), '', {
          duration: 2000,
          verticalPosition: 'top',
        });
        this.requestComputedFieldsUpdate()

        this.clearInputs();

        const inputElements: NodeListOf<HTMLInputElement> = this.formContainer.nativeElement.querySelectorAll('input');

        if (inputElements.length > 1) {
          const secondInput = inputElements[1];
          secondInput.focus();
        }


      } else if (order && order.body && order.body.error && order.body.error.data.message) {
        this.dialogService.warning({
          input: order.body.error.data.message,
          whatToDo: order.body.error.data.message,
          code: '9999',
        });
      }

      this.updateInput(input, 'loading', false)


    }
    else{
      this.updateInput(input, 'loading', false)
    }
  }
  
  async createTableRecords() {
    for (let table of this.tableInputs) {
      if (table.value) {
        let values = []
        for(let val of table.value) {
          values.push([0, 0, val])
        }
        this.formValues[table.field.name] = values
      }
    }
    for (let attachment of this.attachmentInputs) {
      if (attachment.value && attachment.value && attachment.value.length > 0) {
        if (attachment.field && attachment.field.name === 'attachment_ids' &&
            (attachment.field.ttype === 'one2many' || attachment.field.ttype === 'many2many')) {

          let values = []

          const fileToBase64 = (file: File): Promise<string> => {
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.readAsDataURL(file);
              reader.onload = () => {
                if (reader.result) {
                  const base64Data = reader.result.toString().split(',')[1];
                  resolve(base64Data);
                } else {
                  reject(new Error('Failed to read file'));
                }
              };
              reader.onerror = reject;
            });
          };

          for (let file of attachment.value) {
            if (file instanceof File) {
              try {
                const base64Data = await fileToBase64(file);
                values.push([0, 0, {
                  name: file.name,
                  attachment: base64Data,
                }]);
              } catch (error) {
                console.error('Error converting file to base64:', error);
              }
            }
          }

          if (values.length > 0) {
            this.formValues[attachment.field.name] = values;
          }
        }
        else if (attachment.field && attachment.field.ttype === 'binary' && attachment.value.length > 0) {
          const file = attachment.value[0];
          if (file instanceof File) {
            try {
              const reader = new FileReader();
              const base64Data = await new Promise<string>((resolve, reject) => {
                reader.onload = () => {
                  if (reader.result) {
                    const base64 = reader.result.toString().split(',')[1];
                    resolve(base64);
                  } else {
                    reject(new Error('Failed to read file'));
                  }
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
              });

              this.formValues[attachment.field.name] = base64Data;
              if (attachment.field.name + '_filename' in this.formValues) {
                this.formValues[attachment.field.name + '_filename'] = file.name;
              }
            } catch (error) {
              console.error('Error processing binary file:', error);
            }
          }
        }
      }
    }
  }
  

  async clearInputs() {
    for (let input of this.formInputs) {
      if(input.clear_after_submit){
        if(input && input.field && input.field.ttype == 'boolean'){
          if (  this.defaultVals.hasOwnProperty(input.field.name)  && this.defaultVals[input.field.name]) {
            this.updateInput(input, 'value', this.defaultVals[input.field.name])
          } else {
            this.updateInput(input, 'value', false)
          }
        } else if (input && input.field && input.field.name && this.defaultVals.hasOwnProperty(input.field.name)  && this.defaultVals[input.field.name]) {
          if ( this.defaultVals[input.field.name] == '{{user_id}}') {
            let fields_to_fetch = ['id', 'display_name']
            if(input.is_auto_fill_mapping_fields_enabled && input.mapping_relation_fields && input.mapping_relation_fields.length > 0){
              for(let mappingField of input.mapping_relation_fields){
                fields_to_fetch.push(mappingField[1])
              }
            }
            let user = await this.odooRpc.call('rb_delivery.user', 'get_user_info_with_fields', [ fields_to_fetch])
            user = user.body.result.result.result[0]
            this.updateValue(input,{
              id: user.id,
              display_name: user.display_name
            } )
            this.updateMappingFieldsInfo(user, input)
          } else if (input && input.field && this.defaultVals[input.field.name] && typeof this.defaultVals[input.field.name] =='string' && this.defaultVals[input.field.name].includes('{{id:')) {
            let ids = this.extractNumbers(input.value)
            let inputValue = await this.odooRpc.searchRead(input.field.model_name, [['id', 'in', ids]], ['id', 'display_name'], 0, 0, "")
            if (inputValue && inputValue.body && inputValue.body.result && inputValue.body.result.result && inputValue.body.result.result.result) {
              inputValue = inputValue.body.result.result.result
              if(input.field.ttype == 'many2one') {
                this.updateValue(input,{
                id: inputValue[0].id,
                display_name: inputValue[0].display_name
              })
              }
              else {
                this.updateValue(input,inputValue)
              }
            }
         } else if (input && input.field && this.defaultVals[input.field.name] && typeof this.defaultVals[input.field.name] =='string' && this.defaultVals[input.field.name]) {
          this.updateValue(input,this.defaultVals[input.field.name])
         }
        } else
          this.updateInput(input, 'value', undefined)

        if (input.field)
          this.formValues[input.field.name] = undefined


        if (input.parent_value)
          input.parent_value = undefined
      }
    }

    const clearedTableInputs = this.tableInputs.map(table => {
      if (table.clear_after_submit) {
        const clonedTable = this.objectService.deepClone(table);
        clonedTable.value = undefined;

        return clonedTable;
      }
      return table;
    });

    this.tableInputs = clearedTableInputs;

    const clearedAttachmentInputs = this.attachmentInputs.map(attachment => {
      if (attachment.clear_after_submit) {
        const clonedAttachment = this.objectService.deepClone(attachment);
        clonedAttachment.value = undefined;

        if (attachment.field) {
          this.formValues[attachment.field.name] = undefined;
        }

        return clonedAttachment;
      }
      return attachment;
    });

    this.attachmentInputs = clearedAttachmentInputs;

    if (this.attachmentComponents) {
      this.attachmentComponents.forEach(component => {
        if (component.input && component.input.clear_after_submit) {
          component.clearFiles();
        }
      });
    }
  }

  async updateValue(input: any, value: any) {
    let self = this
    let tableIndex = this.tableInputs.indexOf(input)
    if (tableIndex > -1) {
      this.tableInputs[tableIndex].value = value
      this.formValues[input.field.name] = value
      this.requestComputedFieldsUpdate()
      return
    }

    let attachmentIndex = this.attachmentInputs.indexOf(input)
    if (attachmentIndex > -1) {
      this.attachmentInputs[attachmentIndex].value = value
      return
    }

    if(input.is_auto_fill_mapping_fields_enabled ){
      if(value && (value.id || value)){
        this.formValues[input.field.name] = value.id || value
      }
      else{
        this.formValues[input.field.name] = this.record?false:undefined
      }
      input.value = value
      this.emitValuesSubject.next(input)
    }
    else if (input.field.ttype == 'many2one') {
      if(value && (value.id || value[0])){
        this.formValues[input.field.name] = value.id || value[0]
        if(!this.record){
          this.updateChildValue(input, value.id || value[0])
        }
        
        if(!value.id){
          value={id:value[0],display_name:value[1]}
        }
      }
      else{
        this.formValues[input.field.name] = this.record?false:undefined
        if(!this.record){
          this.updateChildValue(input, undefined)
        }
      }
      input.value = value
      this.emitValuesSubject.next(input)
    }
    else if (input.field.ttype == 'selection') {
      if(value && (value.id || value[0])){
        if(input.selection_items && input.selection_items.length > 0){
          for(let  selectionItem of input.selection_items){
            if(selectionItem[0] == value){
              value = selectionItem
              break
            }
          }
        }
        this.formValues[input.field.name] = value.id || value[0]
      }

      else{
        this.formValues[input.field.name] = this.record?false:undefined
      }
     
      input.value = value
      this.emitValuesSubject.next(input)

    }
    else if (input.field.ttype == 'one2many' || input.field.ttype == 'many2many' &&(!input.is_auto_fill_mapping_fields_enabled || this.record)){
      if(!this.formValues[input.field.name]){
        this.formValues[input.field.name] = []
      }
      if(!this.formValues['sequences']){
        this.formValues['sequences'] = []
      }
      let ids:number[]=[]
      let sequences:string[]=[]
      let isValuesFetched=false
      let createNewRecord = false
      let multiScannedElemtns = false
      for(let item of value){
        if(item.id && !item.createNewRecord){
          isValuesFetched=true
          ids.push(Number(item.id))
        }
        else if(!item.id && !item.createNewRecord && !item.multiScannedElemtns){
          ids.push(item)
        }
        if(item.multiScannedElemtns){
          multiScannedElemtns = true
          this.formValues['sequences'] = item.sequences
          this.formValues['createNewRecord']=true
          this.formValues['from_mobile']=true
        }
      }
      if(!isValuesFetched && value.length>0){
        let fetched = false
        if(!multiScannedElemtns && !createNewRecord){
          let newVals = await this.fetchValuesOf(input,ids)
          if (newVals && newVals.length > 0) {
            fetched = true
          }
          value = newVals && newVals.length > 0 ? newVals : value
        }
        if(value.length>0 && fetched){
          this.formValues[input.field.name]=[[6,0,ids]]
        } else {
          this.formValues[input.field.name]=value
        }
      }else{
        this.formValues[input.field.name]=[[6,0,ids]]
      }
      if(multiScannedElemtns){
        const sequencesToFilter: Set<string> = new Set(value[0].sequences);
        input.value = input.value.filter((item: { sequence: string; }) => sequencesToFilter.has(item.sequence));
      }
      else{
        input.value = [...value]      
      }
      this.emitValuesSubject.next(input)
    }
    else if(input.field.ttype == 'datetime'){
      this.formValues[input.field.name] = value
      input.value = value
      this.emitValuesSubject.next(input)
    }
    else {
      this.formValues[input.field.name] = value
      input.value = value
    }
    this.checkValidations(input)

  }

  updateChildValue(parentField: any, parentValue: number | undefined) {
    try {
      const formInputs = this.formInputs || this.steps[this.stepper.selectedIndex].form_inputs;
  
      if (parentField.is_auto_fill_mapping_fields_enabled && parentField.mapping_relation_fields?.length > 0) {
        const childFields = parentField.mapping_relation_fields.map((field: any[]) => field[0]);
        
        const childInputs = formInputs.filter((input: any) =>
          input.field && input.field.name && childFields.includes(input.field.name)
        );
  
        for (const childInput of childInputs) {
          this.updateValue(childInput, undefined);
        }
      } else {
        const childInput = formInputs.find((input: any) =>
          input.field && input.parent_field && input.parent_field.name == parentField.field.name
        );
  
        if (childInput) {
          childInput.value = undefined;
          childInput.parent_value = parentValue;
          this.updateValue(childInput, undefined);
        }
      }
    } catch (error) {
      console.error('Error updating child input:', error);
    }
  }

  close(){
  }

  invokeComputeFunction(event?: any, input?: any) {
    this.filterUpdatedValues(this.updatedFormValues)
  }

  fetchValuesOf(input: any, ids: number[]): Promise<any> {
    let fields = ['id', 'display_name'];
    
    return this.odooRpc
        .searchRead(input.field.model_name, [['id', 'in', ids]], fields, 0, 0, "id DESC")
        .then(responseData => {
          
            if (responseData && responseData.body && responseData.body.result && responseData.body.result.success) {
                return responseData.body.result.result;
            } else {
                return [];
            }
        });
  }

  checkValidations(input?: any) {

    let valid=true
    let firstNonValidInput:any=false
    let formInputs:any[]=[]
    if(input){
      valid = this.validateInput(input)
      if(!valid){
        firstNonValidInput=input
      }
    }
    else if(this.formInputs){
      formInputs=this.formInputs
    }
    else if(this.steps){
      formInputs = this.steps[this.stepper._getFocusIndex() as number].form_inputs
    }

    for(let input of formInputs.filter(inputField=>inputField.field)){
      if(!this.validateInput(input)){
        valid=false
        if(!firstNonValidInput) 
          firstNonValidInput=input
      }
    }
    // if(firstNonValidInput){
    //   // to scroll to the non valid input so the user can see it
    //   document.getElementById(firstNonValidInput.field.id.toString())?.scrollIntoView({
    //     behavior:"smooth",
    //     block:"start",
    //     inline:"nearest"
    //   })
    // }
    
    return valid
  }

  updateInput(input: any, attribute: string, value: any) {
    let formInputs
    if (this.formInputs) {
      formInputs = this.formInputs
    } else {
      formInputs = this.steps[this.stepper.selectedIndex].form_inputs
    }
    formInputs[this.getIndexOfInput(input)][attribute] = value

    // this.checkComputedFields(this.formInputs, this.formValues)
    if (this.formInputs[this.getIndexOfInput(input)].field && (['many2one', 'selection', 'one2many', 'many2many', 'boolean'].includes(formInputs[this.getIndexOfInput(input)].field.ttype)))
      this.requestComputedFieldsUpdate()
    else if (!this.formInputs[this.getIndexOfInput(input)].field)
      this.requestComputedFieldsUpdate()
    
  }

  validateInput(input: any) {

    let value = this.getValueOfInput(input)
    let valid = true
    let validations:any[]=[]
    let inputs:any[] = []
    if(this.steps && this.steps.length > 0){
      inputs = this.steps[this.stepper.selectedIndex].form_inputs
    }
    else{
      inputs = this.formInputs
    }
    let valueLength=value?value.length:0
    if(input.validations.required && (!value || value=='') && !input.field.is_location){
      validations.push({messageKey: 'THIS_FIELD_IS_REQUIRED'})
      valid=false
    }
    if(input.validations.max_length && valueLength>input.validations.max_length){

      validations.push({messageKey: 'MAXIMUM_LENGTH',messageParams:{value: input.validations.max_length}})
      valid=false
      
    }
    if(input.validations.min_length &&valueLength<input.validations.min_length){
      validations.push({messageKey: 'MINIMUM_LENGTH',messageParams:{value: input.validations.min_length}})
      valid=false
      
    }
    if(input.validations.email && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)){
      validations.push({messageKey: this.translate.instant('PLEASE_ENTER_VALID_EMAIL')})
      valid=false
    }
    this.updateInput(input,'validationMessage',validations)
    return valid
  }

  getValueOfInput(input:any) {
    if (this.formInputs){
      let inputIndex =  this.formInputs[this.getIndexOfInput(input)]
      return inputIndex.value
    }
    else if(this.steps && this.steps.length>0){
      return input.value
    }
    return false

  }

  getIndexOfInput(input: any) {
    let formInputs
    if (this.formInputs) {
      formInputs = this.formInputs
    } else {
      formInputs = this.steps[this.stepper.selectedIndex].form_inputs
    }
    try {
      return formInputs.indexOf(input)
    }
    catch {
      return -1
    }
  }

  setParentLoading(value: boolean, input: any) {
    let formInputs: any[] = []
    if (this.formInputs) {
      formInputs = this.formInputs
    } else {
      formInputs = this.steps[this.stepper.selectedIndex].form_inputs
    }
    formInputs[this.getIndexOfParentInput(input)].loading = value

  }

  updateParentValue(input: string, value: any) {
    try {
      let formInputs
      if (this.formInputs) {
        formInputs = this.formInputs
      } else {
        formInputs = this.steps[this.stepper.selectedIndex].form_inputs
      }
      formInputs[formInputs.indexOf(input)].parent_value = value['id']
      let childInput = formInputs[formInputs.indexOf(input)]
      childInput.parent_value = value['id']
      
      let parentInput = formInputs[this.getIndexOfParentInput(childInput)]

      formInputs[this.getIndexOfParentInput(childInput)].validationMessage = []

      parentInput.value = value
      this.formValues[parentInput.field.name] = value.id
      this.emitValuesSubject.next(input)

      
    } catch {
    }
  }

  getIndexOfParentInput(input: any) {
    let formInputs
    if (this.formInputs) {
      formInputs = this.formInputs
    } else {
      formInputs = this.steps[this.stepper.selectedIndex].form_inputs
    }
    try {
      let parentInput = formInputs.filter((rec: any) => rec.field && rec.field.name == input.parent_field.name)[0]
      return formInputs.indexOf(parentInput)
    }
    catch {
      return -1
    }
  }

  updateMappingFieldsInfo(values : any,inputToAutoFill :any){
    let autoFilledInputs = this.formInputs.filter((input: { is_auto_fill_mapping_fields_enabled: any; field: { name: any; }; }) => input.is_auto_fill_mapping_fields_enabled && input.field  && inputToAutoFill.field && inputToAutoFill.field.name == input.field.name);

    if (autoFilledInputs && autoFilledInputs.length > 0) {
      for (let field of autoFilledInputs) {
        for (let input of field.mapping_relation_fields) {
          let formInput = this.formInputs.find((formInput: { field: { name: any; }; }) =>
            formInput.field && formInput.field.name && formInput.field.name == input[0]
          );
          if (formInput) {
            this.updateValue(formInput, values[input[1]]);
          }
        }
      }
    }
  }

  removeUnchangedValues() {
    for(let input of this.formInputs){
      
      if(input.field ){
        if(input.field.ttype=='many2one' && this.record[input.field.name] && this.formValues[input.field.name] == this.record[input.field.name][0]){
          delete this.formValues[input.field.name]
        }
        else if(
          (input.field.ttype=='many2many' || input.field.ttype=='one2many')
          && this.formValues[input.field.name][0][2].every((id:number)=>this.record[input.field.name].includes(id))
          && this.record[input.field.name].every((id:number)=>this.formValues[input.field.name][0][2].includes(id))
        ){
          delete this.formValues[input.field.name]
        }
        else if(this.formValues[input.field.name] == this.record[input.field.name]){
          delete this.formValues[input.field.name]
        }
      }
      
    }
  }

  splitFormInputs() {
    if (this.formInputs && Array.isArray(this.formInputs)) {
      this.leftFormInputs = this.formInputs.filter(input => input.quick_order_position === 'left');
      this.rightFormInputs = this.formInputs.filter(input => input.quick_order_position === 'right');
    }
  }


  async filterUpdatedValues(updatedFormValues:any) {
    let changedValues:any={}
    let inputsToUpdats: any[] = []
    let computeFunctionsToCall = []
    if(!this.formValues){
      this.formValues = updatedFormValues
      changedValues = updatedFormValues
    } else {
      for (let key in this.formValues) {
        if (!(key in updatedFormValues) || this.formValues[key] !== updatedFormValues[key]) {
          updatedFormValues[key] = this.formValues[key];
          changedValues[key] = updatedFormValues[key];
        }
      }
    }

    for(let valueUpdated in changedValues){
      if(this.formInputs)
      for(let input of this.formInputs){
        if(input && input.field && input.field.name == valueUpdated){
            for(let computeField of this.computedFields){
              if(computeField['computeFunctionFields'].includes(input.field.id)){
                computeFunctionsToCall.push(computeField['computeFunctionName'])
                let inputToBeUpdatedImdex = this.formInputs.findIndex((inputToGet:any) => inputToGet && inputToGet.field && inputToGet.field.id == computeField['inputToBeUpdated'].field.id)
                let inputToBeUpdated = this.formInputs[inputToBeUpdatedImdex]
                if(!inputsToUpdats.includes(inputToBeUpdated)){
                  inputsToUpdats.push(inputToBeUpdated)
                }
                if(inputToBeUpdated && inputToBeUpdated.field && inputToBeUpdated.field.id){
                  for(let seconfComputeField of this.computedFields){
                    if(seconfComputeField['computeFunctionFields'].includes(inputToBeUpdated.field.id)){
                      computeFunctionsToCall.push(seconfComputeField['computeFunctionName'])
                      let secondinputToBeUpdatedImdex = this.formInputs.findIndex((inputToGet:any) => inputToGet && inputToGet.field && inputToGet.field.id == seconfComputeField['inputToBeUpdated'].field.id)
                      let secondInputToBeUpdated = this.formInputs[secondinputToBeUpdatedImdex]
                      if(!inputsToUpdats.includes(secondInputToBeUpdated)){
                        inputsToUpdats.push(secondInputToBeUpdated)
                      }
                    }
                  }
                }
              }
            }
        }
      }

      for(let input of this.tableInputs){
        if(input && input.field && input.field.name == valueUpdated){
            for(let computeField of this.computedFields){
              if(computeField['computeFunctionFields'].includes(input.field.id)){
                computeFunctionsToCall.push(computeField['computeFunctionName'])
                let inputToBeUpdatedImdex = this.formInputs.findIndex((inputToGet:any) => inputToGet && inputToGet.field && inputToGet.field.id == computeField['inputToBeUpdated'].field.id)
                let inputToBeUpdated = this.formInputs[inputToBeUpdatedImdex]
                if(!inputsToUpdats.includes(inputToBeUpdated)){
                  inputsToUpdats.push(inputToBeUpdated)
                }
                if(inputToBeUpdated && inputToBeUpdated.field && inputToBeUpdated.field.id){
                  for(let seconfComputeField of this.computedFields){
                    if(seconfComputeField['computeFunctionFields'].includes(inputToBeUpdated.field.id)){
                      computeFunctionsToCall.push(seconfComputeField['computeFunctionName'])
                      let secondinputToBeUpdatedImdex = this.formInputs.findIndex((inputToGet:any) => inputToGet && inputToGet.field && inputToGet.field.id == seconfComputeField['inputToBeUpdated'].field.id)
                      let secondInputToBeUpdated = this.formInputs[secondinputToBeUpdatedImdex]
                      if(!inputsToUpdats.includes(secondInputToBeUpdated)){
                        inputsToUpdats.push(secondInputToBeUpdated)
                      }
                    }
                  }
                }
              }
            }
        }
      }
    }

    computeFunctionsToCall = Array.from(new Set(computeFunctionsToCall));
    inputsToUpdats = Array.from(new Set(inputsToUpdats));
    if(computeFunctionsToCall.length > 0 && inputsToUpdats.length > 0){
      this.checkComputedFields(inputsToUpdats,this.formValues)
    }
    changedValues={}
    this.updatedFormValues = this.objectService.deepClone(this.formValues)

  }


  async checkComputedFields(inputsToUpdats:any[],formValues:any){
    for(let inp of inputsToUpdats){
      let formInputIndex = inputsToUpdats.findIndex((input:any) => input.field && input.field.id === (inp && inp.field && inp.field.id));
      if (formInputIndex > -1 && inputsToUpdats[formInputIndex] && inputsToUpdats[formInputIndex].value == undefined) {
        inputsToUpdats[formInputIndex].value = false
      }
    }
    formValues['is_quick_order'] = true
    let computeFunctionResult = await this.odooRpc.call('rb_delivery.mobile_compute_functions','do_function',[formValues,inputsToUpdats])
    if(computeFunctionResult && computeFunctionResult.body && computeFunctionResult.body.result && computeFunctionResult.body.result.result.success && computeFunctionResult.body.result.result.result)
    for(let resultInput of computeFunctionResult.body.result.result.result){
      if (!resultInput)
        continue;
      let formInputIndex = this.formInputs.findIndex((input:any) => input.field && input.field.id === resultInput.field.id);
      if (formInputIndex < 0) {
        formInputIndex = this.tableInputs.findIndex((input:any) => input.field && input.field.id === resultInput.field.id);
        if (resultInput.value instanceof Array || typeof resultInput.value === 'object') {
          this.formValues[resultInput.field.name] = resultInput.value.id || resultInput.value[0] || resultInput.value
        } else {
          this.formValues[resultInput.field.name] = resultInput.value
        }
        this.tableInputs[formInputIndex].value = resultInput.value
        this.tableInputs[formInputIndex].invisible = resultInput.invisible
        if(resultInput.field && resultInput.field.name){
          if (resultInput.value instanceof Array || typeof resultInput.value === 'object') {
            this.tableInputs[resultInput.field.name] = resultInput.value.id || resultInput.value[0] || resultInput.value
          } else {
            this.tableInputs[resultInput.field.name] = resultInput.value
          }      
        }
        continue;
      }
      if (resultInput.value instanceof Array || typeof resultInput.value === 'object') {
        this.formValues[resultInput.field.name] = resultInput.value.id || resultInput.value[0] || resultInput.value
        
      } else {
        this.formValues[resultInput.field.name] = resultInput.value
      }
      this.formInputs[formInputIndex].value = resultInput.value
      this.formInputs[formInputIndex].invisible = resultInput.invisible
      this.formInputs[formInputIndex].validations = resultInput.validations
      this.formInputs[formInputIndex].warning_message = resultInput.warning_message
      if(resultInput.field && resultInput.field.name){
        if ((resultInput.value instanceof Array || typeof resultInput.value === 'object') && resultInput.value) {
          this.formValues[resultInput.field.name] = resultInput.value.id || resultInput.value[0] || resultInput.value
        } else if( resultInput.value) {
          this.formValues[resultInput.field.name] = resultInput.value
        }      
      }
    }
  }

  setupInputUpdateStream() {
    this.updateInputsSubject.pipe(
      debounceTime(800)  
    ).subscribe(({ inputsToUpdate }) => {
      this.filterUpdatedValues(inputsToUpdate)
      this.checkComputedFields(this.tableInputs, this.formValues);
    });
  }

  requestComputedFieldsUpdate() {
    this.updateInputsSubject.next({ inputsToUpdate: this.updatedFormValues });
  }

  focusInput() {
    if (this.firstInput) {
      this.firstInput.nativeElement.focus();
    } else {
      console.warn('The input element is not available yet.');
      setTimeout(() => {
        if (this.firstInput) {
          this.firstInput.nativeElement.focus();
        }
      }, 100); 
    }
  }
}


export enum OperationType {
  submitForm = "submitForm",
}