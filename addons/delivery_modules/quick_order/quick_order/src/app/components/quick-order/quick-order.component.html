<div class="form-container" #formContainer *ngIf="formInputs && formInputs.length > 0">
  <div class="form-content row">
    <div class="left-inputs col-md-6 col-12" appEqualWidth>
      <ng-container *ngFor="let input of leftFormInputs; let i = index;">
        <div class="form-group row align-items-center" [ngStyle]="{ display: input.invisible ? 'none' : 'flex', gap: '5px' }" *ngIf="(input.position == 'in_form' || input.is_separator)">
          <div class="col-5 d-flex align-items-center" style="flex: 0 9 ;    display: flex;
    align-items: center;" *ngIf="input && input.field && input.field.placeholder">
            <label class="input-label">{{ input.field.placeholder | translate }}</label>
          </div>
          <div class="col-7" style="flex: auto;">
            <app-form-input
              #firstInput *ngIf="i === 0"
              class="small-input"
              [field]="input.field"
              [parentField]="input.parent_field"
              [domain]="input.domain"
              [searchDomain]="input.search_domain"
              [limitPerSearch]="input.limit_per_search"
              [value]="input.value"
              [haveImage]="input.have_image"
              [imageFieldName]="input.image_field_name"
              [parentValue]="input.parent_value"
              [isSeparator]="input.is_separator"
              [firstSeparator]="input.is_separator && i == 0"
              [color]="input.color"
              [background]="input.background"
              [separatorTitle]="input.separator_title"
              [showBarcodeScanner]="input.show_barcode_scanner"
              [showVoiceToTextAbility]="input.show_voice_to_text_ability"
              [loading]="input.loading"
              [success]="input.success"
              [fail]="input.fail"
              [isFormCreator]="input.is_form_creator"
              [formName]="input.form_name"
              [isButton]="input.is_button"
              [buttonFunction]="input.button_function"
              [buttonText]="input.button_text"
              [buttonIcon]="input.button_icon"
              [selectionItems]="input.selection_items"
              (selectedValue)="updateValue(input, $event)"
              (selectedParentValue)="updateParentValue(input, $event)"
              (setParentLoading)="setParentLoading($event, input)"
              (buttonPress)="doFunction(input)"
              [validationMessage]="input.validationMessage"
              [InTouchSelection]="input.quick_order_touch_selection"
              [isMonetary]="input.is_monetary"
              (updateMappingFields)="updateMappingFieldsInfo($event, input)"
              [isAutoFillMappingFieldsEnabled]="input.is_auto_fill_mapping_fields_enabled"
              [mappingFields]="input.mapping_relation_fields"
              (computeEmmiter)="invokeComputeFunction($event, input)"
              [validations]="input.validations"
              [connected_field_to_selection_modal]="input.connected_field_to_selection_modal"
              [search_domain_inside_selection_modal]="input.search_domain_inside_selection_modal"
              [showClientHistory]="input.show_client_history"
              [numberOfInputs]="formInputs.length"
              [warning_message]="input.warning_message"
              [showCreateButton]="input.show_create_button"
              (validationEmiter)="validateInput(input)"
            ></app-form-input>
            <app-form-input
            *ngIf="i !== 0"
            class="small-input"
            [field]="input.field"
            [parentField]="input.parent_field"
            [domain]="input.domain"
            [searchDomain]="input.search_domain"
            [limitPerSearch]="input.limit_per_search"
            [value]="input.value"
            [haveImage]="input.have_image"
            [imageFieldName]="input.image_field_name"
            [parentValue]="input.parent_value"
            [isSeparator]="input.is_separator"
            [firstSeparator]="input.is_separator && i == 0"
            [color]="input.color"
            [background]="input.background"
            [separatorTitle]="input.separator_title"
            [showBarcodeScanner]="input.show_barcode_scanner"
            [showVoiceToTextAbility]="input.show_voice_to_text_ability"
            [loading]="input.loading"
            [success]="input.success"
            [fail]="input.fail"
            [isFormCreator]="input.is_form_creator"
            [formName]="input.form_name"
            [isButton]="input.is_button"
            [buttonFunction]="input.button_function"
            [buttonText]="input.button_text"
            [buttonIcon]="input.button_icon"
            [selectionItems]="input.selection_items"
            (selectedValue)="updateValue(input, $event)"
            (selectedParentValue)="updateParentValue(input, $event)"
            (setParentLoading)="setParentLoading($event, input)"
            (buttonPress)="doFunction(input)"
            [validationMessage]="input.validationMessage"
            (computeEmmiter)="invokeComputeFunction($event, input)"
            [InTouchSelection]="input.quick_order_touch_selection"
            [isMonetary]="input.is_monetary"
            (updateMappingFields)="updateMappingFieldsInfo($event, input)"
            [isAutoFillMappingFieldsEnabled]="input.is_auto_fill_mapping_fields_enabled"
            [mappingFields]="input.mapping_relation_fields"
            [validations]="input.validations"
            [connected_field_to_selection_modal]="input.connected_field_to_selection_modal"
            [search_domain_inside_selection_modal]="input.search_domain_inside_selection_modal"
            [showClientHistory]="input.show_client_history"
            [numberOfInputs]="formInputs.length"
            [warning_message]="input.warning_message"
            (validationEmiter)="validateInput(input)"
            [showCreateButton]="input.show_create_button"
          ></app-form-input>
          </div>
        </div>
      </ng-container>
    </div>

    <div class="right-inputs col-md-6 col-12" appEqualWidth>
      <ng-container  *ngFor="let input of rightFormInputs; let i = index;">
        <div class="form-group row align-items-center" [ngStyle]="{ display: input.invisible ? 'none' : 'flex', gap: '5px' }" *ngIf="(input.position == 'in_form' || input.is_separator)">
          <div class="col-5 d-flex align-items-center" style="flex: 0 9;    display: flex;
    align-items: center;" *ngIf="input && input.field && input.field.placeholder">
            <label class="input-label">{{ input.field.placeholder | translate }}</label>
          </div>
          <div style="flex: auto;" class="col-7" >
            <app-form-input
              class="small-input"
              [field]="input.field"
              [parentField]="input.parent_field"
              [domain]="input.domain"
              [searchDomain]="input.search_domain"
              [limitPerSearch]="input.limit_per_search"
              [value]="input.value"
              [haveImage]="input.have_image"
              [imageFieldName]="input.image_field_name"
              [parentValue]="input.parent_value"
              [isSeparator]="input.is_separator"
              [firstSeparator]="input.is_separator && i == 0"
              [color]="input.color"
              [background]="input.background"
              [separatorTitle]="input.separator_title"
              [showBarcodeScanner]="input.show_barcode_scanner"
              [showVoiceToTextAbility]="input.show_voice_to_text_ability"
              [loading]="input.loading"
              [success]="input.success"
              [fail]="input.fail"
              [isFormCreator]="input.is_form_creator"
              [formName]="input.form_name"
              [isButton]="input.is_button"
              (computeEmmiter)="invokeComputeFunction($event, input)"
              [buttonFunction]="input.button_function"
              [buttonText]="input.button_text"
              [buttonIcon]="input.button_icon"
              [selectionItems]="input.selection_items"
              (selectedValue)="updateValue(input, $event)"
              (selectedParentValue)="updateParentValue(input, $event)"
              (setParentLoading)="setParentLoading($event, input)"
              (buttonPress)="doFunction(input)"
              [validationMessage]="input.validationMessage"
              [isMonetary]="input.is_monetary"
              (updateMappingFields)="updateMappingFieldsInfo($event, input)"
              [isAutoFillMappingFieldsEnabled]="input.is_auto_fill_mapping_fields_enabled"
              [mappingFields]="input.mapping_relation_fields"
              [validations]="input.validations"
              [InTouchSelection]="input.quick_order_touch_selection"
              [connected_field_to_selection_modal]="input.connected_field_to_selection_modal"
              [search_domain_inside_selection_modal]="input.search_domain_inside_selection_modal"
              [showClientHistory]="input.show_client_history"
              [numberOfInputs]="formInputs.length"
              [warning_message]="input.warning_message"
              [showCreateButton]="input.show_create_button"
              (validationEmiter)="validateInput(input)"
            ></app-form-input>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="form-tables" *ngIf="tableInputs && tableInputs.length > 0">
      <div *ngFor="let table of tableInputs; let i = index" style="width: 98%;">
        <div *ngIf="!table.invisible" style="width: 100%;" class="table-inner-container">
          <div class="table-title-container">
            <h3 class="table-title">{{ table.field.placeholder | translate }}:</h3>
          </div>
          <app-table-form-input style="width: 100%;" [formValues]="formValues" (selectedValue)="updateValue(table, $event)" [input]="table" [fieldRelation]="table.field.model_name" [field]="table.field"> </app-table-form-input>
        </div>
      </div>
  </div>

  <div class="form-attachments" *ngIf="attachmentInputs && attachmentInputs.length > 0">
      <div *ngFor="let attachment of attachmentInputs; let i = index" style="width: 98%;">
        <div *ngIf="!attachment.invisible" style="width: 99%;" class="attachment-inner-container">
          <div class="attachment-title-container">
            <h3 class="attachment-title">{{ attachment.field.placeholder | translate }}:</h3>
          </div>
          <app-attachment-form-input
            style="width: 100%;"
            [formValues]="formValues"
            (selectedValue)="updateValue(attachment, $event)"
            [input]="attachment"
            [field]="attachment.field">
          </app-attachment-form-input>
        </div>
      </div>
  </div>

  <div class="form-footer">
    <app-form-footer [orders]="orders" [formFooterInputs]="formFooterInputs" (buttonPress)="doFunction($event)"></app-form-footer>
  </div>
</div>

<div class="table-container">
  <app-orders-table [orders]="orders"></app-orders-table>
</div>

<div class="error-message-container" *ngIf="formInputs && formInputs.length == 0 && loading">
  <p>{{ 'Loading your Form' | translate }}</p>
</div>

<div class="error-message-container" *ngIf="formInputs && formInputs.length == 0 && !loading">
  <p>{{ 'YOU_DONT_HAVE_QUICK_ORDER_FORM' | translate }}</p>
</div>
