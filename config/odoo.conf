[options]
addons_path = /mnt/extra-addons/delivery_modules,/mnt/extra-addons

db_maxconn = 20

#HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
# Tuning Options
#HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
workers = 12
max_cron_threads = 1
limit_time_cpu = 1320
limit_time_real = 1300
limit_request = 8192
limit_memory_hard = 43690208256
limit_memory_soft = 41408506880

# log_level = debug_sql
# # (Alternatively, more explicitly):
# log_handler = odoo.sql_db:DEBUG
admin_passwd = admin

dbfilter = bosta
server_wide_modules = web,queue_job,ir_attachment_url

[queue_job]
channels = root:2, root.vhub:1, root.notification:1




