#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the _compute_agent_cost function to properly check allowed statuses
for both agent and delivered_by cases.
"""

import re

def fix_agent_cost_function():
    file_path = "/Users/<USER>/Desktop/olivery_web/delivery3/delivery/addons/delivery_modules/rb_delivery/models/order/order_model.py"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Define the old pattern (lines 3262-3267)
    old_pattern = r'''        if self\.state in allow_statuses and agent:
            self\.agent_cost = self\.get_agent_cost\(agent,'delivered_by'\)
        elif delivered_by:
            self\.agent_cost = self\.get_agent_cost\(delivered_by,'delivered_by'\)
        if self\.extra_agent_cost:
            self\.agent_cost = self\.agent_cost \+ self\.extra_agent_cost'''
    
    # Define the new replacement
    new_replacement = '''        if self.state in allow_statuses:
            if agent:
                self.agent_cost = self.get_agent_cost(agent,'delivered_by')
            elif delivered_by:
                self.agent_cost = self.get_agent_cost(delivered_by,'delivered_by')
        if self.extra_agent_cost:
            self.agent_cost = self.agent_cost + self.extra_agent_cost'''
    
    # Replace the pattern
    new_content = re.sub(old_pattern, new_replacement, content)
    
    if new_content != content:
        # Write the updated content back
        with open(file_path, 'w') as f:
            f.write(new_content)
        print("✅ Successfully fixed the _compute_agent_cost function!")
        print("The agent cost will now only be recalculated when the order status is in allowed_status_ids.")
    else:
        print("❌ Pattern not found. The function may have already been modified or the format is different.")

if __name__ == "__main__":
    fix_agent_cost_function()
