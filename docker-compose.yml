version: '3'

services:

  nginx:
    image: nginx:latest
    #container_name: production_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    ports:
      - 80:80
      - 443:443
    depends_on:
      - "web"

  web:
    image: odoo_rubik:1.0
    build: ./odoo/.
    depends_on:
      - db
    ports:
      - "8088:8069"

    stdin_open: true
    tty: true

    volumes:
      -  odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons

    # command: odoo -u rb_delivery -d unitedexp

  db:
    image: postgres:12
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_USER=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data/pgdata
volumes:
  odoo-web-data:
  odoo-db-data: